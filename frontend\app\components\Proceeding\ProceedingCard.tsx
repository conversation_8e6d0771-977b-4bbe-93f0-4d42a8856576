import React from 'react';
import Link from 'next/link';
import {
  CalendarIcon,
  UserIcon,
  BuildingOfficeIcon,
  TagIcon,
  ChartBarIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import StatusBadge from '../UI/StatusBadge';

interface ProceedingCardProps {
  proceeding: {
    id: number;
    name: string;
    description?: string;
    objective: string;
    status: string;
    priority: string;
    unique_id: string;
    initiation_date: string;
    planned_start_date?: string;
    planned_end_date?: string;
    progress_percent: number;
    total_steps: number;
    completed_steps: number;
    current_step_order?: number;
    owner: {
      id: number;
      username: string;
      email: string;
    };
    agency?: {
      id: number;
      name: string;
    };
    category?: {
      id: number;
      name: string;
    };
    tags?: string;
    is_public: boolean;
    created_at: string;
    updated_at: string;
  };
  className?: string;
}

const ProceedingCard: React.FC<ProceedingCardProps> = ({ proceeding, className = '' }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'low': 'text-green-600',
      'medium': 'text-yellow-600',
      'high': 'text-orange-600',
      'urgent': 'text-red-600',
      'critical': 'text-red-800'
    };
    return colors[priority as keyof typeof colors] || 'text-gray-600';
  };

  const getStatusConfig = (status: string) => {
    const configs = {
      'planning': { label: 'Planning', colors: 'bg-gray-100 text-gray-800' },
      'active': { label: 'Active', colors: 'bg-blue-100 text-blue-800' },
      'suspended': { label: 'Suspended', colors: 'bg-yellow-100 text-yellow-800' },
      'completed': { label: 'Completed', colors: 'bg-green-100 text-green-800' },
      'cancelled': { label: 'Cancelled', colors: 'bg-red-100 text-red-800' },
      'under_review': { label: 'Under Review', colors: 'bg-purple-100 text-purple-800' }
    };
    return configs[status as keyof typeof configs] || { label: status, colors: 'bg-gray-100 text-gray-800' };
  };

  const statusConfig = getStatusConfig(proceeding.status);

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <Link 
              href={`/proceedings/${proceeding.id}`}
              className="text-lg font-semibold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
            >
              {proceeding.name}
            </Link>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              {proceeding.unique_id}
            </p>
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.colors}`}>
              {statusConfig.label}
            </span>
            <span className={`text-xs font-medium ${getPriorityColor(proceeding.priority)}`}>
              {proceeding.priority?.toUpperCase() || 'UNKNOWN'}
            </span>
          </div>
        </div>

        {/* Description */}
        {proceeding.description && (
          <p className="text-gray-600 dark:text-gray-300 text-sm mb-4 line-clamp-2">
            {proceeding.description}
          </p>
        )}

        {/* Objective */}
        <p className="text-gray-700 dark:text-gray-200 text-sm mb-4 line-clamp-3">
          <span className="font-medium">Objective:</span> {proceeding.objective}
        </p>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Progress
            </span>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {proceeding.completed_steps}/{proceeding.total_steps} steps ({Math.round(proceeding.progress_percent)}%)
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${proceeding.progress_percent}%` }}
            ></div>
          </div>
        </div>

        {/* Metadata */}
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center">
            <UserIcon className="h-4 w-4 mr-2" />
            <span>{proceeding.owner.username}</span>
          </div>
          
          {proceeding.agency && (
            <div className="flex items-center">
              <BuildingOfficeIcon className="h-4 w-4 mr-2" />
              <span>{proceeding.agency.name}</span>
            </div>
          )}
          
          <div className="flex items-center">
            <CalendarIcon className="h-4 w-4 mr-2" />
            <span>Started {formatDate(proceeding.initiation_date)}</span>
          </div>
          
          {proceeding.current_step_order && (
            <div className="flex items-center">
              <ChartBarIcon className="h-4 w-4 mr-2" />
              <span>Step {proceeding.current_step_order}</span>
            </div>
          )}
        </div>

        {/* Tags */}
        {proceeding.tags && (
          <div className="mt-4 flex items-center">
            <TagIcon className="h-4 w-4 mr-2 text-gray-400" />
            <div className="flex flex-wrap gap-1">
              {proceeding.tags.split(',').map((tag, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                >
                  {tag.trim()}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
            <ClockIcon className="h-3 w-3 mr-1" />
            <span>Updated {formatDate(proceeding.updated_at)}</span>
          </div>
          
          {proceeding.is_public && (
            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              Public
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProceedingCard;
