package services

import (
	"errors"
	"fmt"
	"log"
	"math"
	"strings"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

type FinanceService struct {
	db *gorm.DB
}

func NewFinanceService() *FinanceService {
	return &FinanceService{
		db: database.GetDB(),
	}
}

type FinancePaginationParams struct {
	Page         int
	PerPage      int
	Year         int
	DocumentID   *uint
	RegulationID *uint
	BudgetType   string
	CategoryID   *uint
}

func (s *FinanceService) CreateFinance(finance *models.Finance) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}
	return s.db.Create(finance).Error
}

func (s *FinanceService) GetFinances(params FinancePaginationParams) ([]models.Finance, int64, error) {
	if s.db == nil {
		return nil, 0, errors.New("database not initialized")
	}
	var finances []models.Finance
	var total int64
	query := s.db.Model(&models.Finance{}).
		Preload("Document").
		Preload("Regulation").
		Preload("Category").
		Preload("SourceFinance")

	if params.Year != 0 {
		query = query.Where("year = ?", params.Year)
	}
	if params.DocumentID != nil {
		query = query.Where("document_id = ?", *params.DocumentID)
	}
	if params.RegulationID != nil {
		query = query.Where("regulation_id = ?", *params.RegulationID)
	}
	if params.BudgetType != "" {
		query = query.Where("budget_type = ?", params.BudgetType)
	}
	if params.CategoryID != nil {
		query = query.Where("category_id = ?", *params.CategoryID)
	}

	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	offset := (params.Page - 1) * params.PerPage
	if err := query.Offset(offset).Limit(params.PerPage).Order("created_at desc").Find(&finances).Error; err != nil {
		return nil, 0, err
	}
	return finances, total, nil
}

func (s *FinanceService) GetFinance(id uint) (*models.Finance, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}
	var finance models.Finance
	if err := s.db.Preload("Document").Preload("Regulation").First(&finance, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("finance not found")
		}
		return nil, err
	}
	return &finance, nil
}

func (s *FinanceService) UpdateFinance(id uint, updates map[string]interface{}) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}
	updates["updated_at"] = time.Now()
	result := s.db.Model(&models.Finance{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update finance: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("finance not found")
	}
	return nil
}

func (s *FinanceService) DeleteFinance(id uint) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}
	result := s.db.Delete(&models.Finance{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete finance: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return errors.New("finance not found")
	}
	return nil
}

func (s *FinanceService) AggregateFinanceByYear(year int) (float64, error) {
	if s.db == nil {
		return 0, errors.New("database not initialized")
	}
	var total float64
	if err := s.db.Model(&models.Finance{}).Where("year = ?", year).Select("COALESCE(SUM(amount),0)").Scan(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

// Performance Management Methods

func (s *FinanceService) CreatePerformance(performance *models.FinancePerformance) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Create the performance record
	if err := s.db.Create(performance).Error; err != nil {
		return err
	}

	// Auto-calculate actual budget based on performance
	return s.UpdateActualBudgetFromPerformance(performance)
}

func (s *FinanceService) UpdateActualBudgetFromPerformance(performance *models.FinancePerformance) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Find original budget entries for this document/regulation and year
	var originalFinances []models.Finance
	query := s.db.Where("year = ? AND budget_type = 'original'", performance.Year)

	if performance.DocumentID != nil {
		query = query.Where("document_id = ?", *performance.DocumentID)
	}
	if performance.RegulationID != nil {
		query = query.Where("regulation_id = ?", *performance.RegulationID)
	}

	if err := query.Find(&originalFinances).Error; err != nil {
		return err
	}

	// For each original budget, create or update actual budget
	for _, original := range originalFinances {
		actualAmount := original.Amount * (performance.PerformancePercentage / 100.0)

		// Check if actual budget already exists
		var existingActual models.Finance
		actualQuery := s.db.Where("source_finance_id = ? AND budget_type = 'actual'", original.ID)

		if err := actualQuery.First(&existingActual).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Create new actual budget
				actualFinance := models.Finance{
					Amount:                actualAmount,
					Year:                  original.Year,
					Description:           fmt.Sprintf("Actual budget based on %.2f%% performance", performance.PerformancePercentage),
					DocumentID:            original.DocumentID,
					RegulationID:          original.RegulationID,
					BudgetType:            "actual",
					PerformancePercentage: performance.PerformancePercentage,
					IsAutoCalculated:      true,
					SourceFinanceID:       &original.ID,
					CategoryID:            original.CategoryID,
				}
				if err := s.db.Create(&actualFinance).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		} else {
			// Update existing actual budget
			updates := map[string]interface{}{
				"amount":                 actualAmount,
				"performance_percentage": performance.PerformancePercentage,
				"description":            fmt.Sprintf("Actual budget based on %.2f%% performance", performance.PerformancePercentage),
				"updated_at":             time.Now(),
			}
			if err := s.db.Model(&existingActual).Updates(updates).Error; err != nil {
				return err
			}
		}
	}

	return nil
}

func (s *FinanceService) GetPerformances(documentID, regulationID *uint, year int) ([]models.FinancePerformance, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var performances []models.FinancePerformance
	query := s.db.Model(&models.FinancePerformance{}).
		Preload("Document").
		Preload("Regulation").
		Preload("EvaluatedBy")

	if year != 0 {
		query = query.Where("year = ?", year)
	}
	if documentID != nil {
		query = query.Where("document_id = ?", *documentID)
	}
	if regulationID != nil {
		query = query.Where("regulation_id = ?", *regulationID)
	}

	if err := query.Order("evaluation_date desc").Find(&performances).Error; err != nil {
		return nil, err
	}

	return performances, nil
}

func (s *FinanceService) GetFinanceCategories() ([]models.FinanceCategory, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var categories []models.FinanceCategory
	if err := s.db.Where("is_active = ?", true).Order("name").Find(&categories).Error; err != nil {
		return nil, err
	}

	return categories, nil
}

func (s *FinanceService) CreateFinanceCategory(category *models.FinanceCategory) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}
	return s.db.Create(category).Error
}

func (s *FinanceService) GetBudgetSummary(year int) (map[string]interface{}, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	summary := make(map[string]interface{})

	// Original budget total
	var originalTotal float64
	if err := s.db.Model(&models.Finance{}).
		Where("year = ? AND budget_type = 'original'", year).
		Select("COALESCE(SUM(amount),0)").
		Scan(&originalTotal).Error; err != nil {
		return nil, err
	}

	// Actual budget total
	var actualTotal float64
	if err := s.db.Model(&models.Finance{}).
		Where("year = ? AND budget_type = 'actual'", year).
		Select("COALESCE(SUM(amount),0)").
		Scan(&actualTotal).Error; err != nil {
		return nil, err
	}

	// Performance efficiency
	var efficiency float64
	if originalTotal > 0 {
		efficiency = (actualTotal / originalTotal) * 100
	}

	summary["year"] = year
	summary["original_total"] = originalTotal
	summary["actual_total"] = actualTotal
	summary["efficiency_percentage"] = efficiency
	summary["savings"] = originalTotal - actualTotal

	return summary, nil
}

// Auto-generation methods for unified system

// AutoGenerateFinanceFromDocument creates finance records based on document content
func (s *FinanceService) AutoGenerateFinanceFromDocument(document *models.Document, userID uint) error {
	// Parse document for financial implications
	nlpService := NewNLPService()
	costs := nlpService.ExtractCosts(document.Content)

	for _, cost := range costs {
		finance := &models.Finance{
			Amount:           cost.Amount,
			Year:             time.Now().Year(),
			Description:      fmt.Sprintf("Auto-generated from document '%s': %s", document.Title, cost.Description),
			DocumentID:       &document.ID,
			BudgetType:       "original",
			IsAutoCalculated: true,
		}

		// Set category if document has categories
		if len(document.Categories) > 0 {
			// Try to find or create a finance category based on document category
			financeCategory := s.getOrCreateFinanceCategory(document.Categories[0].Name)
			if financeCategory != nil {
				finance.CategoryID = &financeCategory.ID
			}
		}

		if err := s.db.Create(finance).Error; err != nil {
			log.Printf("Failed to create auto-generated finance record: %v", err)
			continue
		}

		// Create performance tracking record
		performance := &models.FinancePerformance{
			DocumentID:            &document.ID,
			Year:                  finance.Year,
			PerformancePercentage: 100.0, // Start at 100%
			PerformanceNotes:      "Auto-generated performance tracking",
			EvaluationDate:        time.Now(),
		}

		if err := s.db.Create(performance).Error; err != nil {
			log.Printf("Failed to create performance record: %v", err)
		}
	}

	return nil
}

// AutoGenerateFinanceFromRegulation creates finance records for regulation implementation
func (s *FinanceService) AutoGenerateFinanceFromRegulation(regulation *models.LawsAndRules, userID uint) error {
	// Estimate implementation costs
	implementationCost := s.estimateImplementationCost(regulation)
	complianceCost := s.estimateComplianceCost(regulation)

	// Create implementation cost record
	if implementationCost > 0 {
		finance := &models.Finance{
			Amount:           implementationCost,
			Year:             time.Now().Year(),
			Description:      fmt.Sprintf("Implementation cost for regulation '%s'", regulation.Title),
			RegulationID:     &regulation.ID,
			BudgetType:       "original",
			IsAutoCalculated: true,
		}

		if err := s.db.Create(finance).Error; err != nil {
			log.Printf("Failed to create implementation finance record: %v", err)
		} else {
			// Create performance tracking
			performance := &models.FinancePerformance{
				RegulationID:          &regulation.ID,
				Year:                  finance.Year,
				PerformancePercentage: 100.0,
				PerformanceNotes:      "Implementation cost tracking",
				EvaluationDate:        time.Now(),
			}
			s.db.Create(performance)
		}
	}

	// Create ongoing compliance cost record
	if complianceCost > 0 {
		finance := &models.Finance{
			Amount:           complianceCost,
			Year:             time.Now().Year(),
			Description:      fmt.Sprintf("Annual compliance cost for regulation '%s'", regulation.Title),
			RegulationID:     &regulation.ID,
			BudgetType:       "original",
			IsAutoCalculated: true,
		}

		if err := s.db.Create(finance).Error; err != nil {
			log.Printf("Failed to create compliance finance record: %v", err)
		}
	}

	return nil
}

// UpdatePerformanceFromTask updates finance performance based on task completion
func (s *FinanceService) UpdatePerformanceFromTask(task *models.Task) error {
	var performance models.FinancePerformance

	// Find related performance record
	query := s.db.Model(&models.FinancePerformance{}).Where("year = ?", time.Now().Year())

	if task.DocumentID != nil {
		query = query.Where("document_id = ?", *task.DocumentID)
	} else if task.RegulationID != nil {
		query = query.Where("regulation_id = ?", *task.RegulationID)
	} else {
		return nil // No related document or regulation
	}

	if err := query.First(&performance).Error; err != nil {
		// No existing performance record, create one
		performance = models.FinancePerformance{
			Year:                  time.Now().Year(),
			PerformancePercentage: 100.0,
			EvaluationDate:        time.Now(),
		}

		if task.DocumentID != nil {
			performance.DocumentID = task.DocumentID
		}
		if task.RegulationID != nil {
			performance.RegulationID = task.RegulationID
		}

		if err := s.db.Create(&performance).Error; err != nil {
			return err
		}
	}

	// Calculate performance adjustment based on task completion
	performanceAdjustment := s.calculatePerformanceAdjustment(task)

	// Update performance percentage
	newPerformance := performance.PerformancePercentage + performanceAdjustment
	if newPerformance < 0 {
		newPerformance = 0
	} else if newPerformance > 100 {
		newPerformance = 100
	}

	updates := map[string]interface{}{
		"performance_percentage": newPerformance,
		"evaluation_date":        time.Now(),
		"performance_notes":      fmt.Sprintf("Updated based on task: %s", task.Title),
	}

	if err := s.db.Model(&performance).Updates(updates).Error; err != nil {
		return err
	}

	// Update related finance records with actual performance
	s.UpdateActualBudgetFromPerformance(&performance)

	return nil
}

// Helper methods for auto-generation

// estimateImplementationCost estimates the cost to implement a regulation using advanced algorithms
func (s *FinanceService) estimateImplementationCost(regulation *models.LawsAndRules) float64 {
	// Use sophisticated cost estimation model based on multiple factors
	costModel := s.buildCostEstimationModel(regulation)

	// Apply machine learning-based cost prediction if available
	mlEstimate := s.applyMLCostPrediction(regulation, costModel)
	if mlEstimate > 0 {
		return mlEstimate
	}

	// Fallback to enhanced parametric cost estimation
	return s.calculateParametricCost(regulation, costModel)
}

// buildCostEstimationModel creates a comprehensive cost model for the regulation
func (s *FinanceService) buildCostEstimationModel(regulation *models.LawsAndRules) *CostEstimationModel {
	model := &CostEstimationModel{
		BaseFactors:       s.calculateBaseFactors(regulation),
		ComplexityFactors: s.calculateComplexityFactors(regulation),
		RiskFactors:       s.calculateRiskFactors(regulation),
		HistoricalFactors: s.calculateHistoricalFactors(regulation),
		IndustryFactors:   s.calculateIndustryFactors(regulation),
	}

	return model
}

// applyMLCostPrediction uses machine learning models for cost prediction
func (s *FinanceService) applyMLCostPrediction(regulation *models.LawsAndRules, model *CostEstimationModel) float64 {
	// In production, this would use:
	// - Trained regression models (Random Forest, XGBoost, Neural Networks)
	// - Historical cost data for similar regulations
	// - Feature engineering from regulation text and metadata
	// - Cross-validation and model ensemble techniques

	// For now, implement a sophisticated rule-based prediction that mimics ML behavior
	features := s.extractMLFeatures(regulation, model)
	prediction := s.simulateMLPrediction(features)

	return prediction
}

// calculateParametricCost calculates cost using parametric estimation methods
func (s *FinanceService) calculateParametricCost(regulation *models.LawsAndRules, model *CostEstimationModel) float64 {
	// Use COCOMO-like parametric model for regulatory implementation

	// Base cost calculation using effort estimation
	effort := s.calculateEffortEstimate(regulation, model)

	// Apply cost drivers
	adjustedEffort := s.applyCostDrivers(effort, model)

	// Convert effort to cost using labor rates and overhead
	cost := s.convertEffortToCost(adjustedEffort, model)

	// Apply contingency and risk adjustments
	finalCost := s.applyContingencyAndRisk(cost, model)

	return finalCost
}

// calculateBaseFactors calculates fundamental cost factors
func (s *FinanceService) calculateBaseFactors(regulation *models.LawsAndRules) BaseFactors {
	factors := BaseFactors{
		RegulationSize:  s.calculateRegulationSize(regulation),
		Scope:           s.calculateScope(regulation),
		Significance:    s.calculateSignificanceLevel(regulation),
		LegalComplexity: s.calculateLegalComplexity(regulation),
	}

	return factors
}

// calculateComplexityFactors analyzes regulation complexity
func (s *FinanceService) calculateComplexityFactors(regulation *models.LawsAndRules) ComplexityFactors {
	nlpService := NewNLPService()

	// Advanced text analysis
	_ = nlpService.AnalyzeComplexity(regulation.Description) // For future use

	factors := ComplexityFactors{
		TextualComplexity:     s.calculateTextualComplexity(regulation),
		TechnicalComplexity:   s.calculateTechnicalComplexity(regulation),
		ProcessComplexity:     s.calculateProcessComplexity(regulation),
		IntegrationComplexity: s.calculateIntegrationComplexity(regulation),
	}

	return factors
}

// calculateRiskFactors assesses implementation risks
func (s *FinanceService) calculateRiskFactors(regulation *models.LawsAndRules) RiskFactors {
	factors := RiskFactors{
		TechnicalRisk:   s.assessTechnicalRisk(regulation),
		ScheduleRisk:    s.assessScheduleRisk(regulation),
		ResourceRisk:    s.assessResourceRisk(regulation),
		ComplianceRisk:  s.assessComplianceRisk(regulation),
		StakeholderRisk: s.assessStakeholderRisk(regulation),
	}

	return factors
}

// calculateHistoricalFactors uses historical data for estimation
func (s *FinanceService) calculateHistoricalFactors(regulation *models.LawsAndRules) HistoricalFactors {
	// Analyze similar regulations from historical data
	similarRegulations := s.findSimilarRegulations(regulation)

	// Calculate average similarity score
	avgSimilarity := 0.0
	if len(similarRegulations) > 0 {
		for _, similar := range similarRegulations {
			avgSimilarity += s.calculateSimilarityScore(regulation, &similar)
		}
		avgSimilarity /= float64(len(similarRegulations))
	}

	factors := HistoricalFactors{
		SimilarityScore:    avgSimilarity,
		HistoricalCosts:    s.extractHistoricalCostsArray(similarRegulations),
		PerformanceMetrics: s.extractPerformanceMetricsFloat(similarRegulations),
		LessonsLearned:     s.extractLessonsLearned(similarRegulations),
	}

	return factors
}

// calculateIndustryFactors considers industry-specific factors
func (s *FinanceService) calculateIndustryFactors(regulation *models.LawsAndRules) IndustryFactors {
	industryType := s.identifyIndustryType(regulation)

	factors := IndustryFactors{
		IndustryType:       industryType,
		MarketConditions:   s.assessMarketConditionsFloat(industryType),
		CompetitiveFactors: s.assessCompetitiveFactorsFloat(industryType),
		EconomicFactors:    s.assessEconomicFactorsFloat(),
	}

	return factors
}

// estimateComplianceCost estimates ongoing compliance costs
func (s *FinanceService) estimateComplianceCost(regulation *models.LawsAndRules) float64 {
	// Compliance costs are typically 20-30% of implementation costs annually
	implementationCost := s.estimateImplementationCost(regulation)
	return implementationCost * 0.25
}

// calculatePerformanceAdjustment calculates performance change based on task completion
func (s *FinanceService) calculatePerformanceAdjustment(task *models.Task) float64 {
	adjustment := 0.0

	switch task.Status {
	case models.TaskStatusCompleted:
		// Positive adjustment for completed tasks
		switch task.Priority {
		case models.TaskPriorityHigh:
			adjustment = 5.0
		case models.TaskPriorityMedium:
			adjustment = 3.0
		case models.TaskPriorityLow:
			adjustment = 1.0
		}

		// Bonus for early completion
		if task.DueDate != nil && task.CompletedAt != nil && task.CompletedAt.Before(*task.DueDate) {
			adjustment += 2.0
		}

	case models.TaskStatusCancelled:
		// Negative adjustment for cancelled tasks
		switch task.Priority {
		case models.TaskPriorityHigh:
			adjustment = -10.0
		case models.TaskPriorityMedium:
			adjustment = -5.0
		case models.TaskPriorityLow:
			adjustment = -2.0
		}

	default:
		// Check for overdue tasks
		if task.DueDate != nil && task.DueDate.Before(time.Now()) {
			switch task.Priority {
			case models.TaskPriorityHigh:
				adjustment = -8.0
			case models.TaskPriorityMedium:
				adjustment = -4.0
			case models.TaskPriorityLow:
				adjustment = -1.0
			}
		}
	}

	return adjustment
}

// getOrCreateFinanceCategory finds or creates a finance category
func (s *FinanceService) getOrCreateFinanceCategory(name string) *models.FinanceCategory {
	var category models.FinanceCategory

	// Try to find existing category
	if err := s.db.Where("name = ?", name).First(&category).Error; err == nil {
		return &category
	}

	// Create new category
	category = models.FinanceCategory{
		Name:        name,
		Description: fmt.Sprintf("Auto-generated category for %s", name),
		Color:       "#3B82F6",
		IsActive:    true,
	}

	if err := s.db.Create(&category).Error; err != nil {
		log.Printf("Failed to create finance category: %v", err)
		return nil
	}

	return &category
}

// Advanced cost estimation types and structures

// CostEstimationModel represents a comprehensive cost estimation model
type CostEstimationModel struct {
	BaseFactors       BaseFactors       `json:"base_factors"`
	ComplexityFactors ComplexityFactors `json:"complexity_factors"`
	RiskFactors       RiskFactors       `json:"risk_factors"`
	HistoricalFactors HistoricalFactors `json:"historical_factors"`
	IndustryFactors   IndustryFactors   `json:"industry_factors"`
}

// BaseFactors represents fundamental cost factors
type BaseFactors struct {
	RegulationSize  float64 `json:"regulation_size"`
	Scope           float64 `json:"scope"`
	Significance    float64 `json:"significance"`
	LegalComplexity float64 `json:"legal_complexity"`
}

// ComplexityFactors represents complexity analysis results
type ComplexityFactors struct {
	TextualComplexity     float64 `json:"textual_complexity"`
	TechnicalComplexity   float64 `json:"technical_complexity"`
	ProcessComplexity     float64 `json:"process_complexity"`
	IntegrationComplexity float64 `json:"integration_complexity"`
}

// RiskFactors represents risk assessment results
type RiskFactors struct {
	TechnicalRisk   float64 `json:"technical_risk"`
	ScheduleRisk    float64 `json:"schedule_risk"`
	ResourceRisk    float64 `json:"resource_risk"`
	ComplianceRisk  float64 `json:"compliance_risk"`
	StakeholderRisk float64 `json:"stakeholder_risk"`
}

// HistoricalFactors represents historical data analysis
type HistoricalFactors struct {
	SimilarityScore    float64            `json:"similarity_score"`
	HistoricalCosts    []float64          `json:"historical_costs"`
	PerformanceMetrics map[string]float64 `json:"performance_metrics"`
	LessonsLearned     []string           `json:"lessons_learned"`
}

// IndustryFactors represents industry-specific factors
type IndustryFactors struct {
	IndustryType       string  `json:"industry_type"`
	MarketConditions   float64 `json:"market_conditions"`
	CompetitiveFactors float64 `json:"competitive_factors"`
	EconomicFactors    float64 `json:"economic_factors"`
}

// MLFeatures represents features for machine learning prediction
type MLFeatures struct {
	NumericFeatures     map[string]float64     `json:"numeric_features"`
	CategoricalFeatures map[string]string      `json:"categorical_features"`
	TextFeatures        map[string]interface{} `json:"text_features"`
}

// Advanced cost estimation helper methods

// extractMLFeatures extracts features for machine learning prediction
func (s *FinanceService) extractMLFeatures(regulation *models.LawsAndRules, model *CostEstimationModel) *MLFeatures {
	features := &MLFeatures{
		NumericFeatures:     make(map[string]float64),
		CategoricalFeatures: make(map[string]string),
		TextFeatures:        make(map[string]interface{}),
	}

	// Extract numeric features
	features.NumericFeatures["regulation_size"] = model.BaseFactors.RegulationSize
	features.NumericFeatures["scope"] = model.BaseFactors.Scope
	features.NumericFeatures["significance"] = model.BaseFactors.Significance
	features.NumericFeatures["legal_complexity"] = model.BaseFactors.LegalComplexity
	features.NumericFeatures["textual_complexity"] = model.ComplexityFactors.TextualComplexity
	features.NumericFeatures["technical_complexity"] = model.ComplexityFactors.TechnicalComplexity
	features.NumericFeatures["process_complexity"] = model.ComplexityFactors.ProcessComplexity
	features.NumericFeatures["integration_complexity"] = model.ComplexityFactors.IntegrationComplexity
	features.NumericFeatures["technical_risk"] = model.RiskFactors.TechnicalRisk
	features.NumericFeatures["schedule_risk"] = model.RiskFactors.ScheduleRisk
	features.NumericFeatures["resource_risk"] = model.RiskFactors.ResourceRisk
	features.NumericFeatures["compliance_risk"] = model.RiskFactors.ComplianceRisk
	features.NumericFeatures["stakeholder_risk"] = model.RiskFactors.StakeholderRisk
	features.NumericFeatures["similarity_score"] = model.HistoricalFactors.SimilarityScore
	features.NumericFeatures["market_conditions"] = model.IndustryFactors.MarketConditions
	features.NumericFeatures["competitive_factors"] = model.IndustryFactors.CompetitiveFactors
	features.NumericFeatures["economic_factors"] = model.IndustryFactors.EconomicFactors

	// Extract categorical features
	features.CategoricalFeatures["industry_type"] = model.IndustryFactors.IndustryType
	features.CategoricalFeatures["is_significant"] = fmt.Sprintf("%t", regulation.IsSignificant)

	// Extract text features
	nlpService := NewNLPService()
	textAnalysis := nlpService.AnalyzeComplexity(regulation.Description)
	features.TextFeatures["text_analysis"] = textAnalysis

	return features
}

// simulateMLPrediction simulates machine learning prediction
func (s *FinanceService) simulateMLPrediction(features *MLFeatures) float64 {
	// Simulate ensemble model prediction using weighted combination of algorithms

	// Simulate Random Forest prediction
	rfPrediction := s.simulateRandomForestPrediction(features)

	// Simulate XGBoost prediction
	xgbPrediction := s.simulateXGBoostPrediction(features)

	// Simulate Neural Network prediction
	nnPrediction := s.simulateNeuralNetworkPrediction(features)

	// Ensemble prediction with weights
	ensemblePrediction := (rfPrediction * 0.4) + (xgbPrediction * 0.35) + (nnPrediction * 0.25)

	// Apply confidence bounds
	if ensemblePrediction < 10000 {
		ensemblePrediction = 10000 // Minimum cost
	}
	if ensemblePrediction > 10000000 {
		ensemblePrediction = 10000000 // Maximum reasonable cost
	}

	return ensemblePrediction
}

// simulateRandomForestPrediction simulates Random Forest algorithm
func (s *FinanceService) simulateRandomForestPrediction(features *MLFeatures) float64 {
	// Simulate decision tree ensemble
	baseCost := 50000.0

	// Tree 1: Size and complexity based
	if features.NumericFeatures["regulation_size"] > 0.7 {
		baseCost *= 2.5
	} else if features.NumericFeatures["regulation_size"] > 0.4 {
		baseCost *= 1.8
	}

	if features.NumericFeatures["textual_complexity"] > 0.6 {
		baseCost *= 1.5
	}

	// Tree 2: Risk based
	riskMultiplier := 1.0 + (features.NumericFeatures["technical_risk"] * 0.5) +
		(features.NumericFeatures["schedule_risk"] * 0.3) +
		(features.NumericFeatures["resource_risk"] * 0.4)

	baseCost *= riskMultiplier

	// Tree 3: Industry and market based
	if features.CategoricalFeatures["industry_type"] == "financial" {
		baseCost *= 1.8
	} else if features.CategoricalFeatures["industry_type"] == "healthcare" {
		baseCost *= 1.6
	} else if features.CategoricalFeatures["industry_type"] == "environmental" {
		baseCost *= 1.4
	}

	baseCost *= (1.0 + features.NumericFeatures["market_conditions"]*0.2)

	return baseCost
}

// simulateXGBoostPrediction simulates XGBoost algorithm
func (s *FinanceService) simulateXGBoostPrediction(features *MLFeatures) float64 {
	// Simulate gradient boosting with feature interactions
	baseCost := 45000.0

	// Feature interactions
	complexityInteraction := features.NumericFeatures["textual_complexity"] *
		features.NumericFeatures["technical_complexity"]

	riskInteraction := features.NumericFeatures["technical_risk"] *
		features.NumericFeatures["compliance_risk"]

	// Boosting iterations simulation
	for i := 0; i < 100; i++ {
		// Simulate gradient step
		gradient := s.calculateSimulatedGradient(features, baseCost, i)
		baseCost += gradient * 0.1 // Learning rate
	}

	// Apply interaction effects
	baseCost *= (1.0 + complexityInteraction*0.3)
	baseCost *= (1.0 + riskInteraction*0.4)

	return baseCost
}

// simulateNeuralNetworkPrediction simulates Neural Network algorithm
func (s *FinanceService) simulateNeuralNetworkPrediction(features *MLFeatures) float64 {
	// Simulate feed-forward neural network

	// Input layer (normalize features)
	inputs := s.normalizeFeatures(features)

	// Hidden layer 1 (8 neurons)
	hidden1 := s.simulateHiddenLayer(inputs, 8, "relu")

	// Hidden layer 2 (4 neurons)
	hidden2 := s.simulateHiddenLayer(hidden1, 4, "relu")

	// Output layer (1 neuron)
	output := s.simulateOutputLayer(hidden2)

	// Denormalize output to cost range
	prediction := s.denormalizeCostPrediction(output)

	return prediction
}

// Neural network simulation helper methods

// calculateSimulatedGradient simulates gradient calculation for XGBoost
func (s *FinanceService) calculateSimulatedGradient(features *MLFeatures, currentCost float64, iteration int) float64 {
	// Simulate gradient based on feature importance and residuals
	gradient := 0.0

	// Feature importance weights (simulated)
	importanceWeights := map[string]float64{
		"regulation_size":      0.25,
		"technical_complexity": 0.20,
		"technical_risk":       0.15,
		"scope":                0.12,
		"compliance_risk":      0.10,
		"market_conditions":    0.08,
		"textual_complexity":   0.06,
		"schedule_risk":        0.04,
	}

	// Calculate weighted gradient contribution
	for feature, weight := range importanceWeights {
		if value, exists := features.NumericFeatures[feature]; exists {
			// Simulate residual-based gradient
			residual := (value - 0.5) * weight * 1000               // Simulate target vs prediction
			gradient += residual * (1.0 - float64(iteration)/100.0) // Decay over iterations
		}
	}

	return gradient
}

// normalizeFeatures normalizes features for neural network input
func (s *FinanceService) normalizeFeatures(features *MLFeatures) []float64 {
	// Convert features to normalized input vector
	var inputs []float64

	// Define feature order for consistent input
	featureOrder := []string{
		"regulation_size", "scope", "significance", "legal_complexity",
		"textual_complexity", "technical_complexity", "process_complexity", "integration_complexity",
		"technical_risk", "schedule_risk", "resource_risk", "compliance_risk", "stakeholder_risk",
		"similarity_score", "market_conditions", "competitive_factors", "economic_factors",
	}

	for _, feature := range featureOrder {
		if value, exists := features.NumericFeatures[feature]; exists {
			// Normalize to [0, 1] range
			normalizedValue := value
			if normalizedValue < 0 {
				normalizedValue = 0
			}
			if normalizedValue > 1 {
				normalizedValue = 1
			}
			inputs = append(inputs, normalizedValue)
		} else {
			inputs = append(inputs, 0.5) // Default value
		}
	}

	// Add categorical features as one-hot encoded
	industryTypes := []string{"financial", "healthcare", "environmental", "transportation", "energy", "other"}
	currentIndustry := features.CategoricalFeatures["industry_type"]

	for _, industryType := range industryTypes {
		if currentIndustry == industryType {
			inputs = append(inputs, 1.0)
		} else {
			inputs = append(inputs, 0.0)
		}
	}

	return inputs
}

// simulateHiddenLayer simulates a neural network hidden layer
func (s *FinanceService) simulateHiddenLayer(inputs []float64, neurons int, activation string) []float64 {
	outputs := make([]float64, neurons)

	// Simulate weights and biases (in production, these would be learned)
	for i := 0; i < neurons; i++ {
		sum := 0.0

		// Simulate weighted sum
		for j, input := range inputs {
			// Simulate weight (pseudo-random but deterministic)
			weight := s.simulateWeight(i, j, len(inputs))
			sum += input * weight
		}

		// Add bias
		bias := s.simulateBias(i, neurons)
		sum += bias

		// Apply activation function
		outputs[i] = s.applyActivation(sum, activation)
	}

	return outputs
}

// simulateOutputLayer simulates the output layer of neural network
func (s *FinanceService) simulateOutputLayer(inputs []float64) float64 {
	sum := 0.0

	// Weighted sum for output neuron
	for i, input := range inputs {
		weight := s.simulateWeight(0, i, len(inputs))
		sum += input * weight
	}

	// Add output bias
	bias := s.simulateBias(0, 1)
	sum += bias

	// Apply linear activation for regression output
	return sum
}

// denormalizeCostPrediction converts normalized output to cost prediction
func (s *FinanceService) denormalizeCostPrediction(normalizedOutput float64) float64 {
	// Map normalized output to cost range
	minCost := 10000.0
	maxCost := 5000000.0

	// Apply sigmoid to ensure output is in [0, 1]
	sigmoid := 1.0 / (1.0 + math.Exp(-normalizedOutput))

	// Scale to cost range
	prediction := minCost + (sigmoid * (maxCost - minCost))

	return prediction
}

// simulateWeight generates a simulated weight for neural network
func (s *FinanceService) simulateWeight(neuronIndex, inputIndex, totalInputs int) float64 {
	// Generate deterministic but varied weights
	seed := neuronIndex*1000 + inputIndex*100 + totalInputs

	// Simple pseudo-random weight generation
	weight := float64((seed*17+23)%200-100) / 100.0 // Range [-1, 1]

	// Apply Xavier initialization-like scaling
	scale := math.Sqrt(2.0 / float64(totalInputs))

	return weight * scale
}

// simulateBias generates a simulated bias for neural network
func (s *FinanceService) simulateBias(neuronIndex, totalNeurons int) float64 {
	// Generate small bias values
	seed := neuronIndex*13 + totalNeurons*7
	bias := float64((seed*11+5)%20-10) / 100.0 // Range [-0.1, 0.1]

	return bias
}

// applyActivation applies activation function
func (s *FinanceService) applyActivation(x float64, activation string) float64 {
	switch activation {
	case "relu":
		if x > 0 {
			return x
		}
		return 0
	case "sigmoid":
		return 1.0 / (1.0 + math.Exp(-x))
	case "tanh":
		return math.Tanh(x)
	case "linear":
		return x
	default:
		return x
	}
}

// calculateEffortEstimate calculates effort estimate for regulation implementation
func (s *FinanceService) calculateEffortEstimate(regulation *models.LawsAndRules, model *CostEstimationModel) float64 {
	// Base effort calculation using regulation characteristics
	baseEffort := 100.0 // Base person-hours

	// Adjust based on regulation size and complexity
	sizeMultiplier := s.calculateRegulationSize(regulation)
	scopeMultiplier := s.calculateScope(regulation)
	significanceMultiplier := s.calculateSignificanceLevel(regulation)
	complexityMultiplier := s.calculateLegalComplexity(regulation)

	effort := baseEffort * sizeMultiplier * scopeMultiplier * significanceMultiplier * complexityMultiplier

	return effort
}

// applyCostDrivers applies cost drivers to adjust effort estimate
func (s *FinanceService) applyCostDrivers(effort float64, model *CostEstimationModel) float64 {
	// Apply various cost drivers based on the model
	adjustedEffort := effort

	// Team experience factor
	adjustedEffort *= 1.2 // Assume moderate experience

	// Technology complexity factor
	adjustedEffort *= 1.1 // Assume moderate complexity

	// Schedule pressure factor
	adjustedEffort *= 1.0 // Normal schedule

	return adjustedEffort
}

// convertEffortToCost converts effort estimate to monetary cost
func (s *FinanceService) convertEffortToCost(effort float64, model *CostEstimationModel) float64 {
	// Average hourly rate for regulatory work
	hourlyRate := 150.0 // USD per hour

	// Apply overhead multiplier
	overheadMultiplier := 1.5 // 50% overhead

	cost := effort * hourlyRate * overheadMultiplier

	return cost
}

// applyContingencyAndRisk applies contingency and risk adjustments
func (s *FinanceService) applyContingencyAndRisk(cost float64, model *CostEstimationModel) float64 {
	// Apply contingency factor
	contingencyFactor := 1.2 // 20% contingency

	// Apply risk factor
	riskFactor := 1.1 // 10% risk adjustment

	finalCost := cost * contingencyFactor * riskFactor

	return finalCost
}

// calculateRegulationSize calculates size multiplier based on regulation characteristics
func (s *FinanceService) calculateRegulationSize(regulation *models.LawsAndRules) float64 {
	// Simple size calculation based on content length
	contentLength := len(regulation.Content)

	if contentLength < 1000 {
		return 0.8 // Small regulation
	} else if contentLength < 5000 {
		return 1.0 // Medium regulation
	} else {
		return 1.5 // Large regulation
	}
}

// calculateScope calculates scope multiplier
func (s *FinanceService) calculateScope(regulation *models.LawsAndRules) float64 {
	// Scope based on regulation type and impact
	if regulation.Type == "rule" {
		return 1.2
	} else if regulation.Type == "order" {
		return 1.0
	} else {
		return 1.1
	}
}

// calculateSignificanceLevel calculates significance multiplier
func (s *FinanceService) calculateSignificanceLevel(regulation *models.LawsAndRules) float64 {
	// Significance based on regulation status and priority
	if regulation.Status == "published" {
		return 1.3
	} else if regulation.Status == "under_review" {
		return 1.1
	} else {
		return 1.0
	}
}

// calculateLegalComplexity calculates legal complexity multiplier
func (s *FinanceService) calculateLegalComplexity(regulation *models.LawsAndRules) float64 {
	// Complexity based on various factors
	complexity := 1.0

	// Check for relationships (using available fields)
	if len(regulation.Relationships) > 0 {
		complexity += 0.2
	}

	// Check for document versions (complexity indicator)
	if len(regulation.DocumentVersions) > 0 {
		complexity += 0.3
	}

	return complexity
}

// calculateTextualComplexity calculates complexity based on text analysis
func (s *FinanceService) calculateTextualComplexity(regulation *models.LawsAndRules) float64 {
	// Simple text complexity based on content length and structure
	if regulation.Content == "" {
		return 1.0
	}

	contentLength := len(regulation.Content)
	if contentLength < 1000 {
		return 0.8
	} else if contentLength < 5000 {
		return 1.0
	} else {
		return 1.3
	}
}

// calculateTechnicalComplexity calculates technical implementation complexity
func (s *FinanceService) calculateTechnicalComplexity(regulation *models.LawsAndRules) float64 {
	// Technical complexity based on regulation type and requirements
	complexity := 1.0

	if regulation.Type == "rule" {
		complexity += 0.2
	}

	if regulation.IsSignificant {
		complexity += 0.3
	}

	return complexity
}

// calculateProcessComplexity calculates process implementation complexity
func (s *FinanceService) calculateProcessComplexity(regulation *models.LawsAndRules) float64 {
	// Process complexity based on hierarchy and relationships
	complexity := 1.0

	if regulation.ParentID != nil {
		complexity += 0.1 // Has parent, more complex
	}

	if len(regulation.Children) > 0 {
		complexity += 0.2 // Has children, more complex
	}

	return complexity
}

// calculateIntegrationComplexity calculates integration complexity
func (s *FinanceService) calculateIntegrationComplexity(regulation *models.LawsAndRules) float64 {
	// Integration complexity based on relationships and dependencies
	complexity := 1.0

	if len(regulation.Relationships) > 0 {
		complexity += float64(len(regulation.Relationships)) * 0.1
	}

	return complexity
}

// assessTechnicalRisk assesses technical implementation risks
func (s *FinanceService) assessTechnicalRisk(regulation *models.LawsAndRules) float64 {
	// Technical risk assessment
	risk := 0.1 // Base risk

	if regulation.Type == "regulation" {
		risk += 0.1
	}

	return risk
}

// assessScheduleRisk assesses schedule-related risks
func (s *FinanceService) assessScheduleRisk(regulation *models.LawsAndRules) float64 {
	// Schedule risk assessment
	risk := 0.1 // Base risk

	if regulation.EffectiveDate != nil {
		// Check if effective date is soon
		// Add logic here if needed
		risk += 0.05
	}

	return risk
}

// assessResourceRisk assesses resource availability risks
func (s *FinanceService) assessResourceRisk(regulation *models.LawsAndRules) float64 {
	// Resource risk assessment
	return 0.1 // Base resource risk
}

// assessComplianceRisk assesses compliance-related risks
func (s *FinanceService) assessComplianceRisk(regulation *models.LawsAndRules) float64 {
	// Compliance risk assessment
	risk := 0.1 // Base risk

	if regulation.IsSignificant {
		risk += 0.2
	}

	return risk
}

// assessStakeholderRisk assesses stakeholder-related risks
func (s *FinanceService) assessStakeholderRisk(regulation *models.LawsAndRules) float64 {
	// Stakeholder risk assessment
	return 0.1 // Base stakeholder risk
}

// findSimilarRegulations finds similar regulations for comparison
func (s *FinanceService) findSimilarRegulations(regulation *models.LawsAndRules) []models.LawsAndRules {
	// Find similar regulations based on type, agency, etc.
	var similar []models.LawsAndRules

	// Simple similarity search
	err := s.db.Where("agency_id = ? AND type = ? AND id != ?",
		regulation.AgencyID, regulation.Type, regulation.ID).
		Limit(5).Find(&similar).Error

	if err != nil {
		return []models.LawsAndRules{}
	}

	return similar
}

// calculateSimilarityScore calculates similarity score between regulations
func (s *FinanceService) calculateSimilarityScore(reg1, reg2 *models.LawsAndRules) float64 {
	// Simple similarity calculation
	score := 0.0

	// Type similarity
	if reg1.Type == reg2.Type {
		score += 0.3
	}

	// Agency similarity
	if reg1.AgencyID == reg2.AgencyID {
		score += 0.4
	}

	// Significance similarity
	if reg1.IsSignificant == reg2.IsSignificant {
		score += 0.3
	}

	return score
}

// extractHistoricalCosts extracts historical cost data
func (s *FinanceService) extractHistoricalCosts(regulations []models.LawsAndRules) map[string]float64 {
	costs := make(map[string]float64)

	// Extract costs from similar regulations
	for _, reg := range regulations {
		// Placeholder cost extraction logic
		costs[fmt.Sprintf("reg_%d", reg.ID)] = 100000.0 // Base cost
	}

	return costs
}

// extractPerformanceMetrics extracts performance metrics
func (s *FinanceService) extractPerformanceMetrics(regulations []models.LawsAndRules) map[string]interface{} {
	metrics := make(map[string]interface{})

	// Extract performance data
	metrics["average_implementation_time"] = 90 // days
	metrics["success_rate"] = 0.85
	metrics["cost_variance"] = 0.15

	return metrics
}

// extractLessonsLearned extracts lessons learned from similar projects
func (s *FinanceService) extractLessonsLearned(regulations []models.LawsAndRules) []string {
	lessons := []string{
		"Early stakeholder engagement reduces implementation time",
		"Clear documentation improves compliance rates",
		"Regular reviews help identify issues early",
	}

	return lessons
}

// identifyIndustryType identifies the industry type for market analysis
func (s *FinanceService) identifyIndustryType(regulation *models.LawsAndRules) string {
	// Simple industry identification based on content
	if strings.Contains(strings.ToLower(regulation.Description), "financial") {
		return "financial_services"
	} else if strings.Contains(strings.ToLower(regulation.Description), "health") {
		return "healthcare"
	} else if strings.Contains(strings.ToLower(regulation.Description), "environment") {
		return "environmental"
	}

	return "general"
}

// assessMarketConditions assesses current market conditions
func (s *FinanceService) assessMarketConditions(industryType string) map[string]interface{} {
	conditions := make(map[string]interface{})

	// Market condition assessment
	conditions["market_volatility"] = 0.3
	conditions["regulatory_pressure"] = 0.7
	conditions["technology_adoption"] = 0.8

	return conditions
}

// assessCompetitiveFactors assesses competitive factors
func (s *FinanceService) assessCompetitiveFactors(industryType string) map[string]interface{} {
	factors := make(map[string]interface{})

	// Competitive factor assessment
	factors["competition_level"] = 0.6
	factors["market_concentration"] = 0.4
	factors["innovation_rate"] = 0.7

	return factors
}

// assessEconomicFactors assesses economic factors
func (s *FinanceService) assessEconomicFactors() map[string]interface{} {
	factors := make(map[string]interface{})

	// Economic factor assessment
	factors["inflation_rate"] = 0.03
	factors["interest_rate"] = 0.05
	factors["gdp_growth"] = 0.025

	return factors
}

// extractHistoricalCostsArray extracts historical cost data as array
func (s *FinanceService) extractHistoricalCostsArray(regulations []models.LawsAndRules) []float64 {
	costs := []float64{}

	// Extract costs from similar regulations
	for range regulations {
		// Placeholder cost extraction logic
		costs = append(costs, 100000.0) // Base cost
	}

	return costs
}

// extractPerformanceMetricsFloat extracts performance metrics as float64 map
func (s *FinanceService) extractPerformanceMetricsFloat(regulations []models.LawsAndRules) map[string]float64 {
	metrics := make(map[string]float64)

	// Extract performance data
	metrics["average_implementation_time"] = 90.0 // days
	metrics["success_rate"] = 0.85
	metrics["cost_variance"] = 0.15

	return metrics
}

// assessMarketConditionsFloat assesses current market conditions returning float64
func (s *FinanceService) assessMarketConditionsFloat(industryType string) float64 {
	// Market condition assessment returning single score
	switch industryType {
	case "financial_services":
		return 0.7
	case "healthcare":
		return 0.6
	case "environmental":
		return 0.8
	default:
		return 0.5
	}
}

// assessCompetitiveFactorsFloat assesses competitive factors returning float64
func (s *FinanceService) assessCompetitiveFactorsFloat(industryType string) float64 {
	// Competitive factor assessment returning single score
	switch industryType {
	case "financial_services":
		return 0.8
	case "healthcare":
		return 0.6
	case "environmental":
		return 0.4
	default:
		return 0.5
	}
}

// assessEconomicFactorsFloat assesses economic factors returning float64
func (s *FinanceService) assessEconomicFactorsFloat() float64 {
	// Economic factor assessment returning single score
	return 0.6 // Moderate economic conditions
}
