'use client'

import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  UserGroupIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';

interface AnalyticsData {
  documents: {
    total: number;
    by_status: Record<string, number>;
    by_type: Record<string, number>;
    by_agency: Record<string, number>;
    recent_activity: Array<{
      date: string;
      count: number;
    }>;
    processing_stats: {
      pending: number;
      completed: number;
      failed: number;
    };
  };
  agencies: {
    total: number;
    most_active: Array<{
      name: string;
      document_count: number;
    }>;
    performance_metrics: Record<string, number>;
  };
  categories: {
    total: number;
    usage_stats: Record<string, number>;
    trending: Array<{
      name: string;
      growth_rate: number;
    }>;
  };
  users: {
    total: number;
    by_role: Record<string, number>;
    activity_stats: Array<{
      date: string;
      active_users: number;
    }>;
    engagement_metrics: {
      daily_active: number;
      weekly_active: number;
      monthly_active: number;
    };
  };
  system: {
    storage_usage: {
      total_gb: number;
      used_gb: number;
      percentage: number;
    };
    performance: {
      avg_response_time: number;
      uptime_percentage: number;
      error_rate: number;
    };
    retention: {
      policies_active: number;
      documents_archived: number;
      space_freed_gb: number;
    };
  };
}

const AnalyticsPage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeRange, setTimeRange] = useState('30d');

  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'admin') {
      window.location.href = '/dashboard';
      return;
    }

    fetchAnalytics();
  }, [isAuthenticated, user, timeRange]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await apiService.get<AnalyticsData>(`/analytics/dashboard?range=${timeRange}`);
      setAnalytics(response);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch analytics');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated || user?.role !== 'admin') {
    return null;
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <ChartBarIcon className="h-8 w-8 text-primary-600 mr-3" />
                Advanced Analytics Dashboard
              </h1>
              <p className="mt-2 text-gray-600">
                Comprehensive insights into document management system usage, performance, and trends
              </p>
            </div>
            <div>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="7d">Last 7 days</option>
                <option value="30d">Last 30 days</option>
                <option value="90d">Last 90 days</option>
                <option value="1y">Last year</option>
              </select>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading analytics...</p>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800">{error}</p>
          </div>
        ) : analytics ? (
          <div className="space-y-8">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center">
                  <DocumentTextIcon className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Documents</p>
                    <p className="text-2xl font-bold text-gray-900">{analytics.documents.total}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center">
                  <BuildingOfficeIcon className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Active Agencies</p>
                    <p className="text-2xl font-bold text-gray-900">{analytics.agencies.total}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center">
                  <TagIcon className="h-8 w-8 text-purple-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Categories</p>
                    <p className="text-2xl font-bold text-gray-900">{analytics.categories.total}</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center">
                  <UserGroupIcon className="h-8 w-8 text-orange-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Users</p>
                    <p className="text-2xl font-bold text-gray-900">{analytics.users.total}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* System Health Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Storage Usage</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analytics.system.storage_usage.used_gb}GB / {analytics.system.storage_usage.total_gb}GB
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="w-16 h-16 relative">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#e5e7eb"
                          strokeWidth="3"
                        />
                        <path
                          d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="#3b82f6"
                          strokeWidth="3"
                          strokeDasharray={`${analytics.system.storage_usage.percentage}, 100`}
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-semibold text-gray-700">
                          {analytics.system.storage_usage.percentage}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-8 w-8 text-green-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">System Uptime</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analytics.system.performance.uptime_percentage}%
                    </p>
                    <p className="text-xs text-gray-500">
                      Avg response: {analytics.system.performance.avg_response_time}ms
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-md">
                <div className="flex items-center">
                  <ClockIcon className="h-8 w-8 text-blue-500" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Processing Jobs</p>
                    <div className="flex space-x-4 mt-1">
                      <span className="text-sm text-yellow-600">
                        {analytics.documents.processing_stats.pending} pending
                      </span>
                      <span className="text-sm text-green-600">
                        {analytics.documents.processing_stats.completed} done
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Document Status Distribution */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Document Status Distribution</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(analytics.documents.by_status).map(([status, count]) => (
                  <div key={status} className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{count}</div>
                    <div className="text-sm text-gray-600 capitalize">{status}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Most Active Agencies */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Most Active Agencies</h3>
              <div className="space-y-3">
                {analytics.agencies.most_active.slice(0, 5).map((agency, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <span className="text-sm text-gray-900">{agency.name}</span>
                    <span className="text-sm font-medium text-gray-600">{agency.document_count} documents</span>
                  </div>
                ))}
              </div>
            </div>

            {/* User Engagement */}
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">User Engagement</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{analytics.users.engagement_metrics.daily_active}</div>
                  <div className="text-sm text-gray-600">Daily Active Users</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{analytics.users.engagement_metrics.weekly_active}</div>
                  <div className="text-sm text-gray-600">Weekly Active Users</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{analytics.users.engagement_metrics.monthly_active}</div>
                  <div className="text-sm text-gray-600">Monthly Active Users</div>
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </Layout>
  );
};

export default AnalyticsPage;
