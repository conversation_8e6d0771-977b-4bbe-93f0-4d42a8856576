'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon,
  PresentationChartBarIcon,
  ArrowDownTrayIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface AnalyticsDashboard {
  id: number;
  name: string;
  description: string;
  dashboard_type: 'system' | 'user' | 'document' | 'regulation' | 'finance' | 'custom';
  widgets: any[];
  layout: any;
  is_public: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

const AnalyticsDashboardsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [dashboards, setDashboards] = useState<AnalyticsDashboard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchDashboards();
  }, []);

  const fetchDashboards = async () => {
    try {
      setLoading(true);
      // Fetch real analytics dashboards from API
      const response = await fetch('/api/analytics/dashboards', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics dashboards');
      }

      const data = await response.json();
      const dashboards: AnalyticsDashboard[] = data.data || [];

      setDashboards(dashboards);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch analytics dashboards');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this dashboard?')) return;
    
    try {
      // await apiService.deleteAnalyticsDashboard(id);
      setDashboards(dashboards.filter(dashboard => dashboard.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete dashboard');
    }
  };

  const handleExport = async (id: number) => {
    try {
      // await apiService.exportDashboard(id);
      alert('Dashboard exported successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to export dashboard');
    }
  };

  const filteredDashboards = dashboards.filter(dashboard =>
    dashboard.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dashboard.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dashboard.dashboard_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'system':
        return 'bg-blue-100 text-blue-800';
      case 'user':
        return 'bg-green-100 text-green-800';
      case 'document':
        return 'bg-purple-100 text-purple-800';
      case 'regulation':
        return 'bg-orange-100 text-orange-800';
      case 'finance':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view analytics dashboards.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboards</h1>
              <p className="text-gray-600 mt-1">Create and manage analytics dashboards with data visualization</p>
            </div>
            <Link
              href="/admin/analytics/dashboards/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Dashboard
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search dashboards..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading analytics dashboards...</p>
          </div>
        ) : (
          /* Dashboards Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDashboards.length === 0 ? (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No analytics dashboards found.</p>
                <Link
                  href="/admin/analytics/dashboards/new"
                  className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create your first dashboard
                </Link>
              </div>
            ) : (
              filteredDashboards.map((dashboard) => (
                <div key={dashboard.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {dashboard.name}
                      </h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(dashboard.dashboard_type)} mt-1`}>
                        {dashboard.dashboard_type}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      {dashboard.is_public && (
                        <ShareIcon className="h-4 w-4 text-green-600" title="Public Dashboard" />
                      )}
                      <PresentationChartBarIcon className="h-5 w-5 text-gray-400" />
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {dashboard.description}
                  </p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Widgets:</span>
                      <span className="font-medium">{dashboard.widgets.length}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Layout:</span>
                      <span className="font-medium">{dashboard.layout.columns}x{dashboard.layout.rows}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Updated:</span>
                      <span className="font-medium">{new Date(dashboard.updated_at).toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => router.push(`/admin/analytics/dashboards/${dashboard.id}`)}
                      className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
                    >
                      <EyeIcon className="h-4 w-4 inline mr-1" />
                      View
                    </button>
                    <button
                      onClick={() => router.push(`/admin/analytics/dashboards/${dashboard.id}/edit`)}
                      className="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700"
                    >
                      <PencilIcon className="h-4 w-4 inline mr-1" />
                      Edit
                    </button>
                    <button
                      onClick={() => handleExport(dashboard.id)}
                      className="bg-yellow-600 text-white px-3 py-2 rounded text-sm hover:bg-yellow-700"
                      title="Export Dashboard"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(dashboard.id)}
                      className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default AnalyticsDashboardsPage;
