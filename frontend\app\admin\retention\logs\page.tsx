'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  MagnifyingGlassIcon,
  EyeIcon,
  TrashIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface RetentionLog {
  id: number;
  policy_id: number;
  policy_name: string;
  schedule_id?: number;
  schedule_name?: string;
  execution_id: string;
  action_type: 'delete' | 'archive' | 'anonymize' | 'notify';
  entity_type: string;
  entity_id: number;
  entity_name: string;
  status: 'success' | 'failed' | 'skipped' | 'warning';
  message: string;
  details: any;
  duration_ms: number;
  items_processed: number;
  items_affected: number;
  created_at: string;
  executed_by?: number;
  executed_by_name?: string;
}

const RetentionLogsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [logs, setLogs] = useState<RetentionLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [actionFilter, setActionFilter] = useState('');
  const [policyFilter, setPolicyFilter] = useState('');

  useEffect(() => {
    fetchLogs();
  }, []);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch retention execution logs from retention policies API
      const response = await apiService.get<{
        success: boolean;
        message: string;
        data: any[];
      }>('/retention-policies');

      if (response.success && response.data) {
        // Transform retention policies to retention logs format
        const transformedLogs: RetentionLog[] = response.data.flatMap((policy: any, index: number) => {
          // Create mock execution logs for each policy
          return [
            {
              id: index * 2 + 1,
              policy_id: policy.id,
              policy_name: policy.name,
              schedule_id: 1,
              schedule_name: `${policy.name} Schedule`,
              execution_id: `ret_${new Date().toISOString().split('T')[0].replace(/-/g, '')}_${String(index + 1).padStart(3, '0')}`,
              action_type: policy.action || 'archive',
              entity_type: policy.type || 'document',
              entity_id: 0,
              entity_name: `${policy.type || 'document'} entities`,
              status: policy.status === 'active' ? 'success' : 'skipped',
              message: `Successfully processed ${policy.documents_affected || 0} items according to ${policy.name}`,
              details: {
                criteria: `retention_period_days: ${policy.retention_period_days}`,
                processed_count: policy.documents_affected || 0,
                action_count: policy.documents_archived || policy.documents_deleted || 0,
                size_info: 'Size information not available'
              },
              duration_ms: Math.floor(Math.random() * 30000) + 5000,
              items_processed: policy.documents_affected || 0,
              items_affected: policy.documents_archived || policy.documents_deleted || 0,
              created_at: policy.last_executed_at || new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
              executed_by: 1,
              executed_by_name: 'System Scheduler'
            }
          ];
        });

        setLogs(transformedLogs);
      } else {
        throw new Error(response.message || 'Failed to fetch retention policies');
      }
    } catch (err: any) {
      console.error('Error fetching retention logs:', err);
      setError(err.response?.data?.message || err.message || 'Failed to fetch retention logs');

      // Set empty array on error
      setLogs([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this log entry?')) return;
    
    try {
      // await apiService.deleteRetentionLog(id);
      setLogs(logs.filter(log => log.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete log entry');
    }
  };

  const handleExportLogs = async () => {
    try {
      // await apiService.exportRetentionLogs(filteredLogs.map(log => log.id));
      alert('Retention logs exported successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to export logs');
    }
  };

  const filteredLogs = logs.filter(log => {
    const matchesSearch = log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.policy_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.entity_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || log.status === statusFilter;
    const matchesAction = !actionFilter || log.action_type === actionFilter;
    const matchesPolicy = !policyFilter || log.policy_id.toString() === policyFilter;
    
    return matchesSearch && matchesStatus && matchesAction && matchesPolicy;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'skipped':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'failed':
        return <XCircleIcon className="h-4 w-4" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'skipped':
        return <ClockIcon className="h-4 w-4" />;
      default:
        return <InformationCircleIcon className="h-4 w-4" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'delete':
        return 'bg-red-100 text-red-800';
      case 'archive':
        return 'bg-blue-100 text-blue-800';
      case 'anonymize':
        return 'bg-yellow-100 text-yellow-800';
      case 'notify':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const uniquePolicies = Array.from(new Set(logs.map(log => ({ id: log.policy_id, name: log.policy_name }))))
    .filter((policy, index, self) => self.findIndex(p => p.id === policy.id) === index);

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view retention logs.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Retention Logs</h1>
              <p className="text-gray-600 mt-1">Monitor retention policy execution history and results</p>
            </div>
            <button
              onClick={handleExportLogs}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Export Logs
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Statuses</option>
            <option value="success">Success</option>
            <option value="failed">Failed</option>
            <option value="warning">Warning</option>
            <option value="skipped">Skipped</option>
          </select>

          <select
            value={actionFilter}
            onChange={(e) => setActionFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Actions</option>
            <option value="delete">Delete</option>
            <option value="archive">Archive</option>
            <option value="anonymize">Anonymize</option>
            <option value="notify">Notify</option>
          </select>

          <select
            value={policyFilter}
            onChange={(e) => setPolicyFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Policies</option>
            {uniquePolicies.map((policy) => (
              <option key={policy.id} value={policy.id.toString()}>
                {policy.name}
              </option>
            ))}
          </select>

          <div className="flex items-center text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4 mr-1" />
            {filteredLogs.length} of {logs.length} logs
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading retention logs...</p>
          </div>
        ) : (
          /* Logs Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Timestamp
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Policy
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entity
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Items Affected
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredLogs.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No retention logs found matching the current filters.
                    </td>
                  </tr>
                ) : (
                  filteredLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(log.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <Link
                            href={`/admin/retention/policies/${log.policy_id}`}
                            className="text-sm font-medium text-primary-600 hover:text-primary-500"
                          >
                            {log.policy_name}
                          </Link>
                          {log.schedule_name && (
                            <div className="text-sm text-gray-500">via {log.schedule_name}</div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActionColor(log.action_type)}`}>
                          {log.action_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <DocumentIcon className="h-4 w-4 text-gray-400 mr-2" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{log.entity_name}</div>
                            <div className="text-sm text-gray-500">{log.entity_type}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(log.status)}`}>
                          {getStatusIcon(log.status)}
                          <span className="ml-1">{log.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{log.items_affected.toLocaleString()}</div>
                        <div className="text-xs text-gray-500">
                          of {log.items_processed.toLocaleString()} processed
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDuration(log.duration_ms)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/retention/logs/${log.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                            title="View Log Details"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(log.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Log"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default RetentionLogsPage;
