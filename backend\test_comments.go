package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

const baseURL = "http://localhost:8080"

// Test data structures
type CommentRequest struct {
	CommenterName  string `json:"commenter_name"`
	CommenterEmail string `json:"commenter_email,omitempty"`
	Organization   string `json:"organization,omitempty"`
	Subject        string `json:"subject,omitempty"`
	Content        string `json:"content"`
	IsPublic       *bool  `json:"is_public,omitempty"`
	CertificateID  *uint  `json:"certificate_id,omitempty"`
	SignatureData  string `json:"signature_data,omitempty"`
}

type SignedCommentRequest struct {
	CommenterName    string `json:"commenter_name"`
	CommenterEmail   string `json:"commenter_email,omitempty"`
	Organization     string `json:"organization,omitempty"`
	Subject          string `json:"subject,omitempty"`
	Content          string `json:"content"`
	DigitalSignature struct {
		SignatureValue string `json:"signature_value"`
		CertificateID  uint   `json:"certificate_id"`
		Timestamp      string `json:"timestamp"`
		HashAlgorithm  string `json:"hash_algorithm"`
		SigningMethod  string `json:"signing_method"`
	} `json:"digital_signature"`
}

type ApiResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

var authToken string

func makeRequest(method, endpoint string, body interface{}) (*http.Response, error) {
	var reqBody io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, err
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	req, err := http.NewRequest(method, baseURL+endpoint, reqBody)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if authToken != "" {
		req.Header.Set("Authorization", "Bearer "+authToken)
	}

	client := &http.Client{Timeout: 30 * time.Second}
	return client.Do(req)
}

func login() error {
	loginData := map[string]string{
		"username": "admin",
		"password": "admin123",
	}

	resp, err := makeRequest("POST", "/auth/login", loginData)
	if err != nil {
		return fmt.Errorf("login request failed: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("login failed with status: %d", resp.StatusCode)
	}

	var response struct {
		Token string `json:"token"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode login response: %v", err)
	}

	authToken = response.Token
	fmt.Println("✅ Login successful")
	return nil
}

func testGetDocumentComments(documentID int) error {
	fmt.Printf("\n🔍 Testing GET /documents/%d/comments\n", documentID)

	resp, err := makeRequest("GET", fmt.Sprintf("/documents/%d/comments", documentID), nil)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == http.StatusOK {
		fmt.Println("✅ Get document comments successful")
	} else {
		fmt.Printf("❌ Get document comments failed with status: %d\n", resp.StatusCode)
	}

	return nil
}

func testCreateComment(documentID int) error {
	fmt.Printf("\n📝 Testing POST /documents/%d/comments\n", documentID)

	isPublic := true
	comment := CommentRequest{
		CommenterName:  "Test User",
		CommenterEmail: "<EMAIL>",
		Organization:   "Test Organization",
		Subject:        "Test Comment Subject",
		Content:        "This is a test comment to verify the comment system is working correctly.",
		IsPublic:       &isPublic,
	}

	resp, err := makeRequest("POST", fmt.Sprintf("/documents/%d/comments", documentID), comment)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == http.StatusCreated {
		fmt.Println("✅ Create comment successful")

		// Parse response to get comment ID
		var response ApiResponse
		if err := json.Unmarshal(body, &response); err == nil {
			if data, ok := response.Data.(map[string]interface{}); ok {
				if commentID, ok := data["comment_id"].(float64); ok {
					return testGetComment(int(commentID))
				}
			}
		}
	} else {
		fmt.Printf("❌ Create comment failed with status: %d\n", resp.StatusCode)
	}

	return nil
}

func testCreateSignedComment(documentID int) error {
	fmt.Printf("\n🔐 Testing POST /documents/%d/comments/signed\n", documentID)

	comment := SignedCommentRequest{
		CommenterName:  "Signed Test User",
		CommenterEmail: "<EMAIL>",
		Organization:   "Signed Test Organization",
		Subject:        "Digitally Signed Test Comment",
		Content:        "This is a digitally signed test comment to verify the signature system.",
	}

	comment.DigitalSignature.SignatureValue = "mock_signature_value_12345"
	comment.DigitalSignature.CertificateID = 1
	comment.DigitalSignature.Timestamp = time.Now().Format(time.RFC3339)
	comment.DigitalSignature.HashAlgorithm = "sha256"
	comment.DigitalSignature.SigningMethod = "rsa_pss"

	resp, err := makeRequest("POST", fmt.Sprintf("/documents/%d/comments/signed", documentID), comment)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == http.StatusCreated {
		fmt.Println("✅ Create signed comment successful")
	} else {
		fmt.Printf("❌ Create signed comment failed with status: %d\n", resp.StatusCode)
	}

	return nil
}

func testGetComment(commentID int) error {
	fmt.Printf("\n🔍 Testing GET /comments/%d\n", commentID)

	resp, err := makeRequest("GET", fmt.Sprintf("/comments/%d", commentID), nil)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == http.StatusOK {
		fmt.Println("✅ Get comment successful")
	} else {
		fmt.Printf("❌ Get comment failed with status: %d\n", resp.StatusCode)
	}

	return nil
}

func testVerifyCommentSignature(commentID int) error {
	fmt.Printf("\n🔐 Testing GET /comments/%d/verify\n", commentID)

	resp, err := makeRequest("GET", fmt.Sprintf("/comments/%d/verify", commentID), nil)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == http.StatusOK {
		fmt.Println("✅ Verify comment signature successful")
	} else {
		fmt.Printf("❌ Verify comment signature failed with status: %d\n", resp.StatusCode)
	}

	return nil
}

func testGetUserCertificates() error {
	fmt.Printf("\n📜 Testing GET /user/certificates\n")

	resp, err := makeRequest("GET", "/user/certificates", nil)
	if err != nil {
		return fmt.Errorf("request failed: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response: %v", err)
	}

	fmt.Printf("Status: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	if resp.StatusCode == http.StatusOK {
		fmt.Println("✅ Get user certificates successful")
	} else {
		fmt.Printf("❌ Get user certificates failed with status: %d\n", resp.StatusCode)
	}

	return nil
}

func testCommentsMain() {
	fmt.Println("🚀 Starting Comment System Tests")
	fmt.Println("================================")

	// Login first
	if err := login(); err != nil {
		fmt.Printf("❌ Login failed: %v\n", err)
		return
	}

	// Test document ID (assuming document 1 exists)
	documentID := 1

	// Run tests
	tests := []func() error{
		func() error { return testGetDocumentComments(documentID) },
		func() error { return testCreateComment(documentID) },
		func() error { return testCreateSignedComment(documentID) },
		func() error { return testGetUserCertificates() },
		func() error { return testVerifyCommentSignature(1) }, // Assuming comment 1 exists
	}

	for _, test := range tests {
		if err := test(); err != nil {
			fmt.Printf("❌ Test failed: %v\n", err)
		}
		time.Sleep(1 * time.Second) // Small delay between tests
	}

	fmt.Println("\n🏁 Comment System Tests Completed")
}
