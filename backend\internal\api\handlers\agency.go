package handlers

import (
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// AgencyRequest represents the request structure for agencies
type AgencyRequest struct {
	Name         string `json:"name" binding:"required"`
	ShortName    string `json:"short_name"`
	Slug         string `json:"slug"`
	Description  string `json:"description"`
	Website      string `json:"website"`
	Email        string `json:"email"`
	Phone        string `json:"phone"`
	Address      string `json:"address"`
	AgencyType   string `json:"agency_type"`
	Jurisdiction string `json:"jurisdiction"`
	IsActive     bool   `json:"is_active"`
}

// UpdateAgencyRequest represents the request structure for updating agencies
type UpdateAgencyRequest struct {
	Name         *string `json:"name"`
	ShortName    *string `json:"short_name"`
	Slug         *string `json:"slug"`
	Description  *string `json:"description"`
	Website      *string `json:"website"`
	Email        *string `json:"email"`
	Phone        *string `json:"phone"`
	Address      *string `json:"address"`
	AgencyType   *string `json:"agency_type"`
	Jurisdiction *string `json:"jurisdiction"`
	IsActive     *bool   `json:"is_active"`
}

// generateSlug creates a URL-friendly slug from a string
func generateSlug(text string) string {
	// Convert to lowercase
	slug := strings.ToLower(text)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	return slug
}

// GetAgencies returns all agencies with pagination
func GetAgencies(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total agencies
	var total int64
	db.Model(&models.Agency{}).Count(&total)

	// Get agencies with pagination
	var agencies []models.Agency
	offset := (page - 1) * perPage
	if err := db.Order("name ASC").Limit(perPage).Offset(offset).Find(&agencies).Error; err != nil {
		HandleInternalError(c, "Failed to fetch agencies: "+err.Error())
		return
	}

	// Convert to response format
	agencyResponses := make([]gin.H, len(agencies))
	for i, agency := range agencies {
		agencyResponses[i] = gin.H{
			"id":          agency.ID,
			"name":        agency.Name,
			"slug":        agency.Slug,
			"description": agency.Description,
			"website":     agency.Website,
			"email":       agency.Email,
			"phone":       agency.Phone,
			"address":     agency.Address,
			"is_active":   agency.IsActive,
			"created_at":  agency.CreatedAt,
			"updated_at":  agency.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       agencyResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetAgency returns a single agency by ID
func GetAgency(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get agency
	var agency models.Agency
	if err := db.First(&agency, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		HandleInternalError(c, "Failed to fetch agency: "+err.Error())
		return
	}

	response := gin.H{
		"id":          agency.ID,
		"name":        agency.Name,
		"slug":        agency.Slug,
		"description": agency.Description,
		"website":     agency.Website,
		"email":       agency.Email,
		"phone":       agency.Phone,
		"address":     agency.Address,
		"is_active":   agency.IsActive,
		"created_at":  agency.CreatedAt,
		"updated_at":  agency.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency retrieved successfully",
		Data:    response,
	})
}

// CreateAgency creates a new agency
func CreateAgency(c *gin.Context) {
	var req AgencyRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Generate slug if not provided
	if req.Slug == "" {
		req.Slug = generateSlug(req.Name)
	}

	// Generate short_name if not provided
	if req.ShortName == "" {
		req.ShortName = generateShortName(req.Name)
	}

	// Always check for duplicate short_name and make it unique
	var existingAgency models.Agency
	originalShortName := req.ShortName
	counter := 1
	for {
		err := db.Where("short_name = ? AND deleted_at IS NULL", req.ShortName).First(&existingAgency).Error
		if err != nil {
			if strings.Contains(err.Error(), "record not found") {
				break // Short name is unique
			}
		}
		// If we found a duplicate, modify the short name
		req.ShortName = fmt.Sprintf("%s_%d", originalShortName, counter)
		counter++
		if counter > 100 { // Safety limit
			req.ShortName = fmt.Sprintf("%s_%d", originalShortName, time.Now().UnixNano()%100000)
			break
		}
	}

	// Always check for duplicate slug and make it unique
	originalSlug := req.Slug
	slugCounter := 1
	for {
		err := db.Where("slug = ? AND deleted_at IS NULL", req.Slug).First(&existingAgency).Error
		if err != nil {
			if strings.Contains(err.Error(), "record not found") {
				break // Slug is unique
			}
		}
		req.Slug = fmt.Sprintf("%s-%d", originalSlug, slugCounter)
		slugCounter++
		if slugCounter > 100 { // Safety limit
			req.Slug = fmt.Sprintf("%s-%d", originalSlug, time.Now().UnixNano()%100000)
			break
		}
	}

	// Create agency
	agency := &models.Agency{
		Name:         req.Name,
		ShortName:    req.ShortName,
		Slug:         req.Slug,
		Description:  req.Description,
		Website:      req.Website,
		Email:        req.Email,
		Phone:        req.Phone,
		Address:      req.Address,
		AgencyType:   req.AgencyType,
		Jurisdiction: req.Jurisdiction,
		IsActive:     req.IsActive,
	}

	if err := db.Create(agency).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Agency with this name or identifier already exists")
			return
		}
		HandleInternalError(c, "Failed to create agency: "+err.Error())
		return
	}

	response := gin.H{
		"id":          agency.ID,
		"name":        agency.Name,
		"slug":        agency.Slug,
		"description": agency.Description,
		"website":     agency.Website,
		"email":       agency.Email,
		"phone":       agency.Phone,
		"address":     agency.Address,
		"is_active":   agency.IsActive,
		"created_at":  agency.CreatedAt,
		"updated_at":  agency.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Agency created successfully",
		Data:    response,
	})
}

// UpdateAgency updates an existing agency
func UpdateAgency(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req UpdateAgencyRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing agency
	var agency models.Agency
	if err := db.First(&agency, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		HandleInternalError(c, "Failed to fetch agency: "+err.Error())
		return
	}

	// Update agency fields only if provided
	if req.Name != nil {
		agency.Name = *req.Name
	}
	if req.Slug != nil {
		agency.Slug = *req.Slug
	}
	if req.Description != nil {
		agency.Description = *req.Description
	}
	if req.Website != nil {
		agency.Website = *req.Website
	}
	if req.Email != nil {
		agency.Email = *req.Email
	}
	if req.Phone != nil {
		agency.Phone = *req.Phone
	}
	if req.Address != nil {
		agency.Address = *req.Address
	}
	if req.AgencyType != nil {
		agency.AgencyType = *req.AgencyType
	}
	if req.Jurisdiction != nil {
		agency.Jurisdiction = *req.Jurisdiction
	}
	if req.IsActive != nil {
		agency.IsActive = *req.IsActive
	}

	if err := db.Save(&agency).Error; err != nil {
		HandleInternalError(c, "Failed to update agency: "+err.Error())
		return
	}

	response := gin.H{
		"id":          agency.ID,
		"name":        agency.Name,
		"slug":        agency.Slug,
		"description": agency.Description,
		"website":     agency.Website,
		"email":       agency.Email,
		"phone":       agency.Phone,
		"address":     agency.Address,
		"is_active":   agency.IsActive,
		"created_at":  agency.CreatedAt,
		"updated_at":  agency.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency updated successfully",
		Data:    response,
	})
}

// DeleteAgency deletes an agency
func DeleteAgency(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if agency exists
	var agency models.Agency
	if err := db.First(&agency, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		HandleInternalError(c, "Failed to fetch agency: "+err.Error())
		return
	}

	// Delete agency
	if err := db.Delete(&agency).Error; err != nil {
		HandleInternalError(c, "Failed to delete agency: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency deleted successfully",
	})
}

// GetPublicAgencies returns all public agencies
func GetPublicAgencies(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get all active agencies
	var agencies []models.Agency
	if err := db.Where("is_active = ?", true).Order("name ASC").Find(&agencies).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch agencies",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	agencyResponses := make([]gin.H, len(agencies))
	for i, agency := range agencies {
		// Count public documents for this agency
		var documentCount int64
		db.Model(&models.Document{}).
			Where("agency_id = ? AND is_public = ? AND visibility_level = ?", agency.ID, true, 1).
			Count(&documentCount)

		agencyResponses[i] = gin.H{
			"id":             agency.ID,
			"name":           agency.Name,
			"slug":           agency.Slug,
			"description":    agency.Description,
			"website":        agency.Website,
			"document_count": documentCount,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Public agencies retrieved successfully",
		Data:    agencyResponses,
	})
}

// GetPublicAgency returns a single public agency by ID
func GetPublicAgency(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get agency
	var agency models.Agency
	if err := db.Where("id = ? AND is_active = ?", id, true).First(&agency).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch agency",
			Message: err.Error(),
		})
		return
	}

	// Count public documents for this agency
	var documentCount int64
	db.Model(&models.Document{}).
		Where("agency_id = ? AND is_public = ? AND visibility_level = ?", agency.ID, true, 1).
		Count(&documentCount)

	response := gin.H{
		"id":             agency.ID,
		"name":           agency.Name,
		"slug":           agency.Slug,
		"description":    agency.Description,
		"website":        agency.Website,
		"email":          agency.Email,
		"phone":          agency.Phone,
		"address":        agency.Address,
		"document_count": documentCount,
		"created_at":     agency.CreatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Public agency retrieved successfully",
		Data:    response,
	})
}

// GetAgencyDocuments returns public documents for a specific agency
func GetAgencyDocuments(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify agency exists
	var agency models.Agency
	if err := db.Where("id = ? AND is_active = ?", id, true).First(&agency).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch agency",
			Message: err.Error(),
		})
		return
	}

	// Count total documents for this agency
	var total int64
	db.Model(&models.Document{}).
		Where("agency_id = ? AND is_public = ? AND visibility_level = ?", id, true, 1).
		Count(&total)

	// Get documents with pagination
	var documents []models.Document
	offset := (page - 1) * perPage
	if err := db.Preload("Agency").
		Where("agency_id = ? AND is_public = ? AND visibility_level = ?", id, true, 1).
		Order("publication_date DESC, created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch agency documents",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	documentResponses := make([]gin.H, len(documents))
	for i, doc := range documents {
		documentResponses[i] = gin.H{
			"id":               doc.ID,
			"title":            doc.Title,
			"slug":             doc.Slug,
			"abstract":         doc.Abstract,
			"type":             doc.Type,
			"status":           doc.Status,
			"publication_date": doc.PublicationDate,
			"effective_date":   doc.EffectiveDate,
			"created_at":       doc.CreatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       documentResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetAgencyDefaults returns default values for agency creation
func GetAgencyDefaults(c *gin.Context) {
	defaults := gin.H{
		"is_active": true,
		"country":   "US",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency defaults retrieved successfully",
		Data:    defaults,
	})
}

// CreateAgencyContact creates a new agency contact
func CreateAgencyContact(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		Title       string `json:"title"`
		Department  string `json:"department"`
		Email       string `json:"email"`
		Phone       string `json:"phone"`
		Fax         string `json:"fax"`
		Address     string `json:"address"`
		ContactType string `json:"contact_type"`
		Purpose     string `json:"purpose"`
		IsPublic    *bool  `json:"is_public"`
		IsPrimary   *bool  `json:"is_primary"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify agency exists
	var agency models.Agency
	if err := db.First(&agency, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		HandleInternalError(c, "Failed to fetch agency: "+err.Error())
		return
	}

	// Set defaults
	isPublic := true
	if req.IsPublic != nil {
		isPublic = *req.IsPublic
	}
	isPrimary := false
	if req.IsPrimary != nil {
		isPrimary = *req.IsPrimary
	}

	// If setting as primary, unset other primary contacts for this agency
	if isPrimary {
		db.Model(&models.AgencyContact{}).Where("agency_id = ?", id).Update("is_primary", false)
	}

	// Create contact
	contact := models.AgencyContact{
		AgencyID:    id,
		Name:        req.Name,
		Title:       req.Title,
		Department:  req.Department,
		Email:       req.Email,
		Phone:       req.Phone,
		Fax:         req.Fax,
		Address:     req.Address,
		ContactType: req.ContactType,
		Purpose:     req.Purpose,
		IsPublic:    isPublic,
		IsPrimary:   isPrimary,
	}

	if err := db.Create(&contact).Error; err != nil {
		HandleInternalError(c, "Failed to create agency contact: "+err.Error())
		return
	}

	// Load the created contact with agency
	if err := db.Preload("Agency").First(&contact, contact.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created contact: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Agency contact created successfully",
		Data:    contact,
	})
}

// GetAgencyContacts returns contacts for an agency
func GetAgencyContacts(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify agency exists
	var agency models.Agency
	if err := db.First(&agency, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		HandleInternalError(c, "Failed to fetch agency: "+err.Error())
		return
	}

	// Build query
	query := db.Model(&models.AgencyContact{}).Where("agency_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Where("name ILIKE ? OR title ILIKE ? OR department ILIKE ? OR email ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}

	// Filter by contact type if provided
	if contactType := c.Query("contact_type"); contactType != "" {
		query = query.Where("contact_type = ?", contactType)
	}

	// Filter by purpose if provided
	if purpose := c.Query("purpose"); purpose != "" {
		query = query.Where("purpose = ?", purpose)
	}

	// Filter by public status if provided
	if public := c.Query("public"); public != "" {
		if public == "true" {
			query = query.Where("is_public = ?", true)
		} else if public == "false" {
			query = query.Where("is_public = ?", false)
		}
	}

	// Filter by primary status if provided
	if primary := c.Query("primary"); primary != "" {
		if primary == "true" {
			query = query.Where("is_primary = ?", true)
		} else if primary == "false" {
			query = query.Where("is_primary = ?", false)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("is_primary DESC, name ASC")
	}

	// Get contacts with agency
	var contacts []models.AgencyContact
	if err := query.Preload("Agency").Find(&contacts).Error; err != nil {
		HandleInternalError(c, "Failed to fetch agency contacts: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       contacts,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetAgencyContact returns a specific agency contact
func GetAgencyContact(c *gin.Context) {
	contactID, valid := ValidateID(c, "contactId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get contact with agency
	var contact models.AgencyContact
	if err := db.Preload("Agency").First(&contact, contactID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency contact")
			return
		}
		HandleInternalError(c, "Failed to fetch agency contact: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency contact retrieved successfully",
		Data:    contact,
	})
}

// UpdateAgencyContact updates an agency contact
func UpdateAgencyContact(c *gin.Context) {
	contactID, valid := ValidateID(c, "contactId")
	if !valid {
		return
	}

	var req struct {
		Name        *string `json:"name"`
		Title       *string `json:"title"`
		Department  *string `json:"department"`
		Email       *string `json:"email"`
		Phone       *string `json:"phone"`
		Fax         *string `json:"fax"`
		Address     *string `json:"address"`
		ContactType *string `json:"contact_type"`
		Purpose     *string `json:"purpose"`
		IsPublic    *bool   `json:"is_public"`
		IsPrimary   *bool   `json:"is_primary"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing contact
	var contact models.AgencyContact
	if err := db.First(&contact, contactID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency contact")
			return
		}
		HandleInternalError(c, "Failed to fetch agency contact: "+err.Error())
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Title != nil {
		updates["title"] = *req.Title
	}
	if req.Department != nil {
		updates["department"] = *req.Department
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Fax != nil {
		updates["fax"] = *req.Fax
	}
	if req.Address != nil {
		updates["address"] = *req.Address
	}
	if req.ContactType != nil {
		updates["contact_type"] = *req.ContactType
	}
	if req.Purpose != nil {
		updates["purpose"] = *req.Purpose
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.IsPrimary != nil {
		updates["is_primary"] = *req.IsPrimary
		// If setting as primary, unset other primary contacts for this agency
		if *req.IsPrimary {
			db.Model(&models.AgencyContact{}).Where("agency_id = ? AND id != ?", contact.AgencyID, contactID).Update("is_primary", false)
		}
	}

	// Update contact
	if err := db.Model(&contact).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update agency contact: "+err.Error())
		return
	}

	// Load updated contact with agency
	if err := db.Preload("Agency").First(&contact, contact.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated contact: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency contact updated successfully",
		Data:    contact,
	})
}

// DeleteAgencyContact deletes an agency contact
func DeleteAgencyContact(c *gin.Context) {
	contactID, valid := ValidateID(c, "contactId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing contact
	var contact models.AgencyContact
	if err := db.First(&contact, contactID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency contact")
			return
		}
		HandleInternalError(c, "Failed to fetch agency contact: "+err.Error())
		return
	}

	// Delete contact
	if err := db.Delete(&contact).Error; err != nil {
		HandleInternalError(c, "Failed to delete agency contact: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency contact deleted successfully",
		Data:    gin.H{"id": contactID},
	})
}

// GetAgencyRegulations returns regulations for an agency
func GetAgencyRegulations(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify agency exists
	var agency models.Agency
	if err := db.First(&agency, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		HandleInternalError(c, "Failed to fetch agency: "+err.Error())
		return
	}

	// Build query for regulation relationships
	query := db.Model(&models.RegulationAgencyRelationship{}).Where("agency_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Joins("JOIN laws_and_rules ON regulation_agency_relationships.regulation_id = laws_and_rules.id").
			Where("laws_and_rules.title ILIKE ? OR laws_and_rules.content ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}

	// Filter by relationship type if provided
	if relType := c.Query("relationship_type"); relType != "" {
		query = query.Where("relationship_type = ?", relType)
	}

	// Filter by active status if provided
	if active := c.Query("active"); active != "" {
		if active == "true" {
			query = query.Where("is_active = ?", true)
		} else if active == "false" {
			query = query.Where("is_active = ?", false)
		}
	}

	// Filter by effective status if provided
	if effective := c.Query("effective"); effective != "" {
		now := time.Now()
		if effective == "true" {
			query = query.Where("(effective_date IS NULL OR effective_date <= ?) AND (termination_date IS NULL OR termination_date > ?)", now, now)
		} else if effective == "false" {
			query = query.Where("(effective_date IS NOT NULL AND effective_date > ?) OR (termination_date IS NOT NULL AND termination_date <= ?)", now, now)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get relationships with regulations
	var relationships []models.RegulationAgencyRelationship
	if err := query.Preload("Regulation").Preload("Agency").Preload("CreatedBy").Find(&relationships).Error; err != nil {
		HandleInternalError(c, "Failed to fetch agency regulations: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       relationships,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// generateShortName generates a short name from the full agency name
func generateShortName(name string) string {
	// Remove common words and create acronym
	words := strings.Fields(strings.ToUpper(name))
	var acronym strings.Builder

	// Skip common words
	skipWords := map[string]bool{
		"THE": true, "OF": true, "AND": true, "FOR": true, "TO": true,
		"IN": true, "ON": true, "AT": true, "BY": true, "WITH": true,
	}

	for _, word := range words {
		if !skipWords[word] && len(word) > 0 {
			acronym.WriteByte(word[0])
		}
	}

	result := acronym.String()
	if len(result) == 0 {
		// Fallback: use first 4 characters of name
		cleaned := strings.ReplaceAll(strings.ToUpper(name), " ", "")
		if len(cleaned) > 4 {
			result = cleaned[:4]
		} else {
			result = cleaned
		}
	}

	return result
}
