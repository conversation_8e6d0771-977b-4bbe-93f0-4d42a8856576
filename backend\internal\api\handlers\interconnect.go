package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// InterconnectRequest represents the request structure for interconnect records
type InterconnectRequest struct {
	SourceType   string `json:"source_type" binding:"required"`
	SourceID     uint   `json:"source_id" binding:"required"`
	TargetType   string `json:"target_type" binding:"required"`
	TargetID     uint   `json:"target_id" binding:"required"`
	Relationship string `json:"relationship" binding:"required"`
	Description  string `json:"description"`
	IsActive     bool   `json:"is_active"`
}

// GetInterconnects returns all interconnect records with pagination
func GetInterconnects(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON><PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total interconnect records
	var total int64
	db.Model(&models.Interconnect{}).Count(&total)

	// Get interconnect records with pagination
	var interconnects []models.Interconnect
	offset := (page - 1) * perPage
	if err := db.Order("created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&interconnects).Error; err != nil {
		HandleInternalError(c, "Failed to fetch interconnect records: "+err.Error())
		return
	}

	// Convert to response format
	interconnectResponses := make([]gin.H, len(interconnects))
	for i, interconnect := range interconnects {
		interconnectResponses[i] = gin.H{
			"id":           interconnect.ID,
			"source_type":  interconnect.SourceType,
			"source_id":    interconnect.SourceID,
			"target_type":  interconnect.TargetType,
			"target_id":    interconnect.TargetID,
			"relationship": interconnect.Relationship,
			"description":  interconnect.Description,
			"is_active":    interconnect.IsActive,
			"created_at":   interconnect.CreatedAt,
			"updated_at":   interconnect.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       interconnectResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetInterconnect returns a single interconnect record by ID
func GetInterconnect(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnect record
	var interconnect models.Interconnect
	if err := db.First(&interconnect, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Interconnect record")
			return
		}
		HandleInternalError(c, "Failed to fetch interconnect record: "+err.Error())
		return
	}

	response := gin.H{
		"id":           interconnect.ID,
		"source_type":  interconnect.SourceType,
		"source_id":    interconnect.SourceID,
		"target_type":  interconnect.TargetType,
		"target_id":    interconnect.TargetID,
		"relationship": interconnect.Relationship,
		"description":  interconnect.Description,
		"is_active":    interconnect.IsActive,
		"created_at":   interconnect.CreatedAt,
		"updated_at":   interconnect.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Interconnect record retrieved successfully",
		Data:    response,
	})
}

// CreateInterconnect creates a new interconnect record
func CreateInterconnect(c *gin.Context) {
	var req InterconnectRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create interconnect record
	interconnect := &models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
	}

	if err := db.Create(interconnect).Error; err != nil {
		HandleInternalError(c, "Failed to create interconnect record: "+err.Error())
		return
	}

	response := gin.H{
		"id":           interconnect.ID,
		"source_type":  interconnect.SourceType,
		"source_id":    interconnect.SourceID,
		"target_type":  interconnect.TargetType,
		"target_id":    interconnect.TargetID,
		"relationship": interconnect.Relationship,
		"description":  interconnect.Description,
		"is_active":    interconnect.IsActive,
		"created_at":   interconnect.CreatedAt,
		"updated_at":   interconnect.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Interconnect record created successfully",
		Data:    response,
	})
}

// UpdateInterconnect updates an existing interconnect record
func UpdateInterconnect(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req InterconnectRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing interconnect record
	var interconnect models.Interconnect
	if err := db.First(&interconnect, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Interconnect record")
			return
		}
		HandleInternalError(c, "Failed to fetch interconnect record: "+err.Error())
		return
	}

	// Update interconnect record fields
	interconnect.SourceType = req.SourceType
	interconnect.SourceID = req.SourceID
	interconnect.TargetType = req.TargetType
	interconnect.TargetID = req.TargetID
	interconnect.Relationship = req.Relationship
	interconnect.Description = req.Description
	interconnect.IsActive = req.IsActive

	if err := db.Save(&interconnect).Error; err != nil {
		HandleInternalError(c, "Failed to update interconnect record: "+err.Error())
		return
	}

	response := gin.H{
		"id":           interconnect.ID,
		"source_type":  interconnect.SourceType,
		"source_id":    interconnect.SourceID,
		"target_type":  interconnect.TargetType,
		"target_id":    interconnect.TargetID,
		"relationship": interconnect.Relationship,
		"description":  interconnect.Description,
		"is_active":    interconnect.IsActive,
		"created_at":   interconnect.CreatedAt,
		"updated_at":   interconnect.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Interconnect record updated successfully",
		Data:    response,
	})
}

// DeleteInterconnect deletes an interconnect record
func DeleteInterconnect(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if interconnect record exists
	var interconnect models.Interconnect
	if err := db.First(&interconnect, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Interconnect record")
			return
		}
		HandleInternalError(c, "Failed to fetch interconnect record: "+err.Error())
		return
	}

	// Delete interconnect record
	if err := db.Delete(&interconnect).Error; err != nil {
		HandleInternalError(c, "Failed to delete interconnect record: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Interconnect record deleted successfully",
	})
}
