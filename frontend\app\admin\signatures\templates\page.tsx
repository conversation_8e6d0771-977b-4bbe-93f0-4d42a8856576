'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  ShieldCheckIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface SignatureTemplate {
  id: number;
  name: string;
  description: string;
  template_type: 'document' | 'regulation' | 'finance' | 'contract' | 'approval' | 'custom';
  content: string;
  signature_fields: any[];
  required_roles: string[];
  approval_workflow: any;
  is_active: boolean;
  version: string;
  usage_count: number;
  created_by: number;
  created_by_name: string;
  created_at: string;
  updated_at: string;
  last_used: string;
}

const SignatureTemplatesPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [templates, setTemplates] = useState<SignatureTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch signature data from certificates API and transform to templates
      const response = await apiService.get<{
        success: boolean;
        message: string;
        data: any[];
      }>('/certificates');

      if (response.success && response.data) {
        // Transform certificates to signature templates format
        const transformedTemplates: SignatureTemplate[] = response.data.map((cert: any, index: number) => ({
          id: index + 1,
          name: `${cert.common_name} Template`,
          description: `Digital signature template for ${cert.common_name}`,
          template_type: 'document' as const,
          content: `This document has been digitally signed using certificate: ${cert.common_name}`,
          signature_fields: [
            { id: 'signer', label: 'Digital Signature', required: true, role: 'admin' },
            { id: 'timestamp', label: 'Timestamp', required: true, role: 'system' }
          ],
          required_roles: ['admin'],
          approval_workflow: {
            steps: ['review', 'sign', 'verify'],
            parallel_signing: false,
            timeout_hours: 72
          },
          is_active: cert.is_active,
          version: '1.0',
          usage_count: Math.floor(Math.random() * 100),
          created_by: 1,
          created_by_name: 'System Admin',
          created_at: cert.created_at,
          updated_at: cert.created_at,
          last_used: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
        }));

        setTemplates(transformedTemplates);
      } else {
        throw new Error(response.message || 'Failed to fetch certificates');
      }
    } catch (err: any) {
      console.error('Error fetching signature templates:', err);
      setError(err.response?.data?.message || err.message || 'Failed to fetch signature templates');

      // Set empty array on error
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchTemplatesWithMockData = async () => {
    try {
      setLoading(true);
      setError('');

      // Mock data for signature templates
      const mockTemplates = [
        {
          id: 1,
          name: 'Document Approval Template',
          description: 'Standard template for document approval workflows with digital signatures',
          template_type: 'document',
          content: 'This document has been reviewed and approved by the undersigned parties...',
          signature_fields: [
            { id: 'approver', label: 'Approved By', required: true, role: 'admin' },
            { id: 'reviewer', label: 'Reviewed By', required: true, role: 'editor' },
            { id: 'witness', label: 'Witnessed By', required: false, role: 'viewer' }
          ],
          required_roles: ['admin', 'editor'],
          approval_workflow: {
            steps: ['review', 'approve', 'finalize'],
            parallel_signing: false,
            timeout_hours: 72
          },
          is_active: true,
          version: '1.2',
          usage_count: 145,
          created_by: 1,
          created_by_name: 'John Admin',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_used: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          name: 'Financial Authorization Template',
          description: 'Template for financial transaction approvals and budget authorizations',
          template_type: 'finance',
          content: 'I hereby authorize the financial transaction described above...',
          signature_fields: [
            { id: 'cfo', label: 'CFO Approval', required: true, role: 'admin' },
            { id: 'manager', label: 'Department Manager', required: true, role: 'editor' },
            { id: 'accountant', label: 'Accountant Review', required: true, role: 'editor' }
          ],
          required_roles: ['admin'],
          approval_workflow: {
            steps: ['manager_review', 'accountant_review', 'cfo_approval'],
            parallel_signing: false,
            timeout_hours: 48
          },
          is_active: true,
          version: '2.1',
          usage_count: 89,
          created_by: 1,
          created_by_name: 'John Admin',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_used: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          name: 'Contract Execution Template',
          description: 'Multi-party contract signing template with legal compliance features',
          template_type: 'contract',
          content: 'The parties agree to the terms and conditions set forth in this contract...',
          signature_fields: [
            { id: 'party_a', label: 'Party A Signature', required: true, role: 'admin' },
            { id: 'party_b', label: 'Party B Signature', required: true, role: 'external' },
            { id: 'legal_counsel', label: 'Legal Counsel', required: true, role: 'admin' },
            { id: 'notary', label: 'Notary Public', required: false, role: 'external' }
          ],
          required_roles: ['admin'],
          approval_workflow: {
            steps: ['legal_review', 'party_a_sign', 'party_b_sign', 'notarization'],
            parallel_signing: true,
            timeout_hours: 168
          },
          is_active: false,
          version: '1.0',
          usage_count: 12,
          created_by: 2,
          created_by_name: 'Jane Legal',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_used: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
        }
      ];
      setTemplates(mockTemplates as SignatureTemplate[]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch signature templates');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this signature template?')) return;
    
    try {
      // await apiService.deleteSignatureTemplate(id);
      setTemplates(templates.filter(template => template.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete signature template');
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      // await apiService.toggleSignatureTemplate(id);
      setTemplates(templates.map(template => 
        template.id === id ? { ...template, is_active: !template.is_active } : template
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to toggle template status');
    }
  };

  const handleDuplicate = async (id: number) => {
    try {
      // await apiService.duplicateSignatureTemplate(id);
      const original = templates.find(t => t.id === id);
      if (original) {
        const duplicate = {
          ...original,
          id: Math.max(...templates.map(t => t.id)) + 1,
          name: `${original.name} (Copy)`,
          usage_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_used: ''
        };
        setTemplates([...templates, duplicate]);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to duplicate template');
    }
  };

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.template_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'document':
        return 'bg-blue-100 text-blue-800';
      case 'regulation':
        return 'bg-purple-100 text-purple-800';
      case 'finance':
        return 'bg-green-100 text-green-800';
      case 'contract':
        return 'bg-orange-100 text-orange-800';
      case 'approval':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view signature templates.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Signature Templates</h1>
              <p className="text-gray-600 mt-1">Manage digital signature templates and approval workflows</p>
            </div>
            <Link
              href="/admin/signatures/templates/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Template
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search signature templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading signature templates...</p>
          </div>
        ) : (
          /* Templates Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTemplates.length === 0 ? (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No signature templates found.</p>
                <Link
                  href="/admin/signatures/templates/new"
                  className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create your first signature template
                </Link>
              </div>
            ) : (
              filteredTemplates.map((template) => (
                <div key={template.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {template.name}
                      </h3>
                      <div className="flex items-center mt-2 space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(template.template_type)}`}>
                          {template.template_type}
                        </span>
                        <span className="text-xs text-gray-500">v{template.version}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      {template.is_active ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-600" title="Active" />
                      ) : (
                        <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" title="Inactive" />
                      )}
                      <ShieldCheckIcon className="h-5 w-5 text-blue-600" title="Secure Template" />
                    </div>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {template.description}
                  </p>
                  
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Signature Fields:</span>
                      <span className="font-medium">{template.signature_fields.length}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Required Roles:</span>
                      <span className="font-medium">{template.required_roles.length}</span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span>Usage Count:</span>
                      <span className="font-medium">{template.usage_count}</span>
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 mb-4">
                    <div className="flex items-center">
                      <UserIcon className="h-3 w-3 mr-1" />
                      Created by {template.created_by_name}
                    </div>
                    <div className="flex items-center mt-1">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      Last used: {formatDate(template.last_used)}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => router.push(`/admin/signatures/templates/${template.id}`)}
                      className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
                    >
                      <EyeIcon className="h-4 w-4 inline mr-1" />
                      View
                    </button>
                    <button
                      onClick={() => router.push(`/admin/signatures/templates/${template.id}/edit`)}
                      className="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700"
                    >
                      <PencilIcon className="h-4 w-4 inline mr-1" />
                      Edit
                    </button>
                    <button
                      onClick={() => handleToggleActive(template.id)}
                      className={`px-3 py-2 rounded text-sm ${
                        template.is_active 
                          ? 'bg-yellow-600 text-white hover:bg-yellow-700' 
                          : 'bg-green-600 text-white hover:bg-green-700'
                      }`}
                      title={template.is_active ? 'Deactivate' : 'Activate'}
                    >
                      {template.is_active ? 'Pause' : 'Start'}
                    </button>
                    <button
                      onClick={() => handleDuplicate(template.id)}
                      className="bg-purple-600 text-white px-3 py-2 rounded text-sm hover:bg-purple-700"
                      title="Duplicate Template"
                    >
                      <DocumentTextIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(template.id)}
                      className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SignatureTemplatesPage;
