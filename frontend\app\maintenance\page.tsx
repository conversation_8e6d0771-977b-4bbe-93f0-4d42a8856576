'use client'

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import {
  WrenchScrewdriverIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CalendarIcon,
  ServerIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';

interface MaintenanceInfo {
  title: string;
  description: string;
  start_time: string;
  estimated_end_time: string;
  actual_end_time?: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'extended';
  affected_services: string[];
  progress_percentage: number;
  updates: MaintenanceUpdate[];
}

interface MaintenanceUpdate {
  id: string;
  timestamp: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
}

const MaintenancePage: React.FC = () => {
  const [maintenanceInfo, setMaintenanceInfo] = useState<MaintenanceInfo>({
    title: 'Scheduled System Maintenance',
    description: 'We are performing scheduled maintenance to improve system performance and security. During this time, some services may be temporarily unavailable.',
    start_time: new Date().toISOString(),
    estimated_end_time: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
    status: 'in_progress',
    affected_services: ['Document Management', 'User Authentication', 'Search Services', 'API Gateway'],
    progress_percentage: 65,
    updates: [
      {
        id: '1',
        timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
        message: 'Maintenance started. Beginning database optimization.',
        type: 'info'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
        message: 'Database optimization completed. Starting security updates.',
        type: 'success'
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
        message: 'Security updates in progress. Some services may be temporarily unavailable.',
        type: 'warning'
      }
    ]
  });

  const [currentTime, setCurrentTime] = useState(new Date());
  const [timeRemaining, setTimeRemaining] = useState('');

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
      
      const endTime = new Date(maintenanceInfo.estimated_end_time);
      const now = new Date();
      const diff = endTime.getTime() - now.getTime();
      
      if (diff > 0) {
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
        setTimeRemaining(`${hours}h ${minutes}m`);
      } else {
        setTimeRemaining('Completing soon...');
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [maintenanceInfo.estimated_end_time]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <CalendarIcon className="h-8 w-8 text-blue-600" />;
      case 'in_progress':
        return <WrenchScrewdriverIcon className="h-8 w-8 text-orange-600" />;
      case 'completed':
        return <CheckCircleIcon className="h-8 w-8 text-green-600" />;
      case 'extended':
        return <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />;
      default:
        return <ClockIcon className="h-8 w-8 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'text-blue-600 bg-blue-100';
      case 'in_progress':
        return 'text-orange-600 bg-orange-100';
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'extended':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getUpdateIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-orange-100 mb-6">
              {getStatusIcon(maintenanceInfo.status)}
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">{maintenanceInfo.title}</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {maintenanceInfo.description}
            </p>
          </div>

          {/* Status Overview */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <div className="mr-4">
                  {getStatusIcon(maintenanceInfo.status)}
                </div>
                <div>
                  <h2 className="text-2xl font-semibold text-gray-900">
                    Status: {maintenanceInfo.status.replace('_', ' ').toUpperCase()}
                  </h2>
                  <p className="text-gray-600">
                    {maintenanceInfo.status === 'in_progress' && `Estimated completion: ${timeRemaining}`}
                    {maintenanceInfo.status === 'completed' && 'Maintenance has been completed successfully'}
                    {maintenanceInfo.status === 'scheduled' && 'Maintenance is scheduled to begin soon'}
                    {maintenanceInfo.status === 'extended' && 'Maintenance is taking longer than expected'}
                  </p>
                </div>
              </div>
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(maintenanceInfo.status)}`}>
                {maintenanceInfo.status.replace('_', ' ').toUpperCase()}
              </span>
            </div>

            {/* Progress Bar */}
            {maintenanceInfo.status === 'in_progress' && (
              <div className="mb-6">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{maintenanceInfo.progress_percentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-orange-600 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${maintenanceInfo.progress_percentage}%` }}
                  ></div>
                </div>
              </div>
            )}

            {/* Timeline */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <CalendarIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <h3 className="text-sm font-medium text-gray-900">Start Time</h3>
                <p className="text-sm text-gray-600">
                  {new Date(maintenanceInfo.start_time).toLocaleString()}
                </p>
              </div>
              
              <div className="text-center">
                <ClockIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <h3 className="text-sm font-medium text-gray-900">Estimated End</h3>
                <p className="text-sm text-gray-600">
                  {new Date(maintenanceInfo.estimated_end_time).toLocaleString()}
                </p>
              </div>
              
              <div className="text-center">
                <ArrowPathIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                <h3 className="text-sm font-medium text-gray-900">Duration</h3>
                <p className="text-sm text-gray-600">
                  {Math.round((new Date(maintenanceInfo.estimated_end_time).getTime() - new Date(maintenanceInfo.start_time).getTime()) / (1000 * 60 * 60))} hours
                </p>
              </div>
            </div>
          </div>

          {/* Affected Services */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Affected Services</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {maintenanceInfo.affected_services.map((service, index) => (
                <div key={index} className="flex items-center p-3 border border-gray-200 rounded-lg">
                  <ServerIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">{service}</span>
                  <span className="ml-auto text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full">
                    {maintenanceInfo.status === 'completed' ? 'Restored' : 'Affected'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Live Updates */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Live Updates</h2>
            <div className="space-y-4">
              {maintenanceInfo.updates.map((update) => (
                <div key={update.id} className="flex items-start p-4 border border-gray-200 rounded-lg">
                  <div className="mr-3 mt-0.5">
                    {getUpdateIcon(update.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="text-sm font-medium text-gray-900">{update.message}</p>
                      <span className="text-xs text-gray-500">
                        {new Date(update.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* What You Can Do */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">What You Can Do</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
                  <span className="text-sm text-gray-700">Check back in a few minutes for updates</span>
                </div>
                <div className="flex items-start">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
                  <span className="text-sm text-gray-700">Follow our status page for real-time updates</span>
                </div>
                <div className="flex items-start">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
                  <span className="text-sm text-gray-700">Contact support if you have urgent needs</span>
                </div>
                <div className="flex items-start">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5" />
                  <span className="text-sm text-gray-700">Subscribe to maintenance notifications</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Need Immediate Help?</h3>
              <div className="space-y-4">
                <Link
                  href="/contact"
                  className="block w-full text-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700"
                >
                  Contact Emergency Support
                </Link>
                <Link
                  href="/status"
                  className="block w-full text-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  View System Status
                </Link>
                <a
                  href="tel:**************"
                  className="block w-full text-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  Call Support: 1-800-NOTECONTROL
                </a>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-blue-50 rounded-lg p-6">
            <div className="flex items-start">
              <ShieldCheckIcon className="h-6 w-6 text-blue-600 mr-3 mt-1" />
              <div>
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Security & Data Protection</h3>
                <div className="space-y-2 text-sm text-blue-700">
                  <p>• All your data remains secure during maintenance</p>
                  <p>• No data will be lost or compromised</p>
                  <p>• Security updates are being applied to enhance protection</p>
                  <p>• System backups are completed before any changes</p>
                </div>
              </div>
            </div>
          </div>

          {/* Auto-refresh notice */}
          <div className="mt-8 text-center">
            <p className="text-sm text-gray-500">
              This page automatically refreshes every 30 seconds to provide the latest updates.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default MaintenancePage;
