package handlers

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// FinanceRequest represents the request structure for finance records
type FinanceRequest struct {
	Amount                float64 `json:"amount" binding:"required"`
	Year                  int     `json:"year" binding:"required"`
	Description           string  `json:"description"`
	DocumentID            *uint   `json:"document_id"`
	RegulationID          *uint   `json:"regulation_id"`
	BudgetType            string  `json:"budget_type"`
	PerformancePercentage float64 `json:"performance_percentage"`
	IsAutoCalculated      bool    `json:"is_auto_calculated"`
	SourceFinanceID       *uint   `json:"source_finance_id"`
	CategoryID            *uint   `json:"category_id"`
}

// GetFinanceRecords returns all finance records with pagination
func GetFinanceRecords(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.<PERSON><PERSON>ult<PERSON><PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query with filters
	query := db.Model(&models.Finance{})

	// Apply filters
	if year := c.Query("year"); year != "" {
		query = query.Where("year = ?", year)
	}
	if budgetType := c.Query("budget_type"); budgetType != "" {
		query = query.Where("budget_type = ?", budgetType)
	}
	if categoryID := c.Query("category_id"); categoryID != "" {
		query = query.Where("category_id = ?", categoryID)
	}
	if documentID := c.Query("document_id"); documentID != "" {
		query = query.Where("document_id = ?", documentID)
	}
	if regulationID := c.Query("regulation_id"); regulationID != "" {
		query = query.Where("regulation_id = ?", regulationID)
	}

	// Count total finance records
	var total int64
	query.Count(&total)

	// Get finance records with pagination
	var records []models.Finance
	offset := (page - 1) * perPage
	if err := query.Preload("Category").
		Preload("Document").
		Preload("Regulation").
		Order("year DESC, created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&records).Error; err != nil {
		HandleInternalError(c, "Failed to fetch finance records: "+err.Error())
		return
	}

	// Convert to response format
	recordResponses := make([]gin.H, len(records))
	for i, record := range records {
		recordResponses[i] = gin.H{
			"id":                     record.ID,
			"amount":                 record.Amount,
			"year":                   record.Year,
			"description":            record.Description,
			"budget_type":            record.BudgetType,
			"performance_percentage": record.PerformancePercentage,
			"is_auto_calculated":     record.IsAutoCalculated,
			"category":               record.Category,
			"document":               record.Document,
			"regulation":             record.Regulation,
			"created_at":             record.CreatedAt,
			"updated_at":             record.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       recordResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetFinanceRecord returns a single finance record by ID
func GetFinanceRecord(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get finance record
	var record models.Finance
	if err := db.Preload("Category").
		Preload("Document").
		Preload("Regulation").
		First(&record, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Finance record")
			return
		}
		HandleInternalError(c, "Failed to fetch finance record: "+err.Error())
		return
	}

	response := gin.H{
		"id":                     record.ID,
		"amount":                 record.Amount,
		"year":                   record.Year,
		"description":            record.Description,
		"budget_type":            record.BudgetType,
		"performance_percentage": record.PerformancePercentage,
		"is_auto_calculated":     record.IsAutoCalculated,
		"category":               record.Category,
		"document":               record.Document,
		"regulation":             record.Regulation,
		"created_at":             record.CreatedAt,
		"updated_at":             record.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Finance record retrieved successfully",
		Data:    response,
	})
}

// CreateFinanceRecord creates a new finance record
func CreateFinanceRecord(c *gin.Context) {
	var req FinanceRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create finance record
	record := &models.Finance{
		Amount:                req.Amount,
		Year:                  req.Year,
		Description:           req.Description,
		BudgetType:            req.BudgetType,
		PerformancePercentage: req.PerformancePercentage,
		DocumentID:            req.DocumentID,
		RegulationID:          req.RegulationID,
		IsAutoCalculated:      req.IsAutoCalculated,
		SourceFinanceID:       req.SourceFinanceID,
		CategoryID:            req.CategoryID,
	}

	if err := db.Create(record).Error; err != nil {
		HandleInternalError(c, "Failed to create finance record: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Category").Preload("Document").Preload("Regulation").First(record, record.ID)

	response := gin.H{
		"id":                     record.ID,
		"amount":                 record.Amount,
		"year":                   record.Year,
		"description":            record.Description,
		"budget_type":            record.BudgetType,
		"performance_percentage": record.PerformancePercentage,
		"is_auto_calculated":     record.IsAutoCalculated,
		"category":               record.Category,
		"document":               record.Document,
		"regulation":             record.Regulation,
		"created_at":             record.CreatedAt,
		"updated_at":             record.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Finance record created successfully",
		Data:    response,
	})
}

// UpdateFinanceRecord updates an existing finance record
func UpdateFinanceRecord(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req FinanceRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing finance record
	var record models.Finance
	if err := db.First(&record, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Finance record")
			return
		}
		HandleInternalError(c, "Failed to fetch finance record: "+err.Error())
		return
	}

	// Update finance record fields
	record.Description = req.Description
	record.BudgetType = req.BudgetType
	record.Amount = req.Amount
	record.Year = req.Year
	record.DocumentID = req.DocumentID
	record.RegulationID = req.RegulationID
	record.PerformancePercentage = req.PerformancePercentage
	record.IsAutoCalculated = req.IsAutoCalculated
	record.SourceFinanceID = req.SourceFinanceID
	record.CategoryID = req.CategoryID

	if err := db.Save(&record).Error; err != nil {
		HandleInternalError(c, "Failed to update finance record: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Category").Preload("Agency").First(&record, record.ID)

	response := gin.H{
		"id":                     record.ID,
		"amount":                 record.Amount,
		"year":                   record.Year,
		"description":            record.Description,
		"budget_type":            record.BudgetType,
		"performance_percentage": record.PerformancePercentage,
		"is_auto_calculated":     record.IsAutoCalculated,
		"category":               record.Category,
		"document":               record.Document,
		"regulation":             record.Regulation,
		"created_at":             record.CreatedAt,
		"updated_at":             record.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Finance record updated successfully",
		Data:    response,
	})
}

// DeleteFinanceRecord deletes a finance record
func DeleteFinanceRecord(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if finance record exists
	var record models.Finance
	if err := db.First(&record, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Finance record")
			return
		}
		HandleInternalError(c, "Failed to fetch finance record: "+err.Error())
		return
	}

	// Delete finance record
	if err := db.Delete(&record).Error; err != nil {
		HandleInternalError(c, "Failed to delete finance record: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Finance record deleted successfully",
	})
}

// GetFinances is an alias for GetFinanceRecords
func GetFinances(c *gin.Context) {
	GetFinanceRecords(c)
}

// CreateFinance is an alias for CreateFinanceRecord
func CreateFinance(c *gin.Context) {
	CreateFinanceRecord(c)
}

// GetFinance is an alias for GetFinanceRecord
func GetFinance(c *gin.Context) {
	GetFinanceRecord(c)
}

// UpdateFinance is an alias for UpdateFinanceRecord
func UpdateFinance(c *gin.Context) {
	UpdateFinanceRecord(c)
}

// DeleteFinance is an alias for DeleteFinanceRecord
func DeleteFinance(c *gin.Context) {
	DeleteFinanceRecord(c)
}

// AggregateFinanceByYear aggregates finance data by year
func AggregateFinanceByYear(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get year parameter (optional)
	year := c.Query("year")

	// Build aggregation query
	var results []struct {
		Year               int     `json:"year"`
		TotalAmount        float64 `json:"total_amount"`
		OriginalBudget     float64 `json:"original_budget"`
		ActualBudget       float64 `json:"actual_budget"`
		AveragePerformance float64 `json:"average_performance"`
		RecordCount        int     `json:"record_count"`
	}

	query := db.Model(&models.Finance{}).
		Select(`
			year,
			SUM(amount) as total_amount,
			SUM(CASE WHEN budget_type = 'original' THEN amount ELSE 0 END) as original_budget,
			SUM(CASE WHEN budget_type = 'actual' THEN amount ELSE 0 END) as actual_budget,
			AVG(performance_percentage) as average_performance,
			COUNT(*) as record_count
		`).
		Group("year").
		Order("year DESC")

	// Filter by year if provided
	if year != "" {
		query = query.Where("year = ?", year)
	}

	if err := query.Scan(&results).Error; err != nil {
		HandleInternalError(c, "Failed to aggregate finance data: "+err.Error())
		return
	}

	// Calculate additional metrics
	for i := range results {
		if results[i].OriginalBudget > 0 {
			results[i].ActualBudget = (results[i].ActualBudget / results[i].OriginalBudget) * 100
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Finance data aggregated successfully",
		Data: gin.H{
			"aggregations": results,
			"total_years":  len(results),
		},
	})
}

// GetBudgetSummary returns budget summary
func GetBudgetSummary(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get year parameter (default to current year)
	year := c.DefaultQuery("year", strconv.Itoa(time.Now().Year()))

	// Get budget summary data
	var summary struct {
		TotalOriginalBudget float64 `json:"total_original_budget"`
		TotalActualBudget   float64 `json:"total_actual_budget"`
		TotalRecords        int     `json:"total_records"`
		AveragePerformance  float64 `json:"average_performance"`
	}

	err := db.Model(&models.Finance{}).
		Select(`
			SUM(CASE WHEN budget_type = 'original' THEN amount ELSE 0 END) as total_original_budget,
			SUM(CASE WHEN budget_type = 'actual' THEN amount ELSE 0 END) as total_actual_budget,
			COUNT(*) as total_records,
			AVG(performance_percentage) as average_performance
		`).
		Where("year = ?", year).
		Scan(&summary).Error

	if err != nil {
		HandleInternalError(c, "Failed to get budget summary: "+err.Error())
		return
	}

	// Calculate variance
	variance := summary.TotalActualBudget - summary.TotalOriginalBudget
	variancePercent := float64(0)
	if summary.TotalOriginalBudget > 0 {
		variancePercent = (variance / summary.TotalOriginalBudget) * 100
	}

	// Get category breakdown
	var categoryBreakdown []struct {
		CategoryID   *uint   `json:"category_id"`
		CategoryName string  `json:"category_name"`
		TotalAmount  float64 `json:"total_amount"`
		RecordCount  int     `json:"record_count"`
	}

	db.Model(&models.Finance{}).
		Select(`
			f.category_id,
			COALESCE(fc.name, 'Uncategorized') as category_name,
			SUM(f.amount) as total_amount,
			COUNT(*) as record_count
		`).
		Joins("LEFT JOIN finance_categories fc ON f.category_id = fc.id").
		Where("f.year = ?", year).
		Group("f.category_id, fc.name").
		Order("total_amount DESC").
		Scan(&categoryBreakdown)

	// Get monthly breakdown
	var monthlyBreakdown []struct {
		Month       int     `json:"month"`
		TotalAmount float64 `json:"total_amount"`
		RecordCount int     `json:"record_count"`
	}

	db.Model(&models.Finance{}).
		Select(`
			EXTRACT(MONTH FROM created_at) as month,
			SUM(amount) as total_amount,
			COUNT(*) as record_count
		`).
		Where("year = ?", year).
		Group("EXTRACT(MONTH FROM created_at)").
		Order("month").
		Scan(&monthlyBreakdown)

	responseData := gin.H{
		"year":               year,
		"summary":            summary,
		"variance":           variance,
		"variance_percent":   variancePercent,
		"category_breakdown": categoryBreakdown,
		"monthly_breakdown":  monthlyBreakdown,
		"generated_at":       time.Now(),
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Budget summary retrieved successfully",
		Data:    responseData,
	})
}

// CreatePerformance creates a new performance record
func CreatePerformance(c *gin.Context) {
	var req struct {
		DocumentID            *uint   `json:"document_id"`
		RegulationID          *uint   `json:"regulation_id"`
		Year                  int     `json:"year" binding:"required"`
		PerformancePercentage float64 `json:"performance_percentage" binding:"required"`
		PerformanceNotes      string  `json:"performance_notes"`
		EvaluatedByID         *uint   `json:"evaluated_by_id"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Validate that at least one target is specified
	if req.DocumentID == nil && req.RegulationID == nil {
		HandleBadRequest(c, "Either document_id or regulation_id must be specified")
		return
	}

	// Validate document if provided
	if req.DocumentID != nil {
		var document models.Document
		if err := db.First(&document, *req.DocumentID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Document")
				return
			}
			HandleInternalError(c, "Failed to validate document: "+err.Error())
			return
		}
	}

	// Validate regulation if provided
	if req.RegulationID != nil {
		var regulation models.LawsAndRules
		if err := db.First(&regulation, *req.RegulationID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Regulation")
				return
			}
			HandleInternalError(c, "Failed to validate regulation: "+err.Error())
			return
		}
	}

	// Set evaluator if not provided
	evaluatedByID := req.EvaluatedByID
	if evaluatedByID == nil {
		evaluatedByID = userID.(*uint)
	}

	// Create performance record
	performance := models.FinancePerformance{
		DocumentID:            req.DocumentID,
		RegulationID:          req.RegulationID,
		Year:                  req.Year,
		PerformancePercentage: req.PerformancePercentage,
		PerformanceNotes:      req.PerformanceNotes,
		EvaluationDate:        time.Now(),
		EvaluatedByID:         evaluatedByID,
	}

	if err := db.Create(&performance).Error; err != nil {
		HandleInternalError(c, "Failed to create performance record: "+err.Error())
		return
	}

	// Load the created performance with relationships
	if err := db.Preload("Document").Preload("Regulation").Preload("EvaluatedBy").First(&performance, performance.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created performance: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Performance record created successfully",
		Data:    performance,
	})
}

// GetPerformances returns all performance records
func GetPerformances(c *gin.Context) {
	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.FinancePerformance{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("performance_notes ILIKE ?", "%"+search.Query+"%")
	}

	// Filter by year if provided
	if year := c.Query("year"); year != "" {
		query = query.Where("year = ?", year)
	}

	// Filter by document ID if provided
	if documentID := c.Query("document_id"); documentID != "" {
		query = query.Where("document_id = ?", documentID)
	}

	// Filter by regulation ID if provided
	if regulationID := c.Query("regulation_id"); regulationID != "" {
		query = query.Where("regulation_id = ?", regulationID)
	}

	// Filter by performance range if provided
	if minPerf := c.Query("min_performance"); minPerf != "" {
		query = query.Where("performance_percentage >= ?", minPerf)
	}
	if maxPerf := c.Query("max_performance"); maxPerf != "" {
		query = query.Where("performance_percentage <= ?", maxPerf)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("evaluation_date DESC")
	}

	// Get performances with relationships
	var performances []models.FinancePerformance
	if err := query.Preload("Document").Preload("Regulation").Preload("EvaluatedBy").Find(&performances).Error; err != nil {
		HandleInternalError(c, "Failed to fetch performance records: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       performances,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetFinanceCategories returns finance categories
func GetFinanceCategories(c *gin.Context) {
	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.FinanceCategory{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		if search.Status == "active" {
			query = query.Where("is_active = ?", true)
		} else if search.Status == "inactive" {
			query = query.Where("is_active = ?", false)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("name ASC")
	}

	// Get categories
	var categories []models.FinanceCategory
	if err := query.Find(&categories).Error; err != nil {
		HandleInternalError(c, "Failed to fetch finance categories: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       categories,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateFinanceCategory creates a new finance category
func CreateFinanceCategory(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		Color       string `json:"color"`
		IsActive    *bool  `json:"is_active"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Set default values
	if req.Color == "" {
		req.Color = "#3B82F6"
	}
	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	// Create finance category
	category := models.FinanceCategory{
		Name:        req.Name,
		Description: req.Description,
		Color:       req.Color,
		IsActive:    isActive,
	}

	if err := db.Create(&category).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Finance category with this name already exists")
			return
		}
		HandleInternalError(c, "Failed to create finance category: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Finance category created successfully",
		Data:    category,
	})
}
