'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircleIcon,
  XMarkIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';

interface RegulationFormData {
  title: string;
  short_title: string;
  slug: string;
  type: string;
  status: string;
  description: string;
  content: string;
  notes: string;

  // Legal identifiers
  public_law_number: string;
  regulatory_identifier: string;
  cfr_title: string;
  usc_title: string;
  docket_number: string;

  // Hierarchical structure
  hierarchy_level: string;
  chapter_number: string;
  subchapter: string;
  part_number: string;
  section_number: string;
  subsection: string;
  parent_id: string;
  order_in_parent: number;

  // Dates
  enactment_date: string;
  effective_date: string;
  termination_date: string;
  publication_date: string;

  // Relationships
  agency_id: string;
  current_document_version_id: string;

  // Metadata
  is_significant: boolean;

  // Legacy fields for compatibility (will be removed in cleanup)
  cfr_citation: string;
  regulatory_impact_analysis: boolean;
  small_business_impact: boolean;
  environmental_impact: boolean;
  federalism_implications: boolean;
}

// Helper function to get today's date in YYYY-MM-DD format
const getTodayDate = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

// Helper function to get smart defaults for CFR/USC numbers
const getSmartDefaults = () => {
  const currentYear = new Date().getFullYear();
  return {
    cfr_title: '40', // Default to EPA regulations
    usc_title: '42', // Default to Public Health
    chapter_number: '1',
    publication_date: getTodayDate(),
    effective_date: getTodayDate(),
  };
};

const NewRegulationPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [agencies, setAgencies] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [regulations, setRegulations] = useState<any[]>([]);

  const smartDefaults = getSmartDefaults();
  const [formData, setFormData] = useState<RegulationFormData>({
    title: '',
    short_title: '',
    slug: '',
    type: 'regulation',
    status: 'draft',
    description: '',
    content: '',
    notes: '',

    // Legal identifiers
    public_law_number: '',
    regulatory_identifier: '',
    cfr_title: smartDefaults.cfr_title,
    usc_title: smartDefaults.usc_title,
    docket_number: '',

    // Hierarchical structure
    hierarchy_level: 'regulation',
    chapter_number: smartDefaults.chapter_number,
    subchapter: '',
    part_number: '',
    section_number: '',
    subsection: '',
    parent_id: '',
    order_in_parent: 0,

    // Dates
    enactment_date: '',
    effective_date: smartDefaults.effective_date,
    termination_date: '',
    publication_date: smartDefaults.publication_date,

    // Relationships
    agency_id: '',
    current_document_version_id: '',

    // Metadata
    is_significant: false,

    // Legacy fields for compatibility
    cfr_citation: '',
    regulatory_impact_analysis: false,
    small_business_impact: false,
    environmental_impact: false,
    federalism_implications: false,
  });
  const [tagInput, setTagInput] = useState('');

  useEffect(() => {
    if (!isAuthenticated || (user?.role !== 'admin' && user?.role !== 'editor')) {
      router.push('/dashboard');
      return;
    }

    const fetchData = async () => {
      try {
        // Fetch agencies
        const agenciesResponse = await apiService.getAgencies({ per_page: 100 });
        setAgencies(agenciesResponse.data);

        // Fetch categories
        const categoriesResponse = await apiService.getCategories({ per_page: 100 });
        setCategories(categoriesResponse.data);

        // Fetch existing regulations for parent/supersedes relationships
        const regulationsResponse = await apiService.getRegulations({ per_page: 100 });
        setRegulations(regulationsResponse.data);

        // Load default values for new regulation
        await loadDefaultValues();

        // Set default agency and category if available
        if (agenciesResponse.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            agency_id: prev.agency_id || agenciesResponse.data[0].id.toString()
          }));
        }

        if (categoriesResponse.data.length > 0) {
          setFormData(prev => ({
            ...prev,
            category_id: prev.category_id || categoriesResponse.data[0].id.toString()
          }));
        }
      } catch (err) {
        console.error('Error fetching data:', err);
      }
    };

    fetchData();
  }, [isAuthenticated, user, router]);

  // Load default values from preloading API
  const loadDefaultValues = async () => {
    try {
      const response = await apiService.getRegulationDefaults();
      const defaults = response.data;

      // Format dates for input fields (YYYY-MM-DD)
      const formatDate = (dateString: string) => {
        if (!dateString) return '';
        return new Date(dateString).toISOString().split('T')[0];
      };

      // Get current date for default values
      const today = new Date().toISOString().split('T')[0];
      const futureDate = new Date();
      futureDate.setDate(futureDate.getDate() + 30);
      const future30Days = futureDate.toISOString().split('T')[0];

      setFormData(prev => ({
        ...prev,
        enactment_date: formatDate(defaults.enactment_date) || today,
        publication_date: formatDate(defaults.publication_date) || today,
        effective_date: formatDate(defaults.effective_date) || future30Days,
        version: defaults.version_number || '1.0.0',
        status: defaults.status || 'draft',
        type: defaults.type || 'regulation',
        hierarchy_level: defaults.hierarchy_level || 'regulation',
        is_significant: defaults.is_significant !== undefined ? defaults.is_significant : false,
        public_law_number: defaults.public_law_number || '',
        regulatory_identifier: defaults.regulatory_identifier || '',
        docket_number: defaults.docket_number || '',
      }));
    } catch (err) {
      console.error('Error loading default values:', err);
    }
  };

  // Regenerate Public Law Number
  const regeneratePublicLawNumber = async () => {
    try {
      const response = await apiService.getPublicLawNumber();
      const data = response.data;
      setFormData(prev => ({
        ...prev,
        public_law_number: data.public_law_number || ''
      }));
    } catch (err) {
      console.error('Error regenerating public law number:', err);
    }
  };

  // Regenerate Regulatory Identifier (RIN)
  const regenerateRegulatoryIdentifier = async (agencyId: string) => {
    if (!agencyId) return;

    try {
      const response = await apiService.getRegulatoryIdentifier(parseInt(agencyId));
      const data = response.data;
      setFormData(prev => ({
        ...prev,
        regulatory_identifier: data.regulatory_identifier || ''
      }));
    } catch (err) {
      console.error('Error regenerating regulatory identifier:', err);
    }
  };

  // Regenerate Regulation Docket Number
  const regenerateRegulationDocketNumber = async (agencyId: string) => {
    if (!agencyId) return;

    try {
      const response = await apiService.getRegulationDocketNumber(parseInt(agencyId));
      const data = response.data;
      setFormData(prev => ({
        ...prev,
        docket_number: data.regulation_docket_number || ''
      }));
    } catch (err) {
      console.error('Error regenerating regulation docket number:', err);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));

      // Reload defaults when parent regulation changes
      if (name === 'parent_regulation_id' && value) {
        reloadDefaultsForParent(value);
      }

      // Regenerate identifiers when agency changes
      if (name === 'agency_id' && value) {
        regenerateRegulatoryIdentifier(value);
        regenerateRegulationDocketNumber(value);
      }

      // Smart date calculations when publication date changes
      if (name === 'publication_date' && value) {
        calculateRelatedDates(value);
      }
    }
  };

  // Calculate related dates based on publication date
  const calculateRelatedDates = (publicationDate: string) => {
    if (!publicationDate) return;

    const pubDate = new Date(publicationDate);

    // Calculate effective date (30 days after publication for regulations)
    const effectiveDate = new Date(pubDate);
    effectiveDate.setDate(effectiveDate.getDate() + 30);

    // Format date for input field (YYYY-MM-DD)
    const formatDate = (date: Date) => date.toISOString().split('T')[0];

    setFormData(prev => ({
      ...prev,
      // Only update if the field is empty (don't override user input)
      effective_date: prev.effective_date || formatDate(effectiveDate),
    }));
  };

  // Reload defaults when parent regulation changes
  const reloadDefaultsForParent = async (parentId: string) => {
    try {
      const defaults = await apiService.get(`/preloading/regulations?parent_id=${parentId}`) as any;

      if (defaults) {

        // Format dates for input fields (YYYY-MM-DD)
        const formatDate = (dateString: string) => {
          if (!dateString) return '';
          return new Date(dateString).toISOString().split('T')[0];
        };

        // Only update certain fields, preserve user input for others
        setFormData(prev => ({
          ...prev,
          hierarchy_level: defaults.hierarchy_level || prev.hierarchy_level,
          // Don't override dates if user has already set them
          enactment_date: prev.enactment_date || formatDate(defaults.enactment_date),
          publication_date: prev.publication_date || formatDate(defaults.publication_date),
          effective_date: prev.effective_date || formatDate(defaults.effective_date),
        }));
      }
    } catch (err) {
      console.error('Error reloading defaults for parent:', err);
    }
  };

  const handleAddTag = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      if (!formData.tags.includes(tagInput.trim())) {
        setFormData(prev => ({
          ...prev,
          tags: [...prev.tags, tagInput.trim()]
        }));
      }
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const generateCFRCitation = () => {
    const { chapter_number, part_number, section_number } = formData;
    if (chapter_number && part_number) {
      let citation = `${chapter_number} CFR ${part_number}`;
      if (section_number) {
        citation += `.${section_number}`;
      }
      setFormData(prev => ({ ...prev, cfr_citation: citation }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const regulationData = {
        title: formData.title,
        short_title: formData.short_title || undefined,
        type: formData.type,
        status: formData.status,
        description: formData.description || undefined,
        content: formData.content || undefined,
        notes: formData.notes || undefined,

        // Legal identifiers
        public_law_number: formData.public_law_number || undefined,
        regulatory_identifier: formData.regulatory_identifier || undefined,
        cfr_title: formData.cfr_title || undefined,
        usc_title: formData.usc_title || undefined,
        docket_number: formData.docket_number || undefined,

        // Hierarchical structure
        hierarchy_level: formData.hierarchy_level || undefined,
        chapter_number: formData.chapter_number || undefined,
        subchapter: formData.subchapter || undefined,
        part_number: formData.part_number || undefined,
        section_number: formData.section_number || undefined,
        subsection: formData.subsection || undefined,
        parent_id: formData.parent_id ? parseInt(formData.parent_id) : undefined,

        // Dates
        enactment_date: formData.enactment_date || undefined,
        effective_date: formData.effective_date || undefined,
        termination_date: formData.termination_date || undefined,
        publication_date: formData.publication_date || undefined,

        // Relationships
        agency_id: formData.agency_id ? parseInt(formData.agency_id) : undefined,

        // Metadata
        is_significant: formData.is_significant,
      };

      const response = await apiService.createRegulation(regulationData);
      setSuccess('Regulation created successfully!');

      // Redirect to regulation detail page after a short delay
      setTimeout(() => {
        router.push(`/regulations/${response.data.id}`);
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create regulation. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated || (user?.role !== 'admin' && user?.role !== 'editor')) {
    return null;
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/regulations"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Regulations
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Create New Regulation</h1>
          <p className="text-gray-600">
            Add a new regulation to the Code of Federal Regulations
          </p>
        </div>

        {/* Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6 flex items-center">
            <XMarkIcon className="h-5 w-5 mr-2" />
            {error}
          </div>
        )}
        {success && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md mb-6 flex items-center">
            <CheckCircleIcon className="h-5 w-5 mr-2" />
            {success}
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Regulation Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  required
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter regulation title"
                />
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                  Type <span className="text-red-500">*</span>
                </label>
                <select
                  id="type"
                  name="type"
                  required
                  value={formData.type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="regulation">Regulation</option>
                  <option value="rule">Rule</option>
                  <option value="law">Law</option>
                  <option value="directive">Directive</option>
                  <option value="order">Order</option>
                </select>
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="draft">Draft</option>
                  <option value="under_review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="published">Published</option>
                  <option value="effective">Effective</option>
                  <option value="terminated">Terminated</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div>
                <label htmlFor="agency_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Agency
                </label>
                <select
                  id="agency_id"
                  name="agency_id"
                  value={formData.agency_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select an agency (optional)</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id}>
                      {agency.name} ({agency.short_name})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  id="category_id"
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select a category (optional)</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Legal Identifiers */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Legal Identifiers</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="short_title" className="block text-sm font-medium text-gray-700 mb-2">
                    Short Title
                  </label>
                  <input
                    type="text"
                    id="short_title"
                    name="short_title"
                    value={formData.short_title}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter short title"
                  />
                </div>

                <div>
                  <label htmlFor="public_law_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Public Law Number
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="public_law_number"
                      name="public_law_number"
                      value={formData.public_law_number}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., 117-58"
                    />
                    <button
                      type="button"
                      onClick={regeneratePublicLawNumber}
                      className="px-3 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      title="Generate new Public Law Number"
                    >
                      🔄
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="regulatory_identifier" className="block text-sm font-medium text-gray-700 mb-2">
                    Regulatory Identifier (RIN)
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="regulatory_identifier"
                      name="regulatory_identifier"
                      value={formData.regulatory_identifier}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., 2040-AF33"
                    />
                    <button
                      type="button"
                      onClick={() => regenerateRegulatoryIdentifier(formData.agency_id)}
                      disabled={!formData.agency_id}
                      className="px-3 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Generate new Regulatory Identifier (requires agency selection)"
                    >
                      🔄
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="cfr_title" className="block text-sm font-medium text-gray-700 mb-2">
                    CFR Title
                  </label>
                  <input
                    type="text"
                    id="cfr_title"
                    name="cfr_title"
                    value={formData.cfr_title}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 40"
                  />
                </div>

                <div>
                  <label htmlFor="usc_title" className="block text-sm font-medium text-gray-700 mb-2">
                    USC Title
                  </label>
                  <input
                    type="text"
                    id="usc_title"
                    name="usc_title"
                    value={formData.usc_title}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 42"
                  />
                </div>

                <div>
                  <label htmlFor="docket_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Docket Number
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="docket_number"
                      name="docket_number"
                      value={formData.docket_number}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., EPA-REG-0001"
                    />
                    <button
                      type="button"
                      onClick={() => regenerateRegulationDocketNumber(formData.agency_id)}
                      disabled={!formData.agency_id}
                      className="px-3 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Generate new Docket Number (requires agency selection)"
                    >
                      🔄
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* CFR Structure */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">CFR Structure</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                  <label htmlFor="chapter_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Chapter
                  </label>
                  <input
                    type="text"
                    id="chapter_number"
                    name="chapter_number"
                    value={formData.chapter_number}
                    onChange={handleChange}
                    onBlur={generateCFRCitation}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 40"
                  />
                </div>

                <div>
                  <label htmlFor="subchapter" className="block text-sm font-medium text-gray-700 mb-2">
                    Subchapter
                  </label>
                  <input
                    type="text"
                    id="subchapter"
                    name="subchapter"
                    value={formData.subchapter}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., A"
                  />
                </div>

                <div>
                  <label htmlFor="part_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Part
                  </label>
                  <input
                    type="text"
                    id="part_number"
                    name="part_number"
                    value={formData.part_number}
                    onChange={handleChange}
                    onBlur={generateCFRCitation}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 52"
                  />
                </div>

                <div>
                  <label htmlFor="section_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Section
                  </label>
                  <input
                    type="text"
                    id="section_number"
                    name="section_number"
                    value={formData.section_number}
                    onChange={handleChange}
                    onBlur={generateCFRCitation}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 21"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="subsection" className="block text-sm font-medium text-gray-700 mb-2">
                    Subsection
                  </label>
                  <input
                    type="text"
                    id="subsection"
                    name="subsection"
                    value={formData.subsection}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., (a)(1)"
                  />
                </div>

                <div>
                  <label htmlFor="cfr_citation" className="block text-sm font-medium text-gray-700 mb-2">
                    CFR Citation
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      id="cfr_citation"
                      name="cfr_citation"
                      value={formData.cfr_citation}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., 40 CFR 52.21"
                    />
                    <button
                      type="button"
                      onClick={generateCFRCitation}
                      className="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md text-sm text-gray-600 hover:bg-gray-200"
                    >
                      Generate
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Brief description of the regulation..."
              />
            </div>

            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                Regulation Content *
              </label>
              <textarea
                id="content"
                name="content"
                rows={12}
                required
                value={formData.content}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Full regulation text (supports Markdown)..."
              />
              <p className="mt-1 text-sm text-gray-500">
                You can use Markdown formatting for rich text content.
              </p>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Internal notes about this regulation..."
              />
            </div>

            {/* Relationships */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="parent_regulation_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Parent Regulation
                </label>
                <select
                  id="parent_regulation_id"
                  name="parent_regulation_id"
                  value={formData.parent_regulation_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">No parent regulation</option>
                  {regulations.map((regulation) => (
                    <option key={regulation.id} value={regulation.id}>
                      {regulation.cfr_citation || regulation.title}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="supersedes_regulation_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Supersedes Regulation
                </label>
                <select
                  id="supersedes_regulation_id"
                  name="supersedes_regulation_id"
                  value={formData.supersedes_regulation_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Does not supersede any regulation</option>
                  {regulations.map((regulation) => (
                    <option key={regulation.id} value={regulation.id}>
                      {regulation.cfr_citation || regulation.title}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Dates */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Important Dates</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="enactment_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Enactment Date
                  </label>
                  <input
                    type="date"
                    id="enactment_date"
                    name="enactment_date"
                    value={formData.enactment_date}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="publication_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Publication Date
                  </label>
                  <input
                    type="date"
                    id="publication_date"
                    name="publication_date"
                    value={formData.publication_date}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="effective_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Effective Date
                  </label>
                  <input
                  type="date"
                  id="effective_date"
                  name="effective_date"
                  value={formData.effective_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="termination_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Termination Date
                </label>
                <input
                  type="date"
                  id="termination_date"
                  name="termination_date"
                  value={formData.termination_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label htmlFor="comment_end_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Comment End Date
                </label>
                <input
                  type="date"
                  id="comment_end_date"
                  name="comment_end_date"
                  value={formData.comment_end_date}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              </div>
            </div>

            {/* Tags */}
            <div>
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <div className="space-y-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleAddTag}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Type a tag and press Enter"
                />
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 text-primary-600 hover:text-primary-800"
                        >
                          <XMarkIcon className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Regulatory Attributes */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Regulatory Attributes</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_significant"
                    checked={formData.is_significant}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Significant Regulation</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="public_comment_period"
                    checked={formData.public_comment_period}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Public Comment Period</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="regulatory_impact_analysis"
                    checked={formData.regulatory_impact_analysis}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Regulatory Impact Analysis</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="small_business_impact"
                    checked={formData.small_business_impact}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Small Business Impact</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="environmental_impact"
                    checked={formData.environmental_impact}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Environmental Impact</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="federalism_implications"
                    checked={formData.federalism_implications}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Federalism Implications</span>
                </label>
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link
                href="/regulations"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Creating...' : 'Create Regulation'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default NewRegulationPage;
