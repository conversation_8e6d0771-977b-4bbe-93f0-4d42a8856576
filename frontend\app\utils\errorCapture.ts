/**
 * Enhanced Error Capture Script
 * This script provides comprehensive error monitoring and reporting
 */

import { errorLogger } from './errorLogger';

// Enhanced console logging with error capture
const enhanceConsole = () => {
  const originalMethods = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info,
    debug: console.debug,
  };

  // Override console.error to capture all errors
  console.error = (...args: any[]) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    errorLogger.logError({
      type: 'runtime',
      message: `Console Error: ${message}`,
      severity: 'high',
      context: {
        consoleArgs: args,
        source: 'console.error',
      },
    });

    originalMethods.error.apply(console, args);
  };

  // Override console.warn to capture warnings
  console.warn = (...args: any[]) => {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    errorLogger.logError({
      type: 'runtime',
      message: `Console Warning: ${message}`,
      severity: 'medium',
      context: {
        consoleArgs: args,
        source: 'console.warn',
      },
    });

    originalMethods.warn.apply(console, args);
  };

  return originalMethods;
};

// Network request monitoring
const monitorNetworkRequests = () => {
  // Monitor fetch requests
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    const [resource, config] = args;
    const url = typeof resource === 'string' ? resource : resource instanceof URL ? resource.href : (resource as Request).url;
    const method = config?.method || 'GET';

    try {
      const response = await originalFetch(...args);
      
      // Log failed HTTP responses
      if (!response.ok) {
        errorLogger.logError({
          type: 'axios',
          message: `Fetch request failed: ${response.status} ${response.statusText}`,
          severity: response.status >= 500 ? 'critical' : 'high',
          request: {
            method,
            url,
            data: config?.body,
            headers: config?.headers,
          },
          response: {
            status: response.status,
            statusText: response.statusText,
          },
        });
      }

      return response;
    } catch (error: any) {
      errorLogger.logError({
        type: 'axios',
        message: `Fetch request error: ${error.message}`,
        severity: 'critical',
        stack: error.stack,
        request: {
          method,
          url,
          data: config?.body,
          headers: config?.headers,
        },
      });
      throw error;
    }
  };
};

// React DevTools error monitoring
const monitorReactErrors = () => {
  // Monitor React DevTools errors if available
  if (typeof window !== 'undefined' && (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    const hook = (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__;
    
    if (hook.onCommitFiberRoot) {
      const originalOnCommitFiberRoot = hook.onCommitFiberRoot;
      hook.onCommitFiberRoot = (id: any, root: any, ...args: any[]) => {
        try {
          return originalOnCommitFiberRoot(id, root, ...args);
        } catch (error: any) {
          errorLogger.logError({
            type: 'react',
            message: `React DevTools error: ${error.message}`,
            stack: error.stack,
            severity: 'high',
            context: {
              source: 'React DevTools',
              rootId: id,
            },
          });
          throw error;
        }
      };
    }
  }
};

// Performance monitoring
const monitorPerformance = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // Tasks longer than 50ms
              errorLogger.logError({
                type: 'runtime',
                message: `Long task detected: ${entry.duration.toFixed(2)}ms`,
                severity: entry.duration > 100 ? 'high' : 'medium',
                context: {
                  duration: entry.duration,
                  startTime: entry.startTime,
                  entryType: entry.entryType,
                  source: 'PerformanceObserver',
                },
              });
            }
          }
        });

        observer.observe({ entryTypes: ['longtask'] });
      } catch (e) {
        console.warn('PerformanceObserver not supported for longtask');
      }
    }

    // Monitor navigation timing
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = navigation.loadEventEnd - navigation.fetchStart;
          if (loadTime > 3000) { // Page load longer than 3 seconds
            errorLogger.logError({
              type: 'runtime',
              message: `Slow page load: ${loadTime.toFixed(2)}ms`,
              severity: loadTime > 5000 ? 'high' : 'medium',
              context: {
                loadTime,
                domContentLoaded: navigation.domContentLoadedEventEnd - navigation.fetchStart,
                source: 'NavigationTiming',
              },
            });
          }
        }
      }, 0);
    });
  }
};

// Memory monitoring
const monitorMemory = () => {
  if (typeof window !== 'undefined' && 'performance' in window && (performance as any).memory) {
    const checkMemory = () => {
      const memory = (performance as any).memory;
      const usedMB = memory.usedJSHeapSize / 1024 / 1024;
      const limitMB = memory.jsHeapSizeLimit / 1024 / 1024;
      const usagePercent = (usedMB / limitMB) * 100;

      if (usagePercent > 80) {
        errorLogger.logError({
          type: 'runtime',
          message: `High memory usage: ${usedMB.toFixed(2)}MB (${usagePercent.toFixed(1)}%)`,
          severity: usagePercent > 90 ? 'critical' : 'high',
          context: {
            usedMB,
            limitMB,
            usagePercent,
            source: 'MemoryMonitor',
          },
        });
      }
    };

    // Check memory every 30 seconds
    setInterval(checkMemory, 30000);
  }
};

// Initialize all monitoring
export const initializeErrorCapture = () => {
  if (typeof window === 'undefined') return;

  console.log('🚨 Initializing comprehensive error capture system...');

  enhanceConsole();
  monitorNetworkRequests();
  monitorReactErrors();
  monitorPerformance();
  monitorMemory();

  // Add global error summary to console
  const logErrorSummary = () => {
    const summary = errorLogger.getErrorSummary();
    if (summary.total > 0) {
      console.group('🚨 ERROR SUMMARY');
      console.log(`Total Errors: ${summary.total}`);
      console.log('By Type:', summary.byType);
      console.log('By Severity:', summary.bySeverity);
      console.log('Recent Errors:', summary.recent.slice(0, 3));
      console.groupEnd();
    }
  };

  // Log summary every 60 seconds if there are errors
  setInterval(() => {
    const summary = errorLogger.getErrorSummary();
    if (summary.total > 0) {
      logErrorSummary();
    }
  }, 60000);

  // Add keyboard shortcut to show error summary (Ctrl+Shift+E)
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'E') {
      e.preventDefault();
      logErrorSummary();
    }
  });

  console.log('✅ Error capture system initialized');
  console.log('💡 Press Ctrl+Shift+E to view error summary');
};

// Auto-initialize in browser
if (typeof window !== 'undefined') {
  // Initialize after DOM is ready
  if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeErrorCapture);
    } else {
      initializeErrorCapture();
    }
  }
}
