'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../../services/enterpriseApi';
import { BudgetPlan, ChartOfAccounts } from '../../../../../types/enterprise';

const EditBudgetPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const budgetId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accounts, setAccounts] = useState<ChartOfAccounts[]>([]);
  const [formData, setFormData] = useState<Partial<BudgetPlan>>({
    budget_code: '',
    budget_name: '',
    description: '',
    fiscal_year: new Date().getFullYear(),
    start_date: '',
    end_date: '',
    account_id: 0,
    planned_amount: 0,
    revised_amount: 0,
    actual_amount: 0,
    committed_amount: 0,
    variance_amount: 0,
    variance_percent: 0,
    status: 'draft',
    budget_type: 'operational',
    department_id: undefined,
    cost_center_id: undefined,
    alert_threshold: 90,
    is_alert_enabled: true,
    metadata: ''
  });

  useEffect(() => {
    if (budgetId) {
      fetchBudget();
      fetchAccounts();
    }
  }, [budgetId]);

  const fetchBudget = async () => {
    try {
      setFetchLoading(true);
      const response = await financialApi.getBudget(budgetId);
      const budget = response.data;
      setFormData({
        ...budget,
        start_date: budget.start_date.split('T')[0],
        end_date: budget.end_date.split('T')[0],
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch budget');
    } finally {
      setFetchLoading(false);
    }
  };

  const fetchAccounts = async () => {
    try {
      const response = await financialApi.getAccounts();
      setAccounts(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch accounts');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await financialApi.updateBudget(budgetId, formData);
      router.push(`/enterprise/financial/budgets/${budgetId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update budget');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  // Calculate variance when planned or actual changes
  useEffect(() => {
    const variance = (formData.planned_amount || 0) - (formData.actual_amount || 0);
    const variancePercent = formData.planned_amount ? (variance / formData.planned_amount) * 100 : 0;
    setFormData(prev => ({ ...prev, variance_amount: variance, variance_percent: variancePercent }));
  }, [formData.planned_amount, formData.actual_amount]);

  if (fetchLoading) return <div className="p-6">Loading budget...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Budget</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/financial/budgets/${budgetId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Budget Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Code *
            </label>
            <input
              type="text"
              name="budget_code"
              value={formData.budget_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., BUD-2025-001"
            />
          </div>

          {/* Budget Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Name *
            </label>
            <input
              type="text"
              name="budget_name"
              value={formData.budget_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Annual Operating Budget"
            />
          </div>

          {/* Fiscal Year */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fiscal Year *
            </label>
            <input
              type="number"
              name="fiscal_year"
              value={formData.fiscal_year}
              onChange={handleChange}
              required
              min="2000"
              max="2100"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Budget Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Type *
            </label>
            <select
              name="budget_type"
              value={formData.budget_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="operational">Operational</option>
              <option value="capital">Capital</option>
              <option value="project">Project</option>
              <option value="departmental">Departmental</option>
            </select>
          </div>

          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Start Date *
            </label>
            <input
              type="date"
              name="start_date"
              value={formData.start_date}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* End Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              End Date *
            </label>
            <input
              type="date"
              name="end_date"
              value={formData.end_date}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Account */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account *
            </label>
            <select
              name="account_id"
              value={formData.account_id}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Account</option>
              {accounts.map((account) => (
                <option key={account.id} value={account.id}>
                  {account.account_code} - {account.account_name}
                </option>
              ))}
            </select>
          </div>

          {/* Planned Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Planned Amount *
            </label>
            <input
              type="number"
              name="planned_amount"
              value={formData.planned_amount}
              onChange={handleChange}
              required
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Revised Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Revised Amount
            </label>
            <input
              type="number"
              name="revised_amount"
              value={formData.revised_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Actual Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Actual Amount
            </label>
            <input
              type="number"
              name="actual_amount"
              value={formData.actual_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Committed Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Committed Amount
            </label>
            <input
              type="number"
              name="committed_amount"
              value={formData.committed_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Variance Amount (calculated) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Variance Amount (Calculated)
            </label>
            <input
              type="number"
              name="variance_amount"
              value={formData.variance_amount}
              readOnly
              className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600"
            />
          </div>

          {/* Variance Percent (calculated) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Variance Percent (Calculated)
            </label>
            <input
              type="number"
              name="variance_percent"
              value={formData.variance_percent}
              readOnly
              className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600"
            />
          </div>

          {/* Department ID */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department ID
            </label>
            <input
              type="number"
              name="department_id"
              value={formData.department_id || ''}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Department ID"
            />
          </div>

          {/* Cost Center ID */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost Center ID
            </label>
            <input
              type="number"
              name="cost_center_id"
              value={formData.cost_center_id || ''}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Cost Center ID"
            />
          </div>

          {/* Alert Threshold */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Alert Threshold (%)
            </label>
            <input
              type="number"
              name="alert_threshold"
              value={formData.alert_threshold}
              onChange={handleChange}
              min="0"
              max="100"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="draft">Draft</option>
              <option value="submitted">Submitted</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="active">Active</option>
            </select>
          </div>

          {/* Alert Enabled */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                name="is_alert_enabled"
                checked={formData.is_alert_enabled}
                onChange={handleChange}
                className="mr-2"
              />
              Enable Alerts
            </label>
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Budget description..."
          />
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/financial/budgets/${budgetId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Budget'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditBudgetPage;
