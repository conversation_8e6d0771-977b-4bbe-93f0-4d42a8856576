-- Regulation Relationships Migration
-- Creates tables for tracking relationships between regulations and documents/agencies/categories

-- Create regulation_document_relationships table
CREATE TABLE regulation_document_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Source regulation
    regulation_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    regulation_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Target document
    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Relationship details
    relationship_type TEXT NOT NULL CHECK (relationship_type IN (
        'implements', 'based_on', 'amends', 'repeals', 'references', 'supersedes'
    )),
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    description TEXT,
    citation_text TEXT,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    
    -- Ensure unique relationships of same type between same entities
    UNIQUE(regulation_id, document_id, relationship_type)
);

-- Create regulation_agency_relationships table
CREATE TABLE regulation_agency_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Source regulation
    regulation_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    regulation_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Target agency
    agency_id BIGINT NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
    
    -- Relationship details
    relationship_type TEXT NOT NULL CHECK (relationship_type IN (
        'established_by', 'authorized_by', 'modified_by', 'abolished_by'
    )),
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    description TEXT,
    authority_scope TEXT,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    
    -- Ensure unique relationships of same type between same entities
    UNIQUE(regulation_id, agency_id, relationship_type)
);

-- Create regulation_category_relationships table
CREATE TABLE regulation_category_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Source regulation
    regulation_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    regulation_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Target category
    category_id BIGINT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    
    -- Relationship details
    relationship_type TEXT NOT NULL CHECK (relationship_type IN (
        'created_by', 'modified_by', 'abolished_by', 'governed_by'
    )),
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    description TEXT,
    scope_definition TEXT,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    
    -- Ensure unique relationships of same type between same entities
    UNIQUE(regulation_id, category_id, relationship_type)
);

-- Add indexes for better performance
CREATE INDEX idx_regulation_document_relationships_regulation_id ON regulation_document_relationships(regulation_id);
CREATE INDEX idx_regulation_document_relationships_document_id ON regulation_document_relationships(document_id);
CREATE INDEX idx_regulation_document_relationships_type ON regulation_document_relationships(relationship_type);
CREATE INDEX idx_regulation_document_relationships_effective_date ON regulation_document_relationships(effective_date);
CREATE INDEX idx_regulation_document_relationships_is_active ON regulation_document_relationships(is_active);

CREATE INDEX idx_regulation_agency_relationships_regulation_id ON regulation_agency_relationships(regulation_id);
CREATE INDEX idx_regulation_agency_relationships_agency_id ON regulation_agency_relationships(agency_id);
CREATE INDEX idx_regulation_agency_relationships_type ON regulation_agency_relationships(relationship_type);
CREATE INDEX idx_regulation_agency_relationships_effective_date ON regulation_agency_relationships(effective_date);
CREATE INDEX idx_regulation_agency_relationships_is_active ON regulation_agency_relationships(is_active);

CREATE INDEX idx_regulation_category_relationships_regulation_id ON regulation_category_relationships(regulation_id);
CREATE INDEX idx_regulation_category_relationships_category_id ON regulation_category_relationships(category_id);
CREATE INDEX idx_regulation_category_relationships_type ON regulation_category_relationships(relationship_type);
CREATE INDEX idx_regulation_category_relationships_effective_date ON regulation_category_relationships(effective_date);
CREATE INDEX idx_regulation_category_relationships_is_active ON regulation_category_relationships(is_active);

-- Add columns to existing tables to support reverse lookups (optional, for performance)
-- These will be computed fields that can be populated by triggers or application logic

-- Add regulation_count to documents table for quick stats
ALTER TABLE documents ADD COLUMN regulation_count INTEGER DEFAULT 0;

-- Add regulation_count to agencies table for quick stats  
ALTER TABLE agencies ADD COLUMN regulation_count INTEGER DEFAULT 0;

-- Add regulation_count to categories table for quick stats
ALTER TABLE categories ADD COLUMN regulation_count INTEGER DEFAULT 0;

-- Create triggers to maintain counts (optional - can be done in application logic instead)
-- These triggers will automatically update the count fields when relationships are added/removed

-- Function to update document regulation count
CREATE OR REPLACE FUNCTION update_document_regulation_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE documents 
        SET regulation_count = regulation_count + 1 
        WHERE id = NEW.document_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE documents 
        SET regulation_count = regulation_count - 1 
        WHERE id = OLD.document_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update agency regulation count
CREATE OR REPLACE FUNCTION update_agency_regulation_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE agencies 
        SET regulation_count = regulation_count + 1 
        WHERE id = NEW.agency_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE agencies 
        SET regulation_count = regulation_count - 1 
        WHERE id = OLD.agency_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update category regulation count
CREATE OR REPLACE FUNCTION update_category_regulation_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE categories 
        SET regulation_count = regulation_count + 1 
        WHERE id = NEW.category_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE categories 
        SET regulation_count = regulation_count - 1 
        WHERE id = OLD.category_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_update_document_regulation_count
    AFTER INSERT OR DELETE ON regulation_document_relationships
    FOR EACH ROW EXECUTE FUNCTION update_document_regulation_count();

CREATE TRIGGER trigger_update_agency_regulation_count
    AFTER INSERT OR DELETE ON regulation_agency_relationships
    FOR EACH ROW EXECUTE FUNCTION update_agency_regulation_count();

CREATE TRIGGER trigger_update_category_regulation_count
    AFTER INSERT OR DELETE ON regulation_category_relationships
    FOR EACH ROW EXECUTE FUNCTION update_category_regulation_count();

-- Initialize counts for existing records
UPDATE documents SET regulation_count = (
    SELECT COUNT(*) FROM regulation_document_relationships 
    WHERE document_id = documents.id AND deleted_at IS NULL
);

UPDATE agencies SET regulation_count = (
    SELECT COUNT(*) FROM regulation_agency_relationships 
    WHERE agency_id = agencies.id AND deleted_at IS NULL
);

UPDATE categories SET regulation_count = (
    SELECT COUNT(*) FROM regulation_category_relationships 
    WHERE category_id = categories.id AND deleted_at IS NULL
);
