'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { contentApi } from '../../services/enterpriseApi';
import { ContentRepository } from '../../types/enterprise';

const ContentManagementPage: React.FC = () => {
  const router = useRouter();
  const [repositories, setRepositories] = useState<ContentRepository[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('repositories');

  const tabs = [
    { id: 'repositories', name: 'Content Repositories', icon: '📁' },
    { id: 'workflows', name: 'Workflows', icon: '⚡' },
    { id: 'versions', name: 'Version Control', icon: '📝' },
    { id: 'collaboration', name: 'Collaboration', icon: '👥' }
  ];

  useEffect(() => {
    fetchRepositories();
  }, []);

  const fetchRepositories = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching repositories with contentApi:', contentApi);

      // Check if contentApi is properly imported
      if (!contentApi || !contentApi.getRepositories) {
        throw new Error('contentApi is not properly imported or getRepositories method is missing');
      }

      // Fetch content repositories from enterprise content API
      const response = await contentApi.getRepositories();
      console.log('Repository response:', response);

      if (response && response.data) {
        setRepositories(response.data);
      } else {
        throw new Error(response?.message || 'Failed to fetch content repositories - no data in response');
      }
    } catch (err: any) {
      console.error('Error fetching repositories:', err);
      setError(err.response?.data?.message || err.message || 'Failed to load content repositories');

      // Set empty array on error
      setRepositories([]);
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getClassificationColor = (classification: string): string => {
    const colors: { [key: string]: string } = {
      'public': 'bg-green-100 text-green-800',
      'internal': 'bg-blue-100 text-blue-800',
      'confidential': 'bg-yellow-100 text-yellow-800',
      'restricted': 'bg-orange-100 text-orange-800',
      'secret': 'bg-red-100 text-red-800',
      'top_secret': 'bg-purple-100 text-purple-800'
    };
    return colors[classification] || 'bg-gray-100 text-gray-800';
  };

  const renderRepositories = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Content Repositories</h2>
        <button
          onClick={() => router.push('/enterprise/content/repositories/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Repository
        </button>
      </div>

      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading repositories...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {repositories.map((repo) => (
            <div key={repo.id} className="bg-white rounded-lg shadow border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{repo.name}</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getClassificationColor(repo.classification)}`}>
                  {repo.classification.toUpperCase()}
                </span>
              </div>
              
              <p className="text-gray-600 text-sm mb-4">{repo.description}</p>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Type:</span>
                  <span className="text-gray-900">{repo.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Storage:</span>
                  <span className="text-gray-900">{repo.storage_type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Usage:</span>
                  <span className="text-gray-900">
                    {formatFileSize(repo.current_size)} / {formatFileSize(repo.max_size)}
                  </span>
                </div>
              </div>

              <div className="mt-4 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `${(repo.current_size / repo.max_size) * 100}%` }}
                ></div>
              </div>

              <div className="mt-4 flex space-x-2">
                <button
                  onClick={() => router.push(`/enterprise/content/repositories/${repo.id}`)}
                  className="flex-1 bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm hover:bg-blue-100 transition-colors"
                >
                  View Details
                </button>
                <button
                  onClick={() => router.push(`/enterprise/content/repositories/${repo.id}/edit`)}
                  className="flex-1 bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-100 transition-colors"
                >
                  Edit
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );

  const renderWorkflows = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Content Workflows</h2>
        <button
          onClick={() => router.push('/enterprise/content/workflows/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Workflow
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Workflow management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderVersions = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Version Control</h2>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Version control interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderCollaboration = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Collaboration Sessions</h2>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Collaboration management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'repositories':
        return renderRepositories();
      case 'workflows':
        return renderWorkflows();
      case 'versions':
        return renderVersions();
      case 'collaboration':
        return renderCollaboration();
      default:
        return renderRepositories();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Enterprise Content Management</h1>
          <p className="mt-2 text-gray-600">
            Manage content repositories, workflows, version control, and collaboration
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {renderTabContent()}

        {/* Error Display */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContentManagementPage;
