'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  PencilSquareIcon,
  CheckCircleIcon,
  XMarkIcon,
  ClockIcon,
  ShieldCheckIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';

interface DigitalSignature {
  id: number;
  document_id: number;
  document_title: string;
  signer_id: number;
  signer_name: string;
  status: 'pending' | 'signed' | 'rejected' | 'expired';
  signature_type: string;
  certificate_id?: number;
  signature_data?: string;
  signed_at?: string;
  expires_at?: string;
  created_at: string;
  updated_at: string;
}

const DigitalSignaturesPage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [signatures, setSignatures] = useState<DigitalSignature[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState<'all' | 'pending' | 'signed' | 'rejected'>('all');

  useEffect(() => {
    if (!isAuthenticated || (user?.role !== 'admin' && user?.role !== 'publisher')) {
      window.location.href = '/dashboard';
      return;
    }

    fetchSignatures();
  }, [isAuthenticated, user, filter]);

  const fetchSignatures = async () => {
    try {
      setLoading(true);
      const response = await apiService.get<DigitalSignature[]>('/signatures/my');
      setSignatures(response);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch signatures');
    } finally {
      setLoading(false);
    }
  };

  const handleSignDocument = async (signatureId: number) => {
    try {
      await apiService.post(`/signatures/${signatureId}/sign`, {});
      fetchSignatures();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to sign document');
    }
  };

  const handleRejectSignature = async (signatureId: number) => {
    try {
      await apiService.post(`/signatures/${signatureId}/reject`, {});
      fetchSignatures();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to reject signature');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'signed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'rejected':
        return <XMarkIcon className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'signed':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredSignatures = signatures.filter(sig => 
    filter === 'all' || sig.status === filter
  );

  if (!isAuthenticated || (user?.role !== 'admin' && user?.role !== 'publisher')) {
    return null;
  }

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <ShieldCheckIcon className="h-8 w-8 text-primary-600 mr-3" />
                Digital Signatures
              </h1>
              <p className="mt-2 text-gray-600">
                Manage digital signatures for documents requiring authentication
              </p>
            </div>
            <Link
              href="/admin/signatures/request"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Request Signature
            </Link>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XMarkIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6">
          <div className="flex space-x-4">
            {['all', 'pending', 'signed', 'rejected'].map((status) => (
              <button
                key={status}
                onClick={() => setFilter(status as any)}
                className={`px-4 py-2 rounded-md text-sm font-medium ${
                  filter === status
                    ? 'bg-primary-100 text-primary-700 border border-primary-200'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </button>
            ))}
          </div>
        </div>

        {/* Signatures List */}
        <div className="bg-white shadow-md rounded-lg overflow-hidden">
          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading signatures...</p>
            </div>
          ) : filteredSignatures.length === 0 ? (
            <div className="p-8 text-center">
              <ShieldCheckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No signatures found</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Document
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Signer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredSignatures.map((signature) => (
                    <tr key={signature.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {signature.document_title}
                            </div>
                            <div className="text-sm text-gray-500">
                              ID: {signature.document_id}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{signature.signer_name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(signature.status)}
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(signature.status)}`}>
                            {signature.status}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {signature.signature_type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {signature.signed_at 
                          ? new Date(signature.signed_at).toLocaleDateString()
                          : new Date(signature.created_at).toLocaleDateString()
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        {signature.status === 'pending' && (
                          <div className="flex space-x-2">
                            <button
                              onClick={() => handleSignDocument(signature.id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              Sign
                            </button>
                            <button
                              onClick={() => handleRejectSignature(signature.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Reject
                            </button>
                          </div>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default DigitalSignaturesPage;
