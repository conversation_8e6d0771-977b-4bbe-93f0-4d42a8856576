'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { complianceApi } from '../../../../services/enterpriseApi';

interface ComplianceFinding {
  id: number;
  created_at: string;
  updated_at: string;
  finding_code: string;
  title: string;
  description?: string;
  assessment_id?: number;
  requirement_id?: number;
  severity: string;
  category: string;
  status: string;
  identified_date: string;
  due_date?: string;
  resolved_date?: string;
  assigned_to_id?: number;
  evidence?: string;
  impact?: string;
  recommendation?: string;
  remediation_plan?: string;
  cost_to_fix?: number;
  currency_code?: string;
  risk_rating?: string;
  compliance_gap?: string;
  root_cause?: string;
  recurrence_risk?: string;
  validation_method?: string;
  is_systemic?: boolean;
  metadata?: string;
}

const ComplianceFindingViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const findingId = parseInt(params.id as string);
  
  const [finding, setFinding] = useState<ComplianceFinding | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (findingId) {
      fetchFinding();
    }
  }, [findingId]);

  const fetchFinding = async () => {
    try {
      setLoading(true);
      const response = await complianceApi.getFinding(findingId);
      setFinding(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch compliance finding');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this compliance finding?')) return;
    
    try {
      await complianceApi.deleteFinding(findingId);
      router.push('/enterprise/compliance/findings');
    } catch (err: any) {
      setError(err.message || 'Failed to delete compliance finding');
    }
  };

  const handleResolve = async () => {
    try {
      await complianceApi.updateFinding(findingId, { 
        status: 'resolved',
        resolved_date: new Date().toISOString().split('T')[0]
      });
      await fetchFinding(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Failed to resolve finding');
    }
  };

  if (loading) return <div className="p-6">Loading compliance finding...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!finding) return <div className="p-6">Compliance finding not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Compliance Finding Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/compliance/findings')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Findings
          </button>
          <button
            onClick={() => router.push(`/enterprise/compliance/findings/${findingId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Finding
          </button>
          {finding.status !== 'resolved' && (
            <button
              onClick={handleResolve}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Mark Resolved
            </button>
          )}
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Finding
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{finding.title}</h2>
              <p className="text-sm text-gray-600">Code: {finding.finding_code}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                finding.status === 'resolved' 
                  ? 'bg-green-100 text-green-800'
                  : finding.status === 'in_progress'
                  ? 'bg-blue-100 text-blue-800'
                  : finding.status === 'open'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {finding.status.replace('_', ' ')}
              </span>
            </div>
          </div>
        </div>

        {/* Finding Overview */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Finding Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className={`p-4 rounded-lg ${
              finding.severity === 'critical' ? 'bg-red-50' :
              finding.severity === 'high' ? 'bg-orange-50' :
              finding.severity === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              <div className={`text-2xl font-bold ${
                finding.severity === 'critical' ? 'text-red-600' :
                finding.severity === 'high' ? 'text-orange-600' :
                finding.severity === 'medium' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {finding.severity.toUpperCase()}
              </div>
              <div className="text-sm text-gray-600">Severity</div>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {finding.category.toUpperCase()}
              </div>
              <div className="text-sm text-blue-600">Category</div>
            </div>

            <div className={`p-4 rounded-lg ${
              finding.risk_rating === 'critical' ? 'bg-red-50' :
              finding.risk_rating === 'high' ? 'bg-orange-50' :
              finding.risk_rating === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              <div className={`text-2xl font-bold ${
                finding.risk_rating === 'critical' ? 'text-red-600' :
                finding.risk_rating === 'high' ? 'text-orange-600' :
                finding.risk_rating === 'medium' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {finding.risk_rating?.toUpperCase() || 'N/A'}
              </div>
              <div className="text-sm text-gray-600">Risk Rating</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {finding.cost_to_fix ? `${finding.currency_code} ${finding.cost_to_fix.toLocaleString()}` : 'N/A'}
              </div>
              <div className="text-sm text-purple-600">Cost to Fix</div>
            </div>
          </div>
        </div>

        {/* Finding Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Finding Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Identified Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(finding.identified_date).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Due Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {finding.due_date ? new Date(finding.due_date).toLocaleDateString() : 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Resolved Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {finding.resolved_date ? new Date(finding.resolved_date).toLocaleDateString() : 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Assessment ID</label>
              <p className="mt-1 text-sm text-gray-900">{finding.assessment_id || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Requirement ID</label>
              <p className="mt-1 text-sm text-gray-900">{finding.requirement_id || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Assigned To</label>
              <p className="mt-1 text-sm text-gray-900">{finding.assigned_to_id || 'Unassigned'}</p>
            </div>
          </div>

          {finding.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.description}
              </div>
            </div>
          )}
        </div>

        {/* Evidence and Impact */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Evidence and Impact</h3>
          
          {finding.evidence && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Evidence</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.evidence}
              </div>
            </div>
          )}

          {finding.impact && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Impact</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.impact}
              </div>
            </div>
          )}

          {finding.root_cause && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Root Cause</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.root_cause}
              </div>
            </div>
          )}
        </div>

        {/* Recommendations and Remediation */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Recommendations and Remediation</h3>
          
          {finding.recommendation && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Recommendation</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.recommendation}
              </div>
            </div>
          )}

          {finding.remediation_plan && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Remediation Plan</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.remediation_plan}
              </div>
            </div>
          )}

          {finding.validation_method && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Validation Method</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.validation_method}
              </div>
            </div>
          )}
        </div>

        {/* Additional Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Recurrence Risk</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{finding.recurrence_risk || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Systemic Finding</label>
              <p className="mt-1 text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  finding.is_systemic 
                    ? 'bg-red-100 text-red-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {finding.is_systemic ? 'Yes' : 'No'}
                </span>
              </p>
            </div>
          </div>

          {finding.compliance_gap && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Compliance Gap</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {finding.compliance_gap}
              </div>
            </div>
          )}
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(finding.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(finding.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceFindingViewPage;
