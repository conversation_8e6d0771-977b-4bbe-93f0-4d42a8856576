package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GetPreloadData returns commonly needed data for form preloading
func GetPreloadData(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get active agencies
	var agencies []models.Agency
	db.Where("is_active = ?", true).Order("name ASC").Find(&agencies)

	// Get active categories
	var categories []models.Category
	db.Where("is_active = ?", true).Order("name ASC").Find(&categories)

	// Get active users
	var users []models.User
	db.Where("is_active = ?", true).Order("first_name ASC, last_name ASC").Find(&users)

	// Convert to response format
	agencyOptions := make([]gin.H, len(agencies))
	for i, agency := range agencies {
		agencyOptions[i] = gin.H{
			"id":   agency.ID,
			"name": agency.Name,
			"slug": agency.Slug,
		}
	}

	categoryOptions := make([]gin.H, len(categories))
	for i, category := range categories {
		categoryOptions[i] = gin.H{
			"id":    category.ID,
			"name":  category.Name,
			"slug":  category.Slug,
			"color": category.Color,
			"icon":  category.Icon,
		}
	}

	userOptions := make([]gin.H, len(users))
	for i, user := range users {
		userOptions[i] = gin.H{
			"id":         user.ID,
			"first_name": user.FirstName,
			"last_name":  user.LastName,
			"email":      user.Email,
			"role":       user.Role,
		}
	}

	// Common status options
	documentStatuses := []gin.H{
		{"value": "draft", "label": "Draft"},
		{"value": "review", "label": "Under Review"},
		{"value": "approved", "label": "Approved"},
		{"value": "published", "label": "Published"},
		{"value": "archived", "label": "Archived"},
	}

	documentTypes := []gin.H{
		{"value": "rule", "label": "Rule"},
		{"value": "notice", "label": "Notice"},
		{"value": "proposed_rule", "label": "Proposed Rule"},
		{"value": "final_rule", "label": "Final Rule"},
		{"value": "interim_rule", "label": "Interim Rule"},
	}

	priorities := []gin.H{
		{"value": "low", "label": "Low"},
		{"value": "medium", "label": "Medium"},
		{"value": "high", "label": "High"},
		{"value": "urgent", "label": "Urgent"},
	}

	visibilityLevels := []gin.H{
		{"value": 1, "label": "Public"},
		{"value": 2, "label": "Internal"},
		{"value": 3, "label": "Restricted"},
		{"value": 4, "label": "Confidential"},
	}

	response := gin.H{
		"agencies":           agencyOptions,
		"categories":         categoryOptions,
		"users":              userOptions,
		"document_statuses":  documentStatuses,
		"document_types":     documentTypes,
		"priorities":         priorities,
		"visibility_levels":  visibilityLevels,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Preload data retrieved successfully",
		Data:    response,
	})
}

// GetFormDefaults returns default values for various forms
func GetFormDefaults(c *gin.Context) {
	formType := c.Query("type")

	var defaults gin.H

	switch formType {
	case "document":
		defaults = gin.H{
			"type":             "rule",
			"status":           "draft",
			"language":         "en",
			"visibility_level": 1,
			"is_public":        false,
			"accepts_comments": true,
			"significant_rule": false,
		}
	case "agency":
		defaults = gin.H{
			"is_active": true,
			"country":   "US",
		}
	case "category":
		defaults = gin.H{
			"is_active": true,
			"color":     "#007bff",
			"icon":      "folder",
		}
	case "user":
		defaults = gin.H{
			"is_active": true,
			"role":      "user",
			"language":  "en",
			"timezone":  "UTC",
		}
	case "finance":
		defaults = gin.H{
			"currency":     "USD",
			"status":       "draft",
			"is_approved":  false,
			"budget_type":  "operational",
		}
	case "proceeding":
		defaults = gin.H{
			"status":      "draft",
			"is_active":   true,
			"visibility":  "internal",
			"priority":    "medium",
			"auto_assign": false,
		}
	default:
		defaults = gin.H{}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Form defaults retrieved successfully",
		Data:    defaults,
	})
}

// GetEntityRelationships returns relationship data for a specific entity
func GetEntityRelationships(c *gin.Context) {
	entityType := c.Query("entity_type")
	entityID := c.Query("entity_id")

	if entityType == "" || entityID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing parameters",
			Message: "Both entity_type and entity_id are required",
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get relationships where this entity is the source
	var sourceRelationships []models.Interconnect
	db.Where("source_type = ? AND source_id = ? AND is_active = ?", entityType, entityID, true).
		Find(&sourceRelationships)

	// Get relationships where this entity is the target
	var targetRelationships []models.Interconnect
	db.Where("target_type = ? AND target_id = ? AND is_active = ?", entityType, entityID, true).
		Find(&targetRelationships)

	// Convert to response format
	sourceResponses := make([]gin.H, len(sourceRelationships))
	for i, rel := range sourceRelationships {
		sourceResponses[i] = gin.H{
			"id":           rel.ID,
			"target_type":  rel.TargetType,
			"target_id":    rel.TargetID,
			"relationship": rel.Relationship,
			"description":  rel.Description,
		}
	}

	targetResponses := make([]gin.H, len(targetRelationships))
	for i, rel := range targetRelationships {
		targetResponses[i] = gin.H{
			"id":           rel.ID,
			"source_type":  rel.SourceType,
			"source_id":    rel.SourceID,
			"relationship": rel.Relationship,
			"description":  rel.Description,
		}
	}

	response := gin.H{
		"entity_type":          entityType,
		"entity_id":            entityID,
		"outgoing_relationships": sourceResponses,
		"incoming_relationships": targetResponses,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Entity relationships retrieved successfully",
		Data:    response,
	})
}
