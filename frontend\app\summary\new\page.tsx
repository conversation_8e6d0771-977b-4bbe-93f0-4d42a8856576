'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  CalendarIcon,
  GlobeAltIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import { Agency, Category } from '../../types';
import MarkdownEditor from '../../components/MarkdownEditor/MarkdownEditor';

interface SummaryFormData {
  title: string;
  content: string;
  abstract: string;
  summary_type: 'news' | 'analysis' | 'update' | 'alert' | 'report';
  entity_type: 'document' | 'regulation' | 'agency' | 'category' | 'task' | 'proceeding' | 'general';
  entity_id?: number;
  action_type: 'create' | 'update' | 'delete' | 'publish' | 'review' | 'approve' | 'other';
  publication_date: string;
  is_public: boolean;
  is_featured: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  tags: string;
  external_link: string;
  agency_id?: number;
  category_id?: number;
}

const NewSummaryPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);

  const [formData, setFormData] = useState<SummaryFormData>({
    title: '',
    content: '',
    abstract: '',
    summary_type: 'news',
    entity_type: 'general',
    action_type: 'create',
    publication_date: new Date().toISOString().slice(0, 16),
    is_public: true,
    is_featured: false,
    priority: 'medium',
    tags: '',
    external_link: ''
  });

  useEffect(() => {
    fetchAgencies();
    fetchCategories();
  }, []);

  const fetchAgencies = async () => {
    try {
      const response = await apiService.getAgencies();
      setAgencies(response.data || []);
    } catch (err) {
      console.error('Failed to fetch agencies:', err);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await apiService.getCategories();
      setCategories(response.data || []);
    } catch (err) {
      console.error('Failed to fetch categories:', err);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleContentChange = (content: string) => {
    setFormData(prev => ({ ...prev, content }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim() || !formData.content.trim()) {
      setError('Title and content are required');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      await apiService.createSummary(formData);
      router.push('/summary');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create summary');
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be logged in to create summaries.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/summary"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Summaries
          </Link>
          
          <h1 className="text-3xl font-bold text-gray-900">Create Summary</h1>
          <p className="text-gray-600 mt-1">Create a new summary or news item</p>
        </div>

        {/* Form */}
        <div className="max-w-4xl">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Title */}
            <div>
              <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                Title *
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter summary title"
                required
              />
            </div>

            {/* Abstract */}
            <div>
              <label htmlFor="abstract" className="block text-sm font-medium text-gray-700 mb-2">
                Abstract
              </label>
              <textarea
                id="abstract"
                name="abstract"
                value={formData.abstract}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Brief summary or abstract"
              />
            </div>

            {/* Content */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Content *
              </label>
              <MarkdownEditor
                value={formData.content}
                onChange={handleContentChange}
                placeholder="Enter the full content in Markdown format..."
              />
            </div>

            {/* Type and Category Fields */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="summary_type" className="block text-sm font-medium text-gray-700 mb-2">
                  Summary Type
                </label>
                <select
                  id="summary_type"
                  name="summary_type"
                  value={formData.summary_type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="news">News</option>
                  <option value="analysis">Analysis</option>
                  <option value="update">Update</option>
                  <option value="alert">Alert</option>
                  <option value="report">Report</option>
                </select>
              </div>

              <div>
                <label htmlFor="entity_type" className="block text-sm font-medium text-gray-700 mb-2">
                  Entity Type
                </label>
                <select
                  id="entity_type"
                  name="entity_type"
                  value={formData.entity_type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="general">General</option>
                  <option value="document">Document</option>
                  <option value="regulation">Regulation</option>
                  <option value="agency">Agency</option>
                  <option value="category">Category</option>
                  <option value="task">Task</option>
                  <option value="proceeding">Proceeding</option>
                </select>
              </div>

              <div>
                <label htmlFor="action_type" className="block text-sm font-medium text-gray-700 mb-2">
                  Action Type
                </label>
                <select
                  id="action_type"
                  name="action_type"
                  value={formData.action_type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="create">Create</option>
                  <option value="update">Update</option>
                  <option value="delete">Delete</option>
                  <option value="publish">Publish</option>
                  <option value="review">Review</option>
                  <option value="approve">Approve</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            {/* Entity ID */}
            <div>
              <label htmlFor="entity_id" className="block text-sm font-medium text-gray-700 mb-2">
                Entity ID (Optional)
              </label>
              <input
                type="number"
                id="entity_id"
                name="entity_id"
                value={formData.entity_id || ''}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="ID of related entity"
              />
            </div>

            {/* Publication Date and Priority */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="publication_date" className="block text-sm font-medium text-gray-700 mb-2">
                  <CalendarIcon className="h-4 w-4 inline mr-1" />
                  Publication Date
                </label>
                <input
                  type="datetime-local"
                  id="publication_date"
                  name="publication_date"
                  value={formData.publication_date}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-2">
                  Priority
                </label>
                <select
                  id="priority"
                  name="priority"
                  value={formData.priority}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>

            {/* Tags */}
            <div>
              <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
                <TagIcon className="h-4 w-4 inline mr-1" />
                Tags
              </label>
              <input
                type="text"
                id="tags"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter tags separated by commas"
              />
            </div>

            {/* External Link */}
            <div>
              <label htmlFor="external_link" className="block text-sm font-medium text-gray-700 mb-2">
                External Link
              </label>
              <input
                type="url"
                id="external_link"
                name="external_link"
                value={formData.external_link}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="https://example.com"
              />
            </div>

            {/* Related Agency and Category */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="agency_id" className="block text-sm font-medium text-gray-700 mb-2">
                  <BuildingOfficeIcon className="h-4 w-4 inline mr-1" />
                  Related Agency
                </label>
                <select
                  id="agency_id"
                  name="agency_id"
                  value={formData.agency_id || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select an agency (optional)</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id}>
                      {agency.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Related Category
                </label>
                <select
                  id="category_id"
                  name="category_id"
                  value={formData.category_id || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select a category (optional)</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Toggles */}
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_public"
                  name="is_public"
                  checked={formData.is_public}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="is_public" className="ml-2 block text-sm text-gray-700">
                  <GlobeAltIcon className="h-4 w-4 inline mr-1" />
                  Public summary (visible to all users)
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_featured"
                  name="is_featured"
                  checked={formData.is_featured}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="is_featured" className="ml-2 block text-sm text-gray-700">
                  <StarIcon className="h-4 w-4 inline mr-1" />
                  Featured summary (highlighted on homepage)
                </label>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              <Link
                href="/summary"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Summary'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default NewSummaryPage;
