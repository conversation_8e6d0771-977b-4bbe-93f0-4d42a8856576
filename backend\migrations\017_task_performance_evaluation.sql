-- Migration 017: Task Performance Evaluation System
-- This migration adds comprehensive performance evaluation functionality to tasks
-- matching the finance performance evaluation structure

-- Add performance evaluation columns to tasks table
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS performance_percentage NUMERIC(5,2) DEFAULT 0.00;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS deadline_adherence_score NUMERIC(5,2) DEFAULT 0.00;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS quality_score NUMERIC(5,2) DEFAULT 0.00;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS completion_efficiency NUMERIC(5,2) DEFAULT 0.00;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS priority_handling_score NUMERIC(5,2) DEFAULT 0.00;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS performance_notes TEXT;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS evaluation_date TIMESTAMP WITH TIME ZONE;
ALTER TABLE tasks ADD COLUMN IF NOT EXISTS is_auto_calculated BOOLEAN DEFAULT true;
<PERSON>TE<PERSON> TABLE tasks ADD COLUMN IF NOT EXISTS evaluated_by_id BIGINT REFERENCES users(id) ON DELETE SET NULL;

-- Create task_performance_history table to track performance changes over time
CREATE TABLE IF NOT EXISTS task_performance_history (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,

    -- Task reference
    task_id BIGINT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,

    -- Performance metrics (matching finance performance structure)
    performance_percentage NUMERIC(5,2) NOT NULL DEFAULT 0.00,
    deadline_adherence_score NUMERIC(5,2) NOT NULL DEFAULT 0.00,
    quality_score NUMERIC(5,2) NOT NULL DEFAULT 0.00,
    completion_efficiency NUMERIC(5,2) NOT NULL DEFAULT 0.00,
    priority_handling_score NUMERIC(5,2) NOT NULL DEFAULT 0.00,
    performance_notes TEXT,
    evaluation_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    is_auto_calculated BOOLEAN DEFAULT true,
    evaluated_by_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    change_reason TEXT,

    -- Constraints
    CONSTRAINT check_performance_percentage CHECK (performance_percentage >= 0 AND performance_percentage <= 100),
    CONSTRAINT check_deadline_adherence_score CHECK (deadline_adherence_score >= 0 AND deadline_adherence_score <= 100),
    CONSTRAINT check_quality_score CHECK (quality_score >= 0 AND quality_score <= 100),
    CONSTRAINT check_completion_efficiency CHECK (completion_efficiency >= 0 AND completion_efficiency <= 100),
    CONSTRAINT check_priority_handling_score CHECK (priority_handling_score >= 0 AND priority_handling_score <= 100)
);

-- Add constraints to tasks table for performance scores
ALTER TABLE tasks ADD CONSTRAINT IF NOT EXISTS check_task_performance_percentage 
    CHECK (performance_percentage >= 0 AND performance_percentage <= 100);
ALTER TABLE tasks ADD CONSTRAINT IF NOT EXISTS check_task_deadline_adherence_score 
    CHECK (deadline_adherence_score >= 0 AND deadline_adherence_score <= 100);
ALTER TABLE tasks ADD CONSTRAINT IF NOT EXISTS check_task_quality_score 
    CHECK (quality_score >= 0 AND quality_score <= 100);
ALTER TABLE tasks ADD CONSTRAINT IF NOT EXISTS check_task_completion_efficiency 
    CHECK (completion_efficiency >= 0 AND completion_efficiency <= 100);
ALTER TABLE tasks ADD CONSTRAINT IF NOT EXISTS check_task_priority_handling_score 
    CHECK (priority_handling_score >= 0 AND priority_handling_score <= 100);

-- Create indexes for performance queries
CREATE INDEX IF NOT EXISTS idx_tasks_performance_percentage ON tasks(performance_percentage);
CREATE INDEX IF NOT EXISTS idx_tasks_evaluation_date ON tasks(evaluation_date);
CREATE INDEX IF NOT EXISTS idx_tasks_is_auto_calculated ON tasks(is_auto_calculated);
CREATE INDEX IF NOT EXISTS idx_task_performance_history_task_id ON task_performance_history(task_id);
CREATE INDEX IF NOT EXISTS idx_task_performance_history_evaluation_date ON task_performance_history(evaluation_date);
CREATE INDEX IF NOT EXISTS idx_task_performance_history_deleted_at ON task_performance_history(deleted_at);

-- Create function to automatically create performance history when task performance is updated
CREATE OR REPLACE FUNCTION create_task_performance_history()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create history if performance fields have changed
    IF (OLD.performance_percentage IS DISTINCT FROM NEW.performance_percentage OR
        OLD.deadline_adherence_score IS DISTINCT FROM NEW.deadline_adherence_score OR
        OLD.quality_score IS DISTINCT FROM NEW.quality_score OR
        OLD.completion_efficiency IS DISTINCT FROM NEW.completion_efficiency OR
        OLD.priority_handling_score IS DISTINCT FROM NEW.priority_handling_score) THEN
        
        INSERT INTO task_performance_history (
            task_id,
            performance_percentage,
            deadline_adherence_score,
            quality_score,
            completion_efficiency,
            priority_handling_score,
            performance_notes,
            evaluation_date,
            is_auto_calculated,
            evaluated_by_id,
            change_reason
        ) VALUES (
            NEW.id,
            NEW.performance_percentage,
            NEW.deadline_adherence_score,
            NEW.quality_score,
            NEW.completion_efficiency,
            NEW.priority_handling_score,
            NEW.performance_notes,
            COALESCE(NEW.evaluation_date, NOW()),
            NEW.is_auto_calculated,
            NEW.evaluated_by_id,
            CASE 
                WHEN NEW.is_auto_calculated THEN 'Automatic performance evaluation'
                ELSE 'Manual performance evaluation'
            END
        );
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create performance history
DROP TRIGGER IF EXISTS trigger_task_performance_history ON tasks;
CREATE TRIGGER trigger_task_performance_history
    AFTER UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION create_task_performance_history();

-- Create function to automatically sync task performance with finance performance
CREATE OR REPLACE FUNCTION sync_task_performance_with_finance()
RETURNS TRIGGER AS $$
BEGIN
    -- Update finance performance if task is linked to document or regulation
    IF NEW.document_id IS NOT NULL OR NEW.regulation_id IS NOT NULL THEN
        -- Update or create finance performance record
        INSERT INTO finance_performance (
            document_id,
            regulation_id,
            year,
            performance_percentage,
            performance_notes,
            evaluation_date,
            evaluated_by_id
        ) VALUES (
            NEW.document_id,
            NEW.regulation_id,
            EXTRACT(YEAR FROM COALESCE(NEW.evaluation_date, NOW())),
            NEW.performance_percentage,
            CONCAT('Task Performance Sync: ', NEW.performance_notes),
            COALESCE(NEW.evaluation_date, NOW()),
            NEW.evaluated_by_id
        )
        ON CONFLICT (document_id, regulation_id, year) 
        DO UPDATE SET
            performance_percentage = NEW.performance_percentage,
            performance_notes = CONCAT('Task Performance Sync: ', NEW.performance_notes),
            evaluation_date = COALESCE(NEW.evaluation_date, NOW()),
            evaluated_by_id = NEW.evaluated_by_id,
            updated_at = NOW()
        WHERE (finance_performance.document_id = NEW.document_id AND NEW.document_id IS NOT NULL)
           OR (finance_performance.regulation_id = NEW.regulation_id AND NEW.regulation_id IS NOT NULL);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to sync task performance with finance
DROP TRIGGER IF EXISTS trigger_sync_task_finance_performance ON tasks;
CREATE TRIGGER trigger_sync_task_finance_performance
    AFTER UPDATE ON tasks
    FOR EACH ROW
    WHEN (NEW.performance_percentage IS DISTINCT FROM OLD.performance_percentage)
    EXECUTE FUNCTION sync_task_performance_with_finance();

-- Add comments for documentation
COMMENT ON COLUMN tasks.performance_percentage IS 'Overall task performance score (0-100%)';
COMMENT ON COLUMN tasks.deadline_adherence_score IS 'Score based on meeting deadlines (0-100%)';
COMMENT ON COLUMN tasks.quality_score IS 'Task completion quality assessment (0-100%)';
COMMENT ON COLUMN tasks.completion_efficiency IS 'Time efficiency in task completion (0-100%)';
COMMENT ON COLUMN tasks.priority_handling_score IS 'Score for handling task priority appropriately (0-100%)';
COMMENT ON COLUMN tasks.performance_notes IS 'Notes about task performance evaluation';
COMMENT ON COLUMN tasks.evaluation_date IS 'Date when performance was evaluated';
COMMENT ON COLUMN tasks.is_auto_calculated IS 'Whether performance was calculated automatically';
COMMENT ON COLUMN tasks.evaluated_by_id IS 'User who evaluated the performance (if manual)';

COMMENT ON TABLE task_performance_history IS 'Historical record of task performance evaluations';
COMMENT ON COLUMN task_performance_history.change_reason IS 'Reason for performance evaluation change';
