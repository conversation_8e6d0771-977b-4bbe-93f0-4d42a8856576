package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/services"

	"github.com/gin-gonic/gin"
)

// Common response structures

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}

// SuccessResponse represents a success response
type SuccessResponse struct {
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PaginationResponse represents a paginated response
type PaginationResponse struct {
	Data       interface{} `json:"data"`
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PerPage    int         `json:"per_page"`
	TotalPages int         `json:"total_pages"`
	HasNext    bool        `json:"has_next"`
	HasPrev    bool        `json:"has_prev"`
}

// PaginationParams represents pagination parameters
type PaginationParams struct {
	Page    int `form:"page" json:"page"`
	PerPage int `form:"per_page" json:"per_page"`
}

// SearchParams represents search parameters
type SearchParams struct {
	Query     string `form:"query" json:"query"`
	Type      string `form:"type" json:"type"`
	Status    string `form:"status" json:"status"`
	Agency    string `form:"agency" json:"agency"`
	Category  string `form:"category" json:"category"`
	SortBy    string `form:"sort_by" json:"sort_by"`
	SortOrder string `form:"sort_order" json:"sort_order"`
}

// Common utility functions

// GetPaginationParams extracts pagination parameters from request
func GetPaginationParams(c *gin.Context) PaginationParams {
	page := 1
	perPage := 25

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 100 {
			perPage = pp
		}
	}

	return PaginationParams{
		Page:    page,
		PerPage: perPage,
	}
}

// GetSearchParams extracts search parameters from request
func GetSearchParams(c *gin.Context) SearchParams {
	return SearchParams{
		Query:     c.Query("query"),
		Type:      c.Query("type"),
		Status:    c.Query("status"),
		Agency:    c.Query("agency"),
		Category:  c.Query("category"),
		SortBy:    c.DefaultQuery("sort_by", "created_at"),
		SortOrder: c.DefaultQuery("sort_order", "desc"),
	}
}

// ValidateID validates and parses an ID parameter
func ValidateID(c *gin.Context, param string) (uint, bool) {
	idStr := c.Param(param)
	if idStr == "" {
		HandleBadRequest(c, "ID parameter is required")
		return 0, false
	}

	var id uint
	if _, err := fmt.Sscanf(idStr, "%d", &id); err != nil {
		HandleBadRequest(c, "Invalid ID format")
		return 0, false
	}

	return id, true
}

// BindJSON binds JSON request body and handles errors
func BindJSON(c *gin.Context, obj interface{}) bool {
	if err := c.ShouldBindJSON(obj); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request body",
			Message: err.Error(),
		})
		return false
	}
	return true
}

// CreatePaginationResponse creates a paginated response
func CreatePaginationResponse(data interface{}, total int64, page, perPage int) PaginationResponse {
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))

	return PaginationResponse{
		Data:       data,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// Error handling functions

// HandleNotFound handles not found errors
func HandleNotFound(c *gin.Context, resource string) {
	c.JSON(http.StatusNotFound, ErrorResponse{
		Error:   "Not Found",
		Message: fmt.Sprintf("%s not found", resource),
	})
}

// HandleUnauthorized handles unauthorized errors
func HandleUnauthorized(c *gin.Context, message string) {
	c.JSON(http.StatusUnauthorized, ErrorResponse{
		Error:   "Unauthorized",
		Message: message,
	})
}

// HandleBadRequest handles bad request errors
func HandleBadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, ErrorResponse{
		Error:   "Bad Request",
		Message: message,
	})
}

// HandleForbidden handles forbidden errors
func HandleForbidden(c *gin.Context, message string) {
	c.JSON(http.StatusForbidden, ErrorResponse{
		Error:   "Forbidden",
		Message: message,
	})
}

// HandleInternalError handles internal server errors with comprehensive logging
func HandleInternalError(c *gin.Context, message string) {
	// Log error with structured logging
	logStructuredError(c, services.ErrorLevelError, message, nil, "internal_error")

	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Error:   "Internal Server Error",
		Message: message,
	})
}

// HandleInternalErrorWithError handles internal server errors with error details
func HandleInternalErrorWithError(c *gin.Context, message string, err error) {
	// Log error with structured logging
	logStructuredError(c, services.ErrorLevelError, message, err, "internal_error")

	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Error:   "Internal Server Error",
		Message: message,
	})
}

// HandleCriticalError handles critical errors that require immediate attention
func HandleCriticalError(c *gin.Context, message string, err error) {
	// Log critical error with immediate alerting
	logStructuredError(c, services.ErrorLevelCritical, message, err, "critical_error")

	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Error:   "Critical System Error",
		Message: "A critical error has occurred. Please contact system administrators.",
	})
}

// logStructuredError logs errors using the structured error service
func logStructuredError(c *gin.Context, level services.ErrorLevel, message string, err error, operation string) {
	db := database.GetDB()
	if db == nil {
		// Fallback to simple logging if database is not available
		fmt.Printf("[%s] %s: %s\n", level, operation, message)
		if err != nil {
			fmt.Printf("  Error: %s\n", err.Error())
		}
		return
	}

	errorService := services.NewErrorService(db)

	// Extract user ID if available
	var userID *uint
	if userIDInterface, exists := c.Get("user_id"); exists {
		if uid, ok := userIDInterface.(uint); ok {
			userID = &uid
		}
	}

	// Create error context
	errorCtx := services.ErrorContext{
		UserID:     userID,
		Component:  "http_handler",
		Operation:  operation,
		HTTPMethod: c.Request.Method,
		HTTPPath:   c.Request.URL.Path,
		HTTPStatus: c.Writer.Status(),
		ClientIP:   c.ClientIP(),
		UserAgent:  c.GetHeader("User-Agent"),
		Metadata: map[string]interface{}{
			"request_id": c.GetHeader("X-Request-ID"),
			"endpoint":   fmt.Sprintf("%s %s", c.Request.Method, c.Request.URL.Path),
		},
	}

	// Log the error
	if level == services.ErrorLevelCritical {
		errorService.LogCriticalError(c.Request.Context(), message, err, errorCtx)
	} else {
		errorService.LogError(c.Request.Context(), level, message, err, errorCtx)
	}
}

// HealthCheck returns the health status of the API
func HealthCheck(c *gin.Context) {
	// Check database connection
	db := database.GetDB()
	dbStatus := "healthy"
	if db == nil {
		dbStatus = "unhealthy"
	} else {
		sqlDB, err := db.DB()
		if err != nil || sqlDB.Ping() != nil {
			dbStatus = "unhealthy"
		}
	}

	status := gin.H{
		"status":   "healthy",
		"database": dbStatus,
		"version":  "1.0.0",
		"time":     time.Now().Format(time.RFC3339),
	}

	// If database is unhealthy, mark overall status as unhealthy
	if dbStatus == "unhealthy" {
		status["status"] = "unhealthy"
		c.JSON(http.StatusServiceUnavailable, status)
		return
	}

	c.JSON(http.StatusOK, status)
}
