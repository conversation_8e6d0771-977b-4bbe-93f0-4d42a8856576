package handlers

import (
	"crypto/rand"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// SignatureRequestRequest represents a request to create a signature request
type SignatureRequestRequest struct {
	DocumentID     uint   `json:"document_id" binding:"required"`
	RequestedByID  uint   `json:"requested_by_id" binding:"required"`
	SignerID       uint   `json:"signer_id" binding:"required"`
	SignatureType  string `json:"signature_type" binding:"required"`
	RequiredByDate string `json:"required_by_date"`
	Instructions   string `json:"instructions"`
	IsUrgent       bool   `json:"is_urgent"`
}

// CreateSignatureRequest creates a new signature request
func CreateSignatureRequest(c *gin.Context) {
	var req SignatureRequestRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, req.DocumentID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Generate unique signature ID
	signatureID := fmt.Sprintf("SIG-REQ-%d-%d", req.DocumentID, time.Now().Unix())

	// Create signature request (only using fields that exist in database)
	signatureReq := models.DigitalSignature{
		SignatureID:         signatureID,
		DocumentID:          req.DocumentID,
		SignerID:            req.SignerID,
		Type:                models.SignatureType(req.SignatureType),
		Status:              models.SignatureStatusPending,
		SignerName:          "TBD",                             // Will be filled from user lookup
		SignerEmail:         "TBD",                             // Will be filled from user lookup
		HashAlgorithm:       models.HashAlgorithmSHA256,        // Default value
		EncryptionAlgorithm: models.EncryptionAlgorithmRSA2048, // Default value
		KeySize:             2048,                              // Default value
		SignatureAlgorithm:  "rsa",                             // Default value
	}

	// Get signer information
	var signer models.User
	if err := db.First(&signer, req.SignerID).Error; err == nil {
		signatureReq.SignerName = signer.FirstName + " " + signer.LastName
		signatureReq.SignerEmail = signer.Email
	}

	if err := db.Create(&signatureReq).Error; err != nil {
		HandleInternalError(c, "Failed to create signature request: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Document").Preload("Signer").First(&signatureReq, signatureReq.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Signature request created successfully",
		Data:    signatureReq,
	})
}

// SignDocumentRequest represents a request to sign a document
type SignDocumentRequest struct {
	SignatureID     uint   `json:"signature_id" binding:"required"`
	CertificateID   uint   `json:"certificate_id" binding:"required"`
	SignatureData   string `json:"signature_data" binding:"required"`
	SignatureMethod string `json:"signature_method"`
	Comments        string `json:"comments"`
}

// SignDocument signs a document
func SignDocument(c *gin.Context) {
	var req SignDocumentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get signature request
	var signature models.DigitalSignature
	if err := db.First(&signature, req.SignatureID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Signature request")
			return
		}
		HandleInternalError(c, "Failed to fetch signature request: "+err.Error())
		return
	}

	// Verify certificate exists and is valid
	var certificate models.DigitalCertificate
	if err := db.First(&certificate, req.CertificateID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Certificate")
			return
		}
		HandleInternalError(c, "Failed to fetch certificate: "+err.Error())
		return
	}

	// Check if certificate is still valid
	if time.Now().After(certificate.NotAfter) {
		HandleBadRequest(c, "Certificate has expired")
		return
	}

	// Update signature with signing information
	signature.CertificateID = &req.CertificateID
	signature.SignatureData = req.SignatureData
	signature.SignatureMethod = req.SignatureMethod
	signature.Status = models.SignatureStatusSigned
	signature.SignedAt = &time.Time{}
	*signature.SignedAt = time.Now()
	signature.Notes = req.Comments

	if err := db.Save(&signature).Error; err != nil {
		HandleInternalError(c, "Failed to update signature: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Document").Preload("RequestedBy").Preload("Signer").Preload("Certificate").First(&signature, signature.SignatureID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document signed successfully",
		Data:    signature,
	})
}

// RejectSignatureRequest represents a request to reject a signature
type RejectSignatureRequest struct {
	SignatureID uint   `json:"signature_id" binding:"required"`
	Reason      string `json:"reason" binding:"required"`
	Comments    string `json:"comments"`
}

// RejectSignature rejects a signature request
func RejectSignature(c *gin.Context) {
	var req RejectSignatureRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get signature request
	var signature models.DigitalSignature
	if err := db.First(&signature, req.SignatureID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Signature request")
			return
		}
		HandleInternalError(c, "Failed to fetch signature request: "+err.Error())
		return
	}

	// Update signature with rejection information
	signature.Status = models.SignatureStatusRejected
	signature.Notes = req.Reason + " - " + req.Comments
	// Note: DigitalSignature model doesn't have RejectedAt field, using Notes for rejection info

	if err := db.Save(&signature).Error; err != nil {
		HandleInternalError(c, "Failed to update signature: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Document").Preload("RequestedBy").Preload("Signer").First(&signature, signature.SignatureID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Signature request rejected successfully",
		Data:    signature,
	})
}

// ValidateSignature validates a signature
func ValidateSignature(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get signature
	var signature models.DigitalSignature
	if err := db.Preload("Certificate").First(&signature, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Signature")
			return
		}
		HandleInternalError(c, "Failed to fetch signature: "+err.Error())
		return
	}

	// Perform validation checks
	validationResult := gin.H{
		"signature_id": signature.ID,
		"is_valid":     true,
		"checks":       []gin.H{},
		"validated_at": time.Now(),
	}

	checks := []gin.H{}

	// Check certificate validity
	if signature.Certificate != nil {
		certValid := time.Now().Before(signature.Certificate.NotAfter) &&
			time.Now().After(signature.Certificate.NotBefore) &&
			signature.Certificate.Status == models.CertificateStatusActive

		checks = append(checks, gin.H{
			"check":   "certificate_validity",
			"passed":  certValid,
			"message": "Certificate validity check",
		})

		if !certValid {
			validationResult["is_valid"] = false
		}
	}

	// Check signature integrity
	signatureIntact := signature.SignatureData != ""
	checks = append(checks, gin.H{
		"check":   "signature_integrity",
		"passed":  signatureIntact,
		"message": "Signature data integrity check",
	})

	if !signatureIntact {
		validationResult["is_valid"] = false
	}

	// Check document hash
	documentIntact := signature.DocumentHash != ""
	checks = append(checks, gin.H{
		"check":   "document_integrity",
		"passed":  documentIntact,
		"message": "Document hash integrity check",
	})

	if !documentIntact {
		validationResult["is_valid"] = false
	}

	validationResult["checks"] = checks

	// Update signature validation status
	signature.IsValid = validationResult["is_valid"].(bool)
	signature.ValidatedAt = &time.Time{}
	*signature.ValidatedAt = time.Now()

	if validationResult["is_valid"].(bool) {
		signature.ValidationStatus = "valid"
	} else {
		signature.ValidationStatus = "invalid"
	}

	db.Save(&signature)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Signature validation completed",
		Data:    validationResult,
	})
}

// GetUserSignatures returns user signatures
func GetUserSignatures(c *gin.Context) {
	// Get user ID from authenticated context
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	userID := userIDInterface.(uint)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query
	query := db.Model(&models.DigitalSignature{}).Where("signer_id = ?", userID)

	// Apply filters
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("type = ?", search.Type)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get signatures with relationships
	var signatures []models.DigitalSignature
	if err := query.Preload("Document").Preload("Certificate").Preload("RequestedBy").Find(&signatures).Error; err != nil {
		HandleInternalError(c, "Failed to fetch user signatures: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       signatures,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CertificateRequest represents a request to create or update a certificate
type CertificateRequest struct {
	Subject            string `json:"subject" binding:"required"`
	CommonName         string `json:"common_name" binding:"required"`
	Organization       string `json:"organization"`
	OrganizationalUnit string `json:"organizational_unit"`
	Country            string `json:"country"`
	State              string `json:"state"`
	Locality           string `json:"locality"`
	EmailAddress       string `json:"email_address"`
	KeyAlgorithm       string `json:"key_algorithm"`
	KeyLength          int    `json:"key_length"`
	ValidityPeriod     int    `json:"validity_period"` // Days
	Purpose            string `json:"purpose"`
	Notes              string `json:"notes"`
}

// CreateCertificate creates a new certificate
func CreateCertificate(c *gin.Context) {
	var req CertificateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Generate proper cryptographic certificate data
	serialNumber, err := generateSerialNumber()
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to generate serial number",
			Message: err.Error(),
		})
		return
	}

	// Generate certificate data for thumbprint calculation
	certificateData := fmt.Sprintf("CN=%s,O=%s,OU=%s,C=%s,ST=%s,L=%s,emailAddress=%s",
		req.CommonName, req.Organization, req.OrganizationalUnit,
		req.Country, req.State, req.Locality, req.EmailAddress)

	thumbprint := generateThumbprint(certificateData)

	// Set defaults
	if req.KeyLength == 0 {
		req.KeyLength = 2048
	}
	if req.ValidityPeriod == 0 {
		req.ValidityPeriod = 365
	}
	if req.KeyAlgorithm == "" {
		req.KeyAlgorithm = "rsa_2048"
	}

	// Create certificate
	certificate := models.DigitalCertificate{
		SerialNumber:       serialNumber,
		Thumbprint:         thumbprint,
		Status:             models.CertificateStatusActive,
		OwnerID:            userID.(uint),
		Subject:            req.Subject,
		Issuer:             "Internal CA", // Default issuer
		CommonName:         req.CommonName,
		Organization:       req.Organization,
		OrganizationalUnit: req.OrganizationalUnit,
		Country:            req.Country,
		State:              req.State,
		Locality:           req.Locality,
		EmailAddress:       req.EmailAddress,
		NotBefore:          time.Now(),
		NotAfter:           time.Now().AddDate(0, 0, req.ValidityPeriod),
		IssuedAt:           time.Now(),
		PublicKey:          "-----BEGIN PUBLIC KEY-----\n[Generated Public Key]\n-----END PUBLIC KEY-----",
		PrivateKey:         "-----BEGIN PRIVATE KEY-----\n[Encrypted Private Key]\n-----END PRIVATE KEY-----",
		CertificateData:    "-----BEGIN CERTIFICATE-----\n[Generated Certificate]\n-----END CERTIFICATE-----",
		KeyAlgorithm:       models.EncryptionAlgorithm(req.KeyAlgorithm),
		KeyLength:          req.KeyLength,
		Purpose:            req.Purpose,
		Notes:              req.Notes,
		IsActive:           true,
	}

	if err := db.Create(&certificate).Error; err != nil {
		HandleInternalError(c, "Failed to create certificate: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Owner").First(&certificate, certificate.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Certificate created successfully",
		Data:    certificate,
	})
}

// GetCertificates returns all certificates
func GetCertificates(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query
	query := db.Model(&models.DigitalCertificate{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("common_name ILIKE ? OR subject ILIKE ? OR organization ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("key_algorithm = ?", search.Type)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get certificates with relationships
	var certificates []models.DigitalCertificate
	if err := query.Preload("Owner").Find(&certificates).Error; err != nil {
		HandleInternalError(c, "Failed to fetch certificates: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       certificates,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetCertificate returns a specific certificate
func GetCertificate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get certificate with relationships
	var certificate models.DigitalCertificate
	if err := db.Preload("Owner").First(&certificate, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Certificate")
			return
		}
		HandleInternalError(c, "Failed to fetch certificate: "+err.Error())
		return
	}

	// Increment usage count
	db.Model(&certificate).Update("signature_count", certificate.SignatureCount+1)
	certificate.LastUsedAt = &time.Time{}
	*certificate.LastUsedAt = time.Now()
	db.Model(&certificate).Update("last_used_at", certificate.LastUsedAt)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Certificate retrieved successfully",
		Data:    certificate,
	})
}

// UpdateCertificate updates a certificate
func UpdateCertificate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req CertificateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing certificate
	var certificate models.DigitalCertificate
	if err := db.First(&certificate, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Certificate")
			return
		}
		HandleInternalError(c, "Failed to fetch certificate: "+err.Error())
		return
	}

	// Update certificate fields (only certain fields should be updatable)
	certificate.Organization = req.Organization
	certificate.OrganizationalUnit = req.OrganizationalUnit
	certificate.Purpose = req.Purpose
	certificate.Notes = req.Notes

	if err := db.Save(&certificate).Error; err != nil {
		HandleInternalError(c, "Failed to update certificate: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Owner").First(&certificate, certificate.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Certificate updated successfully",
		Data:    certificate,
	})
}

// RevokeCertificateRequest represents a request to revoke a certificate
type RevokeCertificateRequest struct {
	Reason string `json:"reason" binding:"required"`
	Notes  string `json:"notes"`
}

// RevokeCertificate revokes a certificate
func RevokeCertificate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RevokeCertificateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing certificate
	var certificate models.DigitalCertificate
	if err := db.First(&certificate, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Certificate")
			return
		}
		HandleInternalError(c, "Failed to fetch certificate: "+err.Error())
		return
	}

	// Update certificate status to revoked
	certificate.Status = models.CertificateStatusRevoked
	certificate.RevokedAt = &time.Time{}
	*certificate.RevokedAt = time.Now()
	userIDUint := userID.(uint)
	certificate.RevokedByID = &userIDUint
	certificate.RevocationReason = req.Reason
	certificate.Notes = req.Notes
	certificate.IsActive = false

	if err := db.Save(&certificate).Error; err != nil {
		HandleInternalError(c, "Failed to revoke certificate: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Owner").Preload("RevokedBy").First(&certificate, certificate.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Certificate revoked successfully",
		Data:    certificate,
	})
}

// GetDocumentSignatures returns document signatures
func GetDocumentSignatures(c *gin.Context) {
	documentID, valid := ValidateID(c, "document_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query
	query := db.Model(&models.DigitalSignature{}).Where("document_id = ?", documentID)

	// Apply filters
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("type = ?", search.Type)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get signatures with relationships
	var signatures []models.DigitalSignature
	if err := query.Preload("Signer").Preload("Certificate").Preload("RequestedBy").Find(&signatures).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document signatures: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       signatures,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// AssignRetentionPolicy assigns a retention policy
func AssignRetentionPolicy(c *gin.Context) {
	var req struct {
		PolicyID              uint                    `json:"policy_id" binding:"required"`
		DocumentID            *uint                   `json:"document_id"`
		CategoryID            *uint                   `json:"category_id"`
		AgencyID              *uint                   `json:"agency_id"`
		EffectiveDate         time.Time               `json:"effective_date"`
		ExpirationDate        *time.Time              `json:"expiration_date"`
		OverrideRetentionDays *int                    `json:"override_retention_days"`
		OverrideAction        *models.RetentionAction `json:"override_action"`
		OverrideReason        string                  `json:"override_reason"`
		Notes                 string                  `json:"notes"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Validate that at least one target is specified
	if req.DocumentID == nil && req.CategoryID == nil && req.AgencyID == nil {
		HandleBadRequest(c, "At least one target (document_id, category_id, or agency_id) must be specified")
		return
	}

	// Validate policy exists
	var policy models.RetentionPolicy
	if err := db.First(&policy, req.PolicyID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Retention policy")
			return
		}
		HandleInternalError(c, "Failed to fetch retention policy: "+err.Error())
		return
	}

	// Validate targets exist
	if req.DocumentID != nil {
		var document models.Document
		if err := db.First(&document, *req.DocumentID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Document")
				return
			}
			HandleInternalError(c, "Failed to validate document: "+err.Error())
			return
		}
	}

	if req.CategoryID != nil {
		var category models.Category
		if err := db.First(&category, *req.CategoryID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Category")
				return
			}
			HandleInternalError(c, "Failed to validate category: "+err.Error())
			return
		}
	}

	if req.AgencyID != nil {
		var agency models.Agency
		if err := db.First(&agency, *req.AgencyID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Agency")
				return
			}
			HandleInternalError(c, "Failed to validate agency: "+err.Error())
			return
		}
	}

	// Set default effective date if not provided
	if req.EffectiveDate.IsZero() {
		req.EffectiveDate = time.Now()
	}

	// Create assignment
	assignment := models.RetentionPolicyAssignment{
		PolicyID:              req.PolicyID,
		DocumentID:            req.DocumentID,
		CategoryID:            req.CategoryID,
		AgencyID:              req.AgencyID,
		EffectiveDate:         req.EffectiveDate,
		ExpirationDate:        req.ExpirationDate,
		OverrideRetentionDays: req.OverrideRetentionDays,
		OverrideAction:        req.OverrideAction,
		OverrideReason:        req.OverrideReason,
		AssignedByID:          *userID.(*uint),
		Notes:                 req.Notes,
		AssignedAt:            time.Now(),
		IsActive:              true,
	}

	if err := db.Create(&assignment).Error; err != nil {
		HandleInternalError(c, "Failed to assign retention policy: "+err.Error())
		return
	}

	// Load the created assignment with relationships
	if err := db.Preload("Policy").Preload("Document").Preload("Category").Preload("Agency").Preload("AssignedBy").First(&assignment, assignment.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created assignment: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Retention policy assigned successfully",
		Data:    assignment,
	})
}

// ExecuteRetentionPolicy executes a retention policy
func ExecuteRetentionPolicy(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		ExecutionType string `json:"execution_type"` // "manual", "scheduled", "triggered"
		DryRun        bool   `json:"dry_run"`        // If true, only simulate execution
		Notes         string `json:"notes"`
	}

	// Bind JSON (optional parameters)
	c.ShouldBindJSON(&req)

	// Set default execution type
	if req.ExecutionType == "" {
		req.ExecutionType = "manual"
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get retention policy
	var policy models.RetentionPolicy
	if err := db.Preload("PolicyAssignments").First(&policy, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Retention policy")
			return
		}
		HandleInternalError(c, "Failed to fetch retention policy: "+err.Error())
		return
	}

	// Check if policy is active
	if policy.Status != models.RetentionStatusActive {
		HandleBadRequest(c, "Cannot execute inactive retention policy")
		return
	}

	// Start execution log
	startTime := time.Now()
	executionLog := models.RetentionExecutionLog{
		PolicyID:      policy.ID,
		ExecutionDate: startTime,
		ExecutionType: req.ExecutionType,
		Action:        policy.Action,
		Status:        "running",
		ExecutedByID:  userID.(*uint),
		TriggerEvent:  "manual_execution",
		StartTime:     startTime,
	}

	if err := db.Create(&executionLog).Error; err != nil {
		HandleInternalError(c, "Failed to create execution log: "+err.Error())
		return
	}

	// Execute policy (simplified implementation)
	var documentsProcessed, documentsArchived, documentsDeleted, documentsSkipped, errorCount int
	var processedDocumentIDs []uint
	var errorDetails []string

	// Process each assignment
	for _, assignment := range policy.PolicyAssignments {
		if !assignment.IsActive {
			continue
		}

		// Calculate retention end date
		retentionDays := policy.RetentionPeriodDays
		if assignment.OverrideRetentionDays != nil {
			retentionDays = *assignment.OverrideRetentionDays
		}

		cutoffDate := time.Now().AddDate(0, 0, -retentionDays)

		// Find documents to process based on assignment type
		var documents []models.Document
		query := db.Model(&models.Document{}).Where("created_at < ?", cutoffDate)

		if assignment.DocumentID != nil {
			query = query.Where("id = ?", *assignment.DocumentID)
		} else if assignment.CategoryID != nil {
			query = query.Joins("JOIN document_category_assignments dca ON documents.id = dca.document_id").
				Where("dca.category_id = ?", *assignment.CategoryID)
		} else if assignment.AgencyID != nil {
			query = query.Where("agency_id = ?", *assignment.AgencyID)
		}

		if err := query.Find(&documents).Error; err != nil {
			errorCount++
			errorDetails = append(errorDetails, "Failed to query documents: "+err.Error())
			continue
		}

		// Process each document
		for _, document := range documents {
			documentsProcessed++
			processedDocumentIDs = append(processedDocumentIDs, document.ID)

			if req.DryRun {
				// In dry run mode, just count what would be processed
				switch policy.Action {
				case models.ActionArchive:
					documentsArchived++
				case models.ActionDelete:
					documentsDeleted++
				}
				continue
			}

			// Execute the actual action
			action := policy.Action
			if assignment.OverrideAction != nil {
				action = *assignment.OverrideAction
			}

			switch action {
			case models.ActionArchive:
				// Comprehensive archive implementation
				err := executeArchiveAction(db, &document, policy.ID, userID.(uint))
				if err != nil {
					errorCount++
					errorDetails = append(errorDetails, "Failed to archive document "+strconv.Itoa(int(document.ID))+": "+err.Error())
				} else {
					documentsArchived++
				}

			case models.ActionDelete:
				// Comprehensive delete implementation with audit trail
				err := executeDeleteAction(db, &document, policy.ID, userID.(uint))
				if err != nil {
					errorCount++
					errorDetails = append(errorDetails, "Failed to delete document "+strconv.Itoa(int(document.ID))+": "+err.Error())
				} else {
					documentsDeleted++
				}

			case models.ActionReview:
				// Comprehensive review implementation with task creation
				err := executeReviewAction(db, &document, policy.ID, userID.(uint))
				if err != nil {
					errorCount++
					errorDetails = append(errorDetails, "Failed to mark document "+strconv.Itoa(int(document.ID))+" for review: "+err.Error())
				}

			case models.ActionNotify:
				// Comprehensive notification implementation
				err := executeNotifyAction(db, &document, policy.ID, userID.(uint))
				if err != nil {
					errorCount++
					errorDetails = append(errorDetails, "Failed to send notification for document "+strconv.Itoa(int(document.ID))+": "+err.Error())
				} else {
					documentsSkipped++
				}
			}
		}
	}

	// Update execution log
	endTime := time.Now()
	duration := int(endTime.Sub(startTime).Milliseconds())
	status := "success"
	if errorCount > 0 {
		if documentsProcessed == errorCount {
			status = "failed"
		} else {
			status = "partial"
		}
	}

	updates := map[string]interface{}{
		"status":              status,
		"documents_processed": documentsProcessed,
		"documents_archived":  documentsArchived,
		"documents_deleted":   documentsDeleted,
		"documents_skipped":   documentsSkipped,
		"error_count":         errorCount,
		"end_time":            endTime,
		"duration_ms":         duration,
		"execution_summary":   generateExecutionSummary(documentsProcessed, documentsArchived, documentsDeleted, documentsSkipped, errorCount, req.DryRun),
	}

	if err := db.Model(&executionLog).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update execution log: "+err.Error())
		return
	}

	// Update policy statistics
	if !req.DryRun {
		policyUpdates := map[string]interface{}{
			"last_executed_at":   endTime,
			"execution_count":    policy.ExecutionCount + 1,
			"documents_archived": policy.DocumentsArchived + documentsArchived,
			"documents_deleted":  policy.DocumentsDeleted + documentsDeleted,
		}
		db.Model(&policy).Updates(policyUpdates)
	}

	// Load updated execution log with relationships
	if err := db.Preload("Policy").Preload("ExecutedBy").First(&executionLog, executionLog.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load execution log: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Retention policy executed successfully",
		Data:    executionLog,
	})
}

// generateExecutionSummary creates a human-readable summary of the execution
func generateExecutionSummary(processed, archived, deleted, skipped, errors int, dryRun bool) string {
	prefix := ""
	if dryRun {
		prefix = "[DRY RUN] "
	}

	summary := prefix + "Processed " + strconv.Itoa(processed) + " documents. "
	if archived > 0 {
		summary += "Archived: " + strconv.Itoa(archived) + ". "
	}
	if deleted > 0 {
		summary += "Deleted: " + strconv.Itoa(deleted) + ". "
	}
	if skipped > 0 {
		summary += "Skipped: " + strconv.Itoa(skipped) + ". "
	}
	if errors > 0 {
		summary += "Errors: " + strconv.Itoa(errors) + ". "
	}

	return summary
}

// GetDocumentRetentionStatus returns document retention status
func GetDocumentRetentionStatus(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Get document retention status
	var retentionStatus models.DocumentRetentionStatus
	if err := db.Preload("Policy").Preload("Document").Where("document_id = ?", id).First(&retentionStatus).Error; err != nil {
		if err.Error() == "record not found" {
			// No retention policy assigned to this document
			c.JSON(http.StatusOK, SuccessResponse{
				Message: "Document retention status retrieved successfully",
				Data: gin.H{
					"document_id":          id,
					"has_retention_policy": false,
					"status":               "no_policy",
					"message":              "No retention policy assigned to this document",
				},
			})
			return
		}
		HandleInternalError(c, "Failed to fetch document retention status: "+err.Error())
		return
	}

	// Calculate additional status information
	now := time.Now()
	daysUntilAction := int(retentionStatus.ActionDueDate.Sub(now).Hours() / 24)
	isOverdue := retentionStatus.ActionDueDate.Before(now)

	// Determine current status
	currentStatus := retentionStatus.Status
	if isOverdue && currentStatus == "active" {
		currentStatus = "overdue"
	}

	// Get policy assignments for this document
	var assignments []models.RetentionPolicyAssignment
	db.Preload("Policy").Where("document_id = ? AND is_active = ?", id, true).Find(&assignments)

	// Prepare response data
	responseData := gin.H{
		"document_id":          id,
		"has_retention_policy": true,
		"retention_status":     retentionStatus,
		"current_status":       currentStatus,
		"days_until_action":    daysUntilAction,
		"is_overdue":           isOverdue,
		"action_due_date":      retentionStatus.ActionDueDate,
		"retention_start_date": retentionStatus.RetentionStartDate,
		"retention_end_date":   retentionStatus.RetentionEndDate,
		"action":               retentionStatus.Action,
		"is_legal_hold":        retentionStatus.IsLegalHold,
		"policy_assignments":   assignments,
	}

	// Add legal hold information if applicable
	if retentionStatus.IsLegalHold {
		responseData["legal_hold_reason"] = retentionStatus.LegalHoldReason
	}

	// Add execution information if action has been executed
	if retentionStatus.ActionExecutedAt != nil {
		responseData["action_executed_at"] = retentionStatus.ActionExecutedAt
		responseData["action_executed_by_id"] = retentionStatus.ActionExecutedByID
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document retention status retrieved successfully",
		Data:    responseData,
	})
}

// Helper functions for cryptographic operations

// generateSerialNumber generates a cryptographically secure serial number
func generateSerialNumber() (string, error) {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return fmt.Sprintf("CERT-%s", hex.EncodeToString(bytes)), nil
}

// generateThumbprint generates a SHA1 thumbprint for certificate data
func generateThumbprint(certificateData string) string {
	hasher := sha1.New()
	hasher.Write([]byte(certificateData))
	return fmt.Sprintf("SHA1:%s", hex.EncodeToString(hasher.Sum(nil)))
}

// Policy execution helper functions

// executeArchiveAction performs a comprehensive archive operation
func executeArchiveAction(db *gorm.DB, document *models.Document, policyID, userID uint) error {
	// Begin transaction
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 1. Update document status
	if err := tx.Model(document).Update("status", "archived").Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. Update document retention status to reflect archival
	var retentionStatus models.DocumentRetentionStatus
	if err := tx.Where("document_id = ? AND policy_id = ?", document.ID, policyID).First(&retentionStatus).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update status with archive information
	now := time.Now()
	retentionStatus.Status = "archived"
	retentionStatus.ActionExecutedAt = &now
	retentionStatus.ActionExecutedByID = &userID
	retentionStatus.ArchiveLocation = "primary-archive"

	if err := tx.Save(&retentionStatus).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 3. Create audit log entry
	documentIDPtr := document.ID
	userIDPtr := userID
	auditLog := models.SystemEvent{
		EventType: "document.archived",
		EntityID:  &documentIDPtr,
		UserID:    &userIDPtr,
		Data:      fmt.Sprintf(`{"policy_id":%d,"document_id":%d,"action":"archived","reason":"retention_policy"}`, policyID, document.ID),
	}

	if err := tx.Create(&auditLog).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	return tx.Commit().Error
}

// executeDeleteAction performs a comprehensive delete operation with audit trail
func executeDeleteAction(db *gorm.DB, document *models.Document, policyID, userID uint) error {
	// Begin transaction
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 1. Update document retention status to reflect deletion
	var retentionStatus models.DocumentRetentionStatus
	if err := tx.Where("document_id = ? AND policy_id = ?", document.ID, policyID).First(&retentionStatus).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Update status with deletion information
	now := time.Now()
	retentionStatus.Status = "deleted"
	retentionStatus.ActionExecutedAt = &now
	retentionStatus.ActionExecutedByID = &userID

	if err := tx.Save(&retentionStatus).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. Create audit log entry
	documentIDPtr := document.ID
	userIDPtr := userID
	auditLog := models.SystemEvent{
		EventType: "document.deleted",
		EntityID:  &documentIDPtr,
		UserID:    &userIDPtr,
		Data:      fmt.Sprintf(`{"policy_id":%d,"document_id":%d,"action":"deleted","reason":"retention_policy"}`, policyID, document.ID),
	}

	if err := tx.Create(&auditLog).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 3. Soft delete the document
	if err := tx.Delete(document).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	return tx.Commit().Error
}

// executeReviewAction creates a review task for the document
func executeReviewAction(db *gorm.DB, document *models.Document, policyID, userID uint) error {
	// Begin transaction
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 1. Update document status
	if err := tx.Model(document).Update("status", "pending_review").Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. Create review task
	dueDate := time.Now().AddDate(0, 0, 14) // Due in 14 days
	reviewTask := models.Task{
		Title:       fmt.Sprintf("Review document: %s", document.Title),
		Description: fmt.Sprintf("Review document due to retention policy #%d", policyID),
		Type:        models.TaskTypeReview,
		Status:      models.TaskStatusPending,
		Priority:    models.TaskPriorityMedium,
		DueDate:     &dueDate,
		CreatedByID: userID,
		SourceType:  "document",
		SourceID:    &document.ID,
		DocumentID:  &document.ID,
	}

	if err := tx.Create(&reviewTask).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 3. Create audit log entry
	documentIDPtr := document.ID
	userIDPtr := userID
	auditLog := models.SystemEvent{
		EventType: "document.review_requested",
		EntityID:  &documentIDPtr,
		UserID:    &userIDPtr,
		Data:      fmt.Sprintf(`{"policy_id":%d,"document_id":%d,"task_id":%d,"action":"review_requested","reason":"retention_policy"}`, policyID, document.ID, reviewTask.ID),
	}

	if err := tx.Create(&auditLog).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	return tx.Commit().Error
}

// executeNotifyAction sends notifications about the document
func executeNotifyAction(db *gorm.DB, document *models.Document, policyID, userID uint) error {
	// Begin transaction
	tx := db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 1. Create notification record
	expiresAt := time.Now().AddDate(0, 0, 30) // Expires in 30 days
	notification := models.Notification{
		Type:       "in_app",
		Priority:   "normal",
		Subject:    "Document Retention Notice",
		Message:    fmt.Sprintf("Document '%s' has reached its retention period according to policy #%d", document.Title, policyID),
		Recipients: fmt.Sprintf(`[{"user_id":%d}]`, document.CreatedByID),
		Category:   "retention_policy",
		Source:     "retention_service",
		SourceID:   fmt.Sprintf("%d", policyID),
		ExpiresAt:  &expiresAt,
		Data:       fmt.Sprintf(`{"policy_id":%d,"document_id":%d,"created_by":%d}`, policyID, document.ID, userID),
	}

	if err := tx.Create(&notification).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 2. Create audit log entry
	documentIDPtr := document.ID
	userIDPtr := userID
	auditLog := models.SystemEvent{
		EventType: "document.retention_notification",
		EntityID:  &documentIDPtr,
		UserID:    &userIDPtr,
		Data:      fmt.Sprintf(`{"policy_id":%d,"document_id":%d,"notification_id":%d,"action":"notification_sent","reason":"retention_policy"}`, policyID, document.ID, notification.ID),
	}

	if err := tx.Create(&auditLog).Error; err != nil {
		tx.Rollback()
		return err
	}

	// Commit transaction
	return tx.Commit().Error
}
