import React, { useState, useEffect } from 'react';
import {
  PlusIcon,
  TrashIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  DocumentTextIcon,
  BookOpenIcon,
  SparklesIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { RegulationWithChunks, ChunkWithContent } from '../../types';
import regulationApi from '../../services/regulationApi';

interface StructuredSection {
  type: string;
  number: string;
  title: string;
  content: string;
  orderIndex: number;
  subsections?: StructuredSection[];
}

interface StructuredContentManagerProps {
  regulation: RegulationWithChunks;
  onContentUpdated: (updatedRegulation: RegulationWithChunks) => void;
}

const StructuredContentManager: React.FC<StructuredContentManagerProps> = ({
  regulation,
  onContentUpdated
}) => {
  const [sections, setSections] = useState<StructuredSection[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showAddSection, setShowAddSection] = useState(false);

  // Initialize sections from existing chunks
  useEffect(() => {
    if (regulation.chunks && regulation.chunks.length > 0) {
      const convertedSections = convertChunksToSections(regulation.chunks);
      setSections(convertedSections);
    } else {
      // No structured content exists
      setSections([]);
    }
  }, [regulation]);

  const convertChunksToSections = (chunks: ChunkWithContent[]): StructuredSection[] => {
    // Comprehensive hierarchical conversion with proper structure validation
    return chunks
      .filter(chunk => chunk && chunk.chunk_type) // Filter out invalid chunks
      .sort((a, b) => a.order_in_parent - b.order_in_parent) // Ensure proper ordering
      .map((chunk, index) => {
        // Validate and normalize chunk data
        const section: StructuredSection = {
          type: normalizeChunkType(chunk.chunk_type),
          number: generateSectionNumber(chunk),
          title: sanitizeTitle(chunk.title || ''),
          content: processContent(chunk.current_content?.content || ''),
          orderIndex: chunk.order_in_parent || index,
          subsections: []
        };

        // Recursively process children with proper hierarchy validation
        if (chunk.children && chunk.children.length > 0) {
          // Validate hierarchy depth to prevent infinite nesting
          const maxDepth = getMaxHierarchyDepth(chunk.chunk_type);
          if (getCurrentHierarchyDepth(chunks, chunk) < maxDepth) {
            section.subsections = convertChunksToSections(chunk.children);
          } else {
            console.warn(`Maximum hierarchy depth reached for chunk ${chunk.id}, skipping children`);
          }
        }

        return section;
      });
  };

  // Helper functions for comprehensive hierarchical content management

  const normalizeChunkType = (chunkType: string): string => {
    // Normalize chunk types to standard values
    const typeMapping: { [key: string]: string } = {
      'title': 'title',
      'division': 'division',
      'chapter': 'chapter',
      'subtitle': 'subtitle',
      'section': 'section',
      'subsection': 'subsection',
      'paragraph': 'paragraph',
      'clause': 'clause',
      'subclause': 'subclause',
      // Handle legacy or alternative naming
      'part': 'section',
      'subpart': 'subsection',
      'item': 'paragraph',
      'subitem': 'clause'
    };

    return typeMapping[chunkType.toLowerCase()] || 'section';
  };

  const generateSectionNumber = (chunk: ChunkWithContent): string => {
    // Generate proper section numbering based on hierarchy and type
    if (chunk.number) {
      return chunk.number;
    }

    // Generate number based on chunk type and position
    const typeNumbering: { [key: string]: string } = {
      'title': 'Title',
      'division': 'Division',
      'chapter': 'Chapter',
      'subtitle': 'Subtitle',
      'section': '§',
      'subsection': '',
      'paragraph': '',
      'clause': '',
      'subclause': ''
    };

    const prefix = typeNumbering[chunk.chunk_type] || '';
    const orderNumber = chunk.order_in_parent || 1;

    // Generate hierarchical numbering
    switch (chunk.chunk_type) {
      case 'title':
      case 'division':
      case 'chapter':
        return `${prefix} ${orderNumber}`;
      case 'section':
        return `${prefix} ${orderNumber}`;
      case 'subsection':
        return `(${String.fromCharCode(96 + orderNumber)})`; // (a), (b), (c)...
      case 'paragraph':
        return `(${orderNumber})`;
      case 'clause':
        return `(${String.fromCharCode(64 + orderNumber)})`; // (A), (B), (C)...
      case 'subclause':
        return `(${orderNumber.toString().split('').map(d => ['i', 'ii', 'iii', 'iv', 'v', 'vi', 'vii', 'viii', 'ix'][parseInt(d) - 1] || d).join('')})`; // (i), (ii), (iii)...
      default:
        return orderNumber.toString();
    }
  };

  const sanitizeTitle = (title: string): string => {
    // Sanitize and format titles
    if (!title || title.trim() === '') {
      return '';
    }

    // Remove excessive whitespace and normalize
    let sanitized = title.trim().replace(/\s+/g, ' ');

    // Capitalize first letter if not already capitalized
    if (sanitized.length > 0 && sanitized[0] !== sanitized[0].toUpperCase()) {
      sanitized = sanitized[0].toUpperCase() + sanitized.slice(1);
    }

    // Remove trailing periods unless it's an abbreviation
    if (sanitized.endsWith('.') && !sanitized.match(/\b[A-Z]{2,}\.$/)) {
      sanitized = sanitized.slice(0, -1);
    }

    return sanitized;
  };

  const processContent = (content: string): string => {
    // Process and clean content
    if (!content || content.trim() === '') {
      return '';
    }

    // Remove excessive whitespace while preserving paragraph breaks
    let processed = content
      .replace(/[ \t]+/g, ' ') // Replace multiple spaces/tabs with single space
      .replace(/\n\s*\n/g, '\n\n') // Normalize paragraph breaks
      .trim();

    // Ensure proper sentence spacing
    processed = processed.replace(/([.!?])\s+/g, '$1 ');

    // Handle common formatting issues
    processed = processed
      .replace(/\s+([,.;:])/g, '$1') // Remove space before punctuation
      .replace(/([.!?])\s*([A-Z])/g, '$1 $2'); // Ensure space after sentence endings

    return processed;
  };

  const getMaxHierarchyDepth = (chunkType: string): number => {
    // Define maximum allowed depth for each chunk type
    const maxDepths: { [key: string]: number } = {
      'title': 6,
      'division': 5,
      'chapter': 4,
      'subtitle': 4,
      'section': 3,
      'subsection': 2,
      'paragraph': 1,
      'clause': 1,
      'subclause': 0
    };

    return maxDepths[chunkType] || 3;
  };

  const getCurrentHierarchyDepth = (allChunks: ChunkWithContent[], currentChunk: ChunkWithContent): number => {
    // Calculate current depth in hierarchy
    let depth = 0;
    let current = currentChunk;

    // Traverse up the hierarchy to count depth
    while (current.parent_chunk_id) {
      depth++;
      const parent = allChunks.find(chunk => chunk.id === current.parent_chunk_id);
      if (!parent) break; // Prevent infinite loops
      current = parent;

      // Safety check to prevent infinite loops
      if (depth > 10) {
        console.warn('Maximum depth calculation reached, possible circular reference');
        break;
      }
    }

    return depth;
  };

  const validateHierarchyStructure = (sections: StructuredSection[]): boolean => {
    // Validate the overall hierarchy structure
    for (const section of sections) {
      // Check for proper type progression
      if (!isValidTypeProgression(section)) {
        return false;
      }

      // Recursively validate subsections
      if (section.subsections && section.subsections.length > 0) {
        if (!validateHierarchyStructure(section.subsections)) {
          return false;
        }
      }
    }

    return true;
  };

  const isValidTypeProgression = (section: StructuredSection): boolean => {
    // Define valid type progressions
    const typeHierarchy = [
      'title', 'division', 'chapter', 'subtitle', 'section', 'subsection', 'paragraph', 'clause', 'subclause'
    ];

    if (!section.subsections || section.subsections.length === 0) {
      return true;
    }

    const currentIndex = typeHierarchy.indexOf(section.type);
    if (currentIndex === -1) return false;

    // Check that all subsections have valid types
    for (const subsection of section.subsections) {
      const subsectionIndex = typeHierarchy.indexOf(subsection.type);
      if (subsectionIndex === -1 || subsectionIndex <= currentIndex) {
        return false;
      }
    }

    return true;
  };

  const addSection = () => {
    const newSection: StructuredSection = {
      type: 'section',
      number: '',
      title: '',
      content: '',
      orderIndex: sections.length,
      subsections: []
    };
    setSections([...sections, newSection]);
    setShowAddSection(false);
  };

  const updateSection = (index: number, field: keyof StructuredSection, value: string) => {
    const updatedSections = [...sections];
    if (field === 'subsections') return; // Handle subsections separately
    (updatedSections[index] as any)[field] = value;
    setSections(updatedSections);
  };

  const removeSection = (index: number) => {
    const updatedSections = sections.filter((_, i) => i !== index);
    setSections(updatedSections);
  };

  const moveSection = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === sections.length - 1)
    ) {
      return;
    }

    const updatedSections = [...sections];
    const targetIndex = direction === 'up' ? index - 1 : index + 1;
    [updatedSections[index], updatedSections[targetIndex]] = [
      updatedSections[targetIndex],
      updatedSections[index]
    ];

    // Update order indices
    updatedSections.forEach((section, i) => {
      section.orderIndex = i;
    });

    setSections(updatedSections);
  };

  const generateDefaultStructure = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await regulationApi.generateDefaultStructure(regulation.regulation.id);
      
      // Reload the regulation with new structured content
      const updatedRegulation = await regulationApi.getRegulationWithChunks(regulation.regulation.id);
      onContentUpdated(updatedRegulation);
      
      // Update local sections
      if (updatedRegulation.chunks) {
        const convertedSections = convertChunksToSections(updatedRegulation.chunks);
        setSections(convertedSections);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to generate default structure');
    } finally {
      setLoading(false);
    }
  };

  const saveStructuredContent = async () => {
    setLoading(true);
    setError(null);

    try {
      // Use POST for new content, PUT for updates
      const hasExistingContent = regulation.chunks && regulation.chunks.length > 0;

      if (hasExistingContent) {
        await regulationApi.updateStructuredContent(regulation.regulation.id, {
          sections: sections
        });
      } else {
        await regulationApi.addStructuredContent(regulation.regulation.id, {
          sections: sections
        });
      }

      // Reload the regulation with new structured content
      const updatedRegulation = await regulationApi.getRegulationWithChunks(regulation.regulation.id);
      onContentUpdated(updatedRegulation);
    } catch (err: any) {
      setError(err.message || 'Failed to save structured content');
    } finally {
      setLoading(false);
    }
  };

  const deleteAllStructuredContent = async () => {
    if (!confirm('Are you sure you want to delete all structured content? This action cannot be undone.')) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await regulationApi.deleteStructuredContent(regulation.regulation.id);

      // Reload the regulation
      const updatedRegulation = await regulationApi.getRegulationWithChunks(regulation.regulation.id);
      onContentUpdated(updatedRegulation);

      // Clear local sections
      setSections([]);
    } catch (err: any) {
      setError(err.message || 'Failed to delete structured content');
    } finally {
      setLoading(false);
    }
  };

  const sectionTypes = [
    { value: 'title', label: 'Title' },
    { value: 'chapter', label: 'Chapter' },
    { value: 'section', label: 'Section' },
    { value: 'subsection', label: 'Subsection' },
    { value: 'paragraph', label: 'Paragraph' }
  ];

  if (regulation.chunks && regulation.chunks.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div className="flex items-center mb-4">
          <ExclamationTriangleIcon className="h-6 w-6 text-yellow-600 mr-2" />
          <h3 className="text-lg font-medium text-yellow-800">
            No Structured Content Available
          </h3>
        </div>
        <p className="text-yellow-700 mb-4">
          This regulation does not have any structured content yet. You can either generate a default structure or create custom sections.
        </p>
        <div className="flex space-x-4">
          <button
            onClick={generateDefaultStructure}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            <SparklesIcon className="h-4 w-4 mr-2" />
            {loading ? 'Generating...' : 'Generate Default Structure'}
          </button>
          <button
            onClick={() => setShowAddSection(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Custom Section
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <BookOpenIcon className="h-6 w-6 text-gray-400 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">Structured Content</h3>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddSection(true)}
            className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Section
          </button>
          {sections.length > 0 && (
            <button
              onClick={deleteAllStructuredContent}
              disabled={loading}
              className="inline-flex items-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete All
            </button>
          )}
          <button
            onClick={saveStructuredContent}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            <DocumentTextIcon className="h-4 w-4 mr-2" />
            {loading ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Sections */}
      <div className="space-y-4">
        {sections.map((section, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <select
                  value={section.type}
                  onChange={(e) => updateSection(index, 'type', e.target.value)}
                  className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  {sectionTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
                <input
                  type="text"
                  placeholder="Number (e.g., 1.1, I, A)"
                  value={section.number}
                  onChange={(e) => updateSection(index, 'number', e.target.value)}
                  className="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => moveSection(index, 'up')}
                  disabled={index === 0}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                >
                  <ChevronUpIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => moveSection(index, 'down')}
                  disabled={index === sections.length - 1}
                  className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                >
                  <ChevronDownIcon className="h-4 w-4" />
                </button>
                <button
                  onClick={() => removeSection(index)}
                  className="p-1 text-red-400 hover:text-red-600"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
            
            <div className="space-y-3">
              <input
                type="text"
                placeholder="Section Title"
                value={section.title}
                onChange={(e) => updateSection(index, 'title', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
              <textarea
                placeholder="Section Content"
                value={section.content}
                onChange={(e) => updateSection(index, 'content', e.target.value)}
                rows={4}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
        ))}
      </div>

      {/* Add Section Form */}
      {showAddSection && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-sm font-medium text-gray-900">Add New Section</h4>
            <button
              onClick={() => setShowAddSection(false)}
              className="text-gray-400 hover:text-gray-600"
            >
              ×
            </button>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={addSection}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
            >
              Add Section
            </button>
            <button
              onClick={() => setShowAddSection(false)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default StructuredContentManager;
