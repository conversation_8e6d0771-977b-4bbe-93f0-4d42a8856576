'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  BuildingOfficeIcon,
  BriefcaseIcon,
  CalendarIcon,
  ShieldCheckIcon,
  PencilIcon,
  TrashIcon,
  CheckCircleIcon,
  XCircleIcon,
  KeyIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';
import { User } from '../../../types';


interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string;
}

interface Permission {
  id: number;
  name: string;
  display_name: string;
  resource: string;
  action: string;
}

const UserViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const { user: currentUser } = useAuthStore();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const fetchUser = async () => {
    try {
      const response = await apiService.getUser(parseInt(userId));
      setUser(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch user');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);
      await apiService.deleteUser(parseInt(userId));
      router.push('/admin/users');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete user');
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canEdit = currentUser && currentUser.role === 'admin';
  const canDelete = canEdit && user && user.id !== currentUser.id; // Can't delete yourself

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading user...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !user) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">User Not Found</h1>
            <p className="text-gray-600 mt-2">{error || 'The requested user could not be found.'}</p>
            <Link
              href="/admin/users"
              className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Users
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/admin/users"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Users
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {user.first_name} {user.last_name}
              </h1>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  user.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {user.is_active ? (
                    <>
                      <CheckCircleIcon className="h-3 w-3 mr-1" />
                      Active
                    </>
                  ) : (
                    <>
                      <XCircleIcon className="h-3 w-3 mr-1" />
                      Inactive
                    </>
                  )}
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <ShieldCheckIcon className="h-3 w-3 mr-1" />
                  {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                </span>
              </div>
            </div>
            
            {canEdit && (
              <div className="flex space-x-2">
                <Link
                  href={`/admin/users/${user.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </Link>
                {canDelete && (
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* User Details */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">User Details</h2>
          </div>
          
          <div className="px-6 py-4 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <UserIcon className="h-4 w-4 mr-2" />
                  Username
                </h3>
                <p className="text-gray-900">@{user.username}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <EnvelopeIcon className="h-4 w-4 mr-2" />
                  Email
                </h3>
                <p className="text-gray-900">{user.email}</p>
              </div>

              {user.phone && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <PhoneIcon className="h-4 w-4 mr-2" />
                    Phone
                  </h3>
                  <p className="text-gray-900">{user.phone}</p>
                </div>
              )}

              {user.department && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                    Department
                  </h3>
                  <p className="text-gray-900">{user.department}</p>
                </div>
              )}

              {user.title && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <BriefcaseIcon className="h-4 w-4 mr-2" />
                    Job Title
                  </h3>
                  <p className="text-gray-900">{user.title}</p>
                </div>
              )}
            </div>

            {/* Bio */}
            {user.bio && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Bio</h3>
                <p className="text-gray-900">{user.bio}</p>
              </div>
            )}

            {/* Role */}
            {user.role && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4 flex items-center">
                  <ShieldCheckIcon className="h-4 w-4 mr-2" />
                  User Role
                </h3>
                <div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                >
                  <h4 className="text-sm font-medium text-gray-900 capitalize">
                    {user.role.replace('_', ' ')}
                  </h4>
                  <p className="text-xs text-gray-500 mt-1">
                    {user.role}
                  </p>
                </div>
              </div>
            )}



            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4 border-t border-gray-200">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Created
                </h3>
                <p className="text-gray-900">{formatDate(user.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h3>
                <p className="text-gray-900">{formatDate(user.updated_at)}</p>
              </div>
              {user.last_login_at && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Last Login</h3>
                  <p className="text-gray-900">{formatDate(user.last_login_at)}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <TrashIcon className="mx-auto h-12 w-12 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900 mt-2">Delete User</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Are you sure you want to delete this user? This action cannot be undone and will remove all associated data.
                </p>
                <div className="flex justify-center space-x-3 mt-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                    disabled={deleteLoading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default UserViewPage;
