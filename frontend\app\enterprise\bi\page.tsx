'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Dashboard {
  id: number;
  dashboard_name: string;
  description: string;
  category: string;
  view_count: number;
  last_viewed: string;
  is_active: boolean;
}

interface KPI {
  id: number;
  kpi_name: string;
  category: string;
  current_value: number;
  target_value: number;
  unit: string;
  trend: string;
  last_calculated: string;
}

interface Report {
  id: number;
  report_name: string;
  report_type: string;
  analytics_type: string;
  status: string;
  execution_count: number;
  last_run: string;
}

const BusinessIntelligencePage: React.FC = () => {
  const router = useRouter();
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [kpis, setKPIs] = useState<KPI[]>([]);
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'dashboards', name: 'Dashboards', icon: '📈' },
    { id: 'reports', name: 'Reports', icon: '📋' },
    { id: 'kpis', name: 'KPIs', icon: '🎯' },
    { id: 'datasources', name: 'Data Sources', icon: '🗄️' },
    { id: 'datamining', name: 'Data Mining', icon: '🔍' }
  ];

  useEffect(() => {
    fetchBIData();
  }, []);

  const fetchBIData = async () => {
    try {
      setLoading(true);
      
      // Mock data for dashboards
      const mockDashboards: Dashboard[] = [
        {
          id: 1,
          dashboard_name: 'Executive Dashboard',
          description: 'High-level KPIs and metrics for executives',
          category: 'executive',
          view_count: 245,
          last_viewed: '2024-01-15T10:30:00Z',
          is_active: true
        },
        {
          id: 2,
          dashboard_name: 'Financial Performance',
          description: 'Financial metrics and budget tracking',
          category: 'financial',
          view_count: 156,
          last_viewed: '2024-01-14T16:45:00Z',
          is_active: true
        },
        {
          id: 3,
          dashboard_name: 'Operational Metrics',
          description: 'Day-to-day operational performance indicators',
          category: 'operational',
          view_count: 89,
          last_viewed: '2024-01-13T09:15:00Z',
          is_active: true
        }
      ];

      // Mock data for KPIs
      const mockKPIs: KPI[] = [
        {
          id: 1,
          kpi_name: 'Revenue Growth',
          category: 'financial',
          current_value: 15.2,
          target_value: 20.0,
          unit: 'percentage',
          trend: 'up',
          last_calculated: '2024-01-15T08:00:00Z'
        },
        {
          id: 2,
          kpi_name: 'Customer Satisfaction',
          category: 'customer',
          current_value: 4.3,
          target_value: 4.5,
          unit: 'rating',
          trend: 'stable',
          last_calculated: '2024-01-15T08:00:00Z'
        },
        {
          id: 3,
          kpi_name: 'Employee Turnover',
          category: 'operational',
          current_value: 8.5,
          target_value: 5.0,
          unit: 'percentage',
          trend: 'down',
          last_calculated: '2024-01-15T08:00:00Z'
        }
      ];

      // Mock data for reports
      const mockReports: Report[] = [
        {
          id: 1,
          report_name: 'Monthly Financial Summary',
          report_type: 'scheduled',
          analytics_type: 'descriptive',
          status: 'active',
          execution_count: 12,
          last_run: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          report_name: 'Sales Forecast',
          report_type: 'adhoc',
          analytics_type: 'predictive',
          status: 'active',
          execution_count: 5,
          last_run: '2024-01-10T14:30:00Z'
        }
      ];

      setDashboards(mockDashboards);
      setKPIs(mockKPIs);
      setReports(mockReports);
    } catch (err) {
      setError('Failed to load business intelligence data');
      console.error('Error fetching BI data:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTrendIcon = (trend: string): string => {
    switch (trend) {
      case 'up': return '📈';
      case 'down': return '📉';
      case 'stable': return '➡️';
      default: return '➡️';
    }
  };

  const getTrendColor = (trend: string): string => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      case 'stable': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getCategoryColor = (category: string): string => {
    const colors: { [key: string]: string } = {
      'executive': 'bg-purple-100 text-purple-800',
      'financial': 'bg-green-100 text-green-800',
      'operational': 'bg-blue-100 text-blue-800',
      'customer': 'bg-orange-100 text-orange-800'
    };
    return colors[category] || 'bg-gray-100 text-gray-800';
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">📈</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Dashboards</p>
              <p className="text-2xl font-semibold text-gray-900">{dashboards.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">🎯</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">KPIs Tracked</p>
              <p className="text-2xl font-semibold text-gray-900">{kpis.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">📋</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Reports Generated</p>
              <p className="text-2xl font-semibold text-gray-900">{reports.reduce((sum, r) => sum + r.execution_count, 0)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">🗄️</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Data Sources</p>
              <p className="text-2xl font-semibold text-gray-900">8</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => router.push('/enterprise/bi/dashboards/new')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="text-2xl mb-2">📊</div>
            <div className="font-medium text-gray-900">Create Dashboard</div>
            <div className="text-sm text-gray-600">Build interactive dashboards</div>
          </button>
          <button
            onClick={() => router.push('/enterprise/bi/reports/new')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="text-2xl mb-2">📋</div>
            <div className="font-medium text-gray-900">Generate Report</div>
            <div className="text-sm text-gray-600">Create custom reports</div>
          </button>
          <button
            onClick={() => router.push('/enterprise/bi/kpis/new')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="text-2xl mb-2">🎯</div>
            <div className="font-medium text-gray-900">Define KPI</div>
            <div className="text-sm text-gray-600">Set up key performance indicators</div>
          </button>
          <button
            onClick={() => router.push('/enterprise/bi/datasources/new')}
            className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="text-2xl mb-2">🗄️</div>
            <div className="font-medium text-gray-900">Add Data Source</div>
            <div className="text-sm text-gray-600">Connect new data sources</div>
          </button>
        </div>
      </div>
    </div>
  );

  const renderDashboards = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Business Intelligence Dashboards</h2>
        <button
          onClick={() => router.push('/enterprise/bi/dashboards/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Dashboard
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {dashboards.map((dashboard) => (
          <div key={dashboard.id} className="bg-white rounded-lg shadow border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{dashboard.dashboard_name}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(dashboard.category)}`}>
                {dashboard.category.toUpperCase()}
              </span>
            </div>
            
            <p className="text-gray-600 text-sm mb-4">{dashboard.description}</p>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Views:</span>
                <span className="text-gray-900 font-medium">{dashboard.view_count}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Last Viewed:</span>
                <span className="text-gray-900">{formatDate(dashboard.last_viewed)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Status:</span>
                <span className={`font-medium ${dashboard.is_active ? 'text-green-600' : 'text-red-600'}`}>
                  {dashboard.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>

            <div className="mt-4 flex space-x-2">
              <button
                onClick={() => router.push(`/enterprise/bi/dashboards/${dashboard.id}`)}
                className="flex-1 bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm hover:bg-blue-100 transition-colors"
              >
                View Dashboard
              </button>
              <button
                onClick={() => router.push(`/enterprise/bi/dashboards/${dashboard.id}/edit`)}
                className="flex-1 bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-100 transition-colors"
              >
                Edit
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderKPIs = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Key Performance Indicators</h2>
        <button
          onClick={() => router.push('/enterprise/bi/kpis/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Add KPI
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {kpis.map((kpi) => (
          <div key={kpi.id} className="bg-white rounded-lg shadow border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{kpi.kpi_name}</h3>
              <span className={`text-2xl ${getTrendColor(kpi.trend)}`}>
                {getTrendIcon(kpi.trend)}
              </span>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-500">Current Value:</span>
                <span className="text-2xl font-bold text-gray-900">
                  {kpi.current_value}{kpi.unit === 'percentage' ? '%' : ''}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Target:</span>
                <span className="text-gray-900 font-medium">
                  {kpi.target_value}{kpi.unit === 'percentage' ? '%' : ''}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Category:</span>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(kpi.category)}`}>
                  {kpi.category.toUpperCase()}
                </span>
              </div>
            </div>

            <div className="mt-4 bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  kpi.current_value >= kpi.target_value ? 'bg-green-600' : 'bg-blue-600'
                }`}
                style={{ width: `${Math.min((kpi.current_value / kpi.target_value) * 100, 100)}%` }}
              ></div>
            </div>

            <div className="mt-4 text-xs text-gray-500">
              Last updated: {formatDate(kpi.last_calculated)}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderReports = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Analytics Reports</h2>
        <button
          onClick={() => router.push('/enterprise/bi/reports/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Report
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Reports management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderDataSources = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Data Sources</h2>
        <button
          onClick={() => router.push('/enterprise/bi/datasources/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Add Data Source
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Data sources management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderDataMining = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Data Mining & ML Models</h2>
        <button
          onClick={() => router.push('/enterprise/bi/datamining/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Model
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Data mining and machine learning interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverview();
      case 'dashboards':
        return renderDashboards();
      case 'reports':
        return renderReports();
      case 'kpis':
        return renderKPIs();
      case 'datasources':
        return renderDataSources();
      case 'datamining':
        return renderDataMining();
      default:
        return renderOverview();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Business Intelligence & Analytics</h1>
          <p className="mt-2 text-gray-600">
            Dashboards, reports, KPIs, data sources, and advanced analytics
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {renderTabContent()}

        {/* Error Display */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BusinessIntelligencePage;
