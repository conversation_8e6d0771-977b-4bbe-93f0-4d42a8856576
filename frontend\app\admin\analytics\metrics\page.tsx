'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon as TrendingUpIcon,
  ArrowTrendingDownIcon as TrendingDownIcon,
  ArrowRightIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface AnalyticsMetric {
  id: number;
  name: string;
  description: string;
  metric_type: 'counter' | 'gauge' | 'histogram' | 'summary';
  category: string;
  current_value: number;
  previous_value: number;
  target_value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  threshold_warning: number;
  threshold_critical: number;
  data_source: string;
  calculation_method: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_calculated: string;
}

const AnalyticsMetricsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [metrics, setMetrics] = useState<AnalyticsMetric[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchMetrics();
  }, []);

  const fetchMetrics = async () => {
    try {
      setLoading(true);
      // Fetch real analytics metrics from API
      const response = await fetch('/api/analytics/metrics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics metrics');
      }

      const data = await response.json();
      const metrics: AnalyticsMetric[] = data.data || [];

      setMetrics(metrics);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch analytics metrics');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this metric?')) return;
    
    try {
      // await apiService.deleteAnalyticsMetric(id);
      setMetrics(metrics.filter(metric => metric.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete metric');
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      // await apiService.toggleMetricActive(id);
      setMetrics(metrics.map(metric => 
        metric.id === id ? { ...metric, is_active: !metric.is_active } : metric
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to toggle metric status');
    }
  };

  const filteredMetrics = metrics.filter(metric =>
    metric.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    metric.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    metric.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'counter':
        return 'bg-blue-100 text-blue-800';
      case 'gauge':
        return 'bg-green-100 text-green-800';
      case 'histogram':
        return 'bg-purple-100 text-purple-800';
      case 'summary':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon className="h-4 w-4 text-green-600" />;
      case 'down':
        return <TrendingDownIcon className="h-4 w-4 text-red-600" />;
      default:
        return <ArrowRightIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  const getHealthStatus = (metric: AnalyticsMetric) => {
    if (metric.current_value >= metric.threshold_critical) {
      return { color: 'text-red-600', icon: <ExclamationTriangleIcon className="h-4 w-4" />, status: 'Critical' };
    } else if (metric.current_value >= metric.threshold_warning) {
      return { color: 'text-yellow-600', icon: <ExclamationTriangleIcon className="h-4 w-4" />, status: 'Warning' };
    } else {
      return { color: 'text-green-600', icon: <ChartBarIcon className="h-4 w-4" />, status: 'Healthy' };
    }
  };

  const formatValue = (value: number | undefined, unit: string) => {
    const safeValue = value || 0;
    if (unit === '%') {
      return `${safeValue.toFixed(1)}%`;
    } else if (unit === 'ms') {
      return `${safeValue.toFixed(0)}ms`;
    } else if (unit.includes('/')) {
      return `${safeValue.toFixed(1)} ${unit}`;
    } else {
      return `${safeValue.toLocaleString()} ${unit}`;
    }
  };

  const calculateChange = (current: number | undefined, previous: number | undefined) => {
    const safeCurrent = current || 0;
    const safePrevious = previous || 0;
    if (safePrevious === 0) return 0;
    return ((safeCurrent - safePrevious) / safePrevious) * 100;
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view analytics metrics.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Analytics Metrics</h1>
              <p className="text-gray-600 mt-1">Monitor and manage system performance metrics</p>
            </div>
            <Link
              href="/admin/analytics/metrics/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Metric
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search metrics..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading analytics metrics...</p>
          </div>
        ) : (
          /* Metrics Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredMetrics.length === 0 ? (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No analytics metrics found.</p>
                <Link
                  href="/admin/analytics/metrics/new"
                  className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Create your first metric
                </Link>
              </div>
            ) : (
              filteredMetrics.map((metric) => {
                const health = getHealthStatus(metric);
                const change = calculateChange(metric.current_value, metric.previous_value);
                
                return (
                  <div key={metric.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 truncate">
                          {metric.name}
                        </h3>
                        <p className="text-sm text-gray-500">{metric.category}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(metric.metric_type)}`}>
                          {metric.metric_type}
                        </span>
                        <div className={`flex items-center ${health.color}`}>
                          {health.icon}
                        </div>
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-3xl font-bold text-gray-900">
                          {formatValue(metric.current_value, metric.unit)}
                        </span>
                        <div className="flex items-center space-x-1">
                          {getTrendIcon(metric.trend)}
                          <span className={`text-sm font-medium ${
                            change > 0 ? 'text-green-600' : change < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {change > 0 ? '+' : ''}{(change || 0).toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      <div className="text-sm text-gray-600">
                        Target: {formatValue(metric.target_value, metric.unit)}
                      </div>
                      <div className="text-sm text-gray-600">
                        Previous: {formatValue(metric.previous_value, metric.unit)}
                      </div>
                    </div>
                    
                    <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                      {metric.description}
                    </p>
                    
                    <div className="text-xs text-gray-500 mb-4">
                      <p>Source: {metric.data_source}</p>
                      <p>Last calculated: {new Date(metric.last_calculated).toLocaleString()}</p>
                      <p className={`font-medium ${metric.is_active ? 'text-green-600' : 'text-red-600'}`}>
                        {metric.is_active ? 'Active' : 'Inactive'}
                      </p>
                    </div>
                    
                    <div className="flex space-x-2">
                      <button
                        onClick={() => router.push(`/admin/analytics/metrics/${metric.id}`)}
                        className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
                      >
                        <EyeIcon className="h-4 w-4 inline mr-1" />
                        View
                      </button>
                      <button
                        onClick={() => router.push(`/admin/analytics/metrics/${metric.id}/edit`)}
                        className="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700"
                      >
                        <PencilIcon className="h-4 w-4 inline mr-1" />
                        Edit
                      </button>
                      <button
                        onClick={() => handleToggleActive(metric.id)}
                        className={`px-3 py-2 rounded text-sm ${
                          metric.is_active 
                            ? 'bg-yellow-600 text-white hover:bg-yellow-700' 
                            : 'bg-green-600 text-white hover:bg-green-700'
                        }`}
                        title={metric.is_active ? 'Deactivate' : 'Activate'}
                      >
                        {metric.is_active ? 'Pause' : 'Start'}
                      </button>
                      <button
                        onClick={() => handleDelete(metric.id)}
                        className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default AnalyticsMetricsPage;
