import React, { useState } from 'react';
import { ParseTextRequest, ProcessingResult, ParsedItem, APIResult } from '../types';
import apiService from '../services/api';

interface IntelligentTextParserProps {
  content: string;
  sourceType?: 'document' | 'regulation';
  sourceId?: number;
  onParsingComplete?: (result: ProcessingResult) => void;
  className?: string;
}

const IntelligentTextParser: React.FC<IntelligentTextParserProps> = ({
  content,
  sourceType,
  sourceId,
  onParsingComplete,
  className = ''
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingResult, setProcessingResult] = useState<ProcessingResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const handleParseText = async (autoExecute: boolean = false) => {
    if (!content.trim()) {
      setError('No content to parse');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const request: ParseTextRequest = {
        content,
        auto_execute: autoExecute,
        source_type: sourceType,
        source_id: sourceId
      };

      const response = await apiService.parseText(request);
      setProcessingResult(response.data);
      setShowPreview(true);
      
      if (onParsingComplete) {
        onParsingComplete(response.data);
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error && 'response' in err &&
        typeof err.response === 'object' && err.response !== null &&
        'data' in err.response && typeof err.response.data === 'object' &&
        err.response.data !== null && 'message' in err.response.data
        ? String(err.response.data.message)
        : 'Failed to parse text';
      setError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'task': return 'text-blue-600 bg-blue-50';
      case 'agency': return 'text-purple-600 bg-purple-50';
      case 'category': return 'text-indigo-600 bg-indigo-50';
      case 'date': return 'text-teal-600 bg-teal-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'created': return 'text-green-600 bg-green-50';
      case 'exists': return 'text-blue-600 bg-blue-50';
      case 'failed': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`intelligent-text-parser ${className}`}>
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">
            Intelligent Text Analysis
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={() => handleParseText(false)}
              disabled={isProcessing || !content.trim()}
              className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? 'Analyzing...' : 'Preview Actions'}
            </button>
            <button
              onClick={() => handleParseText(true)}
              disabled={isProcessing || !content.trim()}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isProcessing ? 'Processing...' : 'Auto-Execute'}
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {isProcessing && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <p className="text-sm text-blue-600">Analyzing text for actionable items...</p>
            </div>
          </div>
        )}

        {showPreview && processingResult && (
          <div className="space-y-4">
            <div className="bg-gray-50 p-3 rounded-md">
              <p className="text-sm font-medium text-gray-900 mb-1">Analysis Summary</p>
              <p className="text-sm text-gray-600">{processingResult.summary}</p>
            </div>

            {processingResult.parsed_items.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Extracted Items</h4>
                <div className="space-y-3">
                  {processingResult.parsed_items.map((item: ParsedItem, index: number) => (
                    <div key={index} className="border border-gray-200 rounded-md p-3">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(item.type)}`}>
                            {item.type}
                          </span>
                          {item.priority && (
                            <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(item.priority)}`}>
                              {item.priority}
                            </span>
                          )}
                          <span className="text-xs text-gray-500">
                            {Math.round(item.confidence * 100)}% confidence
                          </span>
                        </div>
                      </div>
                      <h5 className="font-medium text-gray-900 mb-1">{item.title}</h5>
                      <p className="text-sm text-gray-600 mb-2">{item.description}</p>
                      {item.due_date && (
                        <p className="text-sm text-gray-500">
                          <span className="font-medium">Due:</span> {formatDate(item.due_date)}
                        </p>
                      )}
                      <div className="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-600">
                        <span className="font-medium">Source:</span> &ldquo;{item.source_text}&rdquo;
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {processingResult.api_results.length > 0 && (
              <div>
                <h4 className="text-md font-medium text-gray-900 mb-3">Execution Results</h4>
                <div className="space-y-2">
                  {processingResult.api_results.map((result: APIResult, index: number) => (
                    <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-md">
                      <div className="flex items-center space-x-3">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(result.type)}`}>
                          {result.type}
                        </span>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getActionColor(result.action)}`}>
                          {result.action}
                        </span>
                        <span className="text-sm font-medium text-gray-900">{result.entity_name}</span>
                      </div>
                      {result.error && (
                        <span className="text-xs text-red-600">{result.error}</span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {processingResult.errors && processingResult.errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <h4 className="text-sm font-medium text-red-800 mb-2">Errors</h4>
                <ul className="text-sm text-red-600 space-y-1">
                  {processingResult.errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default IntelligentTextParser;
