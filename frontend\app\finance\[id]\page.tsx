'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  ChartBarIcon,
  PencilIcon,
  TrashIcon,
  TagIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import financeApi from '../../services/financeApi';

interface FinanceRecord {
  id: number;
  year: number;
  amount: number;
  performance_percentage: number;
  budget_type: string;
  description: string;
  document_id?: number;
  regulation_id?: number;
  category_id?: number;
  created_at: string;
  updated_at: string;
  created_by?: {
    id: number;
    username: string;
    first_name: string;
    last_name: string;
  };
  document?: {
    id: number;
    title: string;
  };
  regulation?: {
    id: number;
    title: string;
  };
  category?: {
    id: number;
    name: string;
  };
}

const FinanceViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const financeId = params.id as string;
  const { user } = useAuthStore();
  const [finance, setFinance] = useState<FinanceRecord | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (financeId) {
      fetchFinance();
    }
  }, [financeId]);

  const fetchFinance = async () => {
    try {
      const response = await financeApi.getFinance(parseInt(financeId));
      setFinance(response);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch finance record');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);
      await financeApi.deleteFinance(parseInt(financeId));
      router.push('/finance');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete finance record');
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getBudgetTypeColor = (type: string) => {
    switch (type) {
      case 'operational':
        return 'bg-blue-100 text-blue-800';
      case 'capital':
        return 'bg-green-100 text-green-800';
      case 'emergency':
        return 'bg-red-100 text-red-800';
      case 'research':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const canEdit = user && (user.role === 'admin' || user.role === 'editor');

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading finance record...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !finance) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Finance Record Not Found</h1>
            <p className="text-gray-600 mt-2">{error || 'The requested finance record could not be found.'}</p>
            <Link
              href="/finance"
              className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Finance
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/finance"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Finance
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Finance Record - {finance.year}
              </h1>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getBudgetTypeColor(finance.budget_type)}`}>
                  {finance.budget_type.charAt(0).toUpperCase() + finance.budget_type.slice(1)}
                </span>
                <span className={`text-sm font-medium ${getPerformanceColor(finance.performance_percentage)}`}>
                  <ChartBarIcon className="h-4 w-4 inline mr-1" />
                  {finance.performance_percentage}% Performance
                </span>
              </div>
            </div>
            
            {canEdit && (
              <div className="flex space-x-2">
                <Link
                  href={`/finance/${finance.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </Link>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Finance Details */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Finance Details</h2>
          </div>
          
          <div className="px-6 py-4 space-y-6">
            {/* Financial Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-blue-700 mb-2 flex items-center">
                  <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                  Amount ({finance.budget_type})
                </h3>
                <p className="text-2xl font-bold text-blue-900">{formatCurrency(finance.amount)}</p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-green-700 mb-2 flex items-center">
                  <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                  Budget Type
                </h3>
                <p className="text-2xl font-bold text-green-900">{finance.budget_type.toUpperCase()}</p>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium text-purple-700 mb-2 flex items-center">
                  <ChartBarIcon className="h-4 w-4 mr-2" />
                  Performance
                </h3>
                <p className={`text-2xl font-bold ${getPerformanceColor(finance.performance_percentage)}`}>
                  {finance.performance_percentage}%
                </p>
              </div>
            </div>

            {/* Financial Details */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Financial Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Amount</p>
                  <p className="text-lg font-semibold text-blue-600">
                    {formatCurrency(finance.amount)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Performance vs Target</p>
                  <p className={`text-lg font-semibold ${
                    finance.performance_percentage >= 100 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {finance.performance_percentage}% of target
                  </p>
                </div>
              </div>
            </div>

            {/* Description */}
            {finance.description && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
                <p className="text-gray-900 whitespace-pre-wrap">{finance.description}</p>
              </div>
            )}

            {/* Related Entities */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {finance.document && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                    Related Document
                  </h3>
                  <Link
                    href={`/documents/${finance.document.id}`}
                    className="text-primary-600 hover:text-primary-500 underline"
                  >
                    {finance.document.title}
                  </Link>
                </div>
              )}

              {finance.regulation && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Related Regulation</h3>
                  <Link
                    href={`/regulations/${finance.regulation.id}`}
                    className="text-primary-600 hover:text-primary-500 underline"
                  >
                    {finance.regulation.title}
                  </Link>
                </div>
              )}

              {finance.category && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <TagIcon className="h-4 w-4 mr-2" />
                    Category
                  </h3>
                  <Link
                    href={`/categories/${finance.category.id}`}
                    className="text-primary-600 hover:text-primary-500 underline"
                  >
                    {finance.category.name}
                  </Link>
                </div>
              )}
            </div>

            {/* Created By */}
            {finance.created_by && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Created By</h3>
                <p className="text-gray-900">
                  {finance.created_by.first_name} {finance.created_by.last_name} (@{finance.created_by.username})
                </p>
              </div>
            )}

            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <ClockIcon className="h-4 w-4 mr-2" />
                  Created
                </h3>
                <p className="text-gray-900">{formatDate(finance.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h3>
                <p className="text-gray-900">{formatDate(finance.updated_at)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <TrashIcon className="mx-auto h-12 w-12 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900 mt-2">Delete Finance Record</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Are you sure you want to delete this finance record? This action cannot be undone.
                </p>
                <div className="flex justify-center space-x-3 mt-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                    disabled={deleteLoading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default FinanceViewPage;
