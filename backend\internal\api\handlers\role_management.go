package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// RoleRequest represents the request structure for roles
type RoleRequest struct {
	Name        string   `json:"name" binding:"required"`
	Description string   `json:"description"`
	Permissions []string `json:"permissions"`
	IsActive    bool     `json:"is_active"`
}

// GetRoles returns all roles with pagination
func GetRoles(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.<PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total roles
	var total int64
	db.Model(&models.Role{}).Count(&total)

	// Get roles with pagination
	var roles []models.Role
	offset := (page - 1) * perPage
	if err := db.Order("name ASC").
		Limit(perPage).
		Offset(offset).
		Find(&roles).Error; err != nil {
		HandleInternalError(c, "Failed to fetch roles: "+err.Error())
		return
	}

	// Convert to response format
	roleResponses := make([]gin.H, len(roles))
	for i, role := range roles {
		roleResponses[i] = gin.H{
			"id":             role.ID,
			"name":           role.Name,
			"display_name":   role.DisplayName,
			"description":    role.Description,
			"permissions":    role.Permissions,
			"is_system_role": role.IsSystemRole,
			"created_at":     role.CreatedAt,
			"updated_at":     role.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       roleResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetRole returns a single role by ID
func GetRole(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get role
	var role models.Role
	if err := db.First(&role, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Role")
			return
		}
		HandleInternalError(c, "Failed to fetch role: "+err.Error())
		return
	}

	response := gin.H{
		"id":             role.ID,
		"name":           role.Name,
		"display_name":   role.DisplayName,
		"description":    role.Description,
		"permissions":    role.Permissions,
		"is_system_role": role.IsSystemRole,
		"created_at":     role.CreatedAt,
		"updated_at":     role.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Role retrieved successfully",
		Data:    response,
	})
}

// CreateRole creates a new role
func CreateRole(c *gin.Context) {
	var req RoleRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if role name already exists
	var existingRole models.Role
	if err := db.Where("name = ?", req.Name).First(&existingRole).Error; err == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Role name already exists",
			Message: "A role with this name already exists",
		})
		return
	}

	// Generate intelligent display name
	displayName, err := generateRoleDisplayName(req.Name, req.Description, db)
	if err != nil {
		// Fallback to name if generation fails
		displayName = req.Name
	}

	// Create role
	role := &models.Role{
		Name:         req.Name,
		DisplayName:  displayName,
		Description:  req.Description,
		IsSystemRole: false,
	}

	if err := db.Create(role).Error; err != nil {
		HandleInternalError(c, "Failed to create role: "+err.Error())
		return
	}

	response := gin.H{
		"id":             role.ID,
		"name":           role.Name,
		"display_name":   role.DisplayName,
		"description":    role.Description,
		"permissions":    role.Permissions,
		"is_system_role": role.IsSystemRole,
		"created_at":     role.CreatedAt,
		"updated_at":     role.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Role created successfully",
		Data:    response,
	})
}

// UpdateRole updates an existing role
func UpdateRole(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RoleRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing role
	var role models.Role
	if err := db.First(&role, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Role")
			return
		}
		HandleInternalError(c, "Failed to fetch role: "+err.Error())
		return
	}

	// Check if role name already exists (excluding current role)
	var existingRole models.Role
	if err := db.Where("name = ? AND id != ?", req.Name, id).First(&existingRole).Error; err == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Role name already exists",
			Message: "A role with this name already exists",
		})
		return
	}

	// Generate intelligent display name if name or description changed
	var displayName string
	if role.Name != req.Name || role.Description != req.Description {
		generatedDisplayName, err := generateRoleDisplayName(req.Name, req.Description, db)
		if err != nil {
			// Fallback to name if generation fails
			displayName = req.Name
		} else {
			displayName = generatedDisplayName
		}
	} else {
		// Keep existing display name if no changes
		displayName = role.DisplayName
	}

	// Update role fields
	role.Name = req.Name
	role.DisplayName = displayName
	role.Description = req.Description

	if err := db.Save(&role).Error; err != nil {
		HandleInternalError(c, "Failed to update role: "+err.Error())
		return
	}

	response := gin.H{
		"id":             role.ID,
		"name":           role.Name,
		"display_name":   role.DisplayName,
		"description":    role.Description,
		"permissions":    role.Permissions,
		"is_system_role": role.IsSystemRole,
		"created_at":     role.CreatedAt,
		"updated_at":     role.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Role updated successfully",
		Data:    response,
	})
}

// DeleteRole deletes a role
func DeleteRole(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if role exists
	var role models.Role
	if err := db.First(&role, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Role")
			return
		}
		HandleInternalError(c, "Failed to fetch role: "+err.Error())
		return
	}

	// Check if role is being used by any users
	var userCount int64
	db.Model(&models.User{}).Where("role = ?", role.Name).Count(&userCount)
	if userCount > 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Role is in use",
			Message: "Cannot delete role that is assigned to users",
		})
		return
	}

	// Delete role
	if err := db.Delete(&role).Error; err != nil {
		HandleInternalError(c, "Failed to delete role: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Role deleted successfully",
	})
}

// GetPermissions returns all permissions
func GetPermissions(c *gin.Context) {
	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.Permission{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("name ILIKE ? OR display_name ILIKE ? OR description ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}

	// Filter by resource if provided
	if resource := c.Query("resource"); resource != "" {
		query = query.Where("resource = ?", resource)
	}

	// Filter by action if provided
	if action := c.Query("action"); action != "" {
		query = query.Where("action = ?", action)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("resource ASC, action ASC")
	}

	// Get permissions
	var permissions []models.Permission
	if err := query.Find(&permissions).Error; err != nil {
		HandleInternalError(c, "Failed to fetch permissions: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       permissions,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetUserRoles returns user roles
func GetUserRoles(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify user exists
	var user models.User
	if err := db.First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Build query for user roles
	query := db.Model(&models.UserRole{}).Where("user_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Joins("JOIN roles ON user_roles.role_id = roles.id").
			Where("roles.name ILIKE ? OR roles.display_name ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}

	// Filter by active status if provided
	if active := c.Query("active"); active != "" {
		if active == "true" {
			query = query.Where("is_active = ?", true)
		} else if active == "false" {
			query = query.Where("is_active = ?", false)
		}
	}

	// Filter by expired status if provided
	if expired := c.Query("expired"); expired != "" {
		if expired == "true" {
			query = query.Where("expires_at IS NOT NULL AND expires_at <= ?", time.Now())
		} else if expired == "false" {
			query = query.Where("expires_at IS NULL OR expires_at > ?", time.Now())
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("assigned_at DESC")
	}

	// Get user roles with relationships
	var userRoles []models.UserRole
	if err := query.Preload("Role").Preload("AssignedByUser").Find(&userRoles).Error; err != nil {
		HandleInternalError(c, "Failed to fetch user roles: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       userRoles,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// AssignUserRoles assigns roles to a user
func AssignUserRoles(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		RoleIDs   []uint     `json:"role_ids" binding:"required"`
		ExpiresAt *time.Time `json:"expires_at"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context (who is assigning the roles)
	assignerID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify target user exists
	var user models.User
	if err := db.First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Verify all roles exist
	var roles []models.Role
	if err := db.Where("id IN ?", req.RoleIDs).Find(&roles).Error; err != nil {
		HandleInternalError(c, "Failed to fetch roles: "+err.Error())
		return
	}

	if len(roles) != len(req.RoleIDs) {
		HandleBadRequest(c, "One or more role IDs are invalid")
		return
	}

	// Begin transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	var assignedRoles []models.UserRole
	now := time.Now()

	// Assign each role
	for _, roleID := range req.RoleIDs {
		// Check if user already has this role
		var existingRole models.UserRole
		if err := tx.Where("user_id = ? AND role_id = ?", id, roleID).First(&existingRole).Error; err == nil {
			// Role already assigned, update it if needed
			updates := map[string]interface{}{
				"is_active":   true,
				"assigned_at": now,
				"assigned_by": *assignerID.(*uint),
			}
			if req.ExpiresAt != nil {
				updates["expires_at"] = *req.ExpiresAt
			} else {
				updates["expires_at"] = nil
			}

			if err := tx.Model(&existingRole).Updates(updates).Error; err != nil {
				tx.Rollback()
				HandleInternalError(c, "Failed to update existing role assignment: "+err.Error())
				return
			}
			assignedRoles = append(assignedRoles, existingRole)
		} else {
			// Create new role assignment
			userRole := models.UserRole{
				UserID:     id,
				RoleID:     roleID,
				AssignedBy: assignerID.(*uint),
				AssignedAt: now,
				ExpiresAt:  req.ExpiresAt,
				IsActive:   true,
			}

			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				HandleInternalError(c, "Failed to assign role: "+err.Error())
				return
			}
			assignedRoles = append(assignedRoles, userRole)
		}
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		HandleInternalError(c, "Failed to commit role assignments: "+err.Error())
		return
	}

	// Load assigned roles with relationships
	var result []models.UserRole
	if err := db.Where("user_id = ? AND role_id IN ?", id, req.RoleIDs).
		Preload("Role").Preload("AssignedByUser").Find(&result).Error; err != nil {
		HandleInternalError(c, "Failed to load assigned roles: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Roles assigned successfully",
		Data:    result,
	})
}

// RemoveUserRole removes a role from a user
func RemoveUserRole(c *gin.Context) {
	userID, valid := ValidateID(c, "userId")
	if !valid {
		return
	}

	roleID, valid := ValidateID(c, "roleId")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context (who is removing the role)
	removerID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify target user exists
	var user models.User
	if err := db.First(&user, userID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Verify role exists
	var role models.Role
	if err := db.First(&role, roleID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Role")
			return
		}
		HandleInternalError(c, "Failed to fetch role: "+err.Error())
		return
	}

	// Find the user role assignment
	var userRole models.UserRole
	if err := db.Where("user_id = ? AND role_id = ?", userID, roleID).First(&userRole).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User role assignment")
			return
		}
		HandleInternalError(c, "Failed to fetch user role assignment: "+err.Error())
		return
	}

	// Check if this is a system role that shouldn't be removed
	if role.IsSystemRole && role.Name == "admin" {
		// Count how many admin users exist
		var adminCount int64
		db.Model(&models.UserRole{}).
			Joins("JOIN roles ON user_roles.role_id = roles.id").
			Where("roles.name = ? AND user_roles.is_active = ?", "admin", true).
			Count(&adminCount)

		if adminCount <= 1 {
			HandleBadRequest(c, "Cannot remove the last admin user")
			return
		}
	}

	// Remove the role assignment (soft delete)
	if err := db.Delete(&userRole).Error; err != nil {
		HandleInternalError(c, "Failed to remove user role: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Role removed successfully",
		Data: gin.H{
			"user_id":    userID,
			"role_id":    roleID,
			"removed_by": *removerID.(*uint),
			"removed_at": time.Now(),
		},
	})
}

// GetUserPermissionsAdmin returns user permissions for admin
func GetUserPermissionsAdmin(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify user exists
	var user models.User
	if err := db.First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Get user's effective roles
	effectiveRoles, err := models.GetEffectiveRoles(id, db)
	if err != nil {
		HandleInternalError(c, "Failed to fetch user roles: "+err.Error())
		return
	}

	// Get user's permissions based on roles
	permissions, err := models.GetUserPermissions(id, db)
	if err != nil {
		HandleInternalError(c, "Failed to fetch user permissions: "+err.Error())
		return
	}

	// Get detailed role information
	var roleDetails []models.RoleInfo
	for _, role := range effectiveRoles {
		// Get assignment details
		var userRole models.UserRole
		if err := db.Where("user_id = ? AND role_id = ? AND is_active = ?", id, role.ID, true).
			First(&userRole).Error; err == nil {
			roleDetails = append(roleDetails, models.RoleInfo{
				ID:          role.ID,
				Name:        role.Name,
				DisplayName: role.DisplayName,
				AssignedAt:  userRole.AssignedAt,
				ExpiresAt:   userRole.ExpiresAt,
				IsActive:    userRole.IsActive && !userRole.IsExpired(),
			})
		}
	}

	// Get permission details
	var permissionDetails []models.PermissionInfo
	for _, permission := range permissions {
		permissionDetails = append(permissionDetails, models.PermissionInfo{
			ID:          permission.ID,
			Name:        permission.Name,
			DisplayName: permission.DisplayName,
			Resource:    permission.Resource,
			Action:      permission.Action,
		})
	}

	// Compile response
	response := gin.H{
		"user": gin.H{
			"id":         user.ID,
			"username":   user.Username,
			"email":      user.Email,
			"first_name": user.FirstName,
			"last_name":  user.LastName,
			"is_active":  user.IsActive,
		},
		"roles":       roleDetails,
		"permissions": permissionDetails,
		"summary": gin.H{
			"total_roles":       len(roleDetails),
			"total_permissions": len(permissionDetails),
			"effective_roles":   len(effectiveRoles),
			"generated_at":      time.Now(),
		},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User permissions retrieved successfully",
		Data:    response,
	})
}

// GetAvailablePermissions returns all available permissions
func GetAvailablePermissions(c *gin.Context) {
	permissions := []gin.H{
		{"name": "read_documents", "description": "Read documents"},
		{"name": "write_documents", "description": "Create and edit documents"},
		{"name": "delete_documents", "description": "Delete documents"},
		{"name": "read_agencies", "description": "Read agencies"},
		{"name": "write_agencies", "description": "Create and edit agencies"},
		{"name": "delete_agencies", "description": "Delete agencies"},
		{"name": "read_categories", "description": "Read categories"},
		{"name": "write_categories", "description": "Create and edit categories"},
		{"name": "delete_categories", "description": "Delete categories"},
		{"name": "read_users", "description": "Read users"},
		{"name": "write_users", "description": "Create and edit users"},
		{"name": "delete_users", "description": "Delete users"},
		{"name": "manage_roles", "description": "Manage roles and permissions"},
		{"name": "admin_access", "description": "Full administrative access"},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Available permissions retrieved successfully",
		Data:    permissions,
	})
}

// generateRoleDisplayName generates an intelligent display name based on role context
func generateRoleDisplayName(name, description string, db *gorm.DB) (string, error) {
	// Clean and normalize the role name
	cleanName := strings.TrimSpace(name)
	cleanDescription := strings.TrimSpace(description)

	// Role type patterns and their display name templates
	rolePatterns := map[string]string{
		// Administrative roles
		"admin":         "System Administrator",
		"administrator": "System Administrator",
		"super_admin":   "Super Administrator",
		"root":          "Root Administrator",

		// Management roles
		"manager":    "Department Manager",
		"supervisor": "Supervisor",
		"director":   "Director",
		"head":       "Department Head",
		"chief":      "Chief Officer",
		"lead":       "Team Lead",

		// Editorial roles
		"editor":    "Content Editor",
		"reviewer":  "Content Reviewer",
		"publisher": "Content Publisher",
		"author":    "Content Author",
		"writer":    "Content Writer",

		// Viewer roles
		"viewer":   "Content Viewer",
		"reader":   "Document Reader",
		"observer": "System Observer",

		// Analyst roles
		"analyst":    "Data Analyst",
		"researcher": "Research Analyst",
		"specialist": "Subject Matter Specialist",

		// Technical roles
		"developer":  "System Developer",
		"engineer":   "System Engineer",
		"technician": "Technical Support",
		"support":    "Support Specialist",

		// Legal/Compliance roles
		"legal":      "Legal Counsel",
		"compliance": "Compliance Officer",
		"auditor":    "System Auditor",
		"inspector":  "Quality Inspector",

		// Agency-specific roles
		"agency_admin":  "Agency Administrator",
		"agency_user":   "Agency User",
		"public_user":   "Public User",
		"external_user": "External User",
	}

	// Check for direct pattern matches
	lowerName := strings.ToLower(cleanName)
	for pattern, template := range rolePatterns {
		if strings.Contains(lowerName, pattern) {
			return enhanceDisplayNameWithContext(template, cleanName, cleanDescription, db)
		}
	}

	// Extract department/organizational context from description
	departmentContext := extractDepartmentContext(cleanDescription, db)

	// Generate display name based on role structure
	if departmentContext != "" {
		return generateDepartmentalDisplayName(cleanName, departmentContext), nil
	}

	// Check for hierarchical indicators
	if hierarchyLevel := extractHierarchyLevel(cleanName, cleanDescription); hierarchyLevel != "" {
		return generateHierarchicalDisplayName(cleanName, hierarchyLevel), nil
	}

	// Check for permission-based context
	permissionContext := extractPermissionContext(cleanName, cleanDescription)
	if permissionContext != "" {
		return generatePermissionBasedDisplayName(cleanName, permissionContext), nil
	}

	// Fallback: Capitalize and format the name nicely
	return formatRoleName(cleanName), nil
}

// enhanceDisplayNameWithContext adds organizational context to the base display name
func enhanceDisplayNameWithContext(baseDisplayName, roleName, description string, db *gorm.DB) (string, error) {
	// Extract agency context
	if agencyName := extractAgencyContext(description, db); agencyName != "" {
		return fmt.Sprintf("%s (%s)", baseDisplayName, agencyName), nil
	}

	// Extract department context
	if deptName := extractDepartmentContext(description, db); deptName != "" {
		return fmt.Sprintf("%s - %s", baseDisplayName, deptName), nil
	}

	// Extract level context (senior, junior, etc.)
	if level := extractLevelContext(roleName, description); level != "" {
		return fmt.Sprintf("%s %s", level, baseDisplayName), nil
	}

	return baseDisplayName, nil
}

// extractDepartmentContext extracts department information from description
func extractDepartmentContext(description string, db *gorm.DB) string {
	if description == "" {
		return ""
	}

	// Common department keywords
	deptKeywords := []string{
		"finance", "financial", "accounting", "budget",
		"human resources", "hr", "personnel", "staffing",
		"legal", "compliance", "regulatory", "audit",
		"technology", "it", "information technology", "systems",
		"operations", "operational", "logistics", "facilities",
		"marketing", "communications", "public relations", "outreach",
		"research", "development", "innovation", "analytics",
		"security", "safety", "risk management", "emergency",
		"procurement", "purchasing", "contracts", "vendor",
		"quality", "assurance", "standards", "certification",
	}

	lowerDesc := strings.ToLower(description)
	for _, keyword := range deptKeywords {
		if strings.Contains(lowerDesc, keyword) {
			return strings.Title(keyword)
		}
	}

	return ""
}

// extractAgencyContext extracts agency information from description
func extractAgencyContext(description string, db *gorm.DB) string {
	if description == "" || db == nil {
		return ""
	}

	// Try to find agency references in description
	var agencies []models.Agency
	if err := db.Find(&agencies).Error; err != nil {
		return ""
	}

	lowerDesc := strings.ToLower(description)
	for _, agency := range agencies {
		if strings.Contains(lowerDesc, strings.ToLower(agency.Name)) ||
			strings.Contains(lowerDesc, strings.ToLower(agency.ShortName)) {
			return agency.ShortName
		}
	}

	return ""
}

// extractHierarchyLevel extracts hierarchy level from role name/description
func extractHierarchyLevel(name, description string) string {
	text := strings.ToLower(name + " " + description)

	hierarchyLevels := map[string]string{
		"senior":    "Senior",
		"junior":    "Junior",
		"lead":      "Lead",
		"principal": "Principal",
		"chief":     "Chief",
		"head":      "Head",
		"assistant": "Assistant",
		"associate": "Associate",
		"deputy":    "Deputy",
		"vice":      "Vice",
		"executive": "Executive",
		"level_1":   "Level 1",
		"level_2":   "Level 2",
		"level_3":   "Level 3",
		"tier_1":    "Tier 1",
		"tier_2":    "Tier 2",
		"tier_3":    "Tier 3",
	}

	for keyword, level := range hierarchyLevels {
		if strings.Contains(text, keyword) {
			return level
		}
	}

	return ""
}

// extractLevelContext extracts seniority/level context
func extractLevelContext(name, description string) string {
	return extractHierarchyLevel(name, description)
}

// extractPermissionContext extracts permission-based context
func extractPermissionContext(name, description string) string {
	text := strings.ToLower(name + " " + description)

	permissionContexts := map[string]string{
		"read_only":   "Read-Only",
		"readonly":    "Read-Only",
		"view_only":   "View-Only",
		"full_access": "Full Access",
		"limited":     "Limited Access",
		"restricted":  "Restricted Access",
		"elevated":    "Elevated Access",
		"privileged":  "Privileged Access",
		"standard":    "Standard Access",
		"basic":       "Basic Access",
		"advanced":    "Advanced Access",
		"power_user":  "Power User",
		"super_user":  "Super User",
	}

	for keyword, context := range permissionContexts {
		if strings.Contains(text, keyword) {
			return context
		}
	}

	return ""
}

// generateDepartmentalDisplayName creates display name with department context
func generateDepartmentalDisplayName(roleName, department string) string {
	formattedRole := formatRoleName(roleName)
	return fmt.Sprintf("%s - %s", formattedRole, department)
}

// generateHierarchicalDisplayName creates display name with hierarchy context
func generateHierarchicalDisplayName(roleName, hierarchy string) string {
	baseRole := formatRoleName(roleName)
	// Remove hierarchy from base role if it's already there
	lowerBase := strings.ToLower(baseRole)
	lowerHierarchy := strings.ToLower(hierarchy)
	if strings.Contains(lowerBase, lowerHierarchy) {
		return baseRole
	}
	return fmt.Sprintf("%s %s", hierarchy, baseRole)
}

// generatePermissionBasedDisplayName creates display name with permission context
func generatePermissionBasedDisplayName(roleName, permissionContext string) string {
	baseRole := formatRoleName(roleName)
	return fmt.Sprintf("%s (%s)", baseRole, permissionContext)
}

// formatRoleName formats a role name for display
func formatRoleName(name string) string {
	// Replace underscores and hyphens with spaces
	formatted := strings.ReplaceAll(name, "_", " ")
	formatted = strings.ReplaceAll(formatted, "-", " ")

	// Split into words and capitalize each
	words := strings.Fields(formatted)
	for i, word := range words {
		if len(word) > 0 {
			words[i] = strings.ToUpper(word[:1]) + strings.ToLower(word[1:])
		}
	}

	return strings.Join(words, " ")
}
