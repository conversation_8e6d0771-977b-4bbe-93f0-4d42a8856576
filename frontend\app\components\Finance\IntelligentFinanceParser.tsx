'use client';

import React, { useState } from 'react';
import { 
  SparklesIcon, 
  DocumentTextIcon, 
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import { CreateFinanceRequest } from '../../types';

interface ParsedFinanceItem {
  amount: number;
  description: string;
  confidence: number;
  context: string;
  suggested_category?: string;
}

interface IntelligentFinanceParserProps {
  content: string;
  sourceType: 'document' | 'regulation';
  sourceId: number;
  year: number;
  onFinancesExtracted: (finances: CreateFinanceRequest[]) => void;
}

const IntelligentFinanceParser: React.FC<IntelligentFinanceParserProps> = ({
  content,
  sourceType,
  sourceId,
  year,
  onFinancesExtracted
}) => {
  const [parsing, setParsing] = useState(false);
  const [parsedItems, setParsedItems] = useState<ParsedFinanceItem[]>([]);
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());

  // Enhanced regex patterns for financial amounts
  const parseFinancialContent = (text: string): ParsedFinanceItem[] => {
    const patterns = [
      // Dollar amounts with various formats
      /\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*(million|billion|thousand|k|m|b)?/gi,
      // Written amounts
      /(\d+(?:\.\d+)?)\s*(million|billion|thousand)\s*dollars?/gi,
      // Budget allocations
      /(?:budget|allocate[ds]?|fund(?:ing)?|appropriat[ed]?)\s+(?:of\s+)?\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*(million|billion|thousand|k|m|b)?/gi,
      // Cost estimates
      /(?:cost[s]?|expense[s]?|expenditure[s]?)\s+(?:of\s+)?\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*(million|billion|thousand|k|m|b)?/gi,
      // Financial penalties
      /(?:fine[s]?|penalty|penalties)\s+(?:of\s+)?\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*(million|billion|thousand|k|m|b)?/gi
    ];

    const items: ParsedFinanceItem[] = [];
    const sentences = text.split(/[.!?]+/);

    patterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const rawAmount = match[1].replace(/,/g, '');
        const multiplier = match[2]?.toLowerCase();
        
        let amount = parseFloat(rawAmount);
        
        // Apply multipliers
        switch (multiplier) {
          case 'thousand':
          case 'k':
            amount *= 1000;
            break;
          case 'million':
          case 'm':
            amount *= 1000000;
            break;
          case 'billion':
          case 'b':
            amount *= 1000000000;
            break;
        }

        // Find the sentence containing this match
        const matchIndex = match.index || 0;
        const sentence = sentences.find(s => 
          text.indexOf(s) <= matchIndex && 
          text.indexOf(s) + s.length >= matchIndex
        ) || match[0];

        // Determine confidence based on context
        let confidence = 0.5;
        const lowerSentence = sentence.toLowerCase();
        
        if (lowerSentence.includes('budget') || lowerSentence.includes('appropriat')) {
          confidence += 0.3;
        }
        if (lowerSentence.includes('allocat') || lowerSentence.includes('fund')) {
          confidence += 0.2;
        }
        if (lowerSentence.includes('annual') || lowerSentence.includes('yearly')) {
          confidence += 0.1;
        }

        // Suggest category based on context
        let suggested_category = 'Operations';
        if (lowerSentence.includes('research') || lowerSentence.includes('development')) {
          suggested_category = 'Research';
        } else if (lowerSentence.includes('compliance') || lowerSentence.includes('regulatory')) {
          suggested_category = 'Compliance';
        } else if (lowerSentence.includes('infrastructure') || lowerSentence.includes('technology')) {
          suggested_category = 'Infrastructure';
        } else if (lowerSentence.includes('personnel') || lowerSentence.includes('staff')) {
          suggested_category = 'Personnel';
        } else if (lowerSentence.includes('legal') || lowerSentence.includes('administrative')) {
          suggested_category = 'Legal';
        }

        // Generate description
        const description = `${sourceType === 'document' ? 'Document' : 'Regulation'} financial allocation: ${sentence.trim().substring(0, 100)}${sentence.length > 100 ? '...' : ''}`;

        items.push({
          amount,
          description,
          confidence: Math.min(confidence, 1.0),
          context: sentence.trim(),
          suggested_category
        });
      }
    });

    // Remove duplicates and sort by confidence
    const uniqueItems = items.filter((item, index, self) => 
      index === self.findIndex(i => 
        Math.abs(i.amount - item.amount) < 0.01 && 
        i.context === item.context
      )
    ).sort((a, b) => b.confidence - a.confidence);

    return uniqueItems.slice(0, 10); // Limit to top 10 results
  };

  const handleParse = async () => {
    setParsing(true);
    
    try {
      // Simulate API delay for realistic UX
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const items = parseFinancialContent(content);
      setParsedItems(items);
      
      // Auto-select high-confidence items
      const autoSelected = new Set<number>();
      items.forEach((item, index) => {
        if (item.confidence >= 0.7) {
          autoSelected.add(index);
        }
      });
      setSelectedItems(autoSelected);
      
    } catch (error) {
      console.error('Failed to parse financial content:', error);
    } finally {
      setParsing(false);
    }
  };

  const handleItemToggle = (index: number) => {
    const newSelected = new Set(selectedItems);
    if (newSelected.has(index)) {
      newSelected.delete(index);
    } else {
      newSelected.add(index);
    }
    setSelectedItems(newSelected);
  };

  const handleCreateFinances = () => {
    const selectedFinances: CreateFinanceRequest[] = Array.from(selectedItems).map(index => {
      const item = parsedItems[index];
      return {
        amount: item.amount,
        year,
        description: item.description,
        [sourceType === 'document' ? 'document_id' : 'regulation_id']: sourceId,
        budget_type: 'original' as const
      };
    });
    
    onFinancesExtracted(selectedFinances);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-100';
    if (confidence >= 0.6) return 'text-blue-600 bg-blue-100';
    if (confidence >= 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getConfidenceIcon = (confidence: number) => {
    if (confidence >= 0.7) return CheckCircleIcon;
    return ExclamationTriangleIcon;
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <SparklesIcon className="h-6 w-6 text-purple-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900">
            Intelligent Finance Parser
          </h3>
        </div>
        <button
          onClick={handleParse}
          disabled={parsing || !content.trim()}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {parsing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Parsing...
            </>
          ) : (
            <>
              <SparklesIcon className="h-4 w-4 mr-2" />
              Parse Financial Content
            </>
          )}
        </button>
      </div>

      {parsedItems.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              Found {parsedItems.length} potential financial entries. Select the ones you want to create:
            </p>
            <button
              onClick={handleCreateFinances}
              disabled={selectedItems.size === 0}
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <CurrencyDollarIcon className="h-4 w-4 mr-1" />
              Create {selectedItems.size} Finance Entries
            </button>
          </div>

          <div className="space-y-2 max-h-96 overflow-y-auto">
            {parsedItems.map((item, index) => {
              const ConfidenceIcon = getConfidenceIcon(item.confidence);
              const isSelected = selectedItems.has(index);
              
              return (
                <div
                  key={index}
                  className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                    isSelected 
                      ? 'border-purple-300 bg-purple-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleItemToggle(index)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => handleItemToggle(index)}
                          className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                        />
                        <span className="font-medium text-gray-900">
                          {formatCurrency(item.amount)}
                        </span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getConfidenceColor(item.confidence)}`}>
                          <ConfidenceIcon className="h-3 w-3 mr-1" />
                          {Math.round(item.confidence * 100)}% confidence
                        </span>
                        {item.suggested_category && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-gray-800 bg-gray-100">
                            {item.suggested_category}
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-1">
                        {item.description}
                      </p>
                      <p className="text-xs text-gray-500 italic">
                        Context: "{item.context}"
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {!content.trim() && (
        <div className="text-center py-8">
          <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No content to parse</h3>
          <p className="mt-1 text-sm text-gray-500">
            The {sourceType} content is empty or not available for financial parsing.
          </p>
        </div>
      )}
    </div>
  );
};

export default IntelligentFinanceParser;
