// Common types for the Federal Register Clone application

export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  role: UserR<PERSON>;
  is_active: boolean;
  is_verified: boolean;
  last_login_at?: string;
  title?: string;
  department?: string;
  organization?: string;
  phone?: string;
  bio?: string;
  agency_id?: number;
  agency?: Agency;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'admin' | 'editor' | 'reviewer' | 'publisher' | 'viewer' | 'viewer_level_1' | 'viewer_level_2' | 'viewer_level_3' | 'moderator';

export interface UserPreferences {
  id?: number;
  user_id: number;
  // Notification preferences
  email_notifications: boolean;
  document_alerts: boolean;
  weekly_digest: boolean;
  comment_notifications: boolean;
  // Display preferences
  documents_per_page: number;
  default_view: string;
  theme: string;
  language: string;
  timezone: string;
  // Search preferences
  default_search_sort: string;
  save_search_history: boolean;
  autocomplete_enabled: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface Agency {
  id: number;
  name: string;
  short_name?: string;
  slug?: string;
  description?: string;
  website?: string;
  email?: string;
  phone?: string;
  fax?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  country?: string;
  parent_agency_id?: number;
  parent_agency?: Agency;
  sub_agencies?: Agency[];
  is_active?: boolean;
  agency_type?: string;
  jurisdiction?: string;
  established_at?: string;
  established_date?: string; // Legacy field
  logo_url?: string;
  primary_color?: string;
  secondary_color?: string;
  document_count?: number;
  active_documents?: number;
  recent_documents?: number;
  created_at?: string;
  updated_at?: string;
  // Contact information
  mission_statement?: string;
  contact_person?: string;
  contact_title?: string;
  // Regulation relationships
  regulation_count?: number;
  regulation_relationships?: RegulationAgencyRelationship[];
  // Legacy compatibility fields
  website_url?: string;
  contact_email?: string;
  contact_phone?: string;
  abbreviation?: string;
  status?: string;
}

export interface Document {
  id: number;
  title: string;
  content: string;
  type: string;
  status: string;
  agency: string | { id: number; name: string; short_name: string };
  created_at: string;
  view_count: number;
  // Optional fields for compatibility
  slug?: string;
  abstract?: string;
  fr_document_number?: string;
  fr_citation?: string;
  cfr_citations?: string;
  publication_date?: string;
  effective_date?: string;
  termination_date?: string;
  comment_due_date?: string;
  page_count?: number;
  word_count?: number;
  language?: string;
  original_format?: string;
  file_size?: number;
  checksum?: string;
  agency_id?: number;
  created_by_id?: number;
  created_by?: User;
  updated_by_id?: number;
  updated_by?: User;
  parent_document_id?: number;
  parent_document?: Document;
  child_documents?: Document[];
  categories?: Category[];
  tags?: Tag[];
  subjects?: Subject[];
  files?: DocumentFile[];
  reviews?: DocumentReview[];
  comments?: DocumentComment[];
  download_count: number;
  workflow_stage?: string;
  approved_at?: string;
  approved_by_id?: number;
  approved_by?: User;
  published_at?: string;
  published_by_id?: number;
  published_by?: User;
  regulatory_identifier?: string;
  docket_number?: string;
  significant_rule: boolean;
  economic_impact?: string;
  small_entity_impact: boolean;
  accepts_comments: boolean;
  comment_count: number;
  comment_instructions?: string;
  public_hearing_date?: string;
  public_hearing_info?: string;
  visibility_level: number;
  is_public: boolean;
  updated_at: string;
  // Additional fields for compatibility
  pdf_url?: string;
  assigned_to?: User;
  summary?: string; // Legacy field for abstract
  // Workflow fields
  workflow_status?: string;
  submitted_at?: string;
  withdrawn_at?: string;
  submitted_by_id?: number;
  withdrawn_by_id?: number;
  // Regulation relationships
  regulation_count?: number;
  regulation_relationships?: RegulationDocumentRelationship[];
}

export type DocumentType = 'rule' | 'proposed_rule' | 'notice' | 'presidential' | 'correction' | 'other';

export type DocumentStatus = 'draft' | 'under_review' | 'approved' | 'published' | 'withdrawn' | 'superseded' | 'public_inspection';

export interface DocumentFile {
  id: number;
  document_id: number;
  file_name: string;
  original_name: string;
  file_path: string;
  file_type: string;
  file_size: number;
  mime_type: string;
  checksum: string;
  description?: string;
  is_public: boolean;
  is_primary: boolean;
  sort_order: number;
  uploaded_by_id: number;
  uploaded_by: User;
  download_count: number;
  created_at: string;
  updated_at: string;
}

export interface DocumentReview {
  id: number;
  document_id: number;
  document: Document;
  reviewer_id: number;
  reviewer: User;
  status: string;
  comments?: string;
  rating?: number;
  reviewed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentComment {
  id: number;
  document_id: number;
  document: Document;
  commenter_name: string;
  commenter_email?: string;
  organization?: string;
  subject?: string;
  content: string;
  is_public: boolean;
  is_verified: boolean;
  ip_address?: string;
  is_moderated: boolean;
  moderated_by?: number;
  moderator?: User;
  moderated_at?: string;
  created_at: string;
  updated_at: string;
}

// Alias for backward compatibility
export type Comment = DocumentComment;

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  icon?: string;
  sort_order?: number;
  parent_category_id?: number;
  parent_category?: Category;
  sub_categories?: Category[];
  is_active?: boolean;
  document_count?: number;
  created_at?: string;
  updated_at?: string;
  // CFR information
  cfr_title?: string;
  keywords?: string[];
  // Regulation relationships
  regulation_count?: number;
  regulation_relationships?: RegulationCategoryRelationship[];
  // Legacy compatibility fields
  parent_id?: number;
}

export interface Tag {
  id: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  usage_count: number;
  is_active: boolean;
  created_by_id?: number;
  created_by?: User;
  created_at: string;
  updated_at: string;
}

export interface Subject {
  id: number;
  name: string;
  slug: string;
  description?: string;
  cfr_title?: string;
  parent_subject_id?: number;
  parent_subject?: Subject;
  sub_subjects?: Subject[];
  sort_order: number;
  is_active: boolean;
  document_count?: number;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  // Pagination fields for some responses
  page?: number;
  per_page?: number;
  total?: number;
  total_pages?: number;
  id?: number; // For create responses
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
  has_next: boolean;
  has_prev: boolean;
}

export interface ErrorResponse {
  error: string;
  message: string;
}

// Search and filter types
export interface SearchParams {
  query?: string;
  sort?: string;
  order?: 'asc' | 'desc';
  category?: string;
  agency?: string;
  agency_id?: string; // For compatibility
  category_id?: string; // For compatibility
  type?: DocumentType | string; // Allow string for compatibility
  status?: DocumentStatus | string; // Allow string for compatibility
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  per_page?: number;
  search?: string; // For compatibility
}

export interface FilterOptions {
  agencies: Agency[];
  categories: Category[];
  subjects: Subject[];
  documentTypes: { value: DocumentType; label: string }[];
  documentStatuses: { value: DocumentStatus; label: string }[];
}

// Authentication types
export interface LoginRequest {
  identifier: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  title?: string;
  organization?: string;
}

export interface AuthResponse {
  token: string;
  refresh_token: string;
  user: User;
  expires_in: number;
}

// Form types - matches backend API exactly
export interface DocumentFormData {
  // Basic document information
  title: string;
  slug?: string;
  abstract?: string;
  content: string; // Make content required to match Document interface
  type: string; // Backend expects string, not enum
  status?: string;

  // Federal Register specific fields
  fr_document_number?: string;
  fr_citation?: string;
  cfr_citations?: string;
  publication_date?: string;
  effective_date?: string;
  termination_date?: string;
  comment_due_date?: string;

  // Document metadata
  page_count?: number;
  word_count?: number;
  language?: string;
  original_format?: string;
  file_size?: number;
  checksum?: string;

  // Relationships
  agency_id: number;
  created_by_id?: number;
  updated_by_id?: number;
  parent_document_id?: number;

  // Categories, tags, and subjects
  category_ids?: number[];
  tag_ids?: number[];
  subject_ids?: number[];

  // Regulatory information
  regulatory_identifier?: string;
  docket_number?: string;
  significant_rule?: boolean;
  economic_impact?: string;
  small_entity_impact?: boolean;

  // Public participation
  accepts_comments?: boolean;
  comment_count?: number;
  comment_instructions?: string;
  public_hearing_date?: string;
  public_hearing_info?: string;

  // Document visibility and access control
  visibility_level?: number;
  is_public?: boolean;

  // Search and indexing
  view_count?: number;
  download_count?: number;

  // OCR and digital signature fields
  ocr_text?: string;
  ocr_confidence?: number;
  ocr_processed?: boolean;
  require_signature?: boolean;
  signature_method?: string;
}

export interface CommentFormData {
  commenter_name: string;
  commenter_email?: string;
  organization?: string;
  subject?: string;
  content: string;
  // Digital signature fields
  certificate_id?: number;
  signature_data?: string;
  signature_method?: string;
  require_signature?: boolean;
}

// Digital Certificate Types
export interface DigitalCertificate {
  id: number;
  serial: string;
  subject: string;
  issuer: string;
  common_name: string;
  not_before: string;
  not_after: string;
  status: 'active' | 'expired' | 'revoked' | 'suspended' | 'pending' | 'renewing';
  is_default: boolean;
  is_active: boolean;
  purpose: string;
  created_at: string;
}

// Digital Signature Types
export interface DigitalSignature {
  signature_id: string;
  comment_id?: number;
  document_id?: number;
  is_verified: boolean;
  signature_valid: boolean;
  certificate_valid: boolean;
  signed_at: string;
  signer_name: string;
  signer_email: string;
  certificate?: DigitalCertificate;
  verification_checks: {
    certificate_not_expired: boolean;
    certificate_active: boolean;
    signature_timestamp_valid: boolean;
    document_hash_match: boolean;
  };
  overall_valid: boolean;
}

// Signed Comment Request
export interface SignedCommentFormData extends CommentFormData {
  digital_signature: {
    signature_value: string;
    certificate_id: number;
    timestamp: string;
    hash_algorithm: string;
    signing_method: string;
  };
}

// Extended Digital Signature and Certificate types for CRUD operations
export interface DigitalSignatureExtended {
  id: number;
  signature_id: string;
  type: string;
  status: string;
  document_id: number;
  signer_id: number;
  signer_name: string;
  signer_email: string;
  signer_title?: string;
  certificate_id?: number;
  signature_data?: string;
  signature_algorithm?: string;
  hash_algorithm?: string;
  timestamp?: string;
  location?: string;
  reason?: string;
  contact_info?: string;
  mfa_required?: boolean;
  mfa_verified?: boolean;
  ip_address?: string;
  user_agent?: string;
  device_info?: string;
  geo_location?: string;
  workflow_id?: number;
  signature_order?: number;
  is_final?: boolean;
  rejection_reason?: string;
  notes?: string;
  metadata?: string;
  created_at: string;
  updated_at: string;
}

export interface DigitalCertificateExtended {
  id: number;
  serial_number: string;
  thumbprint: string;
  status: string;
  owner_id: number;
  subject: string;
  issuer: string;
  common_name: string;
  organization?: string;
  organizational_unit?: string;
  country?: string;
  state?: string;
  locality?: string;
  email_address?: string;
  not_before: string;
  not_after: string;
  issued_at: string;
  revoked_at?: string;
  revoked_by_id?: number;
  revocation_reason?: string;
  public_key: string;
  private_key?: string;
  certificate_data?: string;
  key_usage?: string;
  extended_key_usage?: string;
  ca_issued?: boolean;
  ca_certificate_id?: number;
  certificate_chain?: string;
  signature_count?: number;
  last_used_at?: string;
  purpose?: string;
  is_default?: boolean;
  is_active?: boolean;
  curve_type?: string;
  subject_alt_names?: string;
  created_at: string;
  updated_at: string;
}

export interface RetentionPolicy {
  id: number;
  name: string;
  description?: string;
  type: string;
  status: string;
  retention_period_days?: number;
  retention_period_years?: number;
  action: string;
  auto_execute?: boolean;
  trigger_event?: string;
  trigger_document_type?: string;
  trigger_category?: string;
  trigger_agency?: string;
  trigger_status?: string;
  trigger_date_field?: string;
  trigger_conditions?: string;
  regulatory_framework?: string;
  legal_basis?: string;
  exceptions?: string;
  created_by_id: number;
  owner_id: number;
  approved_at?: string;
  approved_by_id?: number;
  last_executed_at?: string;
  next_execution_at?: string;
  execution_count?: number;
  is_active?: boolean;
  created_at: string;
  updated_at: string;
}

export interface ProcessingJob {
  id: number;
  name: string;
  type: string;
  status: string;
  priority?: string;
  document_id?: number;
  template_id?: number;
  input_data?: string;
  output_data?: string;
  progress?: number;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  retry_count?: number;
  max_retries?: number;
  created_by_id: number;
  assigned_to_id?: number;
  metadata?: string;
  created_at: string;
  updated_at: string;
}

// UI State types
export interface UIState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  loading: boolean;
  error: string | null;
}

export interface NotificationState {
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  timestamp: number;
}

// Regulation types
export type RegulationType = 'law' | 'rule' | 'regulation' | 'code';

export type RegulationStatus = 'draft' | 'under_review' | 'approved' | 'published' | 'effective' | 'terminated' | 'archived';

export type ChunkType = 'title' | 'division' | 'chapter' | 'subtitle' | 'section' | 'subsection' | 'paragraph' | 'clause' | 'subclause';

export type RelationshipType = 'amends' | 'repeals' | 'refers_to' | 'supersedes' | 'implements';

// New relationship types for regulation-entity relationships
export type DocumentRelationshipType = 'implements' | 'based_on' | 'amends' | 'repeals' | 'references' | 'supersedes';

export type AgencyRelationshipType = 'established_by' | 'authorized_by' | 'modified_by' | 'abolished_by';

export type CategoryRelationshipType = 'created_by' | 'modified_by' | 'abolished_by' | 'governed_by';

export interface LawsAndRules {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  short_title?: string;
  type: RegulationType;
  status: RegulationStatus;
  current_document_version_id?: number;
  current_document_version?: RegulationDocumentVersion;

  // Legal identifiers - unified from both views
  public_law_number?: string;
  regulatory_identifier?: string; // RIN
  cfr_title?: string;
  cfr_citation?: string; // For compatibility
  usc_title?: string;
  docket_number?: string;

  // Dates
  enactment_date?: string;
  effective_date?: string;
  termination_date?: string;
  publication_date?: string;

  // Relationships
  agency_id: number;
  agency: Agency;
  created_by_id: number;
  created_by: User;

  // Content and metadata
  description?: string;
  content?: string;
  notes?: string;
  is_significant: boolean;

  // Hierarchical structure - unified from both views
  hierarchy_level: string;
  chapter_number?: string;
  subchapter?: string;
  part_number?: string;
  section_number?: string;
  subsection?: string;
  parent_id?: number;
  parent?: LawsAndRules;
  children?: LawsAndRules[];
  order_in_parent: number;

  // Additional fields for compatibility and functionality
  chunks_count?: number;
  versions_count?: number;
  version?: string; // Current version number

  // Regulatory analysis fields
  regulatory_impact_analysis?: boolean;
  small_business_impact?: boolean;
  environmental_impact?: boolean;
  federalism_implications?: boolean;

  // Comment and review fields
  comment_end_date?: string;
  parent_regulation?: LawsAndRules;
  supersedes_regulation?: LawsAndRules;
  tags?: string[];

  // New relationship types
  document_relationships?: RegulationDocumentRelationship[];
  agency_relationships?: RegulationAgencyRelationship[];
  category_relationships?: RegulationCategoryRelationship[];
}

// Type alias for easier usage
export type Regulation = LawsAndRules;

export interface RegulationDocumentVersion {
  id: number;
  created_at: string;
  updated_at: string;
  law_rule_id: number;
  version_number: string;
  publication_date?: string;
  effective_date?: string;
  is_official: boolean;
  created_by_id: number;
  created_by: User;
  notes?: string;
  summary_of_changes?: string;
}

export interface RegulationChunk {
  id: number;
  created_at: string;
  updated_at: string;
  law_rule_id: number;
  parent_chunk_id?: number;
  parent_chunk?: RegulationChunk;
  child_chunks?: RegulationChunk[];
  order_in_parent: number;
  chunk_type: ChunkType;
  chunk_identifier: string;
  number?: string;
  title?: string;
  current_chunk_content_version_id?: number;
  current_chunk_content_version?: ChunkContentVersion;
}

export interface ChunkContentVersion {
  id: number;
  created_at: string;
  updated_at: string;
  chunk_id: number;
  content: string;
  version_number: number;
  is_current: boolean;
  is_active: boolean;
  modified_by_id: number;
  modified_by: User;
  change_description?: string;
}

export interface ChunkWithContent {
  id: number;
  created_at: string;
  updated_at: string;
  law_rule_id: number;
  parent_chunk_id?: number;
  order_in_parent: number;
  chunk_type: ChunkType;
  chunk_identifier: string;
  number?: string;
  title?: string;
  current_content?: ChunkContentVersion;
  children: ChunkWithContent[];
}

export interface RegulationWithChunks {
  id: number;
  regulation: LawsAndRules;
  chunks: ChunkWithContent[];
  version_id: number;
  description?: string;
  notes?: string;
}

export interface RegulationFilters {
  type?: RegulationType;
  agency_id?: number;
  search?: string;
  effective_after?: string;
  effective_before?: string;
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export interface CreateRegulationRequest {
  title: string;
  short_title?: string;
  type: RegulationType;
  status?: RegulationStatus;
  hierarchy_level?: ChunkType;

  // Legal identifiers
  public_law_number?: string;
  regulatory_identifier?: string;
  cfr_title?: string;
  usc_title?: string;
  docket_number?: string;

  // Relationships
  agency_id: number;

  // Content and metadata
  description?: string;
  content?: string;
  notes?: string;
  is_significant?: boolean;

  // Dates
  enactment_date?: string;
  effective_date?: string;
  termination_date?: string;
  publication_date?: string;

  // Hierarchical structure
  chapter_number?: string;
  subchapter?: string;
  part_number?: string;
  section_number?: string;
  subsection?: string;
  parent_id?: number;

  // Initial content
  initial_chunks?: CreateChunkRequest[];
}

export interface CreateChunkRequest {
  chunk_type: ChunkType;
  chunk_identifier: string;
  number?: string;
  title?: string;
  content: string;
  parent_chunk_id?: number;
  order_in_parent?: number;
}

export interface AmendChunkRequest {
  content: string;
  change_description: string;
}

// Regulation relationship interfaces
export interface RegulationDocumentRelationship {
  id: number;
  created_at: string;
  updated_at: string;
  regulation_id: number;
  regulation?: LawsAndRules;
  regulation_chunk_id?: number;
  regulation_chunk?: ChunkWithContent;
  document_id: number;
  document?: Document;
  relationship_type: DocumentRelationshipType;
  effective_date?: string;
  termination_date?: string;
  description?: string;
  citation_text?: string;
  created_by_id: number;
  created_by?: User;
  is_active: boolean;
}

export interface RegulationAgencyRelationship {
  id: number;
  created_at: string;
  updated_at: string;
  regulation_id: number;
  regulation?: LawsAndRules;
  regulation_chunk_id?: number;
  regulation_chunk?: ChunkWithContent;
  agency_id: number;
  agency?: Agency;
  relationship_type: AgencyRelationshipType;
  effective_date?: string;
  termination_date?: string;
  description?: string;
  authority_scope?: string;
  created_by_id: number;
  created_by?: User;
  is_active: boolean;
}

export interface RegulationCategoryRelationship {
  id: number;
  created_at: string;
  updated_at: string;
  regulation_id: number;
  regulation?: LawsAndRules;
  regulation_chunk_id?: number;
  regulation_chunk?: ChunkWithContent;
  category_id: number;
  category?: Category;
  relationship_type: CategoryRelationshipType;
  effective_date?: string;
  termination_date?: string;
  description?: string;
  scope_definition?: string;
  created_by_id: number;
  created_by?: User;
  is_active: boolean;
}

// Request interfaces for creating relationships
export interface CreateRegulationDocumentRelationshipRequest {
  document_id: number;
  regulation_chunk_id?: number;
  relationship_type: DocumentRelationshipType;
  effective_date?: string;
  termination_date?: string;
  description?: string;
  citation_text?: string;
}

export interface CreateRegulationAgencyRelationshipRequest {
  agency_id: number;
  regulation_chunk_id?: number;
  relationship_type: AgencyRelationshipType;
  effective_date?: string;
  termination_date?: string;
  description?: string;
  authority_scope?: string;
}

export interface CreateRegulationCategoryRelationshipRequest {
  category_id: number;
  regulation_chunk_id?: number;
  relationship_type: CategoryRelationshipType;
  effective_date?: string;
  termination_date?: string;
  description?: string;
  scope_definition?: string;
}

// Update request interfaces
export interface UpdateRegulationRelationshipRequest {
  relationship_type?: string;
  effective_date?: string;
  termination_date?: string;
  description?: string;
  citation_text?: string;
  authority_scope?: string;
  scope_definition?: string;
  is_active?: boolean;
}

// Relationship summary interfaces
export interface RegulationRelationshipsSummary {
  regulation_id: number;
  document_relationships: RegulationDocumentRelationship[];
  agency_relationships: RegulationAgencyRelationship[];
  category_relationships: RegulationCategoryRelationship[];
  counts: {
    documents: number;
    agencies: number;
    categories: number;
    total: number;
  };
}

export interface RelationshipStats {
  document_relationships: {
    by_type: Array<{ relationship_type: string; count: number }>;
    total: number;
  };
  agency_relationships: {
    by_type: Array<{ relationship_type: string; count: number }>;
    total: number;
  };
  category_relationships: {
    by_type: Array<{ relationship_type: string; count: number }>;
    total: number;
  };
  overall_total: number;
}

// Summary types
export type SummaryType = 'news' | 'update' | 'announcement' | 'alert';
export type EntityType = 'document' | 'regulation' | 'agency' | 'category';
export type ActionType = 'create' | 'update' | 'delete' | 'publish' | 'archive';

export interface Summary {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  abstract: string;
  summary_type: SummaryType;
  entity_type: EntityType;
  entity_id: number;
  action_type: ActionType;
  publication_date?: string;
  is_public: boolean;
  is_featured: boolean;
  view_count: number;
  priority: number;
  tags?: string;
  external_link?: string;
  created_by_id: number;
  created_by: User;
  agency_id?: number;
  agency?: Agency;
  category_id?: number;
  category?: Category;
}

export interface SummaryFilters {
  entity_type?: string;
  action_type?: string;
  summary_type?: string;
  agency_id?: number;
  category_id?: number;
  is_public?: boolean;
  is_featured?: boolean;
  page?: number;
  per_page?: number;
  sort_by?: string;
  sort_order?: string;
  date_from?: string;
  date_to?: string;
}

// Proceeding types
export interface Proceeding {
  id: number;
  name: string;
  description?: string;
  objective: string;
  status: string;
  priority: string;
  initiation_date: string;
  unique_id: string;
  prp_sections?: string;
  prp_alignment?: string;
  new_prp_elements?: string;
  planned_start_date?: string;
  actual_start_date?: string;
  planned_end_date?: string;
  actual_end_date?: string;
  estimated_duration?: number;
  actual_duration?: number;
  review_required?: boolean;
  review_scheduled?: boolean;
  review_date?: string;
  review_completed?: boolean;
  review_completed_at?: string;
  review_report_id?: number;
  critical_milestones?: string;
  existing_directives_reviewed?: boolean;
  referenced_rules?: string;
  referenced_orders?: string;
  referenced_directives?: string;
  conflict_analysis?: string;
  integration_plan?: string;
  requires_ifr?: boolean;
  ifr_triggered?: boolean;
  ifr_description?: string;
  ifr_document_id?: number;
  initiated_by_id: number;
  owner_id: number;
  owner?: {
    id: number;
    username: string;
    email: string;
  };
  agency_id?: number;
  agency?: {
    id: number;
    name: string;
  };
  category_id?: number;
  total_steps: number;
  completed_steps: number;
  progress_percent: number;
  current_step_order?: number;
  tags?: string;
  is_public: boolean;
  notes?: string;
  attachments?: string;
  external_refs?: string;
  created_at: string;
  updated_at: string;
}

// Task Management Types
export type TaskType = 'review' | 'deadline' | 'hearing' | 'comment' | 'general' | 'reminder' | 'follow_up';
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';
export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface Task {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  description: string;
  type: TaskType;
  status: TaskStatus;
  priority: TaskPriority;
  due_date?: string;
  due_time?: string;
  start_date?: string;
  start_time?: string;
  end_date?: string;
  duration?: number; // Duration in minutes
  is_all_day: boolean;
  time_zone: string;
  is_recurring: boolean;
  recurrence_rule?: string;
  recurrence_pattern?: string; // Alternative name for recurrence_rule
  recurrence_end?: string;
  parent_task_id?: number;
  parent_task?: Task;
  source_type?: string; // "document", "regulation", "manual", "parsed_text"
  source_id?: number;
  source_text?: string;
  parsed_from_text: boolean | string; // Can be boolean or string with source text
  assigned_to_id?: number;
  assigned_to?: User;
  created_by_id: number;
  created_by: User;
  document_id?: number;
  document?: Document;
  regulation_id?: number;
  regulation?: LawsAndRules;
  agency_id?: number;
  agency?: Agency;
  category_id?: number;
  category?: Category;
  reminder_enabled: boolean;
  reminder_time?: string;
  reminder_date?: string; // Legacy field for reminder_time
  reminder_minutes?: number; // Reminder time in minutes before due date
  notification_sent: boolean;
  location?: string;
  url?: string;
  notes?: string;
  tags?: string | string[]; // Can be string or array
  is_public: boolean;
  completed_at?: string;
  completed_by?: number;

  // Performance Evaluation (matching finance performance structure)
  performance_percentage: number;
  deadline_adherence_score: number;
  quality_score: number;
  completion_efficiency: number;
  priority_handling_score: number;
  performance_notes: string;
  evaluation_date?: string;
  is_auto_calculated: boolean;
  evaluated_by_id?: number;
  evaluated_by?: User;
}

export interface TaskFormData {
  title: string;
  description?: string;
  type: TaskType;
  priority: TaskPriority;
  due_date?: string;
  start_date?: string;
  end_date?: string;
  duration?: number;
  is_all_day?: boolean;
  time_zone?: string;
  assigned_to_id?: number;
  document_id?: number;
  regulation_id?: number;
  agency_id?: number;
  category_id?: number;
  location?: string;
  url?: string;
  notes?: string;
  tags?: string | string[]; // Can be string or array
  reminder_date?: string; // Legacy field
  is_public?: boolean;
}

export interface TaskPerformanceHistory {
  id: number;
  task_id: number;
  task?: Task;
  performance_percentage: number;
  deadline_adherence_score: number;
  quality_score: number;
  completion_efficiency: number;
  priority_handling_score: number;
  performance_notes: string;
  evaluation_date: string;
  is_auto_calculated: boolean;
  evaluated_by_id?: number;
  evaluated_by?: User;
  change_reason: string;
  created_at: string;
  updated_at: string;
}

export interface TaskFilters {
  assigned_to_id?: number;
  created_by_id?: number;
  status?: TaskStatus;
  type?: TaskType;
  priority?: TaskPriority;
  document_id?: number;
  regulation_id?: number;
  agency_id?: number;
  category_id?: number;
  parsed_from_text?: boolean;
  is_public?: boolean;
  due_date_from?: string;
  due_date_to?: string;
  search?: string;
  limit?: number;
  offset?: number;
  order_by?: string;
  sort?: string; // For sorting
  page?: number; // For pagination
  per_page?: number; // For pagination
}

// Intelligent Text Parsing Types
export interface ParsedItem {
  type: string; // "task", "agency", "category", "date"
  action: string; // "create", "add", "schedule"
  title: string;
  description: string;
  due_date?: string;
  start_date?: string;
  end_date?: string;
  duration?: number;
  priority: TaskPriority;
  task_type: TaskType;
  source_text: string;
  confidence: number; // 0.0 to 1.0
  metadata: Record<string, any>;
}

export interface APIResult {
  type: string; // "task", "agency", "category"
  action: string; // "created", "updated", "failed", "exists"
  entity_id?: number;
  entity_name: string;
  error?: string;
  source_text: string;
  confidence: number;
  metadata?: any;
}

export interface ProcessingResult {
  parsed_items: ParsedItem[];
  api_results: APIResult[];
  summary: string;
  success: boolean;
  errors?: string[];
}

export interface ParseTextRequest {
  content: string;
  auto_execute: boolean;
  source_type?: string; // "document", "regulation"
  source_id?: number;
}

// Finance System Types

export interface Finance {
  id: number;
  amount: number;
  year: number;
  description: string;
  document_id?: number;
  document?: Document;
  regulation_id?: number;
  regulation?: LawsAndRules;
  budget_type: 'original' | 'actual';
  performance_percentage: number;
  is_auto_calculated: boolean;
  source_finance_id?: number;
  source_finance?: Finance;
  category_id?: number;
  category?: FinanceCategory;
  created_at: string;
  updated_at: string;
}

export interface FinancePerformance {
  id: number;
  document_id?: number;
  document?: Document;
  regulation_id?: number;
  regulation?: LawsAndRules;
  year: number;
  performance_percentage: number;
  performance_notes: string;
  evaluation_date: string;
  evaluated_by_id?: number;
  evaluated_by?: User;
  created_at: string;
  updated_at: string;
}

export interface FinanceCategory {
  id: number;
  name: string;
  description: string;
  color: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateFinanceRequest {
  amount: number;
  year: number;
  description: string;
  document_id?: number;
  regulation_id?: number;
  budget_type?: 'original' | 'actual';
  performance_percentage?: number;
  category_id?: number;
}

export interface UpdateFinanceRequest {
  amount?: number;
  year?: number;
  description?: string;
  document_id?: number;
  regulation_id?: number;
  budget_type?: 'original' | 'actual';
  performance_percentage?: number;
  category_id?: number;
}

export interface CreatePerformanceRequest {
  document_id?: number;
  regulation_id?: number;
  year: number;
  performance_percentage: number;
  performance_notes: string;
  evaluated_by_id?: number;
}

export interface CreateFinanceCategoryRequest {
  name: string;
  description: string;
  color?: string;
}

export interface FinanceFilters {
  year?: number;
  document_id?: number;
  regulation_id?: number;
  budget_type?: 'original' | 'actual';
  category_id?: number;
}

export interface BudgetSummary {
  year: number;
  original_total: number;
  actual_total: number;
  efficiency_percentage: number;
  savings: number;
}

export interface FinanceTableData {
  id: number;
  title: string;
  type: 'document' | 'regulation';
  entity_id: number;
  original_budget: number;
  actual_budget: number;
  performance_percentage: number;
  category: string;
  category_color: string;
  year: number;
}

// Additional interfaces for missing types
export interface ProceedingFilters {
  status?: string;
  priority?: string;
  agency_id?: number;
  category_id?: number;
  search?: string;
  sort?: string;
  order?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

export interface ProceedingFormData {
  name: string;
  description?: string;
  objective: string;
  status: string;
  priority: string;
  initiation_date: string;
  prp_alignment?: string;
  primary_steps?: any[];
  [key: string]: any;
}

export interface ProceedingStep {
  id: number;
  name: string;
  description?: string;
  order: number;
  status: string;
  completed: boolean;
  proceeding_id: number;
}

export interface ProceedingStepManagerProps {
  proceedingId: number;
  steps: ProceedingStep[];
  onUpdateStepStatus: (stepId: number, status: string) => void;
  onRefresh: () => void;
}

export interface DashboardStats {
  documents?: {
    total: number;
    published: number;
    draft: number;
  };
  agencies?: {
    total: number;
    active: number;
  };
  categories?: {
    total: number;
  };
  proceedings?: {
    total: number;
    active: number;
    completed: number;
  };
}

export interface SummaryItem {
  id: number;
  title: string;
  abstract: string;
  content?: string; // For compatibility
  is_important: boolean;
  is_public: boolean;
  is_featured: boolean;
  summary_type: SummaryType;
  entity_type: EntityType;
  entity_id: number;
  action_type: ActionType;
  publication_date?: string;
  view_count: number;
  priority: number;
  tags?: string;
  external_link?: string;
  created_by_id: number;
  created_by: User;
  agency_id?: number;
  agency?: Agency;
  category_id?: number;
  category?: Category;
  created_at: string;
  updated_at: string;
  [key: string]: any;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  updateUser?: (user: User) => void;
}
