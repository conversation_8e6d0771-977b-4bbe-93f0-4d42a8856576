package tests

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"federal-register-clone/internal/api/handlers"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// setupTestDB creates an in-memory SQLite database for testing
func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect to test database")
	}

	// Auto-migrate all models
	err = db.AutoMigrate(
		&models.User{},
		&models.Agency{},
		&models.Category{},
		&models.Document{},
		&models.LawsAndRules{},
		&models.Task{},
		&models.Finance{},
		&models.FinanceCategory{},
		&models.FinancePerformance{},
		&models.Summary{},
		&models.RegulationDocumentRelationship{},
		&models.AutoGenerationConfig{},
		&models.SystemEvent{},
		&models.EntityRelationship{},
	)
	if err != nil {
		panic("failed to migrate test database")
	}

	return db
}

// createTestData creates sample data for testing
func createTestData(db *gorm.DB) (uint, uint, uint, uint) {
	// Create test user
	user := models.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "hashedpassword",
		IsActive: true,
	}
	db.Create(&user)

	// Create test agency
	agency := models.Agency{
		Name:        "Test Agency",
		Description: "A test agency for unified system testing",
		Website:     "https://test.gov",
		IsActive:    true,
	}
	db.Create(&agency)

	// Create test category
	category := models.Category{
		Name:        "Test Category",
		Description: "A test category for unified system testing",
		Color:       "#3B82F6",
		IsActive:    true,
	}
	db.Create(&category)

	// Create test document
	now := time.Now()
	effectiveDate := now.AddDate(0, 1, 0) // 1 month from now
	document := models.Document{
		Title:           "Test Document for Unified System",
		Content:         "This document contains important deadlines. The implementation must be completed by January 15, 2025. The estimated cost is $50,000 for this project.",
		Type:            models.DocumentTypeRule,
		Status:          models.DocumentStatusDraft,
		AgencyID:        agency.ID,
		CreatedByID:     user.ID,
		UpdatedByID:     user.ID,
		PublicationDate: &now,
		EffectiveDate:   &effectiveDate,
		SignificantRule: true,
		AcceptsComments: true,
		IsPublic:        true,
		Abstract:        "Test document abstract",
		EconomicImpact:  "Significant economic impact expected",
	}
	db.Create(&document)

	// Create test regulation
	regulation := models.LawsAndRules{
		Title:         "Test Regulation for Unified System",
		Description:   "This regulation requires compliance within 90 days. The implementation cost is estimated at $75,000.",
		AgencyID:      agency.ID,
		CreatedByID:   user.ID,
		EffectiveDate: &effectiveDate,
		IsSignificant: true,
	}
	db.Create(&regulation)

	return user.ID, agency.ID, document.ID, regulation.ID
}

// TestUnifiedSystemIntegration tests the complete unified system workflow
func TestUnifiedSystemIntegration(t *testing.T) {
	// Setup test database
	db := setupTestDB()
	database.SetDB(db)

	// Create test data
	userID, agencyID, documentID, regulationID := createTestData(db)

	// Test auto-generation service
	t.Run("AutoGenerationService", func(t *testing.T) {
		autoGenService := services.NewAutoGenerationService(db)
		
		// Get test document
		var document models.Document
		err := db.Preload("Agency").Preload("Categories").First(&document, documentID).Error
		require.NoError(t, err)

		// Test document auto-generation
		config := services.DefaultConfig()
		err = autoGenService.ProcessDocumentCreation(&document, userID, config)
		assert.NoError(t, err)

		// Verify summaries were created
		var summaryCount int64
		db.Model(&models.Summary{}).Where("entity_type = ? AND entity_id = ?", "document", documentID).Count(&summaryCount)
		assert.Greater(t, summaryCount, int64(0), "Summary should be auto-generated")

		// Verify tasks were created
		var taskCount int64
		db.Model(&models.Task{}).Where("document_id = ?", documentID).Count(&taskCount)
		assert.Greater(t, taskCount, int64(0), "Tasks should be auto-generated")

		// Verify finance records were created
		var financeCount int64
		db.Model(&models.Finance{}).Where("document_id = ?", documentID).Count(&financeCount)
		assert.Greater(t, financeCount, int64(0), "Finance records should be auto-generated")
	})

	// Test relationship service
	t.Run("RelationshipService", func(t *testing.T) {
		relationshipService := services.NewRelationshipService(db)

		// Create document-regulation relationship
		err := relationshipService.CreateDocumentRegulationRelationship(
			documentID, regulationID, models.DocumentRelationshipTypeImplements, userID, "Test relationship")
		assert.NoError(t, err)

		// Verify relationship was created
		var relationshipCount int64
		db.Model(&models.RegulationDocumentRelationship{}).
			Where("document_id = ? AND regulation_id = ? AND is_active = ?", documentID, regulationID, true).
			Count(&relationshipCount)
		assert.Equal(t, int64(1), relationshipCount, "Relationship should be created")

		// Test auto-relationship generation
		err = relationshipService.AutoCreateRelationships("document", documentID, userID)
		assert.NoError(t, err)
	})

	// Test NLP service
	t.Run("NLPService", func(t *testing.T) {
		nlpService := services.NewNLPService()

		testContent := "The deadline for compliance is January 15, 2025. The estimated cost is $50,000."

		// Test deadline extraction
		deadlines := nlpService.ExtractDeadlines(testContent)
		assert.Greater(t, len(deadlines), 0, "Should extract deadlines from content")

		// Test cost extraction
		costs := nlpService.ExtractCosts(testContent)
		assert.Greater(t, len(costs), 0, "Should extract costs from content")
		if len(costs) > 0 {
			assert.Equal(t, 50000.0, costs[0].Amount, "Should extract correct cost amount")
		}
	})

	// Test finance service
	t.Run("FinanceService", func(t *testing.T) {
		financeService := services.NewFinanceService(db)

		// Get test document
		var document models.Document
		err := db.Preload("Agency").Preload("Categories").First(&document, documentID).Error
		require.NoError(t, err)

		// Test auto-generation from document
		err = financeService.AutoGenerateFinanceFromDocument(&document, userID)
		assert.NoError(t, err)

		// Verify finance records were created
		var financeCount int64
		db.Model(&models.Finance{}).Where("document_id = ? AND is_auto_calculated = ?", documentID, true).Count(&financeCount)
		assert.Greater(t, financeCount, int64(0), "Auto-generated finance records should exist")
	})

	// Test task service
	t.Run("TaskService", func(t *testing.T) {
		taskService := services.NewTaskService(db)

		// Get test document
		var document models.Document
		err := db.Preload("Agency").Preload("Categories").First(&document, documentID).Error
		require.NoError(t, err)

		// Test auto-generation from document
		err = taskService.AutoGenerateTasksFromDocument(&document, userID)
		assert.NoError(t, err)

		// Verify tasks were created
		var taskCount int64
		db.Model(&models.Task{}).Where("document_id = ? AND parsed_from_text = ?", documentID, true).Count(&taskCount)
		assert.Greater(t, taskCount, int64(0), "Auto-generated tasks should exist")
	})

	// Test event service
	t.Run("EventService", func(t *testing.T) {
		eventService := services.NewEventService(db)
		eventService.SetupDefaultListeners()

		// Emit test event
		err := eventService.EmitEvent("document.created", map[string]interface{}{
			"document_id": documentID,
			"user_id":     userID,
			"timestamp":   time.Now(),
		})
		assert.NoError(t, err)

		// Verify event was stored
		var eventCount int64
		db.Model(&models.SystemEvent{}).Where("event_type = ?", "document.created").Count(&eventCount)
		assert.Greater(t, eventCount, int64(0), "System event should be stored")
	})
}

// TestAPIEndpoints tests the API endpoints for the unified system
func TestAPIEndpoints(t *testing.T) {
	// Setup test database
	db := setupTestDB()
	database.SetDB(db)

	// Create test data
	userID, _, documentID, regulationID := createTestData(db)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add middleware to set user ID
	router.Use(func(c *gin.Context) {
		c.Set("user_id", userID)
		c.Next()
	})

	// Setup routes
	router.GET("/api/dashboard/stats", handlers.GetDashboardStats)
	router.GET("/api/dashboard/relationships", handlers.GetDashboardRelationships)
	router.GET("/api/dashboard/activity", handlers.GetDashboardActivity)
	router.GET("/api/relationships/:entityType/:entityId", handlers.GetEntityRelationships)
	router.POST("/api/relationships", handlers.CreateRelationship)
	router.GET("/api/auto-generation/configs", handlers.GetAutoGenerationConfigs)
	router.POST("/api/auto-generation/configs", handlers.UpdateAutoGenerationConfigs)

	// Test dashboard stats endpoint
	t.Run("DashboardStats", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/dashboard/stats", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "documents")
		assert.Contains(t, data, "regulations")
		assert.Contains(t, data, "tasks")
		assert.Contains(t, data, "finances")
	})

	// Test relationship creation endpoint
	t.Run("CreateRelationship", func(t *testing.T) {
		payload := map[string]interface{}{
			"source_entity_type": "document",
			"source_entity_id":   documentID,
			"target_entity_type": "regulation",
			"target_entity_id":   regulationID,
			"relationship_type":  "implements",
			"description":        "Test API relationship",
		}

		payloadBytes, _ := json.Marshal(payload)
		req, _ := http.NewRequest("POST", "/api/relationships", strings.NewReader(string(payloadBytes)))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
	})

	// Test auto-generation config endpoint
	t.Run("AutoGenerationConfigs", func(t *testing.T) {
		// Test GET configs
		req, _ := http.NewRequest("GET", "/api/auto-generation/configs", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		// Test POST configs
		payload := map[string]interface{}{
			"configs": []map[string]interface{}{
				{
					"entity_type":                    "document",
					"enable_summary_generation":     true,
					"enable_task_generation":        true,
					"enable_calendar_generation":    true,
					"enable_finance_generation":     true,
					"enable_relationship_generation": true,
				},
			},
		}

		payloadBytes, _ := json.Marshal(payload)
		req, _ = http.NewRequest("POST", "/api/auto-generation/configs", strings.NewReader(string(payloadBytes)))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})
}

// TestPerformanceCalculation tests the performance-based finance calculation
func TestPerformanceCalculation(t *testing.T) {
	db := setupTestDB()
	database.SetDB(db)

	userID, _, documentID, _ := createTestData(db)

	// Create a finance record
	finance := models.Finance{
		Amount:      100000.0,
		Year:        2025,
		Description: "Test budget",
		DocumentID:  &documentID,
		BudgetType:  "original",
	}
	db.Create(&finance)

	// Create a task related to the document
	dueDate := time.Now().AddDate(0, 0, 7) // 7 days from now
	task := models.Task{
		Title:       "Test Task",
		Description: "Test task for performance calculation",
		Type:        models.TaskTypeDeadline,
		Status:      models.TaskStatusPending,
		Priority:    models.TaskPriorityHigh,
		DueDate:     &dueDate,
		DocumentID:  &documentID,
		CreatedByID: userID,
	}
	db.Create(&task)

	// Test task completion and performance update
	financeService := services.NewFinanceService(db)
	taskService := services.NewTaskService(db)

	// Mark task as complete
	err := taskService.MarkTaskComplete(task.ID, userID)
	assert.NoError(t, err)

	// Verify performance was updated
	var performance models.FinancePerformance
	err = db.Where("document_id = ?", documentID).First(&performance).Error
	assert.NoError(t, err)
	assert.Greater(t, performance.PerformancePercentage, 100.0, "Performance should increase after task completion")

	// Verify actual finance record was created
	var actualFinance models.Finance
	err = db.Where("source_finance_id = ? AND budget_type = ?", finance.ID, "actual").First(&actualFinance).Error
	assert.NoError(t, err)
	assert.NotEqual(t, finance.Amount, actualFinance.Amount, "Actual amount should be different from original")
}

// BenchmarkAutoGeneration benchmarks the auto-generation performance
func BenchmarkAutoGeneration(b *testing.B) {
	db := setupTestDB()
	database.SetDB(db)

	userID, _, _, _ := createTestData(db)
	autoGenService := services.NewAutoGenerationService(db)
	config := services.DefaultConfig()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Create a new document for each iteration
		document := models.Document{
			Title:       fmt.Sprintf("Benchmark Document %d", i),
			Content:     "This document has a deadline of January 15, 2025 and costs $10,000.",
			Type:        models.DocumentTypeRule,
			Status:      models.DocumentStatusDraft,
			AgencyID:    1,
			CreatedByID: userID,
		}
		db.Create(&document)

		// Benchmark auto-generation
		autoGenService.ProcessDocumentCreation(&document, userID, config)
	}
}
