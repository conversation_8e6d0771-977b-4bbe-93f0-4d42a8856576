import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginRequest, RegisterRequest } from '../types';
import apiService from '../services/api';

// Token validation helper
function validateTokenFormat(token: string): boolean {
  if (!token || typeof token !== 'string') return false;

  // JWT should have 3 parts separated by dots
  const parts = token.split('.');
  if (parts.length !== 3) return false;

  // Each part should be non-empty
  if (parts.some(part => part.length === 0)) return false;

  try {
    // Try to decode the payload to check expiration
    const payload = JSON.parse(atob(parts[1]));

    // Check if token is expired
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  getCurrentUser: () => Promise<void>;
  initializeAuth: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
  updateUser: (user: User) => void;
  clearAuthState: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await apiService.login(credentials);
          
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: (error as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Login failed',
          });
          throw error;
        }
      },

      register: async (userData: RegisterRequest) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await apiService.register(userData);
          
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: (error as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Registration failed',
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          await apiService.logout();
        } catch (error) {
          // Continue with logout even if API call fails
          console.error('Logout API call failed:', error);
        } finally {
          // Clear all authentication data
          if (typeof window !== 'undefined') {
            localStorage.removeItem(process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token');
            localStorage.removeItem(process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token');
            localStorage.removeItem('auth-storage'); // Clear zustand persistence

            // Clear cookies
            document.cookie = `${process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token'}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
            document.cookie = `${process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token'}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
          }

          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      getCurrentUser: async () => {
        try {
          if (!apiService.isAuthenticated()) {
            set({ isAuthenticated: false, user: null });
            return;
          }

          set({ isLoading: true });

          const userResponse = await apiService.getCurrentUser();

          set({
            user: userResponse.data,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: (error as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to get user info',
          });
        }
      },

      initializeAuth: async () => {
        try {
          if (typeof window === 'undefined') return;

          set({ isLoading: true });

          const token = localStorage.getItem(process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token');
          if (!token) {
            set({ isAuthenticated: false, user: null, isLoading: false });
            return;
          }

          // Validate token format before making API call
          if (!validateTokenFormat(token)) {
            // Clear invalid token
            localStorage.removeItem(process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token');
            localStorage.removeItem(process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token');
            document.cookie = `${process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token'}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
            set({ isAuthenticated: false, user: null, isLoading: false });
            return;
          }

          // If we have a valid token, try to get current user
          const userResponse = await apiService.getCurrentUser();
          set({
            user: userResponse.data,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          // If token is invalid, clear it completely
          if (typeof window !== 'undefined') {
            localStorage.removeItem(process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token');
            localStorage.removeItem(process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token');
            // Clear cookies too
            document.cookie = `${process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token'}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
            document.cookie = `${process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token'}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
          }
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      updateUser: (user: User) => {
        set({ user });
      },

      clearAuthState: () => {
        if (typeof window !== 'undefined') {
          localStorage.removeItem(process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token');
          localStorage.removeItem(process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token');
          localStorage.removeItem('auth-storage');

          // Clear cookies
          document.cookie = `${process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token'}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
          document.cookie = `${process.env.NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY || 'federal_register_refresh_token'}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
        }

        set({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        // Don't persist isAuthenticated - it should be determined dynamically
      }),
    }
  )
);

// Export both named and default for compatibility
export default useAuthStore;
export const authStore = useAuthStore;
