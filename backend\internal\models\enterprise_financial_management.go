package models

import (
	"time"

	"gorm.io/gorm"
)

// AccountType represents different types of financial accounts
type AccountType string

const (
	AccountTypeAsset      AccountType = "asset"
	AccountTypeLiability  AccountType = "liability"
	AccountTypeEquity     AccountType = "equity"
	AccountTypeRevenue    AccountType = "revenue"
	AccountTypeExpense    AccountType = "expense"
)

// TransactionType represents different types of financial transactions
type TransactionType string

const (
	TransactionTypeDebit   TransactionType = "debit"
	TransactionTypeCredit  TransactionType = "credit"
	TransactionTypeTransfer TransactionType = "transfer"
)

// ChartOfAccounts represents the enterprise chart of accounts
type ChartOfAccounts struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Account identification
	AccountCode   string      `json:"account_code" gorm:"uniqueIndex;not null"`
	AccountName   string      `json:"account_name" gorm:"not null"`
	AccountType   AccountType `json:"account_type" gorm:"not null"`
	Description   string      `json:"description" gorm:"type:text"`
	
	// Account hierarchy
	ParentAccountID *uint             `json:"parent_account_id"`
	ParentAccount   *ChartOfAccounts  `json:"parent_account,omitempty" gorm:"foreignKey:ParentAccountID"`
	Level           int               `json:"level" gorm:"default:0"`
	
	// Account properties
	IsActive        bool    `json:"is_active" gorm:"default:true"`
	IsControlAccount bool   `json:"is_control_account" gorm:"default:false"`
	AllowPosting    bool    `json:"allow_posting" gorm:"default:true"`
	
	// Financial properties
	NormalBalance   TransactionType `json:"normal_balance" gorm:"not null"`
	CurrentBalance  float64         `json:"current_balance" gorm:"default:0"`
	OpeningBalance  float64         `json:"opening_balance" gorm:"default:0"`
	
	// Currency and localization
	CurrencyCode    string `json:"currency_code" gorm:"default:'USD'"`
	TaxCode         string `json:"tax_code"`
	
	// Compliance and reporting
	ReportingCode   string `json:"reporting_code"`                               // For regulatory reporting
	GLClass         string `json:"gl_class"`                                     // General ledger classification
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// GeneralLedger represents general ledger entries
type GeneralLedger struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Entry identification
	EntryNumber     string    `json:"entry_number" gorm:"uniqueIndex;not null"`
	TransactionDate time.Time `json:"transaction_date" gorm:"not null"`
	PostingDate     time.Time `json:"posting_date" gorm:"not null"`
	
	// Account reference
	AccountID       uint            `json:"account_id" gorm:"not null"`
	Account         ChartOfAccounts `json:"account" gorm:"foreignKey:AccountID"`
	
	// Transaction details
	TransactionType TransactionType `json:"transaction_type" gorm:"not null"`
	DebitAmount     float64         `json:"debit_amount" gorm:"default:0"`
	CreditAmount    float64         `json:"credit_amount" gorm:"default:0"`
	
	// Reference information
	ReferenceType   string `json:"reference_type"`                              // "invoice", "payment", "journal"
	ReferenceID     string `json:"reference_id"`
	Description     string `json:"description" gorm:"type:text"`
	
	// Document linkage
	DocumentID      *uint     `json:"document_id"`
	Document        *Document `json:"document,omitempty" gorm:"foreignKey:DocumentID"`
	
	// User and approval
	CreatedByID     uint `json:"created_by_id" gorm:"not null"`
	CreatedBy       User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	ApprovedByID    *uint `json:"approved_by_id"`
	ApprovedBy      *User `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`
	ApprovedAt      *time.Time `json:"approved_at"`
	
	// Status and reconciliation
	Status          string     `json:"status" gorm:"default:'pending'"`          // "pending", "posted", "reversed"
	IsReconciled    bool       `json:"is_reconciled" gorm:"default:false"`
	ReconciledAt    *time.Time `json:"reconciled_at"`
	
	// Currency and exchange
	CurrencyCode    string  `json:"currency_code" gorm:"default:'USD'"`
	ExchangeRate    float64 `json:"exchange_rate" gorm:"default:1.0"`
	BaseCurrencyAmount float64 `json:"base_currency_amount"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// BudgetPlan represents budget planning and management
type BudgetPlan struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Budget identification
	BudgetCode      string    `json:"budget_code" gorm:"uniqueIndex;not null"`
	BudgetName      string    `json:"budget_name" gorm:"not null"`
	Description     string    `json:"description" gorm:"type:text"`
	
	// Budget period
	FiscalYear      int       `json:"fiscal_year" gorm:"not null"`
	StartDate       time.Time `json:"start_date" gorm:"not null"`
	EndDate         time.Time `json:"end_date" gorm:"not null"`
	
	// Budget type and category
	BudgetType      string `json:"budget_type" gorm:"default:'operational'"`     // "operational", "capital", "project"
	BudgetCategory  string `json:"budget_category"`                              // "revenue", "expense", "investment"
	
	// Budget amounts
	PlannedAmount   float64 `json:"planned_amount" gorm:"not null"`
	RevisedAmount   float64 `json:"revised_amount"`
	ActualAmount    float64 `json:"actual_amount" gorm:"default:0"`
	CommittedAmount float64 `json:"committed_amount" gorm:"default:0"`
	
	// Variance analysis
	VarianceAmount  float64 `json:"variance_amount" gorm:"default:0"`
	VariancePercent float64 `json:"variance_percent" gorm:"default:0"`
	
	// Account linkage
	AccountID       uint            `json:"account_id" gorm:"not null"`
	Account         ChartOfAccounts `json:"account" gorm:"foreignKey:AccountID"`
	
	// Organizational structure
	DepartmentID    *uint       `json:"department_id"`
	CostCenterID    *uint       `json:"cost_center_id"`
	ProjectID       *uint       `json:"project_id"`
	
	// Approval workflow
	Status          string     `json:"status" gorm:"default:'draft'"`            // "draft", "submitted", "approved", "active"
	SubmittedAt     *time.Time `json:"submitted_at"`
	ApprovedByID    *uint      `json:"approved_by_id"`
	ApprovedBy      *User      `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`
	ApprovedAt      *time.Time `json:"approved_at"`
	
	// Monitoring and alerts
	AlertThreshold  float64 `json:"alert_threshold" gorm:"default:90"`           // Percentage threshold for alerts
	IsAlertEnabled  bool    `json:"is_alert_enabled" gorm:"default:true"`
	LastAlertSent   *time.Time `json:"last_alert_sent"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// CostCenter represents cost center management
type CostCenter struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Cost center identification
	CostCenterCode string `json:"cost_center_code" gorm:"uniqueIndex;not null"`
	Name           string `json:"name" gorm:"not null"`
	Description    string `json:"description" gorm:"type:text"`
	
	// Organizational hierarchy
	ParentCostCenterID *uint       `json:"parent_cost_center_id"`
	ParentCostCenter   *CostCenter `json:"parent_cost_center,omitempty" gorm:"foreignKey:ParentCostCenterID"`
	Level              int         `json:"level" gorm:"default:0"`
	
	// Management
	ManagerID       *uint `json:"manager_id"`
	Manager         *User `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`
	
	// Financial properties
	BudgetAmount    float64 `json:"budget_amount" gorm:"default:0"`
	ActualAmount    float64 `json:"actual_amount" gorm:"default:0"`
	CommittedAmount float64 `json:"committed_amount" gorm:"default:0"`
	
	// Cost allocation
	AllocationMethod string  `json:"allocation_method" gorm:"default:'direct'"`  // "direct", "activity", "percentage"
	AllocationBase   string  `json:"allocation_base"`                           // "headcount", "area", "usage"
	AllocationRate   float64 `json:"allocation_rate" gorm:"default:0"`
	
	// Status and monitoring
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	LastReviewed    *time.Time `json:"last_reviewed"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// FinancialReport represents financial reporting
type FinancialReport struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Report identification
	ReportCode      string    `json:"report_code" gorm:"uniqueIndex;not null"`
	ReportName      string    `json:"report_name" gorm:"not null"`
	ReportType      string    `json:"report_type" gorm:"not null"`              // "balance_sheet", "income_statement", "cash_flow"
	Description     string    `json:"description" gorm:"type:text"`
	
	// Report period
	PeriodType      string    `json:"period_type" gorm:"not null"`              // "monthly", "quarterly", "yearly"
	StartDate       time.Time `json:"start_date" gorm:"not null"`
	EndDate         time.Time `json:"end_date" gorm:"not null"`
	
	// Report generation
	GeneratedAt     *time.Time `json:"generated_at"`
	GeneratedByID   *uint      `json:"generated_by_id"`
	GeneratedBy     *User      `json:"generated_by,omitempty" gorm:"foreignKey:GeneratedByID"`
	
	// Report content
	ReportData      string `json:"report_data" gorm:"type:text"`                // JSON report data
	ReportFormat    string `json:"report_format" gorm:"default:'json'"`         // "json", "pdf", "excel"
	FilePath        string `json:"file_path"`
	
	// Status and approval
	Status          string     `json:"status" gorm:"default:'draft'"`           // "draft", "generated", "reviewed", "approved"
	ReviewedByID    *uint      `json:"reviewed_by_id"`
	ReviewedBy      *User      `json:"reviewed_by,omitempty" gorm:"foreignKey:ReviewedByID"`
	ReviewedAt      *time.Time `json:"reviewed_at"`
	
	// Distribution
	DistributionList string `json:"distribution_list" gorm:"type:text"`         // JSON list of recipients
	PublishedAt      *time.Time `json:"published_at"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// TableName returns the table name for ChartOfAccounts model
func (ChartOfAccounts) TableName() string {
	return "chart_of_accounts"
}

// TableName returns the table name for GeneralLedger model
func (GeneralLedger) TableName() string {
	return "general_ledger"
}

// TableName returns the table name for BudgetPlan model
func (BudgetPlan) TableName() string {
	return "budget_plans"
}

// TableName returns the table name for CostCenter model
func (CostCenter) TableName() string {
	return "cost_centers"
}

// TableName returns the table name for FinancialReport model
func (FinancialReport) TableName() string {
	return "financial_reports"
}
