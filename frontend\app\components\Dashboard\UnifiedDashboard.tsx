'use client';

import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  ScaleIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClipboardDocumentListIcon,
  BuildingOfficeIcon,
  TagIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import apiService from '../../services/api';

interface DashboardStats {
  documents: number;
  regulations: number;
  tasks: number;
  finances: number;
  summaries: number;
  agencies: number;
  categories: number;
  overdue_tasks: number;
  total_budget: number;
  performance_avg: number;
}

interface RelationshipData {
  entity_type: string;
  entity_id: number;
  entity_title: string;
  related_count: number;
  relationships: Array<{
    type: string;
    target_type: string;
    target_id: number;
    target_title: string;
  }>;
}

interface AutoGenerationActivity {
  id: number;
  event_type: string;
  entity_type: string;
  entity_id: number;
  created_at: string;
  generated_items: Array<{
    type: string;
    id: number;
    title: string;
  }>;
}

const UnifiedDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [relationships, setRelationships] = useState<RelationshipData[]>([]);
  const [recentActivity, setRecentActivity] = useState<AutoGenerationActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard statistics
      const statsResponse = await apiService.get('/api/dashboard/stats') as any;
      setStats(statsResponse.data);

      // Fetch relationship data
      const relationshipsResponse = await apiService.get('/api/dashboard/relationships') as any;
      setRelationships(relationshipsResponse.data);

      // Fetch recent auto-generation activity
      const activityResponse = await apiService.get('/api/dashboard/activity') as any;
      setRecentActivity(activityResponse.data);

    } catch (err) {
      setError('Failed to load dashboard data');
      console.error('Dashboard error:', err);
    } finally {
      setLoading(false);
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: number | string;
    icon: React.ReactNode;
    color: string;
    trend?: number;
  }> = ({ title, value, icon, color, trend }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center">
        <div className={`flex-shrink-0 p-3 rounded-md ${color}`}>
          {icon}
        </div>
        <div className="ml-4 flex-1">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <div className="flex items-center">
            <p className="text-2xl font-semibold text-gray-900">{value}</p>
            {trend !== undefined && (
              <span className={`ml-2 text-sm ${trend >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {trend >= 0 ? '+' : ''}{trend}%
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const RelationshipCard: React.FC<{ data: RelationshipData }> = ({ data }) => (
    <div className="bg-white rounded-lg shadow p-4">
      <h4 className="font-medium text-gray-900 mb-2">{data.entity_title}</h4>
      <p className="text-sm text-gray-500 mb-3">
        {data.entity_type} • {data.related_count} connections
      </p>
      <div className="space-y-1">
        {data.relationships.slice(0, 3).map((rel, index) => (
          <div key={index} className="text-xs text-gray-600 flex items-center">
            <span className="w-2 h-2 bg-blue-400 rounded-full mr-2"></span>
            {rel.type} → {rel.target_type}: {rel.target_title}
          </div>
        ))}
        {data.relationships.length > 3 && (
          <p className="text-xs text-gray-400">
            +{data.relationships.length - 3} more connections
          </p>
        )}
      </div>
    </div>
  );

  const ActivityItem: React.FC<{ activity: AutoGenerationActivity }> = ({ activity }) => (
    <div className="flex items-start space-x-3 py-3">
      <div className="flex-shrink-0">
        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
          <ArrowTrendingUpIcon className="w-4 h-4 text-green-600" />
        </div>
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm text-gray-900">
          Auto-generated {activity.generated_items.length} items from {activity.entity_type}
        </p>
        <div className="mt-1 space-y-1">
          {activity.generated_items.map((item, index) => (
            <p key={index} className="text-xs text-gray-500">
              • {item.type}: {item.title}
            </p>
          ))}
        </div>
        <p className="text-xs text-gray-400 mt-1">
          {new Date(activity.created_at).toLocaleString()}
        </p>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <p className="text-sm text-red-700 mt-1">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <h1 className="text-2xl font-bold text-gray-900">Unified System Dashboard</h1>
        <p className="text-gray-600 mt-1">
          Overview of all interconnected entities and auto-generated content
        </p>
      </div>

      {/* Statistics Grid */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Documents"
            value={stats.documents}
            icon={<DocumentTextIcon className="w-6 h-6 text-white" />}
            color="bg-blue-500"
          />
          <StatCard
            title="Regulations"
            value={stats.regulations}
            icon={<ScaleIcon className="w-6 h-6 text-white" />}
            color="bg-green-500"
          />
          <StatCard
            title="Active Tasks"
            value={stats.tasks}
            icon={<ClipboardDocumentListIcon className="w-6 h-6 text-white" />}
            color="bg-yellow-500"
          />
          <StatCard
            title="Total Budget"
            value={`$${stats.total_budget.toLocaleString()}`}
            icon={<CurrencyDollarIcon className="w-6 h-6 text-white" />}
            color="bg-purple-500"
          />
        </div>
      )}

      {/* Performance Metrics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <StatCard
            title="Overdue Tasks"
            value={stats.overdue_tasks}
            icon={<ExclamationTriangleIcon className="w-6 h-6 text-white" />}
            color={stats.overdue_tasks > 0 ? "bg-red-500" : "bg-green-500"}
          />
          <StatCard
            title="Avg Performance"
            value={`${stats.performance_avg.toFixed(1)}%`}
            icon={<ChartBarIcon className="w-6 h-6 text-white" />}
            color="bg-indigo-500"
          />
          <StatCard
            title="Auto-Generated"
            value={stats.summaries}
            icon={<ArrowTrendingUpIcon className="w-6 h-6 text-white" />}
            color="bg-teal-500"
          />
        </div>
      )}

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Entity Relationships */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Entity Relationships
          </h2>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {relationships.map((rel, index) => (
              <RelationshipCard key={index} data={rel} />
            ))}
            {relationships.length === 0 && (
              <p className="text-gray-500 text-center py-8">
                No relationship data available
              </p>
            )}
          </div>
        </div>

        {/* Recent Auto-Generation Activity */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Recent Auto-Generation Activity
          </h2>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {recentActivity.map((activity) => (
              <ActivityItem key={activity.id} activity={activity} />
            ))}
            {recentActivity.length === 0 && (
              <p className="text-gray-500 text-center py-8">
                No recent activity
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <DocumentTextIcon className="w-8 h-8 text-blue-500 mb-2" />
            <span className="text-sm font-medium">New Document</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <ScaleIcon className="w-8 h-8 text-green-500 mb-2" />
            <span className="text-sm font-medium">New Regulation</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <ClipboardDocumentListIcon className="w-8 h-8 text-yellow-500 mb-2" />
            <span className="text-sm font-medium">New Task</span>
          </button>
          <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
            <ChartBarIcon className="w-8 h-8 text-purple-500 mb-2" />
            <span className="text-sm font-medium">View Analytics</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default UnifiedDashboard;
