'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { biApi } from '../../../services/enterpriseApi';

interface Dashboard {
  id: number;
  created_at: string;
  updated_at: string;
  dashboard_code: string;
  dashboard_name: string;
  description?: string;
  category: string;
  dashboard_type: string;
  layout_config?: string;
  refresh_interval: number;
  is_public: boolean;
  is_active: boolean;
  owner_id: number;
  shared_with?: string;
  tags?: string;
  metadata?: string;
}

const DashboardsListPage: React.FC = () => {
  const router = useRouter();
  const [dashboards, setDashboards] = useState<Dashboard[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');

  useEffect(() => {
    fetchDashboards();
  }, []);

  const fetchDashboards = async () => {
    try {
      setLoading(true);
      const response = await biApi.getDashboards({
        search: searchTerm,
        category: filterCategory || undefined,
      });
      setDashboards(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch dashboards');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this dashboard?')) return;
    
    try {
      await biApi.deleteDashboard(id);
      await fetchDashboards(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to delete dashboard');
    }
  };

  const filteredDashboards = dashboards.filter(dashboard =>
    dashboard.dashboard_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dashboard.dashboard_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) return <div className="p-6">Loading dashboards...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">BI Dashboards</h1>
        <button
          onClick={() => router.push('/enterprise/bi/dashboards/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Create New Dashboard
        </button>
      </div>

      {/* Search and Filter */}
      <div className="mb-6 flex gap-4">
        <input
          type="text"
          placeholder="Search dashboards..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border rounded px-3 py-2 flex-1"
        />
        <select
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          className="border rounded px-3 py-2"
        >
          <option value="">All Categories</option>
          <option value="financial">Financial</option>
          <option value="operational">Operational</option>
          <option value="sales">Sales</option>
          <option value="hr">HR</option>
          <option value="compliance">Compliance</option>
        </select>
        <button
          onClick={fetchDashboards}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Search
        </button>
      </div>

      {/* Dashboards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredDashboards.map((dashboard) => (
          <div key={dashboard.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{dashboard.dashboard_name}</h3>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  dashboard.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {dashboard.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">Code: {dashboard.dashboard_code}</p>
              <p className="text-sm text-gray-600 mb-4">Category: {dashboard.category}</p>
              
              {dashboard.description && (
                <p className="text-sm text-gray-700 mb-4 line-clamp-3">{dashboard.description}</p>
              )}
              
              <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>Type: {dashboard.dashboard_type}</span>
                <span className={`inline-flex px-2 py-1 rounded-full ${
                  dashboard.is_public 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {dashboard.is_public ? 'Public' : 'Private'}
                </span>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => router.push(`/enterprise/bi/dashboards/${dashboard.id}`)}
                  className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
                >
                  View
                </button>
                <button
                  onClick={() => router.push(`/enterprise/bi/dashboards/${dashboard.id}/edit`)}
                  className="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDelete(dashboard.id)}
                  className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {filteredDashboards.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <div className="text-6xl mb-4">📊</div>
          <h3 className="text-lg font-medium mb-2">No dashboards found</h3>
          <p className="text-sm">Create your first dashboard to get started with business intelligence.</p>
        </div>
      )}
    </div>
  );
};

export default DashboardsListPage;
