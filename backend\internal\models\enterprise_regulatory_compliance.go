package models

import (
	"time"

	"gorm.io/gorm"
)

// ComplianceRequirement represents specific compliance requirements
type ComplianceRequirement struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Requirement identification
	RequirementCode string              `json:"requirement_code" gorm:"uniqueIndex;not null"`
	Title           string              `json:"title" gorm:"not null"`
	Description     string              `json:"description" gorm:"type:text"`
	Framework       ComplianceFramework `json:"framework" gorm:"not null"`

	// Requirement details
	Category    string    `json:"category"` // "technical", "administrative", "physical"
	Subcategory string    `json:"subcategory"`
	RiskLevel   RiskLevel `json:"risk_level" gorm:"default:'medium'"`

	// Implementation details
	ControlType    string `json:"control_type"`                    // "preventive", "detective", "corrective"
	Implementation string `json:"implementation" gorm:"type:text"` // Implementation guidance
	TestingMethod  string `json:"testing_method" gorm:"type:text"` // Testing procedures

	// Frequency and timing
	TestingFrequency string `json:"testing_frequency" gorm:"default:'annual'"` // "monthly", "quarterly", "annual"
	ReviewFrequency  string `json:"review_frequency" gorm:"default:'annual'"`

	// Compliance dates
	EffectiveDate  time.Time  `json:"effective_date"`
	ExpirationDate *time.Time `json:"expiration_date"`

	// Status and ownership
	Status  string `json:"status" gorm:"default:'active'"` // "active", "inactive", "superseded"
	OwnerID *uint  `json:"owner_id"`
	Owner   *User  `json:"owner,omitempty" gorm:"foreignKey:OwnerID"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ComplianceFinding represents compliance findings and issues
type ComplianceFinding struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Finding identification
	FindingCode string `json:"finding_code" gorm:"uniqueIndex;not null"`
	Title       string `json:"title" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`

	// Assessment linkage
	AssessmentID  uint                  `json:"assessment_id" gorm:"not null"`
	Assessment    ComplianceAssessment  `json:"assessment" gorm:"foreignKey:AssessmentID"`
	RequirementID uint                  `json:"requirement_id" gorm:"not null"`
	Requirement   ComplianceRequirement `json:"requirement" gorm:"foreignKey:RequirementID"`

	// Finding details
	FindingType string    `json:"finding_type" gorm:"default:'deficiency'"` // "deficiency", "observation", "best_practice"
	RiskLevel   RiskLevel `json:"risk_level" gorm:"not null"`
	Impact      string    `json:"impact" gorm:"type:text"`
	Likelihood  string    `json:"likelihood" gorm:"default:'medium'"` // "low", "medium", "high"

	// Evidence and documentation
	Evidence    string `json:"evidence" gorm:"type:text"`     // Evidence description
	DocumentIDs string `json:"document_ids" gorm:"type:text"` // JSON array of document IDs

	// Remediation
	Recommendation  string     `json:"recommendation" gorm:"type:text"`
	RemediationPlan string     `json:"remediation_plan" gorm:"type:text"`
	TargetDate      *time.Time `json:"target_date"`
	ResponsibleID   *uint      `json:"responsible_id"`
	Responsible     *User      `json:"responsible,omitempty" gorm:"foreignKey:ResponsibleID"`

	// Status tracking
	Status          string     `json:"status" gorm:"default:'open'"` // "open", "in_progress", "resolved", "closed"
	ResolutionDate  *time.Time `json:"resolution_date"`
	ResolutionNotes string     `json:"resolution_notes" gorm:"type:text"`

	// Verification
	VerifiedByID *uint      `json:"verified_by_id"`
	VerifiedBy   *User      `json:"verified_by,omitempty" gorm:"foreignKey:VerifiedByID"`
	VerifiedAt   *time.Time `json:"verified_at"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// RiskAssessment represents enterprise risk assessments
type RiskAssessment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Risk identification
	RiskCode        string `json:"risk_code" gorm:"uniqueIndex;not null"`
	RiskTitle       string `json:"risk_title" gorm:"not null"`
	RiskDescription string `json:"risk_description" gorm:"type:text"`
	RiskCategory    string `json:"risk_category"` // "operational", "financial", "strategic", "compliance"

	// Risk analysis
	InherentRisk RiskLevel `json:"inherent_risk" gorm:"not null"`         // Risk before controls
	ResidualRisk RiskLevel `json:"residual_risk" gorm:"not null"`         // Risk after controls
	RiskAppetite RiskLevel `json:"risk_appetite" gorm:"default:'medium'"` // Acceptable risk level

	// Risk scoring
	ImpactScore     int     `json:"impact_score" gorm:"default:1"`     // 1-5 scale
	LikelihoodScore int     `json:"likelihood_score" gorm:"default:1"` // 1-5 scale
	RiskScore       float64 `json:"risk_score" gorm:"default:0"`       // Calculated risk score

	// Risk ownership
	RiskOwnerID uint `json:"risk_owner_id" gorm:"not null"`
	RiskOwner   User `json:"risk_owner" gorm:"foreignKey:RiskOwnerID"`

	// Risk treatment
	TreatmentStrategy string `json:"treatment_strategy"` // "accept", "mitigate", "transfer", "avoid"
	MitigationPlan    string `json:"mitigation_plan" gorm:"type:text"`

	// Monitoring and review
	LastReviewDate  *time.Time `json:"last_review_date"`
	NextReviewDate  time.Time  `json:"next_review_date"`
	ReviewFrequency string     `json:"review_frequency" gorm:"default:'quarterly'"`

	// Status
	Status string `json:"status" gorm:"default:'active'"` // "active", "closed", "transferred"

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// PolicyManagement represents policy and procedure management
type PolicyManagement struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Policy identification
	PolicyCode  string `json:"policy_code" gorm:"uniqueIndex;not null"`
	PolicyTitle string `json:"policy_title" gorm:"not null"`
	PolicyType  string `json:"policy_type" gorm:"default:'policy'"` // "policy", "procedure", "guideline", "standard"
	Description string `json:"description" gorm:"type:text"`

	// Policy content
	Content string `json:"content" gorm:"type:text"` // Policy content in markdown
	Version string `json:"version" gorm:"default:'1.0'"`

	// Policy hierarchy
	ParentPolicyID *uint             `json:"parent_policy_id"`
	ParentPolicy   *PolicyManagement `json:"parent_policy,omitempty" gorm:"foreignKey:ParentPolicyID"`

	// Compliance linkage
	Framework      ComplianceFramework `json:"framework"`
	RequirementIDs string              `json:"requirement_ids" gorm:"type:text"` // JSON array of requirement IDs

	// Ownership and approval
	OwnerID    uint       `json:"owner_id" gorm:"not null"`
	Owner      User       `json:"owner" gorm:"foreignKey:OwnerID"`
	ApproverID *uint      `json:"approver_id"`
	Approver   *User      `json:"approver,omitempty" gorm:"foreignKey:ApproverID"`
	ApprovedAt *time.Time `json:"approved_at"`

	// Lifecycle management
	EffectiveDate   time.Time  `json:"effective_date"`
	ExpirationDate  *time.Time `json:"expiration_date"`
	ReviewDate      time.Time  `json:"review_date"`
	ReviewFrequency string     `json:"review_frequency" gorm:"default:'annual'"`

	// Status and distribution
	Status      string     `json:"status" gorm:"default:'draft'"` // "draft", "review", "approved", "published", "archived"
	IsPublished bool       `json:"is_published" gorm:"default:false"`
	PublishedAt *time.Time `json:"published_at"`

	// Training and acknowledgment
	RequiresTraining bool `json:"requires_training" gorm:"default:false"`
	RequiresAck      bool `json:"requires_acknowledgment" gorm:"default:false"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for ComplianceRequirement model
func (ComplianceRequirement) TableName() string {
	return "compliance_requirements"
}

// TableName returns the table name for ComplianceFinding model
func (ComplianceFinding) TableName() string {
	return "compliance_findings"
}

// TableName returns the table name for RiskAssessment model
func (RiskAssessment) TableName() string {
	return "risk_assessments"
}

// TableName returns the table name for PolicyManagement model
func (PolicyManagement) TableName() string {
	return "policy_management"
}
