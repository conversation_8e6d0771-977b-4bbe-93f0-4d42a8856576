import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import {
  EyeIcon,
  CodeBracketIcon,
  DocumentTextIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

interface MarkdownEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  label?: string;
  rows?: number;
  className?: string;
  showPreview?: boolean;
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  onChange,
  placeholder = "Enter markdown content...",
  label = "Content",
  rows = 10,
  className = "",
  showPreview = true
}) => {
  const [mode, setMode] = useState<'edit' | 'preview' | 'split'>('edit');

  const handleModeChange = (newMode: 'edit' | 'preview' | 'split') => {
    setMode(newMode);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Label and Mode Switcher */}
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {label}
        </label>
        
        {showPreview && (
          <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
            <button
              type="button"
              onClick={() => handleModeChange('edit')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                mode === 'edit'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <CodeBracketIcon className="h-4 w-4 inline mr-1" />
              Edit
            </button>
            <button
              type="button"
              onClick={() => handleModeChange('preview')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                mode === 'preview'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <EyeIcon className="h-4 w-4 inline mr-1" />
              Preview
            </button>
            <button
              type="button"
              onClick={() => handleModeChange('split')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                mode === 'split'
                  ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
              }`}
            >
              <DocumentTextIcon className="h-4 w-4 inline mr-1" />
              Split
            </button>
          </div>
        )}
      </div>

      {/* Editor/Preview Area */}
      <div className="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
        {mode === 'edit' && (
          <div className="relative">
            <textarea
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder={placeholder}
              rows={rows}
              className="w-full px-3 py-2 border-0 focus:ring-0 focus:outline-none resize-none dark:bg-gray-700 dark:text-white"
            />
          </div>
        )}

        {mode === 'preview' && (
          <div className="p-4 min-h-[200px] bg-gray-50 dark:bg-gray-800">
            {value ? (
              <div className="prose prose-sm max-w-none dark:prose-invert">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {value}
                </ReactMarkdown>
              </div>
            ) : (
              <div className="flex items-center justify-center h-32 text-gray-500 dark:text-gray-400">
                <div className="text-center">
                  <DocumentTextIcon className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No content to preview</p>
                </div>
              </div>
            )}
          </div>
        )}

        {mode === 'split' && (
          <div className="grid grid-cols-2 divide-x divide-gray-300 dark:divide-gray-600">
            {/* Editor Side */}
            <div className="relative">
              <div className="absolute top-2 left-2 z-10">
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                  <CodeBracketIcon className="h-3 w-3 mr-1" />
                  Markdown
                </span>
              </div>
              <textarea
                value={value}
                onChange={(e) => onChange(e.target.value)}
                placeholder={placeholder}
                rows={rows}
                className="w-full px-3 py-2 pt-10 border-0 focus:ring-0 focus:outline-none resize-none dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            {/* Preview Side */}
            <div className="relative">
              <div className="absolute top-2 left-2 z-10">
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                  <EyeIcon className="h-3 w-3 mr-1" />
                  Preview
                </span>
              </div>
              <div className="p-3 pt-10 min-h-[200px] bg-gray-50 dark:bg-gray-800 overflow-auto">
                {value ? (
                  <div className="prose prose-sm max-w-none dark:prose-invert">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {value}
                    </ReactMarkdown>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-32 text-gray-500 dark:text-gray-400">
                    <div className="text-center">
                      <DocumentTextIcon className="h-6 w-6 mx-auto mb-1" />
                      <p className="text-xs">No content</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Markdown Help */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md p-3">
        <div className="flex">
          <InformationCircleIcon className="h-4 w-4 text-blue-400 mr-2 flex-shrink-0 mt-0.5" />
          <div className="text-xs text-blue-600 dark:text-blue-400">
            <p className="font-medium mb-1">Markdown Support</p>
            <p>
              Supports headers (#), bold (**text**), italic (*text*), lists (- item), 
              links ([text](url)), tables, code blocks (```), and more.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkdownEditor;
