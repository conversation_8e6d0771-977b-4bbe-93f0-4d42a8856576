package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

// TestSuite represents a test suite configuration
type TestSuite struct {
	Name        string
	Path        string
	Description string
	Timeout     time.Duration
}

// TestResult represents the result of running a test suite
type TestResult struct {
	Suite    TestSuite
	Success  bool
	Output   string
	Duration time.Duration
	Error    error
}

func main() {
	fmt.Println("🚀 Running Comprehensive Backend Test Suite")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	// Define test suites
	testSuites := []TestSuite{
		{
			Name:        "NLP Service Tests",
			Path:        "./tests/nlp_service_test.go",
			Description: "Tests for advanced NLP functionality including sentiment analysis, keyword extraction, and text complexity analysis",
			Timeout:     5 * time.Minute,
		},
		{
			Name:        "Document Processing Service Tests",
			Path:        "./tests/document_processing_service_test.go",
			Description: "Tests for document processing pipeline including OCR, classification, and metadata extraction",
			Timeout:     5 * time.Minute,
		},
		{
			Name:        "Analytics Service Tests",
			Path:        "./tests/analytics_service_test.go",
			Description: "Tests for analytics and dashboard functionality including multiple dashboard types",
			Timeout:     5 * time.Minute,
		},
		{
			Name:        "Authentication Tests",
			Path:        "./tests/auth_test.go",
			Description: "Tests for user authentication, authorization, and session management",
			Timeout:     3 * time.Minute,
		},
		{
			Name:        "Regulation Service Tests",
			Path:        "./tests/regulation_service_test.go",
			Description: "Tests for regulation management and versioning system",
			Timeout:     3 * time.Minute,
		},
		{
			Name:        "Regulation Handler Tests",
			Path:        "./tests/regulation_test.go",
			Description: "Tests for regulation API endpoints and handlers",
			Timeout:     3 * time.Minute,
		},
		{
			Name:        "Text Parser Tests",
			Path:        "./tests/text_parser_test.go",
			Description: "Tests for intelligent text parsing and task extraction",
			Timeout:     2 * time.Minute,
		},
		{
			Name:        "Unified System Tests",
			Path:        "./tests/unified_system_test.go",
			Description: "Integration tests for the complete system",
			Timeout:     10 * time.Minute,
		},
	}

	// Run all test suites
	results := make([]TestResult, 0, len(testSuites))
	totalStartTime := time.Now()

	for i, suite := range testSuites {
		fmt.Printf("📋 [%d/%d] Running: %s\n", i+1, len(testSuites), suite.Name)
		fmt.Printf("    Description: %s\n", suite.Description)
		fmt.Printf("    Path: %s\n", suite.Path)
		fmt.Printf("    Timeout: %v\n", suite.Timeout)
		fmt.Println()

		result := runTestSuite(suite)
		results = append(results, result)

		if result.Success {
			fmt.Printf("✅ PASSED: %s (%.2fs)\n", suite.Name, result.Duration.Seconds())
		} else {
			fmt.Printf("❌ FAILED: %s (%.2fs)\n", suite.Name, result.Duration.Seconds())
			if result.Error != nil {
				fmt.Printf("    Error: %v\n", result.Error)
			}
		}
		fmt.Println()
	}

	// Generate comprehensive report
	generateTestReport(results, time.Since(totalStartTime))
}

func runTestSuite(suite TestSuite) TestResult {
	startTime := time.Now()
	
	// Check if test file exists
	if _, err := os.Stat(suite.Path); os.IsNotExist(err) {
		return TestResult{
			Suite:    suite,
			Success:  false,
			Duration: time.Since(startTime),
			Error:    fmt.Errorf("test file does not exist: %s", suite.Path),
		}
	}

	// Prepare go test command
	cmd := exec.Command("go", "test", "-v", "-timeout", suite.Timeout.String(), suite.Path)
	cmd.Dir = filepath.Dir(suite.Path)
	
	// Set environment variables for testing
	cmd.Env = append(os.Environ(),
		"GO_ENV=test",
		"DB_TYPE=sqlite",
		"DB_PATH=:memory:",
	)

	// Run the test
	output, err := cmd.CombinedOutput()
	duration := time.Since(startTime)

	success := err == nil
	if err != nil {
		// Check if it's just a test failure vs a compilation error
		if exitError, ok := err.(*exec.ExitError); ok {
			// Test ran but some tests failed
			if exitError.ExitCode() == 1 {
				success = false // Test failures
			}
		}
	}

	return TestResult{
		Suite:    suite,
		Success:  success,
		Output:   string(output),
		Duration: duration,
		Error:    err,
	}
}

func generateTestReport(results []TestResult, totalDuration time.Duration) {
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println("📊 COMPREHENSIVE TEST REPORT")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println()

	// Summary statistics
	totalTests := len(results)
	passedTests := 0
	failedTests := 0
	totalTestDuration := time.Duration(0)

	for _, result := range results {
		totalTestDuration += result.Duration
		if result.Success {
			passedTests++
		} else {
			failedTests++
		}
	}

	// Print summary
	fmt.Printf("📈 SUMMARY:\n")
	fmt.Printf("   Total Test Suites: %d\n", totalTests)
	fmt.Printf("   Passed: %d (%.1f%%)\n", passedTests, float64(passedTests)/float64(totalTests)*100)
	fmt.Printf("   Failed: %d (%.1f%%)\n", failedTests, float64(failedTests)/float64(totalTests)*100)
	fmt.Printf("   Total Duration: %.2fs\n", totalDuration.Seconds())
	fmt.Printf("   Test Execution Time: %.2fs\n", totalTestDuration.Seconds())
	fmt.Println()

	// Detailed results
	fmt.Printf("📋 DETAILED RESULTS:\n")
	fmt.Println()

	for i, result := range results {
		status := "✅ PASSED"
		if !result.Success {
			status = "❌ FAILED"
		}

		fmt.Printf("%d. %s %s\n", i+1, status, result.Suite.Name)
		fmt.Printf("   Duration: %.2fs\n", result.Duration.Seconds())
		fmt.Printf("   Path: %s\n", result.Suite.Path)
		
		if !result.Success {
			fmt.Printf("   Error: %v\n", result.Error)
			
			// Show relevant output for failed tests
			if result.Output != "" {
				lines := strings.Split(result.Output, "\n")
				fmt.Printf("   Output (last 10 lines):\n")
				start := len(lines) - 10
				if start < 0 {
					start = 0
				}
				for _, line := range lines[start:] {
					if strings.TrimSpace(line) != "" {
						fmt.Printf("     %s\n", line)
					}
				}
			}
		}
		fmt.Println()
	}

	// Coverage and recommendations
	fmt.Printf("🎯 COVERAGE ANALYSIS:\n")
	fmt.Println()
	
	coverageAreas := map[string]bool{
		"NLP Processing":        false,
		"Document Processing":   false,
		"Analytics & Dashboards": false,
		"Authentication":        false,
		"Regulation Management": false,
		"Text Parsing":          false,
		"System Integration":    false,
	}

	for _, result := range results {
		switch {
		case strings.Contains(result.Suite.Name, "NLP"):
			coverageAreas["NLP Processing"] = result.Success
		case strings.Contains(result.Suite.Name, "Document Processing"):
			coverageAreas["Document Processing"] = result.Success
		case strings.Contains(result.Suite.Name, "Analytics"):
			coverageAreas["Analytics & Dashboards"] = result.Success
		case strings.Contains(result.Suite.Name, "Authentication"):
			coverageAreas["Authentication"] = result.Success
		case strings.Contains(result.Suite.Name, "Regulation"):
			coverageAreas["Regulation Management"] = result.Success
		case strings.Contains(result.Suite.Name, "Text Parser"):
			coverageAreas["Text Parsing"] = result.Success
		case strings.Contains(result.Suite.Name, "Unified"):
			coverageAreas["System Integration"] = result.Success
		}
	}

	for area, covered := range coverageAreas {
		status := "✅"
		if !covered {
			status = "❌"
		}
		fmt.Printf("   %s %s\n", status, area)
	}
	fmt.Println()

	// Final verdict
	if passedTests == totalTests {
		fmt.Printf("🎉 ALL TESTS PASSED! The system is ready for production.\n")
	} else {
		fmt.Printf("⚠️  %d test suite(s) failed. Please review and fix issues before deployment.\n", failedTests)
	}

	fmt.Println()
	fmt.Printf("📝 Test report generated at: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println(strings.Repeat("=", 60))
}
