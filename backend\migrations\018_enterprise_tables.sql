-- Migration 018: Enterprise Tables
-- Creates missing enterprise tables for BI, HR, and system functionality

-- =====================================================
-- SYSTEM TABLES
-- =====================================================

-- System Events table for error logging and system monitoring
CREATE TABLE IF NOT EXISTS system_events (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    event_type VARCHAR(50) NOT NULL,
    entity_id INTEGER,
    user_id INTEGER REFERENCES users(id),
    data JSONB,
    processed BOOLEAN DEFAULT FALSE,
    error TEXT
);

-- =====================================================
-- BUSINESS INTELLIGENCE TABLES
-- =====================================================

-- Data Sources table for BI data connections
CREATE TABLE IF NOT EXISTS data_sources (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    source_name VARCHAR(255) NOT NULL,
    source_type VARCHAR(100) NOT NULL, -- database, api, file, etc.
    connection_string TEXT,
    configuration JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    last_sync TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(id),
    
    UNIQUE(source_name)
);

-- Data Warehouses table for BI data storage
CREATE TABLE IF NOT EXISTS data_warehouses (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    warehouse_name VARCHAR(255) NOT NULL,
    warehouse_type VARCHAR(100) NOT NULL,
    connection_details JSONB,
    storage_capacity BIGINT,
    current_usage BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES users(id),
    
    UNIQUE(warehouse_name)
);

-- Dashboards table for BI visualization
CREATE TABLE IF NOT EXISTS dashboards (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    dashboard_name VARCHAR(255) NOT NULL,
    description TEXT,
    layout_config JSONB,
    widgets JSONB,
    is_public BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES users(id),
    last_accessed TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0
);

-- Reports table for BI reporting
CREATE TABLE IF NOT EXISTS reports (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    report_name VARCHAR(255) NOT NULL,
    description TEXT,
    report_type VARCHAR(100) NOT NULL,
    query_definition JSONB,
    parameters JSONB,
    schedule_config JSONB,
    output_format VARCHAR(50) DEFAULT 'pdf',
    is_scheduled BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES users(id),
    last_generated TIMESTAMP WITH TIME ZONE
);

-- KPIs table for Key Performance Indicators
CREATE TABLE IF NOT EXISTS kpis (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    kpi_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    unit VARCHAR(50),
    target_value DECIMAL(15,2),
    current_value DECIMAL(15,2),
    update_frequency VARCHAR(50),
    calculation_method TEXT,
    data_source_id INTEGER REFERENCES data_sources(id),
    owner_id INTEGER REFERENCES users(id),
    is_active BOOLEAN DEFAULT TRUE,
    last_updated TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- HUMAN RESOURCES TABLES
-- =====================================================

-- Departments table for organizational structure
CREATE TABLE IF NOT EXISTS departments (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    department_name VARCHAR(255) NOT NULL,
    department_code VARCHAR(50),
    description TEXT,
    parent_department_id INTEGER REFERENCES departments(id),
    manager_id INTEGER REFERENCES users(id),
    budget DECIMAL(15,2),
    is_active BOOLEAN DEFAULT TRUE,
    
    UNIQUE(department_name),
    UNIQUE(department_code)
);

-- Employees table for staff management
CREATE TABLE IF NOT EXISTS employees (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    employee_id VARCHAR(50) NOT NULL,
    user_id INTEGER REFERENCES users(id),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    department_id INTEGER REFERENCES departments(id),
    position_title VARCHAR(255),
    hire_date DATE,
    salary DECIMAL(15,2),
    employment_status VARCHAR(50) DEFAULT 'active',
    manager_id INTEGER REFERENCES employees(id),
    
    UNIQUE(employee_id),
    UNIQUE(email)
);

-- Positions table for job roles
CREATE TABLE IF NOT EXISTS positions (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    position_title VARCHAR(255) NOT NULL,
    position_code VARCHAR(50),
    department_id INTEGER REFERENCES departments(id),
    description TEXT,
    requirements TEXT,
    salary_range_min DECIMAL(15,2),
    salary_range_max DECIMAL(15,2),
    is_active BOOLEAN DEFAULT TRUE,
    
    UNIQUE(position_title, department_id)
);

-- =====================================================
-- COMPLIANCE TABLES
-- =====================================================

-- Policy Management table for compliance policies
CREATE TABLE IF NOT EXISTS policy_management (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    policy_name VARCHAR(255) NOT NULL,
    policy_number VARCHAR(100),
    description TEXT,
    policy_type VARCHAR(100),
    effective_date DATE,
    expiration_date DATE,
    status VARCHAR(50) DEFAULT 'draft',
    content TEXT,
    approval_required BOOLEAN DEFAULT TRUE,
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(id),
    
    UNIQUE(policy_number)
);

-- =====================================================
-- FINANCIAL TABLES (Additional)
-- =====================================================

-- Budgets table for financial planning
CREATE TABLE IF NOT EXISTS budgets (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    budget_name VARCHAR(255) NOT NULL,
    fiscal_year INTEGER NOT NULL,
    department_id INTEGER REFERENCES departments(id),
    category VARCHAR(100),
    allocated_amount DECIMAL(15,2) NOT NULL,
    spent_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) GENERATED ALWAYS AS (allocated_amount - spent_amount) STORED,
    status VARCHAR(50) DEFAULT 'active',
    created_by INTEGER REFERENCES users(id)
);

-- Expenses table for financial tracking
CREATE TABLE IF NOT EXISTS expenses (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    expense_name VARCHAR(255) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    expense_date DATE NOT NULL,
    category VARCHAR(100),
    department_id INTEGER REFERENCES departments(id),
    budget_id INTEGER REFERENCES budgets(id),
    description TEXT,
    receipt_url VARCHAR(500),
    approved_by INTEGER REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    created_by INTEGER REFERENCES users(id)
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- System Events indexes
CREATE INDEX IF NOT EXISTS idx_system_events_event_type ON system_events(event_type);
CREATE INDEX IF NOT EXISTS idx_system_events_user_id ON system_events(user_id);
CREATE INDEX IF NOT EXISTS idx_system_events_created_at ON system_events(created_at);

-- BI table indexes
CREATE INDEX IF NOT EXISTS idx_data_sources_source_type ON data_sources(source_type);
CREATE INDEX IF NOT EXISTS idx_data_sources_is_active ON data_sources(is_active);
CREATE INDEX IF NOT EXISTS idx_dashboards_created_by ON dashboards(created_by);
CREATE INDEX IF NOT EXISTS idx_dashboards_is_public ON dashboards(is_public);
CREATE INDEX IF NOT EXISTS idx_reports_report_type ON reports(report_type);
CREATE INDEX IF NOT EXISTS idx_reports_is_scheduled ON reports(is_scheduled);
CREATE INDEX IF NOT EXISTS idx_kpis_category ON kpis(category);
CREATE INDEX IF NOT EXISTS idx_kpis_owner_id ON kpis(owner_id);

-- HR table indexes
CREATE INDEX IF NOT EXISTS idx_departments_parent_department_id ON departments(parent_department_id);
CREATE INDEX IF NOT EXISTS idx_employees_department_id ON employees(department_id);
CREATE INDEX IF NOT EXISTS idx_employees_manager_id ON employees(manager_id);
CREATE INDEX IF NOT EXISTS idx_employees_employment_status ON employees(employment_status);
CREATE INDEX IF NOT EXISTS idx_positions_department_id ON positions(department_id);

-- Compliance indexes
CREATE INDEX IF NOT EXISTS idx_policy_management_policy_type ON policy_management(policy_type);
CREATE INDEX IF NOT EXISTS idx_policy_management_status ON policy_management(status);
CREATE INDEX IF NOT EXISTS idx_policy_management_effective_date ON policy_management(effective_date);

-- Financial indexes
CREATE INDEX IF NOT EXISTS idx_budgets_fiscal_year ON budgets(fiscal_year);
CREATE INDEX IF NOT EXISTS idx_budgets_department_id ON budgets(department_id);
CREATE INDEX IF NOT EXISTS idx_expenses_expense_date ON expenses(expense_date);
CREATE INDEX IF NOT EXISTS idx_expenses_department_id ON expenses(department_id);
CREATE INDEX IF NOT EXISTS idx_expenses_budget_id ON expenses(budget_id);
