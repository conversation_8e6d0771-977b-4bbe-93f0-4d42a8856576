-- Federal Register Clone Database Schema
-- Initial migration script to create all core tables

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Drop existing tables if they exist (for clean migration)
DROP TABLE IF EXISTS document_subject_assignments CASCADE;
DROP TABLE IF EXISTS document_tag_assignments CASCADE;
DROP TABLE IF EXISTS document_category_assignments CASCADE;
DROP TABLE IF EXISTS document_files CASCADE;
DROP TABLE IF EXISTS document_comments CASCADE;
DROP TABLE IF EXISTS document_reviews CASCADE;
DROP TABLE IF EXISTS document_versions CASCADE;
DROP TABLE IF EXISTS documents CASCADE;
DROP TABLE IF EXISTS subjects CASCADE;
DROP TABLE IF EXISTS tags CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS agency_contacts CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS user_preferences CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS agencies CASCADE;

-- <PERSON><PERSON> agencies table (no self-reference for now)
CREATE TABLE agencies (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Basic information
    name TEXT NOT NULL,
    short_name TEXT UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    
    -- Contact information
    website TEXT,
    email TEXT,
    phone TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    country TEXT DEFAULT 'US',
    
    -- Hierarchy (simplified)
    parent_agency_id BIGINT,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    agency_type TEXT,
    jurisdiction TEXT,
    established_at TIMESTAMPTZ,
    
    -- Branding
    logo_url TEXT,
    primary_color TEXT,
    secondary_color TEXT
);

-- Create users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Authentication
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    
    -- Profile
    first_name TEXT,
    last_name TEXT,
    title TEXT,
    department TEXT,
    organization TEXT,
    phone TEXT,
    bio TEXT,
    
    -- Authorization and status
    role TEXT DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMPTZ,
    
    -- Agency relationship
    agency_id BIGINT REFERENCES agencies(id)
);

-- Create user sessions table
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    user_id BIGINT NOT NULL REFERENCES users(id),
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    ip_address TEXT,
    user_agent TEXT
);

-- Create user preferences table
CREATE TABLE user_preferences (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    user_id BIGINT NOT NULL UNIQUE REFERENCES users(id),
    
    -- Notification preferences
    email_notifications BOOLEAN DEFAULT true,
    document_alerts BOOLEAN DEFAULT true,
    weekly_digest BOOLEAN DEFAULT false,
    comment_notifications BOOLEAN DEFAULT true,
    
    -- Display preferences
    documents_per_page BIGINT DEFAULT 25,
    default_view TEXT DEFAULT 'list',
    theme TEXT DEFAULT 'light',
    language TEXT DEFAULT 'en',
    timezone TEXT DEFAULT 'UTC',
    
    -- Search preferences
    default_search_sort TEXT DEFAULT 'relevance',
    save_search_history BOOLEAN DEFAULT true,
    auto_complete_enabled BOOLEAN DEFAULT true
);

-- Create categories table (simplified hierarchy)
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    parent_category_id BIGINT,
    color TEXT,
    icon TEXT,
    sort_order BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true
);

-- Create tags table
CREATE TABLE tags (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    color TEXT,
    usage_count BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by_id BIGINT REFERENCES users(id)
);

-- Create subjects table (simplified hierarchy)
CREATE TABLE subjects (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    cfr_title TEXT,
    parent_subject_id BIGINT,
    sort_order BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true
);

-- Create documents table
CREATE TABLE documents (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Basic document information
    title TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    abstract TEXT,
    content TEXT,
    type TEXT NOT NULL,
    status TEXT DEFAULT 'draft',
    
    -- Federal Register specific
    fr_document_number TEXT UNIQUE,
    fr_citation TEXT,
    cfr_citations TEXT,
    
    -- Important dates
    publication_date TIMESTAMPTZ,
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    comment_due_date TIMESTAMPTZ,
    
    -- Document metadata
    page_count BIGINT,
    word_count BIGINT,
    language TEXT DEFAULT 'en',
    original_format TEXT,
    file_size BIGINT,
    checksum TEXT,
    
    -- Relationships
    agency_id BIGINT NOT NULL REFERENCES agencies(id),
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    updated_by_id BIGINT REFERENCES users(id),
    parent_document_id BIGINT,
    
    -- Search and analytics
    search_vector TSVECTOR,
    view_count BIGINT DEFAULT 0,
    download_count BIGINT DEFAULT 0,
    
    -- Workflow
    workflow_stage TEXT,
    approved_at TIMESTAMPTZ,
    approved_by_id BIGINT REFERENCES users(id),
    published_at TIMESTAMPTZ,
    published_by_id BIGINT REFERENCES users(id),
    
    -- Regulatory information
    regulatory_identifier TEXT,
    docket_number TEXT,
    significant_rule BOOLEAN DEFAULT false,
    economic_impact TEXT,
    small_entity_impact BOOLEAN DEFAULT false,
    
    -- Comments
    accepts_comments BOOLEAN DEFAULT false,
    comment_count BIGINT DEFAULT 0,
    comment_instructions TEXT,
    public_hearing_date TIMESTAMPTZ,
    public_hearing_info TEXT
);

-- Create agency_contacts table
CREATE TABLE agency_contacts (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    agency_id BIGINT NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,

    -- Contact information
    name TEXT NOT NULL,
    title TEXT,
    department TEXT,
    email TEXT,
    phone TEXT,
    fax TEXT,
    address TEXT,

    -- Contact type and purpose
    contact_type TEXT, -- primary, media, legal, technical
    purpose TEXT,      -- general, foia, comments, complaints
    is_public BOOLEAN DEFAULT true,
    is_primary BOOLEAN DEFAULT false
);

-- Create junction tables for many-to-many relationships
CREATE TABLE document_category_assignments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    category_id BIGINT REFERENCES categories(id) ON DELETE CASCADE,
    assigned_by_id BIGINT REFERENCES users(id),
    UNIQUE(document_id, category_id)
);

CREATE TABLE document_tag_assignments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    tag_id BIGINT REFERENCES tags(id) ON DELETE CASCADE,
    assigned_by_id BIGINT REFERENCES users(id),
    UNIQUE(document_id, tag_id)
);

CREATE TABLE document_subject_assignments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    subject_id BIGINT REFERENCES subjects(id) ON DELETE CASCADE,
    assigned_by_id BIGINT REFERENCES users(id),
    UNIQUE(document_id, subject_id)
);
