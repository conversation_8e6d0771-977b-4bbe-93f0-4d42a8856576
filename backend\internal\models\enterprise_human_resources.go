package models

import (
	"time"

	"gorm.io/gorm"
)

// EmploymentType represents different types of employment
type EmploymentType string

const (
	EmploymentFullTime   EmploymentType = "full_time"
	EmploymentPartTime   EmploymentType = "part_time"
	EmploymentContract   EmploymentType = "contract"
	EmploymentIntern     EmploymentType = "intern"
	EmploymentConsultant EmploymentType = "consultant"
)

// EmploymentStatus represents employment status
type EmploymentStatus string

const (
	StatusActive     EmploymentStatus = "active"
	StatusInactive   EmploymentStatus = "inactive"
	StatusTerminated EmploymentStatus = "terminated"
	StatusSuspended  EmploymentStatus = "suspended"
	StatusOnLeave    EmploymentStatus = "on_leave"
)

// Employee represents comprehensive employee information
type Employee struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Employee identification
	EmployeeID      string `json:"employee_id" gorm:"uniqueIndex;not null"`
	UserID          uint   `json:"user_id" gorm:"not null"`
	User            User   `json:"user" gorm:"foreignKey:UserID"`
	
	// Personal information
	FirstName       string     `json:"first_name" gorm:"not null"`
	LastName        string     `json:"last_name" gorm:"not null"`
	MiddleName      string     `json:"middle_name"`
	DateOfBirth     *time.Time `json:"date_of_birth"`
	Gender          string     `json:"gender"`
	MaritalStatus   string     `json:"marital_status"`
	Nationality     string     `json:"nationality"`
	
	// Contact information
	PersonalEmail   string `json:"personal_email"`
	PersonalPhone   string `json:"personal_phone"`
	EmergencyContact string `json:"emergency_contact" gorm:"type:text"`      // JSON emergency contact info
	Address         string `json:"address" gorm:"type:text"`                 // JSON address information
	
	// Employment details
	HireDate        time.Time        `json:"hire_date" gorm:"not null"`
	EmploymentType  EmploymentType   `json:"employment_type" gorm:"not null"`
	EmploymentStatus EmploymentStatus `json:"employment_status" gorm:"default:'active'"`
	TerminationDate *time.Time       `json:"termination_date"`
	TerminationReason string         `json:"termination_reason"`
	
	// Job information
	JobTitle        string `json:"job_title" gorm:"not null"`
	JobDescription  string `json:"job_description" gorm:"type:text"`
	DepartmentID    *uint  `json:"department_id"`
	Department      *Department `json:"department,omitempty" gorm:"foreignKey:DepartmentID"`
	ManagerID       *uint  `json:"manager_id"`
	Manager         *Employee `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`
	
	// Compensation
	BaseSalary      float64 `json:"base_salary" gorm:"default:0"`
	Currency        string  `json:"currency" gorm:"default:'USD'"`
	PayFrequency    string  `json:"pay_frequency" gorm:"default:'monthly'"`  // "weekly", "biweekly", "monthly"
	
	// Work schedule
	WorkSchedule    string `json:"work_schedule" gorm:"type:text"`           // JSON work schedule
	TimeZone        string `json:"time_zone" gorm:"default:'UTC'"`
	WorkLocation    string `json:"work_location"`                            // "office", "remote", "hybrid"
	
	// Performance and development
	PerformanceRating float64    `json:"performance_rating" gorm:"default:0"` // 0-5 scale
	LastReviewDate    *time.Time `json:"last_review_date"`
	NextReviewDate    *time.Time `json:"next_review_date"`
	
	// Benefits and entitlements
	VacationDays    int `json:"vacation_days" gorm:"default:0"`
	SickDays        int `json:"sick_days" gorm:"default:0"`
	PersonalDays    int `json:"personal_days" gorm:"default:0"`
	
	// Security and access
	SecurityClearance string `json:"security_clearance"`
	AccessLevel       string `json:"access_level" gorm:"default:'standard'"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                       // JSON metadata
}

// Department represents organizational departments
type Department struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Department identification
	DepartmentCode string `json:"department_code" gorm:"uniqueIndex;not null"`
	Name           string `json:"name" gorm:"not null"`
	Description    string `json:"description" gorm:"type:text"`
	
	// Department hierarchy
	ParentDepartmentID *uint       `json:"parent_department_id"`
	ParentDepartment   *Department `json:"parent_department,omitempty" gorm:"foreignKey:ParentDepartmentID"`
	Level              int         `json:"level" gorm:"default:0"`
	
	// Department management
	ManagerID       *uint     `json:"manager_id"`
	Manager         *Employee `json:"manager,omitempty" gorm:"foreignKey:ManagerID"`
	
	// Budget and cost center
	BudgetAmount    float64 `json:"budget_amount" gorm:"default:0"`
	ActualSpend     float64 `json:"actual_spend" gorm:"default:0"`
	CostCenterID    *uint   `json:"cost_center_id"`
	CostCenter      *CostCenter `json:"cost_center,omitempty" gorm:"foreignKey:CostCenterID"`
	
	// Location and facilities
	Location        string `json:"location"`
	OfficeSpace     string `json:"office_space"`
	
	// Status and metrics
	IsActive        bool `json:"is_active" gorm:"default:true"`
	EmployeeCount   int  `json:"employee_count" gorm:"default:0"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                       // JSON metadata
}

// Position represents job positions and roles
type Position struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Position identification
	PositionCode    string `json:"position_code" gorm:"uniqueIndex;not null"`
	Title           string `json:"title" gorm:"not null"`
	Description     string `json:"description" gorm:"type:text"`
	
	// Position details
	Level           string `json:"level"`                                    // "entry", "mid", "senior", "executive"
	Grade           string `json:"grade"`                                    // Pay grade or band
	JobFamily       string `json:"job_family"`                              // Job family classification
	
	// Department and reporting
	DepartmentID    uint       `json:"department_id" gorm:"not null"`
	Department      Department `json:"department" gorm:"foreignKey:DepartmentID"`
	ReportsToID     *uint      `json:"reports_to_id"`
	ReportsTo       *Position  `json:"reports_to,omitempty" gorm:"foreignKey:ReportsToID"`
	
	// Requirements and qualifications
	Requirements    string `json:"requirements" gorm:"type:text"`            // JSON requirements
	Qualifications  string `json:"qualifications" gorm:"type:text"`          // JSON qualifications
	Skills          string `json:"skills" gorm:"type:text"`                  // JSON skills
	
	// Compensation range
	MinSalary       float64 `json:"min_salary" gorm:"default:0"`
	MaxSalary       float64 `json:"max_salary" gorm:"default:0"`
	Currency        string  `json:"currency" gorm:"default:'USD'"`
	
	// Position status
	IsActive        bool `json:"is_active" gorm:"default:true"`
	IsApproved      bool `json:"is_approved" gorm:"default:false"`
	HeadcountLimit  int  `json:"headcount_limit" gorm:"default:1"`
	CurrentCount    int  `json:"current_count" gorm:"default:0"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                       // JSON metadata
}

// PerformanceReview represents employee performance reviews
type PerformanceReview struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Review identification
	ReviewID        string `json:"review_id" gorm:"uniqueIndex;not null"`
	EmployeeID      uint   `json:"employee_id" gorm:"not null"`
	Employee        Employee `json:"employee" gorm:"foreignKey:EmployeeID"`
	ReviewerID      uint   `json:"reviewer_id" gorm:"not null"`
	Reviewer        Employee `json:"reviewer" gorm:"foreignKey:ReviewerID"`
	
	// Review period
	ReviewPeriod    string    `json:"review_period" gorm:"not null"`         // "Q1 2024", "Annual 2024"
	StartDate       time.Time `json:"start_date" gorm:"not null"`
	EndDate         time.Time `json:"end_date" gorm:"not null"`
	ReviewDate      time.Time `json:"review_date" gorm:"not null"`
	
	// Review type and process
	ReviewType      string `json:"review_type" gorm:"default:'annual'"`      // "annual", "quarterly", "probationary"
	ReviewCycle     string `json:"review_cycle"`                             // Review cycle identifier
	
	// Performance ratings
	OverallRating   float64 `json:"overall_rating" gorm:"default:0"`         // 0-5 scale
	GoalAchievement float64 `json:"goal_achievement" gorm:"default:0"`       // 0-100 percentage
	CompetencyRating string `json:"competency_rating" gorm:"type:text"`      // JSON competency ratings
	
	// Review content
	Achievements    string `json:"achievements" gorm:"type:text"`
	AreasForImprovement string `json:"areas_for_improvement" gorm:"type:text"`
	Goals           string `json:"goals" gorm:"type:text"`                   // JSON goals for next period
	DevelopmentPlan string `json:"development_plan" gorm:"type:text"`
	
	// Comments and feedback
	EmployeeComments string `json:"employee_comments" gorm:"type:text"`
	ManagerComments  string `json:"manager_comments" gorm:"type:text"`
	HRComments       string `json:"hr_comments" gorm:"type:text"`
	
	// Review status and approval
	Status          string     `json:"status" gorm:"default:'draft'"`        // "draft", "submitted", "approved", "completed"
	SubmittedAt     *time.Time `json:"submitted_at"`
	ApprovedAt      *time.Time `json:"approved_at"`
	CompletedAt     *time.Time `json:"completed_at"`
	
	// Compensation recommendations
	SalaryIncrease  float64 `json:"salary_increase" gorm:"default:0"`        // Percentage
	BonusAmount     float64 `json:"bonus_amount" gorm:"default:0"`
	PromotionRecommended bool `json:"promotion_recommended" gorm:"default:false"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                       // JSON metadata
}

// Training represents training and development programs
type Training struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Training identification
	TrainingCode    string `json:"training_code" gorm:"uniqueIndex;not null"`
	Title           string `json:"title" gorm:"not null"`
	Description     string `json:"description" gorm:"type:text"`
	Category        string `json:"category"`                                 // "technical", "soft_skills", "compliance", "leadership"
	
	// Training details
	TrainingType    string `json:"training_type" gorm:"default:'online'"`    // "online", "classroom", "workshop", "conference"
	Duration        int    `json:"duration" gorm:"default:0"`                // Hours
	MaxParticipants int    `json:"max_participants" gorm:"default:0"`        // 0 = unlimited
	
	// Training content
	Curriculum      string `json:"curriculum" gorm:"type:text"`              // JSON curriculum
	Materials       string `json:"materials" gorm:"type:text"`               // JSON materials list
	Prerequisites   string `json:"prerequisites" gorm:"type:text"`           // JSON prerequisites
	
	// Instructor and provider
	InstructorID    *uint     `json:"instructor_id"`
	Instructor      *Employee `json:"instructor,omitempty" gorm:"foreignKey:InstructorID"`
	Provider        string    `json:"provider"`                              // External training provider
	
	// Scheduling
	StartDate       *time.Time `json:"start_date"`
	EndDate         *time.Time `json:"end_date"`
	Schedule        string     `json:"schedule" gorm:"type:text"`            // JSON schedule
	Location        string     `json:"location"`
	
	// Certification and compliance
	CertificationOffered bool   `json:"certification_offered" gorm:"default:false"`
	CertificationValid   int    `json:"certification_valid" gorm:"default:0"` // Months
	ComplianceRequired   bool   `json:"compliance_required" gorm:"default:false"`
	
	// Cost and budget
	Cost            float64 `json:"cost" gorm:"default:0"`
	Currency        string  `json:"currency" gorm:"default:'USD'"`
	BudgetCode      string  `json:"budget_code"`
	
	// Status and enrollment
	Status          string `json:"status" gorm:"default:'planned'"`          // "planned", "open", "in_progress", "completed", "cancelled"
	EnrollmentCount int    `json:"enrollment_count" gorm:"default:0"`
	CompletionRate  float64 `json:"completion_rate" gorm:"default:0"`        // Percentage
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                       // JSON metadata
}

// TrainingEnrollment represents employee training enrollments
type TrainingEnrollment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Enrollment identification
	EmployeeID      uint     `json:"employee_id" gorm:"not null"`
	Employee        Employee `json:"employee" gorm:"foreignKey:EmployeeID"`
	TrainingID      uint     `json:"training_id" gorm:"not null"`
	Training        Training `json:"training" gorm:"foreignKey:TrainingID"`
	
	// Enrollment details
	EnrolledAt      time.Time  `json:"enrolled_at"`
	EnrolledByID    uint       `json:"enrolled_by_id" gorm:"not null"`
	EnrolledBy      Employee   `json:"enrolled_by" gorm:"foreignKey:EnrolledByID"`
	
	// Progress tracking
	Status          string     `json:"status" gorm:"default:'enrolled'"`     // "enrolled", "in_progress", "completed", "failed", "withdrawn"
	StartedAt       *time.Time `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at"`
	ProgressPercent float64    `json:"progress_percent" gorm:"default:0"`
	
	// Assessment and certification
	Score           float64    `json:"score" gorm:"default:0"`               // Assessment score
	PassingScore    float64    `json:"passing_score" gorm:"default:70"`      // Required passing score
	Attempts        int        `json:"attempts" gorm:"default:0"`
	CertificateIssued bool     `json:"certificate_issued" gorm:"default:false"`
	CertificateNumber string   `json:"certificate_number"`
	CertificateExpiry *time.Time `json:"certificate_expiry"`
	
	// Feedback and evaluation
	Feedback        string `json:"feedback" gorm:"type:text"`
	Rating          float64 `json:"rating" gorm:"default:0"`                 // Training rating by employee
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                       // JSON metadata
}

// TableName returns the table name for Employee model
func (Employee) TableName() string {
	return "employees"
}

// TableName returns the table name for Department model
func (Department) TableName() string {
	return "departments"
}

// TableName returns the table name for Position model
func (Position) TableName() string {
	return "positions"
}

// TableName returns the table name for PerformanceReview model
func (PerformanceReview) TableName() string {
	return "performance_reviews"
}

// TableName returns the table name for Training model
func (Training) TableName() string {
	return "trainings"
}

// TableName returns the table name for TrainingEnrollment model
func (TrainingEnrollment) TableName() string {
	return "training_enrollments"
}
