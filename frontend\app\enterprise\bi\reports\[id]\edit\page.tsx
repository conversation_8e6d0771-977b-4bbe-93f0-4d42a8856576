'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { biApi } from '../../../../../services/enterpriseApi';
import { Report } from '../../../../../types/enterprise';

const EditReportPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const reportId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<Report>>({
    report_code: '',
    report_name: '',
    description: '',
    category: 'operational',
    report_type: 'tabular',
    data_source_id: undefined,
    query_definition: '',
    parameters: '',
    output_format: 'pdf',
    is_public: false,
    is_active: true,
    owner_id: 1,
    retention_days: 90,
    metadata: ''
  });

  useEffect(() => {
    if (reportId) {
      fetchReport();
    }
  }, [reportId]);

  const fetchReport = async () => {
    try {
      setFetchLoading(true);
      const response = await biApi.getReport(reportId);
      const report = response.data;
      setFormData({
        ...report,
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch report');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await biApi.updateReport(reportId, formData);
      router.push(`/enterprise/bi/reports/${reportId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update report');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  if (fetchLoading) return <div className="p-6">Loading report...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Report</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/bi/reports/${reportId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Report Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Report Code *
            </label>
            <input
              type="text"
              name="report_code"
              value={formData.report_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Report Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Report Name *
            </label>
            <input
              type="text"
              name="report_name"
              value={formData.report_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="financial">Financial</option>
              <option value="operational">Operational</option>
              <option value="sales">Sales</option>
              <option value="marketing">Marketing</option>
              <option value="hr">Human Resources</option>
              <option value="compliance">Compliance</option>
              <option value="executive">Executive</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          {/* Report Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Report Type *
            </label>
            <select
              name="report_type"
              value={formData.report_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="tabular">Tabular</option>
              <option value="chart">Chart</option>
              <option value="dashboard">Dashboard</option>
              <option value="summary">Summary</option>
              <option value="detailed">Detailed</option>
              <option value="analytical">Analytical</option>
            </select>
          </div>

          {/* Output Format */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Output Format *
            </label>
            <select
              name="output_format"
              value={formData.output_format}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="pdf">PDF</option>
              <option value="excel">Excel</option>
              <option value="csv">CSV</option>
              <option value="html">HTML</option>
              <option value="json">JSON</option>
              <option value="xml">XML</option>
            </select>
          </div>

          {/* Retention Days */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Retention Days
            </label>
            <input
              type="number"
              name="retention_days"
              value={formData.retention_days}
              onChange={handleChange}
              min="1"
              max="3650"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Query Definition */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Query Definition (SQL/Query)
          </label>
          <textarea
            name="query_definition"
            value={formData.query_definition}
            onChange={handleChange}
            rows={6}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
          />
        </div>

        {/* Parameters */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Parameters (JSON)
          </label>
          <textarea
            name="parameters"
            value={formData.parameters}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Checkboxes */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_public"
              checked={formData.is_public}
              onChange={handleChange}
              className="mr-2"
            />
            Public Report
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleChange}
              className="mr-2"
            />
            Active Report
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/bi/reports/${reportId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Report'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditReportPage;
