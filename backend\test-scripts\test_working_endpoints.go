package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

type TestResult struct {
	Entity     string
	Operation  string
	Endpoint   string
	Success    bool
	StatusCode int
	Error      string
	Response   string
}

var authToken string

func main() {
	fmt.Println("🚀 Testing Working API Endpoints")
	fmt.Println("================================")

	// Step 1: Authenticate
	if !authenticate() {
		fmt.Println("❌ Authentication failed. Exiting.")
		return
	}

	var results []TestResult

	// Test working endpoints based on server logs
	fmt.Println("\n📋 Testing Categories...")
	results = append(results, testCategories()...)

	fmt.Println("\n📋 Testing Agencies...")
	results = append(results, testAgencies()...)

	fmt.Println("\n📋 Testing Tags...")
	results = append(results, testTags()...)

	fmt.Println("\n📋 Testing Users...")
	results = append(results, testUsers()...)

	fmt.Println("\n📋 Testing Summaries...")
	results = append(results, testSummaries()...)

	fmt.Println("\n📋 Testing Tasks (fixed)...")
	results = append(results, testTasks()...)

	// Generate summary
	generateSummary(results)
}

func authenticate() bool {
	loginData := map[string]string{
		"identifier": "<EMAIL>",
		"password":   "password",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := http.Post("http://127.0.0.1:8080/api/v1/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Login error: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		fmt.Printf("Login failed: %d - %s\n", resp.StatusCode, string(body))
		return false
	}

	var loginResponse map[string]interface{}
	json.Unmarshal(body, &loginResponse)

	if token, ok := loginResponse["token"].(string); ok {
		authToken = token
		fmt.Println("✅ Authentication successful")
		return true
	}

	fmt.Println("❌ No token in login response")
	return false
}

func makeRequest(method, endpoint string, data interface{}) TestResult {
	var body io.Reader
	if data != nil {
		jsonData, _ := json.Marshal(data)
		body = bytes.NewBuffer(jsonData)
	}

	req, _ := http.NewRequest(method, "http://127.0.0.1:8080/api/v1"+endpoint, body)
	req.Header.Set("Authorization", "Bearer "+authToken)
	if data != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return TestResult{
			Success: false,
			Error:   fmt.Sprintf("Request error: %v", err),
		}
	}
	defer resp.Body.Close()

	responseBody, _ := io.ReadAll(resp.Body)

	return TestResult{
		Success:    resp.StatusCode >= 200 && resp.StatusCode < 300,
		StatusCode: resp.StatusCode,
		Response:   string(responseBody),
		Error:      fmt.Sprintf("HTTP %d error: %s", resp.StatusCode, string(responseBody)),
	}
}

func testCategories() []TestResult {
	var results []TestResult

	// Test GET categories
	result := makeRequest("GET", "/categories", nil)
	result.Entity = "categories"
	result.Operation = "GET"
	result.Endpoint = "/categories"
	results = append(results, result)

	if result.Success {
		fmt.Println("  ✅ GET /categories")
	} else {
		fmt.Printf("  ❌ GET /categories: %s\n", result.Error)
	}

	return results
}

func testAgencies() []TestResult {
	var results []TestResult

	// Test GET agencies
	result := makeRequest("GET", "/agencies", nil)
	result.Entity = "agencies"
	result.Operation = "GET"
	result.Endpoint = "/agencies"
	results = append(results, result)

	if result.Success {
		fmt.Println("  ✅ GET /agencies")
	} else {
		fmt.Printf("  ❌ GET /agencies: %s\n", result.Error)
	}

	return results
}

func testTags() []TestResult {
	var results []TestResult

	// Test GET tags
	result := makeRequest("GET", "/tags", nil)
	result.Entity = "tags"
	result.Operation = "GET"
	result.Endpoint = "/tags"
	results = append(results, result)

	if result.Success {
		fmt.Println("  ✅ GET /tags")
	} else {
		fmt.Printf("  ❌ GET /tags: %s\n", result.Error)
	}

	return results
}

func testUsers() []TestResult {
	var results []TestResult

	// Test GET users
	result := makeRequest("GET", "/users", nil)
	result.Entity = "users"
	result.Operation = "GET"
	result.Endpoint = "/users"
	results = append(results, result)

	if result.Success {
		fmt.Println("  ✅ GET /users")
	} else {
		fmt.Printf("  ❌ GET /users: %s\n", result.Error)
	}

	return results
}

func testSummaries() []TestResult {
	var results []TestResult

	// Test GET summaries
	result := makeRequest("GET", "/summaries", nil)
	result.Entity = "summaries"
	result.Operation = "GET"
	result.Endpoint = "/summaries"
	results = append(results, result)

	if result.Success {
		fmt.Println("  ✅ GET /summaries")
	} else {
		fmt.Printf("  ❌ GET /summaries: %s\n", result.Error)
	}

	return results
}

func testTasks() []TestResult {
	var results []TestResult

	// Test GET tasks
	result := makeRequest("GET", "/tasks", nil)
	result.Entity = "tasks"
	result.Operation = "GET"
	result.Endpoint = "/tasks"
	results = append(results, result)

	if result.Success {
		fmt.Println("  ✅ GET /tasks")
	} else {
		fmt.Printf("  ❌ GET /tasks: %s\n", result.Error)
	}

	return results
}

func generateSummary(results []TestResult) {
	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("📊 TEST SUMMARY")
	fmt.Println(strings.Repeat("=", 50))

	total := len(results)
	successful := 0
	failed := 0

	for _, result := range results {
		if result.Success {
			successful++
		} else {
			failed++
		}
	}

	fmt.Printf("Total tests: %d\n", total)
	fmt.Printf("✅ Successful: %d\n", successful)
	fmt.Printf("❌ Failed: %d\n", failed)
	fmt.Printf("Success rate: %.1f%%\n", float64(successful)/float64(total)*100)

	if failed > 0 {
		fmt.Println("\n❌ FAILED TESTS:")
		for _, result := range results {
			if !result.Success {
				fmt.Printf("  - %s %s: %s\n", result.Operation, result.Endpoint, result.Error)
			}
		}
	}
}
