'use client'

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircleIcon,
  XMarkIcon,
  BookOpenIcon,
  ArrowLeftIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface RegulationFormData {
  title: string;
  short_title: string;
  type: string;
  status: string;
  description: string;
  content: string;
  notes: string;

  // Legal identifiers
  public_law_number: string;
  regulatory_identifier: string;
  cfr_title: string;
  usc_title: string;
  docket_number: string;

  // Hierarchical structure
  hierarchy_level: string;
  chapter_number: string;
  subchapter: string;
  part_number: string;
  section_number: string;
  subsection: string;
  parent_id: string;

  // Dates
  enactment_date: string;
  effective_date: string;
  termination_date: string;
  publication_date: string;
  created_at: string;  // Add created_at for display
  updated_at: string;  // Add updated_at for display

  // Relationships
  agency_id: string;
  agency_name: string;  // From regulation.agency.name
  agency_short_name: string;  // From regulation.agency.short_name
  category_id: string;
  parent_regulation_id: string;
  supersedes_regulation_id: string;
  created_by_id: string;  // From regulation.created_by_id
  created_by_name: string;  // From regulation.created_by full name

  // Current document version info
  current_document_version_id: string;  // From regulation.current_document_version_id
  version_number: string;  // From regulation.current_document_version.version_number
  version_notes: string;  // From regulation.current_document_version.notes
  summary_of_changes: string;  // From regulation.current_document_version.summary_of_changes
  is_official: boolean;  // From regulation.current_document_version.is_official

  // Hierarchical structure additional
  order_in_parent: number;  // From regulation.order_in_parent

  // Metadata
  is_significant: boolean;
  tags: string[];

  // Legacy fields for compatibility
  cfr_citation: string;
  public_comment_period: boolean;
  comment_end_date: string;
  regulatory_impact_analysis: boolean;
  small_business_impact: boolean;
  environmental_impact: boolean;
  federalism_implications: boolean;
}

// Helper function to get today's date in YYYY-MM-DD format
const getTodayDate = () => {
  const today = new Date();
  return today.toISOString().split('T')[0];
};

// Helper function to get smart defaults for empty fields
const getSmartDefaults = () => {
  return {
    cfr_title: '40', // Default to EPA regulations
    usc_title: '42', // Default to Public Health
    chapter_number: '1',
    publication_date: getTodayDate(),
    effective_date: getTodayDate(),
  };
};

const EditRegulationPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [regulation, setRegulation] = useState<any>(null);
  const [agencies, setAgencies] = useState<any[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [regulations, setRegulations] = useState<any[]>([]);
  const [formData, setFormData] = useState<RegulationFormData>({
    title: '',
    short_title: '',
    type: 'regulation',
    status: 'draft',
    description: '',
    content: '',
    notes: '',

    // Legal identifiers
    public_law_number: '',
    regulatory_identifier: '',
    cfr_title: '',
    usc_title: '',
    docket_number: '',

    // Hierarchical structure
    hierarchy_level: 'regulation',
    chapter_number: '',
    subchapter: '',
    part_number: '',
    section_number: '',
    subsection: '',
    parent_id: '',
    order_in_parent: 0,

    // Dates
    enactment_date: '',
    effective_date: '',
    termination_date: '',
    publication_date: '',
    created_at: '',
    updated_at: '',

    // Relationships
    agency_id: '',
    agency_name: '',
    agency_short_name: '',
    category_id: '',
    parent_regulation_id: '',
    supersedes_regulation_id: '',
    created_by_id: '',
    created_by_name: '',

    // Current document version info
    current_document_version_id: '',
    version_number: '',
    version_notes: '',
    summary_of_changes: '',
    is_official: false,

    // Metadata
    is_significant: false,
    tags: [],

    // Legacy fields for compatibility
    cfr_citation: '',
    public_comment_period: false,
    comment_end_date: '',
    regulatory_impact_analysis: false,
    small_business_impact: false,
    environmental_impact: false,
    federalism_implications: false,
  });
  const [tagInput, setTagInput] = useState('');

  const regulationId = params?.id as string;

  useEffect(() => {
    if (!isAuthenticated || (user?.role !== 'admin' && user?.role !== 'editor')) {
      router.push('/dashboard');
      return;
    }

    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch regulation using authenticated endpoint to get all data
        const regulationResponse = await apiService.getAuthenticatedRegulation(parseInt(regulationId));
        console.log('DEBUG: Raw regulation response:', regulationResponse);

        // The API might return data wrapped in a data field or directly
        const regulationData = regulationResponse.regulation || regulationResponse.data || regulationResponse;
        console.log('DEBUG: Processed regulation data:', regulationData);
        setRegulation(regulationData);

        // Get smart defaults for empty fields
        const smartDefaults = getSmartDefaults();

        // Populate form with ALL regulation data from API response
        console.log('DEBUG: About to populate form with data:', regulationData);
        console.log('DEBUG: Agency data:', regulationData.agency);
        console.log('DEBUG: Created by data:', regulationData.created_by);
        console.log('DEBUG: Version data:', regulationData.current_document_version);

        setFormData({
          // Basic information
          title: regulationData.title || '',
          short_title: regulationData.short_title || '',
          type: regulationData.type || 'regulation',
          status: regulationData.status || 'draft',
          description: regulationData.description || '',
          content: regulationData.content || '',
          notes: regulationData.notes || '',

          // Legal identifiers - preload existing values, fallback to smart defaults only if empty
          public_law_number: regulationData.public_law_number || '',
          regulatory_identifier: regulationData.regulatory_identifier || '',
          cfr_title: regulationData.cfr_title || smartDefaults.cfr_title,
          usc_title: regulationData.usc_title || smartDefaults.usc_title,
          docket_number: regulationData.docket_number || '',

          // Hierarchical structure - preload ALL existing values
          hierarchy_level: regulationData.hierarchy_level || 'regulation',
          chapter_number: regulationData.chapter_number || smartDefaults.chapter_number,
          subchapter: regulationData.subchapter || '',
          part_number: regulationData.part_number || '',
          section_number: regulationData.section_number || '',
          subsection: regulationData.subsection || '',
          parent_id: regulationData.parent_id?.toString() || '',
          order_in_parent: regulationData.order_in_parent || 0,

          // Dates - preload ALL existing dates
          enactment_date: regulationData.enactment_date ? regulationData.enactment_date.split('T')[0] : '',
          effective_date: regulationData.effective_date ? regulationData.effective_date.split('T')[0] : smartDefaults.effective_date,
          termination_date: regulationData.termination_date ? regulationData.termination_date.split('T')[0] : '',
          publication_date: regulationData.publication_date ? regulationData.publication_date.split('T')[0] : smartDefaults.publication_date,
          created_at: regulationData.created_at ? regulationData.created_at.split('T')[0] : '',
          updated_at: regulationData.updated_at ? regulationData.updated_at.split('T')[0] : '',

          // Relationships - preload ALL relationship data
          agency_id: regulationData.agency_id?.toString() || '',
          agency_name: regulationData.agency?.name || '',
          agency_short_name: regulationData.agency?.short_name || '',
          category_id: regulationData.category_id?.toString() || '',
          parent_regulation_id: regulationData.parent_regulation_id?.toString() || '',
          supersedes_regulation_id: regulationData.supersedes_regulation_id?.toString() || '',
          created_by_id: regulationData.created_by_id?.toString() || '',
          created_by_name: regulationData.created_by ? `${regulationData.created_by.first_name} ${regulationData.created_by.last_name}` : '',

          // Current document version info - preload ALL version data
          current_document_version_id: regulationData.current_document_version_id?.toString() || '',
          version_number: regulationData.current_document_version?.version_number || '',
          version_notes: regulationData.current_document_version?.notes || '',
          summary_of_changes: regulationData.current_document_version?.summary_of_changes || '',
          is_official: regulationData.current_document_version?.is_official || false,

          // Metadata
          is_significant: regulationData.is_significant || false,
          tags: regulationData.tags || [],

          // Legacy fields for compatibility
          cfr_citation: regulationData.cfr_citation || '',
          public_comment_period: regulationData.public_comment_period || false,
          comment_end_date: regulationData.comment_end_date ? regulationData.comment_end_date.split('T')[0] : '',
          regulatory_impact_analysis: regulationData.regulatory_impact_analysis || false,
          small_business_impact: regulationData.small_business_impact || false,
          environmental_impact: regulationData.environmental_impact || false,
          federalism_implications: regulationData.federalism_implications || false,
        });

        // Fetch agencies
        const agenciesResponse = await apiService.getAgencies({ per_page: 100 });
        setAgencies(agenciesResponse.data);

        // Fetch categories
        const categoriesResponse = await apiService.getCategories();
        setCategories(categoriesResponse.data);

        // Fetch existing regulations for parent/supersedes relationships (excluding current regulation)
        const regulationsResponse = await apiService.getRegulations({ per_page: 100 });
        setRegulations(regulationsResponse.data.filter(r => r.id !== regulationData.id));
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch regulation data');
      } finally {
        setLoading(false);
      }
    };

    if (regulationId) {
      fetchData();
    }
  }, [regulationId, isAuthenticated, user, router]);

  // Regenerate Public Law Number
  const regeneratePublicLawNumber = async () => {
    try {
      const response = await apiService.getPublicLawNumber();
      const data = response.data;
      setFormData(prev => ({
        ...prev,
        public_law_number: data.public_law_number || ''
      }));
    } catch (err) {
      console.error('Error regenerating public law number:', err);
    }
  };

  // Regenerate Regulatory Identifier (RIN)
  const regenerateRegulatoryIdentifier = async (agencyId: string) => {
    if (!agencyId) return;

    try {
      const tokenKey = process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token';
      const response = await fetch(`/api/v1/preloading/regulatory-identifier?agency_id=${agencyId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem(tokenKey)}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFormData(prev => ({
          ...prev,
          regulatory_identifier: data.regulatory_identifier || ''
        }));
      }
    } catch (err) {
      console.error('Error regenerating regulatory identifier:', err);
    }
  };

  // Regenerate Regulation Docket Number
  const regenerateRegulationDocketNumber = async (agencyId: string) => {
    if (!agencyId) return;

    try {
      const tokenKey = process.env.NEXT_PUBLIC_JWT_STORAGE_KEY || 'federal_register_token';
      const response = await fetch(`/api/v1/preloading/regulation-docket-number?agency_id=${agencyId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem(tokenKey)}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setFormData(prev => ({
          ...prev,
          docket_number: data.docket_number || ''
        }));
      }
    } catch (err) {
      console.error('Error regenerating regulation docket number:', err);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));

      // Regenerate identifiers when agency changes
      if (name === 'agency_id' && value) {
        regenerateRegulatoryIdentifier(value);
        regenerateRegulationDocketNumber(value);
      }
    }
  };

  const handleAddTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const generateCFRCitation = () => {
    const { chapter_number, part_number, section_number } = formData;
    if (chapter_number && part_number) {
      let citation = `${chapter_number} CFR ${part_number}`;
      if (section_number) {
        citation += `.${section_number}`;
      }
      setFormData(prev => ({ ...prev, cfr_citation: citation }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!regulation) return;

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      // Use the same structure as create page for consistency
      const updateData = {
        title: formData.title,
        short_title: formData.short_title || undefined,
        type: formData.type,
        status: formData.status,
        description: formData.description || undefined,
        content: formData.content || undefined,
        notes: formData.notes || undefined,
        hierarchy_level: formData.hierarchy_level || undefined,
        public_law_number: formData.public_law_number || undefined,
        regulatory_identifier: formData.regulatory_identifier || undefined,
        cfr_title: formData.cfr_title || undefined,
        usc_title: formData.usc_title || undefined,
        docket_number: formData.docket_number || undefined,
        chapter_number: formData.chapter_number || undefined,
        subchapter: formData.subchapter || undefined,
        part_number: formData.part_number || undefined,
        section_number: formData.section_number || undefined,
        subsection: formData.subsection || undefined,
        enactment_date: formData.enactment_date || undefined,
        effective_date: formData.effective_date || undefined,
        termination_date: formData.termination_date || undefined,
        publication_date: formData.publication_date || undefined,
        is_significant: formData.is_significant,
        tags: formData.tags,
        agency_id: parseInt(formData.agency_id),
        category_id: formData.category_id ? parseInt(formData.category_id) : undefined,
        parent_regulation_id: formData.parent_regulation_id ? parseInt(formData.parent_regulation_id) : undefined,
        supersedes_regulation_id: formData.supersedes_regulation_id ? parseInt(formData.supersedes_regulation_id) : undefined,
        parent_id: formData.parent_id ? parseInt(formData.parent_id) : undefined,
      };

      await apiService.updateRegulation(parseInt(regulationId), updateData);
      setSuccess('Regulation updated successfully!');
      
      // Redirect to regulation detail page after a short delay
      setTimeout(() => {
        router.push(`/regulations/${regulationId}`);
      }, 1500);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update regulation');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error && !regulation) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Regulation not found</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link
              href="/regulations"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Regulations
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href={`/regulations/${regulationId}`}
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Regulation
          </Link>
          
          <div className="flex items-center">
            <PencilIcon className="h-8 w-8 text-primary-600 mr-3" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Regulation</h1>
              <p className="text-gray-600 mt-1">Update regulation information and content</p>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XMarkIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Preloaded Information Display */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Regulation Information (Preloaded)</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">ID:</span>
                  <span className="ml-2 text-gray-600">{regulation?.id || 'N/A'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Created:</span>
                  <span className="ml-2 text-gray-600">{formData.created_at || 'N/A'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Updated:</span>
                  <span className="ml-2 text-gray-600">{formData.updated_at || 'N/A'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Agency:</span>
                  <span className="ml-2 text-gray-600">{formData.agency_name} ({formData.agency_short_name})</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Created By:</span>
                  <span className="ml-2 text-gray-600">{formData.created_by_name || 'N/A'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Version:</span>
                  <span className="ml-2 text-gray-600">{formData.version_number || 'N/A'}</span>
                </div>
              </div>
            </div>

            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Regulation Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  required
                  value={formData.title}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter regulation title"
                />
              </div>

              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                  Type <span className="text-red-500">*</span>
                </label>
                <select
                  id="type"
                  name="type"
                  required
                  value={formData.type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="regulation">Regulation</option>
                  <option value="rule">Rule</option>
                  <option value="law">Law</option>
                  <option value="directive">Directive</option>
                  <option value="order">Order</option>
                </select>
              </div>

              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="draft">Draft</option>
                  <option value="under_review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="published">Published</option>
                  <option value="effective">Effective</option>
                  <option value="terminated">Terminated</option>
                  <option value="archived">Archived</option>
                </select>
              </div>

              <div>
                <label htmlFor="agency_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Agency
                </label>
                <select
                  id="agency_id"
                  name="agency_id"
                  value={formData.agency_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select an agency (optional)</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id}>
                      {agency.name} ({agency.short_name})
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  id="category_id"
                  name="category_id"
                  value={formData.category_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Select a category (optional)</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Legal Identifiers */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Legal Identifiers</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="short_title" className="block text-sm font-medium text-gray-700 mb-2">
                    Short Title
                  </label>
                  <input
                    type="text"
                    id="short_title"
                    name="short_title"
                    value={formData.short_title}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Enter short title"
                  />
                </div>

                <div>
                  <label htmlFor="public_law_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Public Law Number
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="public_law_number"
                      name="public_law_number"
                      value={formData.public_law_number}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., 117-58"
                    />
                    <button
                      type="button"
                      onClick={regeneratePublicLawNumber}
                      className="px-3 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500"
                      title="Generate new Public Law Number"
                    >
                      🔄
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="regulatory_identifier" className="block text-sm font-medium text-gray-700 mb-2">
                    Regulatory Identifier (RIN)
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="regulatory_identifier"
                      name="regulatory_identifier"
                      value={formData.regulatory_identifier}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., 2040-AF33"
                    />
                    <button
                      type="button"
                      onClick={() => regenerateRegulatoryIdentifier(formData.agency_id)}
                      disabled={!formData.agency_id}
                      className="px-3 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Generate new Regulatory Identifier (requires agency selection)"
                    >
                      🔄
                    </button>
                  </div>
                </div>

                <div>
                  <label htmlFor="cfr_title" className="block text-sm font-medium text-gray-700 mb-2">
                    CFR Title
                  </label>
                  <input
                    type="text"
                    id="cfr_title"
                    name="cfr_title"
                    value={formData.cfr_title}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 40"
                  />
                </div>

                <div>
                  <label htmlFor="usc_title" className="block text-sm font-medium text-gray-700 mb-2">
                    USC Title
                  </label>
                  <input
                    type="text"
                    id="usc_title"
                    name="usc_title"
                    value={formData.usc_title}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 42"
                  />
                </div>

                <div>
                  <label htmlFor="docket_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Docket Number
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="text"
                      id="docket_number"
                      name="docket_number"
                      value={formData.docket_number}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., EPA-REG-0001"
                    />
                    <button
                      type="button"
                      onClick={() => regenerateRegulationDocketNumber(formData.agency_id)}
                      disabled={!formData.agency_id}
                      className="px-3 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Generate new Docket Number (requires agency selection)"
                    >
                      🔄
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* CFR Structure */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">CFR Structure</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                  <label htmlFor="chapter_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Chapter
                  </label>
                  <input
                    type="text"
                    id="chapter_number"
                    name="chapter_number"
                    value={formData.chapter_number}
                    onChange={handleChange}
                    onBlur={generateCFRCitation}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 40"
                  />
                </div>

                <div>
                  <label htmlFor="subchapter" className="block text-sm font-medium text-gray-700 mb-2">
                    Subchapter
                  </label>
                  <input
                    type="text"
                    id="subchapter"
                    name="subchapter"
                    value={formData.subchapter}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., A"
                  />
                </div>

                <div>
                  <label htmlFor="part_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Part
                  </label>
                  <input
                    type="text"
                    id="part_number"
                    name="part_number"
                    value={formData.part_number}
                    onChange={handleChange}
                    onBlur={generateCFRCitation}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 52"
                  />
                </div>

                <div>
                  <label htmlFor="section_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Section
                  </label>
                  <input
                    type="text"
                    id="section_number"
                    name="section_number"
                    value={formData.section_number}
                    onChange={handleChange}
                    onBlur={generateCFRCitation}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., 21"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="subsection" className="block text-sm font-medium text-gray-700 mb-2">
                    Subsection
                  </label>
                  <input
                    type="text"
                    id="subsection"
                    name="subsection"
                    value={formData.subsection}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="e.g., (a)(1)"
                  />
                </div>

                <div>
                  <label htmlFor="cfr_citation" className="block text-sm font-medium text-gray-700 mb-2">
                    CFR Citation
                  </label>
                  <div className="flex">
                    <input
                      type="text"
                      id="cfr_citation"
                      name="cfr_citation"
                      value={formData.cfr_citation}
                      onChange={handleChange}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="e.g., 40 CFR 52.21"
                    />
                    <button
                      type="button"
                      onClick={generateCFRCitation}
                      className="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-md text-sm text-gray-600 hover:bg-gray-200"
                    >
                      Generate
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={3}
                value={formData.description}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Brief description of the regulation..."
              />
            </div>

            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                Regulation Content *
              </label>
              <textarea
                id="content"
                name="content"
                rows={12}
                required
                value={formData.content}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Full regulation text (supports Markdown)..."
              />
              <p className="mt-1 text-sm text-gray-500">
                You can use Markdown formatting for rich text content.
              </p>
            </div>

            {/* Notes */}
            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                placeholder="Internal notes about this regulation..."
              />
            </div>

            {/* Relationships */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="parent_regulation_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Parent Regulation
                </label>
                <select
                  id="parent_regulation_id"
                  name="parent_regulation_id"
                  value={formData.parent_regulation_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">No parent regulation</option>
                  {regulations.map((regulation) => (
                    <option key={regulation.id} value={regulation.id}>
                      {regulation.cfr_citation || regulation.title}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="supersedes_regulation_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Supersedes Regulation
                </label>
                <select
                  id="supersedes_regulation_id"
                  name="supersedes_regulation_id"
                  value={formData.supersedes_regulation_id}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">Does not supersede any regulation</option>
                  {regulations.map((regulation) => (
                    <option key={regulation.id} value={regulation.id}>
                      {regulation.cfr_citation || regulation.title}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Dates */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Important Dates</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="enactment_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Enactment Date
                  </label>
                  <input
                    type="date"
                    id="enactment_date"
                    name="enactment_date"
                    value={formData.enactment_date}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="publication_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Publication Date
                  </label>
                  <input
                    type="date"
                    id="publication_date"
                    name="publication_date"
                    value={formData.publication_date}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="effective_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Effective Date
                  </label>
                  <input
                    type="date"
                    id="effective_date"
                    name="effective_date"
                    value={formData.effective_date}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>

                <div>
                  <label htmlFor="termination_date" className="block text-sm font-medium text-gray-700 mb-2">
                    Termination Date
                  </label>
                  <input
                    type="date"
                    id="termination_date"
                    name="termination_date"
                    value={formData.termination_date}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
            </div>

            {/* Metadata */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Metadata</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_significant"
                    name="is_significant"
                    checked={formData.is_significant}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="is_significant" className="ml-2 block text-sm text-gray-900">
                    Significant Regulation
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="public_comment_period"
                    name="public_comment_period"
                    checked={formData.public_comment_period}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="public_comment_period" className="ml-2 block text-sm text-gray-900">
                    Public Comment Period Required
                  </label>
                </div>

                {formData.public_comment_period && (
                  <div>
                    <label htmlFor="comment_end_date" className="block text-sm font-medium text-gray-700 mb-2">
                      Comment Period End Date
                    </label>
                    <input
                      type="date"
                      id="comment_end_date"
                      name="comment_end_date"
                      value={formData.comment_end_date}
                      onChange={handleChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    />
                  </div>
                )}

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="regulatory_impact_analysis"
                    name="regulatory_impact_analysis"
                    checked={formData.regulatory_impact_analysis}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="regulatory_impact_analysis" className="ml-2 block text-sm text-gray-900">
                    Regulatory Impact Analysis Required
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="small_business_impact"
                    name="small_business_impact"
                    checked={formData.small_business_impact}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="small_business_impact" className="ml-2 block text-sm text-gray-900">
                    Small Business Impact Analysis
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="environmental_impact"
                    name="environmental_impact"
                    checked={formData.environmental_impact}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="environmental_impact" className="ml-2 block text-sm text-gray-900">
                    Environmental Impact Assessment
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="federalism_implications"
                    name="federalism_implications"
                    checked={formData.federalism_implications}
                    onChange={handleChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <label htmlFor="federalism_implications" className="ml-2 block text-sm text-gray-900">
                    Federalism Implications
                  </label>
                </div>
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Tags</h3>
              <div className="space-y-4">
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Add a tag"
                  />
                  <button
                    type="button"
                    onClick={handleAddTag}
                    className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    Add
                  </button>
                </div>

                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-2 text-primary-600 hover:text-primary-800"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Link
                href={`/regulations/${regulationId}`}
                className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={saving}
                className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {saving ? 'Updating...' : 'Update Regulation'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default EditRegulationPage;
