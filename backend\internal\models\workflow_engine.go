package models

import (
	"time"

	"gorm.io/gorm"
)

// WorkflowType represents the type of workflow
type WorkflowType string

const (
	WorkflowTypeSequential  WorkflowType = "sequential"  // Steps must be completed in order
	WorkflowTypeParallel    WorkflowType = "parallel"    // Steps can be completed simultaneously
	WorkflowTypeConditional WorkflowType = "conditional" // Steps based on conditions
	WorkflowTypeMixed       WorkflowType = "mixed"       // Combination of above
)

// WorkflowStatus represents the current status of a workflow
type WorkflowStatus string

const (
	WorkflowStatusDraft      WorkflowStatus = "draft"
	WorkflowStatusActive     WorkflowStatus = "active"
	WorkflowStatusInProgress WorkflowStatus = "in_progress"
	WorkflowStatusCompleted  WorkflowStatus = "completed"
	WorkflowStatusCancelled  WorkflowStatus = "cancelled"
	WorkflowStatusSuspended  WorkflowStatus = "suspended"
	WorkflowStatusFailed     WorkflowStatus = "failed"
)

// StepType represents the type of workflow step
type StepType string

const (
	StepTypeApproval     StepType = "approval"     // Requires approval from user(s)
	StepTypeReview       StepType = "review"       // Review step
	StepTypeNotification StepType = "notification" // Send notification
	StepTypeAction       StepType = "action"       // Automated action
	StepTypeCondition    StepType = "condition"    // Conditional branching
	StepTypeScript       StepType = "script"       // Custom script execution
	StepTypeIntegration  StepType = "integration"  // External system integration
)

// StepStatus represents the status of a workflow step
type StepStatus string

const (
	StepStatusPending    StepStatus = "pending"
	StepStatusInProgress StepStatus = "in_progress"
	StepStatusCompleted  StepStatus = "completed"
	StepStatusSkipped    StepStatus = "skipped"
	StepStatusFailed     StepStatus = "failed"
	StepStatusCancelled  StepStatus = "cancelled"
	StepStatusEscalated  StepStatus = "escalated"
)

// EscalationTrigger represents when escalation should occur
type EscalationTrigger string

const (
	EscalationTriggerTime     EscalationTrigger = "time"     // Time-based escalation
	EscalationTriggerOverdue  EscalationTrigger = "overdue"  // SLA breach
	EscalationTriggerManual   EscalationTrigger = "manual"   // Manual escalation
	EscalationTriggerFailure  EscalationTrigger = "failure"  // Step failure
	EscalationTriggerInactive EscalationTrigger = "inactive" // No activity
)

// WorkflowTemplate represents a reusable workflow template
type WorkflowTemplate struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Template identification
	Name        string         `json:"name" gorm:"not null"`           // "Document Review Workflow", "Regulation Publishing Workflow", etc. (required)
	Description string         `json:"description" gorm:"type:text"`   // "Standard workflow for document review and approval", etc. (optional)
	Category    string         `json:"category"`                       // "Document Management", "Regulation Review", etc. (optional)
	Version     string         `json:"version" gorm:"default:'1.0'"`   // "1.0", "2.0", etc.
	Type        WorkflowType   `json:"type" gorm:"default:'approval'"` // "approval", "review", "publication", etc.
	Status      WorkflowStatus `json:"status" gorm:"default:'draft'"`

	// Template configuration
	DefaultSLAHours       int  `json:"default_sla_hours" gorm:"default:48"`
	AllowParallelSteps    bool `json:"allow_parallel_steps" gorm:"default:false"`
	RequireAllApprovals   bool `json:"require_all_approvals" gorm:"default:true"`
	AutoAdvanceOnApproval bool `json:"auto_advance_on_approval" gorm:"default:true"`
	EnableEscalation      bool `json:"enable_escalation" gorm:"default:true"`
	EscalationHours       int  `json:"escalation_hours" gorm:"default:48"`

	// Template metadata
	CreatedByID uint   `json:"created_by_id" gorm:"not null"`
	CreatedBy   User   `json:"created_by" gorm:"foreignKey:CreatedByID"`
	Tags        string `json:"tags" gorm:"type:text"` // JSON array of tags

	// Template definition (JSON)
	StepDefinitions   string `json:"step_definitions" gorm:"type:text"`   // JSON workflow steps
	ConditionRules    string `json:"condition_rules" gorm:"type:text"`    // JSON condition rules
	EscalationRules   string `json:"escalation_rules" gorm:"type:text"`   // JSON escalation rules
	NotificationRules string `json:"notification_rules" gorm:"type:text"` // JSON notification rules

	// Usage statistics
	UsageCount    int        `json:"usage_count" gorm:"default:0"`
	LastUsedAt    *time.Time `json:"last_used_at"`
	SuccessRate   float64    `json:"success_rate" gorm:"default:0"`
	AvgCompletion int        `json:"avg_completion_hours" gorm:"default:0"`

	// Relationships
	WorkflowInstances []WorkflowInstance `json:"workflow_instances,omitempty" gorm:"foreignKey:TemplateID"`
}

// WorkflowInstance represents an active workflow instance
type WorkflowInstance struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Instance identification
	InstanceID string         `json:"instance_id" gorm:"uniqueIndex;not null"`
	Name       string         `json:"name" gorm:"not null"`
	Status     WorkflowStatus `json:"status" gorm:"default:'draft'"`
	Priority   int            `json:"priority" gorm:"default:3"` // 1=High, 2=Medium, 3=Low

	// Template relationship
	TemplateID uint             `json:"template_id" gorm:"not null"`
	Template   WorkflowTemplate `json:"template" gorm:"foreignKey:TemplateID"`

	// Target entity (what this workflow is for)
	EntityType string `json:"entity_type" gorm:"not null"` // "document", "regulation", etc.
	EntityID   uint   `json:"entity_id" gorm:"not null"`

	// Workflow progress
	CurrentStepID   *uint         `json:"current_step_id"`
	CurrentStep     *WorkflowStep `json:"current_step,omitempty" gorm:"foreignKey:CurrentStepID"`
	CompletedSteps  int           `json:"completed_steps" gorm:"default:0"`
	TotalSteps      int           `json:"total_steps" gorm:"not null"`
	ProgressPercent int           `json:"progress_percent" gorm:"default:0"`

	// Timing and SLA
	StartedAt       *time.Time `json:"started_at"`
	CompletedAt     *time.Time `json:"completed_at"`
	DueAt           *time.Time `json:"due_at"`
	SLAHours        int        `json:"sla_hours"`
	IsOverdue       bool       `json:"is_overdue" gorm:"default:false"`
	EscalationLevel int        `json:"escalation_level" gorm:"default:0"`

	// Workflow context
	InitiatedByID uint  `json:"initiated_by_id" gorm:"not null"`
	InitiatedBy   User  `json:"initiated_by" gorm:"foreignKey:InitiatedByID"`
	AssignedToID  *uint `json:"assigned_to_id"`
	AssignedTo    *User `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToID"`

	// Workflow data (JSON)
	InputData   string `json:"input_data" gorm:"type:text"`   // Initial workflow data
	CurrentData string `json:"current_data" gorm:"type:text"` // Current workflow state
	OutputData  string `json:"output_data" gorm:"type:text"`  // Final workflow output
	Variables   string `json:"variables" gorm:"type:text"`    // Workflow variables

	// Metadata
	Notes              string `json:"notes" gorm:"type:text"`
	CancellationReason string `json:"cancellation_reason"`

	// Relationships
	Steps         []WorkflowStep         `json:"steps,omitempty" gorm:"foreignKey:InstanceID"`
	Escalations   []WorkflowEscalation   `json:"escalations,omitempty" gorm:"foreignKey:InstanceID"`
	Notifications []WorkflowNotification `json:"notifications,omitempty" gorm:"foreignKey:InstanceID"`
	AuditLogs     []WorkflowAuditLog     `json:"audit_logs,omitempty" gorm:"foreignKey:InstanceID"`
}

// WorkflowStep represents a step in a workflow instance
type WorkflowStep struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Step identification
	StepID    string     `json:"step_id" gorm:"not null"` // Unique within workflow
	Name      string     `json:"name" gorm:"not null"`
	Type      StepType   `json:"type" gorm:"not null"`
	Status    StepStatus `json:"status" gorm:"default:'pending'"`
	StepOrder int        `json:"step_order" gorm:"not null"`

	// Workflow relationship
	InstanceID uint             `json:"instance_id" gorm:"not null"`
	Instance   WorkflowInstance `json:"instance" gorm:"foreignKey:InstanceID"`

	// Step configuration
	IsRequired     bool   `json:"is_required" gorm:"default:true"`
	AllowSkip      bool   `json:"allow_skip" gorm:"default:false"`
	RequireComment bool   `json:"require_comment" gorm:"default:false"`
	AutoComplete   bool   `json:"auto_complete" gorm:"default:false"`
	ParallelGroup  string `json:"parallel_group"` // Group for parallel execution

	// Conditions and rules (JSON)
	PreConditions    string `json:"pre_conditions" gorm:"type:text"`    // JSON conditions to start
	PostConditions   string `json:"post_conditions" gorm:"type:text"`   // JSON conditions to complete
	ValidationRules  string `json:"validation_rules" gorm:"type:text"`  // JSON validation rules
	ActionDefinition string `json:"action_definition" gorm:"type:text"` // JSON action configuration

	// Assignment and approval
	AssignedToID      *uint `json:"assigned_to_id"`
	AssignedTo        *User `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToID"`
	AssignedGroupID   *uint `json:"assigned_group_id"`
	RequiredApprovers int   `json:"required_approvers" gorm:"default:1"`
	ReceivedApprovals int   `json:"received_approvals" gorm:"default:0"`

	// Timing and SLA
	StartedAt   *time.Time `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	DueAt       *time.Time `json:"due_at"`
	SLAHours    int        `json:"sla_hours" gorm:"default:24"`
	IsOverdue   bool       `json:"is_overdue" gorm:"default:false"`

	// Step execution
	AttemptCount    int    `json:"attempt_count" gorm:"default:0"`
	MaxAttempts     int    `json:"max_attempts" gorm:"default:3"`
	LastError       string `json:"last_error"`
	CompletedByID   *uint  `json:"completed_by_id"`
	CompletedBy     *User  `json:"completed_by,omitempty" gorm:"foreignKey:CompletedByID"`
	CompletionNotes string `json:"completion_notes" gorm:"type:text"`

	// Step data
	InputData  string `json:"input_data" gorm:"type:text"`  // Data passed to step
	OutputData string `json:"output_data" gorm:"type:text"` // Data produced by step

	// Relationships
	Approvals   []WorkflowApproval   `json:"approvals,omitempty" gorm:"foreignKey:StepID"`
	Escalations []WorkflowEscalation `json:"escalations,omitempty" gorm:"foreignKey:StepID"`
}

// WorkflowApproval represents an approval within a workflow step
type WorkflowApproval struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Approval identification
	StepID uint         `json:"step_id" gorm:"not null"`
	Step   WorkflowStep `json:"step" gorm:"foreignKey:StepID"`

	// Approver information
	ApproverID    uint   `json:"approver_id" gorm:"not null"`
	Approver      User   `json:"approver" gorm:"foreignKey:ApproverID"`
	ApproverRole  string `json:"approver_role"`
	ApprovalOrder int    `json:"approval_order" gorm:"default:1"`

	// Approval details
	Status      StepStatus `json:"status" gorm:"default:'pending'"`
	Decision    string     `json:"decision"` // "approved", "rejected", "delegated"
	Comments    string     `json:"comments" gorm:"type:text"`
	ApprovedAt  *time.Time `json:"approved_at"`
	RequestedAt time.Time  `json:"requested_at"`
	DueAt       *time.Time `json:"due_at"`

	// Delegation
	DelegatedToID    *uint      `json:"delegated_to_id"`
	DelegatedTo      *User      `json:"delegated_to,omitempty" gorm:"foreignKey:DelegatedToID"`
	DelegatedAt      *time.Time `json:"delegated_at"`
	DelegationReason string     `json:"delegation_reason"`

	// Metadata
	IPAddress    string `json:"ip_address"`
	UserAgent    string `json:"user_agent"`
	ApprovalData string `json:"approval_data" gorm:"type:text"` // JSON additional data
}

// WorkflowEscalation represents escalation rules and instances
type WorkflowEscalation struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Escalation target
	InstanceID *uint             `json:"instance_id"`
	Instance   *WorkflowInstance `json:"instance,omitempty" gorm:"foreignKey:InstanceID"`
	StepID     *uint             `json:"step_id"`
	Step       *WorkflowStep     `json:"step,omitempty" gorm:"foreignKey:StepID"`

	// Escalation configuration
	TriggerType     EscalationTrigger `json:"trigger_type" gorm:"not null"`
	TriggerAfter    int               `json:"trigger_after"` // Hours/attempts before escalation
	EscalationLevel int               `json:"escalation_level" gorm:"not null"`
	MaxLevel        int               `json:"max_level" gorm:"default:3"`

	// Escalation targets
	EscalateToID    *uint  `json:"escalate_to_id"`
	EscalateTo      *User  `json:"escalate_to,omitempty" gorm:"foreignKey:EscalateToID"`
	EscalateToGroup string `json:"escalate_to_group"`
	EscalateToRole  string `json:"escalate_to_role"`

	// Escalation execution
	IsTriggered   bool       `json:"is_triggered" gorm:"default:false"`
	TriggeredAt   *time.Time `json:"triggered_at"`
	ResolvedAt    *time.Time `json:"resolved_at"`
	Resolution    string     `json:"resolution"`
	EscalatedByID *uint      `json:"escalated_by_id"`
	EscalatedBy   *User      `json:"escalated_by,omitempty" gorm:"foreignKey:EscalatedByID"`

	// Escalation actions (JSON)
	Actions              string `json:"actions" gorm:"type:text"` // JSON escalation actions
	NotificationTemplate string `json:"notification_template" gorm:"type:text"`

	// Metadata
	Reason string `json:"reason"`
	Notes  string `json:"notes" gorm:"type:text"`
}

// WorkflowNotification represents notifications sent during workflow execution
type WorkflowNotification struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Notification target
	InstanceID uint             `json:"instance_id" gorm:"not null"`
	Instance   WorkflowInstance `json:"instance" gorm:"foreignKey:InstanceID"`
	StepID     *uint            `json:"step_id"`
	Step       *WorkflowStep    `json:"step,omitempty" gorm:"foreignKey:StepID"`

	// Notification details
	Type       string `json:"type" gorm:"not null"`        // "email", "sms", "webhook", "in_app"
	Event      string `json:"event" gorm:"not null"`       // "step_started", "step_completed", "escalated", etc.
	Recipients string `json:"recipients" gorm:"type:text"` // JSON array of recipients
	Subject    string `json:"subject"`
	Message    string `json:"message" gorm:"type:text"`
	Template   string `json:"template"`

	// Delivery tracking
	Status        string     `json:"status" gorm:"default:'pending'"` // "pending", "sent", "delivered", "failed"
	SentAt        *time.Time `json:"sent_at"`
	DeliveredAt   *time.Time `json:"delivered_at"`
	FailureReason string     `json:"failure_reason"`
	RetryCount    int        `json:"retry_count" gorm:"default:0"`
	MaxRetries    int        `json:"max_retries" gorm:"default:3"`

	// Metadata
	NotificationData string `json:"notification_data" gorm:"type:text"` // JSON additional data
	ExternalID       string `json:"external_id"`                        // External service ID
}

// WorkflowAuditLog represents audit trail for workflow operations
type WorkflowAuditLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Audit target
	InstanceID uint             `json:"instance_id" gorm:"not null"`
	Instance   WorkflowInstance `json:"instance" gorm:"foreignKey:InstanceID"`
	StepID     *uint            `json:"step_id"`
	Step       *WorkflowStep    `json:"step,omitempty" gorm:"foreignKey:StepID"`

	// Audit details
	Action      string `json:"action" gorm:"not null"` // "created", "started", "completed", etc.
	Description string `json:"description" gorm:"type:text"`
	UserID      *uint  `json:"user_id"`
	User        *User  `json:"user,omitempty" gorm:"foreignKey:UserID"`

	// Technical details
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
	SessionID string `json:"session_id"`

	// Change tracking
	OldValues string `json:"old_values" gorm:"type:text"` // JSON old values
	NewValues string `json:"new_values" gorm:"type:text"` // JSON new values
	ChangeSet string `json:"change_set" gorm:"type:text"` // JSON change details

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON additional data
}

// TableName returns the table name for WorkflowTemplate model
func (WorkflowTemplate) TableName() string {
	return "workflow_templates"
}

// TableName returns the table name for WorkflowInstance model
func (WorkflowInstance) TableName() string {
	return "workflow_instances"
}

// TableName returns the table name for WorkflowStep model
func (WorkflowStep) TableName() string {
	return "workflow_steps"
}

// TableName returns the table name for WorkflowApproval model
func (WorkflowApproval) TableName() string {
	return "workflow_approvals"
}

// TableName returns the table name for WorkflowEscalation model
func (WorkflowEscalation) TableName() string {
	return "workflow_escalations"
}

// TableName returns the table name for WorkflowNotification model
func (WorkflowNotification) TableName() string {
	return "workflow_notifications"
}

// TableName returns the table name for WorkflowAuditLog model
func (WorkflowAuditLog) TableName() string {
	return "workflow_audit_logs"
}
