import React, { useState, useEffect } from 'react';
import {
  ChartBarIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  ArrowTrendingUpIcon
} from '@heroicons/react/24/outline';
import { RelationshipStats } from '../../types';
import regulationApi from '../../services/regulationApi';

const RegulationRelationshipStats: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<RelationshipStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await regulationApi.getRelationshipStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to load relationship stats:', error);
      setError('Failed to load relationship statistics');
    } finally {
      setLoading(false);
    }
  };

  const getRelationshipTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      // Document relationships
      implements: 'Implements',
      based_on: 'Based On',
      amends: 'Amends',
      repeals: 'Repeals',
      references: 'References',
      supersedes: 'Supersedes',
      // Agency relationships
      established_by: 'Established By',
      authorized_by: 'Authorized By',
      modified_by: 'Modified By',
      abolished_by: 'Abolished By',
      // Category relationships
      created_by: 'Created By',
      governed_by: 'Governed By'
    };
    return labels[type] || type;
  };

  const getRelationshipTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      implements: 'bg-green-500',
      based_on: 'bg-blue-500',
      amends: 'bg-yellow-500',
      repeals: 'bg-red-500',
      references: 'bg-gray-500',
      supersedes: 'bg-purple-500',
      established_by: 'bg-green-500',
      authorized_by: 'bg-blue-500',
      modified_by: 'bg-yellow-500',
      abolished_by: 'bg-red-500',
      created_by: 'bg-green-500',
      governed_by: 'bg-blue-500'
    };
    return colors[type] || 'bg-gray-500';
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-4/5"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="text-center text-red-600 dark:text-red-400">
          <ChartBarIcon className="h-12 w-12 mx-auto mb-4" />
          <p>{error}</p>
          <button
            onClick={loadStats}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return null;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <ChartBarIcon className="h-6 w-6 text-blue-500 mr-3" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Regulation Relationship Statistics
          </h3>
        </div>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Overview of relationships between regulations and other entities
        </p>
      </div>

      <div className="p-6">
        {/* Overall Summary */}
        <div className="mb-8 text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full mb-4">
            <ArrowTrendingUpIcon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-3xl font-bold text-gray-900 dark:text-white">
            {stats.overall_total.toLocaleString()}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Total Relationships
          </div>
        </div>

        {/* Category Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Document Relationships */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Documents
              </h4>
            </div>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-2">
              {stats.document_relationships.total.toLocaleString()}
            </div>
            <div className="space-y-1">
              {stats.document_relationships.by_type.map((item) => (
                <div key={item.relationship_type} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {getRelationshipTypeLabel(item.relationship_type)}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Agency Relationships */}
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <BuildingOfficeIcon className="h-6 w-6 text-green-600 dark:text-green-400 mr-2" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Agencies
              </h4>
            </div>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
              {stats.agency_relationships.total.toLocaleString()}
            </div>
            <div className="space-y-1">
              {stats.agency_relationships.by_type.map((item) => (
                <div key={item.relationship_type} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {getRelationshipTypeLabel(item.relationship_type)}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Category Relationships */}
          <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <TagIcon className="h-6 w-6 text-purple-600 dark:text-purple-400 mr-2" />
              <h4 className="text-lg font-medium text-gray-900 dark:text-white">
                Categories
              </h4>
            </div>
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-2">
              {stats.category_relationships.total.toLocaleString()}
            </div>
            <div className="space-y-1">
              {stats.category_relationships.by_type.map((item) => (
                <div key={item.relationship_type} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {getRelationshipTypeLabel(item.relationship_type)}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {item.count}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Relationship Type Distribution */}
        <div>
          <h4 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Relationship Type Distribution
          </h4>
          <div className="space-y-3">
            {/* Document relationship types */}
            {stats.document_relationships.by_type.map((item) => {
              const percentage = stats.overall_total > 0 ? (item.count / stats.overall_total) * 100 : 0;
              return (
                <div key={`doc-${item.relationship_type}`} className="flex items-center">
                  <div className="w-32 text-sm text-gray-600 dark:text-gray-400">
                    {getRelationshipTypeLabel(item.relationship_type)}
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${getRelationshipTypeColor(item.relationship_type)}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-16 text-sm text-gray-900 dark:text-white text-right">
                    {item.count} ({percentage.toFixed(1)}%)
                  </div>
                </div>
              );
            })}

            {/* Agency relationship types */}
            {stats.agency_relationships.by_type.map((item) => {
              const percentage = stats.overall_total > 0 ? (item.count / stats.overall_total) * 100 : 0;
              return (
                <div key={`agency-${item.relationship_type}`} className="flex items-center">
                  <div className="w-32 text-sm text-gray-600 dark:text-gray-400">
                    {getRelationshipTypeLabel(item.relationship_type)}
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${getRelationshipTypeColor(item.relationship_type)}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-16 text-sm text-gray-900 dark:text-white text-right">
                    {item.count} ({percentage.toFixed(1)}%)
                  </div>
                </div>
              );
            })}

            {/* Category relationship types */}
            {stats.category_relationships.by_type.map((item) => {
              const percentage = stats.overall_total > 0 ? (item.count / stats.overall_total) * 100 : 0;
              return (
                <div key={`cat-${item.relationship_type}`} className="flex items-center">
                  <div className="w-32 text-sm text-gray-600 dark:text-gray-400">
                    {getRelationshipTypeLabel(item.relationship_type)}
                  </div>
                  <div className="flex-1 mx-4">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${getRelationshipTypeColor(item.relationship_type)}`}
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-16 text-sm text-gray-900 dark:text-white text-right">
                    {item.count} ({percentage.toFixed(1)}%)
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegulationRelationshipStats;
