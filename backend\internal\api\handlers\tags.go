package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GetPublicTags returns all public tags
func GetPublicTags(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get all active tags
	var tags []models.Tag
	if err := db.Where("is_active = ?", true).Order("name ASC").Find(&tags).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch tags",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	tagResponses := make([]gin.H, len(tags))
	for i, tag := range tags {
		tagResponses[i] = gin.H{
			"id":          tag.ID,
			"name":        tag.Name,
			"slug":        tag.Slug,
			"description": tag.Description,
			"color":       tag.Color,
			"usage_count": tag.UsageCount,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Public tags retrieved successfully",
		Data:    tagResponses,
	})
}

// GetTagDocuments returns public documents for a specific tag
func GetTagDocuments(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify tag exists
	var tag models.Tag
	if err := db.Where("id = ? AND is_active = ?", id, true).First(&tag).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Tag")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch tag",
			Message: err.Error(),
		})
		return
	}

	// Count total documents for this tag
	var total int64
	db.Table("document_tag_assignments").
		Joins("JOIN documents ON document_tag_assignments.document_id = documents.id").
		Where("document_tag_assignments.tag_id = ? AND documents.is_public = ? AND documents.visibility_level = ?",
			id, true, 1).
		Count(&total)

	// Get documents with pagination
	var documents []models.Document
	offset := (page - 1) * perPage
	if err := db.Preload("Agency").
		Joins("JOIN document_tag_assignments ON documents.id = document_tag_assignments.document_id").
		Where("document_tag_assignments.tag_id = ? AND documents.is_public = ? AND documents.visibility_level = ?",
			id, true, 1).
		Order("documents.publication_date DESC, documents.created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch tag documents",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	documentResponses := make([]gin.H, len(documents))
	for i, doc := range documents {
		documentResponses[i] = gin.H{
			"id":               doc.ID,
			"title":            doc.Title,
			"slug":             doc.Slug,
			"abstract":         doc.Abstract,
			"type":             doc.Type,
			"status":           doc.Status,
			"publication_date": doc.PublicationDate,
			"effective_date":   doc.EffectiveDate,
			"agency": gin.H{
				"id":   doc.Agency.ID,
				"name": doc.Agency.Name,
				"slug": doc.Agency.Slug,
			},
			"created_at": doc.CreatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       documentResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetPublicSubjects returns all public subjects (alias for categories)
func GetPublicSubjects(c *gin.Context) {
	// Subjects are essentially categories in this system
	GetPublicCategories(c)
}

// GetSubjectDocuments returns public documents for a specific subject (alias for category documents)
func GetSubjectDocuments(c *gin.Context) {
	// Subject documents are essentially category documents in this system
	GetCategoryDocuments(c)
}

// GetTags returns all tags (authenticated endpoint)
func GetTags(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total tags
	var total int64
	db.Model(&models.Tag{}).Count(&total)

	// Get tags with pagination
	var tags []models.Tag
	offset := (page - 1) * perPage
	if err := db.Order("name ASC").Limit(perPage).Offset(offset).Find(&tags).Error; err != nil {
		HandleInternalError(c, "Failed to fetch tags: "+err.Error())
		return
	}

	// Convert to response format
	tagResponses := make([]gin.H, len(tags))
	for i, tag := range tags {
		tagResponses[i] = gin.H{
			"id":          tag.ID,
			"name":        tag.Name,
			"slug":        tag.Slug,
			"description": tag.Description,
			"color":       tag.Color,
			"usage_count": tag.UsageCount,
			"is_active":   tag.IsActive,
			"created_at":  tag.CreatedAt,
			"updated_at":  tag.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       tagResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateTag creates a new tag
func CreateTag(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Description string `json:"description"`
		Color       string `json:"color"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Generate slug from name
	slug := strings.ToLower(strings.ReplaceAll(req.Name, " ", "-"))
	slug = strings.ReplaceAll(slug, "_", "-")

	// Create tag
	userIDValue := userID.(uint)
	tag := models.Tag{
		Name:        req.Name,
		Slug:        slug,
		Description: req.Description,
		Color:       req.Color,
		IsActive:    true,
		CreatedByID: &userIDValue,
	}

	if err := db.Create(&tag).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Tag with this name already exists")
			return
		}
		HandleInternalError(c, "Failed to create tag: "+err.Error())
		return
	}

	// Load the created tag with relationships
	if err := db.Preload("CreatedBy").First(&tag, tag.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created tag: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Tag created successfully",
		Data:    tag,
	})
}

// GetTag returns a specific tag
func GetTag(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get tag with relationships
	var tag models.Tag
	if err := db.Preload("CreatedBy").First(&tag, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Tag")
			return
		}
		HandleInternalError(c, "Failed to fetch tag: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Tag retrieved successfully",
		Data:    tag,
	})
}

// UpdateTag updates a tag
func UpdateTag(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		Color       string `json:"color"`
		IsActive    *bool  `json:"is_active"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing tag
	var tag models.Tag
	if err := db.First(&tag, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Tag")
			return
		}
		HandleInternalError(c, "Failed to fetch tag: "+err.Error())
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
		// Generate new slug from name
		slug := strings.ToLower(strings.ReplaceAll(req.Name, " ", "-"))
		slug = strings.ReplaceAll(slug, "_", "-")
		updates["slug"] = slug
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Color != "" {
		updates["color"] = req.Color
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	// Update tag
	if err := db.Model(&tag).Updates(updates).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Tag with this name already exists")
			return
		}
		HandleInternalError(c, "Failed to update tag: "+err.Error())
		return
	}

	// Load updated tag with relationships
	if err := db.Preload("CreatedBy").First(&tag, tag.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated tag: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Tag updated successfully",
		Data:    tag,
	})
}

// DeleteTag deletes a tag
func DeleteTag(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if tag exists
	var tag models.Tag
	if err := db.First(&tag, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Tag")
			return
		}
		HandleInternalError(c, "Failed to fetch tag: "+err.Error())
		return
	}

	// Soft delete the tag
	if err := db.Delete(&tag).Error; err != nil {
		HandleInternalError(c, "Failed to delete tag: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Tag deleted successfully",
		Data:    gin.H{"id": id},
	})
}

// GetSubjects returns all subjects (authenticated endpoint)
func GetSubjects(c *gin.Context) {
	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.Subject{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		if search.Status == "active" {
			query = query.Where("is_active = ?", true)
		} else if search.Status == "inactive" {
			query = query.Where("is_active = ?", false)
		}
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("sort_order ASC, name ASC")
	}

	// Get subjects with relationships
	var subjects []models.Subject
	if err := query.Preload("ParentSubject").Preload("SubSubjects").Find(&subjects).Error; err != nil {
		HandleInternalError(c, "Failed to fetch subjects: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       subjects,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateSubject creates a new subject
func CreateSubject(c *gin.Context) {
	var req struct {
		Name            string `json:"name" binding:"required"`
		Description     string `json:"description"`
		CFRTitle        string `json:"cfr_title"`
		ParentSubjectID *uint  `json:"parent_subject_id"`
		SortOrder       int    `json:"sort_order"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate parent subject if provided
	if req.ParentSubjectID != nil {
		var parentSubject models.Subject
		if err := db.First(&parentSubject, *req.ParentSubjectID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleBadRequest(c, "Parent subject not found")
				return
			}
			HandleInternalError(c, "Failed to validate parent subject: "+err.Error())
			return
		}
	}

	// Generate slug from name
	slug := strings.ToLower(strings.ReplaceAll(req.Name, " ", "-"))
	slug = strings.ReplaceAll(slug, "_", "-")

	// Create subject
	subject := models.Subject{
		Name:            req.Name,
		Slug:            slug,
		Description:     req.Description,
		CFRTitle:        req.CFRTitle,
		ParentSubjectID: req.ParentSubjectID,
		SortOrder:       req.SortOrder,
		IsActive:        true,
	}

	if err := db.Create(&subject).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Subject with this name already exists")
			return
		}
		HandleInternalError(c, "Failed to create subject: "+err.Error())
		return
	}

	// Load the created subject with relationships
	if err := db.Preload("ParentSubject").Preload("SubSubjects").First(&subject, subject.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created subject: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Subject created successfully",
		Data:    subject,
	})
}

// GetSubject returns a specific subject
func GetSubject(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get subject with relationships
	var subject models.Subject
	if err := db.Preload("ParentSubject").Preload("SubSubjects").First(&subject, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Subject")
			return
		}
		HandleInternalError(c, "Failed to fetch subject: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Subject retrieved successfully",
		Data:    subject,
	})
}

// UpdateSubject updates a subject
func UpdateSubject(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Name            string `json:"name"`
		Description     string `json:"description"`
		CFRTitle        string `json:"cfr_title"`
		ParentSubjectID *uint  `json:"parent_subject_id"`
		SortOrder       *int   `json:"sort_order"`
		IsActive        *bool  `json:"is_active"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing subject
	var subject models.Subject
	if err := db.First(&subject, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Subject")
			return
		}
		HandleInternalError(c, "Failed to fetch subject: "+err.Error())
		return
	}

	// Validate parent subject if provided
	if req.ParentSubjectID != nil {
		// Check for circular reference
		if *req.ParentSubjectID == id {
			HandleBadRequest(c, "Subject cannot be its own parent")
			return
		}

		var parentSubject models.Subject
		if err := db.First(&parentSubject, *req.ParentSubjectID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleBadRequest(c, "Parent subject not found")
				return
			}
			HandleInternalError(c, "Failed to validate parent subject: "+err.Error())
			return
		}
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
		// Generate new slug from name
		slug := strings.ToLower(strings.ReplaceAll(req.Name, " ", "-"))
		slug = strings.ReplaceAll(slug, "_", "-")
		updates["slug"] = slug
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.CFRTitle != "" {
		updates["cfr_title"] = req.CFRTitle
	}
	if req.ParentSubjectID != nil {
		updates["parent_subject_id"] = req.ParentSubjectID
	}
	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	// Update subject
	if err := db.Model(&subject).Updates(updates).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Subject with this name already exists")
			return
		}
		HandleInternalError(c, "Failed to update subject: "+err.Error())
		return
	}

	// Load updated subject with relationships
	if err := db.Preload("ParentSubject").Preload("SubSubjects").First(&subject, subject.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated subject: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Subject updated successfully",
		Data:    subject,
	})
}

// DeleteSubject deletes a subject
func DeleteSubject(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if subject exists
	var subject models.Subject
	if err := db.First(&subject, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Subject")
			return
		}
		HandleInternalError(c, "Failed to fetch subject: "+err.Error())
		return
	}

	// Check if subject has children
	var childCount int64
	db.Model(&models.Subject{}).Where("parent_subject_id = ?", id).Count(&childCount)
	if childCount > 0 {
		HandleBadRequest(c, "Cannot delete subject with child subjects. Please delete or reassign child subjects first.")
		return
	}

	// Soft delete the subject
	if err := db.Delete(&subject).Error; err != nil {
		HandleInternalError(c, "Failed to delete subject: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Subject deleted successfully",
		Data:    gin.H{"id": id},
	})
}
