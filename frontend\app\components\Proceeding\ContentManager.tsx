'use client'

import React, { useState, useRef } from 'react';
import {
  DocumentTextIcon,
  MicrophoneIcon,
  VideoCameraIcon,
  PhotoIcon,
  PaperClipIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  EyeIcon
} from '@heroicons/react/24/outline';

interface ContentItem {
  id: string;
  type: 'text' | 'voice' | 'document' | 'video' | 'image';
  name: string;
  content?: string;
  file?: File;
  url?: string;
  size?: number;
  duration?: number;
  created_at: string;
}

interface ContentManagerProps {
  stepId: number;
  initialContent?: ContentItem[];
  onContentChange: (content: ContentItem[]) => void;
  canEdit?: boolean;
}

const ContentManager: React.FC<ContentManagerProps> = ({
  stepId,
  initialContent = [],
  onContentChange,
  canEdit = false
}) => {
  const [content, setContent] = useState<ContentItem[]>(initialContent);
  const [activeTab, setActiveTab] = useState<'text' | 'voice' | 'document' | 'video'>('text');
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [textContent, setTextContent] = useState('');
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const contentTypes = [
    { key: 'text', label: 'Text', icon: DocumentTextIcon },
    { key: 'voice', label: 'Voice', icon: MicrophoneIcon },
    { key: 'document', label: 'Documents', icon: PaperClipIcon },
    { key: 'video', label: 'Video', icon: VideoCameraIcon }
  ];

  const addContent = (newContent: ContentItem) => {
    const updatedContent = [...content, newContent];
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const removeContent = (contentId: string) => {
    const updatedContent = content.filter(item => item.id !== contentId);
    setContent(updatedContent);
    onContentChange(updatedContent);
  };

  const handleTextSave = () => {
    if (!textContent.trim()) return;

    const newContent: ContentItem = {
      id: `text-${Date.now()}`,
      type: 'text',
      name: `Text Content ${new Date().toLocaleString()}`,
      content: textContent,
      created_at: new Date().toISOString()
    };

    addContent(newContent);
    setTextContent('');
  };

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      const chunks: BlobPart[] = [];

      mediaRecorder.ondataavailable = (event) => {
        chunks.push(event.data);
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(chunks, { type: 'audio/wav' });
        const file = new File([blob], `recording-${Date.now()}.wav`, { type: 'audio/wav' });
        
        const newContent: ContentItem = {
          id: `voice-${Date.now()}`,
          type: 'voice',
          name: `Voice Recording ${new Date().toLocaleString()}`,
          file: file,
          size: file.size,
          duration: recordingTime,
          created_at: new Date().toISOString()
        };

        addContent(newContent);
        setRecordingTime(0);
      };

      mediaRecorderRef.current = mediaRecorder;
      mediaRecorder.start();
      setIsRecording(true);

      // Start recording timer
      recordingIntervalRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Unable to access microphone. Please check permissions.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);
      
      if (recordingIntervalRef.current) {
        clearInterval(recordingIntervalRef.current);
      }
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    Array.from(files).forEach(file => {
      const fileType = file.type.startsWith('video/') ? 'video' : 'document';
      
      const newContent: ContentItem = {
        id: `${fileType}-${Date.now()}-${Math.random()}`,
        type: fileType,
        name: file.name,
        file: file,
        size: file.size,
        created_at: new Date().toISOString()
      };

      addContent(newContent);
    });

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getContentIcon = (type: string) => {
    switch (type) {
      case 'text':
        return <DocumentTextIcon className="h-5 w-5" />;
      case 'voice':
        return <MicrophoneIcon className="h-5 w-5" />;
      case 'video':
        return <VideoCameraIcon className="h-5 w-5" />;
      default:
        return <PaperClipIcon className="h-5 w-5" />;
    }
  };

  const renderTextTab = () => (
    <div className="space-y-4">
      {canEdit && (
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Add Text Content
          </label>
          <textarea
            value={textContent}
            onChange={(e) => setTextContent(e.target.value)}
            rows={6}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Enter text content for this step..."
          />
          <div className="mt-2 flex justify-end">
            <button
              onClick={handleTextSave}
              disabled={!textContent.trim()}
              className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              Save Text
            </button>
          </div>
        </div>
      )}

      <div className="space-y-3">
        {content.filter(item => item.type === 'text').map(item => (
          <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <DocumentTextIcon className="h-5 w-5 text-gray-400 mt-1" />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {item.name}
                  </h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {new Date(item.created_at).toLocaleString()}
                  </p>
                  <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {item.content}
                    </p>
                  </div>
                </div>
              </div>
              {canEdit && (
                <button
                  onClick={() => removeContent(item.id)}
                  className="text-red-400 hover:text-red-600 transition-colors"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderVoiceTab = () => (
    <div className="space-y-4">
      {canEdit && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-4">
            Voice Recording
          </h4>
          
          <div className="flex items-center space-x-4">
            {!isRecording ? (
              <button
                onClick={startRecording}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                <MicrophoneIcon className="h-4 w-4 mr-2" />
                Start Recording
              </button>
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={stopRecording}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  <StopIcon className="h-4 w-4 mr-2" />
                  Stop Recording
                </button>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {formatDuration(recordingTime)}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="space-y-3">
        {content.filter(item => item.type === 'voice').map(item => (
          <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <MicrophoneIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {item.name}
                  </h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {item.duration && formatDuration(item.duration)} • {item.size && formatFileSize(item.size)}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {item.file && (
                  <audio controls className="h-8">
                    <source src={URL.createObjectURL(item.file)} type="audio/wav" />
                  </audio>
                )}
                {canEdit && (
                  <button
                    onClick={() => removeContent(item.id)}
                    className="text-red-400 hover:text-red-600 transition-colors"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderDocumentTab = () => (
    <div className="space-y-4">
      {canEdit && (
        <div>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx"
            onChange={handleFileUpload}
            className="hidden"
          />
          <button
            onClick={() => fileInputRef.current?.click()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PaperClipIcon className="h-4 w-4 mr-2" />
            Upload Documents
          </button>
        </div>
      )}

      <div className="space-y-3">
        {content.filter(item => item.type === 'document').map(item => (
          <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <PaperClipIcon className="h-5 w-5 text-gray-400" />
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {item.name}
                  </h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {item.size && formatFileSize(item.size)} • {new Date(item.created_at).toLocaleString()}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button className="text-blue-400 hover:text-blue-600 transition-colors">
                  <EyeIcon className="h-4 w-4" />
                </button>
                <button className="text-green-400 hover:text-green-600 transition-colors">
                  <ArrowDownTrayIcon className="h-4 w-4" />
                </button>
                {canEdit && (
                  <button
                    onClick={() => removeContent(item.id)}
                    className="text-red-400 hover:text-red-600 transition-colors"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderVideoTab = () => (
    <div className="space-y-4">
      {canEdit && (
        <div>
          <input
            type="file"
            accept="video/*"
            onChange={handleFileUpload}
            className="hidden"
            id="video-upload"
          />
          <label
            htmlFor="video-upload"
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 cursor-pointer"
          >
            <VideoCameraIcon className="h-4 w-4 mr-2" />
            Upload Video
          </label>
        </div>
      )}

      <div className="space-y-3">
        {content.filter(item => item.type === 'video').map(item => (
          <div key={item.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <VideoCameraIcon className="h-5 w-5 text-gray-400 mt-1" />
                <div className="flex-1">
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                    {item.name}
                  </h4>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mb-3">
                    {item.size && formatFileSize(item.size)} • {new Date(item.created_at).toLocaleString()}
                  </p>
                  {item.file && (
                    <video controls className="w-full max-w-md rounded-md">
                      <source src={URL.createObjectURL(item.file)} type={item.file.type} />
                    </video>
                  )}
                </div>
              </div>
              {canEdit && (
                <button
                  onClick={() => removeContent(item.id)}
                  className="text-red-400 hover:text-red-600 transition-colors"
                >
                  <TrashIcon className="h-4 w-4" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8 px-6">
          {contentTypes.map(({ key, label, icon: Icon }) => (
            <button
              key={key}
              onClick={() => setActiveTab(key as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === key
                  ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
              }`}
            >
              <Icon className="h-4 w-4" />
              <span>{label}</span>
              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 py-0.5 px-2 rounded-full text-xs">
                {content.filter(item => item.type === key).length}
              </span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'text' && renderTextTab()}
        {activeTab === 'voice' && renderVoiceTab()}
        {activeTab === 'document' && renderDocumentTab()}
        {activeTab === 'video' && renderVideoTab()}
      </div>
    </div>
  );
};

export default ContentManager;
