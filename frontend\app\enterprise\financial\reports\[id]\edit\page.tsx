'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../../services/enterpriseApi';
import { FinancialReport } from '../../../../../types/enterprise';

const EditFinancialReportPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const reportId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<FinancialReport>>({
    report_code: '',
    report_name: '',
    description: '',
    report_type: 'balance_sheet',
    period_start: '',
    period_end: '',
    fiscal_year: new Date().getFullYear(),
    fiscal_period: new Date().getMonth() + 1,
    currency_code: 'USD',
    status: 'draft',
    format: 'pdf',
    template_id: undefined,
    parameters: '',
    filters: '',
    sort_order: '',
    grouping: '',
    metadata: ''
  });

  useEffect(() => {
    if (reportId) {
      fetchReport();
    }
  }, [reportId]);

  const fetchReport = async () => {
    try {
      setFetchLoading(true);
      const response = await financialApi.getFinancialReport(reportId);
      const report = response.data;
      setFormData({
        ...report,
        period_start: report.period_start.split('T')[0],
        period_end: report.period_end.split('T')[0],
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch financial report');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await financialApi.updateFinancialReport(reportId, formData);
      router.push(`/enterprise/financial/reports/${reportId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update financial report');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  if (fetchLoading) return <div className="p-6">Loading financial report...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Financial Report</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/financial/reports/${reportId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Report Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Report Code *
            </label>
            <input
              type="text"
              name="report_code"
              value={formData.report_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., RPT-2025-001"
            />
          </div>

          {/* Report Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Report Name *
            </label>
            <input
              type="text"
              name="report_name"
              value={formData.report_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Monthly Balance Sheet"
            />
          </div>

          {/* Report Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Report Type *
            </label>
            <select
              name="report_type"
              value={formData.report_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="balance_sheet">Balance Sheet</option>
              <option value="income_statement">Income Statement</option>
              <option value="cash_flow">Cash Flow Statement</option>
              <option value="trial_balance">Trial Balance</option>
              <option value="budget_variance">Budget Variance</option>
              <option value="general_ledger">General Ledger</option>
              <option value="accounts_receivable">Accounts Receivable</option>
              <option value="accounts_payable">Accounts Payable</option>
            </select>
          </div>

          {/* Format */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Format *
            </label>
            <select
              name="format"
              value={formData.format}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="pdf">PDF</option>
              <option value="excel">Excel</option>
              <option value="csv">CSV</option>
              <option value="html">HTML</option>
            </select>
          </div>

          {/* Period Start */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Period Start *
            </label>
            <input
              type="date"
              name="period_start"
              value={formData.period_start}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Period End */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Period End *
            </label>
            <input
              type="date"
              name="period_end"
              value={formData.period_end}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Fiscal Year */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fiscal Year
            </label>
            <input
              type="number"
              name="fiscal_year"
              value={formData.fiscal_year}
              onChange={handleChange}
              min="2000"
              max="2100"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Fiscal Period */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fiscal Period
            </label>
            <input
              type="number"
              name="fiscal_period"
              value={formData.fiscal_period}
              onChange={handleChange}
              min="1"
              max="12"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency Code
            </label>
            <input
              type="text"
              name="currency_code"
              value={formData.currency_code}
              onChange={handleChange}
              maxLength={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="USD"
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="draft">Draft</option>
              <option value="processing">Processing</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
            </select>
          </div>

          {/* Sort Order */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Sort Order
            </label>
            <input
              type="text"
              name="sort_order"
              value={formData.sort_order}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., account_code ASC"
            />
          </div>

          {/* Grouping */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Grouping
            </label>
            <input
              type="text"
              name="grouping"
              value={formData.grouping}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., account_type, department"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Report description..."
          />
        </div>

        {/* Parameters */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Parameters (JSON)
          </label>
          <textarea
            name="parameters"
            value={formData.parameters}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder='{"include_zero_balances": false, "show_details": true}'
          />
        </div>

        {/* Filters */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Filters (JSON)
          </label>
          <textarea
            name="filters"
            value={formData.filters}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder='{"account_type": "asset", "is_active": true}'
          />
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/financial/reports/${reportId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Report'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditFinancialReportPage;
