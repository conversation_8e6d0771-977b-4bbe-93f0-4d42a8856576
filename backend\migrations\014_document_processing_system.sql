-- Document Processing System Migration
-- This migration creates tables for OCR, metadata extraction, document classification,
-- and entity extraction capabilities

-- Document Processing Jobs Table
CREATE TABLE IF NOT EXISTS document_processing_jobs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Job identification
    job_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    priority INTEGER DEFAULT 3,
    
    -- Document relationship
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    file_id INTEGER,
    
    -- Processing configuration
    engine VARCHAR(100),
    configuration TEXT,
    input_format VARCHAR(50),
    output_format VARCHAR(50),
    language VARCHAR(10) DEFAULT 'en',
    
    -- Processing parameters
    ocr_confidence DECIMAL(3,2) DEFAULT 0.8,
    extract_images BOOLEAN DEFAULT FALSE,
    extract_tables BOOLEAN DEFAULT FALSE,
    extract_metadata BOOLEAN DEFAULT TRUE,
    preprocess_image BOOLEAN DEFAULT TRUE,
    
    -- Execution tracking
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    processing_time_ms INTEGER DEFAULT 0,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Results
    extracted_text TEXT,
    confidence DECIMAL(3,2) DEFAULT 0,
    page_count INTEGER DEFAULT 0,
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    
    -- Error handling
    error_message VARCHAR(500),
    error_details TEXT,
    last_error VARCHAR(500),
    
    -- Job context
    created_by_id INTEGER NOT NULL REFERENCES users(id),
    processed_by_id INTEGER REFERENCES users(id),
    
    -- Output data
    output_data TEXT,
    output_files TEXT
);

-- Extracted Metadata Table
CREATE TABLE IF NOT EXISTS extracted_metadata (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata identification
    job_id INTEGER NOT NULL REFERENCES document_processing_jobs(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Metadata details
    metadata_type VARCHAR(100) NOT NULL,
    key VARCHAR(255) NOT NULL,
    value TEXT,
    confidence DECIMAL(3,2) DEFAULT 0,
    source VARCHAR(100),
    
    -- Location information
    page_number INTEGER,
    x_position DECIMAL(10,6),
    y_position DECIMAL(10,6),
    width DECIMAL(10,6),
    height DECIMAL(10,6),
    
    -- Validation and verification
    is_verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by_id INTEGER REFERENCES users(id),
    
    -- Additional context
    context TEXT,
    category VARCHAR(100),
    tags TEXT,
    notes TEXT
);

-- Document Classifications Table
CREATE TABLE IF NOT EXISTS document_classifications (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Classification identification
    job_id INTEGER NOT NULL REFERENCES document_processing_jobs(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Classification results
    predicted_type VARCHAR(255) NOT NULL,
    confidence DECIMAL(5,4) NOT NULL,
    predicted_category VARCHAR(255),
    predicted_agency VARCHAR(255),
    
    -- Classification details
    classification_model VARCHAR(255),
    model_version VARCHAR(50),
    features TEXT,
    probabilities TEXT,
    
    -- Alternative predictions
    alternative_predictions TEXT,
    
    -- Validation
    is_accurate BOOLEAN,
    validated_at TIMESTAMP WITH TIME ZONE,
    validated_by_id INTEGER REFERENCES users(id),
    
    -- Feedback for model improvement
    feedback_provided BOOLEAN DEFAULT FALSE,
    feedback_notes TEXT
);

-- Extracted Entities Table
CREATE TABLE IF NOT EXISTS extracted_entities (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Entity identification
    job_id INTEGER NOT NULL REFERENCES document_processing_jobs(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Entity details
    entity_type VARCHAR(100) NOT NULL,
    entity_value VARCHAR(500) NOT NULL,
    normalized_value VARCHAR(500),
    confidence DECIMAL(5,4) NOT NULL,
    
    -- Location in document
    page_number INTEGER,
    start_offset INTEGER,
    end_offset INTEGER,
    x_position DECIMAL(10,6),
    y_position DECIMAL(10,6),
    width DECIMAL(10,6),
    height DECIMAL(10,6),
    
    -- Context and relationships
    context TEXT,
    related_entities TEXT,
    
    -- Validation
    is_verified BOOLEAN DEFAULT FALSE,
    verified_at TIMESTAMP WITH TIME ZONE,
    verified_by_id INTEGER REFERENCES users(id)
);

-- Processing Logs Table
CREATE TABLE IF NOT EXISTS processing_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Log identification
    job_id INTEGER NOT NULL REFERENCES document_processing_jobs(id) ON DELETE CASCADE,
    
    -- Log details
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    component VARCHAR(100),
    step VARCHAR(100),
    
    -- Timing
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    duration_ms INTEGER DEFAULT 0,
    
    -- Additional context
    details TEXT,
    stack_trace TEXT,
    
    -- Performance metrics
    memory_usage BIGINT DEFAULT 0,
    cpu_usage DECIMAL(5,2) DEFAULT 0
);

-- Processing Templates Table
CREATE TABLE IF NOT EXISTS processing_templates (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Template identification
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    version VARCHAR(50) DEFAULT '1.0',
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Template configuration
    processing_types TEXT,
    default_engine VARCHAR(100),
    configuration TEXT,
    
    -- File type support
    supported_formats TEXT,
    max_file_size BIGINT DEFAULT 0,
    
    -- Processing parameters
    default_language VARCHAR(10) DEFAULT 'en',
    default_confidence DECIMAL(3,2) DEFAULT 0.8,
    auto_classify BOOLEAN DEFAULT TRUE,
    extract_entities BOOLEAN DEFAULT TRUE,
    generate_summary BOOLEAN DEFAULT FALSE,
    
    -- Template ownership
    created_by_id INTEGER NOT NULL REFERENCES users(id),
    
    -- Usage statistics
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    success_rate DECIMAL(5,2) DEFAULT 0,
    avg_processing_time_ms INTEGER DEFAULT 0
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_job_id ON document_processing_jobs(job_id);
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_document_id ON document_processing_jobs(document_id);
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_status ON document_processing_jobs(status);
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_type ON document_processing_jobs(type);
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_created_by_id ON document_processing_jobs(created_by_id);
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_priority ON document_processing_jobs(priority);
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_created_at ON document_processing_jobs(created_at);
CREATE INDEX IF NOT EXISTS idx_document_processing_jobs_deleted_at ON document_processing_jobs(deleted_at);

CREATE INDEX IF NOT EXISTS idx_extracted_metadata_job_id ON extracted_metadata(job_id);
CREATE INDEX IF NOT EXISTS idx_extracted_metadata_document_id ON extracted_metadata(document_id);
CREATE INDEX IF NOT EXISTS idx_extracted_metadata_type ON extracted_metadata(metadata_type);
CREATE INDEX IF NOT EXISTS idx_extracted_metadata_key ON extracted_metadata(key);
CREATE INDEX IF NOT EXISTS idx_extracted_metadata_is_verified ON extracted_metadata(is_verified);
CREATE INDEX IF NOT EXISTS idx_extracted_metadata_deleted_at ON extracted_metadata(deleted_at);

CREATE INDEX IF NOT EXISTS idx_document_classifications_job_id ON document_classifications(job_id);
CREATE INDEX IF NOT EXISTS idx_document_classifications_document_id ON document_classifications(document_id);
CREATE INDEX IF NOT EXISTS idx_document_classifications_predicted_type ON document_classifications(predicted_type);
CREATE INDEX IF NOT EXISTS idx_document_classifications_confidence ON document_classifications(confidence);
CREATE INDEX IF NOT EXISTS idx_document_classifications_is_accurate ON document_classifications(is_accurate);
CREATE INDEX IF NOT EXISTS idx_document_classifications_deleted_at ON document_classifications(deleted_at);

CREATE INDEX IF NOT EXISTS idx_extracted_entities_job_id ON extracted_entities(job_id);
CREATE INDEX IF NOT EXISTS idx_extracted_entities_document_id ON extracted_entities(document_id);
CREATE INDEX IF NOT EXISTS idx_extracted_entities_entity_type ON extracted_entities(entity_type);
CREATE INDEX IF NOT EXISTS idx_extracted_entities_entity_value ON extracted_entities(entity_value);
CREATE INDEX IF NOT EXISTS idx_extracted_entities_confidence ON extracted_entities(confidence);
CREATE INDEX IF NOT EXISTS idx_extracted_entities_is_verified ON extracted_entities(is_verified);
CREATE INDEX IF NOT EXISTS idx_extracted_entities_deleted_at ON extracted_entities(deleted_at);

CREATE INDEX IF NOT EXISTS idx_processing_logs_job_id ON processing_logs(job_id);
CREATE INDEX IF NOT EXISTS idx_processing_logs_level ON processing_logs(level);
CREATE INDEX IF NOT EXISTS idx_processing_logs_timestamp ON processing_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_processing_logs_component ON processing_logs(component);
CREATE INDEX IF NOT EXISTS idx_processing_logs_deleted_at ON processing_logs(deleted_at);

CREATE INDEX IF NOT EXISTS idx_processing_templates_name ON processing_templates(name);
CREATE INDEX IF NOT EXISTS idx_processing_templates_category ON processing_templates(category);
CREATE INDEX IF NOT EXISTS idx_processing_templates_is_active ON processing_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_processing_templates_created_by_id ON processing_templates(created_by_id);
CREATE INDEX IF NOT EXISTS idx_processing_templates_deleted_at ON processing_templates(deleted_at);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_document_processing_jobs_updated_at BEFORE UPDATE ON document_processing_jobs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_extracted_metadata_updated_at BEFORE UPDATE ON extracted_metadata FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_document_classifications_updated_at BEFORE UPDATE ON document_classifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_extracted_entities_updated_at BEFORE UPDATE ON extracted_entities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_processing_logs_updated_at BEFORE UPDATE ON processing_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_processing_templates_updated_at BEFORE UPDATE ON processing_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample processing templates
INSERT INTO processing_templates (
    name,
    description,
    category,
    processing_types,
    default_engine,
    configuration,
    supported_formats,
    max_file_size,
    auto_classify,
    extract_entities,
    generate_summary,
    created_by_id
) VALUES 
(
    'Standard Document OCR',
    'Standard OCR processing for text documents with metadata extraction',
    'OCR',
    '["ocr", "metadata_extraction", "classification"]',
    'tesseract',
    '{"preprocess": true, "deskew": true, "denoise": true, "language": "eng"}',
    '["pdf", "png", "jpg", "jpeg", "tiff", "bmp"]',
    52428800,
    TRUE,
    TRUE,
    FALSE,
    1
),
(
    'Legal Document Processing',
    'Specialized processing for legal documents with entity extraction',
    'Legal',
    '["ocr", "metadata_extraction", "entity_extraction", "classification"]',
    'azure_cognitive',
    '{"extract_entities": ["PERSON", "ORG", "DATE", "MONEY"], "confidence_threshold": 0.85}',
    '["pdf", "docx", "doc", "txt"]',
    104857600,
    TRUE,
    TRUE,
    TRUE,
    1
),
(
    'Financial Document Analysis',
    'Processing for financial documents with table extraction and analysis',
    'Financial',
    '["ocr", "metadata_extraction", "entity_extraction", "classification"]',
    'aws_textract',
    '{"extract_tables": true, "extract_forms": true, "financial_entities": true}',
    '["pdf", "xlsx", "xls", "csv"]',
    209715200,
    TRUE,
    TRUE,
    TRUE,
    1
) ON CONFLICT DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE document_processing_jobs IS 'Document processing jobs for OCR, metadata extraction, and analysis';
COMMENT ON TABLE extracted_metadata IS 'Metadata extracted from documents through automated processing';
COMMENT ON TABLE document_classifications IS 'Automatic document classification results';
COMMENT ON TABLE extracted_entities IS 'Named entities extracted from document content';
COMMENT ON TABLE processing_logs IS 'Detailed logs from document processing operations';
COMMENT ON TABLE processing_templates IS 'Reusable templates for document processing configurations';
