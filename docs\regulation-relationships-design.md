# Enhanced Regulation Relationship System Design

## Overview

This document outlines the design for enhancing the regulation system to establish comprehensive relationships between regulations and other entities (documents, categories, agencies). The system will track how regulations create, modify, or establish other entities in the system.

## Relationship Types

### 1. Regulation-Document Relationships

Documents can be related to regulations in several ways:

- **IMPLEMENTS**: Document implements a regulation (e.g., a rule implementing a law)
- **BASED_ON**: Document is based on a regulation (e.g., guidance based on a regulation)
- **AMENDS**: Document amends an existing regulation
- **REPEALS**: Document repeals a regulation
- **REFERENCES**: Document references a regulation
- **SUPERSEDES**: Document supersedes a regulation

### 2. Regulation-Agency Relationships

Agencies can be related to regulations:

- **ESTABLISHED_BY**: Agency was established by a regulation/law
- **AUTHORIZED_BY**: Agency's authority comes from a regulation
- **MODIFIED_BY**: Agency structure/authority was modified by a regulation
- **ABOLISHED_BY**: Agency was abolished by a regulation

### 3. Regulation-Category Relationships

Categories can be related to regulations:

- **CREATED_BY**: Category was created/defined by a regulation
- **MODIFIED_BY**: Category scope/definition was modified by a regulation
- **ABOLISHED_BY**: Category was abolished by a regulation
- **GOVERNED_BY**: Category is governed by specific regulations

## Database Schema Design

### New Tables

#### 1. regulation_document_relationships
```sql
CREATE TABLE regulation_document_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Source regulation
    regulation_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    regulation_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Target document
    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Relationship details
    relationship_type TEXT NOT NULL CHECK (relationship_type IN (
        'implements', 'based_on', 'amends', 'repeals', 'references', 'supersedes'
    )),
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    description TEXT,
    citation_text TEXT,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    
    UNIQUE(regulation_id, document_id, relationship_type)
);
```

#### 2. regulation_agency_relationships
```sql
CREATE TABLE regulation_agency_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Source regulation
    regulation_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    regulation_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Target agency
    agency_id BIGINT NOT NULL REFERENCES agencies(id) ON DELETE CASCADE,
    
    -- Relationship details
    relationship_type TEXT NOT NULL CHECK (relationship_type IN (
        'established_by', 'authorized_by', 'modified_by', 'abolished_by'
    )),
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    description TEXT,
    authority_scope TEXT,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    
    UNIQUE(regulation_id, agency_id, relationship_type)
);
```

#### 3. regulation_category_relationships
```sql
CREATE TABLE regulation_category_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Source regulation
    regulation_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    regulation_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Target category
    category_id BIGINT NOT NULL REFERENCES categories(id) ON DELETE CASCADE,
    
    -- Relationship details
    relationship_type TEXT NOT NULL CHECK (relationship_type IN (
        'created_by', 'modified_by', 'abolished_by', 'governed_by'
    )),
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    description TEXT,
    scope_definition TEXT,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    
    UNIQUE(regulation_id, category_id, relationship_type)
);
```

## Model Enhancements

### Go Models

#### RegulationDocumentRelationship
```go
type RegulationDocumentRelationship struct {
    ID        uint           `json:"id" gorm:"primaryKey"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
    
    // Source regulation
    RegulationID      uint                `json:"regulation_id" gorm:"not null"`
    Regulation        LawsAndRules        `json:"regulation" gorm:"foreignKey:RegulationID"`
    RegulationChunkID *uint               `json:"regulation_chunk_id"`
    RegulationChunk   *Chunk              `json:"regulation_chunk,omitempty" gorm:"foreignKey:RegulationChunkID"`
    
    // Target document
    DocumentID uint     `json:"document_id" gorm:"not null"`
    Document   Document `json:"document" gorm:"foreignKey:DocumentID"`
    
    // Relationship details
    RelationshipType RelationshipType `json:"relationship_type" gorm:"not null"`
    EffectiveDate    *time.Time       `json:"effective_date"`
    TerminationDate  *time.Time       `json:"termination_date"`
    Description      string           `json:"description"`
    CitationText     string           `json:"citation_text"`
    
    // Metadata
    CreatedByID uint `json:"created_by_id" gorm:"not null"`
    CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
    IsActive    bool `json:"is_active" gorm:"default:true"`
}
```

## API Endpoints Design

### Regulation Relationship Endpoints

#### Document Relationships
- `GET /api/v1/regulations/{id}/documents` - Get all document relationships for a regulation
- `POST /api/v1/regulations/{id}/documents` - Create new document relationship
- `PUT /api/v1/regulations/{id}/documents/{relationshipId}` - Update document relationship
- `DELETE /api/v1/regulations/{id}/documents/{relationshipId}` - Delete document relationship

#### Agency Relationships
- `GET /api/v1/regulations/{id}/agencies` - Get all agency relationships for a regulation
- `POST /api/v1/regulations/{id}/agencies` - Create new agency relationship
- `PUT /api/v1/regulations/{id}/agencies/{relationshipId}` - Update agency relationship
- `DELETE /api/v1/regulations/{id}/agencies/{relationshipId}` - Delete agency relationship

#### Category Relationships
- `GET /api/v1/regulations/{id}/categories` - Get all category relationships for a regulation
- `POST /api/v1/regulations/{id}/categories` - Create new category relationship
- `PUT /api/v1/regulations/{id}/categories/{relationshipId}` - Update category relationship
- `DELETE /api/v1/regulations/{id}/categories/{relationshipId}` - Delete category relationship

### Reverse Lookup Endpoints

#### For Documents
- `GET /api/v1/documents/{id}/regulations` - Get all regulations related to a document

#### For Agencies
- `GET /api/v1/agencies/{id}/regulations` - Get all regulations related to an agency

#### For Categories
- `GET /api/v1/categories/{id}/regulations` - Get all regulations related to a category

## Frontend Components Design

### Regulation Relationship Management

#### 1. RegulationRelationships Component
- Tabbed interface showing Documents, Agencies, and Categories
- Each tab shows existing relationships with CRUD operations
- Add new relationship buttons for each type

#### 2. RelationshipForm Component
- Generic form for creating/editing relationships
- Dropdown for relationship type
- Entity selector (document/agency/category)
- Date fields for effective/termination dates
- Description and additional fields

#### 3. RelationshipList Component
- Table/list view of existing relationships
- Edit and delete buttons for each relationship
- Filtering and sorting capabilities

## Business Logic

### Validation Rules

1. **Unique Relationships**: Prevent duplicate relationships of the same type between the same entities
2. **Date Validation**: Effective date must be before termination date
3. **Active Relationships**: Only one active relationship of certain types allowed
4. **Circular References**: Prevent circular relationships that could cause logical conflicts

### Automatic Relationship Creation

1. When a document is created with a regulation reference, automatically create a relationship
2. When an agency is assigned to a regulation, create an "authorized_by" relationship
3. When a category is used in a regulation context, create a "governed_by" relationship

## Implementation Priority

1. Database migration and models
2. Backend CRUD APIs
3. Frontend types and API services
4. Basic relationship management UI
5. Advanced features (automatic creation, validation)
6. Testing and documentation

This design provides a comprehensive foundation for tracking relationships between regulations and other entities in the system.
