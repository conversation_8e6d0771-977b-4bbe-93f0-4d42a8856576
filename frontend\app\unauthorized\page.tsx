'use client';

import React from 'react';
import Link from 'next/link';
import { ExclamationTriangleIcon, HomeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '../stores/authStore';

export default function UnauthorizedPage() {
  const { user, isAuthenticated } = useAuthStore();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-red-500" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Access Denied
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          You don't have permission to access this page
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Insufficient Permissions
              </h3>
              <p className="text-sm text-gray-600">
                {isAuthenticated ? (
                  <>
                    Your current role ({user?.role || 'Unknown'}) doesn't have access to this resource.
                    Please contact your administrator if you believe this is an error.
                  </>
                ) : (
                  'You need to be logged in to access this page.'
                )}
              </p>
            </div>

            {isAuthenticated && user && (
              <div className="mb-6 p-4 bg-gray-50 rounded-md">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Your Account</h4>
                <div className="text-sm text-gray-600">
                  <p><strong>Username:</strong> {user.username}</p>
                  <p><strong>Role:</strong> {user.role}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                </div>
              </div>
            )}

            <div className="space-y-4">
              {!isAuthenticated ? (
                <Link
                  href="/login"
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Sign In
                </Link>
              ) : (
                <div className="space-y-3">
                  <button
                    onClick={() => window.history.back()}
                    className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    <ArrowLeftIcon className="h-4 w-4 mr-2" />
                    Go Back
                  </button>
                  
                  <Link
                    href="/dashboard"
                    className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    <HomeIcon className="h-4 w-4 mr-2" />
                    Go to Dashboard
                  </Link>
                </div>
              )}

              <Link
                href="/"
                className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <HomeIcon className="h-4 w-4 mr-2" />
                Go to Home
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <div className="text-center">
            <p className="text-xs text-gray-500">
              Need help? Contact your system administrator or{' '}
              <Link href="/contact" className="text-primary-600 hover:text-primary-500">
                contact support
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
