package handlers

import (
	"crypto"
	"crypto/ecdsa"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/sha512"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/pem"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/api/middleware"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// Comment service instances
var (
	digitalSignatureService *services.DigitalSignatureService
	digitalCertService      *services.DigitalCertificateService
)

// Initialize comment services
func initCommentServices() {
	db := database.GetDB()
	if db != nil {
		if digitalSignatureService == nil {
			digitalSignatureService = services.NewDigitalSignatureService(db)
		}
		if digitalCertService == nil {
			digitalCertService = services.NewDigitalCertificateService(db)
		}
	}
}

// verifyDigitalSignature verifies a digital signature using the provided public key
func verifyDigitalSignature(publicKeyPEM, contentHash, signatureData, signatureMethod string) (bool, error) {
	if signatureData == "" {
		return false, fmt.Errorf("signature data is empty")
	}

	// Basic validation - signature should be base64 encoded and have minimum length
	if len(signatureData) < 64 {
		return false, fmt.Errorf("signature data too short")
	}

	// Validate signature method
	validMethods := []string{"RSA-SHA256", "ECDSA-SHA256", "RSA-PSS"}
	isValidMethod := false
	for _, method := range validMethods {
		if signatureMethod == method {
			isValidMethod = true
			break
		}
	}

	if !isValidMethod {
		return false, fmt.Errorf("unsupported signature method: %s", signatureMethod)
	}

	// Implement actual cryptographic verification
	// 1. Parse the PEM-encoded public key
	block, _ := pem.Decode([]byte(publicKeyPEM))
	if block == nil {
		return false, fmt.Errorf("failed to parse PEM block containing the public key")
	}

	// Parse the public key
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return false, fmt.Errorf("failed to parse public key: %v", err)
	}

	// Type assert to RSA public key (for RSA-SHA256 method)
	rsaPub, ok := pub.(*rsa.PublicKey)
	if !ok && signatureMethod == "RSA-SHA256" {
		return false, fmt.Errorf("public key is not an RSA key")
	}

	// 2. Decode the base64 signature
	signature, err := base64.StdEncoding.DecodeString(signatureData)
	if err != nil {
		return false, fmt.Errorf("failed to decode signature: %v", err)
	}

	// 3. Verify the signature against the content hash
	contentHashBytes, err := hex.DecodeString(contentHash)
	if err != nil {
		return false, fmt.Errorf("failed to decode content hash: %v", err)
	}

	// Verify signature based on method
	switch signatureMethod {
	case "RSA-SHA256":
		err = rsa.VerifyPKCS1v15(rsaPub, crypto.SHA256, contentHashBytes, signature)
		if err != nil {
			return false, fmt.Errorf("RSA-SHA256 signature verification failed: %v", err)
		}
		return true, nil

	case "RSA-PSS-SHA256":
		err = rsa.VerifyPSS(rsaPub, crypto.SHA256, contentHashBytes, signature, &rsa.PSSOptions{
			SaltLength: rsa.PSSSaltLengthAuto,
		})
		if err != nil {
			return false, fmt.Errorf("RSA-PSS-SHA256 signature verification failed: %v", err)
		}
		return true, nil

	case "ECDSA-SHA256":
		// Parse ECDSA public key
		ecdsaPub, ok := pub.(*ecdsa.PublicKey)
		if !ok {
			return false, fmt.Errorf("invalid ECDSA public key")
		}

		// ECDSA signature is typically ASN.1 DER encoded
		valid := ecdsa.VerifyASN1(ecdsaPub, contentHashBytes, signature)
		if !valid {
			return false, fmt.Errorf("ECDSA-SHA256 signature verification failed")
		}
		return true, nil

	case "ECDSA-SHA384":
		ecdsaPub, ok := pub.(*ecdsa.PublicKey)
		if !ok {
			return false, fmt.Errorf("invalid ECDSA public key")
		}

		// Hash content with SHA384 - need to hash the original content, not the hash
		h384 := sha512.Sum384([]byte(contentHash))
		valid := ecdsa.VerifyASN1(ecdsaPub, h384[:], signature)
		if !valid {
			return false, fmt.Errorf("ECDSA-SHA384 signature verification failed")
		}
		return true, nil

	case "ECDSA-SHA512":
		ecdsaPub, ok := pub.(*ecdsa.PublicKey)
		if !ok {
			return false, fmt.Errorf("invalid ECDSA public key")
		}

		// Hash content with SHA512 - need to hash the original content, not the hash
		h512 := sha512.Sum512([]byte(contentHash))
		valid := ecdsa.VerifyASN1(ecdsaPub, h512[:], signature)
		if !valid {
			return false, fmt.Errorf("ECDSA-SHA512 signature verification failed")
		}
		return true, nil

	default:
		return false, fmt.Errorf("unsupported signature method: %s", signatureMethod)
	}
}

// CommentRequest represents the request structure for creating/updating comments
type CommentRequest struct {
	CommenterName  string `json:"commenter_name"` // Optional, will use authenticated user if not provided
	CommenterEmail string `json:"commenter_email"`
	Organization   string `json:"organization"`
	Subject        string `json:"subject"`
	Content        string `json:"content" binding:"required"`
	IsPublic       *bool  `json:"is_public"`
	IsVerified     *bool  `json:"is_verified"`
	IsModerated    *bool  `json:"is_moderated"`

	// Digital signature fields
	CertificateID    *uint  `json:"certificate_id"`
	SignatureData    string `json:"signature_data"`
	SignatureMethod  string `json:"signature_method"`
	RequireSignature bool   `json:"require_signature"`
}

// SignedCommentRequest represents a digitally signed comment submission
type SignedCommentRequest struct {
	CommentRequest
	DigitalSignature struct {
		SignatureValue string `json:"signature_value" binding:"required"`
		CertificateID  uint   `json:"certificate_id" binding:"required"`
		Timestamp      string `json:"timestamp"`
		HashAlgorithm  string `json:"hash_algorithm"`
		SigningMethod  string `json:"signing_method"`
	} `json:"digital_signature" binding:"required"`
}

// GetDocumentComments returns comments for a specific document
// @Summary Get document comments
// @Description Get comments for a specific document with pagination and filtering
// @Tags comments
// @Produce json
// @Param id path int true "Document ID"
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(25)
// @Param verified query bool false "Filter by verification status"
// @Param public query bool false "Filter by public visibility"
// @Success 200 {object} PaginationResponse
// @Failure 404 {object} ErrorResponse
// @Router /documents/{id}/comments [get]
func GetDocumentComments(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	pagination := GetPaginationParams(c)
	initCommentServices()

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists and accepts comments
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Build query for comments
	query := db.Model(&models.DocumentComment{}).
		Where("document_id = ?", id)

	// Apply filters
	if verifiedStr := c.Query("verified"); verifiedStr != "" {
		if verified, err := strconv.ParseBool(verifiedStr); err == nil {
			query = query.Where("is_verified = ?", verified)
		}
	}

	if publicStr := c.Query("public"); publicStr != "" {
		if public, err := strconv.ParseBool(publicStr); err == nil {
			query = query.Where("is_public = ?", public)
		}
	} else {
		// Default to public comments only for non-authenticated requests
		query = query.Where("is_public = ?", true)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to count comments",
			Message: err.Error(),
		})
		return
	}

	// Apply pagination and sorting
	offset := (pagination.Page - 1) * pagination.PerPage
	query = query.Offset(offset).Limit(pagination.PerPage).
		Order("created_at DESC")

	// Execute query
	var comments []models.DocumentComment
	if err := query.Find(&comments).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch comments",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	commentResponses := make([]gin.H, len(comments))
	for i, comment := range comments {
		commentResponse := gin.H{
			"id":              comment.ID,
			"document_id":     comment.DocumentID,
			"commenter_name":  comment.CommenterName,
			"commenter_email": comment.CommenterEmail,
			"organization":    comment.Organization,
			"subject":         comment.Subject,
			"content":         comment.Content,
			"is_public":       comment.IsPublic,
			"is_verified":     comment.IsVerified,
			"is_moderated":    comment.IsModerated,
			"created_at":      comment.CreatedAt.Format("2006-01-02T15:04:05Z"),
		}

		if comment.ModeratedAt != nil {
			commentResponse["moderated_at"] = comment.ModeratedAt.Format("2006-01-02T15:04:05Z")
		}
		if comment.Moderator != nil {
			commentResponse["moderator"] = gin.H{
				"id":        comment.Moderator.ID,
				"username":  comment.Moderator.Username,
				"full_name": comment.Moderator.FirstName + " " + comment.Moderator.LastName,
			}
		}

		commentResponses[i] = commentResponse
	}

	response := CreatePaginationResponse(commentResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// CreateComment creates a new comment on a document
// @Summary Create document comment
// @Description Create a new comment on a document with optional digital signature
// @Tags comments
// @Accept json
// @Produce json
// @Param id path int true "Document ID"
// @Param comment body CommentRequest true "Comment data"
// @Success 201 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /documents/{id}/comments [post]
func CreateComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var request CommentRequest
	if !BindJSON(c, &request) {
		return
	}

	initCommentServices()

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists and accepts comments
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	if !document.AcceptsComments {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Comments not accepted",
			Message: "This document does not accept comments",
		})
		return
	}

	// Check if comment period is still open
	if document.CommentDueDate != nil && time.Now().After(*document.CommentDueDate) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Comment period closed",
			Message: "The comment period for this document has ended",
		})
		return
	}

	// Get client IP address for tracking
	clientIP := c.ClientIP()

	// Get commenter name - use provided name or authenticated user's name
	commenterName := request.CommenterName
	if commenterName == "" {
		// Try to get authenticated user's name
		if userID, exists := c.Get("user_id"); exists {
			var user models.User
			if err := db.First(&user, userID).Error; err == nil {
				commenterName = user.FirstName + " " + user.LastName
				if commenterName == " " {
					commenterName = user.Username
				}
			}
		}
		// If still empty, use a default
		if commenterName == "" {
			commenterName = "Anonymous"
		}
	}

	// Create the comment
	comment := models.DocumentComment{
		DocumentID:     uint(id),
		CommenterName:  commenterName,
		CommenterEmail: request.CommenterEmail,
		Organization:   request.Organization,
		Subject:        request.Subject,
		Content:        request.Content,
		IsPublic:       true,
		IsVerified:     false,
		IPAddress:      clientIP,
		IsModerated:    false,
	}

	// Set optional fields
	if request.IsPublic != nil {
		comment.IsPublic = *request.IsPublic
	}
	if request.IsVerified != nil {
		comment.IsVerified = *request.IsVerified
	}
	if request.IsModerated != nil {
		comment.IsModerated = *request.IsModerated
	}

	// Handle digital signature if provided
	if request.CertificateID != nil && request.SignatureData != "" {
		// Verify certificate exists and is valid
		cert, err := digitalCertService.GetCertificate(*request.CertificateID, 0) // 0 for public verification
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid certificate",
				Message: "The provided certificate is not valid",
			})
			return
		}

		// Verify certificate is active and not expired
		if cert.Status != models.CertificateStatusActive {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Certificate not active",
				Message: "The provided certificate is not active",
			})
			return
		}

		if time.Now().After(cert.NotAfter) {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Certificate expired",
				Message: "The provided certificate has expired",
			})
			return
		}

		// Calculate content hash for signature verification
		contentHash := sha256.Sum256([]byte(request.Content))
		contentHashHex := hex.EncodeToString(contentHash[:])

		// Implement actual signature verification using contentHashHex
		// Verify the signature using the certificate's public key
		isSignatureValid, err := verifyDigitalSignature(cert.PublicKey, contentHashHex, request.SignatureData, request.SignatureMethod)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Signature verification failed",
				Message: fmt.Sprintf("Failed to verify digital signature: %v", err),
			})
			return
		}

		comment.IsVerified = isSignatureValid

		// Store signature metadata (you might want to create a separate table for this)
		comment.Subject = fmt.Sprintf("[SIGNED] %s", comment.Subject)

		// Log the hash for debugging (remove in production)
		fmt.Printf("Content hash for comment: %s\n", contentHashHex)
	}

	if err := db.Create(&comment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create comment",
			Message: err.Error(),
		})
		return
	}

	// Update document comment count
	if err := db.Model(&document).Update("comment_count", gorm.Expr("comment_count + 1")).Error; err != nil {
		fmt.Printf("Warning: Failed to update comment count for document %d: %v\n", document.ID, err)
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Comment submitted successfully",
		Data:    gin.H{"document_id": id, "comment_id": comment.ID},
	})
}

// CreateSignedComment creates a digitally signed comment
// @Summary Create signed comment
// @Description Create a new comment with digital signature verification
// @Tags comments
// @Accept json
// @Produce json
// @Param id path int true "Document ID"
// @Param comment body SignedCommentRequest true "Signed comment data"
// @Success 201 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Router /documents/{id}/comments/signed [post]
func CreateSignedComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var request SignedCommentRequest
	if !BindJSON(c, &request) {
		return
	}

	initCommentServices()

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists and accepts comments
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	if !document.AcceptsComments {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Comments not accepted",
			Message: "This document does not accept comments",
		})
		return
	}

	// Check if comment period is still open
	if document.CommentDueDate != nil && time.Now().After(*document.CommentDueDate) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Comment period closed",
			Message: "The comment period for this document has ended",
		})
		return
	}

	// Verify digital certificate
	cert, err := digitalCertService.GetCertificate(request.DigitalSignature.CertificateID, 0)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid certificate",
			Message: "The provided certificate is not valid",
		})
		return
	}

	// Verify certificate is active and not expired
	if cert.Status != models.CertificateStatusActive {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Certificate not active",
			Message: "The provided certificate is not active",
		})
		return
	}

	if time.Now().After(cert.NotAfter) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Certificate expired",
			Message: "The provided certificate has expired",
		})
		return
	}

	// Verify digital signature
	contentToSign := fmt.Sprintf("%s|%s|%s", request.CommenterName, request.Content, request.DigitalSignature.Timestamp)
	contentHash := sha256.Sum256([]byte(contentToSign))
	contentHashHex := hex.EncodeToString(contentHash[:])

	// Implement actual signature verification using the certificate's public key
	isSignatureValid, err := verifyDigitalSignature(cert.PublicKey, contentHashHex, request.DigitalSignature.SignatureValue, request.DigitalSignature.SigningMethod)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Signature verification failed",
			Message: fmt.Sprintf("Failed to verify digital signature: %v", err),
		})
		return
	}

	if !isSignatureValid {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid signature",
			Message: "The provided digital signature is not valid",
		})
		return
	}

	// Get client IP address for tracking
	clientIP := c.ClientIP()

	// Create the signed comment
	comment := models.DocumentComment{
		DocumentID:     uint(id),
		CommenterName:  request.CommenterName,
		CommenterEmail: request.CommenterEmail,
		Organization:   request.Organization,
		Subject:        fmt.Sprintf("[DIGITALLY SIGNED] %s", request.Subject),
		Content:        request.Content,
		IsPublic:       true,
		IsVerified:     true, // Digitally signed comments are automatically verified
		IPAddress:      clientIP,
		IsModerated:    false,
	}

	// Set optional fields
	if request.IsPublic != nil {
		comment.IsPublic = *request.IsPublic
	}

	if err := db.Create(&comment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create signed comment",
			Message: err.Error(),
		})
		return
	}

	// Create digital signature record
	signedAt := time.Now()
	signature := models.DigitalSignature{
		SignatureID:     fmt.Sprintf("comment_%d_%d", comment.ID, time.Now().Unix()),
		Type:            models.SignatureTypeAdvanced,
		Status:          models.SignatureStatusSigned,
		DocumentID:      document.ID,
		SignerID:        cert.OwnerID,
		SignerName:      request.CommenterName,
		SignerEmail:     request.CommenterEmail,
		DocumentHash:    contentHashHex,
		SignatureData:   request.DigitalSignature.SignatureValue,
		SignedAt:        &signedAt,
		CertificateID:   &cert.ID,
		HashAlgorithm:   models.HashAlgorithm(request.DigitalSignature.HashAlgorithm),
		SignatureMethod: request.DigitalSignature.SigningMethod,
		IsValid:         true,
		RequestedAt:     time.Now(),
		RequestedByID:   cert.OwnerID,
	}

	if err := db.Create(&signature).Error; err != nil {
		// Log error but don't fail the comment creation
		fmt.Printf("Warning: Failed to create signature record for comment %d: %v\n", comment.ID, err)
	}

	// Update document comment count
	if err := db.Model(&document).Update("comment_count", gorm.Expr("comment_count + 1")).Error; err != nil {
		fmt.Printf("Warning: Failed to update comment count for document %d: %v\n", document.ID, err)
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Signed comment submitted successfully",
		Data: gin.H{
			"document_id":  id,
			"comment_id":   comment.ID,
			"signature_id": signature.SignatureID,
			"verified":     true,
		},
	})
}

// GetComment returns a specific comment by ID
// @Summary Get comment
// @Description Get a specific comment by ID
// @Tags comments
// @Produce json
// @Param id path int true "Comment ID"
// @Success 200 {object} SuccessResponse
// @Failure 404 {object} ErrorResponse
// @Router /comments/{id} [get]
func GetComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	var comment models.DocumentComment
	if err := db.Preload("Document").First(&comment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Comment")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch comment",
			Message: err.Error(),
		})
		return
	}

	// Check if comment is public or user has permission to view
	if !comment.IsPublic {
		// Add permission check for non-public comments
		user, exists := middleware.GetCurrentUser(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "Authentication required",
				Message: "You must be logged in to view this comment",
			})
			return
		}

		// Admin users can view all comments
		if user.Role == models.RoleAdmin {
			// Allow access
		} else if user.Role == models.RoleReviewer || user.Role == models.RolePublisher {
			// Reviewers and publishers can view comments for moderation purposes
			// Allow access
		} else if comment.CommenterEmail == user.Email {
			// Users can view their own comments
			// Allow access
		} else {
			// Check if user has permission to view the associated document
			canView := middleware.CanViewDocument(c, &comment.Document)
			if !canView {
				c.JSON(http.StatusForbidden, ErrorResponse{
					Error:   "Access denied",
					Message: "You don't have permission to view this comment",
				})
				return
			}
		}
	}

	commentResponse := gin.H{
		"id":              comment.ID,
		"document_id":     comment.DocumentID,
		"commenter_name":  comment.CommenterName,
		"commenter_email": comment.CommenterEmail,
		"organization":    comment.Organization,
		"subject":         comment.Subject,
		"content":         comment.Content,
		"is_public":       comment.IsPublic,
		"is_verified":     comment.IsVerified,
		"is_moderated":    comment.IsModerated,
		"created_at":      comment.CreatedAt.Format("2006-01-02T15:04:05Z"),
		"updated_at":      comment.UpdatedAt.Format("2006-01-02T15:04:05Z"),
	}

	if comment.Document.ID != 0 {
		commentResponse["document"] = gin.H{
			"id":    comment.Document.ID,
			"title": comment.Document.Title,
		}
	}

	if comment.ModeratedAt != nil {
		commentResponse["moderated_at"] = comment.ModeratedAt.Format("2006-01-02T15:04:05Z")
	}
	if comment.Moderator != nil {
		commentResponse["moderator"] = gin.H{
			"id":        comment.Moderator.ID,
			"username":  comment.Moderator.Username,
			"full_name": comment.Moderator.FirstName + " " + comment.Moderator.LastName,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Comment retrieved successfully",
		Data:    commentResponse,
	})
}

// DeleteComment deletes a comment (admin only)
// @Summary Delete comment
// @Description Delete a comment (moderation purposes)
// @Tags comments
// @Param id path int true "Comment ID"
// @Success 200 {object} SuccessResponse
// @Failure 404 {object} ErrorResponse
// @Router /comments/{id} [delete]
func DeleteComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get current user for permission check
	currentUser, exists := c.Get("user")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	user, ok := currentUser.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid user data",
			Message: "User data type assertion failed",
		})
		return
	}

	// Only admin and editors can delete comments
	if user.Role != models.RoleAdmin && user.Role != models.RoleEditor {
		c.JSON(http.StatusForbidden, ErrorResponse{
			Error:   "Access denied",
			Message: "You don't have permission to delete comments",
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Check if comment exists
	var comment models.DocumentComment
	if err := db.First(&comment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Comment")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch comment",
			Message: err.Error(),
		})
		return
	}

	// Delete the comment (soft delete)
	if err := db.Delete(&comment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete comment",
			Message: err.Error(),
		})
		return
	}

	// Update document comment count
	var document models.Document
	if err := db.First(&document, comment.DocumentID).Error; err == nil {
		if err := db.Model(&document).Update("comment_count", gorm.Expr("comment_count - 1")).Error; err != nil {
			fmt.Printf("Warning: Failed to update comment count for document %d: %v\n", document.ID, err)
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Comment deleted successfully",
		Data:    gin.H{"comment_id": id},
	})
}

// VerifyCommentSignature verifies the digital signature of a comment
// @Summary Verify comment signature
// @Description Verify the digital signature of a comment
// @Tags comments
// @Produce json
// @Param id path int true "Comment ID"
// @Success 200 {object} SuccessResponse
// @Failure 404 {object} ErrorResponse
// @Router /comments/{id}/verify [get]
func VerifyCommentSignature(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	initCommentServices()

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get comment
	var comment models.DocumentComment
	if err := db.First(&comment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Comment")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch comment",
			Message: err.Error(),
		})
		return
	}

	// Find associated digital signature
	var signature models.DigitalSignature
	err := db.Where("document_id = ? AND signer_name = ? AND status = ?",
		comment.DocumentID, comment.CommenterName, models.SignatureStatusSigned).
		First(&signature).Error

	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "Signature not found",
			Message: "No digital signature found for this comment",
		})
		return
	}

	// Get certificate for verification
	var cert models.DigitalCertificate
	if signature.CertificateID != nil {
		if err := db.First(&cert, *signature.CertificateID).Error; err != nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "Certificate not found",
				Message: "Associated certificate not found",
			})
			return
		}
	}

	// Perform signature verification
	verificationResult := gin.H{
		"comment_id":        comment.ID,
		"signature_id":      signature.SignatureID,
		"is_verified":       comment.IsVerified,
		"signature_valid":   signature.IsValid,
		"certificate_valid": cert.Status == models.CertificateStatusActive,
		"signed_at":         signature.SignedAt,
		"signer_name":       signature.SignerName,
		"signer_email":      signature.SignerEmail,
	}

	if signature.CertificateID != nil {
		verificationResult["certificate"] = gin.H{
			"id":         cert.ID,
			"serial":     cert.SerialNumber,
			"subject":    cert.Subject,
			"issuer":     cert.Issuer,
			"not_before": cert.NotBefore.Format("2006-01-02T15:04:05Z"),
			"not_after":  cert.NotAfter.Format("2006-01-02T15:04:05Z"),
			"status":     cert.Status,
		}
	}

	// Additional verification checks
	// Implement actual hash verification
	documentHashMatch := false
	if signature.DocumentHash != "" {
		// Recalculate the content hash for the comment
		contentHash := sha256.Sum256([]byte(comment.Content))
		currentHashHex := hex.EncodeToString(contentHash[:])
		documentHashMatch = signature.DocumentHash == currentHashHex
	}

	verificationChecks := gin.H{
		"certificate_not_expired":   time.Now().Before(cert.NotAfter),
		"certificate_active":        cert.Status == models.CertificateStatusActive,
		"signature_timestamp_valid": signature.SignedAt != nil && signature.SignedAt.Before(time.Now()),
		"document_hash_match":       documentHashMatch,
	}

	verificationResult["verification_checks"] = verificationChecks

	// Overall verification status
	allChecksPass := verificationChecks["certificate_not_expired"].(bool) &&
		verificationChecks["certificate_active"].(bool) &&
		verificationChecks["signature_timestamp_valid"].(bool) &&
		verificationChecks["document_hash_match"].(bool)

	verificationResult["overall_valid"] = allChecksPass

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Signature verification completed",
		Data:    verificationResult,
	})
}

// CreatePublicComment creates a public comment on a document
func CreatePublicComment(c *gin.Context) {
	// This is an alias for CreateComment for public access
	CreateComment(c)
}

// GetUserCertificates returns digital certificates for the current user
// @Summary Get user certificates
// @Description Get digital certificates available for the current user
// @Tags certificates
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 401 {object} ErrorResponse
// @Router /user/certificates [get]
func GetUserCertificates(c *gin.Context) {
	// Get current user
	currentUser, exists := c.Get("user")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	user, ok := currentUser.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid user data",
			Message: "User data type assertion failed",
		})
		return
	}

	initCommentServices()

	// Get user's certificates
	certificates, err := digitalCertService.GetUserCertificates(user.ID, "")
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch certificates",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	certResponses := make([]gin.H, len(certificates))
	for i, cert := range certificates {
		certResponses[i] = gin.H{
			"id":          cert.ID,
			"serial":      cert.SerialNumber,
			"subject":     cert.Subject,
			"issuer":      cert.Issuer,
			"common_name": cert.CommonName,
			"not_before":  cert.NotBefore.Format("2006-01-02T15:04:05Z"),
			"not_after":   cert.NotAfter.Format("2006-01-02T15:04:05Z"),
			"status":      cert.Status,
			"is_default":  cert.IsDefault,
			"is_active":   cert.IsActive,
			"purpose":     cert.Purpose,
			"created_at":  cert.CreatedAt.Format("2006-01-02T15:04:05Z"),
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Certificates retrieved successfully",
		Data:    certResponses,
	})
}

// UpdateComment updates a comment
func UpdateComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Content     *string `json:"content"`
		Subject     *string `json:"subject"`
		IsPublic    *bool   `json:"is_public"`
		IsAnonymous *bool   `json:"is_anonymous"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing comment
	var comment models.DocumentComment
	if err := db.Preload("Document").First(&comment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Comment")
			return
		}
		HandleInternalError(c, "Failed to fetch comment: "+err.Error())
		return
	}

	// Check permissions using proper role-based permission system
	var user models.User
	if err := db.First(&user, userID).Error; err != nil {
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Check if user can update this comment
	canUpdate, err := canUserUpdateComment(&user, &comment, db)
	if err != nil {
		HandleInternalError(c, "Failed to check permissions: "+err.Error())
		return
	}

	if !canUpdate {
		HandleUnauthorized(c, "You don't have permission to update this comment")
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.Content != nil {
		updates["content"] = *req.Content
		// Update content hash if content changes
		contentHash := sha256.Sum256([]byte(*req.Content))
		updates["content_hash"] = hex.EncodeToString(contentHash[:])
	}
	if req.Subject != nil {
		updates["subject"] = *req.Subject
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}
	if req.IsAnonymous != nil {
		updates["is_anonymous"] = *req.IsAnonymous
		// If making anonymous, clear commenter info
		if *req.IsAnonymous {
			updates["commenter_name"] = "Anonymous"
			updates["commenter_email"] = ""
		} else {
			// If making non-anonymous, restore user info
			updates["commenter_name"] = user.FirstName + " " + user.LastName
			updates["commenter_email"] = user.Email
		}
	}

	// Update comment
	if err := db.Model(&comment).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update comment: "+err.Error())
		return
	}

	// Load updated comment with relationships
	if err := db.Preload("Document").First(&comment, comment.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated comment: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Comment updated successfully",
		Data:    comment,
	})
}

// Permission checking functions for comments

// canUserUpdateComment checks if a user can update a specific comment
func canUserUpdateComment(user *models.User, comment *models.DocumentComment, db *gorm.DB) (bool, error) {
	// Admin users can update any comment
	if user.Role == models.RoleAdmin {
		return true, nil
	}

	// Users can update their own comments (check by email)
	if comment.CommenterEmail == user.Email {
		return true, nil
	}

	// Moderators can update any comment for moderation purposes
	if user.Role == models.RoleReviewer || user.Role == models.RolePublisher {
		return true, nil
	}

	// Check if user has specific permission to moderate comments
	hasPermission, err := checkUserPermission(user.ID, "moderate_comments", db)
	if err != nil {
		return false, err
	}
	if hasPermission {
		return true, nil
	}

	// Check if user can edit the associated document (document owners can moderate comments)
	var document models.Document
	if err := db.First(&document, comment.DocumentID).Error; err != nil {
		return false, err
	}

	// Document creator can moderate comments on their document
	if document.CreatedByID == user.ID {
		return true, nil
	}

	// Same agency editors can moderate comments on agency documents
	if user.AgencyID != nil && document.AgencyID == *user.AgencyID {
		if user.Role == models.RoleEditor || user.Role == models.RoleReviewer || user.Role == models.RolePublisher {
			return true, nil
		}
	}

	return false, nil
}

// canUserDeleteComment checks if a user can delete a specific comment
func canUserDeleteComment(user *models.User, comment *models.DocumentComment, db *gorm.DB) (bool, error) {
	// Admin users can delete any comment
	if user.Role == models.RoleAdmin {
		return true, nil
	}

	// Users can delete their own comments (check by email)
	if comment.CommenterEmail == user.Email {
		return true, nil
	}

	// Moderators can delete any comment for moderation purposes
	if user.Role == models.RoleReviewer || user.Role == models.RolePublisher {
		return true, nil
	}

	// Check if user has specific permission to moderate comments
	hasPermission, err := checkUserPermission(user.ID, "moderate_comments", db)
	if err != nil {
		return false, err
	}
	if hasPermission {
		return true, nil
	}

	// Check if user can edit the associated document (document owners can moderate comments)
	var document models.Document
	if err := db.First(&document, comment.DocumentID).Error; err != nil {
		return false, err
	}

	// Document creator can moderate comments on their document
	if document.CreatedByID == user.ID {
		return true, nil
	}

	// Same agency editors can moderate comments on agency documents
	if user.AgencyID != nil && document.AgencyID == *user.AgencyID {
		if user.Role == models.RoleEditor || user.Role == models.RoleReviewer || user.Role == models.RolePublisher {
			return true, nil
		}
	}

	return false, nil
}

// checkUserPermission checks if a user has a specific permission
func checkUserPermission(userID uint, permissionName string, db *gorm.DB) (bool, error) {
	var count int64
	err := db.Table("permissions").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.is_active = ? AND permissions.name = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > ?)",
			userID, true, permissionName, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check user permission: %w", err)
	}

	return count > 0, nil
}
