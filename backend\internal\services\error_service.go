package services

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// ErrorService provides comprehensive error handling and logging
type ErrorService struct {
	db     *gorm.DB
	config ErrorConfig
}

// ErrorConfig configures error handling behavior
type ErrorConfig struct {
	EnableStackTrace   bool          `json:"enable_stack_trace"`
	EnableMetrics      bool          `json:"enable_metrics"`
	EnableNotification bool          `json:"enable_notification"`
	LogLevel           string        `json:"log_level"`
	MaxRetries         int           `json:"max_retries"`
	RetryDelay         time.Duration `json:"retry_delay"`
}

// ErrorLevel represents error severity levels
type ErrorLevel string

const (
	ErrorLevelDebug    ErrorLevel = "debug"
	ErrorLevelInfo     ErrorLevel = "info"
	ErrorLevelWarning  ErrorLevel = "warning"
	ErrorLevelError    ErrorLevel = "error"
	ErrorLevelCritical ErrorLevel = "critical"
	ErrorLevelFatal    ErrorLevel = "fatal"
)

// ErrorContext provides context for error logging
type ErrorContext struct {
	UserID     *uint                  `json:"user_id,omitempty"`
	SessionID  string                 `json:"session_id,omitempty"`
	RequestID  string                 `json:"request_id,omitempty"`
	Component  string                 `json:"component"`
	Operation  string                 `json:"operation"`
	Resource   string                 `json:"resource,omitempty"`
	Metadata   map[string]interface{} `json:"metadata,omitempty"`
	StackTrace string                 `json:"stack_trace,omitempty"`
	HTTPMethod string                 `json:"http_method,omitempty"`
	HTTPPath   string                 `json:"http_path,omitempty"`
	HTTPStatus int                    `json:"http_status,omitempty"`
	ClientIP   string                 `json:"client_ip,omitempty"`
	UserAgent  string                 `json:"user_agent,omitempty"`
}

// StructuredError represents a structured error with context
type StructuredError struct {
	ID          string       `json:"id"`
	Timestamp   time.Time    `json:"timestamp"`
	Level       ErrorLevel   `json:"level"`
	Message     string       `json:"message"`
	Error       string       `json:"error,omitempty"`
	Context     ErrorContext `json:"context"`
	Tags        []string     `json:"tags,omitempty"`
	Fingerprint string       `json:"fingerprint"`
	Count       int          `json:"count"`
	FirstSeen   time.Time    `json:"first_seen"`
	LastSeen    time.Time    `json:"last_seen"`
	Resolved    bool         `json:"resolved"`
	ResolvedAt  *time.Time   `json:"resolved_at,omitempty"`
	ResolvedBy  *uint        `json:"resolved_by,omitempty"`
}

// ErrorMetrics represents error metrics and statistics
type ErrorMetrics struct {
	TotalErrors       int64                   `json:"total_errors"`
	ErrorsByLevel     map[ErrorLevel]int64    `json:"errors_by_level"`
	ErrorsByComponent map[string]int64        `json:"errors_by_component"`
	ErrorsByOperation map[string]int64        `json:"errors_by_operation"`
	ErrorRate         float64                 `json:"error_rate"`
	MeanTimeToResolve time.Duration           `json:"mean_time_to_resolve"`
	TopErrors         []StructuredError       `json:"top_errors"`
	RecentErrors      []StructuredError       `json:"recent_errors"`
	ErrorTrends       map[string][]ErrorTrend `json:"error_trends"`
}

// ErrorTrend represents error trends over time
type ErrorTrend struct {
	Timestamp time.Time  `json:"timestamp"`
	Count     int64      `json:"count"`
	Level     ErrorLevel `json:"level"`
}

// ErrorAlert represents an error alert configuration
type ErrorAlert struct {
	ID            uint          `json:"id"`
	Name          string        `json:"name"`
	Description   string        `json:"description"`
	Conditions    string        `json:"conditions"` // JSON string of alert conditions
	Enabled       bool          `json:"enabled"`
	Recipients    []string      `json:"recipients"`
	Channels      []string      `json:"channels"` // email, slack, webhook, etc.
	Cooldown      time.Duration `json:"cooldown"`
	LastTriggered *time.Time    `json:"last_triggered,omitempty"`
}

// NewErrorService creates a new error service
func NewErrorService(db *gorm.DB) *ErrorService {
	return &ErrorService{
		db: db,
		config: ErrorConfig{
			EnableStackTrace:   true,
			EnableMetrics:      true,
			EnableNotification: true,
			LogLevel:           "warning",
			MaxRetries:         3,
			RetryDelay:         time.Second * 5,
		},
	}
}

// LogError logs a structured error with context
func (s *ErrorService) LogError(ctx context.Context, level ErrorLevel, message string, err error, errorCtx ErrorContext) *StructuredError {
	structuredErr := &StructuredError{
		ID:        s.generateErrorID(),
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Context:   errorCtx,
		FirstSeen: time.Now(),
		LastSeen:  time.Now(),
		Count:     1,
	}

	if err != nil {
		structuredErr.Error = err.Error()
	}

	// Add stack trace if enabled
	if s.config.EnableStackTrace {
		structuredErr.Context.StackTrace = s.captureStackTrace()
	}

	// Generate fingerprint for error grouping
	structuredErr.Fingerprint = s.generateFingerprint(structuredErr)

	// Check for existing error with same fingerprint
	existingError := s.findExistingError(structuredErr.Fingerprint)
	if existingError != nil {
		// Update existing error
		existingError.Count++
		existingError.LastSeen = time.Now()
		s.updateError(existingError)
		structuredErr = existingError
	} else {
		// Save new error
		s.saveError(structuredErr)
	}

	// Update metrics if enabled
	if s.config.EnableMetrics {
		s.updateErrorMetrics(structuredErr)
	}

	// Check alert conditions
	s.checkAlertConditions(structuredErr)

	// Log to console/file for immediate visibility
	s.logToOutput(structuredErr)

	return structuredErr
}

// LogCriticalError logs a critical error and triggers immediate alerts
func (s *ErrorService) LogCriticalError(ctx context.Context, message string, err error, errorCtx ErrorContext) *StructuredError {
	structuredErr := s.LogError(ctx, ErrorLevelCritical, message, err, errorCtx)

	// Immediate notification for critical errors
	s.sendImmediateAlert(structuredErr)

	return structuredErr
}

// LogHTTPError logs HTTP-related errors with request context
func (s *ErrorService) LogHTTPError(ctx context.Context, level ErrorLevel, message string, err error, method, path string, status int, clientIP, userAgent string, userID *uint) *StructuredError {
	errorCtx := ErrorContext{
		UserID:     userID,
		Component:  "http",
		Operation:  fmt.Sprintf("%s %s", method, path),
		HTTPMethod: method,
		HTTPPath:   path,
		HTTPStatus: status,
		ClientIP:   clientIP,
		UserAgent:  userAgent,
	}

	return s.LogError(ctx, level, message, err, errorCtx)
}

// LogDatabaseError logs database-related errors
func (s *ErrorService) LogDatabaseError(ctx context.Context, level ErrorLevel, message string, err error, operation, table string, userID *uint) *StructuredError {
	errorCtx := ErrorContext{
		UserID:    userID,
		Component: "database",
		Operation: operation,
		Resource:  table,
		Metadata: map[string]interface{}{
			"database_operation": operation,
			"table_name":         table,
		},
	}

	return s.LogError(ctx, level, message, err, errorCtx)
}

// LogServiceError logs service-related errors
func (s *ErrorService) LogServiceError(ctx context.Context, level ErrorLevel, message string, err error, service, operation string, metadata map[string]interface{}) *StructuredError {
	errorCtx := ErrorContext{
		Component: service,
		Operation: operation,
		Metadata:  metadata,
	}

	return s.LogError(ctx, level, message, err, errorCtx)
}

// GetErrorMetrics retrieves comprehensive error metrics
func (s *ErrorService) GetErrorMetrics(timeRange time.Duration) (*ErrorMetrics, error) {
	since := time.Now().Add(-timeRange)

	metrics := &ErrorMetrics{
		ErrorsByLevel:     make(map[ErrorLevel]int64),
		ErrorsByComponent: make(map[string]int64),
		ErrorsByOperation: make(map[string]int64),
		ErrorTrends:       make(map[string][]ErrorTrend),
	}

	// Get total error count
	var totalErrors int64
	err := s.db.Model(&models.SystemEvent{}).
		Where("event_type = ? AND created_at >= ?", "error", since).
		Count(&totalErrors).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get total error count: %w", err)
	}
	metrics.TotalErrors = totalErrors

	// Get errors by level
	var levelCounts []struct {
		Level string
		Count int64
	}
	err = s.db.Model(&models.SystemEvent{}).
		Select("metadata->>'level' as level, count(*) as count").
		Where("event_type = ? AND created_at >= ?", "error", since).
		Group("metadata->>'level'").
		Scan(&levelCounts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get errors by level: %w", err)
	}

	for _, lc := range levelCounts {
		metrics.ErrorsByLevel[ErrorLevel(lc.Level)] = lc.Count
	}

	// Get errors by component
	var componentCounts []struct {
		Component string
		Count     int64
	}
	err = s.db.Model(&models.SystemEvent{}).
		Select("metadata->>'component' as component, count(*) as count").
		Where("event_type = ? AND created_at >= ?", "error", since).
		Group("metadata->>'component'").
		Scan(&componentCounts).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get errors by component: %w", err)
	}

	for _, cc := range componentCounts {
		metrics.ErrorsByComponent[cc.Component] = cc.Count
	}

	// Calculate error rate (errors per minute)
	minutes := timeRange.Minutes()
	if minutes > 0 {
		metrics.ErrorRate = float64(totalErrors) / minutes
	}

	// Get recent errors
	var recentEvents []models.SystemEvent
	err = s.db.Where("event_type = ? AND created_at >= ?", "error", since).
		Order("created_at DESC").
		Limit(10).
		Find(&recentEvents).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get recent errors: %w", err)
	}

	for _, event := range recentEvents {
		structuredErr := s.convertEventToStructuredError(&event)
		metrics.RecentErrors = append(metrics.RecentErrors, *structuredErr)
	}

	return metrics, nil
}

// ResolveError marks an error as resolved
func (s *ErrorService) ResolveError(errorID string, resolvedBy uint) error {
	now := time.Now()

	// Update in database
	err := s.db.Model(&models.SystemEvent{}).
		Where("id = ? AND event_type = ?", errorID, "error").
		Updates(map[string]interface{}{
			"metadata":   gorm.Expr("jsonb_set(metadata, '{resolved}', 'true')"),
			"updated_at": now,
		}).Error

	if err != nil {
		return fmt.Errorf("failed to resolve error: %w", err)
	}

	return nil
}

// CreateErrorAlert creates a new error alert configuration
func (s *ErrorService) CreateErrorAlert(alert *ErrorAlert) error {
	// In production, this would save to a dedicated alerts table
	// For now, simulate the creation
	alert.ID = uint(time.Now().Unix())

	// Validate alert conditions
	if err := s.validateAlertConditions(alert.Conditions); err != nil {
		return fmt.Errorf("invalid alert conditions: %w", err)
	}

	return nil
}

// Helper methods for error processing

// generateErrorID generates a unique error ID
func (s *ErrorService) generateErrorID() string {
	return fmt.Sprintf("err_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

// captureStackTrace captures the current stack trace
func (s *ErrorService) captureStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}

// generateFingerprint generates a fingerprint for error grouping
func (s *ErrorService) generateFingerprint(err *StructuredError) string {
	// Create fingerprint based on error message, component, and operation
	fingerprint := fmt.Sprintf("%s:%s:%s", err.Context.Component, err.Context.Operation, err.Message)

	// Add error type if available
	if err.Error != "" {
		fingerprint += ":" + err.Error
	}

	// Simple hash for fingerprint
	hash := 0
	for _, char := range fingerprint {
		hash = hash*31 + int(char)
	}

	return fmt.Sprintf("fp_%x", hash)
}

// findExistingError finds an existing error with the same fingerprint
func (s *ErrorService) findExistingError(fingerprint string) *StructuredError {
	var event models.SystemEvent
	err := s.db.Where("event_type = ? AND data::jsonb->>'fingerprint' = ?", "error", fingerprint).
		Order("created_at DESC").
		First(&event).Error

	if err != nil {
		return nil
	}

	return s.convertEventToStructuredError(&event)
}

// saveError saves a new error to the database
func (s *ErrorService) saveError(structuredErr *StructuredError) error {
	metadata, _ := json.Marshal(structuredErr)

	event := &models.SystemEvent{
		EventType: "error",
		Data:      string(metadata),
		UserID:    structuredErr.Context.UserID,
		Error:     structuredErr.Message,
	}

	return s.db.Create(event).Error
}

// updateError updates an existing error
func (s *ErrorService) updateError(structuredErr *StructuredError) error {
	metadata, _ := json.Marshal(structuredErr)

	return s.db.Model(&models.SystemEvent{}).
		Where("event_type = ? AND data::jsonb->>'fingerprint' = ?", "error", structuredErr.Fingerprint).
		Updates(map[string]interface{}{
			"data": string(metadata),
		}).Error
}

// updateErrorMetrics updates error metrics
func (s *ErrorService) updateErrorMetrics(structuredErr *StructuredError) {
	// In production, this would update metrics in a time-series database
	// For now, we'll log the metric update
	fmt.Printf("Updating error metrics for %s: %s\n", structuredErr.Level, structuredErr.Message)
}

// checkAlertConditions checks if error triggers any alerts
func (s *ErrorService) checkAlertConditions(structuredErr *StructuredError) {
	// In production, this would check against configured alert rules
	// For now, trigger alerts for critical errors
	if structuredErr.Level == ErrorLevelCritical || structuredErr.Level == ErrorLevelFatal {
		s.triggerAlert(structuredErr)
	}
}

// triggerAlert triggers an alert for the error
func (s *ErrorService) triggerAlert(structuredErr *StructuredError) {
	fmt.Printf("ALERT: %s error in %s: %s\n",
		structuredErr.Level,
		structuredErr.Context.Component,
		structuredErr.Message)
}

// sendImmediateAlert sends immediate alert for critical errors
func (s *ErrorService) sendImmediateAlert(structuredErr *StructuredError) {
	// In production, this would send notifications via email, Slack, etc.
	fmt.Printf("IMMEDIATE ALERT: Critical error detected - %s\n", structuredErr.Message)
}

// logToOutput logs error to console/file
func (s *ErrorService) logToOutput(structuredErr *StructuredError) {
	timestamp := structuredErr.Timestamp.Format("2006-01-02 15:04:05")
	fmt.Printf("[%s] %s [%s:%s] %s\n",
		timestamp,
		structuredErr.Level,
		structuredErr.Context.Component,
		structuredErr.Context.Operation,
		structuredErr.Message)

	if structuredErr.Error != "" {
		fmt.Printf("  Error: %s\n", structuredErr.Error)
	}

	if structuredErr.Context.StackTrace != "" && s.config.EnableStackTrace {
		fmt.Printf("  Stack Trace:\n%s\n", structuredErr.Context.StackTrace)
	}
}

// convertEventToStructuredError converts a SystemEvent to StructuredError
func (s *ErrorService) convertEventToStructuredError(event *models.SystemEvent) *StructuredError {
	var structuredErr StructuredError

	if err := json.Unmarshal([]byte(event.Data), &structuredErr); err != nil {
		// Fallback if unmarshal fails
		structuredErr = StructuredError{
			ID:        fmt.Sprintf("event_%d", event.ID),
			Timestamp: event.CreatedAt,
			Level:     ErrorLevelError,
			Message:   event.Error,
			Context: ErrorContext{
				UserID: event.UserID,
			},
		}
	}

	return &structuredErr
}

// validateAlertConditions validates alert condition syntax
func (s *ErrorService) validateAlertConditions(conditions string) error {
	// In production, this would parse and validate the conditions syntax
	// For now, just check if it's valid JSON
	var conditionsMap map[string]interface{}
	return json.Unmarshal([]byte(conditions), &conditionsMap)
}
