import apiService from './api';
import {
  LawsAndRules,
  RegulationWithChunks,
  RegulationDocumentVersion,
  RegulationFilters,
  CreateRegulationRequest,
  AmendChunkRequest,
  PaginatedResponse,
  RegulationDocumentRelationship,
  RegulationAgencyRelationship,
  RegulationCategoryRelationship,
  CreateRegulationDocumentRelationshipRequest,
  CreateRegulationAgencyRelationshipRequest,
  CreateRegulationCategoryRelationshipRequest,
  UpdateRegulationRelationshipRequest,
  RegulationRelationshipsSummary,
  RelationshipStats
} from '../types';

class RegulationApiService {
  private baseUrl = '/public/regulations';
  private protectedUrl = '/regulations';

  // Public API methods (no authentication required)

  /**
   * Get published regulations for public access
   */
  async getPublicRegulations(filters: RegulationFilters = {}): Promise<PaginatedResponse<LawsAndRules>> {
    const params = new URLSearchParams();
    
    if (filters.type) params.append('type', filters.type);
    if (filters.agency_id) params.append('agency_id', filters.agency_id.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.effective_after) params.append('effective_after', filters.effective_after);
    if (filters.effective_before) params.append('effective_before', filters.effective_before);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.per_page) params.append('per_page', filters.per_page.toString());
    if (filters.sort_by) params.append('sort_by', filters.sort_by);
    if (filters.sort_order) params.append('sort_order', filters.sort_order);

    const queryString = params.toString();
    const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;
    
    return apiService.get<PaginatedResponse<LawsAndRules>>(url);
  }

  /**
   * Get a specific regulation with its hierarchical structure
   */
  async getPublicRegulation(id: number, versionId?: number): Promise<RegulationWithChunks> {
    const params = new URLSearchParams();
    if (versionId) params.append('version', versionId.toString());
    
    const queryString = params.toString();
    const url = queryString ? `${this.baseUrl}/${id}?${queryString}` : `${this.baseUrl}/${id}`;
    
    return apiService.get<RegulationWithChunks>(url);
  }

  /**
   * Get all versions of a regulation
   */
  async getPublicRegulationVersions(id: number): Promise<RegulationDocumentVersion[]> {
    const response = await apiService.get<{ data: RegulationDocumentVersion[] }>(`${this.baseUrl}/${id}/versions`);
    return response.data;
  }

  // Protected API methods (authentication required)

  /**
   * Get regulations for authenticated users (includes all statuses)
   */
  async getRegulations(filters: RegulationFilters = {}): Promise<PaginatedResponse<LawsAndRules>> {
    const params = new URLSearchParams();
    
    if (filters.type) params.append('type', filters.type);
    if (filters.agency_id) params.append('agency_id', filters.agency_id.toString());
    if (filters.search) params.append('search', filters.search);
    if (filters.effective_after) params.append('effective_after', filters.effective_after);
    if (filters.effective_before) params.append('effective_before', filters.effective_before);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.per_page) params.append('per_page', filters.per_page.toString());
    if (filters.sort_by) params.append('sort_by', filters.sort_by);
    if (filters.sort_order) params.append('sort_order', filters.sort_order);

    const queryString = params.toString();
    const url = queryString ? `${this.protectedUrl}?${queryString}` : this.protectedUrl;
    
    return apiService.get<PaginatedResponse<LawsAndRules>>(url);
  }

  /**
   * Create a new regulation
   */
  async createRegulation(regulation: CreateRegulationRequest): Promise<{ id: number }> {
    const response = await apiService.post<{ data: { id: number } }>(this.protectedUrl, regulation);
    return response.data;
  }

  /**
   * Get a regulation with management capabilities
   */
  async getRegulation(id: number, versionId?: number): Promise<RegulationWithChunks> {
    const params = new URLSearchParams();
    if (versionId) params.append('version', versionId.toString());

    const queryString = params.toString();
    // Use authenticated endpoint for management capabilities
    const url = queryString ? `${this.protectedUrl}/${id}?${queryString}` : `${this.protectedUrl}/${id}`;

    return apiService.get<RegulationWithChunks>(url);
  }

  /**
   * Get regulation versions (uses same handler as public endpoint)
   */
  async getRegulationVersions(id: number): Promise<RegulationDocumentVersion[]> {
    const response = await apiService.get<{ data: RegulationDocumentVersion[] }>(`${this.protectedUrl}/${id}/versions`);
    return response.data;
  }

  /**
   * Amend a specific chunk of a regulation
   */
  async amendChunk(regulationId: number, chunkId: number, amendment: AmendChunkRequest): Promise<any> {
    const response = await apiService.post<{ data: any }>(
      `${this.protectedUrl}/${regulationId}/chunks/${chunkId}/amend`,
      amendment
    );
    return response.data;
  }

  /**
   * Publish a new official version of a regulation
   */
  async publishVersion(regulationId: number, notes?: string): Promise<RegulationDocumentVersion> {
    const response = await apiService.post<{ data: RegulationDocumentVersion }>(
      `${this.protectedUrl}/${regulationId}/publish`,
      { notes: notes || '' }
    );
    return response.data;
  }

  /**
   * Update an existing regulation
   */
  async updateRegulation(id: number, regulation: CreateRegulationRequest): Promise<void> {
    await apiService.put(`${this.protectedUrl}/${id}`, regulation);
  }

  /**
   * Delete a regulation (soft delete)
   */
  async deleteRegulation(id: number): Promise<void> {
    await apiService.delete(`${this.protectedUrl}/${id}`);
  }

  // Utility methods

  /**
   * Get regulation types for dropdowns
   */
  getRegulationTypes() {
    return [
      { value: 'law', label: 'Law' },
      { value: 'rule', label: 'Rule' },
      { value: 'regulation', label: 'Regulation' },
      { value: 'code', label: 'Code' }
    ];
  }

  /**
   * Get regulation statuses for dropdowns
   */
  getRegulationStatuses() {
    return [
      { value: 'draft', label: 'Draft' },
      { value: 'under_review', label: 'Under Review' },
      { value: 'approved', label: 'Approved' },
      { value: 'published', label: 'Published' },
      { value: 'effective', label: 'Effective' },
      { value: 'terminated', label: 'Terminated' },
      { value: 'archived', label: 'Archived' }
    ];
  }

  /**
   * Get regulation with structured content (chunks)
   */
  async getRegulationWithChunks(id: number, versionId?: number): Promise<RegulationWithChunks> {
    const params = versionId ? { version: versionId.toString() } : {};
    const response = await fetch(`/api/v1/regulations/${id}/structured-content?${new URLSearchParams(params)}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * Add structured content to a regulation
   */
  async addStructuredContent(id: number, content: { sections: any[] }): Promise<any> {
    const response = await fetch(`/api/v1/regulations/${id}/structured-content`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(content)
    });
    const data = await response.json();
    return data.data;
  }

  /**
   * Generate default structured content for a regulation
   */
  async generateDefaultStructure(id: number): Promise<any> {
    const response = await fetch(`/api/v1/regulations/${id}/generate-structure`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });
    const data = await response.json();
    return data.data;
  }

  /**
   * Get structured content for a regulation
   */
  async getStructuredContent(id: number, versionId?: number): Promise<RegulationWithChunks> {
    const params = versionId ? { version: versionId.toString() } : {};
    const response = await fetch(`/api/v1/regulations/${id}/structured-content?${new URLSearchParams(params)}`);
    const data = await response.json();
    return data.data;
  }

  /**
   * Update structured content for a regulation
   */
  async updateStructuredContent(id: number, content: { sections: any[] }): Promise<any> {
    const response = await fetch(`/api/v1/regulations/${id}/structured-content`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(content)
    });
    const data = await response.json();
    return data.data;
  }

  /**
   * Delete structured content for a regulation
   */
  async deleteStructuredContent(id: number): Promise<any> {
    const response = await fetch(`/api/v1/regulations/${id}/structured-content`, {
      method: 'DELETE'
    });
    const data = await response.json();
    return data.data;
  }

  /**
   * Get chunk types for dropdowns
   */
  getChunkTypes() {
    return [
      { value: 'title', label: 'Title' },
      { value: 'division', label: 'Division' },
      { value: 'chapter', label: 'Chapter' },
      { value: 'subtitle', label: 'Subtitle' },
      { value: 'section', label: 'Section' },
      { value: 'subsection', label: 'Subsection' },
      { value: 'paragraph', label: 'Paragraph' },
      { value: 'clause', label: 'Clause' },
      { value: 'subclause', label: 'Subclause' }
    ];
  }

  /**
   * Format regulation title for display
   */
  formatRegulationTitle(regulation: LawsAndRules): string {
    if (regulation.short_title) {
      return `${regulation.short_title} (${regulation.title})`;
    }
    return regulation.title;
  }

  /**
   * Format version number for display
   */
  formatVersionNumber(version: RegulationDocumentVersion): string {
    return `v${version.version_number}${version.is_official ? ' (Official)' : ' (Draft)'}`;
  }

  /**
   * Get status color for UI
   */
  getStatusColor(status: string): string {
    switch (status) {
      case 'draft':
        return 'gray';
      case 'under_review':
        return 'yellow';
      case 'approved':
        return 'blue';
      case 'published':
        return 'green';
      case 'effective':
        return 'emerald';
      case 'terminated':
        return 'red';
      case 'archived':
        return 'slate';
      default:
        return 'gray';
    }
  }

  /**
   * Get type color for UI
   */
  getTypeColor(type: string): string {
    switch (type) {
      case 'law':
        return 'purple';
      case 'rule':
        return 'blue';
      case 'regulation':
        return 'green';
      case 'code':
        return 'orange';
      default:
        return 'gray';
    }
  }

  /**
   * Check if regulation is effective
   */
  isEffective(regulation: LawsAndRules): boolean {
    if (!regulation.effective_date) return false;
    const effectiveDate = new Date(regulation.effective_date);
    const now = new Date();
    
    // Check if effective and not terminated
    const isAfterEffective = now >= effectiveDate;
    const isBeforeTermination = !regulation.termination_date || now <= new Date(regulation.termination_date);
    
    return isAfterEffective && isBeforeTermination;
  }

  // Regulation Relationship Management

  /**
   * Get all relationships for a regulation
   */
  async getRegulationRelationships(id: number): Promise<RegulationRelationshipsSummary> {
    return apiService.get<RegulationRelationshipsSummary>(`${this.protectedUrl}/${id}/relationships`);
  }

  // Document Relationships

  /**
   * Get document relationships for a regulation
   */
  async getRegulationDocuments(id: number): Promise<RegulationDocumentRelationship[]> {
    const response = await apiService.get<{ data: RegulationDocumentRelationship[] }>(`${this.protectedUrl}/${id}/documents`);
    return response.data;
  }

  /**
   * Create a document relationship
   */
  async createRegulationDocumentRelationship(id: number, request: CreateRegulationDocumentRelationshipRequest): Promise<{ id: number }> {
    const response = await apiService.post<{ data: { id: number } }>(`${this.protectedUrl}/${id}/documents`, request);
    return response.data;
  }

  /**
   * Update a document relationship
   */
  async updateRegulationDocumentRelationship(id: number, relationshipId: number, request: UpdateRegulationRelationshipRequest): Promise<RegulationDocumentRelationship> {
    const response = await apiService.put<{ data: RegulationDocumentRelationship }>(`${this.protectedUrl}/${id}/documents/${relationshipId}`, request);
    return response.data;
  }

  /**
   * Delete a document relationship
   */
  async deleteRegulationDocumentRelationship(id: number, relationshipId: number): Promise<void> {
    await apiService.delete(`${this.protectedUrl}/${id}/documents/${relationshipId}`);
  }

  // Agency Relationships

  /**
   * Get agency relationships for a regulation
   */
  async getRegulationAgencies(id: number): Promise<RegulationAgencyRelationship[]> {
    const response = await apiService.get<{ data: RegulationAgencyRelationship[] }>(`${this.protectedUrl}/${id}/agencies`);
    return response.data;
  }

  /**
   * Create an agency relationship
   */
  async createRegulationAgencyRelationship(id: number, request: CreateRegulationAgencyRelationshipRequest): Promise<{ id: number }> {
    const response = await apiService.post<{ data: { id: number } }>(`${this.protectedUrl}/${id}/agencies`, request);
    return response.data;
  }

  /**
   * Update an agency relationship
   */
  async updateRegulationAgencyRelationship(id: number, relationshipId: number, request: UpdateRegulationRelationshipRequest): Promise<RegulationAgencyRelationship> {
    const response = await apiService.put<{ data: RegulationAgencyRelationship }>(`${this.protectedUrl}/${id}/agencies/${relationshipId}`, request);
    return response.data;
  }

  /**
   * Delete an agency relationship
   */
  async deleteRegulationAgencyRelationship(id: number, relationshipId: number): Promise<void> {
    await apiService.delete(`${this.protectedUrl}/${id}/agencies/${relationshipId}`);
  }

  // Category Relationships

  /**
   * Get category relationships for a regulation
   */
  async getRegulationCategories(id: number): Promise<RegulationCategoryRelationship[]> {
    const response = await apiService.get<{ data: RegulationCategoryRelationship[] }>(`${this.protectedUrl}/${id}/categories`);
    return response.data;
  }

  /**
   * Create a category relationship
   */
  async createRegulationCategoryRelationship(id: number, request: CreateRegulationCategoryRelationshipRequest): Promise<{ id: number }> {
    const response = await apiService.post<{ data: { id: number } }>(`${this.protectedUrl}/${id}/categories`, request);
    return response.data;
  }

  /**
   * Update a category relationship
   */
  async updateRegulationCategoryRelationship(id: number, relationshipId: number, request: UpdateRegulationRelationshipRequest): Promise<RegulationCategoryRelationship> {
    const response = await apiService.put<{ data: RegulationCategoryRelationship }>(`${this.protectedUrl}/${id}/categories/${relationshipId}`, request);
    return response.data;
  }

  /**
   * Delete a category relationship
   */
  async deleteRegulationCategoryRelationship(id: number, relationshipId: number): Promise<void> {
    await apiService.delete(`${this.protectedUrl}/${id}/categories/${relationshipId}`);
  }

  // Reverse Lookups

  /**
   * Get regulations related to a document
   */
  async getDocumentRegulations(documentId: number): Promise<RegulationDocumentRelationship[]> {
    const response = await apiService.get<{ data: RegulationDocumentRelationship[] }>(`/documents/${documentId}/regulations`);
    return response.data;
  }

  /**
   * Get regulations related to an agency
   */
  async getAgencyRegulations(agencyId: number): Promise<RegulationAgencyRelationship[]> {
    const response = await apiService.get<{ data: RegulationAgencyRelationship[] }>(`/agencies/${agencyId}/regulations`);
    return response.data;
  }

  /**
   * Get regulations related to a category
   */
  async getCategoryRegulations(categoryId: number): Promise<RegulationCategoryRelationship[]> {
    const response = await apiService.get<{ data: RegulationCategoryRelationship[] }>(`/categories/${categoryId}/regulations`);
    return response.data;
  }

  /**
   * Get relationship statistics
   */
  async getRelationshipStats(): Promise<RelationshipStats> {
    const response = await apiService.get<{ data: RelationshipStats }>('/analytics/relationships/stats');
    return response.data;
  }

  /**
   * Get effectiveness status
   */
  getEffectivenessStatus(regulation: LawsAndRules): 'not_yet_effective' | 'effective' | 'terminated' {
    if (!regulation.effective_date) return 'not_yet_effective';
    
    const effectiveDate = new Date(regulation.effective_date);
    const now = new Date();
    
    if (now < effectiveDate) {
      return 'not_yet_effective';
    }
    
    if (regulation.termination_date && now > new Date(regulation.termination_date)) {
      return 'terminated';
    }
    
    return 'effective';
  }

  /**
   * Format date for display
   */
  formatDate(dateString?: string): string {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}

export default new RegulationApiService();
