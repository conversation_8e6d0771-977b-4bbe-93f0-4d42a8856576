'use client'

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  BuildingOfficeIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon,
  PencilIcon,
  TrashIcon,
  ArrowLeftIcon,
  DocumentTextIcon,
  UserGroupIcon,
  CalendarIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import { Agency } from '../../types';

const AgencyDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [agency, setAgency] = useState<Agency | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [recentDocuments, setRecentDocuments] = useState<any[]>([]);
  const [stats, setStats] = useState({
    total_documents: 0,
    published_documents: 0,
    draft_documents: 0,
    recent_activity: 0
  });

  const agencyId = params?.id as string;

  useEffect(() => {
    if (!agencyId) return;

    const fetchAgency = async () => {
      try {
        setLoading(true);
        const response = await apiService.getAgency(parseInt(agencyId));
        setAgency(response.data);

        // Fetch recent documents from this agency
        try {
          const documentsResponse = await apiService.getDocuments({
            agency_id: agencyId,
            per_page: 5,
            sort: 'created_at',
            order: 'desc' as 'asc' | 'desc'
          });
          setRecentDocuments(documentsResponse.data || []);
        } catch (err) {
          console.log('Recent documents not available');
        }

        // Fetch agency statistics
        try {
          const statsResponse = await apiService.getAgencyStats(parseInt(agencyId));
          setStats(statsResponse.data);
        } catch (err) {
          console.log('Agency stats not available');
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch agency');
      } finally {
        setLoading(false);
      }
    };

    fetchAgency();
  }, [agencyId]);

  const handleDelete = async () => {
    if (!agency || !confirm('Are you sure you want to delete this agency?')) return;
    
    try {
      await apiService.deleteAgency(agency.id);
      router.push('/agencies');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete agency');
    }
  };

  const canEdit = () => {
    if (!user) return false;
    return user.role === 'admin' || user.role === 'editor';
  };

  const canDelete = () => {
    if (!user) return false;
    return user.role === 'admin';
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !agency) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <BuildingOfficeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Agency not found</h3>
            <p className="text-gray-600 mb-4">{error || 'The requested agency could not be found.'}</p>
            <Link
              href="/agencies"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Agencies
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/agencies"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Agencies
          </Link>
          
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
            <div className="flex items-start space-x-4 flex-1">
              {/* Agency Logo */}
              <div className="flex-shrink-0">
                {agency.logo_url ? (
                  <img
                    className="h-16 w-16 rounded-lg object-cover"
                    src={agency.logo_url}
                    alt={`${agency.name} logo`}
                  />
                ) : (
                  <div className="h-16 w-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    <BuildingOfficeIcon className="h-8 w-8 text-gray-500" />
                  </div>
                )}
              </div>
              
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    agency.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {agency.is_active ? 'ACTIVE' : 'INACTIVE'}
                  </span>
                  {agency.agency_type && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 capitalize">
                      {agency.agency_type}
                    </span>
                  )}
                </div>
                
                <h1 className="text-3xl font-bold text-gray-900 mb-2">{agency.name}</h1>
                <p className="text-lg text-gray-600 mb-4">{agency.short_name}</p>
                
                {agency.description && (
                  <p className="text-gray-700 mb-4">{agency.description}</p>
                )}
              </div>
            </div>
            
            {/* Actions */}
            {isAuthenticated && agency && (
              <div className="flex items-center space-x-2 mt-4 lg:mt-0">
                {canEdit() && (
                  <Link
                    href={`/agencies/${agency.id}/edit`}
                    className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    title="Edit Agency"
                  >
                    <PencilIcon className="h-5 w-5" />
                  </Link>
                )}
                {canDelete() && (
                  <button
                    onClick={handleDelete}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete Agency"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Mission Statement */}
            {agency.mission_statement && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 className="text-lg font-semibold text-blue-900 mb-3">Mission Statement</h2>
                <p className="text-blue-800">{agency.mission_statement}</p>
              </div>
            )}

            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <DocumentTextIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{stats.total_documents}</div>
                <div className="text-sm text-gray-600">Total Documents</div>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{stats.published_documents}</div>
                <div className="text-sm text-gray-600">Published</div>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6 text-center">
                <CalendarIcon className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{stats.recent_activity}</div>
                <div className="text-sm text-gray-600">Recent Activity</div>
              </div>
            </div>

            {/* Recent Documents */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">Recent Documents</h2>
                <Link
                  href={`/documents?agency_id=${agency.id}`}
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                >
                  View all
                </Link>
              </div>
              
              {recentDocuments.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No documents found.</p>
              ) : (
                <div className="space-y-4">
                  {recentDocuments.map((document) => (
                    <div key={document.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">
                            <Link 
                              href={`/documents/${document.id}`}
                              className="hover:text-primary-600"
                            >
                              {document.title}
                            </Link>
                          </h4>
                          <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                            {document.abstract || document.summary || 'No description available'}
                          </p>
                          <div className="flex items-center space-x-4 text-xs text-gray-500 mt-2">
                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                              document.status === 'published' ? 'bg-green-100 text-green-800' :
                              document.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {document.status?.toUpperCase()}
                            </span>
                            <span>{new Date(document.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
              
              <div className="space-y-4">
                {agency.website && (
                  <div className="flex items-start">
                    <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Website</p>
                      <a 
                        href={agency.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {agency.website}
                      </a>
                    </div>
                  </div>
                )}

                {agency.email && (
                  <div className="flex items-start">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Email</p>
                      <a 
                        href={`mailto:${agency.email}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {agency.email}
                      </a>
                    </div>
                  </div>
                )}

                {agency.phone && (
                  <div className="flex items-start">
                    <PhoneIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Phone</p>
                      <a 
                        href={`tel:${agency.phone}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {agency.phone}
                      </a>
                    </div>
                  </div>
                )}

                {(agency.address || agency.city || agency.state) && (
                  <div className="flex items-start">
                    <MapPinIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Address</p>
                      <div className="text-sm text-gray-600">
                        {agency.address && <div>{agency.address}</div>}
                        {(agency.city || agency.state || agency.zip_code) && (
                          <div>
                            {agency.city}{agency.city && agency.state && ', '}{agency.state} {agency.zip_code}
                          </div>
                        )}
                        {agency.country && agency.country !== 'United States' && (
                          <div>{agency.country}</div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Primary Contact */}
            {(agency.contact_person || agency.contact_email || agency.contact_phone) && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Primary Contact</h3>
                
                <div className="space-y-3">
                  {agency.contact_person && (
                    <div>
                      <p className="text-sm font-medium text-gray-900">Contact Person</p>
                      <p className="text-sm text-gray-600">{agency.contact_person}</p>
                      {agency.contact_title && (
                        <p className="text-sm text-gray-500">{agency.contact_title}</p>
                      )}
                    </div>
                  )}

                  {agency.contact_email && (
                    <div>
                      <p className="text-sm font-medium text-gray-900">Email</p>
                      <a 
                        href={`mailto:${agency.contact_email}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {agency.contact_email}
                      </a>
                    </div>
                  )}

                  {agency.contact_phone && (
                    <div>
                      <p className="text-sm font-medium text-gray-900">Phone</p>
                      <a 
                        href={`tel:${agency.contact_phone}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {agency.contact_phone}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Additional Information */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>
              
              <div className="space-y-3">
                {agency.established_date && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Established</p>
                    <p className="text-sm text-gray-600">
                      {new Date(agency.established_date).toLocaleDateString()}
                    </p>
                  </div>
                )}

                {agency.jurisdiction && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Jurisdiction</p>
                    <p className="text-sm text-gray-600">{agency.jurisdiction}</p>
                  </div>
                )}

                {agency.parent_agency && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Parent Agency</p>
                    <Link 
                      href={`/agencies/${agency.parent_agency.id}`}
                      className="text-sm text-primary-600 hover:text-primary-700"
                    >
                      {agency.parent_agency.name}
                    </Link>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-900">Created</p>
                  <p className="text-sm text-gray-600">
                    {new Date(agency.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AgencyDetailPage;
