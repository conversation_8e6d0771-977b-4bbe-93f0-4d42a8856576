'use client';

import React, { useState, useEffect } from 'react';
import {
  CogIcon,
  DocumentTextIcon,
  ScaleIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  NewspaperIcon,
  CalendarIcon,
  LinkIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import apiService from '../../services/api';

interface AutoGenerationConfig {
  id?: number;
  entity_type: string;
  enable_summary_generation: boolean;
  enable_task_generation: boolean;
  enable_calendar_generation: boolean;
  enable_finance_generation: boolean;
  enable_relationship_generation: boolean;
  config_data?: any;
}

const AutoGenerationSettings: React.FC = () => {
  const [configs, setConfigs] = useState<AutoGenerationConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const entityTypes = [
    { key: 'document', label: 'Documents', icon: DocumentTextIcon },
    { key: 'regulation', label: 'Regulations', icon: ScaleIcon },
    { key: 'task', label: 'Tasks', icon: ClipboardDocumentListIcon },
    { key: 'finance', label: 'Finance', icon: CurrencyDollarIcon }
  ];

  const generationTypes = [
    { key: 'enable_summary_generation', label: 'Summary Generation', icon: NewspaperIcon, description: 'Auto-generate news-style summaries' },
    { key: 'enable_task_generation', label: 'Task Generation', icon: ClipboardDocumentListIcon, description: 'Auto-create tasks from deadlines' },
    { key: 'enable_calendar_generation', label: 'Calendar Generation', icon: CalendarIcon, description: 'Auto-create calendar events' },
    { key: 'enable_finance_generation', label: 'Finance Generation', icon: CurrencyDollarIcon, description: 'Auto-estimate costs and budgets' },
    { key: 'enable_relationship_generation', label: 'Relationship Generation', icon: LinkIcon, description: 'Auto-link related entities' }
  ];

  useEffect(() => {
    fetchConfigs();
  }, []);

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/api/auto-generation/configs');
      
      // Ensure we have configs for all entity types
      const existingConfigs = (response as any).data || [];
      const allConfigs = entityTypes.map(entityType => {
        const existing = existingConfigs.find((c: AutoGenerationConfig) => c.entity_type === entityType.key);
        return existing || {
          entity_type: entityType.key,
          enable_summary_generation: true,
          enable_task_generation: true,
          enable_calendar_generation: true,
          enable_finance_generation: true,
          enable_relationship_generation: true
        };
      });
      
      setConfigs(allConfigs);
    } catch (error) {
      console.error('Failed to fetch auto-generation configs:', error);
      setMessage({ type: 'error', text: 'Failed to load settings' });
    } finally {
      setLoading(false);
    }
  };

  const updateConfig = (entityType: string, field: keyof AutoGenerationConfig, value: boolean) => {
    setConfigs(prev => prev.map(config => 
      config.entity_type === entityType 
        ? { ...config, [field]: value }
        : config
    ));
  };

  const saveConfigs = async () => {
    try {
      setSaving(true);
      await apiService.post('/api/auto-generation/configs', { configs });
      setMessage({ type: 'success', text: 'Settings saved successfully' });
    } catch (error) {
      console.error('Failed to save configs:', error);
      setMessage({ type: 'error', text: 'Failed to save settings' });
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    const defaultConfigs = entityTypes.map(entityType => ({
      entity_type: entityType.key,
      enable_summary_generation: true,
      enable_task_generation: true,
      enable_calendar_generation: true,
      enable_finance_generation: true,
      enable_relationship_generation: true
    }));
    setConfigs(defaultConfigs);
  };

  const ToggleSwitch: React.FC<{
    enabled: boolean;
    onChange: (enabled: boolean) => void;
    disabled?: boolean;
  }> = ({ enabled, onChange, disabled = false }) => (
    <button
      type="button"
      className={`${
        enabled ? 'bg-blue-600' : 'bg-gray-200'
      } relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      }`}
      role="switch"
      aria-checked={enabled}
      onClick={() => !disabled && onChange(!enabled)}
      disabled={disabled}
    >
      <span
        aria-hidden="true"
        className={`${
          enabled ? 'translate-x-5' : 'translate-x-0'
        } pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out`}
      />
    </button>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <CogIcon className="w-8 h-8 text-blue-500 mr-3" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Auto-Generation Settings</h1>
            <p className="text-gray-600 mt-1">
              Configure automatic generation of related content for each entity type
            </p>
          </div>
        </div>
      </div>

      {/* Message */}
      {message && (
        <div className={`rounded-md p-4 ${
          message.type === 'success' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex">
            {message.type === 'success' ? (
              <CheckIcon className="h-5 w-5 text-green-400" />
            ) : (
              <XMarkIcon className="h-5 w-5 text-red-400" />
            )}
            <div className="ml-3">
              <p className={`text-sm font-medium ${
                message.type === 'success' ? 'text-green-800' : 'text-red-800'
              }`}>
                {message.text}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Settings Grid */}
      <div className="space-y-6">
        {entityTypes.map(entityType => {
          const config = configs.find(c => c.entity_type === entityType.key);
          if (!config) return null;

          return (
            <div key={entityType.key} className="bg-white shadow rounded-lg p-6">
              <div className="flex items-center mb-6">
                <entityType.icon className="w-6 h-6 text-blue-500 mr-3" />
                <h2 className="text-lg font-medium text-gray-900">{entityType.label}</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {generationTypes.map(genType => (
                  <div key={genType.key} className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      <ToggleSwitch
                        enabled={config[genType.key as keyof AutoGenerationConfig] as boolean}
                        onChange={(enabled) => updateConfig(entityType.key, genType.key as keyof AutoGenerationConfig, enabled)}
                        disabled={saving}
                      />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <genType.icon className="w-4 h-4 text-gray-400 mr-2" />
                        <h3 className="text-sm font-medium text-gray-900">{genType.label}</h3>
                      </div>
                      <p className="text-sm text-gray-500 mt-1">{genType.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          );
        })}
      </div>

      {/* Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Actions</h3>
            <p className="text-sm text-gray-500 mt-1">
              Save your changes or reset to default settings
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={resetToDefaults}
              disabled={saving}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
            >
              Reset to Defaults
            </button>
            <button
              onClick={saveConfigs}
              disabled={saving}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          </div>
        </div>
      </div>

      {/* Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-blue-900 mb-3">How Auto-Generation Works</h3>
        <div className="space-y-2 text-sm text-blue-800">
          <p>• <strong>Summary Generation:</strong> Creates news-style summaries when entities are created, updated, or published</p>
          <p>• <strong>Task Generation:</strong> Extracts deadlines from content and creates reminder tasks automatically</p>
          <p>• <strong>Calendar Generation:</strong> Creates calendar events for important dates like effective dates and deadlines</p>
          <p>• <strong>Finance Generation:</strong> Estimates costs and creates budget records based on content analysis</p>
          <p>• <strong>Relationship Generation:</strong> Automatically links related entities based on content similarity and context</p>
        </div>
      </div>
    </div>
  );
};

export default AutoGenerationSettings;
