'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { biApi } from '../../../../services/enterpriseApi';
import { Dashboard } from '../../../../types/enterprise';

const DashboardViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const dashboardId = parseInt(params.id as string);
  
  const [dashboard, setDashboard] = useState<Dashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (dashboardId) {
      fetchDashboard();
    }
  }, [dashboardId]);

  const fetchDashboard = async () => {
    try {
      setLoading(true);
      const response = await biApi.getDashboard(dashboardId);
      setDashboard(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this dashboard?')) return;
    
    try {
      await biApi.deleteDashboard(dashboardId);
      router.push('/enterprise/bi/dashboards');
    } catch (err: any) {
      setError(err.message || 'Failed to delete dashboard');
    }
  };

  const handleRefresh = async () => {
    try {
      await biApi.refreshDashboard(dashboardId);
      await fetchDashboard(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Failed to refresh dashboard');
    }
  };

  if (loading) return <div className="p-6">Loading dashboard...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!dashboard) return <div className="p-6">Dashboard not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Dashboard Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/bi/dashboards')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Dashboards
          </button>
          <button
            onClick={handleRefresh}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            Refresh Dashboard
          </button>
          <button
            onClick={() => router.push(`/enterprise/bi/dashboards/${dashboardId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Dashboard
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Dashboard
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{dashboard.dashboard_name}</h2>
              <p className="text-sm text-gray-600">Code: {dashboard.dashboard_code}</p>
              <p className="text-sm text-gray-600">Category: {dashboard.category}</p>
            </div>
            <div className="text-right">
              <div className="flex flex-col space-y-2">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  dashboard.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {dashboard.is_active ? 'Active' : 'Inactive'}
                </span>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  dashboard.is_public 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {dashboard.is_public ? 'Public' : 'Private'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard Overview */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Dashboard Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 capitalize">
                {dashboard.dashboard_type.replace('_', ' ')}
              </div>
              <div className="text-sm text-blue-600">Dashboard Type</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {Math.floor(dashboard.refresh_interval / 60)} min
              </div>
              <div className="text-sm text-green-600">Refresh Interval</div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {dashboard.view_count || 0}
              </div>
              <div className="text-sm text-orange-600">Total Views</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {dashboard.avg_load_time ? `${dashboard.avg_load_time.toFixed(2)}s` : 'N/A'}
              </div>
              <div className="text-sm text-purple-600">Avg Load Time</div>
            </div>
          </div>
        </div>

        {/* Dashboard Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Dashboard Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Owner ID</label>
              <p className="mt-1 text-sm text-gray-900">{dashboard.owner_id}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Share Token</label>
              <p className="mt-1 text-sm text-gray-900 font-mono text-xs">
                {dashboard.share_token || 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Cache Enabled</label>
              <p className="mt-1 text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  dashboard.cache_enabled 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {dashboard.cache_enabled ? 'Yes' : 'No'}
                </span>
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Cache Duration</label>
              <p className="mt-1 text-sm text-gray-900">
                {dashboard.cache_duration ? `${Math.floor(dashboard.cache_duration / 60)} minutes` : 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Last Refreshed</label>
              <p className="mt-1 text-sm text-gray-900">
                {dashboard.last_refreshed ? 
                  new Date(dashboard.last_refreshed).toLocaleString() : 
                  'Never'
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Last Viewed</label>
              <p className="mt-1 text-sm text-gray-900">
                {dashboard.last_viewed ? 
                  new Date(dashboard.last_viewed).toLocaleString() : 
                  'Never'
                }
              </p>
            </div>
          </div>

          {dashboard.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {dashboard.description}
              </div>
            </div>
          )}
        </div>

        {/* Layout Configuration */}
        {dashboard.layout_config && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Layout Configuration</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">
                {JSON.stringify(JSON.parse(dashboard.layout_config || '{}'), null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Widgets */}
        {dashboard.widgets && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Widgets</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">
                {dashboard.widgets}
              </pre>
            </div>
          </div>
        )}

        {/* Access Control */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Access Control</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Shared With</label>
              <p className="mt-1 text-sm text-gray-900">
                {dashboard.shared_with || 'Not shared'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Access List</label>
              <p className="mt-1 text-sm text-gray-900">
                {dashboard.access_list || 'No restrictions'}
              </p>
            </div>
          </div>
        </div>

        {/* Tags */}
        {dashboard.tags && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {dashboard.tags.split(',').map((tag, index) => (
                <span 
                  key={index}
                  className="inline-flex px-3 py-1 text-sm font-medium bg-blue-100 text-blue-800 rounded-full"
                >
                  {tag.trim()}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Performance Metrics */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {dashboard.view_count || 0}
              </div>
              <div className="text-sm text-blue-600">Total Views</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {dashboard.avg_load_time ? `${dashboard.avg_load_time.toFixed(2)}s` : 'N/A'}
              </div>
              <div className="text-sm text-green-600">Average Load Time</div>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {dashboard.refresh_interval}s
              </div>
              <div className="text-sm text-orange-600">Refresh Interval</div>
            </div>
          </div>
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(dashboard.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(dashboard.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardViewPage;
