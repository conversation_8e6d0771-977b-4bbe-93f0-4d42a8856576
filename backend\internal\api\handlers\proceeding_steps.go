package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// ProceedingStepRequest represents the request structure for proceeding steps
type ProceedingStepRequest struct {
	ProceedingID uint   `json:"proceeding_id" binding:"required"`
	Title        string `json:"title" binding:"required"`
	Description  string `json:"description"`
	StepOrder    int    `json:"step_order" binding:"required"`
	Status       string `json:"status"`
	IsRequired   bool   `json:"is_required"`
	AssignedTo   uint   `json:"assigned_to"`
}

// GetProceedingSteps returns steps for a proceeding
func GetProceedingSteps(c *gin.Context) {
	proceedingID, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify proceeding exists
	var proceeding models.Proceeding
	if err := db.First(&proceeding, proceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Get steps for this proceeding
	var steps []models.ProceedingStep
	if err := db.Preload("AssignedTo").
		Where("proceeding_id = ?", proceedingID).
		Order("step_order ASC").
		Find(&steps).Error; err != nil {
		HandleInternalError(c, "Failed to fetch proceeding steps: "+err.Error())
		return
	}

	// Convert to response format
	stepResponses := make([]gin.H, len(steps))
	for i, step := range steps {
		stepResponses[i] = gin.H{
			"id":            step.ID,
			"proceeding_id": step.ProceedingID,
			"name":          step.Name,
			"description":   step.Description,
			"step_order":    step.StepOrder,
			"step_type":     step.StepType,
			"status":        step.Status,
			"is_mandatory":  step.IsMandatory,
			"created_at":    step.CreatedAt,
			"updated_at":    step.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding steps retrieved successfully",
		Data:    stepResponses,
	})
}

// GetProceedingStep returns a single proceeding step by ID
func GetProceedingStep(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get proceeding step
	var step models.ProceedingStep
	if err := db.Preload("AssignedTo").
		Preload("Proceeding").
		First(&step, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding step")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding step: "+err.Error())
		return
	}

	response := gin.H{
		"id":           step.ID,
		"proceeding":   step.Proceeding,
		"name":         step.Name,
		"description":  step.Description,
		"step_order":   step.StepOrder,
		"step_type":    step.StepType,
		"status":       step.Status,
		"is_mandatory": step.IsMandatory,
		"created_at":   step.CreatedAt,
		"updated_at":   step.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding step retrieved successfully",
		Data:    response,
	})
}

// CreateProceedingStep creates a new proceeding step
func CreateProceedingStep(c *gin.Context) {
	var req ProceedingStepRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify proceeding exists
	var proceeding models.Proceeding
	if err := db.First(&proceeding, req.ProceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid proceeding",
				Message: "Proceeding not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Create proceeding step
	step := &models.ProceedingStep{
		ProceedingID: req.ProceedingID,
		Name:         req.Title,
		Description:  req.Description,
		StepOrder:    req.StepOrder,
		StepType:     models.ProceedingStepType("standard"),
		Status:       models.ProceedingStepStatus(req.Status),
		IsMandatory:  req.IsRequired,
	}

	if err := db.Create(step).Error; err != nil {
		HandleInternalError(c, "Failed to create proceeding step: "+err.Error())
		return
	}

	// Load related data
	db.Preload("AssignedTo").First(step, step.ID)

	response := gin.H{
		"id":            step.ID,
		"proceeding_id": step.ProceedingID,
		"name":          step.Name,
		"description":   step.Description,
		"step_order":    step.StepOrder,
		"step_type":     step.StepType,
		"status":        step.Status,
		"is_mandatory":  step.IsMandatory,
		"created_at":    step.CreatedAt,
		"updated_at":    step.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Proceeding step created successfully",
		Data:    response,
	})
}

// UpdateProceedingStep updates an existing proceeding step
func UpdateProceedingStep(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req ProceedingStepRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing proceeding step
	var step models.ProceedingStep
	if err := db.First(&step, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding step")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding step: "+err.Error())
		return
	}

	// Update proceeding step fields
	step.Name = req.Title
	step.Description = req.Description
	step.StepOrder = req.StepOrder
	step.Status = models.ProceedingStepStatus(req.Status)
	step.IsMandatory = req.IsRequired

	if err := db.Save(&step).Error; err != nil {
		HandleInternalError(c, "Failed to update proceeding step: "+err.Error())
		return
	}

	// Load related data
	db.Preload("AssignedTo").First(&step, step.ID)

	response := gin.H{
		"id":            step.ID,
		"proceeding_id": step.ProceedingID,
		"name":          step.Name,
		"description":   step.Description,
		"step_order":    step.StepOrder,
		"step_type":     step.StepType,
		"status":        step.Status,
		"is_mandatory":  step.IsMandatory,
		"created_at":    step.CreatedAt,
		"updated_at":    step.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding step updated successfully",
		Data:    response,
	})
}

// DeleteProceedingStep deletes a proceeding step
func DeleteProceedingStep(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if proceeding step exists
	var step models.ProceedingStep
	if err := db.First(&step, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding step")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding step: "+err.Error())
		return
	}

	// Delete proceeding step
	if err := db.Delete(&step).Error; err != nil {
		HandleInternalError(c, "Failed to delete proceeding step: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding step deleted successfully",
	})
}

// CompleteProceedingStep marks a proceeding step as completed
func CompleteProceedingStep(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get proceeding step
	var step models.ProceedingStep
	if err := db.First(&step, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding step")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding step: "+err.Error())
		return
	}

	// Mark as completed
	step.Status = "completed"
	// step.CompletedAt = time.Now() // Uncomment when time field is available

	if err := db.Save(&step).Error; err != nil {
		HandleInternalError(c, "Failed to complete proceeding step: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding step completed successfully",
		Data: gin.H{
			"id":     step.ID,
			"status": step.Status,
		},
	})
}
