package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// RegulationRelationshipRequest represents the request structure for regulation relationships
type RegulationRelationshipRequest struct {
	SourceRegulationID uint   `json:"source_regulation_id" binding:"required"`
	TargetRegulationID uint   `json:"target_regulation_id" binding:"required"`
	RelationshipType   string `json:"relationship_type" binding:"required"`
	Description        string `json:"description"`
	IsActive           bool   `json:"is_active"`
}

// GetRegulationRelationships returns relationships for a regulation
func GetRegulationRelationships(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get relationships where this regulation is the source
	var sourceRelationships []models.RegulationRelationship
	db.Preload("TargetRegulation").
		Where("source_regulation_id = ? AND is_active = ?", regulationID, true).
		Find(&sourceRelationships)

	// Get relationships where this regulation is the target
	var targetRelationships []models.RegulationRelationship
	db.Preload("SourceRegulation").
		Where("target_regulation_id = ? AND is_active = ?", regulationID, true).
		Find(&targetRelationships)

	// Convert to response format
	sourceResponses := make([]gin.H, len(sourceRelationships))
	for i, rel := range sourceRelationships {
		sourceResponses[i] = gin.H{
			"id":                rel.ID,
			"target_regulation": rel.TargetRegulation,
			"relationship_type": rel.RelationshipType,
			"description":       rel.Description,
			"created_at":        rel.CreatedAt,
		}
	}

	targetResponses := make([]gin.H, len(targetRelationships))
	for i, rel := range targetRelationships {
		targetResponses[i] = gin.H{
			"id":                rel.ID,
			"source_regulation": rel.SourceRegulation,
			"relationship_type": rel.RelationshipType,
			"description":       rel.Description,
			"created_at":        rel.CreatedAt,
		}
	}

	response := gin.H{
		"regulation_id":          regulationID,
		"outgoing_relationships": sourceResponses,
		"incoming_relationships": targetResponses,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation relationships retrieved successfully",
		Data:    response,
	})
}

// CreateRegulationRelationship creates a new regulation relationship
func CreateRegulationRelationship(c *gin.Context) {
	var req RegulationRelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify source regulation exists
	var sourceRegulation models.LawsAndRules
	if err := db.First(&sourceRegulation, req.SourceRegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid source regulation",
				Message: "Source regulation not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch source regulation: "+err.Error())
		return
	}

	// Verify target regulation exists
	var targetRegulation models.LawsAndRules
	if err := db.First(&targetRegulation, req.TargetRegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid target regulation",
				Message: "Target regulation not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch target regulation: "+err.Error())
		return
	}

	// Check if relationship already exists
	var existingRelationship models.RegulationRelationship
	if err := db.Where("source_regulation_id = ? AND target_regulation_id = ? AND relationship_type = ?",
		req.SourceRegulationID, req.TargetRegulationID, req.RelationshipType).
		First(&existingRelationship).Error; err == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Relationship already exists",
			Message: "This relationship already exists between the regulations",
		})
		return
	}

	// Create regulation relationship
	relationship := &models.RegulationRelationship{
		SourceRegulationID: req.SourceRegulationID,
		TargetRegulationID: req.TargetRegulationID,
		RelationshipType:   req.RelationshipType,
		Description:        req.Description,
		IsActive:           req.IsActive,
	}

	if err := db.Create(relationship).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation relationship: "+err.Error())
		return
	}

	// Load related data
	db.Preload("SourceRegulation").Preload("TargetRegulation").First(relationship, relationship.ID)

	response := gin.H{
		"id":                  relationship.ID,
		"source_regulation":   relationship.SourceRegulation,
		"target_regulation":   relationship.TargetRegulation,
		"relationship_type":   relationship.RelationshipType,
		"description":         relationship.Description,
		"is_active":           relationship.IsActive,
		"created_at":          relationship.CreatedAt,
		"updated_at":          relationship.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation relationship created successfully",
		Data:    response,
	})
}

// UpdateRegulationRelationship updates an existing regulation relationship
func UpdateRegulationRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RegulationRelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing regulation relationship
	var relationship models.RegulationRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation relationship: "+err.Error())
		return
	}

	// Update regulation relationship fields
	relationship.RelationshipType = req.RelationshipType
	relationship.Description = req.Description
	relationship.IsActive = req.IsActive

	if err := db.Save(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation relationship: "+err.Error())
		return
	}

	// Load related data
	db.Preload("SourceRegulation").Preload("TargetRegulation").First(&relationship, relationship.ID)

	response := gin.H{
		"id":                  relationship.ID,
		"source_regulation":   relationship.SourceRegulation,
		"target_regulation":   relationship.TargetRegulation,
		"relationship_type":   relationship.RelationshipType,
		"description":         relationship.Description,
		"is_active":           relationship.IsActive,
		"created_at":          relationship.CreatedAt,
		"updated_at":          relationship.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation relationship updated successfully",
		Data:    response,
	})
}

// DeleteRegulationRelationship deletes a regulation relationship
func DeleteRegulationRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if regulation relationship exists
	var relationship models.RegulationRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation relationship: "+err.Error())
		return
	}

	// Delete regulation relationship
	if err := db.Delete(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to delete regulation relationship: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation relationship deleted successfully",
	})
}
