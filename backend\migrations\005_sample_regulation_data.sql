-- Sample Regulation Data
-- Creates sample regulations with hierarchical structure for testing

-- Insert sample regulation
INSERT INTO laws_and_rules (
    title,
    short_title,
    type,
    status,
    cfr_title,
    agency_id,
    created_by_id,
    description,
    effective_date,
    publication_date,
    is_significant
) VALUES (
    'Environmental Protection Standards',
    'EPA Standards',
    'rule',
    'effective',
    '40',
    1, -- Assuming EPA agency exists with ID 1
    1, -- Assuming admin user exists with ID 1
    'Comprehensive environmental protection standards for air quality, water quality, and waste management.',
    '2024-01-01 00:00:00',
    '2023-12-01 00:00:00',
    true
);

-- Get the ID of the inserted regulation
-- Note: In a real migration, you'd use RETURNING or a sequence

-- Insert document version for the regulation
INSERT INTO regulation_document_versions (
    law_rule_id,
    version_number,
    publication_date,
    effective_date,
    is_official,
    created_by_id,
    notes
) VALUES (
    1, -- Assuming the regulation got ID 1
    '1.0.0',
    '2023-12-01 00:00:00',
    '2024-01-01 00:00:00',
    true,
    1,
    'Initial version of environmental protection standards'
);

-- Update the regulation to point to the current document version
UPDATE laws_and_rules SET current_document_version_id = 1 WHERE id = 1;

-- Insert hierarchical chunks for the regulation
-- Chapter I
INSERT INTO regulation_chunks (
    law_rule_id,
    parent_chunk_id,
    order_in_parent,
    chunk_type,
    chunk_identifier,
    number,
    title
) VALUES (
    1,
    NULL,
    1,
    'chapter',
    'EPA-CHAPTER-I',
    'I',
    'Environmental Protection Agency'
);

-- Title I under Chapter I
INSERT INTO regulation_chunks (
    law_rule_id,
    parent_chunk_id,
    order_in_parent,
    chunk_type,
    chunk_identifier,
    number,
    title
) VALUES (
    1,
    1, -- Chapter I
    1,
    'title',
    'EPA-CHAPTER-I-TITLE-I',
    'I',
    'Environmental Rules'
);

-- Section 1011 - EPA Standard
INSERT INTO regulation_chunks (
    law_rule_id,
    parent_chunk_id,
    order_in_parent,
    chunk_type,
    chunk_identifier,
    number,
    title
) VALUES (
    1,
    2, -- Title I
    1,
    'section',
    'EPA-CHAPTER-I-TITLE-I-SECTION-1011',
    '1011',
    'EPA Standard'
);

-- Section 1012 - EPA Guideline
INSERT INTO regulation_chunks (
    law_rule_id,
    parent_chunk_id,
    order_in_parent,
    chunk_type,
    chunk_identifier,
    number,
    title
) VALUES (
    1,
    2, -- Title I
    2,
    'section',
    'EPA-CHAPTER-I-TITLE-I-SECTION-1012',
    '1012',
    'EPA Guideline'
);

-- Insert content versions for the chunks
-- Content for EPA Standard (Section 1011)
INSERT INTO regulation_chunk_content_versions (
    chunk_id,
    content,
    version_number,
    is_current,
    is_active,
    modified_by_id,
    change_description
) VALUES (
    3, -- Section 1011
    'This section establishes the fundamental standards for environmental protection. All facilities must comply with air quality standards as defined in this regulation. Monitoring requirements include continuous emission monitoring systems (CEMS) for major sources.',
    1,
    true,
    true,
    1,
    'Initial content for EPA Standard'
);

-- Content for EPA Guideline (Section 1012)
INSERT INTO regulation_chunk_content_versions (
    chunk_id,
    content,
    version_number,
    is_current,
    is_active,
    modified_by_id,
    change_description
) VALUES (
    4, -- Section 1012
    'This section provides guidelines for implementing the EPA standards. Facilities should follow best practices for environmental monitoring and reporting. Regular inspections and compliance audits are recommended.',
    1,
    true,
    true,
    1,
    'Initial content for EPA Guideline'
);

-- Update chunks to point to their current content versions
UPDATE regulation_chunks SET current_chunk_content_version_id = 1 WHERE id = 3;
UPDATE regulation_chunks SET current_chunk_content_version_id = 2 WHERE id = 4;

-- Insert chunk mappings for the document version (only for chunks with content)
INSERT INTO regulation_document_version_chunk_maps (
    document_version_id,
    chunk_id,
    chunk_content_version_id
) VALUES
(1, 3, 1),    -- Section 1011 with content version 1
(1, 4, 2);    -- Section 1012 with content version 2

-- Insert a second regulation for testing
INSERT INTO laws_and_rules (
    title,
    short_title,
    type,
    status,
    cfr_title,
    agency_id,
    created_by_id,
    description,
    effective_date,
    publication_date,
    is_significant
) VALUES (
    'Telecommunications Regulations',
    'FCC Rules',
    'regulation',
    'effective',
    '47',
    1, -- Use existing agency ID 1
    1,
    'Federal Communications Commission regulations for telecommunications services.',
    '2024-02-01 00:00:00',
    '2024-01-15 00:00:00',
    false
);
