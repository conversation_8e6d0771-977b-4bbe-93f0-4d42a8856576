package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// RegulationStructuredContentRequest represents the request structure for regulation structured content
type RegulationStructuredContentRequest struct {
	RegulationID uint   `json:"regulation_id" binding:"required"`
	SectionType  string `json:"section_type" binding:"required"`
	SectionOrder int    `json:"section_order" binding:"required"`
	Title        string `json:"title" binding:"required"`
	Content      string `json:"content" binding:"required"`
	Metadata     string `json:"metadata"`
	IsActive     bool   `json:"is_active"`
}

// GetRegulationStructuredContent returns structured content for a regulation
func GetRegulationStructuredContent(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get structured content for this regulation
	var structuredContent []models.RegulationStructuredContent
	if err := db.Where("regulation_id = ?", regulationID).
		Order("section_order ASC").
		Find(&structuredContent).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation structured content: "+err.Error())
		return
	}

	// Convert to response format
	contentResponses := make([]gin.H, len(structuredContent))
	for i, content := range structuredContent {
		contentResponses[i] = gin.H{
			"id":             content.ID,
			"regulation_id":  content.RegulationID,
			"section_number": content.SectionNumber,
			"content_type":   content.ContentType,
			"sort_order":     content.SortOrder,
			"level":          content.Level,
			"title":          content.Title,
			"content":        content.Content,
			"is_active":      content.IsActive,
			"metadata":       content.Metadata,
			"created_at":     content.CreatedAt,
			"updated_at":     content.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation structured content retrieved successfully",
		Data:    contentResponses,
	})
}

// GetRegulationStructuredContentSection returns a single structured content section by ID
func GetRegulationStructuredContentSection(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get structured content section
	var content models.RegulationStructuredContent
	if err := db.Preload("Regulation").First(&content, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation structured content section")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation structured content section: "+err.Error())
		return
	}

	response := gin.H{
		"id":             content.ID,
		"regulation":     content.Regulation,
		"section_number": content.SectionNumber,
		"content_type":   content.ContentType,
		"sort_order":     content.SortOrder,
		"level":          content.Level,
		"title":          content.Title,
		"content":        content.Content,
		"is_active":      content.IsActive,
		"metadata":       content.Metadata,
		"created_at":     content.CreatedAt,
		"updated_at":     content.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation structured content section retrieved successfully",
		Data:    response,
	})
}

// CreateRegulationStructuredContent creates a new structured content section
func CreateRegulationStructuredContent(c *gin.Context) {
	var req RegulationStructuredContentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid regulation",
				Message: "Regulation not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Create structured content section
	content := &models.RegulationStructuredContent{
		RegulationID:  req.RegulationID,
		SectionNumber: "1.0", // Default section number
		ContentType:   req.SectionType,
		SortOrder:     req.SectionOrder,
		Level:         1, // Default level
		Title:         req.Title,
		Content:       req.Content,
		IsActive:      true,
		Metadata:      req.Metadata,
	}

	if err := db.Create(content).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation structured content: "+err.Error())
		return
	}

	response := gin.H{
		"id":             content.ID,
		"regulation_id":  content.RegulationID,
		"section_number": content.SectionNumber,
		"content_type":   content.ContentType,
		"sort_order":     content.SortOrder,
		"level":          content.Level,
		"title":          content.Title,
		"content":        content.Content,
		"is_active":      content.IsActive,
		"metadata":       content.Metadata,
		"created_at":     content.CreatedAt,
		"updated_at":     content.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation structured content created successfully",
		Data:    response,
	})
}

// UpdateRegulationStructuredContent updates an existing structured content section
func UpdateRegulationStructuredContent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RegulationStructuredContentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing structured content section
	var content models.RegulationStructuredContent
	if err := db.First(&content, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation structured content section")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation structured content section: "+err.Error())
		return
	}

	// Update structured content section fields
	content.ContentType = req.SectionType
	content.SortOrder = req.SectionOrder
	content.Title = req.Title
	content.Content = req.Content
	content.Metadata = req.Metadata
	content.IsActive = req.IsActive

	if err := db.Save(&content).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation structured content: "+err.Error())
		return
	}

	response := gin.H{
		"id":             content.ID,
		"regulation_id":  content.RegulationID,
		"section_number": content.SectionNumber,
		"content_type":   content.ContentType,
		"sort_order":     content.SortOrder,
		"level":          content.Level,
		"title":          content.Title,
		"content":        content.Content,
		"is_active":      content.IsActive,
		"metadata":       content.Metadata,
		"created_at":     content.CreatedAt,
		"updated_at":     content.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation structured content updated successfully",
		Data:    response,
	})
}

// DeleteRegulationStructuredContent deletes a structured content section
func DeleteRegulationStructuredContent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if structured content section exists
	var content models.RegulationStructuredContent
	if err := db.First(&content, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation structured content section")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation structured content section: "+err.Error())
		return
	}

	// Delete structured content section
	if err := db.Delete(&content).Error; err != nil {
		HandleInternalError(c, "Failed to delete regulation structured content: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation structured content deleted successfully",
	})
}
