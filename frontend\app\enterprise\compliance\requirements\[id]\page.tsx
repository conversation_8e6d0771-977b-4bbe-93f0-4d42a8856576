'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { complianceApi } from '../../../../services/enterpriseApi';
import { ComplianceRequirement } from '../../../../types/enterprise';

const ComplianceRequirementViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const requirementId = parseInt(params.id as string);
  
  const [requirement, setRequirement] = useState<ComplianceRequirement | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (requirementId) {
      fetchRequirement();
    }
  }, [requirementId]);

  const fetchRequirement = async () => {
    try {
      setLoading(true);
      const response = await complianceApi.getRequirement(requirementId);
      setRequirement(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch compliance requirement');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this compliance requirement?')) return;
    
    try {
      await complianceApi.deleteRequirement(requirementId);
      router.push('/enterprise/compliance/requirements');
    } catch (err: any) {
      setError(err.message || 'Failed to delete compliance requirement');
    }
  };

  if (loading) return <div className="p-6">Loading compliance requirement...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!requirement) return <div className="p-6">Compliance requirement not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Compliance Requirement Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/compliance/requirements')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Requirements
          </button>
          <button
            onClick={() => router.push(`/enterprise/compliance/requirements/${requirementId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Requirement
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Requirement
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{requirement.requirement_name}</h2>
              <p className="text-sm text-gray-600">Code: {requirement.requirement_code}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                requirement.status === 'active' 
                  ? 'bg-green-100 text-green-800'
                  : requirement.status === 'pending'
                  ? 'bg-yellow-100 text-yellow-800'
                  : requirement.status === 'expired'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {requirement.status}
              </span>
            </div>
          </div>
        </div>

        {/* Risk Assessment */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Risk Assessment</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className={`p-4 rounded-lg ${
              requirement.priority === 'critical' ? 'bg-red-50' :
              requirement.priority === 'high' ? 'bg-orange-50' :
              requirement.priority === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              <div className={`text-2xl font-bold ${
                requirement.priority === 'critical' ? 'text-red-600' :
                requirement.priority === 'high' ? 'text-orange-600' :
                requirement.priority === 'medium' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {requirement.priority.toUpperCase()}
              </div>
              <div className="text-sm text-gray-600">Priority</div>
            </div>
            
            <div className={`p-4 rounded-lg ${
              requirement.risk_level === 'critical' ? 'bg-red-50' :
              requirement.risk_level === 'high' ? 'bg-orange-50' :
              requirement.risk_level === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              <div className={`text-2xl font-bold ${
                requirement.risk_level === 'critical' ? 'text-red-600' :
                requirement.risk_level === 'high' ? 'text-orange-600' :
                requirement.risk_level === 'medium' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {requirement.risk_level.toUpperCase()}
              </div>
              <div className="text-sm text-gray-600">Risk Level</div>
            </div>

            <div className={`p-4 rounded-lg ${
              requirement.business_impact === 'critical' ? 'bg-red-50' :
              requirement.business_impact === 'high' ? 'bg-orange-50' :
              requirement.business_impact === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              <div className={`text-2xl font-bold ${
                requirement.business_impact === 'critical' ? 'text-red-600' :
                requirement.business_impact === 'high' ? 'text-orange-600' :
                requirement.business_impact === 'medium' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {requirement.business_impact.toUpperCase()}
              </div>
              <div className="text-sm text-gray-600">Business Impact</div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {requirement.currency_code} {requirement.penalty_amount.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Penalty Amount</div>
            </div>
          </div>
        </div>

        {/* Requirement Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Requirement Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Framework</label>
              <p className="mt-1 text-sm text-gray-900">{requirement.framework}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Regulatory Framework</label>
              <p className="mt-1 text-sm text-gray-900">{requirement.regulatory_framework || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Authority</label>
              <p className="mt-1 text-sm text-gray-900">{requirement.authority || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Category</label>
              <p className="mt-1 text-sm text-gray-900">{requirement.category || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Subcategory</label>
              <p className="mt-1 text-sm text-gray-900">{requirement.subcategory || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Responsible Party</label>
              <p className="mt-1 text-sm text-gray-900">{requirement.responsible_party || 'N/A'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Testing Frequency</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{requirement.testing_frequency}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Review Frequency</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{requirement.review_frequency}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Implementation Cost</label>
              <p className="mt-1 text-sm text-gray-900">
                {requirement.currency_code} {requirement.implementation_cost.toLocaleString()}
              </p>
            </div>
          </div>

          {requirement.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {requirement.description}
              </div>
            </div>
          )}
        </div>

        {/* Compliance Details */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Compliance Details</h3>
          
          {requirement.compliance_criteria && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Compliance Criteria</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {requirement.compliance_criteria}
              </div>
            </div>
          )}

          {requirement.evidence_requirements && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Evidence Requirements</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {requirement.evidence_requirements}
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Approval Required</label>
              <p className="mt-1 text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  requirement.approval_required 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {requirement.approval_required ? 'Yes' : 'No'}
                </span>
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Automated Monitoring</label>
              <p className="mt-1 text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  requirement.automated_monitoring 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {requirement.automated_monitoring ? 'Enabled' : 'Disabled'}
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Important Dates */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Important Dates</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {requirement.effective_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Effective Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(requirement.effective_date).toLocaleDateString()}
                </p>
              </div>
            )}
            
            {requirement.due_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Due Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(requirement.due_date).toLocaleDateString()}
                </p>
              </div>
            )}
            
            {requirement.next_review_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Next Review Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(requirement.next_review_date).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(requirement.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(requirement.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceRequirementViewPage;
