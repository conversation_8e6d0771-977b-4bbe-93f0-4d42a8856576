'use client'

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PencilIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import ProceedingForm from '../../../components/Proceeding/ProceedingForm';
import apiService from '../../../services/api';

interface Proceeding {
  id: number;
  name: string;
  description?: string;
  objective: string;
  status: string;
  priority: string;
  unique_id: string;
  initiation_date: string;
  planned_start_date?: string;
  planned_end_date?: string;
  progress_percent: number;
  total_steps: number;
  completed_steps: number;
  current_step_order?: number;
  owner: {
    id: number;
    username: string;
    email: string;
  };
  agency?: {
    id: number;
    name: string;
  };
  category?: {
    id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

const EditProceedingPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuthStore();
  const [proceeding, setProceeding] = useState<Proceeding | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');

  const proceedingId = parseInt(params.id as string);

  useEffect(() => {
    fetchProceeding();
  }, [proceedingId]);

  const fetchProceeding = async () => {
    try {
      setLoading(true);
      const response = await apiService.getProceeding(proceedingId);
      setProceeding(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch proceeding');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (formData: any) => {
    try {
      setSubmitting(true);
      setError('');
      
      await apiService.updateProceeding(proceedingId, formData);
      router.push(`/proceedings/${proceedingId}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update proceeding');
    } finally {
      setSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push(`/proceedings/${proceedingId}`);
  };

  const canEdit = () => {
    if (!user || !proceeding) return false;
    return user.role === 'admin' || 
           (user.role === 'editor' && proceeding.owner?.id === user.id);
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="h-6 bg-gray-200 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-4 w-2/3"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !proceeding) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <ExclamationCircleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
            <p className="text-gray-600 mb-4">{error || 'Proceeding not found'}</p>
            <Link
              href="/proceedings"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Proceedings
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  if (!canEdit()) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <PencilIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-600 mb-4">
              You don't have permission to edit this proceeding.
            </p>
            <Link
              href={`/proceedings/${proceedingId}`}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Proceeding
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Link
            href={`/proceedings/${proceedingId}`}
            className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Proceeding</h1>
            <p className="text-gray-600 mt-1">{proceeding.name}</p>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="p-6">
            <ProceedingForm
              initialData={proceeding}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              isLoading={submitting}
              isEdit={true}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default EditProceedingPage;
