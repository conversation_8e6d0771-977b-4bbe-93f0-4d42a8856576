-- Create missing proceeding junction tables
CREATE TABLE IF NOT EXISTS proceeding_documents (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related',
    notes TEXT,
    PRIMARY KEY (proceeding_id, document_id)
);

CREATE TABLE IF NOT EXISTS proceeding_tasks (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    task_id INTEGER NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related',
    notes TEXT,
    PRIMARY KEY (proceeding_id, task_id)
);

CREATE TABLE IF NOT EXISTS proceeding_regulations (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    regulation_id INTEGER NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related',
    notes TEXT,
    PRIMARY KEY (proceeding_id, regulation_id)
);
