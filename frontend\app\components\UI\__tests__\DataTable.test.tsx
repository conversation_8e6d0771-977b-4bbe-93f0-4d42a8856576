import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import DataTable, { Column, ActionButton } from '../DataTable';
import { EyeIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

// Mock data for testing
interface TestItem {
  id: number;
  name: string;
  email: string;
  status: string;
  created_at: string;
}

const mockData: TestItem[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'active',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    status: 'inactive',
    created_at: '2024-01-02T00:00:00Z'
  }
];

const mockColumns: Column<TestItem>[] = [
  {
    key: 'name',
    label: 'Name',
    sortable: true,
    render: (value) => <span>{value}</span>
  },
  {
    key: 'email',
    label: 'Email',
    sortable: true,
    render: (value) => <a href={`mailto:${value}`}>{value}</a>
  },
  {
    key: 'status',
    label: 'Status',
    sortable: true,
    render: (value) => (
      <span className={`badge ${value === 'active' ? 'badge-success' : 'badge-danger'}`}>
        {value}
      </span>
    )
  },
  {
    key: 'created_at',
    label: 'Created',
    sortable: true,
    render: (value) => new Date(value).toLocaleDateString()
  }
];

const mockActions: ActionButton<TestItem>[] = [
  {
    label: 'View',
    icon: EyeIcon,
    onClick: jest.fn(),
    className: 'text-blue-600'
  },
  {
    label: 'Edit',
    icon: PencilIcon,
    onClick: jest.fn(),
    className: 'text-green-600',
    show: () => true
  },
  {
    label: 'Delete',
    icon: TrashIcon,
    onClick: jest.fn(),
    className: 'text-red-600',
    show: () => true
  }
];

const mockPagination = {
  page: 1,
  per_page: 10,
  total: 2,
  total_pages: 1
};

describe('DataTable Component', () => {
  const defaultProps = {
    data: mockData,
    columns: mockColumns,
    actions: mockActions,
    loading: false,
    error: null,
    pagination: mockPagination,
    onPageChange: jest.fn(),
    onSort: jest.fn(),
    sortColumn: 'name',
    sortDirection: 'asc' as const,
    emptyMessage: 'No data found'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders table with data correctly', () => {
    render(<DataTable {...defaultProps} />);

    // Check if table headers are rendered
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Created')).toBeInTheDocument();

    // Check if data rows are rendered
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('active')).toBeInTheDocument();
    expect(screen.getByText('inactive')).toBeInTheDocument();
  });

  it('shows loading state correctly', () => {
    render(<DataTable {...defaultProps} loading={true} />);

    // Check for loading skeleton
    expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
  });

  it('shows error state correctly', () => {
    const errorMessage = 'Failed to load data';
    render(<DataTable {...defaultProps} error={errorMessage} />);

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('shows empty state when no data', () => {
    render(<DataTable {...defaultProps} data={[]} />);

    expect(screen.getByText('No data found')).toBeInTheDocument();
  });

  it('handles sorting correctly', () => {
    render(<DataTable {...defaultProps} />);

    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);

    expect(defaultProps.onSort).toHaveBeenCalledWith('name', 'desc');
  });

  it('handles pagination correctly', () => {
    const paginationProps = {
      ...defaultProps,
      pagination: {
        page: 1,
        per_page: 1,
        total: 2,
        total_pages: 2
      }
    };

    render(<DataTable {...paginationProps} />);

    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    expect(defaultProps.onPageChange).toHaveBeenCalledWith(2);
  });

  it('renders action buttons correctly', () => {
    render(<DataTable {...defaultProps} />);

    // Check if action buttons are rendered for each row
    const viewButtons = screen.getAllByLabelText('View');
    const editButtons = screen.getAllByLabelText('Edit');
    const deleteButtons = screen.getAllByLabelText('Delete');

    expect(viewButtons).toHaveLength(2);
    expect(editButtons).toHaveLength(2);
    expect(deleteButtons).toHaveLength(2);
  });

  it('calls action handlers when buttons are clicked', () => {
    render(<DataTable {...defaultProps} />);

    const viewButton = screen.getAllByLabelText('View')[0];
    fireEvent.click(viewButton);

    expect(mockActions[0].onClick).toHaveBeenCalledWith(mockData[0]);
  });

  it('conditionally shows actions based on show function', () => {
    const conditionalActions: ActionButton<TestItem>[] = [
      {
        label: 'Admin Only',
        icon: PencilIcon,
        onClick: jest.fn(),
        show: (item) => item.status === 'active'
      }
    ];

    render(<DataTable {...defaultProps} actions={conditionalActions} />);

    // Should show action for active user but not inactive user
    const adminButtons = screen.getAllByLabelText('Admin Only');
    expect(adminButtons).toHaveLength(1);
  });

  it('handles custom cell rendering', () => {
    render(<DataTable {...defaultProps} />);

    // Check if custom email rendering (as link) works
    const emailLink = screen.getByText('<EMAIL>');
    expect(emailLink.tagName).toBe('A');
    expect(emailLink).toHaveAttribute('href', 'mailto:<EMAIL>');
  });

  it('displays pagination info correctly', () => {
    render(<DataTable {...defaultProps} />);

    expect(screen.getByText(/Showing 1 to 2 of 2 results/)).toBeInTheDocument();
  });

  it('disables pagination buttons appropriately', () => {
    render(<DataTable {...defaultProps} />);

    const prevButton = screen.getByText('Previous');
    const nextButton = screen.getByText('Next');

    expect(prevButton).toBeDisabled();
    expect(nextButton).toBeDisabled();
  });

  it('handles href actions correctly', () => {
    const hrefActions: ActionButton<TestItem>[] = [
      {
        label: 'View Details',
        icon: EyeIcon,
        href: (item) => `/users/${item.id}`,
        className: 'text-blue-600'
      }
    ];

    render(<DataTable {...defaultProps} actions={hrefActions} />);

    const viewLink = screen.getAllByLabelText('View Details')[0];
    expect(viewLink.tagName).toBe('A');
    expect(viewLink).toHaveAttribute('href', '/users/1');
  });

  it('applies correct CSS classes for sorting indicators', () => {
    render(<DataTable {...defaultProps} />);

    const nameHeader = screen.getByText('Name').closest('th');
    expect(nameHeader).toHaveClass('cursor-pointer');
  });

  it('handles empty actions array', () => {
    render(<DataTable {...defaultProps} actions={[]} />);

    // Should not render actions column
    expect(screen.queryByText('Actions')).not.toBeInTheDocument();
  });

  it('handles missing pagination gracefully', () => {
    const { pagination, ...propsWithoutPagination } = defaultProps;
    render(<DataTable {...propsWithoutPagination} pagination={undefined} />);

    // Should not show pagination controls
    expect(screen.queryByText('Previous')).not.toBeInTheDocument();
    expect(screen.queryByText('Next')).not.toBeInTheDocument();
  });
});
