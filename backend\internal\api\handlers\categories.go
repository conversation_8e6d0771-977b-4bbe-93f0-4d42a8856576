package handlers

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// Category handlers - placeholder implementations

var categorySummaryService *services.SummaryService

// GetPublicCategories returns public categories
// @Summary Get public categories
// @Description Get a list of public document categories
// @Tags categories
// @Produce json
// @Success 200 {array} gin.H
// @Router /public/categories [get]
func GetPublicCategories(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get all active categories with document counts
	var categories []models.Category
	if err := db.Where("is_active = ?", true).Order("name ASC").Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch categories",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format with document counts
	categoryResponses := make([]gin.H, len(categories))
	for i, category := range categories {
		// Count documents in this category
		var documentCount int64
		db.Table("document_category_assignments").
			Joins("JOIN documents ON document_category_assignments.document_id = documents.id").
			Where("document_category_assignments.category_id = ? AND documents.is_public = ? AND documents.visibility_level = ?",
				category.ID, true, 1).
			Count(&documentCount)

		categoryResponse := gin.H{
			"id":             category.ID,
			"name":           category.Name,
			"slug":           category.Slug,
			"description":    category.Description,
			"color":          category.Color,
			"icon":           category.Icon,
			"document_count": documentCount,
			"created_at":     category.CreatedAt,
		}

		categoryResponses[i] = categoryResponse
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Categories retrieved successfully",
		Data:    categoryResponses,
	})
}

// GetPublicCategory returns a specific public category
// @Summary Get public category
// @Description Get a specific public category by ID
// @Tags categories
// @Produce json
// @Param id path int true "Category ID"
// @Success 200 {object} gin.H
// @Router /public/categories/{id} [get]
func GetPublicCategory(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find the category
	var category models.Category
	if err := db.Where("id = ? AND is_active = ?", id, true).First(&category).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Category")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch category",
			Message: err.Error(),
		})
		return
	}

	// Count documents in this category
	var documentCount int64
	db.Table("document_category_assignments").
		Joins("JOIN documents ON document_category_assignments.document_id = documents.id").
		Where("document_category_assignments.category_id = ? AND documents.is_public = ? AND documents.visibility_level = ?",
			category.ID, true, 1).
		Count(&documentCount)

	categoryResponse := gin.H{
		"id":             category.ID,
		"name":           category.Name,
		"slug":           category.Slug,
		"description":    category.Description,
		"color":          category.Color,
		"icon":           category.Icon,
		"document_count": documentCount,
		"created_at":     category.CreatedAt,
		"updated_at":     category.UpdatedAt,
		"is_active":      category.IsActive,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category retrieved successfully",
		Data:    categoryResponse,
	})
}

// GetCategoryDocuments returns documents for a specific category
// @Summary Get category documents
// @Description Get documents in a specific category
// @Tags categories
// @Produce json
// @Param id path int true "Category ID"
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(25)
// @Success 200 {object} PaginationResponse
// @Router /public/categories/{id}/documents [get]
func GetCategoryDocuments(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	pagination := GetPaginationParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Check if category exists and is active
	var category models.Category
	if err := db.Where("id = ? AND is_active = ?", id, true).First(&category).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Category")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch category",
			Message: err.Error(),
		})
		return
	}

	// Build query for documents in this category (public only)
	query := db.Model(&models.Document{}).
		Joins("JOIN document_category_assignments ON documents.id = document_category_assignments.document_id").
		Where("document_category_assignments.category_id = ? AND documents.is_public = ? AND documents.visibility_level = ?",
			id, true, 1).
		Preload("Agency").
		Preload("Categories")

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to count documents",
			Message: err.Error(),
		})
		return
	}

	// Apply pagination
	offset := (pagination.Page - 1) * pagination.PerPage
	query = query.Offset(offset).Limit(pagination.PerPage)

	// Apply sorting
	sortBy := c.DefaultQuery("sort_by", "publication_date")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc"
	}
	query = query.Order(sortBy + " " + sortOrder)

	// Execute query
	var documents []models.Document
	if err := query.Find(&documents).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch documents",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	documentResponses := make([]gin.H, len(documents))
	for i, doc := range documents {
		docResponse := gin.H{
			"id":                 doc.ID,
			"title":              doc.Title,
			"abstract":           doc.Abstract,
			"type":               doc.Type,
			"status":             doc.Status,
			"fr_document_number": doc.FRDocumentNumber,
			"docket_number":      doc.DocketNumber,
			"publication_date":   doc.PublicationDate,
			"effective_date":     doc.EffectiveDate,
			"comment_due_date":   doc.CommentDueDate,
			"accepts_comments":   doc.AcceptsComments,
			"view_count":         doc.ViewCount,
			"created_at":         doc.CreatedAt,
			"category_id":        id,
		}

		if doc.Agency.ID != 0 {
			docResponse["agency"] = gin.H{
				"id":         doc.Agency.ID,
				"name":       doc.Agency.Name,
				"short_name": doc.Agency.ShortName,
			}
		}

		documentResponses[i] = docResponse
	}

	response := CreatePaginationResponse(documentResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// Protected category handlers

// GetCategories returns categories for authenticated users
func GetCategories(c *gin.Context) {
	// Get current user for permission check
	currentUser, exists := c.Get("user")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	user, ok := currentUser.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid user data",
			Message: "User data type assertion failed",
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Build query based on user permissions
	query := db.Model(&models.Category{})

	// Admin can see all categories, others can only see active ones
	if user.Role != models.RoleAdmin {
		query = query.Where("is_active = ?", true)
	}

	// Apply filters if provided
	if search := c.Query("search"); search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	if activeStr := c.Query("is_active"); activeStr != "" && user.Role == models.RoleAdmin {
		if active, err := strconv.ParseBool(activeStr); err == nil {
			query = query.Where("is_active = ?", active)
		}
	}

	// Get categories
	var categories []models.Category
	if err := query.Order("name ASC").Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch categories",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	categoryResponses := make([]gin.H, len(categories))
	for i, category := range categories {
		// Count documents in this category
		var documentCount int64
		countQuery := db.Table("document_category_assignments").
			Joins("JOIN documents ON document_category_assignments.document_id = documents.id").
			Where("document_category_assignments.category_id = ?", category.ID)

		// Apply visibility filters based on user role
		if user.Role == models.RoleAdmin {
			// Admin can see all documents
		} else if user.Role == models.RoleEditor {
			// Editor can see public documents and their own drafts
			countQuery = countQuery.Where("(documents.is_public = ? AND documents.visibility_level <= ?) OR documents.created_by_id = ?", true, 2, user.ID)
		} else {
			// Viewer can only see public documents
			countQuery = countQuery.Where("documents.is_public = ? AND documents.visibility_level = ?", true, 1)
		}

		countQuery.Count(&documentCount)

		categoryResponse := gin.H{
			"id":                 category.ID,
			"name":               category.Name,
			"slug":               category.Slug,
			"description":        category.Description,
			"color":              category.Color,
			"icon":               category.Icon,
			"sort_order":         category.SortOrder,
			"parent_category_id": category.ParentCategoryID,
			"is_active":          category.IsActive,
			"document_count":     documentCount,
			"created_at":         category.CreatedAt,
			"updated_at":         category.UpdatedAt,
		}

		categoryResponses[i] = categoryResponse
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Categories retrieved successfully",
		Data:    categoryResponses,
	})
}

// CreateCategoryRequest represents a category creation request
type CreateCategoryRequest struct {
	Name             string `json:"name" binding:"required"`
	Slug             string `json:"slug" binding:"required"`
	Description      string `json:"description"`
	Color            string `json:"color"`
	Icon             string `json:"icon"`
	SortOrder        int    `json:"sort_order"`
	ParentCategoryID *uint  `json:"parent_category_id"`
	IsActive         bool   `json:"is_active"`
}

// CreateCategory creates a new category
func CreateCategory(c *gin.Context) {
	var req CreateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request data",
			Message: err.Error(),
		})
		return
	}

	// Use parent category ID directly since it's now properly typed
	parentCategoryID := req.ParentCategoryID

	// Get current user for permission check
	currentUser, exists := c.Get("user")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	user, ok := currentUser.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid user data",
			Message: "User data type assertion failed",
		})
		return
	}

	// Only admin and editors can create categories
	if user.Role != models.RoleAdmin && user.Role != models.RoleEditor {
		HandleForbidden(c, "Insufficient permissions")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Check if slug already exists
	var existingCategory models.Category
	if err := db.Where("slug = ?", req.Slug).First(&existingCategory).Error; err == nil {
		HandleBadRequest(c, "Category with this slug already exists")
		return
	}

	// Validate parent category if provided
	if parentCategoryID != nil {
		var parentCategory models.Category
		if err := db.First(&parentCategory, *parentCategoryID).Error; err != nil {
			HandleBadRequest(c, "Invalid parent category ID")
			return
		}
	}

	// Create category
	category := &models.Category{
		Name:             req.Name,
		Slug:             req.Slug,
		Description:      req.Description,
		Color:            req.Color,
		Icon:             req.Icon,
		SortOrder:        req.SortOrder,
		ParentCategoryID: parentCategoryID,
		IsActive:         req.IsActive,
	}

	if err := db.Create(category).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Category creation failed",
			Message: err.Error(),
		})
		return
	}

	// Create summary for category creation
	userID, exists := c.Get("userID")
	if exists {
		if categorySummaryService == nil {
			categorySummaryService = services.NewSummaryService(database.GetDB())
		}
		if err := categorySummaryService.CreateCategorySummary(category, models.ActionTypeCreate, userID.(uint)); err != nil {
			// Log the error but don't fail the category creation
			fmt.Printf("Warning: Failed to create summary for category %d: %v\n", category.ID, err)
		}
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Category created successfully",
		Data:    gin.H{"id": category.ID, "name": req.Name, "slug": req.Slug, "parent_category_id": parentCategoryID},
	})
}

// GetCategory returns a specific category
func GetCategory(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get category with ALL related data for complete preloading
	var category models.Category
	if err := db.Preload("ParentCategory").
		Preload("SubCategories").
		Preload("Documents", func(docDB *gorm.DB) *gorm.DB {
			return docDB.Preload("Agency").Preload("CreatedBy").Order("created_at DESC").Limit(10)
		}).
		Preload("RegulationRelationships", func(regDB *gorm.DB) *gorm.DB {
			return regDB.Preload("Regulation").Preload("RegulationChunk").Preload("CreatedBy")
		}).
		First(&category, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Category")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch category",
			Message: err.Error(),
		})
		return
	}

	// Count documents in this category
	var documentCount int64
	db.Table("document_category_assignments").
		Joins("JOIN documents ON document_category_assignments.document_id = documents.id").
		Where("document_category_assignments.category_id = ? AND documents.is_public = ? AND documents.visibility_level = ?",
			category.ID, true, 1).
		Count(&documentCount)

	// Convert to response format
	response := gin.H{
		"id":             category.ID,
		"name":           category.Name,
		"slug":           category.Slug,
		"description":    category.Description,
		"color":          category.Color,
		"icon":           category.Icon,
		"sort_order":     category.SortOrder,
		"is_active":      category.IsActive,
		"document_count": documentCount,
		"created_at":     category.CreatedAt.Format("2006-01-02T15:04:05Z"),
	}

	c.JSON(http.StatusOK, response)
}

// UpdateCategoryRequest represents a category update request
type UpdateCategoryRequest struct {
	Name             string `json:"name"`
	Slug             string `json:"slug"`
	Description      string `json:"description"`
	Color            string `json:"color"`
	Icon             string `json:"icon"`
	SortOrder        int    `json:"sort_order"`
	ParentCategoryID *uint  `json:"parent_category_id"`
	IsActive         bool   `json:"is_active"`
}

// UpdateCategory updates a category
func UpdateCategory(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req UpdateCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request data",
			Message: err.Error(),
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get existing category
	var category models.Category
	if err := db.First(&category, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Category")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch category",
			Message: err.Error(),
		})
		return
	}

	// Check for conflicts if name or slug is being changed
	if req.Name != "" && req.Name != category.Name {
		var existingCategory models.Category
		if err := db.Where("name = ? AND id != ?", req.Name, id).First(&existingCategory).Error; err == nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Category name already exists",
				Message: "A category with this name already exists",
			})
			return
		}
	}

	if req.Slug != "" && req.Slug != category.Slug {
		var existingCategory models.Category
		if err := db.Where("slug = ? AND id != ?", req.Slug, id).First(&existingCategory).Error; err == nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Category slug already exists",
				Message: "A category with this slug already exists",
			})
			return
		}
	}

	// Validate parent category if provided
	if req.ParentCategoryID != nil && *req.ParentCategoryID != 0 {
		var parentCategory models.Category
		if err := db.First(&parentCategory, *req.ParentCategoryID).Error; err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid parent category",
				Message: "The specified parent category does not exist",
			})
			return
		}
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Slug != "" {
		updates["slug"] = req.Slug
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Color != "" {
		updates["color"] = req.Color
	}
	if req.Icon != "" {
		updates["icon"] = req.Icon
	}
	if req.SortOrder != 0 {
		updates["sort_order"] = req.SortOrder
	}
	if req.ParentCategoryID != nil {
		if *req.ParentCategoryID == 0 {
			updates["parent_category_id"] = nil
		} else {
			updates["parent_category_id"] = *req.ParentCategoryID
		}
	}
	// IsActive is always updated since it's a bool field
	updates["is_active"] = req.IsActive

	if len(updates) > 0 {
		if err := db.Model(&category).Updates(updates).Error; err != nil {
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "Failed to update category",
				Message: err.Error(),
			})
			return
		}

		// Create summary for category update
		userID, exists := c.Get("userID")
		if exists {
			if categorySummaryService == nil {
				categorySummaryService = services.NewSummaryService(database.GetDB())
			}
			if err := categorySummaryService.CreateCategorySummary(&category, models.ActionTypeUpdate, userID.(uint)); err != nil {
				// Log the error but don't fail the category update
				fmt.Printf("Warning: Failed to create summary for category update %d: %v\n", id, err)
			}
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category updated successfully",
		Data:    gin.H{"id": id},
	})
}

// DeleteCategory deletes a category
func DeleteCategory(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Check if category exists and get it for summary generation
	var category models.Category
	if err := db.First(&category, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Category")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch category",
			Message: err.Error(),
		})
		return
	}

	// Create summary for category deletion before deleting
	userID, exists := c.Get("userID")
	if exists {
		if categorySummaryService == nil {
			categorySummaryService = services.NewSummaryService(database.GetDB())
		}
		if err := categorySummaryService.CreateCategorySummary(&category, models.ActionTypeDelete, userID.(uint)); err != nil {
			// Log the error but don't fail the category deletion
			fmt.Printf("Warning: Failed to create summary for category deletion %d: %v\n", id, err)
		}
	}

	// Check if category has subcategories
	var subcategoryCount int64
	if err := db.Model(&models.Category{}).Where("parent_category_id = ?", id).Count(&subcategoryCount).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to check subcategories",
			Message: err.Error(),
		})
		return
	}

	if subcategoryCount > 0 {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Category has subcategories",
			Message: "Cannot delete category that has subcategories. Please delete or reassign subcategories first.",
		})
		return
	}

	// Start transaction
	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Remove category assignments from documents
	if err := tx.Where("category_id = ?", id).Delete(&models.DocumentCategoryAssignment{}).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to remove category assignments",
			Message: err.Error(),
		})
		return
	}

	// Delete the category
	if err := tx.Delete(&category).Error; err != nil {
		tx.Rollback()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to delete category",
			Message: err.Error(),
		})
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to commit transaction",
			Message: err.Error(),
		})
		return
	}

	// Delete related summaries for this category
	if categorySummaryService != nil {
		if err := categorySummaryService.DeleteSummariesForEntity(models.EntityTypeCategory, uint(id)); err != nil {
			// Log the error but don't fail the category deletion
			fmt.Printf("Warning: Failed to delete summaries for category %d: %v\n", id, err)
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category deleted successfully",
	})
}

// GetCategoryDefaults returns default values for category creation
func GetCategoryDefaults(c *gin.Context) {
	defaults := gin.H{
		"is_active": true,
		"color":     "#007bff",
		"icon":      "folder",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category defaults retrieved successfully",
		Data:    defaults,
	})
}
