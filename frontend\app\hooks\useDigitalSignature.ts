import { useState, useEffect, useCallback } from 'react';
import { digitalSignatureService } from '../services/digitalSignature';
import { DigitalCertificate, DigitalSignature, CommentFormData } from '../types';

interface UseDigitalSignatureReturn {
  // Certificate management
  certificates: DigitalCertificate[];
  activeCertificates: DigitalCertificate[];
  defaultCertificate: DigitalCertificate | null;
  selectedCertificate: DigitalCertificate | null;
  
  // Loading states
  loading: boolean;
  certificatesLoading: boolean;
  signingLoading: boolean;
  verifyingLoading: boolean;
  
  // Error states
  error: string | null;
  certificateError: string | null;
  signatureError: string | null;
  
  // Actions
  loadCertificates: () => Promise<void>;
  selectCertificate: (certificate: DigitalCertificate | null) => void;
  createSignedComment: (documentId: number, commentData: CommentFormData) => Promise<{ document_id: number; comment_id: number; signature_id: string; verified: boolean } | null>;
  verifySignature: (commentId: number) => Promise<DigitalSignature | null>;
  generateSignature: (content: string) => Promise<string | null>;
  
  // Utility functions
  validateCertificate: (certificate: DigitalCertificate) => Promise<boolean>;
  formatCertificateDisplayName: (certificate: DigitalCertificate) => string;
  getCertificateStatusDisplay: (certificate: DigitalCertificate) => { status: string; color: string; icon: string };
  isCertificateExpiringSoon: (certificate: DigitalCertificate) => boolean;
}

export const useDigitalSignature = (): UseDigitalSignatureReturn => {
  // State
  const [certificates, setCertificates] = useState<DigitalCertificate[]>([]);
  const [activeCertificates, setActiveCertificates] = useState<DigitalCertificate[]>([]);
  const [defaultCertificate, setDefaultCertificate] = useState<DigitalCertificate | null>(null);
  const [selectedCertificate, setSelectedCertificate] = useState<DigitalCertificate | null>(null);
  
  // Loading states
  const [loading, setLoading] = useState(false);
  const [certificatesLoading, setCertificatesLoading] = useState(false);
  const [signingLoading, setSigningLoading] = useState(false);
  const [verifyingLoading, setVerifyingLoading] = useState(false);
  
  // Error states
  const [error, setError] = useState<string | null>(null);
  const [certificateError, setCertificateError] = useState<string | null>(null);
  const [signatureError, setSignatureError] = useState<string | null>(null);

  // Load certificates
  const loadCertificates = useCallback(async () => {
    setCertificatesLoading(true);
    setCertificateError(null);
    
    try {
      const [allCerts, activeCerts, defaultCert] = await Promise.all([
        digitalSignatureService.getUserCertificates(),
        digitalSignatureService.getActiveCertificates(),
        digitalSignatureService.getDefaultCertificate()
      ]);
      
      setCertificates(allCerts);
      setActiveCertificates(activeCerts);
      setDefaultCertificate(defaultCert);
      
      // Auto-select default certificate if none selected
      if (!selectedCertificate && defaultCert) {
        setSelectedCertificate(defaultCert);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load certificates';
      setCertificateError(errorMessage);
      setError(errorMessage);
    } finally {
      setCertificatesLoading(false);
    }
  }, [selectedCertificate]);

  // Select certificate
  const selectCertificate = useCallback((certificate: DigitalCertificate | null) => {
    setSelectedCertificate(certificate);
    setSignatureError(null);
  }, []);

  // Create signed comment
  const createSignedComment = useCallback(async (
    documentId: number, 
    commentData: CommentFormData
  ): Promise<{ document_id: number; comment_id: number; signature_id: string; verified: boolean } | null> => {
    if (!selectedCertificate) {
      setSignatureError('No certificate selected for signing');
      return null;
    }

    setSigningLoading(true);
    setSignatureError(null);

    try {
      // Generate signature for the comment content
      const signatureData = await digitalSignatureService.generateSignature(
        commentData.content, 
        selectedCertificate.id
      );

      // Create signed comment
      const result = await digitalSignatureService.createSignedComment(
        documentId,
        commentData,
        selectedCertificate.id,
        signatureData
      );

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create signed comment';
      setSignatureError(errorMessage);
      setError(errorMessage);
      return null;
    } finally {
      setSigningLoading(false);
    }
  }, [selectedCertificate]);

  // Verify signature
  const verifySignature = useCallback(async (commentId: number): Promise<DigitalSignature | null> => {
    setVerifyingLoading(true);
    setSignatureError(null);

    try {
      const signature = await digitalSignatureService.verifyCommentSignature(commentId);
      return signature;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to verify signature';
      setSignatureError(errorMessage);
      setError(errorMessage);
      return null;
    } finally {
      setVerifyingLoading(false);
    }
  }, []);

  // Generate signature
  const generateSignature = useCallback(async (content: string): Promise<string | null> => {
    if (!selectedCertificate) {
      setSignatureError('No certificate selected for signing');
      return null;
    }

    try {
      const signature = await digitalSignatureService.generateSignature(content, selectedCertificate.id);
      return signature;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate signature';
      setSignatureError(errorMessage);
      return null;
    }
  }, [selectedCertificate]);

  // Utility functions
  const validateCertificate = useCallback(async (certificate: DigitalCertificate): Promise<boolean> => {
    return digitalSignatureService.validateCertificate(certificate);
  }, []);

  const formatCertificateDisplayName = useCallback((certificate: DigitalCertificate): string => {
    return digitalSignatureService.formatCertificateDisplayName(certificate);
  }, []);

  const getCertificateStatusDisplay = useCallback((certificate: DigitalCertificate) => {
    return digitalSignatureService.getCertificateStatusDisplay(certificate);
  }, []);

  const isCertificateExpiringSoon = useCallback((certificate: DigitalCertificate): boolean => {
    return digitalSignatureService.isCertificateExpiringSoon(certificate);
  }, []);

  // Load certificates on mount
  useEffect(() => {
    loadCertificates();
  }, [loadCertificates]);

  // Update loading state
  useEffect(() => {
    setLoading(certificatesLoading || signingLoading || verifyingLoading);
  }, [certificatesLoading, signingLoading, verifyingLoading]);

  return {
    // Certificate management
    certificates,
    activeCertificates,
    defaultCertificate,
    selectedCertificate,
    
    // Loading states
    loading,
    certificatesLoading,
    signingLoading,
    verifyingLoading,
    
    // Error states
    error,
    certificateError,
    signatureError,
    
    // Actions
    loadCertificates,
    selectCertificate,
    createSignedComment,
    verifySignature,
    generateSignature,
    
    // Utility functions
    validateCertificate,
    formatCertificateDisplayName,
    getCertificateStatusDisplay,
    isCertificateExpiringSoon,
  };
};
