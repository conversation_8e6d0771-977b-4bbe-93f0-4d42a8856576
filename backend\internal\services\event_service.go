package services

import (
	"encoding/json"
	"log"
	"sync"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// EventService manages system events and triggers
type EventService struct {
	db        *gorm.DB
	listeners map[string][]EventListener
	mutex     sync.RWMutex
}

// EventListener represents a function that handles events
type EventListener func(eventType string, data map[string]interface{}) error

// SystemEvent represents a system event record
type SystemEvent struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time `json:"created_at"`
	EventType string    `json:"event_type" gorm:"not null"`
	EntityID  *uint     `json:"entity_id"`
	UserID    *uint     `json:"user_id"`
	Data      string    `json:"data" gorm:"type:text"` // JSON data
	Processed bool      `json:"processed" gorm:"default:false"`
	Error     string    `json:"error"`
}

// NewEventService creates a new event service
func NewEventService(db *gorm.DB) *EventService {
	service := &EventService{
		db:        db,
		listeners: make(map[string][]EventListener),
	}

	// Create events table if it doesn't exist
	db.AutoMigrate(&SystemEvent{})

	return service
}

// RegisterListener registers an event listener for a specific event type
func (s *EventService) RegisterListener(eventType string, listener EventListener) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.listeners[eventType] == nil {
		s.listeners[eventType] = make([]EventListener, 0)
	}
	s.listeners[eventType] = append(s.listeners[eventType], listener)
}

// EmitEvent emits an event and triggers all registered listeners
func (s *EventService) EmitEvent(eventType string, data map[string]interface{}) error {
	// Store event in database
	dataJSON, _ := json.Marshal(data)
	event := &SystemEvent{
		EventType: eventType,
		Data:      string(dataJSON),
		Processed: false,
	}

	if entityID, ok := data["entity_id"].(uint); ok {
		event.EntityID = &entityID
	}
	if userID, ok := data["user_id"].(uint); ok {
		event.UserID = &userID
	}

	if err := s.db.Create(event).Error; err != nil {
		log.Printf("Failed to store event: %v", err)
	}

	// Trigger listeners
	s.mutex.RLock()
	listeners := s.listeners[eventType]
	s.mutex.RUnlock()

	for _, listener := range listeners {
		go func(l EventListener) {
			if err := l(eventType, data); err != nil {
				log.Printf("Event listener error for %s: %v", eventType, err)
				// Update event with error
				s.db.Model(event).Update("error", err.Error())
			}
		}(listener)
	}

	// Mark event as processed
	s.db.Model(event).Update("processed", true)

	return nil
}

// GetEvents retrieves system events with optional filtering
func (s *EventService) GetEvents(eventType string, limit int) ([]SystemEvent, error) {
	var events []SystemEvent
	query := s.db.Order("created_at DESC")

	if eventType != "" {
		query = query.Where("event_type = ?", eventType)
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	err := query.Find(&events).Error
	return events, err
}

// SetupDefaultListeners sets up default event listeners for the system
func (s *EventService) SetupDefaultListeners() {
	// Document events
	s.RegisterListener("document.created", s.handleDocumentCreated)
	s.RegisterListener("document.updated", s.handleDocumentUpdated)
	s.RegisterListener("document.published", s.handleDocumentPublished)
	s.RegisterListener("document.deleted", s.handleDocumentDeleted)

	// Regulation events
	s.RegisterListener("regulation.created", s.handleRegulationCreated)
	s.RegisterListener("regulation.updated", s.handleRegulationUpdated)
	s.RegisterListener("regulation.published", s.handleRegulationPublished)
	s.RegisterListener("regulation.deleted", s.handleRegulationDeleted)

	// Task events
	s.RegisterListener("task.created", s.handleTaskCreated)
	s.RegisterListener("task.completed", s.handleTaskCompleted)
	s.RegisterListener("task.overdue", s.handleTaskOverdue)

	// Finance events
	s.RegisterListener("finance.created", s.handleFinanceCreated)
	s.RegisterListener("finance.updated", s.handleFinanceUpdated)

	// Agency events
	s.RegisterListener("agency.created", s.handleAgencyCreated)
	s.RegisterListener("agency.updated", s.handleAgencyUpdated)

	// Category events
	s.RegisterListener("category.created", s.handleCategoryCreated)
	s.RegisterListener("category.updated", s.handleCategoryUpdated)
}

// Event handlers
func (s *EventService) handleDocumentCreated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling document created event: %v", data)

	// Trigger summary generation
	if documentID, ok := data["document_id"].(uint); ok {
		var document models.Document
		if err := s.db.Preload("Agency").Preload("Categories").First(&document, documentID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateDocumentSummary(&document, models.ActionTypeCreate, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleDocumentUpdated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling document updated event: %v", data)

	if documentID, ok := data["document_id"].(uint); ok {
		var document models.Document
		if err := s.db.Preload("Agency").Preload("Categories").First(&document, documentID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateDocumentSummary(&document, models.ActionTypeUpdate, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleDocumentPublished(eventType string, data map[string]interface{}) error {
	log.Printf("Handling document published event: %v", data)

	if documentID, ok := data["document_id"].(uint); ok {
		var document models.Document
		if err := s.db.Preload("Agency").Preload("Categories").First(&document, documentID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateDocumentSummary(&document, models.ActionTypePublish, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleDocumentDeleted(eventType string, data map[string]interface{}) error {
	log.Printf("Handling document deleted event: %v", data)
	return nil
}

func (s *EventService) handleRegulationCreated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling regulation created event: %v", data)

	if regulationID, ok := data["regulation_id"].(uint); ok {
		var regulation models.LawsAndRules
		if err := s.db.Preload("Agency").First(&regulation, regulationID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateRegulationSummary(&regulation, models.ActionTypeCreate, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleRegulationUpdated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling regulation updated event: %v", data)

	if regulationID, ok := data["regulation_id"].(uint); ok {
		var regulation models.LawsAndRules
		if err := s.db.Preload("Agency").First(&regulation, regulationID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateRegulationSummary(&regulation, models.ActionTypeUpdate, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleRegulationPublished(eventType string, data map[string]interface{}) error {
	log.Printf("Handling regulation published event: %v", data)

	if regulationID, ok := data["regulation_id"].(uint); ok {
		var regulation models.LawsAndRules
		if err := s.db.Preload("Agency").First(&regulation, regulationID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateRegulationSummary(&regulation, models.ActionTypePublish, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleRegulationDeleted(eventType string, data map[string]interface{}) error {
	log.Printf("Handling regulation deleted event: %v", data)
	return nil
}

func (s *EventService) handleTaskCreated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling task created event: %v", data)

	// Auto-generate calendar events for tasks
	if taskID, ok := data["task_id"].(uint); ok {
		var task models.Task
		if err := s.db.First(&task, taskID).Error; err == nil {
			// Create calendar event if task has due date
			if task.DueDate != nil {
				calendarEvent := &models.Task{
					Title:       "Task Due: " + task.Title,
					Description: "Reminder for task: " + task.Description,
					Type:        models.TaskTypeDeadline,
					Status:      models.TaskStatusPending,
					StartDate:   task.DueDate,
					EndDate:     task.DueDate,
					IsAllDay:    true,
					SourceType:  "task",
					SourceID:    &task.ID,
					CreatedByID: task.CreatedByID,
				}
				s.db.Create(calendarEvent)
			}
		}
	}

	return nil
}

func (s *EventService) handleTaskCompleted(eventType string, data map[string]interface{}) error {
	log.Printf("Handling task completed event: %v", data)

	// Update related finance performance if applicable
	if taskID, ok := data["task_id"].(uint); ok {
		var task models.Task
		if err := s.db.First(&task, taskID).Error; err == nil {
			// If task is related to a document or regulation, update performance
			if task.DocumentID != nil || task.RegulationID != nil {
				financeService := NewFinanceService()
				financeService.UpdatePerformanceFromTask(&task)
			}
		}
	}

	return nil
}

func (s *EventService) handleTaskOverdue(eventType string, data map[string]interface{}) error {
	log.Printf("Handling task overdue event: %v", data)

	// Create notification or alert
	// This could trigger email notifications, dashboard alerts, etc.

	return nil
}

func (s *EventService) handleFinanceCreated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling finance created event: %v", data)

	// Auto-generate budget review tasks
	if financeID, ok := data["finance_id"].(uint); ok {
		var finance models.Finance
		if err := s.db.First(&finance, financeID).Error; err == nil {
			// Create quarterly review task
			reviewDate := time.Date(finance.Year, 3, 31, 0, 0, 0, 0, time.UTC) // End of Q1
			if reviewDate.After(time.Now()) {
				task := &models.Task{
					Title:       "Budget Review: " + finance.Description,
					Description: "Quarterly review of budget item: " + finance.Description,
					Type:        models.TaskTypeReview,
					Status:      models.TaskStatusPending,
					Priority:    models.TaskPriorityMedium,
					DueDate:     &reviewDate,
					SourceType:  "finance",
					SourceID:    &finance.ID,
					CreatedByID: 1, // System user
				}
				s.db.Create(task)
			}
		}
	}

	return nil
}

func (s *EventService) handleFinanceUpdated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling finance updated event: %v", data)
	return nil
}

func (s *EventService) handleAgencyCreated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling agency created event: %v", data)

	if agencyID, ok := data["agency_id"].(uint); ok {
		var agency models.Agency
		if err := s.db.First(&agency, agencyID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateAgencySummary(&agency, models.ActionTypeCreate, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleAgencyUpdated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling agency updated event: %v", data)

	if agencyID, ok := data["agency_id"].(uint); ok {
		var agency models.Agency
		if err := s.db.First(&agency, agencyID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateAgencySummary(&agency, models.ActionTypeUpdate, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleCategoryCreated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling category created event: %v", data)

	if categoryID, ok := data["category_id"].(uint); ok {
		var category models.Category
		if err := s.db.First(&category, categoryID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateCategorySummary(&category, models.ActionTypeCreate, userID)
			}
		}
	}

	return nil
}

func (s *EventService) handleCategoryUpdated(eventType string, data map[string]interface{}) error {
	log.Printf("Handling category updated event: %v", data)

	if categoryID, ok := data["category_id"].(uint); ok {
		var category models.Category
		if err := s.db.First(&category, categoryID).Error; err == nil {
			summaryService := NewSummaryService(s.db)
			if userID, ok := data["user_id"].(uint); ok {
				summaryService.CreateCategorySummary(&category, models.ActionTypeUpdate, userID)
			}
		}
	}

	return nil
}
