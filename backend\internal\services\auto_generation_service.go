package services

import (
	"fmt"
	"log"
	"strings"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// AutoGenerationService handles automatic generation of related entities
type AutoGenerationService struct {
	db             *gorm.DB
	summaryService *SummaryService
	taskService    *TaskService
	financeService *FinanceService
	nlpService     *NLPService
	eventService   *EventService
}

// NewAutoGenerationService creates a new auto-generation service
func NewAutoGenerationService(db *gorm.DB) *AutoGenerationService {
	return &AutoGenerationService{
		db:             db,
		summaryService: NewSummaryService(db),
		taskService:    NewTaskService(),
		financeService: NewFinanceService(),
		nlpService:     NewNLPService(),
		eventService:   NewEventService(db),
	}
}

// AutoGenerationConfig holds configuration for auto-generation
type AutoGenerationConfig struct {
	EnableSummaryGeneration  bool `json:"enable_summary_generation"`
	EnableTaskGeneration     bool `json:"enable_task_generation"`
	EnableCalendarGeneration bool `json:"enable_calendar_generation"`
	EnableFinanceGeneration  bool `json:"enable_finance_generation"`
	EnableRelationshipGen    bool `json:"enable_relationship_generation"`
}

// DefaultConfig returns default auto-generation configuration
func DefaultConfig() AutoGenerationConfig {
	return AutoGenerationConfig{
		EnableSummaryGeneration:  true,
		EnableTaskGeneration:     true,
		EnableCalendarGeneration: true,
		EnableFinanceGeneration:  true,
		EnableRelationshipGen:    true,
	}
}

// ProcessDocumentCreation handles auto-generation when a document is created
func (s *AutoGenerationService) ProcessDocumentCreation(document *models.Document, userID uint, config AutoGenerationConfig) error {
	log.Printf("Processing auto-generation for document: %s", document.Title)

	var errors []string
	successCount := 0
	totalTasks := 0

	// Generate summary
	if config.EnableSummaryGeneration {
		totalTasks++
		if err := s.summaryService.CreateDocumentSummary(document, models.ActionTypeCreate, userID); err != nil {
			log.Printf("Failed to create document summary: %v", err)
			errors = append(errors, fmt.Sprintf("summary generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated summary for document: %s", document.Title)
		}
	}

	// Generate tasks from content
	if config.EnableTaskGeneration {
		totalTasks++
		if err := s.generateDocumentTasks(document, userID); err != nil {
			log.Printf("Failed to generate document tasks: %v", err)
			errors = append(errors, fmt.Sprintf("task generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated tasks for document: %s", document.Title)
		}
	}

	// Generate calendar events
	if config.EnableCalendarGeneration {
		totalTasks++
		if err := s.generateDocumentCalendarEvents(document, userID); err != nil {
			log.Printf("Failed to generate document calendar events: %v", err)
			errors = append(errors, fmt.Sprintf("calendar generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated calendar events for document: %s", document.Title)
		}
	}

	// Generate finance records
	if config.EnableFinanceGeneration {
		totalTasks++
		if err := s.generateDocumentFinanceRecords(document, userID); err != nil {
			log.Printf("Failed to generate document finance records: %v", err)
			errors = append(errors, fmt.Sprintf("finance generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated finance records for document: %s", document.Title)
		}
	}

	// Generate relationships
	if config.EnableRelationshipGen {
		totalTasks++
		if err := s.generateDocumentRelationships(document, userID); err != nil {
			log.Printf("Failed to generate document relationships: %v", err)
			errors = append(errors, fmt.Sprintf("relationship generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated relationships for document: %s", document.Title)
		}
	}

	// Emit event for other services with detailed results
	s.eventService.EmitEvent("document.created", map[string]interface{}{
		"document_id":   document.ID,
		"user_id":       userID,
		"timestamp":     time.Now(),
		"success_count": successCount,
		"total_tasks":   totalTasks,
		"error_count":   len(errors),
		"errors":        errors,
		"success_rate":  float64(successCount) / float64(max(totalTasks, 1)) * 100,
	})

	// Return error if any critical failures occurred
	if len(errors) > 0 {
		if successCount == 0 {
			return fmt.Errorf("all auto-generation tasks failed: %s", strings.Join(errors, "; "))
		} else if len(errors) > totalTasks/2 {
			return fmt.Errorf("majority of auto-generation tasks failed (%d/%d): %s", len(errors), totalTasks, strings.Join(errors, "; "))
		}
		// Log warnings for partial failures but don't return error
		log.Printf("Auto-generation completed with some failures (%d/%d successful): %s", successCount, totalTasks, strings.Join(errors, "; "))
	}

	log.Printf("Auto-generation completed successfully for document: %s (%d/%d tasks successful)", document.Title, successCount, totalTasks)
	return nil
}

// ProcessRegulationCreation handles auto-generation when a regulation is created
func (s *AutoGenerationService) ProcessRegulationCreation(regulation *models.LawsAndRules, userID uint, config AutoGenerationConfig) error {
	log.Printf("Processing auto-generation for regulation: %s", regulation.Title)

	var errors []string
	successCount := 0
	totalTasks := 0

	// Generate summary
	if config.EnableSummaryGeneration {
		totalTasks++
		if err := s.summaryService.CreateRegulationSummary(regulation, models.ActionTypeCreate, userID); err != nil {
			log.Printf("Failed to create regulation summary: %v", err)
			errors = append(errors, fmt.Sprintf("summary generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated summary for regulation: %s", regulation.Title)
		}
	}

	// Generate implementation tasks
	if config.EnableTaskGeneration {
		totalTasks++
		if err := s.generateRegulationTasks(regulation, userID); err != nil {
			log.Printf("Failed to generate regulation tasks: %v", err)
			errors = append(errors, fmt.Sprintf("task generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated tasks for regulation: %s", regulation.Title)
		}
	}

	// Generate calendar events
	if config.EnableCalendarGeneration {
		totalTasks++
		if err := s.generateRegulationCalendarEvents(regulation, userID); err != nil {
			log.Printf("Failed to generate regulation calendar events: %v", err)
			errors = append(errors, fmt.Sprintf("calendar generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated calendar events for regulation: %s", regulation.Title)
		}
	}

	// Generate finance records
	if config.EnableFinanceGeneration {
		totalTasks++
		if err := s.generateRegulationFinanceRecords(regulation, userID); err != nil {
			log.Printf("Failed to generate regulation finance records: %v", err)
			errors = append(errors, fmt.Sprintf("finance generation: %v", err))
		} else {
			successCount++
			log.Printf("Successfully generated finance records for regulation: %s", regulation.Title)
		}
	}

	// Emit event with detailed results
	s.eventService.EmitEvent("regulation.created", map[string]interface{}{
		"regulation_id": regulation.ID,
		"user_id":       userID,
		"timestamp":     time.Now(),
		"success_count": successCount,
		"total_tasks":   totalTasks,
		"error_count":   len(errors),
		"errors":        errors,
		"success_rate":  float64(successCount) / float64(max(totalTasks, 1)) * 100,
	})

	// Return error if any critical failures occurred
	if len(errors) > 0 {
		if successCount == 0 {
			return fmt.Errorf("all auto-generation tasks failed for regulation: %s", strings.Join(errors, "; "))
		} else if len(errors) > totalTasks/2 {
			return fmt.Errorf("majority of auto-generation tasks failed for regulation (%d/%d): %s", len(errors), totalTasks, strings.Join(errors, "; "))
		}
		// Log warnings for partial failures but don't return error
		log.Printf("Auto-generation completed with some failures for regulation (%d/%d successful): %s", successCount, totalTasks, strings.Join(errors, "; "))
	}

	log.Printf("Auto-generation completed successfully for regulation: %s (%d/%d tasks successful)", regulation.Title, successCount, totalTasks)
	return nil
}

// generateDocumentTasks creates tasks based on document content
func (s *AutoGenerationService) generateDocumentTasks(document *models.Document, userID uint) error {
	// Parse content for dates and deadlines
	deadlines := s.nlpService.ExtractDeadlines(document.Content)

	for _, deadline := range deadlines {
		task := &models.Task{
			Title:          fmt.Sprintf("Document Deadline: %s", deadline.Description),
			Description:    fmt.Sprintf("Deadline extracted from document '%s': %s", document.Title, deadline.Context),
			Type:           models.TaskTypeDeadline,
			Status:         models.TaskStatusPending,
			Priority:       s.calculateTaskPriority(deadline.Urgency),
			DueDate:        &deadline.Date,
			SourceType:     "document",
			SourceID:       &document.ID,
			SourceText:     deadline.Context,
			ParsedFromText: true,
			CreatedByID:    userID,
			AgencyID:       &document.AgencyID,
		}

		if err := s.db.Create(task).Error; err != nil {
			log.Printf("Failed to create task from document: %v", err)
			continue
		}
	}

	// Create review tasks for document lifecycle
	if document.EffectiveDate != nil {
		reviewDate := document.EffectiveDate.AddDate(0, 0, -30) // 30 days before effective
		if reviewDate.After(time.Now()) {
			task := &models.Task{
				Title:       fmt.Sprintf("Review Document Before Effective Date: %s", document.Title),
				Description: fmt.Sprintf("Review document '%s' before it becomes effective on %s", document.Title, document.EffectiveDate.Format("2006-01-02")),
				Type:        models.TaskTypeReview,
				Status:      models.TaskStatusPending,
				Priority:    models.TaskPriorityMedium,
				DueDate:     &reviewDate,
				SourceType:  "document",
				SourceID:    &document.ID,
				CreatedByID: userID,
				AgencyID:    &document.AgencyID,
			}

			if err := s.db.Create(task).Error; err != nil {
				log.Printf("Failed to create review task: %v", err)
			}
		}
	}

	return nil
}

// generateDocumentCalendarEvents creates calendar events from document dates
func (s *AutoGenerationService) generateDocumentCalendarEvents(document *models.Document, userID uint) error {
	// Publication date event
	if document.PublicationDate != nil {
		event := &models.Task{
			Title:       fmt.Sprintf("Document Published: %s", document.Title),
			Description: fmt.Sprintf("Document '%s' is published", document.Title),
			Type:        models.TaskTypeDeadline,
			Status:      models.TaskStatusPending,
			StartDate:   document.PublicationDate,
			EndDate:     document.PublicationDate,
			IsAllDay:    true,
			SourceType:  "document",
			SourceID:    &document.ID,
			CreatedByID: userID,
			AgencyID:    &document.AgencyID,
		}

		if err := s.db.Create(event).Error; err != nil {
			log.Printf("Failed to create publication event: %v", err)
		}
	}

	// Effective date event
	if document.EffectiveDate != nil {
		event := &models.Task{
			Title:       fmt.Sprintf("Document Effective: %s", document.Title),
			Description: fmt.Sprintf("Document '%s' becomes effective", document.Title),
			Type:        models.TaskTypeDeadline,
			Status:      models.TaskStatusPending,
			StartDate:   document.EffectiveDate,
			EndDate:     document.EffectiveDate,
			IsAllDay:    true,
			SourceType:  "document",
			SourceID:    &document.ID,
			CreatedByID: userID,
			AgencyID:    &document.AgencyID,
		}

		if err := s.db.Create(event).Error; err != nil {
			log.Printf("Failed to create effective date event: %v", err)
		}
	}

	// Comment due date event
	if document.CommentDueDate != nil {
		event := &models.Task{
			Title:       fmt.Sprintf("Comments Due: %s", document.Title),
			Description: fmt.Sprintf("Public comments due for document '%s'", document.Title),
			Type:        models.TaskTypeDeadline,
			Status:      models.TaskStatusPending,
			StartDate:   document.CommentDueDate,
			EndDate:     document.CommentDueDate,
			IsAllDay:    true,
			SourceType:  "document",
			SourceID:    &document.ID,
			CreatedByID: userID,
			AgencyID:    &document.AgencyID,
		}

		if err := s.db.Create(event).Error; err != nil {
			log.Printf("Failed to create comment deadline event: %v", err)
		}
	}

	return nil
}

// generateDocumentFinanceRecords creates finance records based on document content
func (s *AutoGenerationService) generateDocumentFinanceRecords(document *models.Document, userID uint) error {
	// Parse content for financial implications
	costs := s.nlpService.ExtractCosts(document.Content)

	for _, cost := range costs {
		finance := &models.Finance{
			Amount:           cost.Amount,
			Year:             time.Now().Year(),
			Description:      fmt.Sprintf("Cost estimate from document '%s': %s", document.Title, cost.Description),
			DocumentID:       &document.ID,
			BudgetType:       "estimated",
			IsAutoCalculated: true,
		}

		if err := s.db.Create(finance).Error; err != nil {
			log.Printf("Failed to create finance record: %v", err)
			continue
		}
	}

	return nil
}

// generateDocumentRelationships finds and creates relationships with existing entities
func (s *AutoGenerationService) generateDocumentRelationships(document *models.Document, userID uint) error {
	// Find related regulations based on content similarity
	relatedRegulations := s.nlpService.FindRelatedRegulations(document.Content, s.db)

	for _, regulation := range relatedRegulations {
		relationship := &models.RegulationDocumentRelationship{
			RegulationID:     regulation.ID,
			DocumentID:       document.ID,
			RelationshipType: models.DocumentRelationshipReferences,
			Description:      "Auto-generated relationship based on content similarity",
			CreatedByID:      userID,
			IsActive:         true,
		}

		if err := s.db.Create(relationship).Error; err != nil {
			log.Printf("Failed to create document-regulation relationship: %v", err)
		}
	}

	return nil
}

// calculateTaskPriority determines task priority based on urgency
func (s *AutoGenerationService) calculateTaskPriority(urgency string) models.TaskPriority {
	switch strings.ToLower(urgency) {
	case "urgent", "immediate", "critical":
		return models.TaskPriorityHigh
	case "soon", "important":
		return models.TaskPriorityMedium
	default:
		return models.TaskPriorityLow
	}
}

// generateRegulationTasks creates implementation and compliance tasks for regulations
func (s *AutoGenerationService) generateRegulationTasks(regulation *models.LawsAndRules, userID uint) error {
	// Implementation deadline task
	if regulation.EffectiveDate != nil {
		implementationDate := regulation.EffectiveDate.AddDate(0, 0, -60) // 60 days before effective
		if implementationDate.After(time.Now()) {
			task := &models.Task{
				Title:       fmt.Sprintf("Implement Regulation: %s", regulation.Title),
				Description: fmt.Sprintf("Prepare for implementation of regulation '%s' effective %s", regulation.Title, regulation.EffectiveDate.Format("2006-01-02")),
				Type:        models.TaskTypeDeadline,
				Status:      models.TaskStatusPending,
				Priority:    models.TaskPriorityHigh,
				DueDate:     &implementationDate,
				SourceType:  "regulation",
				SourceID:    &regulation.ID,
				CreatedByID: userID,
				AgencyID:    &regulation.AgencyID,
			}

			if err := s.db.Create(task).Error; err != nil {
				log.Printf("Failed to create implementation task: %v", err)
			}
		}
	}

	return nil
}

// generateRegulationCalendarEvents creates calendar events for regulation milestones
func (s *AutoGenerationService) generateRegulationCalendarEvents(regulation *models.LawsAndRules, userID uint) error {
	// Effective date event
	if regulation.EffectiveDate != nil {
		event := &models.Task{
			Title:       fmt.Sprintf("Regulation Effective: %s", regulation.Title),
			Description: fmt.Sprintf("Regulation '%s' becomes effective", regulation.Title),
			Type:        models.TaskTypeDeadline,
			Status:      models.TaskStatusPending,
			StartDate:   regulation.EffectiveDate,
			EndDate:     regulation.EffectiveDate,
			IsAllDay:    true,
			SourceType:  "regulation",
			SourceID:    &regulation.ID,
			CreatedByID: userID,
			AgencyID:    &regulation.AgencyID,
		}

		if err := s.db.Create(event).Error; err != nil {
			log.Printf("Failed to create regulation effective event: %v", err)
		}
	}

	return nil
}

// generateRegulationFinanceRecords creates budget records for regulation implementation
func (s *AutoGenerationService) generateRegulationFinanceRecords(regulation *models.LawsAndRules, userID uint) error {
	// Estimate implementation costs based on regulation complexity
	estimatedCost := s.estimateRegulationCost(regulation)

	if estimatedCost > 0 {
		finance := &models.Finance{
			Amount:           estimatedCost,
			Year:             time.Now().Year(),
			Description:      fmt.Sprintf("Estimated implementation cost for regulation '%s'", regulation.Title),
			RegulationID:     &regulation.ID,
			BudgetType:       "estimated",
			IsAutoCalculated: true,
		}

		if err := s.db.Create(finance).Error; err != nil {
			log.Printf("Failed to create regulation finance record: %v", err)
		}
	}

	return nil
}

// estimateRegulationCost provides a basic cost estimation for regulations
func (s *AutoGenerationService) estimateRegulationCost(regulation *models.LawsAndRules) float64 {
	// Simple heuristic based on regulation characteristics
	baseCost := 10000.0 // Base implementation cost

	if regulation.IsSignificant {
		baseCost *= 5 // Significant regulations cost more
	}

	// Adjust based on content length (proxy for complexity)
	contentLength := len(regulation.Description)
	if contentLength > 1000 {
		baseCost *= 1.5
	}

	return baseCost
}
