-- Add missing columns for digital signatures and document processing jobs

-- Add hash_algorithm column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS hash_algorithm VARCHAR(50) DEFAULT 'sha256';

-- Add encryption_algorithm column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS encryption_algorithm VARCHAR(50) DEFAULT 'rsa';

-- Add key_size column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS key_size INTEGER DEFAULT 2048;

-- Add signature_algorithm column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS signature_algorithm VARCHAR(50) DEFAULT 'rsa';

-- Add biometric_type column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS biometric_type VARCHAR(50);

-- Add biometric_data column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS biometric_data TEXT;

-- Add biometric_score column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS biometric_score DECIMAL(3,2);

-- Add biometric_threshold column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS biometric_threshold DECIMAL(3,2);

-- Add liveness_detection column to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS liveness_detection BOOLEAN DEFAULT FALSE;

-- Add processing_time column to document_processing_jobs table  
ALTER TABLE document_processing_jobs 
ADD COLUMN IF NOT EXISTS processing_time INTEGER DEFAULT 0;

-- Update any existing records to have default values
UPDATE digital_signatures SET hash_algorithm = 'sha256' WHERE hash_algorithm IS NULL;
UPDATE digital_signatures SET encryption_algorithm = 'rsa' WHERE encryption_algorithm IS NULL;
UPDATE digital_signatures SET key_size = 2048 WHERE key_size IS NULL;
UPDATE digital_signatures SET signature_algorithm = 'rsa' WHERE signature_algorithm IS NULL;
UPDATE document_processing_jobs SET processing_time = 0 WHERE processing_time IS NULL;
