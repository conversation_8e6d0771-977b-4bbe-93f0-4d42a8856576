'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { complianceApi } from '../../../../services/enterpriseApi';

interface ComplianceFinding {
  id: number;
  created_at: string;
  updated_at: string;
  finding_code: string;
  title: string;
  description?: string;
  assessment_id?: number;
  requirement_id?: number;
  severity: string;
  category: string;
  status: string;
  identified_date: string;
  due_date?: string;
  resolved_date?: string;
  assigned_to_id?: number;
  evidence?: string;
  impact?: string;
  recommendation?: string;
  remediation_plan?: string;
  cost_to_fix?: number;
  currency_code?: string;
  risk_rating?: string;
  compliance_gap?: string;
  root_cause?: string;
  recurrence_risk?: string;
  validation_method?: string;
  is_systemic?: boolean;
  metadata?: string;
}

const NewComplianceFindingPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [assessments, setAssessments] = useState<any[]>([]);
  const [requirements, setRequirements] = useState<any[]>([]);
  const [formData, setFormData] = useState<Partial<ComplianceFinding>>({
    finding_code: '',
    title: '',
    description: '',
    assessment_id: undefined,
    requirement_id: undefined,
    severity: 'medium',
    category: 'compliance',
    status: 'open',
    identified_date: new Date().toISOString().split('T')[0],
    due_date: '',
    resolved_date: '',
    assigned_to_id: undefined,
    evidence: '',
    impact: '',
    recommendation: '',
    remediation_plan: '',
    cost_to_fix: 0,
    currency_code: 'USD',
    risk_rating: 'medium',
    compliance_gap: '',
    root_cause: '',
    recurrence_risk: 'low',
    validation_method: '',
    is_systemic: false,
    metadata: ''
  });

  useEffect(() => {
    fetchAssessments();
    fetchRequirements();
  }, []);

  const fetchAssessments = async () => {
    try {
      const response = await complianceApi.getAssessments();
      setAssessments(response.data);
    } catch (err: any) {
      console.log('Failed to fetch assessments:', err.message);
    }
  };

  const fetchRequirements = async () => {
    try {
      const response = await complianceApi.getRequirements();
      setRequirements(response.data);
    } catch (err: any) {
      console.log('Failed to fetch requirements:', err.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await complianceApi.createFinding(formData);
      router.push('/enterprise/compliance/findings');
    } catch (err: any) {
      setError(err.message || 'Failed to create compliance finding');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create New Compliance Finding</h1>
        <button
          onClick={() => router.push('/enterprise/compliance/findings')}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Back to Findings
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Finding Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Finding Code *
            </label>
            <input
              type="text"
              name="finding_code"
              value={formData.finding_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., FIND-2025-001"
            />
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Missing Data Encryption"
            />
          </div>

          {/* Assessment */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Related Assessment
            </label>
            <select
              name="assessment_id"
              value={formData.assessment_id || ''}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Assessment</option>
              {assessments.map((assessment) => (
                <option key={assessment.id} value={assessment.id}>
                  {assessment.title}
                </option>
              ))}
            </select>
          </div>

          {/* Requirement */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Related Requirement
            </label>
            <select
              name="requirement_id"
              value={formData.requirement_id || ''}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Requirement</option>
              {requirements.map((requirement) => (
                <option key={requirement.id} value={requirement.id}>
                  {requirement.title}
                </option>
              ))}
            </select>
          </div>

          {/* Severity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Severity *
            </label>
            <select
              name="severity"
              value={formData.severity}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="compliance">Compliance</option>
              <option value="security">Security</option>
              <option value="operational">Operational</option>
              <option value="financial">Financial</option>
              <option value="legal">Legal</option>
              <option value="technical">Technical</option>
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
              <option value="deferred">Deferred</option>
            </select>
          </div>

          {/* Risk Rating */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Risk Rating
            </label>
            <select
              name="risk_rating"
              value={formData.risk_rating}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Identified Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Identified Date *
            </label>
            <input
              type="date"
              name="identified_date"
              value={formData.identified_date}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Due Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Due Date
            </label>
            <input
              type="date"
              name="due_date"
              value={formData.due_date}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Cost to Fix */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost to Fix
            </label>
            <input
              type="number"
              name="cost_to_fix"
              value={formData.cost_to_fix}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency Code
            </label>
            <input
              type="text"
              name="currency_code"
              value={formData.currency_code}
              onChange={handleChange}
              maxLength={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="USD"
            />
          </div>
        </div>

        {/* Text Areas */}
        <div className="mt-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Detailed description of the finding..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Evidence
            </label>
            <textarea
              name="evidence"
              value={formData.evidence}
              onChange={handleChange}
              rows={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Evidence supporting this finding..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Impact
            </label>
            <textarea
              name="impact"
              value={formData.impact}
              onChange={handleChange}
              rows={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Impact of this finding on the organization..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Recommendation
            </label>
            <textarea
              name="recommendation"
              value={formData.recommendation}
              onChange={handleChange}
              rows={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Recommended actions to address this finding..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remediation Plan
            </label>
            <textarea
              name="remediation_plan"
              value={formData.remediation_plan}
              onChange={handleChange}
              rows={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Detailed plan to remediate this finding..."
            />
          </div>
        </div>

        {/* Systemic Finding */}
        <div className="mt-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_systemic"
              checked={formData.is_systemic}
              onChange={handleChange}
              className="mr-2"
            />
            This is a systemic finding
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push('/enterprise/compliance/findings')}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Finding'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NewComplianceFindingPage;
