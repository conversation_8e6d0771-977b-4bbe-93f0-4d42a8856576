'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  CalendarIcon,
  UserIcon,
  TagIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../../components/Layout/Layout';
import { useAuthStore } from '../../../../stores/authStore';
import { contentApi } from '../../../../services/enterpriseApi';
import { ContentVersion } from '../../../../types/enterprise';

const ContentVersionViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const versionId = params.id as string;
  const { user } = useAuthStore();
  const [version, setVersion] = useState<ContentVersion | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (versionId) {
      fetchVersion();
    }
  }, [versionId]);

  const fetchVersion = async () => {
    try {
      const response = await contentApi.getVersion(parseInt(versionId));
      setVersion(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch content version');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);
      await contentApi.deleteVersion(parseInt(versionId));
      router.push('/enterprise/content/versions');
    } catch (err: any) {
      setError(err.message || 'Failed to delete content version');
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'rejected':
        return <XCircleIcon className="h-4 w-4" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const canEdit = user && (user.role === 'admin' || user.role === 'editor');

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading content version...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !version) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Content Version Not Found</h1>
            <p className="text-gray-600 mt-2">{error || 'The requested content version could not be found.'}</p>
            <Link
              href="/enterprise/content/versions"
              className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Content Versions
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/enterprise/content/versions"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Content Versions
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Content Version {version.version_number}
              </h1>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(version.approval_status)}`}>
                  {getStatusIcon(version.approval_status)}
                  <span className="ml-1">{version.approval_status.charAt(0).toUpperCase() + version.approval_status.slice(1)}</span>
                </span>
                <span className="text-sm text-gray-500">
                  Document ID: {version.document_id}
                </span>
              </div>
            </div>
            
            {canEdit && (
              <div className="flex space-x-2">
                <Link
                  href={`/enterprise/content/versions/${version.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </Link>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Version Details */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Version Details</h2>
          </div>
          
          <div className="px-6 py-4 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Version Number</h3>
                <p className="text-gray-900">{version.version_number}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">File Name</h3>
                <p className="text-gray-900">{version.title}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">File Size</h3>
                <p className="text-gray-900">{(version.file_size / 1024).toFixed(2)} KB</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">MIME Type</h3>
                <p className="text-gray-900">{version.mime_type}</p>
              </div>
            </div>

            {/* Content */}
            {version.content && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <DocumentTextIcon className="h-4 w-4 mr-2" />
                  Content
                </h3>
                <div className="bg-gray-50 p-4 rounded-md">
                  <pre className="whitespace-pre-wrap text-sm text-gray-900">{version.content}</pre>
                </div>
              </div>
            )}

            {/* Metadata */}
            {version.metadata && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <TagIcon className="h-4 w-4 mr-2" />
                  Metadata
                </h3>
                <div className="bg-gray-50 p-4 rounded-md">
                  <pre className="text-sm text-gray-900">{JSON.stringify(version.metadata, null, 2)}</pre>
                </div>
              </div>
            )}

            {/* Approval Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
              {version.approved_by_id && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <UserIcon className="h-4 w-4 mr-2" />
                    Approved By
                  </h3>
                  <p className="text-gray-900">User ID: {version.approved_by_id}</p>
                </div>
              )}

              {version.approved_at && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <CalendarIcon className="h-4 w-4 mr-2" />
                    Approved At
                  </h3>
                  <p className="text-gray-900">{formatDate(version.approved_at)}</p>
                </div>
              )}
            </div>

            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Created</h3>
                <p className="text-gray-900">{formatDate(version.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h3>
                <p className="text-gray-900">{formatDate(version.updated_at)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <TrashIcon className="mx-auto h-12 w-12 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900 mt-2">Delete Content Version</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Are you sure you want to delete this content version? This action cannot be undone.
                </p>
                <div className="flex justify-center space-x-3 mt-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                    disabled={deleteLoading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ContentVersionViewPage;
