'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  CogIcon,
  BellIcon,
  PaintBrushIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  DevicePhoneMobileIcon,
  ComputerDesktopIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';

interface UserSettings {
  notifications: {
    email_notifications: boolean;
    push_notifications: boolean;
    document_alerts: boolean;
    comment_notifications: boolean;
    weekly_digest: boolean;
    security_alerts: boolean;
    system_updates: boolean;
  };
  display: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    date_format: string;
    items_per_page: number;
    compact_view: boolean;
    show_tooltips: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'agency' | 'private';
    activity_tracking: boolean;
    analytics_consent: boolean;
    data_sharing: boolean;
  };
  accessibility: {
    high_contrast: boolean;
    large_text: boolean;
    reduced_motion: boolean;
    screen_reader_support: boolean;
  };
}

const SettingsPage: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [settings, setSettings] = useState<UserSettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeSection, setActiveSection] = useState('notifications');

  useEffect(() => {
    // Only fetch data if authenticated - Layout component handles auth redirects
    if (!isAuthenticated) {
      return;
    }

    fetchSettings();
  }, [isAuthenticated, router]);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      const response = await apiService.getUserSettings();
      setSettings(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (section: keyof UserSettings, updatedData: any) => {
    try {
      setSaving(true);
      setError('');
      setSuccess('');

      const response = await apiService.updateUserSettings({
        [section]: updatedData
      });

      setSettings(response.data);
      setSuccess('Settings updated successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update settings');
    } finally {
      setSaving(false);
    }
  };

  const sections = [
    { id: 'notifications', name: 'Notifications', icon: BellIcon },
    { id: 'display', name: 'Display', icon: ComputerDesktopIcon },
    { id: 'privacy', name: 'Privacy', icon: ShieldCheckIcon },
    { id: 'accessibility', name: 'Accessibility', icon: GlobeAltIcon },
  ];

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              <div className="h-64 bg-gray-200 rounded"></div>
              <div className="lg:col-span-3 h-96 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (!settings) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <CogIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Settings not found</h3>
            <p className="text-gray-600 mb-4">{error || 'Unable to load your settings.'}</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Settings</h1>
          <p className="text-gray-600 mt-1">Customize your experience and preferences</p>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <XMarkIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <nav className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    activeSection === section.id
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <section.icon className="h-5 w-5 mr-3" />
                  {section.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-md p-6">
              {activeSection === 'notifications' && (
                <NotificationsSection
                  settings={settings.notifications}
                  onUpdate={(data) => updateSettings('notifications', data)}
                  saving={saving}
                />
              )}

              {activeSection === 'display' && (
                <DisplaySection
                  settings={settings.display}
                  onUpdate={(data) => updateSettings('display', data)}
                  saving={saving}
                />
              )}

              {activeSection === 'privacy' && (
                <PrivacySection
                  settings={settings.privacy}
                  onUpdate={(data) => updateSettings('privacy', data)}
                  saving={saving}
                />
              )}

              {activeSection === 'accessibility' && (
                <AccessibilitySection
                  settings={settings.accessibility}
                  onUpdate={(data) => updateSettings('accessibility', data)}
                  saving={saving}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

// Notifications Section Component
const NotificationsSection: React.FC<{
  settings: UserSettings['notifications'];
  onUpdate: (data: UserSettings['notifications']) => void;
  saving: boolean;
}> = ({ settings, onUpdate, saving }) => {
  const [formData, setFormData] = useState(settings);

  const handleChange = (key: keyof UserSettings['notifications']) => {
    const newData = { ...formData, [key]: !formData[key] };
    setFormData(newData);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdate(formData);
  };

  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Notification Preferences</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Email Notifications</h3>
              <p className="text-sm text-gray-500">Receive notifications via email</p>
            </div>
            <button
              type="button"
              onClick={() => handleChange('email_notifications')}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                formData.email_notifications ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  formData.email_notifications ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Push Notifications</h3>
              <p className="text-sm text-gray-500">Receive push notifications in your browser</p>
            </div>
            <button
              type="button"
              onClick={() => handleChange('push_notifications')}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                formData.push_notifications ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  formData.push_notifications ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Document Alerts</h3>
              <p className="text-sm text-gray-500">Get notified when documents are updated</p>
            </div>
            <button
              type="button"
              onClick={() => handleChange('document_alerts')}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                formData.document_alerts ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  formData.document_alerts ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Weekly Digest</h3>
              <p className="text-sm text-gray-500">Receive a weekly summary of activity</p>
            </div>
            <button
              type="button"
              onClick={() => handleChange('weekly_digest')}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                formData.weekly_digest ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  formData.weekly_digest ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-sm font-medium text-gray-900">Security Alerts</h3>
              <p className="text-sm text-gray-500">Important security notifications</p>
            </div>
            <button
              type="button"
              onClick={() => handleChange('security_alerts')}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 ${
                formData.security_alerts ? 'bg-primary-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  formData.security_alerts ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={saving}
            className="px-6 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
          >
            {saving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
};

// Display Section Component (simplified for space)
const DisplaySection: React.FC<{
  settings: UserSettings['display'];
  onUpdate: (data: UserSettings['display']) => void;
  saving: boolean;
}> = ({ settings, onUpdate, saving }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Display Preferences</h2>
      <p className="text-gray-600">Theme, language, and display options would be configured here.</p>
    </div>
  );
};

// Privacy Section Component (simplified for space)
const PrivacySection: React.FC<{
  settings: UserSettings['privacy'];
  onUpdate: (data: UserSettings['privacy']) => void;
  saving: boolean;
}> = ({ settings, onUpdate, saving }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Privacy Settings</h2>
      <p className="text-gray-600">Privacy and data sharing preferences would be configured here.</p>
    </div>
  );
};

// Accessibility Section Component (simplified for space)
const AccessibilitySection: React.FC<{
  settings: UserSettings['accessibility'];
  onUpdate: (data: UserSettings['accessibility']) => void;
  saving: boolean;
}> = ({ settings, onUpdate, saving }) => {
  return (
    <div>
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Accessibility Options</h2>
      <p className="text-gray-600">Accessibility features and options would be configured here.</p>
    </div>
  );
};

export default SettingsPage;
