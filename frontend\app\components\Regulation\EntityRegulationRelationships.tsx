import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  ScaleIcon,
  CalendarIcon,
  InformationCircleIcon,
  ChevronRightIcon
} from '@heroicons/react/24/outline';
import {
  RegulationDocumentRelationship,
  RegulationAgencyRelationship,
  RegulationCategoryRelationship
} from '../../types';
import regulationApi from '../../services/regulationApi';

interface EntityRegulationRelationshipsProps {
  entityType: 'document' | 'agency' | 'category';
  entityId: number;
  entityName: string;
}

const EntityRegulationRelationships: React.FC<EntityRegulationRelationshipsProps> = ({
  entityType,
  entityId,
  entityName
}) => {
  const [loading, setLoading] = useState(false);
  const [relationships, setRelationships] = useState<
    RegulationDocumentRelationship[] | RegulationAgencyRelationship[] | RegulationCategoryRelationship[]
  >([]);

  useEffect(() => {
    loadRelationships();
  }, [entityType, entityId]);

  const loadRelationships = async () => {
    try {
      setLoading(true);
      let data;
      switch (entityType) {
        case 'document':
          data = await regulationApi.getDocumentRegulations(entityId);
          break;
        case 'agency':
          data = await regulationApi.getAgencyRegulations(entityId);
          break;
        case 'category':
          data = await regulationApi.getCategoryRegulations(entityId);
          break;
      }
      setRelationships(data);
    } catch (error) {
      console.error('Failed to load regulation relationships:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRelationshipTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      // Document relationships
      implements: 'Implements',
      based_on: 'Based On',
      amends: 'Amends',
      repeals: 'Repeals',
      references: 'References',
      supersedes: 'Supersedes',
      // Agency relationships
      established_by: 'Established By',
      authorized_by: 'Authorized By',
      modified_by: 'Modified By',
      abolished_by: 'Abolished By',
      // Category relationships
      created_by: 'Created By',
      governed_by: 'Governed By'
    };
    return labels[type] || type;
  };

  const getRelationshipTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      implements: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      based_on: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      amends: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      repeals: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      references: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
      supersedes: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
      established_by: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      authorized_by: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
      modified_by: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
      abolished_by: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
      created_by: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
      governed_by: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    };
    return colors[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
  };

  // const getEntityTypeLabel = () => {
  //   switch (entityType) {
  //     case 'document':
  //       return 'Document';
  //     case 'agency':
  //       return 'Agency';
  //     case 'category':
  //       return 'Category';
  //     default:
  //       return 'Entity';
  //   }
  // };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (relationships.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center mb-4">
          <ScaleIcon className="h-6 w-6 text-blue-500 mr-3" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Related Regulations
          </h3>
        </div>
        <div className="text-center text-gray-500 dark:text-gray-400 py-8">
          <InformationCircleIcon className="h-12 w-12 mx-auto mb-4 text-gray-300 dark:text-gray-600" />
          <p>No regulations are currently related to this {entityType}.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <ScaleIcon className="h-6 w-6 text-blue-500 mr-3" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Related Regulations
            </h3>
          </div>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
            {relationships.length} relationship{relationships.length !== 1 ? 's' : ''}
          </span>
        </div>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Regulations that have a relationship with this {entityType.toLowerCase()}
        </p>
      </div>

      <div className="p-6">
        <div className="space-y-4">
          {relationships.map((relationship) => {
            const regulation = relationship.regulation;
            if (!regulation) return null;

            return (
              <div
                key={relationship.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRelationshipTypeColor(relationship.relationship_type)}`}>
                        {getRelationshipTypeLabel(relationship.relationship_type)}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {regulation.type.toUpperCase()}
                      </span>
                    </div>
                    
                    <Link
                      href={`/regulations/${regulation.id}`}
                      className="block group"
                    >
                      <h4 className="text-lg font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        {regulation.title}
                      </h4>
                      {regulation.short_title && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {regulation.short_title}
                        </p>
                      )}
                    </Link>

                    {relationship.description && (
                      <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                        {relationship.description}
                      </p>
                    )}

                    <div className="mt-3 flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                      {regulation.agency && (
                        <div className="flex items-center">
                          <span className="font-medium">Agency:</span>
                          <span className="ml-1">{regulation.agency.name}</span>
                        </div>
                      )}
                      
                      {relationship.effective_date && (
                        <div className="flex items-center">
                          <CalendarIcon className="h-4 w-4 mr-1" />
                          <span>Effective: {new Date(relationship.effective_date).toLocaleDateString()}</span>
                        </div>
                      )}
                      
                      {regulation.cfr_title && (
                        <div className="flex items-center">
                          <span className="font-medium">CFR:</span>
                          <span className="ml-1">{regulation.cfr_title}</span>
                        </div>
                      )}
                    </div>

                    {/* Additional relationship-specific information */}
                    {'citation_text' in relationship && relationship.citation_text && (
                      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span className="font-medium">Citation:</span> {relationship.citation_text}
                      </div>
                    )}
                    
                    {'authority_scope' in relationship && relationship.authority_scope && (
                      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span className="font-medium">Authority Scope:</span> {relationship.authority_scope}
                      </div>
                    )}
                    
                    {'scope_definition' in relationship && relationship.scope_definition && (
                      <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                        <span className="font-medium">Scope:</span> {relationship.scope_definition}
                      </div>
                    )}
                  </div>

                  <Link
                    href={`/regulations/${regulation.id}`}
                    className="ml-4 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    <ChevronRightIcon className="h-5 w-5" />
                  </Link>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default EntityRegulationRelationships;
