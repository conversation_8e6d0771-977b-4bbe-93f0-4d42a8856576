-- PostgreSQL Setup Script for NoteControl Federal Register Clone
-- Run this script as the postgres superuser

-- Create the database user if it doesn't exist
DO
$do$
BEGIN
   IF NOT EXISTS (
      SELECT FROM pg_catalog.pg_roles
      WHERE  rolname = 'postgres') THEN

      CREATE ROLE postgres LOGIN PASSWORD 'NoteControl2024!';
   END IF;
END
$do$;

-- Grant necessary privileges to the user
ALTER USER postgres CREATEDB;
ALTER USER postgres WITH SUPERUSER;

-- Create the database if it doesn't exist
SELECT 'CREATE DATABASE federal_register_db'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'federal_register_db')\gexec

-- Connect to the new database
\c federal_register_db;

-- Create necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Grant all privileges on the database to the user
GRANT ALL PRIVILEGES ON DATABASE federal_register_db TO postgres;

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO postgres;
GRANT CREATE ON SCHEMA public TO postgres;

-- Show confirmation
\echo 'Database setup completed successfully!'
\echo 'Database: federal_register_db'
\echo 'User: postgres'
\echo 'Extensions installed: uuid-ossp, pg_trgm, unaccent'

-- List databases to confirm
\l

-- List extensions to confirm
\dx
