package services

import (
	"encoding/json"
	"fmt"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// ProceedingService handles proceeding-related business logic
type ProceedingService struct {
	db *gorm.DB
}

// NewProceedingService creates a new proceeding service
func NewProceedingService() *ProceedingService {
	return &ProceedingService{
		db: database.GetDB(),
	}
}

// ValidateSequentialExecution validates that steps can be executed in sequence (requirement 2)
func (s *ProceedingService) ValidateSequentialExecution(proceedingID uint, stepOrder int, newStatus string) error {
	if newStatus != "in_progress" && newStatus != "completed" {
		return nil // No validation needed for other statuses
	}

	// Get the step being updated
	var currentStep models.ProceedingStep
	err := s.db.Where("proceeding_id = ? AND step_order = ?", proceedingID, stepOrder).
		First(&currentStep).Error
	if err != nil {
		return fmt.Errorf("step not found: %w", err)
	}

	// If this step doesn't require previous step completion, allow it
	if !currentStep.RequiresPreviousCompletion {
		return nil
	}

	// Check if previous steps are completed (unless they allow concurrent execution)
	for i := 1; i < stepOrder; i++ {
		var prevStep models.ProceedingStep
		err := s.db.Where("proceeding_id = ? AND step_order = ?", proceedingID, i).
			First(&prevStep).Error
		if err != nil {
			continue // Skip if step doesn't exist
		}

		// If previous step blocks next step and is not completed, reject
		if !prevStep.AllowsParallelExecution &&
			prevStep.Status != models.ProceedingStepCompleted &&
			prevStep.Status != models.ProceedingStepSkipped {
			return fmt.Errorf("step %d must be completed before step %d can start (requirement 2: sequential execution)", i, stepOrder)
		}
	}

	return nil
}

// ValidateMinimumSteps validates that proceeding has at least 5 steps (requirement 3)
func (s *ProceedingService) ValidateMinimumSteps(proceedingID uint) error {
	var stepCount int64
	err := s.db.Model(&models.ProceedingStep{}).
		Where("proceeding_id = ?", proceedingID).
		Count(&stepCount).Error
	if err != nil {
		return fmt.Errorf("failed to count steps: %w", err)
	}

	if stepCount < 5 {
		return fmt.Errorf("proceeding must have at least 5 primary steps (requirement 3). Current count: %d", stepCount)
	}

	return nil
}

// ValidatePRPAlignment validates that proceeding has proper PRP alignment (requirement 6)
func (s *ProceedingService) ValidatePRPAlignment(proceeding *models.Proceeding) error {
	if proceeding.LegalAuthority == "" {
		return fmt.Errorf("Legal authority description is required (requirement 6)")
	}

	// Additional validation could include:
	// - Checking if referenced legal sections exist
	// - Validating format of legal section references
	// - Ensuring new legal elements are properly defined

	return nil
}

// CheckExistingDirectives validates that existing directives have been reviewed (requirement 7)
func (s *ProceedingService) CheckExistingDirectives(proceeding *models.Proceeding) error {
	if !proceeding.ExistingDirectivesReviewed {
		return fmt.Errorf("existing directives must be reviewed before proceeding activation (requirement 7)")
	}

	// Additional validation could include:
	// - Verifying that referenced rules/orders/directives exist
	// - Checking that conflict analysis is complete
	// - Ensuring integration plan is adequate

	return nil
}

// ActivateProceeding activates a proceeding after validating all requirements
func (s *ProceedingService) ActivateProceeding(proceedingID uint, userID uint) error {
	// Get proceeding
	var proceeding models.Proceeding
	err := s.db.First(&proceeding, proceedingID).Error
	if err != nil {
		return fmt.Errorf("proceeding not found: %w", err)
	}

	// Validate minimum steps (requirement 3)
	if err := s.ValidateMinimumSteps(proceedingID); err != nil {
		return err
	}

	// Validate PRP alignment (requirement 6)
	if err := s.ValidatePRPAlignment(&proceeding); err != nil {
		return err
	}

	// Check existing directives (requirement 7)
	if err := s.CheckExistingDirectives(&proceeding); err != nil {
		return err
	}

	// Update proceeding status
	err = s.db.Model(&proceeding).Updates(map[string]interface{}{
		"status":            models.ProceedingStatusActive,
		"actual_start_date": time.Now(),
	}).Error
	if err != nil {
		return fmt.Errorf("failed to activate proceeding: %w", err)
	}

	// Create activation log entry
	logEntry := models.ProceedingLog{
		ProceedingID:   proceedingID,
		LogType:        models.LogTypeStatusChange,
		Title:          "Proceeding Activated",
		Content:        fmt.Sprintf("Proceeding '%s' has been activated and is now in progress.", proceeding.Name),
		PreviousStatus: string(models.ProceedingStatusPlanning),
		NewStatus:      string(models.ProceedingStatusActive),
		AuthorID:       userID,
		EventDate:      &proceeding.UpdatedAt,
		IsPublic:       proceeding.IsPublic,
	}

	return s.db.Create(&logEntry).Error
}

// CompleteProceeding completes a proceeding and schedules mandatory review (requirement 5)
func (s *ProceedingService) CompleteProceeding(proceedingID uint, userID uint) error {
	// Get proceeding
	var proceeding models.Proceeding
	err := s.db.First(&proceeding, proceedingID).Error
	if err != nil {
		return fmt.Errorf("proceeding not found: %w", err)
	}

	// Check that all critical steps are completed
	var incompleteCriticalSteps int64
	err = s.db.Model(&models.ProceedingStep{}).
		Where("proceeding_id = ? AND is_critical = ? AND status NOT IN (?)",
			proceedingID, true, []string{"completed", "skipped"}).
		Count(&incompleteCriticalSteps).Error
	if err != nil {
		return fmt.Errorf("failed to check critical steps: %w", err)
	}

	if incompleteCriticalSteps > 0 {
		return fmt.Errorf("all critical steps must be completed before proceeding can be completed")
	}

	now := time.Now()

	// Calculate actual duration
	var actualDuration *int
	if proceeding.ActualStartDate != nil {
		duration := int(now.Sub(*proceeding.ActualStartDate).Hours() / 24)
		actualDuration = &duration
	}

	// Update proceeding status
	updates := map[string]interface{}{
		"status":           models.ProceedingStatusCompleted,
		"actual_end_date":  &now,
		"actual_duration":  actualDuration,
		"progress_percent": 100.0,
	}

	// Schedule mandatory review if required (requirement 5)
	if proceeding.ReviewRequired && !proceeding.ReviewScheduled {
		reviewDate := now.AddDate(0, 0, 30) // Schedule review 30 days after completion
		updates["review_scheduled"] = true
		updates["review_date"] = &reviewDate
	}

	err = s.db.Model(&proceeding).Updates(updates).Error
	if err != nil {
		return fmt.Errorf("failed to complete proceeding: %w", err)
	}

	// Create completion log entry
	logEntry := models.ProceedingLog{
		ProceedingID:   proceedingID,
		LogType:        models.LogTypeMilestone,
		Title:          "Proceeding Completed",
		Content:        fmt.Sprintf("Proceeding '%s' has been completed successfully.", proceeding.Name),
		PreviousStatus: string(proceeding.Status),
		NewStatus:      string(models.ProceedingStatusCompleted),
		AuthorID:       userID,
		EventDate:      &now,
		IsPublic:       proceeding.IsPublic,
	}

	return s.db.Create(&logEntry).Error
}

// TriggerIFRProcess triggers the Interim Final Rule process if required (requirement 6)
func (s *ProceedingService) TriggerIFRProcess(proceedingID uint, userID uint, description string) error {
	// Get proceeding
	var proceeding models.Proceeding
	err := s.db.First(&proceeding, proceedingID).Error
	if err != nil {
		return fmt.Errorf("proceeding not found: %w", err)
	}

	if !proceeding.RequiresIFR {
		return fmt.Errorf("proceeding does not require IFR process")
	}

	if proceeding.IFRTriggered {
		return fmt.Errorf("IFR process already triggered for this proceeding")
	}

	// Update proceeding to mark IFR as triggered
	err = s.db.Model(&proceeding).Updates(map[string]interface{}{
		"ifr_triggered":   true,
		"ifr_description": description,
	}).Error
	if err != nil {
		return fmt.Errorf("failed to trigger IFR process: %w", err)
	}

	// Create IFR trigger log entry
	logEntry := models.ProceedingLog{
		ProceedingID: proceedingID,
		LogType:      models.LogTypeDecision,
		Title:        "IFR Process Triggered",
		Content:      fmt.Sprintf("Interim Final Rule process has been triggered for proceeding '%s'. Description: %s", proceeding.Name, description),
		Decision:     "Trigger IFR Process",
		Rationale:    description,
		AuthorID:     userID,
		EventDate:    &proceeding.UpdatedAt,
		IsPublic:     proceeding.IsPublic,
	}

	return s.db.Create(&logEntry).Error
}

// GetProceedingProgress calculates and returns proceeding progress information
func (s *ProceedingService) GetProceedingProgress(proceedingID uint) (*models.Proceeding, error) {
	var proceeding models.Proceeding
	err := s.db.Preload("ProceedingSteps", func(db *gorm.DB) *gorm.DB {
		return db.Order("step_order ASC")
	}).First(&proceeding, proceedingID).Error
	if err != nil {
		return nil, fmt.Errorf("proceeding not found: %w", err)
	}

	// Calculate progress
	totalSteps := len(proceeding.ProceedingSteps)
	completedSteps := 0
	var currentStepOrder *int

	for _, step := range proceeding.ProceedingSteps {
		if step.Status == models.ProceedingStepCompleted || step.Status == models.ProceedingStepSkipped {
			completedSteps++
		} else if currentStepOrder == nil &&
			(step.Status == models.ProceedingStepNotStarted || step.Status == models.ProceedingStepInProgress) {
			currentStepOrder = &step.StepOrder
		}
	}

	progressPercent := float64(0)
	if totalSteps > 0 {
		progressPercent = (float64(completedSteps) / float64(totalSteps)) * 100
	}

	// Update proceeding with calculated progress
	updates := map[string]interface{}{
		"total_steps":        totalSteps,
		"completed_steps":    completedSteps,
		"progress_percent":   progressPercent,
		"current_step_order": currentStepOrder,
	}

	err = s.db.Model(&proceeding).Updates(updates).Error
	if err != nil {
		return nil, fmt.Errorf("failed to update progress: %w", err)
	}

	// Reload with updated values
	err = s.db.Preload("ProceedingSteps", func(db *gorm.DB) *gorm.DB {
		return db.Order("step_order ASC")
	}).First(&proceeding, proceedingID).Error

	return &proceeding, err
}

// IntegrateWithExistingSystems integrates proceeding with other Administrative Procedure components
func (s *ProceedingService) IntegrateWithExistingSystems(proceedingID uint, integrationData map[string]interface{}) error {
	// This method would handle integration with:
	// - Information Collection systems
	// - Review Report systems
	// - Order management systems
	// - Task Table management
	// - IFR development systems

	// Implement real system integration

	// 1. Information Collection Integration
	if collectionData, exists := integrationData["information_collection"]; exists {
		if err := s.integrateInformationCollection(proceedingID, collectionData); err != nil {
			return fmt.Errorf("failed to integrate information collection: %w", err)
		}
	}

	// 2. Review Report Integration
	if reviewData, exists := integrationData["review_report"]; exists {
		if err := s.integrateReviewReport(proceedingID, reviewData); err != nil {
			return fmt.Errorf("failed to integrate review report: %w", err)
		}
	}

	// 3. Order Management Integration
	if orderData, exists := integrationData["order_management"]; exists {
		if err := s.integrateOrderManagement(proceedingID, orderData); err != nil {
			return fmt.Errorf("failed to integrate order management: %w", err)
		}
	}

	// 4. Task Table Integration
	if taskData, exists := integrationData["task_table"]; exists {
		if err := s.integrateTaskTable(proceedingID, taskData); err != nil {
			return fmt.Errorf("failed to integrate task table: %w", err)
		}
	}

	// 5. IFR Development Integration
	if ifrData, exists := integrationData["ifr_development"]; exists {
		if err := s.integrateIFRDevelopment(proceedingID, ifrData); err != nil {
			return fmt.Errorf("failed to integrate IFR development: %w", err)
		}
	}

	// Store the complete integration data as JSON for audit trail
	integrationJSON, err := json.Marshal(integrationData)
	if err != nil {
		return fmt.Errorf("failed to marshal integration data: %w", err)
	}

	// Update proceeding with integration information
	err = s.db.Model(&models.Proceeding{}).
		Where("id = ?", proceedingID).
		Update("external_refs", string(integrationJSON)).Error
	if err != nil {
		return fmt.Errorf("failed to update integration data: %w", err)
	}

	return nil
}

// integrateInformationCollection integrates with information collection systems
func (s *ProceedingService) integrateInformationCollection(proceedingID uint, data interface{}) error {
	// Create information collection tasks and workflows
	collectionMap, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid information collection data format")
	}

	// Create collection tasks
	if sources, exists := collectionMap["sources"]; exists {
		if sourceList, ok := sources.([]interface{}); ok {
			for _, source := range sourceList {
				if sourceStr, ok := source.(string); ok {
					dueDate := time.Now().AddDate(0, 0, 30)
					task := &models.Task{
						Title:       fmt.Sprintf("Collect information from %s", sourceStr),
						Description: fmt.Sprintf("Gather required information from %s for proceeding %d", sourceStr, proceedingID),
						Type:        models.TaskTypeGeneral,
						Priority:    models.TaskPriorityMedium,
						Status:      models.TaskStatusPending,
						DueDate:     &dueDate,
						SourceType:  "proceeding",
						SourceID:    &proceedingID,
						CreatedByID: 1, // System user
					}
					s.db.Create(task)
				}
			}
		}
	}

	return nil
}

// integrateReviewReport integrates with review report systems
func (s *ProceedingService) integrateReviewReport(proceedingID uint, data interface{}) error {
	// Create review report workflows and assignments
	reviewMap, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid review report data format")
	}

	// Create review tasks
	if reviewers, exists := reviewMap["reviewers"]; exists {
		if reviewerList, ok := reviewers.([]interface{}); ok {
			for _, reviewer := range reviewerList {
				if reviewerStr, ok := reviewer.(string); ok {
					dueDate := time.Now().AddDate(0, 0, 14)
					task := &models.Task{
						Title:       fmt.Sprintf("Review report by %s", reviewerStr),
						Description: fmt.Sprintf("Conduct review of proceeding %d materials", proceedingID),
						Type:        models.TaskTypeReview,
						Priority:    models.TaskPriorityHigh,
						Status:      models.TaskStatusPending,
						DueDate:     &dueDate,
						SourceType:  "proceeding",
						SourceID:    &proceedingID,
						CreatedByID: 1, // System user
					}
					s.db.Create(task)
				}
			}
		}
	}

	return nil
}

// integrateOrderManagement integrates with order management systems
func (s *ProceedingService) integrateOrderManagement(proceedingID uint, data interface{}) error {
	// Create order management workflows
	orderMap, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid order management data format")
	}

	// Create order tracking tasks
	if orderType, exists := orderMap["order_type"]; exists {
		if orderTypeStr, ok := orderType.(string); ok {
			dueDate := time.Now().AddDate(0, 0, 7)
			task := &models.Task{
				Title:       fmt.Sprintf("Manage %s order", orderTypeStr),
				Description: fmt.Sprintf("Handle order management for %s in proceeding %d", orderTypeStr, proceedingID),
				Type:        models.TaskTypeGeneral,
				Priority:    models.TaskPriorityMedium,
				Status:      models.TaskStatusPending,
				DueDate:     &dueDate,
				SourceType:  "proceeding",
				SourceID:    &proceedingID,
				CreatedByID: 1, // System user
			}
			s.db.Create(task)
		}
	}

	return nil
}

// integrateTaskTable integrates with task table management systems
func (s *ProceedingService) integrateTaskTable(proceedingID uint, data interface{}) error {
	// Create task table management workflows
	taskMap, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid task table data format")
	}

	// Create task management workflows
	if tasks, exists := taskMap["tasks"]; exists {
		if taskList, ok := tasks.([]interface{}); ok {
			for _, taskData := range taskList {
				if taskInfo, ok := taskData.(map[string]interface{}); ok {
					title, _ := taskInfo["title"].(string)
					description, _ := taskInfo["description"].(string)

					dueDate := time.Now().AddDate(0, 0, 21)
					task := &models.Task{
						Title:       title,
						Description: description,
						Type:        models.TaskTypeGeneral,
						Priority:    models.TaskPriorityMedium,
						Status:      models.TaskStatusPending,
						DueDate:     &dueDate,
						SourceType:  "proceeding",
						SourceID:    &proceedingID,
						CreatedByID: 1, // System user
					}
					s.db.Create(task)
				}
			}
		}
	}

	return nil
}

// integrateIFRDevelopment integrates with IFR development systems
func (s *ProceedingService) integrateIFRDevelopment(proceedingID uint, data interface{}) error {
	// Create IFR development workflows
	ifrMap, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid IFR development data format")
	}

	// Create IFR development tasks
	if phases, exists := ifrMap["phases"]; exists {
		if phaseList, ok := phases.([]interface{}); ok {
			for i, phase := range phaseList {
				if phaseStr, ok := phase.(string); ok {
					dueDate := time.Now().AddDate(0, 0, 30+(i*14))
					task := &models.Task{
						Title:       fmt.Sprintf("IFR Development Phase: %s", phaseStr),
						Description: fmt.Sprintf("Complete IFR development phase %s for proceeding %d", phaseStr, proceedingID),
						Type:        models.TaskTypeGeneral,
						Priority:    models.TaskPriorityHigh,
						Status:      models.TaskStatusPending,
						DueDate:     &dueDate,
						SourceType:  "proceeding",
						SourceID:    &proceedingID,
						CreatedByID: 1, // System user
					}
					s.db.Create(task)
				}
			}
		}
	}

	return nil
}
