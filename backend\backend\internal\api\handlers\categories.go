package handlers

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// Category handlers

var categorySummaryService *services.SummaryService

// generateSlug creates a URL-friendly slug from a string
func generateSlug(text string) string {
	// Convert to lowercase and replace spaces with hyphens
	slug := strings.ToLower(text)
	slug = strings.ReplaceAll(slug, " ", "-")
	// Remove special characters (keep only alphanumeric and hyphens)
	var result strings.Builder
	for _, r := range slug {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// GetPublicCategories returns public categories
func GetPublicCategories(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Build query for active categories
	query := db.Where("is_active = ?", true)

	// Apply search filter if provided
	if search := c.Query("search"); search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Apply parent filter if provided
	if parentIDStr := c.Query("parent_id"); parentIDStr != "" {
		if parentIDStr == "null" || parentIDStr == "0" {
			query = query.Where("parent_category_id IS NULL")
		} else if parentID, err := strconv.ParseUint(parentIDStr, 10, 32); err == nil {
			query = query.Where("parent_category_id = ?", uint(parentID))
		}
	}

	// Get categories
	var categories []models.Category
	if err := query.Order("sort_order ASC, name ASC").Find(&categories).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch categories",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	categoryResponses := make([]gin.H, len(categories))
	for i, category := range categories {
		// Count documents in this category
		var documentCount int64
		countQuery := db.Table("document_category_assignments").
			Joins("JOIN documents ON document_category_assignments.document_id = documents.id").
			Where("document_category_assignments.category_id = ?", category.ID)

		// Only count published documents for public view
		countQuery = countQuery.Where("documents.status = ?", "published")
		countQuery.Count(&documentCount)

		categoryResponses[i] = gin.H{
			"id":                 category.ID,
			"name":               category.Name,
			"slug":               category.Slug,
			"description":        category.Description,
			"color":              category.Color,
			"icon":               category.Icon,
			"sort_order":         category.SortOrder,
			"parent_category_id": category.ParentCategoryID,
			"document_count":     documentCount,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Categories retrieved successfully",
		Data:    categoryResponses,
	})
}
