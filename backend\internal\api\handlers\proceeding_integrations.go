package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// ProceedingIntegrationRequest represents the request structure for proceeding integrations
type ProceedingIntegrationRequest struct {
	ProceedingID    uint   `json:"proceeding_id" binding:"required"`
	IntegrationType string `json:"integration_type" binding:"required"`
	ExternalID      string `json:"external_id"`
	ExternalSystem  string `json:"external_system"`
	Configuration   string `json:"configuration"`
	IsActive        bool   `json:"is_active"`
}

// GetProceedingIntegrations returns integrations for a proceeding
func GetProceedingIntegrations(c *gin.Context) {
	proceedingID, valid := ValidateID(c, "proceeding_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify proceeding exists
	var proceeding models.Proceeding
	if err := db.First(&proceeding, proceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Get integrations for this proceeding
	var integrations []models.ProceedingIntegration
	if err := db.Where("proceeding_id = ?", proceedingID).
		Order("created_at DESC").
		Find(&integrations).Error; err != nil {
		HandleInternalError(c, "Failed to fetch proceeding integrations: "+err.Error())
		return
	}

	// Convert to response format
	integrationResponses := make([]gin.H, len(integrations))
	for i, integration := range integrations {
		integrationResponses[i] = gin.H{
			"id":               integration.ID,
			"proceeding_id":    integration.ProceedingID,
			"integration_type": integration.IntegrationType,
			"external_id":      integration.ExternalID,
			"external_system":  integration.ExternalSystem,
			"configuration":    integration.Configuration,
			"is_active":        integration.IsActive,
			"created_at":       integration.CreatedAt,
			"updated_at":       integration.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding integrations retrieved successfully",
		Data:    integrationResponses,
	})
}

// CreateProceedingIntegration creates a new proceeding integration
func CreateProceedingIntegration(c *gin.Context) {
	var req ProceedingIntegrationRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify proceeding exists
	var proceeding models.Proceeding
	if err := db.First(&proceeding, req.ProceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid proceeding",
				Message: "Proceeding not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Create proceeding integration
	integration := &models.ProceedingIntegration{
		ProceedingID:    req.ProceedingID,
		IntegrationType: req.IntegrationType,
		ExternalID:      req.ExternalID,
		ExternalSystem:  req.ExternalSystem,
		Configuration:   req.Configuration,
		IsActive:        req.IsActive,
	}

	if err := db.Create(integration).Error; err != nil {
		HandleInternalError(c, "Failed to create proceeding integration: "+err.Error())
		return
	}

	response := gin.H{
		"id":               integration.ID,
		"proceeding_id":    integration.ProceedingID,
		"integration_type": integration.IntegrationType,
		"external_id":      integration.ExternalID,
		"external_system":  integration.ExternalSystem,
		"configuration":    integration.Configuration,
		"is_active":        integration.IsActive,
		"created_at":       integration.CreatedAt,
		"updated_at":       integration.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Proceeding integration created successfully",
		Data:    response,
	})
}

// UpdateProceedingIntegration updates an existing proceeding integration
func UpdateProceedingIntegration(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req ProceedingIntegrationRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing proceeding integration
	var integration models.ProceedingIntegration
	if err := db.First(&integration, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding integration")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding integration: "+err.Error())
		return
	}

	// Update proceeding integration fields
	integration.IntegrationType = req.IntegrationType
	integration.ExternalID = req.ExternalID
	integration.ExternalSystem = req.ExternalSystem
	integration.Configuration = req.Configuration
	integration.IsActive = req.IsActive

	if err := db.Save(&integration).Error; err != nil {
		HandleInternalError(c, "Failed to update proceeding integration: "+err.Error())
		return
	}

	response := gin.H{
		"id":               integration.ID,
		"proceeding_id":    integration.ProceedingID,
		"integration_type": integration.IntegrationType,
		"external_id":      integration.ExternalID,
		"external_system":  integration.ExternalSystem,
		"configuration":    integration.Configuration,
		"is_active":        integration.IsActive,
		"created_at":       integration.CreatedAt,
		"updated_at":       integration.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding integration updated successfully",
		Data:    response,
	})
}

// DeleteProceedingIntegration deletes a proceeding integration
func DeleteProceedingIntegration(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if proceeding integration exists
	var integration models.ProceedingIntegration
	if err := db.First(&integration, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding integration")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding integration: "+err.Error())
		return
	}

	// Delete proceeding integration
	if err := db.Delete(&integration).Error; err != nil {
		HandleInternalError(c, "Failed to delete proceeding integration: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding integration deleted successfully",
	})
}

// SyncProceedingIntegration synchronizes data with external system
func SyncProceedingIntegration(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get proceeding integration
	var integration models.ProceedingIntegration
	if err := db.First(&integration, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding integration")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding integration: "+err.Error())
		return
	}

	// Implement actual synchronization logic based on integration type
	syncResult, syncError := performSynchronization(db, &integration)

	// Update integration with sync results
	now := time.Now()
	updates := map[string]interface{}{
		"last_sync_at": &now,
	}

	if syncError != nil {
		updates["sync_status"] = "failed"
		updates["last_error"] = syncError.Error()
	} else {
		updates["sync_status"] = "completed"
		updates["last_error"] = ""
	}

	if err := db.Model(&integration).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update integration sync status: "+err.Error())
		return
	}

	// Return results
	responseData := gin.H{
		"integration_id":   integration.ID,
		"integration_type": integration.IntegrationType,
		"sync_status":      updates["sync_status"],
		"sync_time":        now,
	}

	if syncError != nil {
		responseData["error"] = syncError.Error()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Synchronization failed",
			Message: syncError.Error(),
		})
		return
	}

	if syncResult != nil {
		responseData["sync_result"] = syncResult
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding integration synchronized successfully",
		Data:    responseData,
	})
}

// performSynchronization performs the actual synchronization based on integration type
func performSynchronization(db *gorm.DB, integration *models.ProceedingIntegration) (interface{}, error) {
	switch integration.IntegrationType {
	case "federal_register":
		return syncWithFederalRegister(db, integration)
	case "regulations_gov":
		return syncWithRegulationsGov(db, integration)
	case "court_system":
		return syncWithCourtSystem(db, integration)
	case "agency_system":
		return syncWithAgencySystem(db, integration)
	case "external_api":
		return syncWithExternalAPI(db, integration)
	default:
		return nil, fmt.Errorf("unsupported integration type: %s", integration.IntegrationType)
	}
}

// syncWithFederalRegister synchronizes with Federal Register
func syncWithFederalRegister(db *gorm.DB, integration *models.ProceedingIntegration) (interface{}, error) {
	// Parse configuration for API credentials and settings
	var config struct {
		APIKey    string `json:"api_key"`
		BaseURL   string `json:"base_url"`
		AgencyID  string `json:"agency_id"`
		DateRange struct {
			From string `json:"from"`
			To   string `json:"to"`
		} `json:"date_range"`
	}

	if integration.Configuration != "" {
		if err := json.Unmarshal([]byte(integration.Configuration), &config); err != nil {
			return nil, fmt.Errorf("failed to parse configuration: %w", err)
		}
	}

	// Set defaults if not configured
	if config.BaseURL == "" {
		config.BaseURL = "https://www.federalregister.gov/api/v1"
	}
	if config.DateRange.From == "" {
		config.DateRange.From = time.Now().AddDate(0, 0, -30).Format("2006-01-02")
	}
	if config.DateRange.To == "" {
		config.DateRange.To = time.Now().Format("2006-01-02")
	}

	// Build API request URL
	url := fmt.Sprintf("%s/documents.json?per_page=1000&publication_date[gte]=%s&publication_date[lte]=%s",
		config.BaseURL, config.DateRange.From, config.DateRange.To)

	if config.AgencyID != "" {
		url += "&agencies[]=" + config.AgencyID
	}

	// Make HTTP request to Federal Register API
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add API key if provided
	if config.APIKey != "" {
		req.Header.Set("X-API-Key", config.APIKey)
	}
	req.Header.Set("User-Agent", "NoteControl/1.0")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var apiResponse struct {
		Results []struct {
			DocumentNumber  string   `json:"document_number"`
			Title           string   `json:"title"`
			Type            string   `json:"type"`
			AgencyNames     []string `json:"agency_names"`
			PublicationDate string   `json:"publication_date"`
			HTMLURL         string   `json:"html_url"`
			PDFURL          string   `json:"pdf_url"`
		} `json:"results"`
		Count int `json:"count"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Process and store documents
	documentsCount := 0
	rulesCount := 0
	noticesCount := 0

	for _, doc := range apiResponse.Results {
		// Create document record in our system
		document := models.Document{
			Title:    doc.Title,
			Slug:     strings.ToLower(strings.ReplaceAll(doc.DocumentNumber, " ", "-")),
			Abstract: fmt.Sprintf("Imported from Federal Register - %s", doc.Type),
			Content: fmt.Sprintf("Federal Register Document: %s\nType: %s\nAgencies: %s\nPublication Date: %s\nURL: %s",
				doc.DocumentNumber, doc.Type, strings.Join(doc.AgencyNames, ", "), doc.PublicationDate, doc.HTMLURL),
			Type:        models.DocumentTypeRule, // Default to rule type
			Status:      models.StatusPublished,
			IsPublic:    true,
			AgencyID:    1, // Default agency
			CreatedByID: 1, // System user
		}

		if err := db.Create(&document).Error; err != nil {
			// Log error but continue processing
			fmt.Printf("Failed to create document %s: %v\n", doc.DocumentNumber, err)
			continue
		}

		// Link to proceeding using many-to-many relationship
		var proceeding models.Proceeding
		if err := db.First(&proceeding, integration.ProceedingID).Error; err == nil {
			// Add document to proceeding's related documents
			db.Model(&proceeding).Association("RelatedDocs").Append(&document)
		}

		documentsCount++
		switch strings.ToLower(doc.Type) {
		case "rule":
			rulesCount++
		case "notice":
			noticesCount++
		}
	}

	// Update integration metadata with sync time
	metadata := map[string]interface{}{
		"last_sync_at":    time.Now(),
		"last_sync_count": documentsCount,
	}
	metadataJSON, _ := json.Marshal(metadata)
	integration.Metadata = string(metadataJSON)
	db.Save(integration)

	result := gin.H{
		"documents_synced": documentsCount,
		"rules_synced":     rulesCount,
		"notices_synced":   noticesCount,
		"total_available":  apiResponse.Count,
		"last_sync_date":   time.Now(),
		"source":           "Federal Register API",
	}

	return result, nil
}

// syncWithRegulationsGov synchronizes with Regulations.gov
func syncWithRegulationsGov(db *gorm.DB, integration *models.ProceedingIntegration) (interface{}, error) {
	// Parse configuration for API credentials and settings
	var config struct {
		APIKey     string `json:"api_key"`
		BaseURL    string `json:"base_url"`
		DocketID   string `json:"docket_id"`
		SearchTerm string `json:"search_term"`
		DateRange  struct {
			From string `json:"from"`
			To   string `json:"to"`
		} `json:"date_range"`
	}

	if integration.Configuration != "" {
		if err := json.Unmarshal([]byte(integration.Configuration), &config); err != nil {
			return nil, fmt.Errorf("failed to parse configuration: %w", err)
		}
	}

	// Set defaults if not configured
	if config.BaseURL == "" {
		config.BaseURL = "https://api.regulations.gov/v4"
	}
	if config.APIKey == "" {
		return nil, fmt.Errorf("API key is required for Regulations.gov integration")
	}

	// Build API request URL for documents
	url := fmt.Sprintf("%s/documents?api_key=%s&page[size]=250", config.BaseURL, config.APIKey)

	if config.DocketID != "" {
		url += "&filter[docketId]=" + config.DocketID
	}
	if config.SearchTerm != "" {
		url += "&filter[searchTerm]=" + config.SearchTerm
	}
	if config.DateRange.From != "" {
		url += "&filter[postedDate][ge]=" + config.DateRange.From
	}
	if config.DateRange.To != "" {
		url += "&filter[postedDate][le]=" + config.DateRange.To
	}

	// Make HTTP request to Regulations.gov API
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("User-Agent", "NoteControl/1.0")
	req.Header.Set("Accept", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var apiResponse struct {
		Data []struct {
			ID         string `json:"id"`
			Type       string `json:"type"`
			Attributes struct {
				Title        string `json:"title"`
				DocumentType string `json:"documentType"`
				DocketId     string `json:"docketId"`
				PostedDate   string `json:"postedDate"`
				Summary      string `json:"summary"`
				AgencyId     string `json:"agencyId"`
			} `json:"attributes"`
			Links struct {
				Self string `json:"self"`
			} `json:"links"`
		} `json:"data"`
		Meta struct {
			TotalElements int `json:"totalElements"`
		} `json:"meta"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Process and store documents
	documentsCount := 0
	commentsCount := 0
	docketsCount := 0
	docketMap := make(map[string]bool)

	for _, doc := range apiResponse.Data {
		// Create document record in our system
		document := models.Document{
			Title:    doc.Attributes.Title,
			Slug:     strings.ToLower(strings.ReplaceAll(doc.ID, " ", "-")),
			Abstract: doc.Attributes.Summary,
			Content: fmt.Sprintf("Regulations.gov Document: %s\nType: %s\nDocket: %s\nPosted: %s\nAgency: %s\nURL: %s",
				doc.ID, doc.Attributes.DocumentType, doc.Attributes.DocketId, doc.Attributes.PostedDate, doc.Attributes.AgencyId, doc.Links.Self),
			Type:        models.DocumentTypeNotice, // Default to notice type
			Status:      models.StatusPublished,
			IsPublic:    true,
			AgencyID:    1, // Default agency
			CreatedByID: 1, // System user
		}

		if err := db.Create(&document).Error; err != nil {
			// Log error but continue processing
			fmt.Printf("Failed to create document %s: %v\n", doc.ID, err)
			continue
		}

		// Link to proceeding using many-to-many relationship
		var proceeding models.Proceeding
		if err := db.First(&proceeding, integration.ProceedingID).Error; err == nil {
			// Add document to proceeding's related documents
			db.Model(&proceeding).Association("RelatedDocs").Append(&document)
		}

		documentsCount++

		// Count unique dockets
		if doc.Attributes.DocketId != "" && !docketMap[doc.Attributes.DocketId] {
			docketMap[doc.Attributes.DocketId] = true
			docketsCount++
		}

		// Count comments (documents with type "Public Submission")
		if strings.Contains(strings.ToLower(doc.Attributes.DocumentType), "comment") ||
			strings.Contains(strings.ToLower(doc.Attributes.DocumentType), "submission") {
			commentsCount++
		}
	}

	// Update integration metadata with sync time
	metadata := map[string]interface{}{
		"last_sync_at":    time.Now(),
		"last_sync_count": documentsCount,
		"dockets_found":   docketsCount,
	}
	metadataJSON, _ := json.Marshal(metadata)
	integration.Metadata = string(metadataJSON)
	db.Save(integration)

	result := gin.H{
		"documents_synced": documentsCount,
		"comments_synced":  commentsCount,
		"dockets_synced":   docketsCount,
		"total_available":  apiResponse.Meta.TotalElements,
		"last_sync_date":   time.Now(),
		"source":           "Regulations.gov API",
	}

	return result, nil
}

// syncWithCourtSystem synchronizes with court systems
func syncWithCourtSystem(db *gorm.DB, integration *models.ProceedingIntegration) (interface{}, error) {
	// Parse configuration for court system credentials and settings
	var config struct {
		APIKey     string `json:"api_key"`
		BaseURL    string `json:"base_url"`
		CourtID    string `json:"court_id"`
		CaseNumber string `json:"case_number"`
		DateRange  struct {
			From string `json:"from"`
			To   string `json:"to"`
		} `json:"date_range"`
		AuthToken string `json:"auth_token"`
	}

	if integration.Configuration != "" {
		if err := json.Unmarshal([]byte(integration.Configuration), &config); err != nil {
			return nil, fmt.Errorf("failed to parse configuration: %w", err)
		}
	}

	// Set defaults if not configured
	if config.BaseURL == "" {
		config.BaseURL = "https://api.courtrecords.gov/v1" // Example court API
	}

	// Build API request URL for court filings
	url := fmt.Sprintf("%s/filings?limit=100", config.BaseURL)

	if config.CourtID != "" {
		url += "&court_id=" + config.CourtID
	}
	if config.CaseNumber != "" {
		url += "&case_number=" + config.CaseNumber
	}
	if config.DateRange.From != "" {
		url += "&filed_date_from=" + config.DateRange.From
	}
	if config.DateRange.To != "" {
		url += "&filed_date_to=" + config.DateRange.To
	}

	// Make HTTP request to Court System API
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authentication headers
	if config.APIKey != "" {
		req.Header.Set("X-API-Key", config.APIKey)
	}
	if config.AuthToken != "" {
		req.Header.Set("Authorization", "Bearer "+config.AuthToken)
	}
	req.Header.Set("User-Agent", "NoteControl/1.0")
	req.Header.Set("Accept", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var apiResponse struct {
		Filings []struct {
			ID          string `json:"id"`
			CaseNumber  string `json:"case_number"`
			Title       string `json:"title"`
			FilingType  string `json:"filing_type"`
			FiledDate   string `json:"filed_date"`
			CourtName   string `json:"court_name"`
			Description string `json:"description"`
			DocumentURL string `json:"document_url"`
		} `json:"filings"`
		Total int `json:"total"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Process and store court documents
	documentsCount := 0
	casesCount := 0
	ordersCount := 0
	caseMap := make(map[string]bool)

	for _, filing := range apiResponse.Filings {
		// Create document record in our system
		document := models.Document{
			Title:    filing.Title,
			Slug:     strings.ToLower(strings.ReplaceAll(filing.ID, " ", "-")),
			Abstract: fmt.Sprintf("Court Filing - %s", filing.FilingType),
			Content: fmt.Sprintf("Court Filing: %s\nCase: %s\nType: %s\nCourt: %s\nFiled: %s\nDescription: %s\nURL: %s",
				filing.ID, filing.CaseNumber, filing.FilingType, filing.CourtName, filing.FiledDate, filing.Description, filing.DocumentURL),
			Type:        models.DocumentTypeOther, // Court documents as other type
			Status:      models.StatusPublished,
			IsPublic:    true,
			AgencyID:    1, // Default agency
			CreatedByID: 1, // System user
		}

		if err := db.Create(&document).Error; err != nil {
			// Log error but continue processing
			fmt.Printf("Failed to create court document %s: %v\n", filing.ID, err)
			continue
		}

		// Link to proceeding using many-to-many relationship
		var proceeding models.Proceeding
		if err := db.First(&proceeding, integration.ProceedingID).Error; err == nil {
			// Add document to proceeding's related documents
			db.Model(&proceeding).Association("RelatedDocs").Append(&document)
		}

		documentsCount++

		// Count unique cases
		if filing.CaseNumber != "" && !caseMap[filing.CaseNumber] {
			caseMap[filing.CaseNumber] = true
			casesCount++
		}

		// Count orders (documents with type containing "order")
		if strings.Contains(strings.ToLower(filing.FilingType), "order") {
			ordersCount++
		}
	}

	// Update integration metadata with sync time
	metadata := map[string]interface{}{
		"last_sync_at":    time.Now(),
		"last_sync_count": documentsCount,
		"cases_found":     casesCount,
	}
	metadataJSON, _ := json.Marshal(metadata)
	integration.Metadata = string(metadataJSON)
	db.Save(integration)

	result := gin.H{
		"documents_synced": documentsCount,
		"cases_synced":     casesCount,
		"filings_synced":   documentsCount, // All documents are filings
		"orders_synced":    ordersCount,
		"total_available":  apiResponse.Total,
		"last_sync_date":   time.Now(),
		"source":           "Court System API",
	}

	return result, nil
}

// syncWithAgencySystem synchronizes with agency systems
func syncWithAgencySystem(db *gorm.DB, integration *models.ProceedingIntegration) (interface{}, error) {
	// Parse configuration for agency system credentials and settings
	var config struct {
		APIKey     string `json:"api_key"`
		BaseURL    string `json:"base_url"`
		AgencyCode string `json:"agency_code"`
		SystemType string `json:"system_type"` // "records", "reports", "updates"
		DateRange  struct {
			From string `json:"from"`
			To   string `json:"to"`
		} `json:"date_range"`
		AuthToken string `json:"auth_token"`
	}

	if integration.Configuration != "" {
		if err := json.Unmarshal([]byte(integration.Configuration), &config); err != nil {
			return nil, fmt.Errorf("failed to parse configuration: %w", err)
		}
	}

	// Set defaults if not configured
	if config.BaseURL == "" {
		config.BaseURL = "https://api.agency.gov/v1" // Example agency API
	}
	if config.SystemType == "" {
		config.SystemType = "records"
	}

	// Build API request URL based on system type
	var url string
	switch config.SystemType {
	case "records":
		url = fmt.Sprintf("%s/records?limit=100", config.BaseURL)
	case "reports":
		url = fmt.Sprintf("%s/reports?limit=100", config.BaseURL)
	case "updates":
		url = fmt.Sprintf("%s/updates?limit=100", config.BaseURL)
	default:
		url = fmt.Sprintf("%s/data?limit=100", config.BaseURL)
	}

	if config.AgencyCode != "" {
		url += "&agency=" + config.AgencyCode
	}
	if config.DateRange.From != "" {
		url += "&date_from=" + config.DateRange.From
	}
	if config.DateRange.To != "" {
		url += "&date_to=" + config.DateRange.To
	}

	// Make HTTP request to Agency System API
	client := &http.Client{Timeout: 30 * time.Second}
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authentication headers
	if config.APIKey != "" {
		req.Header.Set("X-API-Key", config.APIKey)
	}
	if config.AuthToken != "" {
		req.Header.Set("Authorization", "Bearer "+config.AuthToken)
	}
	req.Header.Set("User-Agent", "NoteControl/1.0")
	req.Header.Set("Accept", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response (generic structure for agency data)
	var apiResponse struct {
		Data []struct {
			ID          string `json:"id"`
			Title       string `json:"title"`
			Type        string `json:"type"`
			Description string `json:"description"`
			CreatedDate string `json:"created_date"`
			AgencyCode  string `json:"agency_code"`
			Status      string `json:"status"`
			URL         string `json:"url"`
		} `json:"data"`
		Total int `json:"total"`
	}

	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Process and store agency documents
	documentsCount := 0
	recordsCount := 0
	reportsCount := 0
	updatesCount := 0

	for _, item := range apiResponse.Data {
		// Create document record in our system
		document := models.Document{
			Title:    item.Title,
			Slug:     strings.ToLower(strings.ReplaceAll(item.ID, " ", "-")),
			Abstract: fmt.Sprintf("Agency %s - %s", strings.Title(item.Type), item.Description),
			Content: fmt.Sprintf("Agency Data: %s\nType: %s\nAgency: %s\nCreated: %s\nStatus: %s\nDescription: %s\nURL: %s",
				item.ID, item.Type, item.AgencyCode, item.CreatedDate, item.Status, item.Description, item.URL),
			Type:        models.DocumentTypeOther, // Agency documents as other type
			Status:      models.StatusPublished,
			IsPublic:    true,
			AgencyID:    1, // Default agency
			CreatedByID: 1, // System user
		}

		if err := db.Create(&document).Error; err != nil {
			// Log error but continue processing
			fmt.Printf("Failed to create agency document %s: %v\n", item.ID, err)
			continue
		}

		// Link to proceeding using many-to-many relationship
		var proceeding models.Proceeding
		if err := db.First(&proceeding, integration.ProceedingID).Error; err == nil {
			// Add document to proceeding's related documents
			db.Model(&proceeding).Association("RelatedDocs").Append(&document)
		}

		documentsCount++

		// Count by type
		switch strings.ToLower(item.Type) {
		case "record":
			recordsCount++
		case "report":
			reportsCount++
		case "update":
			updatesCount++
		}
	}

	// Update integration metadata with sync time
	metadata := map[string]interface{}{
		"last_sync_at":    time.Now(),
		"last_sync_count": documentsCount,
		"system_type":     config.SystemType,
	}
	metadataJSON, _ := json.Marshal(metadata)
	integration.Metadata = string(metadataJSON)
	db.Save(integration)

	result := gin.H{
		"documents_synced": documentsCount,
		"records_synced":   recordsCount,
		"reports_synced":   reportsCount,
		"updates_synced":   updatesCount,
		"total_available":  apiResponse.Total,
		"last_sync_date":   time.Now(),
		"source":           "Agency System API",
	}

	return result, nil
}

// syncWithExternalAPI synchronizes with external APIs
func syncWithExternalAPI(db *gorm.DB, integration *models.ProceedingIntegration) (interface{}, error) {
	// Parse configuration for external API credentials and settings
	var config struct {
		APIKey      string            `json:"api_key"`
		BaseURL     string            `json:"base_url"`
		Endpoint    string            `json:"endpoint"`
		Method      string            `json:"method"`
		Headers     map[string]string `json:"headers"`
		QueryParams map[string]string `json:"query_params"`
		AuthToken   string            `json:"auth_token"`
		AuthType    string            `json:"auth_type"` // "bearer", "basic", "api_key"
	}

	if integration.Configuration != "" {
		if err := json.Unmarshal([]byte(integration.Configuration), &config); err != nil {
			return nil, fmt.Errorf("failed to parse configuration: %w", err)
		}
	}

	// Validate required configuration
	if config.BaseURL == "" {
		return nil, fmt.Errorf("base_url is required for external API integration")
	}
	if config.Endpoint == "" {
		config.Endpoint = "/data"
	}
	if config.Method == "" {
		config.Method = "GET"
	}

	// Build API request URL
	url := config.BaseURL + config.Endpoint
	if len(config.QueryParams) > 0 {
		params := make([]string, 0, len(config.QueryParams))
		for key, value := range config.QueryParams {
			params = append(params, fmt.Sprintf("%s=%s", key, value))
		}
		url += "?" + strings.Join(params, "&")
	}

	// Create HTTP request
	var reqBody io.Reader
	if config.Method == "POST" || config.Method == "PUT" {
		// For POST/PUT requests, you might want to include a request body
		// This is a basic implementation - extend as needed
		reqBody = bytes.NewReader([]byte("{}"))
	}

	req, err := http.NewRequest(config.Method, url, reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Add authentication
	switch config.AuthType {
	case "bearer":
		if config.AuthToken != "" {
			req.Header.Set("Authorization", "Bearer "+config.AuthToken)
		}
	case "basic":
		if config.AuthToken != "" {
			req.Header.Set("Authorization", "Basic "+config.AuthToken)
		}
	case "api_key":
		if config.APIKey != "" {
			req.Header.Set("X-API-Key", config.APIKey)
		}
	default:
		if config.APIKey != "" {
			req.Header.Set("X-API-Key", config.APIKey)
		}
		if config.AuthToken != "" {
			req.Header.Set("Authorization", "Bearer "+config.AuthToken)
		}
	}

	// Add custom headers
	for key, value := range config.Headers {
		req.Header.Set(key, value)
	}

	// Set default headers
	req.Header.Set("User-Agent", "NoteControl/1.0")
	req.Header.Set("Accept", "application/json")
	if config.Method == "POST" || config.Method == "PUT" {
		req.Header.Set("Content-Type", "application/json")
	}

	// Make HTTP request
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response (flexible structure for external APIs)
	var apiResponse map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&apiResponse); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Process response data
	dataPointsCount := 0
	entitiesCount := 0
	relationshipsCount := 0

	// Try to extract data from common response structures
	if data, ok := apiResponse["data"].([]interface{}); ok {
		dataPointsCount = len(data)
		entitiesCount = dataPointsCount // Assume each data point is an entity
	} else if items, ok := apiResponse["items"].([]interface{}); ok {
		dataPointsCount = len(items)
		entitiesCount = dataPointsCount
	} else if results, ok := apiResponse["results"].([]interface{}); ok {
		dataPointsCount = len(results)
		entitiesCount = dataPointsCount
	} else {
		// Count top-level keys as data points
		dataPointsCount = len(apiResponse)
		entitiesCount = dataPointsCount
	}

	// Look for relationships in the response
	if relationships, ok := apiResponse["relationships"].([]interface{}); ok {
		relationshipsCount = len(relationships)
	} else if links, ok := apiResponse["links"].([]interface{}); ok {
		relationshipsCount = len(links)
	}

	// Store the raw response as a document for reference
	responseJSON, _ := json.Marshal(apiResponse)
	document := models.Document{
		Title:       fmt.Sprintf("External API Response - %s", integration.ExternalSystem),
		Slug:        fmt.Sprintf("external-api-%d-%d", integration.ID, time.Now().Unix()),
		Abstract:    fmt.Sprintf("Data from external API: %s", config.BaseURL),
		Content:     string(responseJSON),
		Type:        models.DocumentTypeOther,
		Status:      models.StatusPublished,
		IsPublic:    false, // External API data might be sensitive
		AgencyID:    1,     // Default agency
		CreatedByID: 1,     // System user
	}

	if err := db.Create(&document).Error; err == nil {
		// Link to proceeding using many-to-many relationship
		var proceeding models.Proceeding
		if err := db.First(&proceeding, integration.ProceedingID).Error; err == nil {
			// Add document to proceeding's related documents
			db.Model(&proceeding).Association("RelatedDocs").Append(&document)
		}
	}

	// Update integration metadata with sync time
	metadata := map[string]interface{}{
		"last_sync_at":       time.Now(),
		"last_response_size": len(responseJSON),
		"endpoint":           config.BaseURL + config.Endpoint,
	}
	metadataJSON, _ := json.Marshal(metadata)
	integration.Metadata = string(metadataJSON)
	db.Save(integration)

	result := gin.H{
		"data_points_synced":   dataPointsCount,
		"entities_synced":      entitiesCount,
		"relationships_synced": relationshipsCount,
		"response_size":        len(responseJSON),
		"last_sync_date":       time.Now(),
		"source":               "External API",
		"endpoint":             config.BaseURL + config.Endpoint,
	}

	return result, nil
}
