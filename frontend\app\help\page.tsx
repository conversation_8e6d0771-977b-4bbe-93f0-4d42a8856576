'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import {
  QuestionMarkCircleIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon,
  BookOpenIcon,
  UserGroupIcon,
  CogIcon,
  ShieldCheckIcon,
  VideoCameraIcon,
  ChatBubbleLeftRightIcon,
  PhoneIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';

interface HelpCategory {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  articles: HelpArticle[];
}

interface HelpArticle {
  id: string;
  title: string;
  description: string;
  content: string;
  tags: string[];
  lastUpdated: string;
}

const HelpPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedArticle, setSelectedArticle] = useState<HelpArticle | null>(null);

  const helpCategories: HelpCategory[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Learn the basics of using the document management system',
      icon: BookOpenIcon,
      articles: [
        {
          id: 'first-login',
          title: 'Your First Login',
          description: 'How to log in and set up your account',
          content: 'Step-by-step guide for first-time users...',
          tags: ['login', 'account', 'setup'],
          lastUpdated: '2025-01-01'
        },
        {
          id: 'dashboard-overview',
          title: 'Dashboard Overview',
          description: 'Understanding your dashboard and navigation',
          content: 'Overview of the main dashboard features...',
          tags: ['dashboard', 'navigation', 'overview'],
          lastUpdated: '2025-01-01'
        }
      ]
    },
    {
      id: 'documents',
      title: 'Document Management',
      description: 'Creating, editing, and managing documents',
      icon: DocumentTextIcon,
      articles: [
        {
          id: 'create-document',
          title: 'Creating Documents',
          description: 'How to create and format new documents',
          content: 'Guide to creating documents...',
          tags: ['documents', 'create', 'format'],
          lastUpdated: '2025-01-01'
        },
        {
          id: 'document-workflow',
          title: 'Document Workflow',
          description: 'Understanding the document approval process',
          content: 'Document workflow explanation...',
          tags: ['workflow', 'approval', 'process'],
          lastUpdated: '2025-01-01'
        }
      ]
    },
    {
      id: 'regulations',
      title: 'Regulations',
      description: 'Working with federal regulations and compliance',
      icon: ShieldCheckIcon,
      articles: [
        {
          id: 'regulation-hierarchy',
          title: 'Understanding Regulation Hierarchy',
          description: 'How regulations are organized and structured',
          content: 'Regulation hierarchy explanation...',
          tags: ['regulations', 'hierarchy', 'structure'],
          lastUpdated: '2025-01-01'
        }
      ]
    },
    {
      id: 'collaboration',
      title: 'Collaboration',
      description: 'Working with teams and sharing documents',
      icon: UserGroupIcon,
      articles: [
        {
          id: 'sharing-documents',
          title: 'Sharing Documents',
          description: 'How to share documents with colleagues',
          content: 'Document sharing guide...',
          tags: ['sharing', 'collaboration', 'permissions'],
          lastUpdated: '2025-01-01'
        }
      ]
    },
    {
      id: 'settings',
      title: 'Settings & Preferences',
      description: 'Customizing your experience',
      icon: CogIcon,
      articles: [
        {
          id: 'notification-settings',
          title: 'Notification Settings',
          description: 'Managing your notification preferences',
          content: 'Notification settings guide...',
          tags: ['notifications', 'settings', 'preferences'],
          lastUpdated: '2025-01-01'
        }
      ]
    }
  ];

  const filteredCategories = helpCategories.filter(category =>
    searchTerm === '' ||
    category.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.articles.some(article =>
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    )
  );

  const allArticles = helpCategories.flatMap(category => category.articles);
  const filteredArticles = allArticles.filter(article =>
    searchTerm === '' ||
    article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    article.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (selectedArticle) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="max-w-4xl mx-auto">
            <button
              onClick={() => setSelectedArticle(null)}
              className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-6"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Help Center
            </button>

            <article className="bg-white rounded-lg shadow-md p-8">
              <header className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">{selectedArticle.title}</h1>
                <p className="text-lg text-gray-600 mb-4">{selectedArticle.description}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  <span>Last updated: {new Date(selectedArticle.lastUpdated).toLocaleDateString()}</span>
                  <div className="flex space-x-2">
                    {selectedArticle.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </header>

              <div className="prose prose-lg max-w-none">
                <p>{selectedArticle.content}</p>
                {/* In a real implementation, this would be rich content */}
              </div>

              <footer className="mt-8 pt-8 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Was this helpful?</h3>
                    <div className="flex space-x-2">
                      <button className="px-4 py-2 bg-green-100 text-green-800 rounded-md hover:bg-green-200">
                        Yes
                      </button>
                      <button className="px-4 py-2 bg-red-100 text-red-800 rounded-md hover:bg-red-200">
                        No
                      </button>
                    </div>
                  </div>
                  <Link
                    href="/contact"
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <ChatBubbleLeftRightIcon className="h-4 w-4 mr-2" />
                    Contact Support
                  </Link>
                </div>
              </footer>
            </article>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-primary-100 mb-6">
            <QuestionMarkCircleIcon className="h-8 w-8 text-primary-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Help Center</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Find answers to your questions and learn how to make the most of the document management system.
          </p>
        </div>

        {/* Search */}
        <div className="max-w-2xl mx-auto mb-12">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Search for help articles..."
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Link
            href="/contact"
            className="flex items-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <ChatBubbleLeftRightIcon className="h-8 w-8 text-primary-600 mr-4" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Contact Support</h3>
              <p className="text-gray-600">Get help from our support team</p>
            </div>
          </Link>

          <a
            href="tel:**************"
            className="flex items-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
          >
            <PhoneIcon className="h-8 w-8 text-primary-600 mr-4" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Call Support</h3>
              <p className="text-gray-600">1-800-NOTECONTROL</p>
            </div>
          </a>

          <button className="flex items-center p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <VideoCameraIcon className="h-8 w-8 text-primary-600 mr-4" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">Video Tutorials</h3>
              <p className="text-gray-600">Watch step-by-step guides</p>
            </div>
          </button>
        </div>

        {/* Search Results or Categories */}
        {searchTerm ? (
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Search Results ({filteredArticles.length})
            </h2>
            <div className="space-y-4">
              {filteredArticles.map(article => (
                <button
                  key={article.id}
                  onClick={() => setSelectedArticle(article)}
                  className="w-full text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
                >
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{article.title}</h3>
                  <p className="text-gray-600 mb-3">{article.description}</p>
                  <div className="flex space-x-2">
                    {article.tags.map(tag => (
                      <span key={tag} className="px-2 py-1 bg-gray-100 rounded-full text-xs text-gray-600">
                        {tag}
                      </span>
                    ))}
                  </div>
                </button>
              ))}
            </div>
          </div>
        ) : (
          <div className="max-w-6xl mx-auto">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">Browse by Category</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {filteredCategories.map(category => (
                <div key={category.id} className="bg-white rounded-lg shadow-md p-6">
                  <div className="flex items-center mb-4">
                    <div className="h-12 w-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                      <category.icon className="h-6 w-6 text-primary-600" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{category.title}</h3>
                      <p className="text-sm text-gray-600">{category.description}</p>
                    </div>
                  </div>
                  
                  <div className="space-y-3">
                    {category.articles.slice(0, 3).map(article => (
                      <button
                        key={article.id}
                        onClick={() => setSelectedArticle(article)}
                        className="block w-full text-left p-3 rounded-md hover:bg-gray-50 transition-colors"
                      >
                        <h4 className="text-sm font-medium text-gray-900">{article.title}</h4>
                        <p className="text-xs text-gray-600 mt-1">{article.description}</p>
                      </button>
                    ))}
                    
                    {category.articles.length > 3 && (
                      <button
                        onClick={() => setSelectedCategory(category.id)}
                        className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                      >
                        View all {category.articles.length} articles →
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Popular Articles */}
        <div className="max-w-4xl mx-auto mt-16">
          <h2 className="text-2xl font-bold text-gray-900 mb-8">Popular Articles</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {allArticles.slice(0, 4).map(article => (
              <button
                key={article.id}
                onClick={() => setSelectedArticle(article)}
                className="text-left p-6 bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow"
              >
                <h3 className="text-lg font-medium text-gray-900 mb-2">{article.title}</h3>
                <p className="text-gray-600">{article.description}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-16">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Still need help?</h3>
          <div className="space-x-4">
            <Link
              href="/contact"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              Contact Support
            </Link>
            <Link
              href="/status"
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              System Status
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default HelpPage;
