'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Check<PERSON>ircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ClockIcon,
  ServerIcon,
  CloudIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  ArrowPathIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import apiService from '../services/api';

interface SystemStatus {
  overall_status: 'operational' | 'degraded' | 'outage';
  last_updated: string;
  services: ServiceStatus[];
  incidents: Incident[];
  maintenance: MaintenanceWindow[];
  metrics: SystemMetrics;
}

interface ServiceStatus {
  name: string;
  status: 'operational' | 'degraded' | 'outage';
  description: string;
  response_time: number;
  uptime: number;
  last_checked: string;
}

interface Incident {
  id: string;
  title: string;
  description: string;
  status: 'investigating' | 'identified' | 'monitoring' | 'resolved';
  severity: 'low' | 'medium' | 'high' | 'critical';
  created_at: string;
  updated_at: string;
  affected_services: string[];
}

interface MaintenanceWindow {
  id: string;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'in_progress' | 'completed';
  affected_services: string[];
}

interface SystemMetrics {
  response_time_avg: number;
  uptime_percentage: number;
  active_users: number;
  documents_processed_today: number;
  api_requests_per_minute: number;
}

const SystemStatusPage: React.FC = () => {
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  useEffect(() => {
    fetchSystemStatus();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(fetchSystemStatus, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      const response = await apiService.getSystemStatus();
      setStatus(response.data);
      setLastRefresh(new Date());
      setError('');
    } catch (err: any) {
      setError('Failed to fetch system status');
      console.error('Status fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'operational':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'degraded':
        return <ExclamationTriangleIcon className="h-5 w-5 text-yellow-500" />;
      case 'outage':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'operational':
        return 'text-green-600 bg-green-100';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-100';
      case 'outage':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-100';
      case 'high':
        return 'text-orange-600 bg-orange-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'low':
        return 'text-blue-600 bg-blue-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatUptime = (uptime: number) => {
    return `${uptime.toFixed(2)}%`;
  };

  const formatResponseTime = (time: number) => {
    return `${time}ms`;
  };

  if (loading && !status) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-6"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Home
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">System Status</h1>
              <p className="text-gray-600 mt-1">
                Real-time status of all document management services
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </span>
              <button
                onClick={fetchSystemStatus}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        {status && (
          <>
            {/* Overall Status */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {getStatusIcon(status.overall_status)}
                  <div className="ml-3">
                    <h2 className="text-xl font-semibold text-gray-900">
                      System Status: {status.overall_status.charAt(0).toUpperCase() + status.overall_status.slice(1)}
                    </h2>
                    <p className="text-gray-600">
                      All systems are {status.overall_status === 'operational' ? 'functioning normally' : 
                      status.overall_status === 'degraded' ? 'experiencing some issues' : 'experiencing significant problems'}
                    </p>
                  </div>
                </div>
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(status.overall_status)}`}>
                  {status.overall_status.toUpperCase()}
                </span>
              </div>
            </div>

            {/* System Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <ClockIcon className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Avg Response Time</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatResponseTime(status.metrics.response_time_avg)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Uptime</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {formatUptime(status.metrics.uptime_percentage)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <ServerIcon className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Active Users</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {status.metrics.active_users.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <DocumentTextIcon className="h-8 w-8 text-orange-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Documents Today</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {status.metrics.documents_processed_today.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <CloudIcon className="h-8 w-8 text-indigo-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">API Requests/min</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {status.metrics.api_requests_per_minute.toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Services Status */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Service Status</h2>
              <div className="space-y-4">
                {status.services.map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center">
                      {getStatusIcon(service.status)}
                      <div className="ml-3">
                        <h3 className="text-lg font-medium text-gray-900">{service.name}</h3>
                        <p className="text-sm text-gray-600">{service.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                        {service.status.toUpperCase()}
                      </span>
                      <div className="text-sm text-gray-500 mt-1">
                        Uptime: {formatUptime(service.uptime)} | Response: {formatResponseTime(service.response_time)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Active Incidents */}
            {status.incidents.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Active Incidents</h2>
                <div className="space-y-4">
                  {status.incidents.map((incident) => (
                    <div key={incident.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <h3 className="text-lg font-medium text-gray-900">{incident.title}</h3>
                            <span className={`ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(incident.severity)}`}>
                              {incident.severity.toUpperCase()}
                            </span>
                          </div>
                          <p className="text-gray-600 mt-2">{incident.description}</p>
                          <div className="flex items-center mt-3 text-sm text-gray-500">
                            <span>Status: {incident.status.replace('_', ' ').toUpperCase()}</span>
                            <span className="mx-2">•</span>
                            <span>Started: {new Date(incident.created_at).toLocaleString()}</span>
                            <span className="mx-2">•</span>
                            <span>Updated: {new Date(incident.updated_at).toLocaleString()}</span>
                          </div>
                          {incident.affected_services.length > 0 && (
                            <div className="mt-2">
                              <span className="text-sm text-gray-500">Affected services: </span>
                              <span className="text-sm text-gray-700">{incident.affected_services.join(', ')}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Scheduled Maintenance */}
            {status.maintenance.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Scheduled Maintenance</h2>
                <div className="space-y-4">
                  {status.maintenance.map((maintenance) => (
                    <div key={maintenance.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900">{maintenance.title}</h3>
                          <p className="text-gray-600 mt-2">{maintenance.description}</p>
                          <div className="flex items-center mt-3 text-sm text-gray-500">
                            <span>Status: {maintenance.status.replace('_', ' ').toUpperCase()}</span>
                            <span className="mx-2">•</span>
                            <span>Start: {new Date(maintenance.start_time).toLocaleString()}</span>
                            <span className="mx-2">•</span>
                            <span>End: {new Date(maintenance.end_time).toLocaleString()}</span>
                          </div>
                          {maintenance.affected_services.length > 0 && (
                            <div className="mt-2">
                              <span className="text-sm text-gray-500">Affected services: </span>
                              <span className="text-sm text-gray-700">{maintenance.affected_services.join(', ')}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}

        {/* Footer */}
        <div className="mt-12 text-center">
          <div className="space-x-6">
            <Link href="/contact" className="text-primary-600 hover:text-primary-700">
              Contact Support
            </Link>
            <Link href="/help" className="text-primary-600 hover:text-primary-700">
              Help Center
            </Link>
            <a href="https://status.notecontrol.gov" className="text-primary-600 hover:text-primary-700">
              Status History
            </a>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SystemStatusPage;
