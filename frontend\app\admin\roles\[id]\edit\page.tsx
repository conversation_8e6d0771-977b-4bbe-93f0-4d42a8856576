'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  ShieldCheckIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../../components/Layout/Layout';
import { useAuthStore } from '../../../../stores/authStore';
import apiService from '../../../../services/api';

interface Role {
  id: number;
  name: string;
  description: string;
  permissions: string[];
  is_system: boolean;
}

interface RoleFormData {
  name: string;
  description: string;
  permissions: string[];
}

const AVAILABLE_PERMISSIONS = [
  'documents.create',
  'documents.read',
  'documents.update',
  'documents.delete',
  'documents.publish',
  'categories.create',
  'categories.read',
  'categories.update',
  'categories.delete',
  'agencies.create',
  'agencies.read',
  'agencies.update',
  'agencies.delete',
  'users.create',
  'users.read',
  'users.update',
  'users.delete',
  'roles.create',
  'roles.read',
  'roles.update',
  'roles.delete',
  'regulations.create',
  'regulations.read',
  'regulations.update',
  'regulations.delete',
  'tasks.create',
  'tasks.read',
  'tasks.update',
  'tasks.delete',
  'admin.access',
  'system.manage'
];

const AdminEditRolePage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const { user, isAuthenticated } = useAuthStore();
  const [role, setRole] = useState<Role | null>(null);
  const [formData, setFormData] = useState<RoleFormData>({
    name: '',
    description: '',
    permissions: []
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  const roleId = params?.id as string;

  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'admin') {
      router.push('/dashboard');
      return;
    }

    if (roleId) {
      fetchRole();
    }
  }, [roleId, isAuthenticated, user, router]);

  const fetchRole = async () => {
    try {
      setLoading(true);
      const response = await apiService.getRole(parseInt(roleId));
      const roleData = response.data;
      setRole(roleData);
      setFormData({
        name: roleData.name || '',
        description: roleData.description || '',
        permissions: roleData.permissions || []
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch role');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      permissions: checked
        ? [...prev.permissions, permission]
        : prev.permissions.filter(p => p !== permission)
    }));
  };

  const handleSelectAllPermissions = () => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.length === AVAILABLE_PERMISSIONS.length 
        ? [] 
        : [...AVAILABLE_PERMISSIONS]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.name.trim()) {
      setError('Role name is required');
      return;
    }

    try {
      setSaving(true);
      setError('');
      
      await apiService.updateRole(parseInt(roleId), formData);
      router.push('/admin/roles');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update role');
    } finally {
      setSaving(false);
    }
  };

  if (!isAuthenticated || user?.role !== 'admin') {
    return null;
  }

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading role...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!role) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <ShieldCheckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Role Not Found</h3>
            <p className="text-gray-600 mb-4">The role you're looking for doesn't exist.</p>
            <Link
              href="/admin/roles"
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Roles
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  if (role.is_system) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <ShieldCheckIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Cannot Edit System Role</h3>
            <p className="text-gray-600 mb-4">System roles cannot be modified.</p>
            <Link
              href="/admin/roles"
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Roles
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  const groupedPermissions = AVAILABLE_PERMISSIONS.reduce((groups, permission) => {
    const [resource] = permission.split('.');
    if (!groups[resource]) {
      groups[resource] = [];
    }
    groups[resource].push(permission);
    return groups;
  }, {} as Record<string, string[]>);

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/admin/roles"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Roles
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Role</h1>
              <p className="text-gray-600 mt-1">Update role information and permissions</p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Name */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Role Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Describe the role and its purpose..."
              />
            </div>

            {/* Permissions */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <label className="block text-sm font-medium text-gray-700">
                  Permissions
                </label>
                <button
                  type="button"
                  onClick={handleSelectAllPermissions}
                  className="text-sm text-primary-600 hover:text-primary-500"
                >
                  {formData.permissions.length === AVAILABLE_PERMISSIONS.length ? 'Deselect All' : 'Select All'}
                </button>
              </div>

              <div className="border border-gray-200 rounded-md p-4 max-h-96 overflow-y-auto">
                {Object.entries(groupedPermissions).map(([resource, permissions]) => (
                  <div key={resource} className="mb-6 last:mb-0">
                    <h4 className="text-sm font-medium text-gray-900 mb-3 capitalize">
                      {resource} Permissions
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {permissions.map((permission) => (
                        <label key={permission} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={formData.permissions.includes(permission)}
                            onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                            className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                          />
                          <span className="ml-2 text-sm text-gray-700">
                            {permission.split('.')[1]}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-3 text-sm text-gray-600">
                Selected {formData.permissions.length} of {AVAILABLE_PERMISSIONS.length} permissions
              </div>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link
                href="/admin/roles"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <XMarkIcon className="h-4 w-4 mr-2" />
                Cancel
              </Link>
              <button
                type="submit"
                disabled={saving}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
              >
                <CheckIcon className="h-4 w-4 mr-2" />
                {saving ? 'Updating...' : 'Update Role'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default AdminEditRolePage;
