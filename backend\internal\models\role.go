package models

import (
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Role represents a system role
type Role struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Name         string         `json:"name" gorm:"uniqueIndex;not null"`
	DisplayName  string         `json:"display_name" gorm:"not null"`
	Description  string         `json:"description"`
	IsSystemRole bool           `json:"is_system_role" gorm:"default:false"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	Permissions []Permission `json:"permissions,omitempty" gorm:"many2many:role_permissions;"`
	UserRoles   []UserRole   `json:"user_roles,omitempty"`
}

// Permission represents a system permission
type Permission struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"uniqueIndex;not null"`
	DisplayName string    `json:"display_name" gorm:"not null"`
	Description string    `json:"description"`
	Resource    string    `json:"resource" gorm:"not null"` // e.g., 'documents', 'agencies', 'users'
	Action      string    `json:"action" gorm:"not null"`   // e.g., 'create', 'read', 'update', 'delete'
	CreatedAt   time.Time `json:"created_at"`

	// Relationships
	Roles []Role `json:"roles,omitempty" gorm:"many2many:role_permissions;"`
}

// UserRole represents the assignment of a role to a user
type UserRole struct {
	ID         uint           `json:"id" gorm:"primaryKey"`
	UserID     uint           `json:"user_id" gorm:"not null"`
	RoleID     uint           `json:"role_id" gorm:"not null"`
	AssignedBy *uint          `json:"assigned_by,omitempty"`
	AssignedAt time.Time      `json:"assigned_at" gorm:"default:CURRENT_TIMESTAMP"`
	ExpiresAt  *time.Time     `json:"expires_at,omitempty"`
	IsActive   bool           `json:"is_active" gorm:"default:true"`
	CreatedAt  time.Time      `json:"created_at"`
	UpdatedAt  time.Time      `json:"updated_at"`
	DeletedAt  gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relationships
	User           User  `json:"user,omitempty"`
	Role           Role  `json:"role,omitempty"`
	AssignedByUser *User `json:"assigned_by_user,omitempty" gorm:"foreignKey:AssignedBy"`

	// Unique constraint
	_ struct{} `gorm:"uniqueIndex:idx_user_role,unique"`
}

// RolePermission represents the junction table for role-permission relationships
type RolePermission struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	RoleID       uint      `json:"role_id" gorm:"not null"`
	PermissionID uint      `json:"permission_id" gorm:"not null"`
	CreatedAt    time.Time `json:"created_at"`

	// Relationships
	Role       Role       `json:"role,omitempty"`
	Permission Permission `json:"permission,omitempty"`

	// Unique constraint
	_ struct{} `gorm:"uniqueIndex:idx_role_permission,unique"`
}

// UserRoleAssignment represents a request to assign/remove roles
type UserRoleAssignment struct {
	UserID    uint       `json:"user_id" binding:"required"`
	RoleIDs   []uint     `json:"role_ids" binding:"required"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

// RoleWithPermissions represents a role with its permissions loaded
type RoleWithPermissions struct {
	Role
	PermissionCount int `json:"permission_count"`
	UserCount       int `json:"user_count"`
}

// UserWithRoles represents a user with their roles loaded
type UserWithRoles struct {
	User
	Roles []RoleInfo `json:"roles"`
}

// RoleInfo represents basic role information
type RoleInfo struct {
	ID          uint       `json:"id"`
	Name        string     `json:"name"`
	DisplayName string     `json:"display_name"`
	AssignedAt  time.Time  `json:"assigned_at"`
	ExpiresAt   *time.Time `json:"expires_at,omitempty"`
	IsActive    bool       `json:"is_active"`
}

// PermissionInfo represents basic permission information
type PermissionInfo struct {
	ID          uint   `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
}

// HasPermission checks if a user has a specific permission through their assigned roles
func (u *User) HasPermission(permissionName string) bool {
	// Get database connection - this should be passed as parameter in production
	// For now, we'll use a global database instance
	db := getGlobalDB()
	if db == nil {
		// Fallback to legacy role check for admin users
		return string(u.Role) == "admin"
	}

	// Check if user has the permission through their active roles
	hasPermission, err := checkUserPermissionByID(u.ID, permissionName, db)
	if err != nil {
		// Fallback to legacy role check for admin users on error
		return string(u.Role) == "admin"
	}

	return hasPermission
}

// HasRole checks if a user has a specific role assigned
func (u *User) HasRole(roleName string) bool {
	// Get database connection
	db := getGlobalDB()
	if db == nil {
		// Fallback to legacy role field check
		return string(u.Role) == roleName
	}

	// Check if user has the role through active role assignments
	hasRole, err := checkUserRoleByID(u.ID, roleName, db)
	if err != nil {
		// Fallback to legacy role field check on error
		return string(u.Role) == roleName
	}

	return hasRole
}

// IsExpired checks if a user role assignment has expired
func (ur *UserRole) IsExpired() bool {
	if ur.ExpiresAt == nil {
		return false
	}
	return time.Now().After(*ur.ExpiresAt)
}

// IsEffective checks if a user role is currently effective (active and not expired)
func (ur *UserRole) IsEffective() bool {
	return ur.IsActive && !ur.IsExpired()
}

// GetEffectiveRoles returns all effective roles for a user
func GetEffectiveRoles(userID uint, db *gorm.DB) ([]Role, error) {
	var roles []Role

	err := db.Table("roles").
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.is_active = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > ?)",
			userID, true, time.Now()).
		Find(&roles).Error

	return roles, err
}

// GetUserPermissions returns all permissions for a user based on their roles
func GetUserPermissions(userID uint, db *gorm.DB) ([]Permission, error) {
	var permissions []Permission

	err := db.Table("permissions").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.is_active = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > ?)",
			userID, true, time.Now()).
		Distinct().
		Find(&permissions).Error

	return permissions, err
}

// TableName overrides the table name for UserRole
func (UserRole) TableName() string {
	return "user_roles"
}

// TableName overrides the table name for RolePermission
func (RolePermission) TableName() string {
	return "role_permissions"
}

// Global database instance variable (should be set by the application)
var globalDBInstance *gorm.DB

// SetGlobalDB sets the global database instance for role checking
func SetGlobalDB(db *gorm.DB) {
	globalDBInstance = db
}

// getGlobalDB returns the global database instance
func getGlobalDB() *gorm.DB {
	return globalDBInstance
}

// checkUserPermissionByID checks if a user has a specific permission through their roles
func checkUserPermissionByID(userID uint, permissionName string, db *gorm.DB) (bool, error) {
	var count int64
	err := db.Table("permissions").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.is_active = ? AND permissions.name = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > ?)",
			userID, true, permissionName, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check user permission: %w", err)
	}

	return count > 0, nil
}

// checkUserRoleByID checks if a user has a specific role assigned
func checkUserRoleByID(userID uint, roleName string, db *gorm.DB) (bool, error) {
	var count int64
	err := db.Table("user_roles").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("user_roles.user_id = ? AND user_roles.is_active = ? AND roles.name = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > ?)",
			userID, true, roleName, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check user role: %w", err)
	}

	return count > 0, nil
}

// HasPermissionWithDB checks if a user has a specific permission with explicit database connection
func (u *User) HasPermissionWithDB(permissionName string, db *gorm.DB) bool {
	hasPermission, err := checkUserPermissionByID(u.ID, permissionName, db)
	if err != nil {
		// Fallback to legacy role check for admin users on error
		return string(u.Role) == "admin"
	}
	return hasPermission
}

// HasRoleWithDB checks if a user has a specific role with explicit database connection
func (u *User) HasRoleWithDB(roleName string, db *gorm.DB) bool {
	hasRole, err := checkUserRoleByID(u.ID, roleName, db)
	if err != nil {
		// Fallback to legacy role field check on error
		return string(u.Role) == roleName
	}
	return hasRole
}

// GetUserEffectivePermissions returns all effective permissions for a user
func (u *User) GetUserEffectivePermissions(db *gorm.DB) ([]Permission, error) {
	return GetUserPermissions(u.ID, db)
}

// GetUserEffectiveRoles returns all effective roles for a user
func (u *User) GetUserEffectiveRoles(db *gorm.DB) ([]Role, error) {
	return GetEffectiveRoles(u.ID, db)
}

// CanPerformAction checks if a user can perform a specific action on a resource
func (u *User) CanPerformAction(resource, action string, db *gorm.DB) bool {
	// Check for specific permission
	permissionName := fmt.Sprintf("%s_%s", action, resource)
	if u.HasPermissionWithDB(permissionName, db) {
		return true
	}

	// Check for wildcard permissions
	wildcardPermission := fmt.Sprintf("%s_*", action)
	if u.HasPermissionWithDB(wildcardPermission, db) {
		return true
	}

	// Check for admin permission
	if u.HasPermissionWithDB("admin_access", db) {
		return true
	}

	// Fallback to legacy role check for admin users
	return string(u.Role) == "admin"
}

// IsRoleExpired checks if a specific role assignment for the user has expired
func (u *User) IsRoleExpired(roleName string, db *gorm.DB) bool {
	var userRole UserRole
	err := db.Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("user_roles.user_id = ? AND roles.name = ? AND user_roles.is_active = ?",
			u.ID, roleName, true).
		First(&userRole).Error

	if err != nil {
		return true // Consider expired if not found or error
	}

	return userRole.IsExpired()
}

// GetRoleHierarchyLevel returns the highest hierarchy level for the user's roles
func (u *User) GetRoleHierarchyLevel(db *gorm.DB) int {
	// Legacy role hierarchy for fallback
	legacyHierarchy := map[string]int{
		"viewer_level_1": 1,
		"viewer_level_2": 2,
		"viewer_level_3": 3,
		"viewer":         3,
		"editor":         4,
		"reviewer":       5,
		"publisher":      6,
		"admin":          7,
	}

	// Get user's effective roles
	roles, err := u.GetUserEffectiveRoles(db)
	if err != nil {
		// Fallback to legacy role
		if level, exists := legacyHierarchy[string(u.Role)]; exists {
			return level
		}
		return 1 // Default to lowest level
	}

	maxLevel := 1
	for _, role := range roles {
		if level, exists := legacyHierarchy[role.Name]; exists {
			if level > maxLevel {
				maxLevel = level
			}
		}
	}

	return maxLevel
}
