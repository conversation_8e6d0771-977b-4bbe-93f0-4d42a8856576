package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// DigitalCertificateRequest represents the request structure for digital certificates
type DigitalCertificateRequest struct {
	SerialNumber string    `json:"serial_number" binding:"required"`
	Thumbprint   string    `json:"thumbprint" binding:"required"`
	Subject      string    `json:"subject" binding:"required"`
	Issuer       string    `json:"issuer" binding:"required"`
	NotBefore    time.Time `json:"not_before" binding:"required"`
	NotAfter     time.Time `json:"not_after" binding:"required"`
	KeyAlgorithm string    `json:"key_algorithm"`
	KeyLength    int       `json:"key_length"`
	Purpose      string    `json:"purpose"`
	Notes        string    `json:"notes"`
	IsActive     bool      `json:"is_active"`
}

// GetDigitalCertificates returns all digital certificates
// @Summary Get all digital certificates
// @Description Returns a list of all digital certificates
// @Tags digital-certificates
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param per_page query int false "Items per page" default(25)
// @Param search query string false "Search term"
// @Param type query string false "Certificate type filter"
// @Param is_active query bool false "Active status filter"
// @Success 200 {object} PaginationResponse
// @Failure 500 {object} ErrorResponse
// @Router /digital-certificates [get]
func GetDigitalCertificates(c *gin.Context) {
	pagination := GetPaginationParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.DigitalCertificate{})

	// Apply filters
	if search := c.Query("search"); search != "" {
		query = query.Where("name ILIKE ? OR subject ILIKE ? OR issuer ILIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if certType := c.Query("type"); certType != "" {
		query = query.Where("type = ?", certType)
	}

	if activeStr := c.Query("is_active"); activeStr != "" {
		if active, err := strconv.ParseBool(activeStr); err == nil {
			query = query.Where("is_active = ?", active)
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		HandleInternalError(c, "Failed to count certificates: "+err.Error())
		return
	}

	// Apply pagination and sorting
	offset := (pagination.Page - 1) * pagination.PerPage
	query = query.Offset(offset).Limit(pagination.PerPage)

	sortBy := c.DefaultQuery("sort_by", "created_at")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc"
	}
	query = query.Order(sortBy + " " + sortOrder)

	// Execute query
	var certificates []models.DigitalCertificate
	if err := query.Find(&certificates).Error; err != nil {
		HandleInternalError(c, "Failed to fetch certificates: "+err.Error())
		return
	}

	// Convert to response format
	certificateResponses := make([]gin.H, len(certificates))
	for i, cert := range certificates {
		certificateResponses[i] = gin.H{
			"id":            cert.ID,
			"serial_number": cert.SerialNumber,
			"thumbprint":    cert.Thumbprint,
			"subject":       cert.Subject,
			"issuer":        cert.Issuer,
			"not_before":    cert.NotBefore,
			"not_after":     cert.NotAfter,
			"key_algorithm": cert.KeyAlgorithm,
			"key_length":    cert.KeyLength,
			"purpose":       cert.Purpose,
			"notes":         cert.Notes,
			"is_active":     cert.IsActive,
			"status":        cert.Status,
			"created_at":    cert.CreatedAt,
			"updated_at":    cert.UpdatedAt,
		}
	}

	response := CreatePaginationResponse(certificateResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// GetDigitalCertificate returns a specific digital certificate
// @Summary Get digital certificate by ID
// @Description Returns a specific digital certificate by its ID
// @Tags digital-certificates
// @Accept json
// @Produce json
// @Param id path int true "Certificate ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /digital-certificates/{id} [get]
func GetDigitalCertificate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var certificate models.DigitalCertificate
	err := db.First(&certificate, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			HandleNotFound(c, "Digital certificate")
		} else {
			HandleInternalError(c, "Failed to retrieve certificate: "+err.Error())
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Digital certificate retrieved successfully",
		Data:    certificate,
	})
}

// CreateDigitalCertificate creates a new digital certificate
// @Summary Create digital certificate
// @Description Creates a new digital certificate
// @Tags digital-certificates
// @Accept json
// @Produce json
// @Param certificate body DigitalCertificateRequest true "Certificate data"
// @Success 201 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /digital-certificates [post]
func CreateDigitalCertificate(c *gin.Context) {
	var req DigitalCertificateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Check for duplicate serial number
	var existingCert models.DigitalCertificate
	if err := db.Where("serial_number = ?", req.SerialNumber).First(&existingCert).Error; err == nil {
		HandleBadRequest(c, "Certificate with this serial number already exists")
		return
	}

	// Check for duplicate thumbprint
	if err := db.Where("thumbprint = ?", req.Thumbprint).First(&existingCert).Error; err == nil {
		HandleBadRequest(c, "Certificate with this thumbprint already exists")
		return
	}

	// Create certificate
	certificate := &models.DigitalCertificate{
		SerialNumber: req.SerialNumber,
		Thumbprint:   req.Thumbprint,
		Subject:      req.Subject,
		Issuer:       req.Issuer,
		NotBefore:    req.NotBefore,
		NotAfter:     req.NotAfter,
		KeyLength:    req.KeyLength,
		Purpose:      req.Purpose,
		Notes:        req.Notes,
		IsActive:     req.IsActive,
		OwnerID:      *userID.(*uint), // Get from authenticated user
	}

	if err := db.Create(certificate).Error; err != nil {
		HandleInternalError(c, "Failed to create certificate: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Digital certificate created successfully",
		Data:    certificate,
	})
}

// UpdateDigitalCertificate updates an existing digital certificate
// @Summary Update digital certificate
// @Description Updates an existing digital certificate
// @Tags digital-certificates
// @Accept json
// @Produce json
// @Param id path int true "Certificate ID"
// @Param certificate body DigitalCertificateRequest true "Updated certificate data"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /digital-certificates/{id} [put]
func UpdateDigitalCertificate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req DigitalCertificateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Find existing certificate
	var certificate models.DigitalCertificate
	err := db.First(&certificate, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			HandleNotFound(c, "Digital certificate")
		} else {
			HandleInternalError(c, "Failed to find certificate: "+err.Error())
		}
		return
	}

	// Check for duplicate serial number (excluding current certificate)
	var existingCert models.DigitalCertificate
	if err := db.Where("serial_number = ? AND id != ?", req.SerialNumber, id).First(&existingCert).Error; err == nil {
		HandleBadRequest(c, "Certificate with this serial number already exists")
		return
	}

	// Check for duplicate thumbprint (excluding current certificate)
	if err := db.Where("thumbprint = ? AND id != ?", req.Thumbprint, id).First(&existingCert).Error; err == nil {
		HandleBadRequest(c, "Certificate with this thumbprint already exists")
		return
	}

	// Update certificate fields
	certificate.SerialNumber = req.SerialNumber
	certificate.Thumbprint = req.Thumbprint
	certificate.Subject = req.Subject
	certificate.Issuer = req.Issuer
	certificate.NotBefore = req.NotBefore
	certificate.NotAfter = req.NotAfter
	certificate.KeyLength = req.KeyLength
	certificate.Purpose = req.Purpose
	certificate.Notes = req.Notes
	certificate.IsActive = req.IsActive

	if err := db.Save(&certificate).Error; err != nil {
		HandleInternalError(c, "Failed to update certificate: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Digital certificate updated successfully",
		Data:    certificate,
	})
}

// DeleteDigitalCertificate deletes a digital certificate
// @Summary Delete digital certificate
// @Description Deletes a digital certificate by its ID
// @Tags digital-certificates
// @Accept json
// @Produce json
// @Param id path int true "Certificate ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 404 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /digital-certificates/{id} [delete]
func DeleteDigitalCertificate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Find existing certificate
	var certificate models.DigitalCertificate
	err := db.First(&certificate, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			HandleNotFound(c, "Digital certificate")
		} else {
			HandleInternalError(c, "Failed to find certificate: "+err.Error())
		}
		return
	}

	// Delete the certificate
	if err := db.Delete(&certificate).Error; err != nil {
		HandleInternalError(c, "Failed to delete certificate: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Digital certificate deleted successfully",
	})
}
