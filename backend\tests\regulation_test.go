package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"federal-register-clone/internal/api"
	"federal-register-clone/internal/api/handlers"
	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)



type RegulationTestSuite struct {
	suite.Suite
	db                *gorm.DB
	router            *gin.Engine
	regulationService *services.RegulationService
	testUser          *models.User
	testAgency        *models.Agency
	authToken         string
}

func (suite *RegulationTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Set global database
	database.DB = db
	suite.db = db

	// Run migrations for all models including regulation models
	err = db.AutoMigrate(
		&models.User{},
		&models.UserSession{},
		&models.UserPreferences{},
		&models.Agency{},
		&models.Document{},
		&models.LawsAndRules{},
		&models.RegulationDocumentVersion{},
		&models.Chunk{},
		&models.ChunkContentVersion{},
		&models.RegulationDocumentVersionChunkMap{},
	)
	suite.Require().NoError(err)

	// Setup router
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:             "test-secret",
			ExpiryHours:        1,
			RefreshExpiryHours: 24,
		},
	}
	suite.router = api.SetupRouter(cfg)

	// Initialize regulation service
	suite.regulationService = services.NewRegulationService(db)

	// Create test data
	suite.createTestData()
}

func (suite *RegulationTestSuite) createTestData() {
	// Create test agency
	agency := &models.Agency{
		Name:        "Test Environmental Protection Agency",
		ShortName:   "TEPA",
		Slug:        "test-epa",
		Description: "Test agency for environmental protection",
		IsActive:    true,
	}
	err := suite.db.Create(agency).Error
	suite.Require().NoError(err)
	suite.testAgency = agency

	// Create test user
	user := &models.User{
		Username:   "testuser",
		Email:      "<EMAIL>",
		FirstName:  "Test",
		LastName:   "User",
		Role:       "editor",
		IsActive:   true,
		IsVerified: true,
	}
	err = suite.db.Create(user).Error
	suite.Require().NoError(err)
	suite.testUser = user

	// Create auth token for testing protected endpoints
	suite.authToken = "Bearer test-token" // In real tests, generate proper JWT
}

func (suite *RegulationTestSuite) TearDownSuite() {
	// Clean up database
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

func (suite *RegulationTestSuite) TestCreateRegulation() {
	// Test data
	effectiveDate := time.Now().AddDate(0, 1, 0) // 1 month from now
	regulationData := map[string]interface{}{
		"title":          "Test Environmental Protection Standards",
		"short_title":    "Test EPA Standards",
		"type":           "rule",
		"cfr_title":      "40",
		"agency_id":      suite.testAgency.ID,
		"description":    "Test comprehensive environmental protection standards",
		"effective_date": effectiveDate.Format("2006-01-02"),
		"is_significant": true,
		"initial_chunks": []map[string]interface{}{
			{
				"chunk_type":       "chapter",
				"number":           "I",
				"title":            "Environmental Protection Agency",
				"chunk_identifier": "TEST-CHAPTER-I",
				"order_in_parent":  1,
			},
		},
	}

	jsonData, _ := json.Marshal(regulationData)
	req, _ := http.NewRequest("POST", "/api/v1/regulations", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "data")

	// Verify regulation was created in database
	var regulation models.LawsAndRules
	err = suite.db.First(&regulation, response["data"].(map[string]interface{})["id"]).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Test Environmental Protection Standards", regulation.Title)
	assert.Equal(suite.T(), models.RegulationType("rule"), regulation.Type)
}

func (suite *RegulationTestSuite) TestGetPublicRegulations() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:         "Public Test Regulation",
		ShortTitle:    "PTR",
		Type:          "rule",
		Status:        "effective",
		CFRTitle:      "40",
		AgencyID:      suite.testAgency.ID,
		CreatedByID:   suite.testUser.ID,
		Description:   "A test regulation for public access",
		EffectiveDate: &time.Time{},
		IsSignificant: false,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Test GET /api/v1/public/regulations
	req, _ := http.NewRequest("GET", "/api/v1/public/regulations", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "data")

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 1)
}

func (suite *RegulationTestSuite) TestGetPublicRegulation() {
	// Create test regulation with chunks
	regulation := &models.LawsAndRules{
		Title:         "Detailed Test Regulation",
		ShortTitle:    "DTR",
		Type:          "regulation",
		Status:        "effective",
		CFRTitle:      "47",
		AgencyID:      suite.testAgency.ID,
		CreatedByID:   suite.testUser.ID,
		Description:   "A detailed test regulation with hierarchical structure",
		EffectiveDate: &time.Time{},
		IsSignificant: true,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Create document version
	now := time.Now()
	version := &models.RegulationDocumentVersion{
		LawRuleID:       regulation.ID,
		VersionNumber:   "1.0.0",
		PublicationDate: &now,
		EffectiveDate:   &now,
		IsOfficial:      true,
		CreatedByID:     suite.testUser.ID,
		Notes:           "Initial version",
	}
	err = suite.db.Create(version).Error
	suite.Require().NoError(err)

	// Update regulation to point to current version
	regulation.CurrentDocumentVersionID = &version.ID
	err = suite.db.Save(regulation).Error
	suite.Require().NoError(err)

	// Test GET /api/v1/public/regulations/:id
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/public/regulations/%d", regulation.ID), nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "regulation")
	assert.Contains(suite.T(), response, "chunks")

	regulationData := response["regulation"].(map[string]interface{})
	assert.Equal(suite.T(), "Detailed Test Regulation", regulationData["title"])
}

func (suite *RegulationTestSuite) TestRegulationFiltering() {
	// Create regulations with different types and agencies
	regulations := []*models.LawsAndRules{
		{
			Title:       "Rule Type Regulation",
			Type:        "rule",
			Status:      "effective",
			AgencyID:    suite.testAgency.ID,
			CreatedByID: suite.testUser.ID,
		},
		{
			Title:       "Law Type Regulation",
			Type:        "law",
			Status:      "effective",
			AgencyID:    suite.testAgency.ID,
			CreatedByID: suite.testUser.ID,
		},
	}

	for _, reg := range regulations {
		err := suite.db.Create(reg).Error
		suite.Require().NoError(err)
	}

	// Test filtering by type
	req, _ := http.NewRequest("GET", "/api/v1/public/regulations?type=rule", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	data := response["data"].([]interface{})
	// Should have at least the rule type regulation we created
	assert.GreaterOrEqual(suite.T(), len(data), 1)
}

func (suite *RegulationTestSuite) TestUpdateRegulation() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:         "Original Title",
		ShortTitle:    "OT",
		Type:          "rule",
		Status:        "draft",
		CFRTitle:      "40",
		AgencyID:      suite.testAgency.ID,
		CreatedByID:   suite.testUser.ID,
		Description:   "Original description",
		IsSignificant: false,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Update data
	updateData := map[string]interface{}{
		"title":          "Updated Title",
		"short_title":    "UT",
		"type":           "regulation",
		"cfr_title":      "47",
		"agency_id":      suite.testAgency.ID,
		"description":    "Updated description",
		"is_significant": true,
	}

	jsonData, _ := json.Marshal(updateData)
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/regulations/%d", regulation.ID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Verify regulation was updated in database
	var updatedRegulation models.LawsAndRules
	err = suite.db.First(&updatedRegulation, regulation.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated Title", updatedRegulation.Title)
	assert.Equal(suite.T(), models.RegulationType("regulation"), updatedRegulation.Type)
	assert.True(suite.T(), updatedRegulation.IsSignificant)
}

func (suite *RegulationTestSuite) TestDeleteRegulation() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:       "To Be Deleted",
		Type:        "rule",
		Status:      "draft",
		AgencyID:    suite.testAgency.ID,
		CreatedByID: suite.testUser.ID,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Delete regulation
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/regulations/%d", regulation.ID), nil)
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	// Verify regulation was soft deleted
	var deletedRegulation models.LawsAndRules
	err = suite.db.Unscoped().First(&deletedRegulation, regulation.ID).Error
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), deletedRegulation.DeletedAt)
}

func (suite *RegulationTestSuite) TestHierarchicalChunkStructure() {
	// Create regulation
	regulation := &models.LawsAndRules{
		Title:       "Hierarchical Test Regulation",
		Type:        "regulation",
		Status:      "effective",
		AgencyID:    suite.testAgency.ID,
		CreatedByID: suite.testUser.ID,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Create hierarchical chunks: Chapter -> Title -> Section
	chapter := &models.Chunk{
		LawRuleID:       regulation.ID,
		ChunkType:       "chapter",
		ChunkIdentifier: "TEST-CHAPTER-I",
		Number:          "I",
		Title:           "Test Chapter",
		OrderInParent:   1,
	}
	err = suite.db.Create(chapter).Error
	suite.Require().NoError(err)

	title := &models.Chunk{
		LawRuleID:       regulation.ID,
		ParentChunkID:   &chapter.ID,
		ChunkType:       "title",
		ChunkIdentifier: "TEST-CHAPTER-I-TITLE-I",
		Number:          "I",
		Title:           "Test Title",
		OrderInParent:   1,
	}
	err = suite.db.Create(title).Error
	suite.Require().NoError(err)

	section := &models.Chunk{
		LawRuleID:       regulation.ID,
		ParentChunkID:   &title.ID,
		ChunkType:       "section",
		ChunkIdentifier: "TEST-CHAPTER-I-TITLE-I-SECTION-1001",
		Number:          "1001",
		Title:           "Test Section",
		OrderInParent:   1,
	}
	err = suite.db.Create(section).Error
	suite.Require().NoError(err)

	// Create content for the section
	content := &models.ChunkContentVersion{
		ChunkID:           section.ID,
		Content:           "This is test content for the section.",
		VersionNumber:     1,
		IsCurrent:         true,
		IsActive:          true,
		ModifiedByID:      suite.testUser.ID,
		ChangeDescription: "Initial content",
	}
	err = suite.db.Create(content).Error
	suite.Require().NoError(err)

	// Update section to point to current content
	section.CurrentChunkContentVersionID = &content.ID
	err = suite.db.Save(section).Error
	suite.Require().NoError(err)

	// Create document version
	now := time.Now()
	version := &models.RegulationDocumentVersion{
		LawRuleID:       regulation.ID,
		VersionNumber:   "1.0.0",
		PublicationDate: &now,
		EffectiveDate:   &now,
		IsOfficial:      true,
		CreatedByID:     suite.testUser.ID,
	}
	err = suite.db.Create(version).Error
	suite.Require().NoError(err)

	// Create chunk mapping for the section (only sections with content need mappings)
	mapping := &models.RegulationDocumentVersionChunkMap{
		DocumentVersionID:     version.ID,
		ChunkID:               section.ID,
		ChunkContentVersionID: content.ID,
	}
	err = suite.db.Create(mapping).Error
	suite.Require().NoError(err)

	// Update regulation to point to current version
	regulation.CurrentDocumentVersionID = &version.ID
	err = suite.db.Save(regulation).Error
	suite.Require().NoError(err)

	// Test the hierarchical structure via API
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/public/regulations/%d", regulation.ID), nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	chunks := response["chunks"].([]interface{})
	assert.Len(suite.T(), chunks, 1) // Should have 1 root chunk (Chapter)

	// Verify chapter structure
	chapterData := chunks[0].(map[string]interface{})
	assert.Equal(suite.T(), "chapter", chapterData["chunk_type"])
	assert.Equal(suite.T(), "I", chapterData["number"])

	// Verify title is child of chapter
	chapterChildren := chapterData["children"].([]interface{})
	assert.Len(suite.T(), chapterChildren, 1)

	titleData := chapterChildren[0].(map[string]interface{})
	assert.Equal(suite.T(), "title", titleData["chunk_type"])
	assert.Equal(suite.T(), "I", titleData["number"])

	// Verify section is child of title
	titleChildren := titleData["children"].([]interface{})
	assert.Len(suite.T(), titleChildren, 1)

	sectionData := titleChildren[0].(map[string]interface{})
	assert.Equal(suite.T(), "section", sectionData["chunk_type"])
	assert.Equal(suite.T(), "1001", sectionData["number"])
	assert.NotNil(suite.T(), sectionData["current_content"])
}

func TestRegulationTestSuite(t *testing.T) {
	suite.Run(t, new(RegulationTestSuite))
}
