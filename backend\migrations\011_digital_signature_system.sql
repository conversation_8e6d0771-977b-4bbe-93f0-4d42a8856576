-- Digital Signature System Migration
-- This migration creates tables for digital signatures, certificates, workflows, and audit logs

-- Digital Certificates Table
CREATE TABLE IF NOT EXISTS digital_certificates (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Certificate identification
    serial_number VARCHAR(255) UNIQUE NOT NULL,
    thumbprint VARCHAR(255) UNIQUE NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    
    -- Certificate owner
    owner_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Certificate details
    subject TEXT NOT NULL,
    issuer TEXT NOT NULL,
    common_name VARCHAR(255) NOT NULL,
    organization VARCHAR(255),
    organizational_unit VARCHAR(255),
    country VARCHAR(10),
    state VARCHAR(255),
    locality VARCHAR(255),
    email_address VARCHAR(255),
    
    -- Certificate validity
    not_before TIMES<PERSON>MP WITH TIME ZONE NOT NULL,
    not_after TIMESTAMP WITH TIME ZONE NOT NULL,
    issued_at TIMESTAMP WITH TIME ZONE NOT NULL,
    revoked_at TIMESTAMP WITH TIME ZONE,
    revoked_by_id INTEGER REFERENCES users(id),
    revocation_reason TEXT,
    
    -- Certificate data
    public_key TEXT NOT NULL,
    private_key TEXT, -- Encrypted private key
    certificate_data TEXT, -- PEM encoded certificate
    key_usage VARCHAR(255),
    extended_key_usage VARCHAR(255),
    
    -- Certificate authority information
    ca_issued BOOLEAN DEFAULT FALSE,
    ca_certificate_id INTEGER REFERENCES digital_certificates(id),
    certificate_chain TEXT,
    
    -- Usage tracking
    signature_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    
    -- Additional metadata
    purpose VARCHAR(255),
    notes TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Digital Signatures Table
CREATE TABLE IF NOT EXISTS digital_signatures (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Signature identification
    signature_id VARCHAR(255) UNIQUE NOT NULL,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    
    -- Document relationship
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Signer information
    signer_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    signer_name VARCHAR(255) NOT NULL,
    signer_email VARCHAR(255) NOT NULL,
    signer_title VARCHAR(255),
    
    -- Signature data
    signature_data TEXT,
    signature_hash VARCHAR(255) NOT NULL,
    document_hash VARCHAR(255) NOT NULL,
    signature_method VARCHAR(255),
    signature_location VARCHAR(255),
    
    -- Certificate information
    certificate_id INTEGER REFERENCES digital_certificates(id),
    certificate_serial VARCHAR(255),
    certificate_issuer VARCHAR(255),
    
    -- Timing information
    requested_at TIMESTAMP WITH TIME ZONE NOT NULL,
    signed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    valid_from TIMESTAMP WITH TIME ZONE,
    valid_until TIMESTAMP WITH TIME ZONE,
    
    -- Workflow information
    workflow_step_id INTEGER,
    signing_order INTEGER DEFAULT 1,
    is_required BOOLEAN DEFAULT TRUE,
    requires_witness BOOLEAN DEFAULT FALSE,
    
    -- Validation and verification
    is_valid BOOLEAN DEFAULT FALSE,
    validation_status VARCHAR(255),
    validated_at TIMESTAMP WITH TIME ZONE,
    validated_by_id INTEGER REFERENCES users(id),
    
    -- Audit trail
    ip_address INET,
    user_agent TEXT,
    device_info TEXT,
    geo_location VARCHAR(255),
    requested_by_id INTEGER NOT NULL REFERENCES users(id),
    
    -- Additional metadata
    reason TEXT,
    notes TEXT,
    metadata JSONB,
    is_visible BOOLEAN DEFAULT TRUE,
    page_number INTEGER,
    x_position DECIMAL(10,6),
    y_position DECIMAL(10,6)
);

-- Signature Workflows Table
CREATE TABLE IF NOT EXISTS signature_workflows (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Workflow identification
    name VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Document relationship
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Workflow status
    status VARCHAR(50) DEFAULT 'pending',
    current_step INTEGER DEFAULT 1,
    total_steps INTEGER NOT NULL,
    completed_steps INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    -- Workflow configuration
    require_sequential BOOLEAN DEFAULT TRUE,
    allow_parallel BOOLEAN DEFAULT FALSE,
    require_all_signers BOOLEAN DEFAULT TRUE,
    
    -- Creator information
    created_by_id INTEGER NOT NULL REFERENCES users(id),
    
    -- Additional metadata
    notes TEXT,
    metadata JSONB
);

-- Signature Audit Logs Table
CREATE TABLE IF NOT EXISTS signature_audit_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Related entities
    signature_id INTEGER REFERENCES digital_signatures(id),
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Audit information
    action VARCHAR(255) NOT NULL,
    description TEXT,
    user_id INTEGER NOT NULL REFERENCES users(id),
    
    -- Technical details
    ip_address INET,
    user_agent TEXT,
    device_info TEXT,
    geo_location VARCHAR(255),
    
    -- Additional metadata
    metadata JSONB
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_digital_certificates_owner_id ON digital_certificates(owner_id);
CREATE INDEX IF NOT EXISTS idx_digital_certificates_status ON digital_certificates(status);
CREATE INDEX IF NOT EXISTS idx_digital_certificates_serial_number ON digital_certificates(serial_number);
CREATE INDEX IF NOT EXISTS idx_digital_certificates_not_after ON digital_certificates(not_after);
CREATE INDEX IF NOT EXISTS idx_digital_certificates_deleted_at ON digital_certificates(deleted_at);

CREATE INDEX IF NOT EXISTS idx_digital_signatures_document_id ON digital_signatures(document_id);
CREATE INDEX IF NOT EXISTS idx_digital_signatures_signer_id ON digital_signatures(signer_id);
CREATE INDEX IF NOT EXISTS idx_digital_signatures_status ON digital_signatures(status);
CREATE INDEX IF NOT EXISTS idx_digital_signatures_signature_id ON digital_signatures(signature_id);
CREATE INDEX IF NOT EXISTS idx_digital_signatures_requested_at ON digital_signatures(requested_at);
CREATE INDEX IF NOT EXISTS idx_digital_signatures_signed_at ON digital_signatures(signed_at);
CREATE INDEX IF NOT EXISTS idx_digital_signatures_deleted_at ON digital_signatures(deleted_at);

CREATE INDEX IF NOT EXISTS idx_signature_workflows_document_id ON signature_workflows(document_id);
CREATE INDEX IF NOT EXISTS idx_signature_workflows_status ON signature_workflows(status);
CREATE INDEX IF NOT EXISTS idx_signature_workflows_created_by_id ON signature_workflows(created_by_id);
CREATE INDEX IF NOT EXISTS idx_signature_workflows_deleted_at ON signature_workflows(deleted_at);

CREATE INDEX IF NOT EXISTS idx_signature_audit_logs_signature_id ON signature_audit_logs(signature_id);
CREATE INDEX IF NOT EXISTS idx_signature_audit_logs_document_id ON signature_audit_logs(document_id);
CREATE INDEX IF NOT EXISTS idx_signature_audit_logs_user_id ON signature_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_signature_audit_logs_action ON signature_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_signature_audit_logs_created_at ON signature_audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_signature_audit_logs_deleted_at ON signature_audit_logs(deleted_at);

-- Add foreign key constraint for workflow_step_id in digital_signatures
-- (This references signature_workflows.id but we'll handle this in the application layer)

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_digital_certificates_updated_at BEFORE UPDATE ON digital_certificates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_digital_signatures_updated_at BEFORE UPDATE ON digital_signatures FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_signature_workflows_updated_at BEFORE UPDATE ON signature_workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_signature_audit_logs_updated_at BEFORE UPDATE ON signature_audit_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample digital certificate for admin user
INSERT INTO digital_certificates (
    serial_number,
    thumbprint,
    status,
    owner_id,
    subject,
    issuer,
    common_name,
    organization,
    country,
    not_before,
    not_after,
    issued_at,
    public_key,
    certificate_data,
    key_usage,
    purpose,
    is_default,
    is_active
) VALUES (
    'CERT-2025-001',
    'SHA1:1234567890ABCDEF1234567890ABCDEF12345678',
    'active',
    1, -- Assuming admin user has ID 1
    'CN=Admin User,O=Federal Register Clone,C=US',
    'CN=Federal Register Clone CA,O=Federal Register Clone,C=US',
    'Admin User',
    'Federal Register Clone',
    'US',
    NOW(),
    NOW() + INTERVAL '1 year',
    NOW(),
    '-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...\n-----END PUBLIC KEY-----',
    '-----BEGIN CERTIFICATE-----\nMIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3...\n-----END CERTIFICATE-----',
    'Digital Signature, Key Encipherment',
    'Document Signing',
    TRUE,
    TRUE
) ON CONFLICT (serial_number) DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE digital_certificates IS 'Stores digital certificates for document signing';
COMMENT ON TABLE digital_signatures IS 'Stores digital signatures applied to documents';
COMMENT ON TABLE signature_workflows IS 'Manages multi-step signature workflows';
COMMENT ON TABLE signature_audit_logs IS 'Audit trail for all signature-related operations';
