'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { financialApi } from '../../../../services/enterpriseApi';
import { BudgetPlan, ChartOfAccounts } from '../../../../types/enterprise';

const NewBudgetPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [accounts, setAccounts] = useState<ChartOfAccounts[]>([]);
  const [formData, setFormData] = useState<Partial<BudgetPlan>>({
    budget_code: '',
    budget_name: '',
    description: '',
    fiscal_year: new Date().getFullYear(),
    budget_period_start: new Date().toISOString().split('T')[0],
    budget_period_end: new Date(new Date().getFullYear(), 11, 31).toISOString().split('T')[0],
    account_id: 0,
    total_amount: 0,
    allocated_amount: 0,
    spent_amount: 0,
    remaining_amount: 0,
    currency_code: 'USD',
    status: 'draft',
    budget_type: 'operational',
    department: '',
    cost_center: '',
    approval_workflow: '',
    metadata: ''
  });

  useEffect(() => {
    fetchAccounts();
  }, []);

  const fetchAccounts = async () => {
    try {
      const response = await financialApi.getAccounts();
      setAccounts(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch accounts');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await financialApi.createBudget(formData);
      router.push('/enterprise/financial/budgets');
    } catch (err: any) {
      setError(err.message || 'Failed to create budget');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'number' ? parseFloat(value) || 0 : value
    }));
  };

  // Calculate remaining amount when total or spent changes
  useEffect(() => {
    const remaining = (formData.total_amount || 0) - (formData.spent_amount || 0);
    setFormData(prev => ({ ...prev, remaining_amount: remaining }));
  }, [formData.total_amount, formData.spent_amount]);

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create New Budget</h1>
        <button
          onClick={() => router.push('/enterprise/financial/budgets')}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Back to Budgets
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Budget Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Code *
            </label>
            <input
              type="text"
              name="budget_code"
              value={formData.budget_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., BUD-2025-001"
            />
          </div>

          {/* Budget Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Name *
            </label>
            <input
              type="text"
              name="budget_name"
              value={formData.budget_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Annual Operating Budget"
            />
          </div>

          {/* Fiscal Year */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fiscal Year *
            </label>
            <input
              type="number"
              name="fiscal_year"
              value={formData.fiscal_year}
              onChange={handleChange}
              required
              min="2000"
              max="2100"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Budget Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Type *
            </label>
            <select
              name="budget_type"
              value={formData.budget_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="operational">Operational</option>
              <option value="capital">Capital</option>
              <option value="project">Project</option>
              <option value="departmental">Departmental</option>
            </select>
          </div>

          {/* Budget Period Start */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Period Start *
            </label>
            <input
              type="date"
              name="budget_period_start"
              value={formData.budget_period_start}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Budget Period End */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Period End *
            </label>
            <input
              type="date"
              name="budget_period_end"
              value={formData.budget_period_end}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Account */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Account *
            </label>
            <select
              name="account_id"
              value={formData.account_id}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Account</option>
              {accounts.map((account) => (
                <option key={account.id} value={account.id}>
                  {account.account_code} - {account.account_name}
                </option>
              ))}
            </select>
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency Code
            </label>
            <input
              type="text"
              name="currency_code"
              value={formData.currency_code}
              onChange={handleChange}
              maxLength={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="USD"
            />
          </div>

          {/* Total Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Total Amount *
            </label>
            <input
              type="number"
              name="total_amount"
              value={formData.total_amount}
              onChange={handleChange}
              required
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Allocated Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Allocated Amount
            </label>
            <input
              type="number"
              name="allocated_amount"
              value={formData.allocated_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Spent Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Spent Amount
            </label>
            <input
              type="number"
              name="spent_amount"
              value={formData.spent_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Remaining Amount (calculated) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remaining Amount (Calculated)
            </label>
            <input
              type="number"
              name="remaining_amount"
              value={formData.remaining_amount}
              readOnly
              className="w-full border rounded px-3 py-2 bg-gray-100 text-gray-600"
            />
          </div>

          {/* Department */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department
            </label>
            <input
              type="text"
              name="department"
              value={formData.department}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Finance, HR, IT"
            />
          </div>

          {/* Cost Center */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost Center
            </label>
            <input
              type="text"
              name="cost_center"
              value={formData.cost_center}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., CC-001"
            />
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="draft">Draft</option>
              <option value="submitted">Submitted</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="active">Active</option>
            </select>
          </div>

          {/* Approval Workflow */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Approval Workflow
            </label>
            <input
              type="text"
              name="approval_workflow"
              value={formData.approval_workflow}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Manager -> Director -> CFO"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Budget description..."
          />
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push('/enterprise/financial/budgets')}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Budget'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NewBudgetPage;
