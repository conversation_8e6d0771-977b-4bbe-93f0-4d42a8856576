package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
)

// GetDocumentCategoryRelationships returns all categories related to a document
func GetDocumentCategoryRelationships(c *gin.Context) {
	// Get document ID from URL
	documentID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		HandleBadRequest(c, "Invalid document ID")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Use raw SQL to avoid GORM model issues
	type CategoryRelationship struct {
		CategoryID   uint   `json:"category_id"`
		CategoryName string `json:"category_name"`
		CategorySlug string `json:"category_slug"`
	}

	var relationships []CategoryRelationship
	query := `
		SELECT c.id as category_id, c.name as category_name, c.slug as category_slug
		FROM categories c
		JOIN document_category_assignments dca ON c.id = dca.category_id
		WHERE dca.document_id = ? AND dca.deleted_at IS NULL AND c.deleted_at IS NULL
		ORDER BY c.name ASC
	`

	if err := db.Raw(query, documentID).Scan(&relationships).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document-category relationships: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Document category relationships retrieved successfully",
		"data":    relationships,
	})
}

// GetDocumentAgencyRelationships returns agency information for a document
func GetDocumentAgencyRelationships(c *gin.Context) {
	// Get document ID from URL
	documentID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		HandleBadRequest(c, "Invalid document ID")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Use raw SQL to avoid GORM model issues
	type AgencyRelationship struct {
		AgencyID   uint   `json:"agency_id"`
		AgencyName string `json:"agency_name"`
		AgencySlug string `json:"agency_slug"`
	}

	var relationship AgencyRelationship
	query := `
		SELECT a.id as agency_id, a.name as agency_name, a.slug as agency_slug
		FROM agencies a
		JOIN documents d ON a.id = d.agency_id
		WHERE d.id = ? AND d.deleted_at IS NULL AND a.deleted_at IS NULL
	`

	if err := db.Raw(query, documentID).Scan(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document-agency relationship: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Document agency relationship retrieved successfully",
		"data":    relationship,
	})
}
