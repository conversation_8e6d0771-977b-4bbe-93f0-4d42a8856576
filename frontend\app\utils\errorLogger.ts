/**
 * Comprehensive Error Logger for Frontend
 * Captures all types of errors including React errors, Axios errors, and runtime errors
 */

export interface ErrorLog {
  id: string;
  timestamp: string;
  type: 'react' | 'axios' | 'runtime' | 'unhandled' | 'promise';
  message: string;
  stack?: string;
  url?: string;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  component?: string;
  props?: any;
  state?: any;
  request?: {
    method?: string;
    url?: string;
    data?: any;
    headers?: any;
  };
  response?: {
    status?: number;
    statusText?: string;
    data?: any;
    headers?: any;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: Record<string, any>;
}

class ErrorLogger {
  private errors: ErrorLog[] = [];
  private maxErrors = 1000;
  private sessionId: string;

  constructor() {
    this.sessionId = this.generateSessionId();
    // Only setup global error handlers in browser environment
    if (typeof window !== 'undefined') {
      this.setupGlobalErrorHandlers();
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private setupGlobalErrorHandlers(): void {
    // Capture unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError({
        type: 'runtime',
        message: event.message,
        stack: event.error?.stack,
        url: event.filename,
        severity: 'high',
        context: {
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });

    // Capture unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        type: 'promise',
        message: event.reason?.message || 'Unhandled Promise Rejection',
        stack: event.reason?.stack,
        severity: 'high',
        context: {
          reason: event.reason,
        },
      });
    });

    // Capture console errors
    const originalConsoleError = console.error;
    console.error = (...args: any[]) => {
      this.logError({
        type: 'runtime',
        message: args.join(' '),
        severity: 'medium',
        context: {
          consoleArgs: args,
        },
      });
      originalConsoleError.apply(console, args);
    };
  }

  logError(errorData: Partial<ErrorLog>): void {
    const error: ErrorLog = {
      id: this.generateErrorId(),
      timestamp: new Date().toISOString(),
      type: errorData.type || 'runtime',
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      url: typeof window !== 'undefined' ? window.location.href : 'server',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
      sessionId: this.sessionId,
      severity: errorData.severity || 'medium',
      component: errorData.component,
      props: errorData.props,
      state: errorData.state,
      request: errorData.request,
      response: errorData.response,
      context: errorData.context,
    };

    this.errors.push(error);

    // Keep only the latest errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(-this.maxErrors);
    }

    // Log to console for immediate visibility
    this.logToConsole(error);

    // Save to localStorage for persistence
    this.saveToStorage();

    // Send to server if critical
    if (error.severity === 'critical') {
      this.sendToServer(error);
    }
  }

  logAxiosError(error: any, config?: any): void {
    const errorData: Partial<ErrorLog> = {
      type: 'axios',
      message: error.message || 'Axios request failed',
      stack: error.stack,
      severity: this.getAxiosErrorSeverity(error),
      request: {
        method: config?.method || error.config?.method,
        url: config?.url || error.config?.url,
        data: config?.data || error.config?.data,
        headers: config?.headers || error.config?.headers,
      },
    };

    if (error.response) {
      errorData.response = {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data,
        headers: error.response.headers,
      };
    }

    this.logError(errorData);
  }

  logReactError(error: Error, errorInfo: any, component?: string): void {
    this.logError({
      type: 'react',
      message: error.message,
      stack: error.stack,
      component,
      severity: 'high',
      context: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
      },
    });
  }

  private getAxiosErrorSeverity(error: any): 'low' | 'medium' | 'high' | 'critical' {
    if (error.response) {
      const status = error.response.status;
      if (status >= 500) return 'critical';
      if (status >= 400) return 'high';
      return 'medium';
    }
    if (error.request) return 'high';
    return 'medium';
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private logToConsole(error: ErrorLog): void {
    const style = this.getConsoleStyle(error.severity);
    console.group(`%c🚨 ${error.type.toUpperCase()} ERROR [${error.severity.toUpperCase()}]`, style);
    console.log('Message:', error.message);
    console.log('Timestamp:', error.timestamp);
    console.log('URL:', error.url);
    if (error.component) console.log('Component:', error.component);
    if (error.stack) console.log('Stack:', error.stack);
    if (error.request) console.log('Request:', error.request);
    if (error.response) console.log('Response:', error.response);
    if (error.context) console.log('Context:', error.context);
    console.groupEnd();
  }

  private getConsoleStyle(severity: string): string {
    const styles = {
      low: 'color: #3b82f6; font-weight: bold;',
      medium: 'color: #f59e0b; font-weight: bold;',
      high: 'color: #ef4444; font-weight: bold;',
      critical: 'color: #dc2626; font-weight: bold; background: #fee2e2; padding: 2px 4px;',
    };
    return styles[severity as keyof typeof styles] || styles.medium;
  }

  private saveToStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const recentErrors = this.errors.slice(-100); // Keep last 100 errors
      localStorage.setItem('frontend_errors', JSON.stringify(recentErrors));
    } catch (e) {
      console.warn('Failed to save errors to localStorage:', e);
    }
  }

  private async sendToServer(error: ErrorLog): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      await fetch('/api/v1/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(error),
      });
    } catch (e) {
      console.warn('Failed to send error to server:', e);
    }
  }

  getErrors(): ErrorLog[] {
    return [...this.errors];
  }

  getErrorsByType(type: ErrorLog['type']): ErrorLog[] {
    return this.errors.filter(error => error.type === type);
  }

  getErrorsBySeverity(severity: ErrorLog['severity']): ErrorLog[] {
    return this.errors.filter(error => error.severity === severity);
  }

  clearErrors(): void {
    this.errors = [];
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem('frontend_errors');
      } catch (e) {
        console.warn('Failed to clear errors from localStorage:', e);
      }
    }
  }

  exportErrors(): string {
    return JSON.stringify(this.errors, null, 2);
  }

  getErrorSummary(): {
    total: number;
    byType: Record<string, number>;
    bySeverity: Record<string, number>;
    recent: ErrorLog[];
  } {
    const byType: Record<string, number> = {};
    const bySeverity: Record<string, number> = {};

    this.errors.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    return {
      total: this.errors.length,
      byType,
      bySeverity,
      recent: this.errors.slice(-10),
    };
  }
}

// Create singleton instance lazily
let errorLoggerInstance: ErrorLogger | null = null;

export const errorLogger = {
  getInstance(): ErrorLogger {
    if (!errorLoggerInstance && typeof window !== 'undefined') {
      errorLoggerInstance = new ErrorLogger();
    }
    return errorLoggerInstance || ({
      errors: [],
      maxErrors: 1000,
      sessionId: 'fallback-session',
      generateSessionId: () => 'fallback-session',
      setupGlobalErrorHandlers: () => {},
      getAxiosErrorSeverity: () => 'medium',
      generateErrorId: () => 'fallback-id',
      logToConsole: () => {},
      logError: () => {},
      logAxiosError: () => {},
      logReactError: () => {},
      getErrors: () => [],
      getErrorsByType: () => [],
      getErrorsBySeverity: () => [],
      clearErrors: () => {},
      exportErrors: () => '[]',
      getErrorSummary: () => ({ total: 0, byType: {}, bySeverity: {}, recent: [] }),
      addError: () => {},
      removeOldErrors: () => {},
      formatError: () => ({}),
      getConsoleStyle: () => '',
      saveToStorage: () => {},
      sendToServer: () => {},
    } as unknown as ErrorLogger);
  },

  logError: (error: Partial<ErrorLog>) => errorLogger.getInstance().logError(error),
  logAxiosError: (error: any, config?: any) => errorLogger.getInstance().logAxiosError(error, config),
  logReactError: (error: Error, errorInfo: any, component?: string) => errorLogger.getInstance().logReactError(error, errorInfo, component),
  getErrors: () => errorLogger.getInstance().getErrors(),
  getErrorsByType: (type: ErrorLog['type']) => errorLogger.getInstance().getErrorsByType(type),
  getErrorsBySeverity: (severity: ErrorLog['severity']) => errorLogger.getInstance().getErrorsBySeverity(severity),
  clearErrors: () => errorLogger.getInstance().clearErrors(),
  exportErrors: () => errorLogger.getInstance().exportErrors(),
  getErrorSummary: () => errorLogger.getInstance().getErrorSummary(),
};

// Helper function for manual error logging
export const logError = (error: Partial<ErrorLog>) => {
  errorLogger.logError(error);
};

// Helper function for axios error logging
export const logAxiosError = (error: any, config?: any) => {
  errorLogger.logAxiosError(error, config);
};

// Helper function for React error logging
export const logReactError = (error: Error, errorInfo: any, component?: string) => {
  errorLogger.logReactError(error, errorInfo, component);
};
