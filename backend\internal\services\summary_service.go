package services

import (
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"federal-register-clone/internal/models"
)

// SummaryService handles summary operations
type SummaryService struct {
	db *gorm.DB
}

// NewSummaryService creates a new summary service
func NewSummaryService(db *gorm.DB) *SummaryService {
	return &SummaryService{db: db}
}

// SummaryFilters represents filters for summary queries
type SummaryFilters struct {
	EntityType  string
	ActionType  string
	SummaryType string
	IsPublic    *bool
	IsFeatured  *bool
	AgencyID    *uint
	CategoryID  *uint
	Page        int
	PerPage     int
	SortBy      string
	SortOrder   string
	DateFrom    *time.Time
	DateTo      *time.Time
}

// SummaryResult represents paginated summary results
type SummaryResult struct {
	Summaries  []models.Summary `json:"summaries"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PerPage    int              `json:"per_page"`
	TotalPages int              `json:"total_pages"`
}

// GetSummaries retrieves summaries with filters and pagination
func (s *SummaryService) GetSummaries(filters SummaryFilters) (*SummaryResult, error) {
	var summaries []models.Summary
	var total int64

	// Build query
	query := s.db.Model(&models.Summary{}).
		Preload("CreatedBy").
		Preload("Agency").
		Preload("Category")

	// Apply filters
	if filters.EntityType != "" {
		query = query.Where("entity_type = ?", filters.EntityType)
	}
	if filters.ActionType != "" {
		query = query.Where("action_type = ?", filters.ActionType)
	}
	if filters.SummaryType != "" {
		query = query.Where("summary_type = ?", filters.SummaryType)
	}
	if filters.IsPublic != nil {
		query = query.Where("is_public = ?", *filters.IsPublic)
	}
	if filters.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filters.IsFeatured)
	}
	if filters.AgencyID != nil {
		query = query.Where("agency_id = ?", *filters.AgencyID)
	}
	if filters.CategoryID != nil {
		query = query.Where("category_id = ?", *filters.CategoryID)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count summaries: %w", err)
	}

	// Apply sorting
	sortBy := "created_at"
	if filters.SortBy != "" {
		sortBy = filters.SortBy
	}
	sortOrder := "DESC"
	if filters.SortOrder != "" {
		sortOrder = strings.ToUpper(filters.SortOrder)
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filters.Page < 1 {
		filters.Page = 1
	}
	if filters.PerPage < 1 {
		filters.PerPage = 25
	}
	offset := (filters.Page - 1) * filters.PerPage
	query = query.Offset(offset).Limit(filters.PerPage)

	// Execute query
	if err := query.Find(&summaries).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch summaries: %w", err)
	}

	totalPages := int((total + int64(filters.PerPage) - 1) / int64(filters.PerPage))

	return &SummaryResult{
		Summaries:  summaries,
		Total:      total,
		Page:       filters.Page,
		PerPage:    filters.PerPage,
		TotalPages: totalPages,
	}, nil
}

// GetPublicSummaries retrieves only public summaries
func (s *SummaryService) GetPublicSummaries(filters SummaryFilters) (*SummaryResult, error) {
	isPublic := true
	filters.IsPublic = &isPublic

	// Only show summaries that are effective (published)
	// Apply additional filtering for public summaries
	if filters.IsPublic == nil {
		isPublic := true
		filters.IsPublic = &isPublic
	}

	return s.GetSummaries(filters)
}

// CreateDocumentSummary creates a summary for document operations
func (s *SummaryService) CreateDocumentSummary(document *models.Document, action models.ActionType, userID uint) error {
	title := s.generateDocumentSummaryTitle(document, action)
	abstract := s.generateDocumentSummaryAbstract(document, action)

	summary := &models.Summary{
		Title:           title,
		Abstract:        abstract,
		SummaryType:     models.SummaryTypeNews,
		EntityType:      models.EntityTypeDocument,
		EntityID:        document.ID,
		ActionType:      action,
		PublicationDate: s.getPublicationDate(document.PublicationDate, action),
		IsPublic:        document.IsPublic,
		IsFeatured:      false,
		Priority:        s.calculatePriority(action, document.Type),
		CreatedByID:     userID,
		AgencyID:        &document.AgencyID,
	}

	// Set category if document has categories
	if len(document.Categories) > 0 {
		summary.CategoryID = &document.Categories[0].ID
	}

	return s.db.Create(summary).Error
}

// CreateRegulationSummary creates a summary for regulation operations
func (s *SummaryService) CreateRegulationSummary(regulation *models.LawsAndRules, action models.ActionType, userID uint) error {
	title := s.generateRegulationSummaryTitle(regulation, action)
	abstract := s.generateRegulationSummaryAbstract(regulation, action)

	summary := &models.Summary{
		Title:           title,
		Abstract:        abstract,
		SummaryType:     models.SummaryTypeNews,
		EntityType:      models.EntityTypeRegulation,
		EntityID:        regulation.ID,
		ActionType:      action,
		PublicationDate: s.getRegulationPublicationDate(regulation.EffectiveDate, action),
		IsPublic:        true, // Regulations are generally public
		IsFeatured:      regulation.IsSignificant,
		Priority:        s.calculateRegulationPriority(action, regulation.IsSignificant),
		CreatedByID:     userID,
		AgencyID:        &regulation.AgencyID,
	}

	return s.db.Create(summary).Error
}

// CreateAgencySummary creates a summary for agency operations
func (s *SummaryService) CreateAgencySummary(agency *models.Agency, action models.ActionType, userID uint) error {
	title := s.generateAgencySummaryTitle(agency, action)
	abstract := s.generateAgencySummaryAbstract(agency, action)

	summary := &models.Summary{
		Title:       title,
		Abstract:    abstract,
		SummaryType: models.SummaryTypeAnnouncement,
		EntityType:  models.EntityTypeAgency,
		EntityID:    agency.ID,
		ActionType:  action,
		IsPublic:    true,
		IsFeatured:  false,
		Priority:    s.calculatePriority(action, "agency"),
		CreatedByID: userID,
		AgencyID:    &agency.ID,
	}

	return s.db.Create(summary).Error
}

// CreateCategorySummary creates a summary for category operations
func (s *SummaryService) CreateCategorySummary(category *models.Category, action models.ActionType, userID uint) error {
	title := s.generateCategorySummaryTitle(category, action)
	abstract := s.generateCategorySummaryAbstract(category, action)

	summary := &models.Summary{
		Title:       title,
		Abstract:    abstract,
		SummaryType: models.SummaryTypeUpdate,
		EntityType:  models.EntityTypeCategory,
		EntityID:    category.ID,
		ActionType:  action,
		IsPublic:    true,
		IsFeatured:  false,
		Priority:    s.calculatePriority(action, "category"),
		CreatedByID: userID,
		CategoryID:  &category.ID,
	}

	return s.db.Create(summary).Error
}

// DeleteSummariesForEntity deletes all summaries for a specific entity
func (s *SummaryService) DeleteSummariesForEntity(entityType models.EntityType, entityID uint) error {
	return s.db.Where("entity_type = ? AND entity_id = ?", entityType, entityID).Delete(&models.Summary{}).Error
}

// Helper methods for generating summary content
func (s *SummaryService) generateDocumentSummaryTitle(document *models.Document, action models.ActionType) string {
	actionName := s.getActionName(action)
	return fmt.Sprintf("%s: %s", actionName, document.Title)
}

func (s *SummaryService) generateDocumentSummaryAbstract(document *models.Document, action models.ActionType) string {
	actionName := strings.ToLower(s.getActionName(action))
	docType := strings.Title(string(document.Type))

	abstract := fmt.Sprintf("A new %s has been %s", docType, actionName)
	if document.Abstract != "" {
		abstract += fmt.Sprintf(": %s", document.Abstract)
	}

	if document.EffectiveDate != nil {
		abstract += fmt.Sprintf(" This document becomes effective on %s.", document.EffectiveDate.Format("January 2, 2006"))
	}

	return abstract
}

func (s *SummaryService) generateRegulationSummaryTitle(regulation *models.LawsAndRules, action models.ActionType) string {
	actionName := s.getActionName(action)
	return fmt.Sprintf("%s: %s", actionName, regulation.Title)
}

func (s *SummaryService) generateRegulationSummaryAbstract(regulation *models.LawsAndRules, action models.ActionType) string {
	actionName := strings.ToLower(s.getActionName(action))

	abstract := fmt.Sprintf("A new regulation has been %s", actionName)
	if regulation.Description != "" {
		abstract += fmt.Sprintf(": %s", regulation.Description)
	}

	if regulation.EffectiveDate != nil {
		abstract += fmt.Sprintf(" This regulation becomes effective on %s.", regulation.EffectiveDate.Format("January 2, 2006"))
	}

	return abstract
}

func (s *SummaryService) generateAgencySummaryTitle(agency *models.Agency, action models.ActionType) string {
	actionName := s.getActionName(action)
	return fmt.Sprintf("Agency %s: %s", actionName, agency.Name)
}

func (s *SummaryService) generateAgencySummaryAbstract(agency *models.Agency, action models.ActionType) string {
	actionName := strings.ToLower(s.getActionName(action))

	abstract := fmt.Sprintf("The agency '%s' has been %s", agency.Name, actionName)
	if agency.Description != "" {
		abstract += fmt.Sprintf(": %s", agency.Description)
	}

	return abstract
}

func (s *SummaryService) generateCategorySummaryTitle(category *models.Category, action models.ActionType) string {
	actionName := s.getActionName(action)
	return fmt.Sprintf("Category %s: %s", actionName, category.Name)
}

func (s *SummaryService) generateCategorySummaryAbstract(category *models.Category, action models.ActionType) string {
	actionName := strings.ToLower(s.getActionName(action))

	abstract := fmt.Sprintf("The category '%s' has been %s", category.Name, actionName)
	if category.Description != "" {
		abstract += fmt.Sprintf(": %s", category.Description)
	}

	return abstract
}

// Helper methods for calculating priority and dates
func (s *SummaryService) getActionName(action models.ActionType) string {
	switch action {
	case models.ActionTypeCreate:
		return "Created"
	case models.ActionTypeUpdate:
		return "Updated"
	case models.ActionTypeDelete:
		return "Deleted"
	case models.ActionTypePublish:
		return "Published"
	case models.ActionTypeArchive:
		return "Archived"
	default:
		return "Modified"
	}
}

func (s *SummaryService) calculatePriority(action models.ActionType, entityType interface{}) int {
	priority := 0

	// Base priority by action
	switch action {
	case models.ActionTypeCreate:
		priority = 3
	case models.ActionTypePublish:
		priority = 5
	case models.ActionTypeUpdate:
		priority = 2
	case models.ActionTypeDelete:
		priority = 1
	case models.ActionTypeArchive:
		priority = 1
	}

	// Adjust by entity type
	switch entityType {
	case "rule", "notice", "proposed_rule":
		priority += 2
	case "agency":
		priority += 1
	}

	return priority
}

func (s *SummaryService) calculateRegulationPriority(action models.ActionType, isSignificant bool) int {
	priority := s.calculatePriority(action, "regulation")

	if isSignificant {
		priority += 3
	}

	return priority
}

func (s *SummaryService) getPublicationDate(docPublicationDate *time.Time, action models.ActionType) *time.Time {
	// For published documents, use their publication date
	if action == models.ActionTypePublish && docPublicationDate != nil {
		return docPublicationDate
	}

	// For other actions, publish immediately
	now := time.Now()
	return &now
}

func (s *SummaryService) getRegulationPublicationDate(effectiveDate *time.Time, action models.ActionType) *time.Time {
	// For regulations, use effective date if available and in the future
	if effectiveDate != nil && effectiveDate.After(time.Now()) {
		return effectiveDate
	}

	// Otherwise publish immediately
	now := time.Now()
	return &now
}

// Enhanced auto-generation methods for intelligent summaries

// CreateIntelligentSummary creates a context-aware summary with enhanced content
func (s *SummaryService) CreateIntelligentSummary(entityType models.EntityType, entityID uint, action models.ActionType, userID uint) error {
	switch entityType {
	case models.EntityTypeDocument:
		return s.createIntelligentDocumentSummary(entityID, action, userID)
	case models.EntityTypeRegulation:
		return s.createIntelligentRegulationSummary(entityID, action, userID)
	case models.EntityTypeTask:
		return s.createIntelligentTaskSummary(entityID, action, userID)
	case models.EntityTypeAgency:
		return s.createIntelligentAgencySummary(entityID, action, userID)
	case models.EntityTypeCategory:
		return s.createIntelligentCategorySummary(entityID, action, userID)
	}
	return nil
}

// createIntelligentDocumentSummary creates enhanced document summaries
func (s *SummaryService) createIntelligentDocumentSummary(documentID uint, action models.ActionType, userID uint) error {
	var document models.Document
	if err := s.db.Preload("Agency").Preload("Categories").Preload("CreatedBy").First(&document, documentID).Error; err != nil {
		return err
	}

	// Generate intelligent title and content
	title := s.generateIntelligentDocumentTitle(&document, action)
	content := s.generateIntelligentDocumentContent(&document, action)
	abstract := s.generateIntelligentDocumentAbstract(&document, action)

	// Determine priority based on document characteristics
	priority := s.calculateIntelligentPriority(&document, action)

	// Get related entities for context
	relatedInfo := s.getDocumentRelatedInfo(&document)

	summary := &models.Summary{
		Title:           title,
		Content:         content,
		Abstract:        abstract,
		SummaryType:     s.determineSummaryType(&document, action),
		EntityType:      models.EntityTypeDocument,
		EntityID:        document.ID,
		ActionType:      action,
		PublicationDate: s.getPublicationDate(document.PublicationDate, action),
		IsPublic:        document.IsPublic,
		IsFeatured:      s.shouldBeFeatured(&document, action),
		Priority:        priority,
		CreatedByID:     userID,
		AgencyID:        &document.AgencyID,
		Tags:            s.generateIntelligentTags(&document, relatedInfo),
	}

	// Set category if document has categories
	if len(document.Categories) > 0 {
		summary.CategoryID = &document.Categories[0].ID
	}

	return s.db.Create(summary).Error
}

// createIntelligentRegulationSummary creates enhanced regulation summaries
func (s *SummaryService) createIntelligentRegulationSummary(regulationID uint, action models.ActionType, userID uint) error {
	var regulation models.LawsAndRules
	if err := s.db.Preload("Agency").Preload("CreatedBy").First(&regulation, regulationID).Error; err != nil {
		return err
	}

	title := s.generateIntelligentRegulationTitle(&regulation, action)
	content := s.generateIntelligentRegulationContent(&regulation, action)
	abstract := s.generateIntelligentRegulationAbstract(&regulation, action)

	// Get impact analysis
	impactInfo := s.getRegulationImpactInfo(&regulation)

	summary := &models.Summary{
		Title:           title,
		Content:         content,
		Abstract:        abstract,
		SummaryType:     models.SummaryTypeNews,
		EntityType:      models.EntityTypeRegulation,
		EntityID:        regulation.ID,
		ActionType:      action,
		PublicationDate: s.getRegulationPublicationDate(regulation.EffectiveDate, action),
		IsPublic:        true,
		IsFeatured:      regulation.IsSignificant,
		Priority:        s.calculateRegulationPriority(action, regulation.IsSignificant),
		CreatedByID:     userID,
		AgencyID:        &regulation.AgencyID,
		Tags:            s.generateRegulationTags(&regulation, impactInfo),
	}

	return s.db.Create(summary).Error
}

// createIntelligentTaskSummary creates summaries for task-related activities
func (s *SummaryService) createIntelligentTaskSummary(taskID uint, action models.ActionType, userID uint) error {
	var task models.Task
	if err := s.db.Preload("CreatedBy").Preload("AssignedTo").Preload("Agency").Preload("Document").First(&task, taskID).Error; err != nil {
		return err
	}

	// Only create summaries for significant task events
	if !s.isSignificantTaskEvent(&task, action) {
		return nil
	}

	title := s.generateTaskSummaryTitle(&task, action)
	content := s.generateTaskSummaryContent(&task, action)
	abstract := s.generateTaskSummaryAbstract(&task, action)

	summary := &models.Summary{
		Title:       title,
		Content:     content,
		Abstract:    abstract,
		SummaryType: models.SummaryTypeUpdate,
		EntityType:  "task",
		EntityID:    task.ID,
		ActionType:  action,
		IsPublic:    task.IsPublic,
		IsFeatured:  task.Priority == models.TaskPriorityHigh,
		Priority:    s.calculateTaskSummaryPriority(&task, action),
		CreatedByID: userID,
		AgencyID:    task.AgencyID,
		Tags:        s.generateTaskTags(&task),
	}

	return s.db.Create(summary).Error
}

// Helper methods for intelligent content generation

// generateIntelligentDocumentTitle creates context-aware document titles
func (s *SummaryService) generateIntelligentDocumentTitle(document *models.Document, action models.ActionType) string {
	actionName := s.getActionName(action)

	// Include agency name for context
	agencyContext := ""
	if document.Agency.Name != "" {
		agencyContext = fmt.Sprintf(" by %s", document.Agency.Name)
	}

	// Include document type and significance
	typeContext := string(document.Type)
	if document.SignificantRule {
		typeContext = "Significant " + typeContext
	}

	return fmt.Sprintf("%s: %s %s%s", actionName, typeContext, document.Title, agencyContext)
}

// generateIntelligentDocumentContent creates detailed document content
func (s *SummaryService) generateIntelligentDocumentContent(document *models.Document, action models.ActionType) string {
	var content strings.Builder

	// Basic information
	content.WriteString(fmt.Sprintf("Document Type: %s\n", document.Type))
	content.WriteString(fmt.Sprintf("Agency: %s\n", document.Agency.Name))

	if document.PublicationDate != nil {
		content.WriteString(fmt.Sprintf("Publication Date: %s\n", document.PublicationDate.Format("January 2, 2006")))
	}

	if document.EffectiveDate != nil {
		content.WriteString(fmt.Sprintf("Effective Date: %s\n", document.EffectiveDate.Format("January 2, 2006")))
	}

	// Document details
	if document.Abstract != "" {
		content.WriteString(fmt.Sprintf("\nAbstract: %s\n", document.Abstract))
	}

	// Regulatory information
	if document.RegulatoryIdentifier != "" {
		content.WriteString(fmt.Sprintf("RIN: %s\n", document.RegulatoryIdentifier))
	}

	if document.DocketNumber != "" {
		content.WriteString(fmt.Sprintf("Docket: %s\n", document.DocketNumber))
	}

	// Impact information
	if document.SignificantRule {
		content.WriteString("This is a significant regulatory action.\n")
	}

	if document.EconomicImpact != "" {
		content.WriteString(fmt.Sprintf("Economic Impact: %s\n", document.EconomicImpact))
	}

	// Public participation
	if document.AcceptsComments {
		content.WriteString("Public comments are being accepted.\n")
		if document.CommentDueDate != nil {
			content.WriteString(fmt.Sprintf("Comments due by: %s\n", document.CommentDueDate.Format("January 2, 2006")))
		}
	}

	return content.String()
}

// Missing helper methods for intelligent summary generation

// generateIntelligentDocumentAbstract creates context-aware abstracts
func (s *SummaryService) generateIntelligentDocumentAbstract(document *models.Document, action models.ActionType) string {
	actionName := strings.ToLower(s.getActionName(action))

	abstract := fmt.Sprintf("A %s has been %s by %s", string(document.Type), actionName, document.Agency.Name)

	if document.Abstract != "" {
		abstract += fmt.Sprintf(": %s", document.Abstract)
	}

	if document.EffectiveDate != nil {
		abstract += fmt.Sprintf(" This document becomes effective on %s.", document.EffectiveDate.Format("January 2, 2006"))
	}

	if document.SignificantRule {
		abstract += " This is a significant regulatory action."
	}

	return abstract
}

// calculateIntelligentPriority determines priority based on document characteristics
func (s *SummaryService) calculateIntelligentPriority(document *models.Document, action models.ActionType) int {
	priority := s.calculatePriority(action, string(document.Type))

	// Boost priority for significant rules
	if document.SignificantRule {
		priority += 3
	}

	// Boost priority for documents with economic impact
	if document.EconomicImpact != "" {
		priority += 2
	}

	// Boost priority for documents accepting comments
	if document.AcceptsComments {
		priority += 1
	}

	return priority
}

// getDocumentRelatedInfo gathers related entity information
func (s *SummaryService) getDocumentRelatedInfo(document *models.Document) map[string]interface{} {
	info := make(map[string]interface{})

	// Count related tasks
	var taskCount int64
	s.db.Model(&models.Task{}).Where("document_id = ?", document.ID).Count(&taskCount)
	info["task_count"] = taskCount

	// Count related finance records
	var financeCount int64
	s.db.Model(&models.Finance{}).Where("document_id = ?", document.ID).Count(&financeCount)
	info["finance_count"] = financeCount

	// Get related regulations
	var regulationCount int64
	s.db.Model(&models.RegulationDocumentRelationship{}).Where("document_id = ? AND is_active = ?", document.ID, true).Count(&regulationCount)
	info["regulation_count"] = regulationCount

	return info
}

// determineSummaryType determines the appropriate summary type
func (s *SummaryService) determineSummaryType(document *models.Document, action models.ActionType) models.SummaryType {
	switch action {
	case models.ActionTypePublish:
		return models.SummaryTypeNews
	case models.ActionTypeCreate:
		return models.SummaryTypeAnnouncement
	case models.ActionTypeUpdate:
		return models.SummaryTypeUpdate
	default:
		return models.SummaryTypeNews
	}
}

// shouldBeFeatured determines if a summary should be featured
func (s *SummaryService) shouldBeFeatured(document *models.Document, action models.ActionType) bool {
	return document.SignificantRule || action == models.ActionTypePublish
}

// generateIntelligentTags creates relevant tags
func (s *SummaryService) generateIntelligentTags(document *models.Document, relatedInfo map[string]interface{}) string {
	var tags []string

	tags = append(tags, string(document.Type))
	tags = append(tags, document.Agency.Name)

	if document.SignificantRule {
		tags = append(tags, "significant")
	}

	if document.AcceptsComments {
		tags = append(tags, "public-comment")
	}

	if len(document.Categories) > 0 {
		tags = append(tags, document.Categories[0].Name)
	}

	return strings.Join(tags, ",")
}

// generateIntelligentRegulationTitle creates regulation titles
func (s *SummaryService) generateIntelligentRegulationTitle(regulation *models.LawsAndRules, action models.ActionType) string {
	actionName := s.getActionName(action)

	title := fmt.Sprintf("%s: %s", actionName, regulation.Title)

	if regulation.IsSignificant {
		title = "Significant Regulation " + title
	}

	return title
}

// generateIntelligentRegulationContent creates regulation content
func (s *SummaryService) generateIntelligentRegulationContent(regulation *models.LawsAndRules, action models.ActionType) string {
	var content strings.Builder

	content.WriteString(fmt.Sprintf("Regulation: %s\n", regulation.Title))
	content.WriteString(fmt.Sprintf("Agency: %s\n", regulation.Agency.Name))

	if regulation.EffectiveDate != nil {
		content.WriteString(fmt.Sprintf("Effective Date: %s\n", regulation.EffectiveDate.Format("January 2, 2006")))
	}

	if regulation.Description != "" {
		content.WriteString(fmt.Sprintf("\nDescription: %s\n", regulation.Description))
	}

	if regulation.IsSignificant {
		content.WriteString("This is a significant regulation.\n")
	}

	return content.String()
}

// generateIntelligentRegulationAbstract creates regulation abstracts
func (s *SummaryService) generateIntelligentRegulationAbstract(regulation *models.LawsAndRules, action models.ActionType) string {
	actionName := strings.ToLower(s.getActionName(action))

	abstract := fmt.Sprintf("A regulation has been %s by %s", actionName, regulation.Agency.Name)

	if regulation.Description != "" {
		abstract += fmt.Sprintf(": %s", regulation.Description)
	}

	if regulation.EffectiveDate != nil {
		abstract += fmt.Sprintf(" This regulation becomes effective on %s.", regulation.EffectiveDate.Format("January 2, 2006"))
	}

	return abstract
}

// getRegulationImpactInfo gathers regulation impact information
func (s *SummaryService) getRegulationImpactInfo(regulation *models.LawsAndRules) map[string]interface{} {
	info := make(map[string]interface{})

	// Count related documents
	var docCount int64
	s.db.Model(&models.RegulationDocumentRelationship{}).Where("regulation_id = ? AND is_active = ?", regulation.ID, true).Count(&docCount)
	info["document_count"] = docCount

	// Count related tasks
	var taskCount int64
	s.db.Model(&models.Task{}).Where("regulation_id = ?", regulation.ID).Count(&taskCount)
	info["task_count"] = taskCount

	return info
}

// generateRegulationTags creates regulation tags
func (s *SummaryService) generateRegulationTags(regulation *models.LawsAndRules, impactInfo map[string]interface{}) string {
	var tags []string

	tags = append(tags, "regulation")
	tags = append(tags, regulation.Agency.Name)

	if regulation.IsSignificant {
		tags = append(tags, "significant")
	}

	return strings.Join(tags, ",")
}

// Additional missing methods for task and other entity summaries

// isSignificantTaskEvent determines if a task event is worth summarizing
func (s *SummaryService) isSignificantTaskEvent(task *models.Task, action models.ActionType) bool {
	// Only summarize high priority tasks or completion events
	return task.Priority == models.TaskPriorityHigh || action == models.ActionTypeUpdate
}

// generateTaskSummaryTitle creates task summary titles
func (s *SummaryService) generateTaskSummaryTitle(task *models.Task, action models.ActionType) string {
	actionName := s.getActionName(action)
	return fmt.Sprintf("Task %s: %s", actionName, task.Title)
}

// generateTaskSummaryContent creates task summary content
func (s *SummaryService) generateTaskSummaryContent(task *models.Task, action models.ActionType) string {
	var content strings.Builder

	content.WriteString(fmt.Sprintf("Task: %s\n", task.Title))
	content.WriteString(fmt.Sprintf("Type: %s\n", task.Type))
	content.WriteString(fmt.Sprintf("Priority: %s\n", task.Priority))
	content.WriteString(fmt.Sprintf("Status: %s\n", task.Status))

	if task.DueDate != nil {
		content.WriteString(fmt.Sprintf("Due Date: %s\n", task.DueDate.Format("January 2, 2006")))
	}

	if task.Description != "" {
		content.WriteString(fmt.Sprintf("Description: %s\n", task.Description))
	}

	return content.String()
}

// generateTaskSummaryAbstract creates task summary abstracts
func (s *SummaryService) generateTaskSummaryAbstract(task *models.Task, action models.ActionType) string {
	actionName := strings.ToLower(s.getActionName(action))
	return fmt.Sprintf("Task '%s' has been %s with priority %s", task.Title, actionName, task.Priority)
}

// calculateTaskSummaryPriority calculates task summary priority
func (s *SummaryService) calculateTaskSummaryPriority(task *models.Task, action models.ActionType) int {
	priority := 1

	switch task.Priority {
	case models.TaskPriorityHigh:
		priority = 5
	case models.TaskPriorityMedium:
		priority = 3
	case models.TaskPriorityLow:
		priority = 1
	}

	if action == models.ActionTypeUpdate {
		priority += 1
	}

	return priority
}

// generateTaskTags creates task tags
func (s *SummaryService) generateTaskTags(task *models.Task) string {
	var tags []string

	tags = append(tags, "task")
	tags = append(tags, string(task.Type))
	tags = append(tags, string(task.Priority))

	if task.SourceType != "" {
		tags = append(tags, task.SourceType)
	}

	return strings.Join(tags, ",")
}

// createIntelligentAgencySummary creates agency summaries
func (s *SummaryService) createIntelligentAgencySummary(agencyID uint, action models.ActionType, userID uint) error {
	var agency models.Agency
	if err := s.db.First(&agency, agencyID).Error; err != nil {
		return err
	}

	title := fmt.Sprintf("Agency %s: %s", s.getActionName(action), agency.Name)
	content := fmt.Sprintf("Agency: %s\nDescription: %s", agency.Name, agency.Description)
	abstract := fmt.Sprintf("Agency '%s' has been %s", agency.Name, strings.ToLower(s.getActionName(action)))

	summary := &models.Summary{
		Title:       title,
		Content:     content,
		Abstract:    abstract,
		SummaryType: models.SummaryTypeAnnouncement,
		EntityType:  models.EntityTypeAgency,
		EntityID:    agency.ID,
		ActionType:  action,
		IsPublic:    true,
		IsFeatured:  false,
		Priority:    s.calculatePriority(action, "agency"),
		CreatedByID: userID,
		AgencyID:    &agency.ID,
		Tags:        "agency," + agency.Name,
	}

	return s.db.Create(summary).Error
}

// createIntelligentCategorySummary creates category summaries
func (s *SummaryService) createIntelligentCategorySummary(categoryID uint, action models.ActionType, userID uint) error {
	var category models.Category
	if err := s.db.First(&category, categoryID).Error; err != nil {
		return err
	}

	title := fmt.Sprintf("Category %s: %s", s.getActionName(action), category.Name)
	content := fmt.Sprintf("Category: %s\nDescription: %s", category.Name, category.Description)
	abstract := fmt.Sprintf("Category '%s' has been %s", category.Name, strings.ToLower(s.getActionName(action)))

	summary := &models.Summary{
		Title:       title,
		Content:     content,
		Abstract:    abstract,
		SummaryType: models.SummaryTypeUpdate,
		EntityType:  models.EntityTypeCategory,
		EntityID:    category.ID,
		ActionType:  action,
		IsPublic:    true,
		IsFeatured:  false,
		Priority:    s.calculatePriority(action, "category"),
		CreatedByID: userID,
		CategoryID:  &category.ID,
		Tags:        "category," + category.Name,
	}

	return s.db.Create(summary).Error
}
