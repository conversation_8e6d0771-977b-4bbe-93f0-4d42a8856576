package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// DeleteBudget deletes a budget
func DeleteBudget(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid budget ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var budget models.BudgetPlan
	if err := db.First(&budget, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Budget not found"})
		return
	}

	if err := db.Delete(&budget).Error; err != nil {
		HandleInternalError(c, "Failed to delete budget: "+err.Error())
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{"message": "Budget deleted successfully"})
}

// ApproveBudget approves a budget
func ApproveBudget(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid budget ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var budget models.BudgetPlan
	if err := db.First(&budget, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Budget not found"})
		return
	}

	// Mark as approved
	now := time.Now()
	budget.Status = "approved"
	budget.ApprovedAt = &now

	if err := db.Save(&budget).Error; err != nil {
		HandleInternalError(c, "Failed to approve budget: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Account").Preload("ApprovedBy").First(&budget, budget.ID)

	c.JSON(http.StatusOK, gin.H{"budget": budget})
}

// GetBudgetVarianceAnalysis returns budget variance analysis
func GetBudgetVarianceAnalysis(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get variance analysis data
	var analysis []struct {
		BudgetName      string  `json:"budget_name"`
		PlannedAmount   float64 `json:"planned_amount"`
		ActualAmount    float64 `json:"actual_amount"`
		VarianceAmount  float64 `json:"variance_amount"`
		VariancePercent float64 `json:"variance_percent"`
		Status          string  `json:"status"`
	}

	if err := db.Model(&models.BudgetPlan{}).
		Select("budget_name, planned_amount, actual_amount, variance_amount, variance_percent, status").
		Where("status = ?", "approved").
		Find(&analysis).Error; err != nil {
		HandleInternalError(c, "Failed to fetch variance analysis: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"analysis": analysis})
}

// Cost Center Handlers

// GetCostCenters returns all cost centers with pagination
func GetCostCenters(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total cost centers
	var total int64
	db.Model(&models.CostCenter{}).Count(&total)

	// Get cost centers with pagination
	var costCenters []models.CostCenter
	offset := (page - 1) * perPage
	if err := db.Preload("Manager").
		Offset(offset).
		Limit(perPage).
		Order("cost_center_code ASC").
		Find(&costCenters).Error; err != nil {
		HandleInternalError(c, "Failed to fetch cost centers: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"cost_centers": costCenters,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateCostCenter creates a new cost center
func CreateCostCenter(c *gin.Context) {
	var req CostCenterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create cost center
	costCenter := models.CostCenter{
		CostCenterCode:   req.CostCenterCode,
		Name:             req.CostCenterName,
		Description:      req.Description,
		ManagerID:        req.ManagerID,
		BudgetAmount:     req.BudgetAmount,
		ActualAmount:     req.ActualAmount,
		CommittedAmount:  req.CommittedAmount,
		AllocationMethod: req.AllocationMethod,
		AllocationBase:   req.AllocationBase,
		AllocationRate:   req.AllocationRate,
		IsActive:         req.IsActive,
		Metadata:         req.Metadata,
	}

	if err := db.Create(&costCenter).Error; err != nil {
		HandleInternalError(c, "Failed to create cost center: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Manager").First(&costCenter, costCenter.ID)

	c.JSON(http.StatusCreated, gin.H{"cost_center": costCenter})
}

// GetCostCenter returns a specific cost center
func GetCostCenter(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid cost center ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var costCenter models.CostCenter
	if err := db.Preload("Manager").First(&costCenter, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Cost center not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"cost_center": costCenter})
}

// UpdateCostCenter updates a cost center
func UpdateCostCenter(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid cost center ID"})
		return
	}

	var req CostCenterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var costCenter models.CostCenter
	if err := db.First(&costCenter, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Cost center not found"})
		return
	}

	// Update cost center fields
	costCenter.CostCenterCode = req.CostCenterCode
	costCenter.Name = req.CostCenterName
	costCenter.Description = req.Description
	costCenter.ManagerID = req.ManagerID
	costCenter.BudgetAmount = req.BudgetAmount
	costCenter.ActualAmount = req.ActualAmount
	costCenter.CommittedAmount = req.CommittedAmount
	costCenter.AllocationMethod = req.AllocationMethod
	costCenter.AllocationBase = req.AllocationBase
	costCenter.AllocationRate = req.AllocationRate
	costCenter.IsActive = req.IsActive
	costCenter.Metadata = req.Metadata

	if err := db.Save(&costCenter).Error; err != nil {
		HandleInternalError(c, "Failed to update cost center: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Manager").First(&costCenter, costCenter.ID)

	c.JSON(http.StatusOK, gin.H{"cost_center": costCenter})
}

// DeleteCostCenter deletes a cost center
func DeleteCostCenter(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid cost center ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var costCenter models.CostCenter
	if err := db.First(&costCenter, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Cost center not found"})
		return
	}

	if err := db.Delete(&costCenter).Error; err != nil {
		HandleInternalError(c, "Failed to delete cost center: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Cost center deleted successfully"})
}

// Financial Reports Handlers

// GetFinancialReports returns all financial reports with pagination
func GetFinancialReports(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total reports
	var total int64
	db.Model(&models.FinancialReport{}).Count(&total)

	// Get reports with pagination
	var reports []models.FinancialReport
	offset := (page - 1) * perPage
	if err := db.Preload("GeneratedBy").Preload("ReviewedBy").
		Offset(offset).
		Limit(perPage).
		Order("created_at DESC").
		Find(&reports).Error; err != nil {
		HandleInternalError(c, "Failed to fetch financial reports: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"reports": reports,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// GenerateFinancialReport creates a new financial report
func GenerateFinancialReport(c *gin.Context) {
	var req FinancialReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create financial report
	report := models.FinancialReport{
		ReportCode:       req.ReportCode,
		ReportName:       req.ReportName,
		ReportType:       req.ReportType,
		Description:      req.Description,
		PeriodType:       req.PeriodType,
		StartDate:        req.StartDate,
		EndDate:          req.EndDate,
		GeneratedByID:    req.GeneratedByID,
		ReportData:       req.ReportData,
		ReportFormat:     req.ReportFormat,
		FilePath:         req.FilePath,
		Status:           req.Status,
		DistributionList: req.DistributionList,
		Metadata:         req.Metadata,
	}

	if err := db.Create(&report).Error; err != nil {
		HandleInternalError(c, "Failed to generate financial report: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("GeneratedBy").Preload("ReviewedBy").First(&report, report.ID)

	c.JSON(http.StatusCreated, gin.H{"report": report})
}

// GetFinancialReport returns a specific financial report
func GetFinancialReport(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var report models.FinancialReport
	if err := db.Preload("GeneratedBy").Preload("ReviewedBy").First(&report, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Financial report not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"report": report})
}

// UpdateFinancialReport updates a financial report
func UpdateFinancialReport(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	var req FinancialReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var report models.FinancialReport
	if err := db.First(&report, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Financial report not found"})
		return
	}

	// Update report fields
	report.ReportCode = req.ReportCode
	report.ReportName = req.ReportName
	report.ReportType = req.ReportType
	report.Description = req.Description
	report.PeriodType = req.PeriodType
	report.StartDate = req.StartDate
	report.EndDate = req.EndDate
	report.GeneratedByID = req.GeneratedByID
	report.ReportData = req.ReportData
	report.ReportFormat = req.ReportFormat
	report.FilePath = req.FilePath
	report.Status = req.Status
	report.DistributionList = req.DistributionList
	report.Metadata = req.Metadata

	if err := db.Save(&report).Error; err != nil {
		HandleInternalError(c, "Failed to update financial report: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("GeneratedBy").Preload("ReviewedBy").First(&report, report.ID)

	c.JSON(http.StatusOK, gin.H{"report": report})
}

// DeleteFinancialReport deletes a financial report
func DeleteFinancialReport(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var report models.FinancialReport
	if err := db.First(&report, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Financial report not found"})
		return
	}

	if err := db.Delete(&report).Error; err != nil {
		HandleInternalError(c, "Failed to delete financial report: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Financial report deleted successfully"})
}
