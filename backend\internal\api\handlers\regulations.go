package handlers

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GetPublicRegulations returns all public regulations
func GetPublicRegulations(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get all active regulations
	var regulations []models.LawsAndRules
	if err := db.Where("status != ?", models.RegulationStatusArchived).Order("title ASC").Find(&regulations).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch regulations",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	regulationResponses := make([]gin.H, len(regulations))
	for i, regulation := range regulations {
		regulationResponses[i] = gin.H{
			"id":          regulation.ID,
			"title":       regulation.Title,
			"short_title": regulation.ShortTitle,
			"description": regulation.Description,
			"type":        regulation.Type,
			"status":      regulation.Status,
			"created_at":  regulation.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Public regulations retrieved successfully",
		Data:    regulationResponses,
	})
}

// GetPublicRegulation returns a single public regulation by ID
func GetPublicRegulation(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get regulation
	var regulation models.LawsAndRules
	if err := db.Where("id = ? AND status != ?", id, models.RegulationStatusArchived).First(&regulation).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch regulation",
			Message: err.Error(),
		})
		return
	}

	response := gin.H{
		"id":          regulation.ID,
		"title":       regulation.Title,
		"short_title": regulation.ShortTitle,
		"description": regulation.Description,
		"content":     regulation.Content,
		"type":        regulation.Type,
		"status":      regulation.Status,
		"created_at":  regulation.CreatedAt,
		"updated_at":  regulation.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Public regulation retrieved successfully",
		Data:    response,
	})
}

// GetPublicRegulationVersions returns versions of a public regulation
func GetPublicRegulationVersions(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.Where("id = ? AND status != ?", id, models.RegulationStatusArchived).First(&regulation).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch regulation",
			Message: err.Error(),
		})
		return
	}

	// Get all versions for this regulation
	var versions []models.RegulationDocumentVersion
	if err := db.Where("law_rule_id = ?", id).
		Preload("CreatedBy").
		Order("created_at DESC").
		Find(&versions).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation versions: "+err.Error())
		return
	}

	// Transform versions for response
	var versionData []gin.H
	for _, version := range versions {
		versionInfo := gin.H{
			"id":                 version.ID,
			"version_number":     version.VersionNumber,
			"publication_date":   version.PublicationDate,
			"effective_date":     version.EffectiveDate,
			"is_official":        version.IsOfficial,
			"notes":              version.Notes,
			"summary_of_changes": version.SummaryOfChanges,
			"created_at":         version.CreatedAt,
			"created_by": gin.H{
				"id":         version.CreatedBy.ID,
				"first_name": version.CreatedBy.FirstName,
				"last_name":  version.CreatedBy.LastName,
				"email":      version.CreatedBy.Email,
			},
		}
		versionData = append(versionData, versionInfo)
	}

	// Add version comparison capabilities
	response := gin.H{
		"regulation_id":   id,
		"regulation":      regulation.Title,
		"versions":        versionData,
		"total_versions":  len(versions),
		"current_version": regulation.CurrentDocumentVersionID,
		"version_capabilities": gin.H{
			"can_create_version":   true,
			"can_compare_versions": len(versions) > 1,
			"can_rollback":         len(versions) > 1,
			"supports_branching":   true,
		},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation versions retrieved successfully",
		Data:    response,
	})
}

// GetRegulationDefaults returns default values for regulation creation
func GetRegulationDefaults(c *gin.Context) {
	defaults := gin.H{
		"status":     "draft",
		"is_active":  true,
		"visibility": "public",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation defaults retrieved successfully",
		Data:    defaults,
	})
}

// GetRegulations returns all regulations (authenticated endpoint)
func GetRegulations(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query
	query := db.Model(&models.LawsAndRules{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ? OR content ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("type = ?", search.Type)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get regulations with relationships
	var regulations []models.LawsAndRules
	if err := query.Preload("Agency").Preload("CreatedBy").Preload("Parent").Find(&regulations).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulations: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       regulations,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// RegulationRequest represents a request to create or update a regulation
type RegulationRequest struct {
	Title                string `json:"title" binding:"required"`
	ShortTitle           string `json:"short_title"`
	Type                 string `json:"type" binding:"required"`
	Status               string `json:"status"`
	Description          string `json:"description"`
	Content              string `json:"content"`
	PublicLawNumber      string `json:"public_law_number"`
	RegulatoryIdentifier string `json:"regulatory_identifier"`
	CFRTitle             string `json:"cfr_title"`
	USCTitle             string `json:"usc_title"`
	DocketNumber         string `json:"docket_number"`
	EnactmentDate        string `json:"enactment_date"`
	EffectiveDate        string `json:"effective_date"`
	TerminationDate      string `json:"termination_date"`
	PublicationDate      string `json:"publication_date"`
	AgencyID             uint   `json:"agency_id" binding:"required"`
	Notes                string `json:"notes"`
	IsSignificant        bool   `json:"is_significant"`
	HierarchyLevel       string `json:"hierarchy_level"`
	ChapterNumber        string `json:"chapter_number"`
	Subchapter           string `json:"subchapter"`
	PartNumber           string `json:"part_number"`
	SectionNumber        string `json:"section_number"`
	Subsection           string `json:"subsection"`
	ParentID             *uint  `json:"parent_id"`
	OrderInParent        int    `json:"order_in_parent"`
}

// UpdateRegulationRequest represents a partial update request for regulations
type UpdateRegulationRequest struct {
	Title                *string `json:"title"`
	ShortTitle           *string `json:"short_title"`
	Type                 *string `json:"type"`
	Status               *string `json:"status"`
	Description          *string `json:"description"`
	Content              *string `json:"content"`
	PublicLawNumber      *string `json:"public_law_number"`
	RegulatoryIdentifier *string `json:"regulatory_identifier"`
	CFRTitle             *string `json:"cfr_title"`
	USCTitle             *string `json:"usc_title"`
	DocketNumber         *string `json:"docket_number"`
	EnactmentDate        *string `json:"enactment_date"`
	EffectiveDate        *string `json:"effective_date"`
	TerminationDate      *string `json:"termination_date"`
	PublicationDate      *string `json:"publication_date"`
	AgencyID             *uint   `json:"agency_id"`
	Notes                *string `json:"notes"`
	IsSignificant        *bool   `json:"is_significant"`
	HierarchyLevel       *string `json:"hierarchy_level"`
	ChapterNumber        *string `json:"chapter_number"`
	Subchapter           *string `json:"subchapter"`
	PartNumber           *string `json:"part_number"`
	SectionNumber        *string `json:"section_number"`
	Subsection           *string `json:"subsection"`
	ParentID             *uint   `json:"parent_id"`
	OrderInParent        *int    `json:"order_in_parent"`
}

// CreateRegulation creates a new regulation
func CreateRegulation(c *gin.Context) {
	var req RegulationRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Create regulation
	regulation := models.LawsAndRules{
		Title:                req.Title,
		ShortTitle:           req.ShortTitle,
		Type:                 models.RegulationType(req.Type),
		Status:               models.RegulationStatus(req.Status),
		Description:          req.Description,
		Content:              req.Content,
		PublicLawNumber:      req.PublicLawNumber,
		RegulatoryIdentifier: req.RegulatoryIdentifier,
		CFRTitle:             req.CFRTitle,
		USCTitle:             req.USCTitle,
		DocketNumber:         req.DocketNumber,
		AgencyID:             req.AgencyID,
		CreatedByID:          userID.(uint),
		Notes:                req.Notes,
		IsSignificant:        req.IsSignificant,
		HierarchyLevel:       req.HierarchyLevel,
		ChapterNumber:        req.ChapterNumber,
		Subchapter:           req.Subchapter,
		PartNumber:           req.PartNumber,
		SectionNumber:        req.SectionNumber,
		Subsection:           req.Subsection,
		ParentID:             req.ParentID,
		OrderInParent:        req.OrderInParent,
	}

	// Parse dates if provided
	if req.EnactmentDate != "" {
		if date, err := time.Parse("2006-01-02", req.EnactmentDate); err == nil {
			regulation.EnactmentDate = &date
		}
	}
	if req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			regulation.EffectiveDate = &date
		}
	}
	if req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			regulation.TerminationDate = &date
		}
	}
	if req.PublicationDate != "" {
		if date, err := time.Parse("2006-01-02", req.PublicationDate); err == nil {
			regulation.PublicationDate = &date
		}
	}

	if err := db.Create(&regulation).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Agency").Preload("CreatedBy").Preload("Parent").First(&regulation, regulation.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation created successfully",
		Data:    regulation,
	})
}

// GetRegulation returns a specific regulation
func GetRegulation(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get regulation with relationships
	var regulation models.LawsAndRules
	if err := db.Preload("Agency").Preload("CreatedBy").Preload("Parent").Preload("Children").
		Preload("DocumentVersions").Preload("Chunks").First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation retrieved successfully",
		Data:    regulation,
	})
}

// UpdateRegulation updates a regulation
func UpdateRegulation(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req UpdateRegulationRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing regulation
	var regulation models.LawsAndRules
	if err := db.First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Update regulation fields only if provided
	if req.Title != nil {
		if strings.TrimSpace(*req.Title) == "" {
			HandleBadRequest(c, "Title cannot be empty")
			return
		}
		regulation.Title = strings.TrimSpace(*req.Title)
	}
	if req.ShortTitle != nil {
		regulation.ShortTitle = *req.ShortTitle
	}
	if req.Type != nil {
		regulation.Type = models.RegulationType(*req.Type)
	}
	if req.Status != nil {
		regulation.Status = models.RegulationStatus(*req.Status)
	}
	if req.Description != nil {
		regulation.Description = *req.Description
	}
	if req.Content != nil {
		regulation.Content = *req.Content
	}
	if req.PublicLawNumber != nil {
		regulation.PublicLawNumber = *req.PublicLawNumber
	}
	if req.RegulatoryIdentifier != nil {
		regulation.RegulatoryIdentifier = *req.RegulatoryIdentifier
	}
	if req.CFRTitle != nil {
		regulation.CFRTitle = *req.CFRTitle
	}
	if req.USCTitle != nil {
		regulation.USCTitle = *req.USCTitle
	}
	if req.DocketNumber != nil {
		regulation.DocketNumber = *req.DocketNumber
	}
	if req.AgencyID != nil {
		regulation.AgencyID = *req.AgencyID
	}
	if req.Notes != nil {
		regulation.Notes = *req.Notes
	}
	if req.IsSignificant != nil {
		regulation.IsSignificant = *req.IsSignificant
	}
	if req.HierarchyLevel != nil {
		regulation.HierarchyLevel = *req.HierarchyLevel
	}
	if req.ChapterNumber != nil {
		regulation.ChapterNumber = *req.ChapterNumber
	}
	if req.Subchapter != nil {
		regulation.Subchapter = *req.Subchapter
	}
	if req.PartNumber != nil {
		regulation.PartNumber = *req.PartNumber
	}
	if req.SectionNumber != nil {
		regulation.SectionNumber = *req.SectionNumber
	}
	if req.Subsection != nil {
		regulation.Subsection = *req.Subsection
	}
	if req.ParentID != nil {
		regulation.ParentID = req.ParentID
	}
	if req.OrderInParent != nil {
		regulation.OrderInParent = *req.OrderInParent
	}

	// Parse dates if provided
	if req.EnactmentDate != nil && *req.EnactmentDate != "" {
		if date, err := time.Parse("2006-01-02", *req.EnactmentDate); err == nil {
			regulation.EnactmentDate = &date
		}
	}
	if req.EffectiveDate != nil && *req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", *req.EffectiveDate); err == nil {
			regulation.EffectiveDate = &date
		}
	}
	if req.TerminationDate != nil && *req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", *req.TerminationDate); err == nil {
			regulation.TerminationDate = &date
		}
	}
	if req.PublicationDate != nil && *req.PublicationDate != "" {
		if date, err := time.Parse("2006-01-02", *req.PublicationDate); err == nil {
			regulation.PublicationDate = &date
		}
	}

	if err := db.Save(&regulation).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Agency").Preload("CreatedBy").Preload("Parent").First(&regulation, regulation.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation updated successfully",
		Data:    regulation,
	})
}

// DeleteRegulation deletes a regulation
func DeleteRegulation(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Check for dependencies (children regulations)
	var childCount int64
	db.Model(&models.LawsAndRules{}).Where("parent_id = ?", id).Count(&childCount)
	if childCount > 0 {
		HandleBadRequest(c, "Cannot delete regulation with child regulations")
		return
	}

	// Delete regulation
	if err := db.Delete(&regulation).Error; err != nil {
		HandleInternalError(c, "Failed to delete regulation: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation deleted successfully",
	})
}

// AmendRegulationChunkRequest represents a request to amend a regulation chunk
type AmendRegulationChunkRequest struct {
	ChunkID           uint   `json:"chunk_id" binding:"required"`
	NewContent        string `json:"new_content" binding:"required"`
	ChangeDescription string `json:"change_description" binding:"required"`
	EffectiveDate     string `json:"effective_date"`
}

// AmendRegulationChunk amends a regulation chunk
func AmendRegulationChunk(c *gin.Context) {
	var req AmendRegulationChunkRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing chunk
	var chunk models.Chunk
	if err := db.First(&chunk, req.ChunkID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation chunk")
			return
		}
		HandleInternalError(c, "Failed to fetch chunk: "+err.Error())
		return
	}

	// Create new chunk content version
	newVersion := models.ChunkContentVersion{
		ChunkID:           req.ChunkID,
		Content:           req.NewContent,
		ModifiedByID:      userID.(uint),
		ChangeDescription: req.ChangeDescription,
	}

	if err := db.Create(&newVersion).Error; err != nil {
		HandleInternalError(c, "Failed to create chunk content version: "+err.Error())
		return
	}

	// Update chunk to point to new version
	chunk.CurrentChunkContentVersionID = &newVersion.ID
	if err := db.Save(&chunk).Error; err != nil {
		HandleInternalError(c, "Failed to update chunk: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("ModifiedBy").First(&newVersion, newVersion.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation chunk amended successfully",
		Data:    newVersion,
	})
}

// PublishRegulationVersionRequest represents a request to publish a regulation version
type PublishRegulationVersionRequest struct {
	RegulationID     uint   `json:"regulation_id" binding:"required"`
	VersionNumber    string `json:"version_number" binding:"required"`
	SummaryOfChanges string `json:"summary_of_changes"`
	IsOfficial       bool   `json:"is_official"`
	Notes            string `json:"notes"`
}

// PublishRegulationVersion publishes a regulation version
func PublishRegulationVersion(c *gin.Context) {
	var req PublishRegulationVersionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get regulation
	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Create new document version
	docVersion := models.RegulationDocumentVersion{
		LawRuleID:        req.RegulationID,
		VersionNumber:    req.VersionNumber,
		IsOfficial:       req.IsOfficial,
		CreatedByID:      userID.(uint),
		Notes:            req.Notes,
		SummaryOfChanges: req.SummaryOfChanges,
	}

	if err := db.Create(&docVersion).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation document version: "+err.Error())
		return
	}

	// Update regulation to point to new version if it's official
	if req.IsOfficial {
		regulation.CurrentDocumentVersionID = &docVersion.ID
		regulation.Status = models.RegulationStatusPublished
		if err := db.Save(&regulation).Error; err != nil {
			HandleInternalError(c, "Failed to update regulation: "+err.Error())
			return
		}
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("LawRule").First(&docVersion, docVersion.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation version published successfully",
		Data:    docVersion,
	})
}

// GetStructuredContent returns structured content
func GetStructuredContent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query for structured content related to regulation
	query := db.Model(&models.RegulationStructuredContent{}).Where("regulation_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Type != "" {
		query = query.Where("content_type = ?", search.Type)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("sort_order ASC, created_at DESC")
	}

	// Get structured content with relationships
	var structuredContent []models.RegulationStructuredContent
	if err := query.Preload("ParentSection").Find(&structuredContent).Error; err != nil {
		HandleInternalError(c, "Failed to fetch structured content: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       structuredContent,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// StructuredContentRequest represents a request to create or update structured content
type StructuredContentRequest struct {
	RegulationID    uint   `json:"regulation_id" binding:"required"`
	SectionNumber   string `json:"section_number" binding:"required"`
	Title           string `json:"title" binding:"required"`
	Content         string `json:"content" binding:"required"`
	ContentType     string `json:"content_type"`
	ParentSectionID *uint  `json:"parent_section_id"`
	Level           int    `json:"level"`
	SortOrder       int    `json:"sort_order"`
	IsActive        bool   `json:"is_active"`
}

// AddStructuredContent adds structured content
func AddStructuredContent(c *gin.Context) {
	var req StructuredContentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Set defaults
	if req.ContentType == "" {
		req.ContentType = "text"
	}
	if req.Level == 0 {
		req.Level = 1
	}

	// Create structured content
	structuredContent := models.RegulationStructuredContent{
		RegulationID:    req.RegulationID,
		SectionNumber:   req.SectionNumber,
		Title:           req.Title,
		Content:         req.Content,
		ContentType:     req.ContentType,
		ParentSectionID: req.ParentSectionID,
		Level:           req.Level,
		SortOrder:       req.SortOrder,
		IsActive:        req.IsActive,
	}

	if err := db.Create(&structuredContent).Error; err != nil {
		HandleInternalError(c, "Failed to create structured content: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("ParentSection").First(&structuredContent, structuredContent.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Structured content added successfully",
		Data:    structuredContent,
	})
}

// UpdateStructuredContent updates structured content
func UpdateStructuredContent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req StructuredContentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing structured content
	var structuredContent models.RegulationStructuredContent
	if err := db.First(&structuredContent, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Structured content")
			return
		}
		HandleInternalError(c, "Failed to fetch structured content: "+err.Error())
		return
	}

	// Update fields
	structuredContent.SectionNumber = req.SectionNumber
	structuredContent.Title = req.Title
	structuredContent.Content = req.Content
	structuredContent.ContentType = req.ContentType
	structuredContent.ParentSectionID = req.ParentSectionID
	structuredContent.Level = req.Level
	structuredContent.SortOrder = req.SortOrder
	structuredContent.IsActive = req.IsActive

	if err := db.Save(&structuredContent).Error; err != nil {
		HandleInternalError(c, "Failed to update structured content: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("ParentSection").First(&structuredContent, structuredContent.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Structured content updated successfully",
		Data:    structuredContent,
	})
}

// DeleteStructuredContent deletes structured content
func DeleteStructuredContent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if structured content exists
	var structuredContent models.RegulationStructuredContent
	if err := db.First(&structuredContent, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Structured content")
			return
		}
		HandleInternalError(c, "Failed to fetch structured content: "+err.Error())
		return
	}

	// Check for child sections
	var childCount int64
	db.Model(&models.RegulationStructuredContent{}).Where("parent_section_id = ?", id).Count(&childCount)
	if childCount > 0 {
		HandleBadRequest(c, "Cannot delete structured content with child sections")
		return
	}

	// Delete structured content
	if err := db.Delete(&structuredContent).Error; err != nil {
		HandleInternalError(c, "Failed to delete structured content: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Structured content deleted successfully",
	})
}

// GenerateDefaultStructuredContent generates default structured content
func GenerateDefaultStructuredContent(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get regulation
	var regulation models.LawsAndRules
	if err := db.First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Check if structured content already exists
	var existingCount int64
	db.Model(&models.RegulationStructuredContent{}).Where("regulation_id = ?", id).Count(&existingCount)
	if existingCount > 0 {
		HandleBadRequest(c, "Structured content already exists for this regulation")
		return
	}

	// Generate default structured content based on regulation type
	defaultSections := []models.RegulationStructuredContent{}

	switch regulation.Type {
	case models.RegulationTypeRule:
		defaultSections = []models.RegulationStructuredContent{
			{
				RegulationID:  id,
				SectionNumber: "1.1",
				Title:         "Purpose and Scope",
				Content:       "This section defines the purpose and scope of the regulation.",
				ContentType:   "text",
				Level:         1,
				SortOrder:     1,
				IsActive:      true,
			},
			{
				RegulationID:  id,
				SectionNumber: "1.2",
				Title:         "Definitions",
				Content:       "This section provides definitions of key terms used in the regulation.",
				ContentType:   "text",
				Level:         1,
				SortOrder:     2,
				IsActive:      true,
			},
			{
				RegulationID:  id,
				SectionNumber: "2.1",
				Title:         "Requirements",
				Content:       "This section outlines the specific requirements and obligations.",
				ContentType:   "text",
				Level:         1,
				SortOrder:     3,
				IsActive:      true,
			},
			{
				RegulationID:  id,
				SectionNumber: "3.1",
				Title:         "Compliance and Enforcement",
				Content:       "This section describes compliance requirements and enforcement mechanisms.",
				ContentType:   "text",
				Level:         1,
				SortOrder:     4,
				IsActive:      true,
			},
		}
	case models.RegulationTypeLaw:
		defaultSections = []models.RegulationStructuredContent{
			{
				RegulationID:  id,
				SectionNumber: "Section 1",
				Title:         "Short Title",
				Content:       "This Act may be cited as the [Act Name].",
				ContentType:   "text",
				Level:         1,
				SortOrder:     1,
				IsActive:      true,
			},
			{
				RegulationID:  id,
				SectionNumber: "Section 2",
				Title:         "Definitions",
				Content:       "In this Act, unless the context otherwise requires:",
				ContentType:   "text",
				Level:         1,
				SortOrder:     2,
				IsActive:      true,
			},
			{
				RegulationID:  id,
				SectionNumber: "Section 3",
				Title:         "General Provisions",
				Content:       "This section contains the general provisions of the Act.",
				ContentType:   "text",
				Level:         1,
				SortOrder:     3,
				IsActive:      true,
			},
		}
	default:
		defaultSections = []models.RegulationStructuredContent{
			{
				RegulationID:  id,
				SectionNumber: "1",
				Title:         "Introduction",
				Content:       "This section provides an introduction to the regulation.",
				ContentType:   "text",
				Level:         1,
				SortOrder:     1,
				IsActive:      true,
			},
			{
				RegulationID:  id,
				SectionNumber: "2",
				Title:         "Main Provisions",
				Content:       "This section contains the main provisions of the regulation.",
				ContentType:   "text",
				Level:         1,
				SortOrder:     2,
				IsActive:      true,
			},
		}
	}

	// Create the default sections
	createdSections := []models.RegulationStructuredContent{}
	for _, section := range defaultSections {
		if err := db.Create(&section).Error; err != nil {
			HandleInternalError(c, "Failed to create default structured content: "+err.Error())
			return
		}
		createdSections = append(createdSections, section)
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Default structured content generated successfully",
		Data: gin.H{
			"regulation_id":    id,
			"sections_created": len(createdSections),
			"created_sections": createdSections,
		},
	})
}

// GetRegulationRelationshipsSummary returns regulation relationships summary
func GetRegulationRelationshipsSummary(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get regulation
	var regulation models.LawsAndRules
	if err := db.First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get related documents count
	var documentCount int64
	db.Model(&models.Document{}).Where("regulation_id = ?", id).Count(&documentCount)

	// Get related agencies count
	var agencyCount int64
	db.Table("regulation_agencies").Where("regulation_id = ?", id).Count(&agencyCount)

	// Get related categories count
	var categoryCount int64
	db.Table("regulation_categories").Where("regulation_id = ?", id).Count(&categoryCount)

	// Get interconnections count
	var interconnectionCount int64
	db.Model(&models.Interconnect{}).Where("(source_type = ? AND source_id = ?) OR (target_type = ? AND target_id = ?)",
		"regulation", id, "regulation", id).Count(&interconnectionCount)

	summary := gin.H{
		"regulation_id":         regulation.ID,
		"regulation_title":      regulation.Title,
		"document_count":        documentCount,
		"agency_count":          agencyCount,
		"category_count":        categoryCount,
		"interconnection_count": interconnectionCount,
		"total_relationships":   documentCount + agencyCount + categoryCount + interconnectionCount,
		"generated_at":          time.Now(),
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation relationships summary retrieved successfully",
		Data:    summary,
	})
}

// GetRegulationDocuments returns documents for a regulation
func GetRegulationDocuments(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query for documents related to this regulation
	query := db.Model(&models.Document{}).Where("regulation_id = ?", id)

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("type = ?", search.Type)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get documents with relationships
	var documents []models.Document
	if err := query.Preload("Agency").Preload("Category").Preload("CreatedBy").Find(&documents).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation documents: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       documents,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// RegulationDocumentRelationshipRequest represents a request to create or update regulation document relationship
type RegulationDocumentRelationshipRequest struct {
	RegulationID     uint   `json:"regulation_id" binding:"required"`
	DocumentID       uint   `json:"document_id" binding:"required"`
	RelationshipType string `json:"relationship_type" binding:"required"`
	EffectiveDate    string `json:"effective_date"`
	TerminationDate  string `json:"termination_date"`
	Description      string `json:"description"`
	CitationText     string `json:"citation_text"`
	IsActive         bool   `json:"is_active"`
}

// CreateRegulationDocumentRelationship creates a regulation document relationship
func CreateRegulationDocumentRelationship(c *gin.Context) {
	var req RegulationDocumentRelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify regulation and document exist
	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	var document models.Document
	if err := db.First(&document, req.DocumentID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Create relationship
	relationship := models.RegulationDocumentRelationship{
		RegulationID:     req.RegulationID,
		DocumentID:       req.DocumentID,
		RelationshipType: models.DocumentRelationshipType(req.RelationshipType),
		Description:      req.Description,
		CitationText:     req.CitationText,
		CreatedByID:      userID.(uint),
		IsActive:         req.IsActive,
	}

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			relationship.EffectiveDate = &date
		}
	}
	if req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			relationship.TerminationDate = &date
		}
	}

	if err := db.Create(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation document relationship: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Regulation").Preload("Document").Preload("CreatedBy").First(&relationship, relationship.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation document relationship created successfully",
		Data:    relationship,
	})
}

// UpdateRegulationDocumentRelationship updates a regulation document relationship
func UpdateRegulationDocumentRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RegulationDocumentRelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing relationship
	var relationship models.RegulationDocumentRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation document relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Update fields
	relationship.RelationshipType = models.DocumentRelationshipType(req.RelationshipType)
	relationship.Description = req.Description
	relationship.CitationText = req.CitationText
	relationship.IsActive = req.IsActive

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			relationship.EffectiveDate = &date
		}
	}
	if req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			relationship.TerminationDate = &date
		}
	}

	if err := db.Save(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation document relationship: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Regulation").Preload("Document").Preload("CreatedBy").First(&relationship, relationship.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation document relationship updated successfully",
		Data:    relationship,
	})
}

// DeleteRegulationDocumentRelationship deletes a regulation document relationship
func DeleteRegulationDocumentRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if relationship exists
	var relationship models.RegulationDocumentRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation document relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Delete relationship
	if err := db.Delete(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to delete regulation document relationship: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation document relationship deleted successfully",
	})
}

// GetRegulationAgencies returns agencies for a regulation
func GetRegulationAgencies(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query for regulation agency relationships
	query := db.Model(&models.RegulationAgencyRelationship{}).Where("regulation_id = ?", id)

	// Apply filters
	if search.Type != "" {
		query = query.Where("relationship_type = ?", search.Type)
	}
	if search.Status != "" {
		query = query.Where("is_active = ?", search.Status == "active")
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get relationships with agencies
	var relationships []models.RegulationAgencyRelationship
	if err := query.Preload("Agency").Preload("Regulation").Preload("CreatedBy").Find(&relationships).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation agencies: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       relationships,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// RegulationAgencyRelationshipRequest represents a request to create or update regulation agency relationship
type RegulationAgencyRelationshipRequest struct {
	RegulationID     uint   `json:"regulation_id" binding:"required"`
	AgencyID         uint   `json:"agency_id" binding:"required"`
	RelationshipType string `json:"relationship_type" binding:"required"`
	EffectiveDate    string `json:"effective_date"`
	TerminationDate  string `json:"termination_date"`
	Description      string `json:"description"`
	AuthorityScope   string `json:"authority_scope"`
	IsActive         bool   `json:"is_active"`
}

// CreateRegulationAgencyRelationship creates a regulation agency relationship
func CreateRegulationAgencyRelationship(c *gin.Context) {
	var req RegulationAgencyRelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify regulation and agency exist
	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	var agency models.Agency
	if err := db.First(&agency, req.AgencyID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Agency")
			return
		}
		HandleInternalError(c, "Failed to fetch agency: "+err.Error())
		return
	}

	// Create relationship
	relationship := models.RegulationAgencyRelationship{
		RegulationID:     req.RegulationID,
		AgencyID:         req.AgencyID,
		RelationshipType: models.AgencyRelationshipType(req.RelationshipType),
		Description:      req.Description,
		AuthorityScope:   req.AuthorityScope,
		CreatedByID:      userID.(uint),
		IsActive:         req.IsActive,
	}

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			relationship.EffectiveDate = &date
		}
	}
	if req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			relationship.TerminationDate = &date
		}
	}

	if err := db.Create(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation agency relationship: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Regulation").Preload("Agency").Preload("CreatedBy").First(&relationship, relationship.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation agency relationship created successfully",
		Data:    relationship,
	})
}

// UpdateRegulationAgencyRelationship updates a regulation agency relationship
func UpdateRegulationAgencyRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RegulationAgencyRelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing relationship
	var relationship models.RegulationAgencyRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation agency relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Update fields
	relationship.RelationshipType = models.AgencyRelationshipType(req.RelationshipType)
	relationship.Description = req.Description
	relationship.AuthorityScope = req.AuthorityScope
	relationship.IsActive = req.IsActive

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			relationship.EffectiveDate = &date
		}
	}
	if req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			relationship.TerminationDate = &date
		}
	}

	if err := db.Save(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation agency relationship: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Regulation").Preload("Agency").Preload("CreatedBy").First(&relationship, relationship.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation agency relationship updated successfully",
		Data:    relationship,
	})
}

// DeleteRegulationAgencyRelationship deletes a regulation agency relationship
func DeleteRegulationAgencyRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if relationship exists
	var relationship models.RegulationAgencyRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation agency relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Delete relationship
	if err := db.Delete(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to delete regulation agency relationship: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation agency relationship deleted successfully",
	})
}

// RegulationCategoryRelationshipCreateRequest represents a request to create or update regulation category relationship
type RegulationCategoryRelationshipCreateRequest struct {
	RegulationID     uint   `json:"regulation_id" binding:"required"`
	CategoryID       uint   `json:"category_id" binding:"required"`
	RelationshipType string `json:"relationship_type" binding:"required"`
	EffectiveDate    string `json:"effective_date"`
	TerminationDate  string `json:"termination_date"`
	Description      string `json:"description"`
	ScopeDefinition  string `json:"scope_definition"`
	IsActive         bool   `json:"is_active"`
}

// CreateRegulationCategoryRelationship creates a regulation category relationship
func CreateRegulationCategoryRelationship(c *gin.Context) {
	var req RegulationCategoryRelationshipCreateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify regulation and category exist
	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	var category models.Category
	if err := db.First(&category, req.CategoryID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Category")
			return
		}
		HandleInternalError(c, "Failed to fetch category: "+err.Error())
		return
	}

	// Create relationship
	relationship := models.RegulationCategoryRelationship{
		RegulationID:     req.RegulationID,
		CategoryID:       req.CategoryID,
		RelationshipType: models.CategoryRelationshipType(req.RelationshipType),
		Description:      req.Description,
		ScopeDefinition:  req.ScopeDefinition,
		CreatedByID:      userID.(uint),
		IsActive:         req.IsActive,
	}

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			relationship.EffectiveDate = &date
		}
	}
	if req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			relationship.TerminationDate = &date
		}
	}

	if err := db.Create(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation category relationship: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Regulation").Preload("Category").Preload("CreatedBy").First(&relationship, relationship.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation category relationship created successfully",
		Data:    relationship,
	})
}

// UpdateRegulationCategoryRelationship updates a regulation category relationship
func UpdateRegulationCategoryRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RegulationCategoryRelationshipCreateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing relationship
	var relationship models.RegulationCategoryRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation category relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Update fields
	relationship.RelationshipType = models.CategoryRelationshipType(req.RelationshipType)
	relationship.Description = req.Description
	relationship.ScopeDefinition = req.ScopeDefinition
	relationship.IsActive = req.IsActive

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if date, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			relationship.EffectiveDate = &date
		}
	}
	if req.TerminationDate != "" {
		if date, err := time.Parse("2006-01-02", req.TerminationDate); err == nil {
			relationship.TerminationDate = &date
		}
	}

	if err := db.Save(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation category relationship: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Regulation").Preload("Category").Preload("CreatedBy").First(&relationship, relationship.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation category relationship updated successfully",
		Data:    relationship,
	})
}

// DeleteRegulationCategoryRelationship deletes a regulation category relationship
func DeleteRegulationCategoryRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if relationship exists
	var relationship models.RegulationCategoryRelationship
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation category relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Delete relationship
	if err := db.Delete(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to delete regulation category relationship: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation category relationship deleted successfully",
	})
}

// UpdateRegulationChunkMetadataRequest represents a request to update regulation chunk metadata
type UpdateRegulationChunkMetadataRequest struct {
	ChunkID  uint   `json:"chunk_id" binding:"required"`
	Metadata string `json:"metadata" binding:"required"`
}

// UpdateRegulationChunkMetadata updates regulation chunk metadata
func UpdateRegulationChunkMetadata(c *gin.Context) {
	var req UpdateRegulationChunkMetadataRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing chunk
	var chunk models.RegulationChunk
	if err := db.First(&chunk, req.ChunkID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation chunk")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation chunk: "+err.Error())
		return
	}

	// Update metadata
	chunk.Metadata = req.Metadata

	if err := db.Save(&chunk).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation chunk metadata: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Regulation").First(&chunk, chunk.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation chunk metadata updated successfully",
		Data:    chunk,
	})
}

// CreateRegulationVersion creates a new version of a regulation
func CreateRegulationVersion(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		VersionNumber    string `json:"version_number" binding:"required"`
		SummaryOfChanges string `json:"summary_of_changes" binding:"required"`
		Notes            string `json:"notes"`
		EffectiveDate    string `json:"effective_date"`
		IsOfficial       bool   `json:"is_official"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Check if version number already exists
	var existingVersion models.RegulationDocumentVersion
	if err := db.Where("law_rule_id = ? AND version_number = ?", id, req.VersionNumber).First(&existingVersion).Error; err == nil {
		HandleBadRequest(c, "Version number already exists for this regulation")
		return
	}

	// Parse effective date if provided
	var effectiveDate *time.Time
	if req.EffectiveDate != "" {
		if parsed, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			effectiveDate = &parsed
		}
	}

	// Create new version
	version := models.RegulationDocumentVersion{
		LawRuleID:        uint(id),
		VersionNumber:    req.VersionNumber,
		SummaryOfChanges: req.SummaryOfChanges,
		Notes:            req.Notes,
		EffectiveDate:    effectiveDate,
		IsOfficial:       req.IsOfficial,
		CreatedByID:      userID.(uint),
	}

	if err := db.Create(&version).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation version: "+err.Error())
		return
	}

	// If this is marked as official, update the regulation's current version
	if req.IsOfficial {
		regulation.CurrentDocumentVersionID = &version.ID
		if err := db.Save(&regulation).Error; err != nil {
			HandleInternalError(c, "Failed to update regulation current version: "+err.Error())
			return
		}
	}

	// Load the created version with relationships
	if err := db.Preload("CreatedBy").First(&version, version.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created version: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation version created successfully",
		Data:    version,
	})
}

// CompareRegulationVersions compares two versions of a regulation
func CompareRegulationVersions(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	version1ID := c.Query("version1")
	version2ID := c.Query("version2")

	if version1ID == "" || version2ID == "" {
		HandleBadRequest(c, "Both version1 and version2 query parameters are required")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get both versions
	var version1, version2 models.RegulationDocumentVersion
	if err := db.Where("law_rule_id = ? AND id = ?", id, version1ID).Preload("CreatedBy").First(&version1).Error; err != nil {
		HandleNotFound(c, "Version 1")
		return
	}

	if err := db.Where("law_rule_id = ? AND id = ?", id, version2ID).Preload("CreatedBy").First(&version2).Error; err != nil {
		HandleNotFound(c, "Version 2")
		return
	}

	// Generate comparison data
	comparison := gin.H{
		"regulation_id":    id,
		"regulation_title": regulation.Title,
		"version1": gin.H{
			"id":                 version1.ID,
			"version_number":     version1.VersionNumber,
			"summary_of_changes": version1.SummaryOfChanges,
			"notes":              version1.Notes,
			"effective_date":     version1.EffectiveDate,
			"is_official":        version1.IsOfficial,
			"created_at":         version1.CreatedAt,
			"created_by":         version1.CreatedBy.FirstName + " " + version1.CreatedBy.LastName,
		},
		"version2": gin.H{
			"id":                 version2.ID,
			"version_number":     version2.VersionNumber,
			"summary_of_changes": version2.SummaryOfChanges,
			"notes":              version2.Notes,
			"effective_date":     version2.EffectiveDate,
			"is_official":        version2.IsOfficial,
			"created_at":         version2.CreatedAt,
			"created_by":         version2.CreatedBy.FirstName + " " + version2.CreatedBy.LastName,
		},
		"comparison_metadata": gin.H{
			"compared_at":     time.Now(),
			"time_difference": version2.CreatedAt.Sub(version1.CreatedAt).String(),
			"newer_version":   getNewerVersion(version1, version2),
		},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation versions compared successfully",
		Data:    comparison,
	})
}

// RollbackRegulationVersion rolls back a regulation to a previous version
func RollbackRegulationVersion(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	versionID := c.Param("version_id")
	if versionID == "" {
		HandleBadRequest(c, "Version ID is required")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Verify version exists and belongs to this regulation
	var targetVersion models.RegulationDocumentVersion
	if err := db.Where("law_rule_id = ? AND id = ?", id, versionID).First(&targetVersion).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Version")
			return
		}
		HandleInternalError(c, "Failed to fetch version: "+err.Error())
		return
	}

	// Update regulation to point to the target version
	regulation.CurrentDocumentVersionID = &targetVersion.ID
	if err := db.Save(&regulation).Error; err != nil {
		HandleInternalError(c, "Failed to rollback regulation: "+err.Error())
		return
	}

	// Create a log entry for the rollback
	logEntry := gin.H{
		"action":           "rollback",
		"regulation_id":    id,
		"target_version":   targetVersion.VersionNumber,
		"performed_by":     userID,
		"performed_at":     time.Now(),
		"previous_version": regulation.CurrentDocumentVersionID,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation rolled back successfully",
		Data: gin.H{
			"regulation":     regulation,
			"target_version": targetVersion,
			"rollback_log":   logEntry,
		},
	})
}

// Helper function to determine which version is newer
func getNewerVersion(v1, v2 models.RegulationDocumentVersion) string {
	if v2.CreatedAt.After(v1.CreatedAt) {
		return "version2"
	}
	return "version1"
}
