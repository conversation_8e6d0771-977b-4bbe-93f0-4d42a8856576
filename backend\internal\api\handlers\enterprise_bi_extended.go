package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// CreateReport creates a new BI report
func CreateReport(c *gin.Context) {
	var req ReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create report
	report := models.Report{
		ReportName:    req.ReportName,
		Description:   req.Description,
		ReportType:    req.ReportType,
		Query:         req.Query,
		DataSources:   req.DataSources,
		Parameters:    req.Parameters,
		OutputFormat:  req.OutputFormat,
		Template:      req.Template,
		Visualization: req.Visualization,
		IsScheduled:   req.Is<PERSON>cheduled,
		Schedule:      req.ScheduleConfig,
		Status:        "active",
		Metadata:      req.Metadata,
	}

	if err := db.Create(&report).Error; err != nil {
		HandleInternalError(c, "Failed to create report: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("CreatedBy").First(&report, report.ID)

	c.JSON(http.StatusCreated, gin.H{"report": report})
}

// ExecuteReport executes a BI report
func ExecuteReport(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var report models.Report
	if err := db.First(&report, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	// Update last execution time
	now := time.Now()
	report.LastRun = &now
	report.ExecutionCount++

	if err := db.Save(&report).Error; err != nil {
		HandleInternalError(c, "Failed to execute report: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("CreatedBy").First(&report, report.ID)

	c.JSON(http.StatusOK, gin.H{"report": report, "message": "Report executed successfully"})
}

// KPI Handlers

// GetKPIs returns all KPIs with pagination
func GetKPIs(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total KPIs
	var total int64
	db.Model(&models.KPI{}).Count(&total)

	// Get KPIs with pagination
	var kpis []models.KPI
	offset := (page - 1) * perPage
	if err := db.Preload("Owner").
		Offset(offset).
		Limit(perPage).
		Order("kpi_name ASC").
		Find(&kpis).Error; err != nil {
		HandleInternalError(c, "Failed to fetch KPIs: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"kpis": kpis,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateKPI creates a new KPI
func CreateKPI(c *gin.Context) {
	var req KPIRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create KPI
	kpi := models.KPI{
		KPIName:         req.KPIName,
		Description:     req.Description,
		Category:        req.Category,
		Formula:         req.Formula,
		DataSource:      req.DataSource,
		Unit:            req.Unit,
		TargetValue:     req.TargetValue,
		MinThreshold:    req.MinThreshold,
		MaxThreshold:    req.MaxThreshold,
		CriticalMin:     req.CriticalMin,
		CriticalMax:     req.CriticalMax,
		CurrentValue:    req.CurrentValue,
		UpdateFrequency: req.UpdateFrequency,
		OwnerID:         req.OwnerID,
		IsActive:        req.IsActive,
		Metadata:        req.Metadata,
	}

	if err := db.Create(&kpi).Error; err != nil {
		HandleInternalError(c, "Failed to create KPI: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Owner").First(&kpi, kpi.ID)

	c.JSON(http.StatusCreated, gin.H{"kpi": kpi})
}

// UpdateKPIValue updates a KPI's current value
func UpdateKPIValue(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid KPI ID"})
		return
	}

	var req struct {
		CurrentValue float64 `json:"current_value" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var kpi models.KPI
	if err := db.First(&kpi, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "KPI not found"})
		return
	}

	// Update KPI value
	kpi.PreviousValue = kpi.CurrentValue
	kpi.CurrentValue = req.CurrentValue
	now := time.Now()
	kpi.LastCalculated = &now

	// Calculate trend
	if kpi.CurrentValue > kpi.PreviousValue {
		kpi.Trend = "up"
	} else if kpi.CurrentValue < kpi.PreviousValue {
		kpi.Trend = "down"
	} else {
		kpi.Trend = "stable"
	}

	if err := db.Save(&kpi).Error; err != nil {
		HandleInternalError(c, "Failed to update KPI value: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Owner").First(&kpi, kpi.ID)

	c.JSON(http.StatusOK, gin.H{"kpi": kpi})
}

// Data Mining Handlers

// GetDataMiningModels returns all data mining models with pagination
func GetDataMiningModels(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total models
	var total int64
	db.Model(&models.DataMining{}).Count(&total)

	// Get models with pagination
	var models_list []models.DataMining
	offset := (page - 1) * perPage
	if err := db.Preload("CreatedBy").
		Offset(offset).
		Limit(perPage).
		Order("model_name ASC").
		Find(&models_list).Error; err != nil {
		HandleInternalError(c, "Failed to fetch data mining models: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"models": models_list,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateDataMiningModel creates a new data mining model
func CreateDataMiningModel(c *gin.Context) {
	var req DataMiningModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create data mining model
	model := models.DataMining{
		ModelName:      req.ModelName,
		Description:    req.Description,
		ModelType:      req.ModelType,
		Algorithm:      req.Algorithm,
		AnalyticsType:  models.AnalyticsType(req.AnalyticsType),
		Parameters:     req.Parameters,
		Features:       req.Features,
		TrainingData:   req.TrainingData,
		Accuracy:       req.Accuracy,
		Precision:      req.Precision,
		Recall:         req.Recall,
		F1Score:        req.F1Score,
		ModelPath:      req.ModelPath,
		ModelVersion:   req.ModelVersion,
		RetrainingFreq: req.RetrainingFreq,
		CreatedByID:    req.CreatedByID,
		Status:         req.Status,
		Metadata:       req.Metadata,
	}

	if err := db.Create(&model).Error; err != nil {
		HandleInternalError(c, "Failed to create data mining model: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("CreatedBy").First(&model, model.ID)

	c.JSON(http.StatusCreated, gin.H{"model": model})
}

// GetDataWarehouse returns a specific data warehouse
func GetDataWarehouse(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid warehouse ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var warehouse models.DataWarehouse
	if err := db.First(&warehouse, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Data warehouse not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"warehouse": warehouse})
}

// UpdateDataWarehouse updates a data warehouse
func UpdateDataWarehouse(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid warehouse ID"})
		return
	}

	var req DataWarehouseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var warehouse models.DataWarehouse
	if err := db.First(&warehouse, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Data warehouse not found"})
		return
	}

	// Update warehouse fields
	warehouse.WarehouseName = req.WarehouseName
	warehouse.Description = req.Description
	warehouse.DatabaseType = req.DatabaseType
	warehouse.ConnectionString = req.ConnectionString
	warehouse.StorageSize = req.StorageCapacity
	warehouse.IsActive = req.IsActive
	warehouse.QueryCount = req.QueryCount
	warehouse.AvgQueryTime = req.AvgQueryTime
	warehouse.Metadata = req.Metadata

	if err := db.Save(&warehouse).Error; err != nil {
		HandleInternalError(c, "Failed to update data warehouse: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"warehouse": warehouse})
}

// DeleteDataWarehouse deletes a data warehouse
func DeleteDataWarehouse(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid warehouse ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var warehouse models.DataWarehouse
	if err := db.First(&warehouse, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Data warehouse not found"})
		return
	}

	if err := db.Delete(&warehouse).Error; err != nil {
		HandleInternalError(c, "Failed to delete data warehouse: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Data warehouse deleted successfully"})
}

// GetDataSource returns a specific data source
func GetDataSource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid data source ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var dataSource models.DataSource
	if err := db.First(&dataSource, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Data source not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data_source": dataSource})
}

// UpdateDataSource updates a data source
func UpdateDataSource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid data source ID"})
		return
	}

	var req DataSourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var dataSource models.DataSource
	if err := db.First(&dataSource, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Data source not found"})
		return
	}

	// Update data source fields
	dataSource.SourceName = req.SourceName
	dataSource.Description = req.Description
	dataSource.SourceType = models.DataSourceType(req.SourceType)
	dataSource.ConnectionString = req.ConnectionString
	dataSource.AuthMethod = req.AuthMethod
	dataSource.Credentials = req.Credentials
	dataSource.Schema = req.Schema
	dataSource.TableMapping = req.TableMapping
	dataSource.FieldMapping = req.FieldMapping
	dataSource.IsActive = req.IsActive
	dataSource.SyncFrequency = req.SyncFrequency
	dataSource.Metadata = req.Metadata

	if err := db.Save(&dataSource).Error; err != nil {
		HandleInternalError(c, "Failed to update data source: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"data_source": dataSource})
}
