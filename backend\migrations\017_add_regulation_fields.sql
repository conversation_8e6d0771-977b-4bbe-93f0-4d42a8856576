-- Migration 017: Add missing fields to regulations table
-- This migration adds the hierarchical structure fields and content field to the laws_and_rules table

-- Add content field for regulation text
ALTER TABLE laws_and_rules ADD COLUMN IF NOT EXISTS content TEXT;

-- Add hierarchical structure fields
ALTER TABLE laws_and_rules ADD COLUMN IF NOT EXISTS chapter_number VARCHAR(50);
ALTER TABLE laws_and_rules ADD COLUMN IF NOT EXISTS subchapter VARCHAR(50);
ALTER TABLE laws_and_rules ADD COLUMN IF NOT EXISTS part_number VARCHAR(50);
ALTER TABLE laws_and_rules ADD COLUMN IF NOT EXISTS section_number VARCHAR(50);
ALTER TABLE laws_and_rules ADD COLUMN IF NOT EXISTS subsection VARCHAR(50);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_chapter_number ON laws_and_rules(chapter_number);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_part_number ON laws_and_rules(part_number);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_section_number ON laws_and_rules(section_number);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_cfr_title ON laws_and_rules(cfr_title);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_usc_title ON laws_and_rules(usc_title);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_public_law_number ON laws_and_rules(public_law_number);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_regulatory_identifier ON laws_and_rules(regulatory_identifier);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_docket_number ON laws_and_rules(docket_number);

-- Update existing regulations to have default hierarchy level if not set
UPDATE laws_and_rules SET hierarchy_level = 'regulation' WHERE hierarchy_level IS NULL OR hierarchy_level = '';

-- Add comments for documentation
COMMENT ON COLUMN laws_and_rules.content IS 'Main regulation content in text format';
COMMENT ON COLUMN laws_and_rules.chapter_number IS 'CFR Chapter number (e.g., I, II, III)';
COMMENT ON COLUMN laws_and_rules.subchapter IS 'CFR Subchapter (e.g., A, B, C)';
COMMENT ON COLUMN laws_and_rules.part_number IS 'CFR Part number (e.g., 100, 200)';
COMMENT ON COLUMN laws_and_rules.section_number IS 'CFR Section number (e.g., 1.1, 2.5)';
COMMENT ON COLUMN laws_and_rules.subsection IS 'CFR Subsection (e.g., (a), (b))';
