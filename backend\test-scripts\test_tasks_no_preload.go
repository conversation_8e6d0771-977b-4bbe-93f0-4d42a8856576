package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func main() {
	// First login to get token
	loginData := map[string]string{
		"identifier": "<EMAIL>",
		"password":   "password",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := http.Post("http://127.0.0.1:8080/api/v1/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Login Error: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		fmt.Printf("Login failed: %d - %s\n", resp.StatusCode, string(body))
		return
	}

	var loginResponse map[string]interface{}
	json.Unmarshal(body, &loginResponse)
	
	var token string
	if tokenStr, ok := loginResponse["token"].(string); ok {
		token = tokenStr
	} else {
		fmt.Printf("No token found in response: %s\n", string(body))
		return
	}

	fmt.Printf("Login successful, token: %s...\n", token[:20])
	
	// Test a simple endpoint that doesn't involve tasks - try categories
	client := &http.Client{}
	req, _ := http.NewRequest("GET", "http://127.0.0.1:8080/api/v1/categories", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	
	fmt.Printf("Making request to categories endpoint...\n")
	resp, err = client.Do(req)
	if err != nil {
		fmt.Printf("Categories Error: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ = io.ReadAll(resp.Body)
	fmt.Printf("Categories Status: %d\n", resp.StatusCode)
	fmt.Printf("Categories Response: %s\n", string(body))
}
