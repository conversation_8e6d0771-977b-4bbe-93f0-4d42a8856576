package services

import (
	"fmt"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// NLPService provides natural language processing capabilities
type NLPService struct{}

// NewNLPService creates a new NLP service
func NewNLPService() *NLPService {
	return &NLPService{}
}

// Deadline represents an extracted deadline from text
type Deadline struct {
	Date        time.Time
	Description string
	Context     string
	Urgency     string
}

// Cost represents an extracted cost from text
type Cost struct {
	Amount      float64
	Description string
	Context     string
	Currency    string
}

// ExtractDeadlines finds dates and deadlines in text content
func (s *NLPService) ExtractDeadlines(content string) []Deadline {
	var deadlines []Deadline

	// Date patterns to match
	datePatterns := []struct {
		pattern string
		format  string
	}{
		{`(\d{1,2})/(\d{1,2})/(\d{4})`, "01/02/2006"},
		{`(\d{4})-(\d{1,2})-(\d{1,2})`, "2006-01-02"},
		{`(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{1,2}),?\s+(\d{4})`, "January 2, 2006"},
		{`(\d{1,2})\s+(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{4})`, "2 January 2006"},
	}

	// Deadline keywords that indicate urgency
	urgencyKeywords := map[string]string{
		"immediately": "urgent",
		"urgent":      "urgent",
		"critical":    "urgent",
		"asap":        "urgent",
		"soon":        "important",
		"within":      "important",
		"by":          "normal",
		"before":      "normal",
		"deadline":    "important",
		"due":         "important",
		"expires":     "important",
		"effective":   "normal",
		"terminates":  "normal",
	}

	// Split content into sentences for context
	sentences := strings.Split(content, ".")

	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) < 10 {
			continue
		}

		// Check for deadline keywords
		lowerSentence := strings.ToLower(sentence)
		urgency := "normal"

		for keyword, level := range urgencyKeywords {
			if strings.Contains(lowerSentence, keyword) {
				urgency = level
				break
			}
		}

		// Extract dates from the sentence
		for _, pattern := range datePatterns {
			re := regexp.MustCompile(pattern.pattern)
			matches := re.FindAllStringSubmatch(sentence, -1)

			for _, match := range matches {
				var dateStr string
				switch pattern.format {
				case "01/02/2006":
					if len(match) >= 4 {
						dateStr = match[1] + "/" + match[2] + "/" + match[3]
					}
				case "2006-01-02":
					if len(match) >= 4 {
						dateStr = match[1] + "-" + match[2] + "-" + match[3]
					}
				case "January 2, 2006":
					if len(match) >= 4 {
						dateStr = match[1] + " " + match[2] + ", " + match[3]
					}
				case "2 January 2006":
					if len(match) >= 4 {
						dateStr = match[1] + " " + match[2] + " " + match[3]
					}
				}

				if parsedDate, err := time.Parse(pattern.format, dateStr); err == nil {
					// Only include future dates
					if parsedDate.After(time.Now()) {
						deadline := Deadline{
							Date:        parsedDate,
							Description: s.extractDeadlineDescription(sentence),
							Context:     sentence,
							Urgency:     urgency,
						}
						deadlines = append(deadlines, deadline)
					}
				}
			}
		}

		// Extract relative dates (e.g., "30 days from now", "within 60 days")
		relativeDates := s.extractRelativeDates(sentence)
		for _, relDate := range relativeDates {
			deadline := Deadline{
				Date:        relDate.Date,
				Description: relDate.Description,
				Context:     sentence,
				Urgency:     urgency,
			}
			deadlines = append(deadlines, deadline)
		}
	}

	return deadlines
}

// ExtractCosts finds monetary amounts and cost estimates in text
func (s *NLPService) ExtractCosts(content string) []Cost {
	var costs []Cost

	// Cost patterns to match
	costPatterns := []string{
		`\$([0-9,]+(?:\.[0-9]{2})?)`,                    // $1,000.00
		`([0-9,]+(?:\.[0-9]{2})?)\s*dollars?`,           // 1000 dollars
		`([0-9,]+(?:\.[0-9]{2})?)\s*USD`,                // 1000 USD
		`cost(?:s)?\s+of\s+\$?([0-9,]+(?:\.[0-9]{2})?)`, // costs of $1000
		`budget(?:ed)?\s+\$?([0-9,]+(?:\.[0-9]{2})?)`,   // budgeted $1000
		`estimate(?:d)?\s+\$?([0-9,]+(?:\.[0-9]{2})?)`,  // estimated $1000
	}

	sentences := strings.Split(content, ".")

	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) < 10 {
			continue
		}

		for _, pattern := range costPatterns {
			re := regexp.MustCompile(pattern)
			matches := re.FindAllStringSubmatch(sentence, -1)

			for _, match := range matches {
				if len(match) >= 2 {
					amountStr := strings.ReplaceAll(match[1], ",", "")
					if amount, err := strconv.ParseFloat(amountStr, 64); err == nil {
						cost := Cost{
							Amount:      amount,
							Description: s.extractCostDescription(sentence),
							Context:     sentence,
							Currency:    "USD",
						}
						costs = append(costs, cost)
					}
				}
			}
		}
	}

	return costs
}

// FindRelatedRegulations finds regulations related to the given content
func (s *NLPService) FindRelatedRegulations(content string, db *gorm.DB) []models.LawsAndRules {
	var regulations []models.LawsAndRules

	// Extract key terms from content
	keyTerms := s.extractKeyTerms(content)

	// Search for regulations containing these terms
	for _, term := range keyTerms {
		var foundRegs []models.LawsAndRules
		db.Where("title ILIKE ? OR description ILIKE ?", "%"+term+"%", "%"+term+"%").
			Limit(5).Find(&foundRegs)

		regulations = append(regulations, foundRegs...)
	}

	// Remove duplicates
	seen := make(map[uint]bool)
	var uniqueRegulations []models.LawsAndRules
	for _, reg := range regulations {
		if !seen[reg.ID] {
			seen[reg.ID] = true
			uniqueRegulations = append(uniqueRegulations, reg)
		}
	}

	return uniqueRegulations
}

// extractDeadlineDescription extracts a meaningful description from a deadline sentence
func (s *NLPService) extractDeadlineDescription(sentence string) string {
	// Remove common words and extract the main action
	words := strings.Fields(sentence)
	var keyWords []string

	stopWords := map[string]bool{
		"the": true, "a": true, "an": true, "and": true, "or": true, "but": true,
		"in": true, "on": true, "at": true, "to": true, "for": true, "of": true,
		"with": true, "by": true, "from": true, "up": true, "about": true, "into": true,
		"through": true, "during": true, "before": true, "after": true, "above": true,
		"below": true, "between": true, "among": true, "within": true,
	}

	for _, word := range words {
		word = strings.ToLower(strings.Trim(word, ".,!?;:"))
		if len(word) > 2 && !stopWords[word] {
			keyWords = append(keyWords, word)
		}
	}

	if len(keyWords) > 5 {
		keyWords = keyWords[:5]
	}

	return strings.Join(keyWords, " ")
}

// extractCostDescription extracts a meaningful description from a cost sentence
func (s *NLPService) extractCostDescription(sentence string) string {
	// Look for cost-related context
	costKeywords := []string{"implementation", "compliance", "administrative", "operational", "maintenance", "development", "training", "equipment"}

	lowerSentence := strings.ToLower(sentence)
	for _, keyword := range costKeywords {
		if strings.Contains(lowerSentence, keyword) {
			return keyword + " cost"
		}
	}

	return "estimated cost"
}

// extractKeyTerms extracts important terms from content for relationship matching
func (s *NLPService) extractKeyTerms(content string) []string {
	// Simple term extraction - in a real implementation, this would use more sophisticated NLP
	words := strings.Fields(strings.ToLower(content))
	termFreq := make(map[string]int)

	// Count word frequency
	for _, word := range words {
		word = strings.Trim(word, ".,!?;:()")
		if len(word) > 4 { // Only consider longer words
			termFreq[word]++
		}
	}

	// Get most frequent terms
	var terms []string
	for term, freq := range termFreq {
		if freq >= 2 { // Appears at least twice
			terms = append(terms, term)
		}
	}

	// Limit to top 10 terms
	if len(terms) > 10 {
		terms = terms[:10]
	}

	return terms
}

// ExtractKeywordsAdvanced performs advanced keyword extraction using TF-IDF and NER
func (s *NLPService) ExtractKeywordsAdvanced(content string) []string {
	if content == "" {
		return []string{}
	}

	// In a production environment, this would use:
	// - TF-IDF (Term Frequency-Inverse Document Frequency) analysis
	// - Named Entity Recognition (NER) using libraries like spaCy or Stanford NLP
	// - Part-of-speech tagging to identify important nouns and noun phrases
	// - Word embeddings for semantic similarity

	// For now, implement an enhanced version of keyword extraction
	keywords := s.extractKeywordsWithTFIDF(content)

	// Add named entities
	entities := s.extractNamedEntities(content)
	keywords = append(keywords, entities...)

	// Remove duplicates and limit results
	uniqueKeywords := s.removeDuplicates(keywords)
	if len(uniqueKeywords) > 15 {
		uniqueKeywords = uniqueKeywords[:15]
	}

	return uniqueKeywords
}

// GenerateExtractiveSummary generates a summary using extractive summarization
func (s *NLPService) GenerateExtractiveSummary(content string, maxLength int) string {
	if content == "" || maxLength <= 0 {
		return ""
	}

	// In a production environment, this would use:
	// - TextRank algorithm for sentence ranking
	// - BERT or other transformer models for sentence embeddings
	// - Graph-based ranking algorithms
	// - Machine learning models trained on summarization datasets

	// For now, implement an enhanced extractive summarization
	sentences := s.splitIntoSentences(content)
	if len(sentences) <= 1 {
		if len(content) <= maxLength {
			return content
		}
		return content[:maxLength] + "..."
	}

	// Score sentences using multiple factors
	sentenceScores := s.scoreSentencesForSummarization(sentences, content)

	// Select top sentences
	selectedSentences := s.selectTopSentences(sentences, sentenceScores, maxLength)

	return strings.Join(selectedSentences, ". ")
}

// ClassifyDocumentCategory classifies document content into categories using ML
func (s *NLPService) ClassifyDocumentCategory(content string) string {
	if content == "" {
		return ""
	}

	// In a production environment, this would use:
	// - Pre-trained classification models (BERT, RoBERTa, etc.)
	// - Custom trained models on regulatory document datasets
	// - Ensemble methods combining multiple classifiers
	// - Feature engineering with domain-specific features

	// For now, implement an enhanced rule-based classifier with ML-like features
	features := s.extractClassificationFeatures(content)
	category := s.classifyUsingFeatures(features)

	return category
}

// Helper methods for advanced NLP processing

// extractKeywordsWithTFIDF implements real TF-IDF keyword extraction algorithm
func (s *NLPService) extractKeywordsWithTFIDF(content string) []string {
	// Preprocess text: tokenization, normalization, stop word removal
	tokens := s.preprocessText(content)

	// Calculate Term Frequency (TF)
	termFreq := s.calculateTermFrequency(tokens)

	// Calculate Inverse Document Frequency (IDF) using a corpus simulation
	idf := s.calculateInverseDocumentFrequency(tokens)

	// Calculate TF-IDF scores
	tfidfScores := s.calculateTFIDFScores(termFreq, idf)

	// Extract top keywords based on TF-IDF scores
	keywords := s.extractTopKeywordsByScore(tfidfScores, 15)

	return keywords
}

// preprocessText performs comprehensive text preprocessing
func (s *NLPService) preprocessText(content string) []string {
	// Convert to lowercase
	content = strings.ToLower(content)

	// Remove special characters and normalize
	content = s.normalizeText(content)

	// Tokenize
	tokens := strings.Fields(content)

	// Filter tokens
	var filteredTokens []string
	for _, token := range tokens {
		token = strings.Trim(token, ".,!?;:()")

		// Skip if too short, too long, or is stop word
		if len(token) >= 3 && len(token) <= 20 && !s.isStopWord(token) && s.isValidToken(token) {
			filteredTokens = append(filteredTokens, token)
		}
	}

	return filteredTokens
}

// normalizeText normalizes text for better processing
func (s *NLPService) normalizeText(text string) string {
	// Remove extra whitespace
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")

	// Remove URLs
	text = regexp.MustCompile(`https?://[^\s]+`).ReplaceAllString(text, "")

	// Remove email addresses
	text = regexp.MustCompile(`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`).ReplaceAllString(text, "")

	// Remove numbers that are not part of meaningful terms
	text = regexp.MustCompile(`\b\d+\b`).ReplaceAllString(text, "")

	return strings.TrimSpace(text)
}

// calculateTermFrequency calculates term frequency for each token
func (s *NLPService) calculateTermFrequency(tokens []string) map[string]float64 {
	termCount := make(map[string]int)

	// Count occurrences
	for _, token := range tokens {
		termCount[token]++
	}

	// Calculate TF using log normalization: 1 + log(count)
	tf := make(map[string]float64)
	for term, count := range termCount {
		if count > 0 {
			tf[term] = 1.0 + math.Log(float64(count))
		}
	}

	return tf
}

// calculateInverseDocumentFrequency calculates IDF scores
func (s *NLPService) calculateInverseDocumentFrequency(tokens []string) map[string]float64 {
	// Simulate a corpus by creating pseudo-documents from the input
	// In production, this would use a pre-built corpus or domain-specific documents

	uniqueTerms := make(map[string]bool)
	for _, token := range tokens {
		uniqueTerms[token] = true
	}

	// Simulate corpus statistics based on term characteristics
	totalDocuments := 10000.0 // Simulated corpus size
	idf := make(map[string]float64)

	for term := range uniqueTerms {
		// Estimate document frequency based on term characteristics
		docFreq := s.estimateDocumentFrequency(term, totalDocuments)

		// Calculate IDF: log(N / df)
		if docFreq > 0 {
			idf[term] = math.Log(totalDocuments / docFreq)
		} else {
			idf[term] = math.Log(totalDocuments) // Maximum IDF for very rare terms
		}
	}

	return idf
}

// estimateDocumentFrequency estimates how frequently a term appears in documents
func (s *NLPService) estimateDocumentFrequency(term string, totalDocs float64) float64 {
	// Estimate based on term characteristics
	termLen := len(term)

	// Common terms (short, frequent patterns)
	commonTerms := map[string]float64{
		"regulation":  totalDocs * 0.3,
		"compliance":  totalDocs * 0.25,
		"requirement": totalDocs * 0.4,
		"policy":      totalDocs * 0.35,
		"procedure":   totalDocs * 0.2,
		"standard":    totalDocs * 0.3,
		"guideline":   totalDocs * 0.15,
		"framework":   totalDocs * 0.1,
	}

	if freq, exists := commonTerms[term]; exists {
		return freq
	}

	// Estimate based on term length and characteristics
	if termLen <= 4 {
		return totalDocs * 0.5 // Short terms are common
	} else if termLen <= 7 {
		return totalDocs * 0.2 // Medium terms
	} else if termLen <= 12 {
		return totalDocs * 0.05 // Long terms are rare
	} else {
		return totalDocs * 0.01 // Very long terms are very rare
	}
}

// calculateTFIDFScores calculates TF-IDF scores for all terms
func (s *NLPService) calculateTFIDFScores(tf map[string]float64, idf map[string]float64) map[string]float64 {
	tfidf := make(map[string]float64)

	for term, tfScore := range tf {
		if idfScore, exists := idf[term]; exists {
			tfidf[term] = tfScore * idfScore
		}
	}

	return tfidf
}

// extractTopKeywordsByScore extracts top keywords based on TF-IDF scores
func (s *NLPService) extractTopKeywordsByScore(scores map[string]float64, limit int) []string {
	// Convert to slice for sorting
	type termScore struct {
		term  string
		score float64
	}

	var termScores []termScore
	for term, score := range scores {
		termScores = append(termScores, termScore{term, score})
	}

	// Sort by score (descending)
	sort.Slice(termScores, func(i, j int) bool {
		return termScores[i].score > termScores[j].score
	})

	// Extract top terms
	var keywords []string
	maxKeywords := limit
	if len(termScores) < maxKeywords {
		maxKeywords = len(termScores)
	}

	for i := 0; i < maxKeywords; i++ {
		keywords = append(keywords, termScores[i].term)
	}

	return keywords
}

// isStopWord checks if a word is a stop word using comprehensive list
func (s *NLPService) isStopWord(word string) bool {
	stopWords := map[string]bool{
		// Common English stop words
		"a": true, "an": true, "and": true, "are": true, "as": true, "at": true, "be": true, "by": true,
		"for": true, "from": true, "has": true, "he": true, "in": true, "is": true, "it": true, "its": true,
		"of": true, "on": true, "that": true, "the": true, "to": true, "was": true, "will": true, "with": true,
		"would": true, "could": true, "should": true, "may": true, "might": true, "can": true, "must": true,
		"shall": true, "have": true, "had": true, "do": true, "does": true, "did": true,
		"this": true, "these": true, "those": true, "they": true, "them": true, "their": true, "there": true,
		"where": true, "when": true, "what": true, "which": true, "who": true, "whom": true, "whose": true,
		"why": true, "how": true, "all": true, "any": true, "both": true, "each": true, "few": true,
		"more": true, "most": true, "other": true, "some": true, "such": true, "no": true, "nor": true,
		"not": true, "only": true, "own": true, "same": true, "so": true, "than": true, "too": true,
		"very": true, "just": true, "now": true, "also": true, "here": true, "then": true, "once": true,
		"during": true, "before": true, "after": true, "above": true, "below": true, "up": true, "down": true,
		"out": true, "off": true, "over": true, "under": true, "again": true, "further": true, "between": true,
		"through": true, "into": true, "about": true, "against": true, "within": true, "without": true,

		// Legal/regulatory specific stop words
		"section": true, "subsection": true, "paragraph": true, "clause": true, "part": true, "chapter": true,
		"title": true, "article": true, "pursuant": true, "accordance": true, "herein": true, "thereof": true,
		"hereof": true, "hereby": true, "whereas": true, "therefore": true, "furthermore": true, "moreover": true,
		"however": true, "nevertheless": true, "notwithstanding": true, "provided": true, "except": true,
	}

	return stopWords[word]
}

// isValidToken checks if a token is valid for keyword extraction
func (s *NLPService) isValidToken(token string) bool {
	// Must contain at least one letter
	hasLetter := false
	for _, char := range token {
		if unicode.IsLetter(char) {
			hasLetter = true
			break
		}
	}

	if !hasLetter {
		return false
	}

	// Should not be all numbers
	if regexp.MustCompile(`^\d+$`).MatchString(token) {
		return false
	}

	// Should not contain too many special characters
	specialCharCount := 0
	for _, char := range token {
		if !unicode.IsLetter(char) && !unicode.IsNumber(char) {
			specialCharCount++
		}
	}

	if float64(specialCharCount)/float64(len(token)) > 0.3 {
		return false
	}

	return true
}

// AnalyzeComplexity analyzes the complexity of content using advanced NLP techniques
func (s *NLPService) AnalyzeComplexity(content string) map[string]interface{} {
	analysis := make(map[string]interface{})

	// Basic metrics
	words := strings.Fields(content)
	sentences := s.splitIntoSentences(content)
	paragraphs := strings.Split(content, "\n\n")

	analysis["word_count"] = len(words)
	analysis["sentence_count"] = len(sentences)
	analysis["paragraph_count"] = len(paragraphs)

	// Advanced linguistic complexity metrics
	analysis["lexical_diversity"] = s.calculateLexicalDiversity(words)
	analysis["average_sentence_length"] = s.calculateAverageSentenceLength(sentences)
	analysis["syntactic_complexity"] = s.calculateSyntacticComplexity(sentences)
	analysis["semantic_density"] = s.calculateSemanticDensity(content)
	analysis["readability_score"] = s.calculateReadabilityScore(content, words, sentences)

	// Domain-specific complexity
	technicalAnalysis := s.analyzeTechnicalTerms(content)
	analysis["technical_density"] = technicalAnalysis["density"]
	analysis["technical_categories"] = technicalAnalysis["categories"]
	analysis["domain_specificity"] = technicalAnalysis["domain_specificity"]
	analysis["regulatory_complexity"] = technicalAnalysis["regulatory_complexity"]

	return analysis
}

// calculateLexicalDiversity calculates the lexical diversity (Type-Token Ratio)
func (s *NLPService) calculateLexicalDiversity(words []string) float64 {
	if len(words) == 0 {
		return 0.0
	}

	uniqueWords := make(map[string]bool)
	for _, word := range words {
		word = strings.ToLower(strings.Trim(word, ".,!?;:()"))
		if len(word) > 0 {
			uniqueWords[word] = true
		}
	}

	return float64(len(uniqueWords)) / float64(len(words))
}

// calculateAverageSentenceLength calculates average sentence length in words
func (s *NLPService) calculateAverageSentenceLength(sentences []string) float64 {
	if len(sentences) == 0 {
		return 0.0
	}

	totalWords := 0
	for _, sentence := range sentences {
		words := strings.Fields(sentence)
		totalWords += len(words)
	}

	return float64(totalWords) / float64(len(sentences))
}

// calculateSyntacticComplexity analyzes syntactic complexity
func (s *NLPService) calculateSyntacticComplexity(sentences []string) float64 {
	if len(sentences) == 0 {
		return 0.0
	}

	complexityScore := 0.0

	for _, sentence := range sentences {
		// Count complex syntactic patterns
		sentence = strings.ToLower(sentence)

		// Subordinate clauses
		subordinateMarkers := []string{"because", "although", "while", "since", "unless", "whereas", "if", "when"}
		for _, marker := range subordinateMarkers {
			if strings.Contains(sentence, marker) {
				complexityScore += 1.0
			}
		}

		// Passive voice indicators
		passiveMarkers := []string{"was", "were", "been", "being"}
		for _, marker := range passiveMarkers {
			if strings.Contains(sentence, marker) {
				complexityScore += 0.5
			}
		}

		// Complex punctuation
		if strings.Contains(sentence, ";") {
			complexityScore += 0.5
		}
		if strings.Contains(sentence, ":") {
			complexityScore += 0.3
		}

		// Long sentences (>25 words)
		words := strings.Fields(sentence)
		if len(words) > 25 {
			complexityScore += 1.0
		}
	}

	return complexityScore / float64(len(sentences))
}

// calculateSemanticDensity calculates semantic density using content words ratio
func (s *NLPService) calculateSemanticDensity(content string) float64 {
	words := strings.Fields(strings.ToLower(content))
	if len(words) == 0 {
		return 0.0
	}

	contentWords := 0
	for _, word := range words {
		word = strings.Trim(word, ".,!?;:()")
		if len(word) > 3 && !s.isStopWord(word) {
			contentWords++
		}
	}

	return float64(contentWords) / float64(len(words))
}

// calculateReadabilityScore calculates a readability score (simplified Flesch-Kincaid)
func (s *NLPService) calculateReadabilityScore(content string, words []string, sentences []string) float64 {
	if len(words) == 0 || len(sentences) == 0 {
		return 0.0
	}

	// Count syllables (simplified)
	totalSyllables := 0
	for _, word := range words {
		word = strings.ToLower(strings.Trim(word, ".,!?;:()"))
		syllables := s.countSyllables(word)
		totalSyllables += syllables
	}

	avgSentenceLength := float64(len(words)) / float64(len(sentences))
	avgSyllablesPerWord := float64(totalSyllables) / float64(len(words))

	// Simplified Flesch Reading Ease formula
	readingEase := 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)

	// Convert to 0-100 scale where higher is more complex
	complexityScore := 100.0 - readingEase
	if complexityScore < 0 {
		complexityScore = 0
	}
	if complexityScore > 100 {
		complexityScore = 100
	}

	return complexityScore
}

// countSyllables counts syllables in a word (simplified algorithm)
func (s *NLPService) countSyllables(word string) int {
	if len(word) == 0 {
		return 0
	}

	word = strings.ToLower(word)
	vowels := "aeiouy"
	syllableCount := 0
	previousWasVowel := false

	for i, char := range word {
		isVowel := strings.ContainsRune(vowels, char)

		if isVowel && !previousWasVowel {
			syllableCount++
		}

		previousWasVowel = isVowel

		// Handle silent 'e' at the end
		if i == len(word)-1 && char == 'e' && syllableCount > 1 {
			syllableCount--
		}
	}

	// Every word has at least one syllable
	if syllableCount == 0 {
		syllableCount = 1
	}

	return syllableCount
}

// AnalyzeSentiment performs advanced sentiment analysis using lexicon-based approach
func (s *NLPService) AnalyzeSentiment(content string) map[string]interface{} {
	result := make(map[string]interface{})

	if content == "" {
		result["sentiment"] = "neutral"
		result["confidence"] = 0.0
		result["scores"] = map[string]float64{"positive": 0.0, "negative": 0.0, "neutral": 1.0}
		return result
	}

	// Preprocess text
	words := s.preprocessTextForSentiment(content)

	// Calculate sentiment scores
	sentimentScores := s.calculateSentimentScores(words)

	// Determine overall sentiment
	overallSentiment, confidence := s.determineSentiment(sentimentScores)

	// Analyze emotional dimensions
	emotions := s.analyzeEmotions(words)

	// Analyze subjectivity
	subjectivity := s.analyzeSubjectivity(words)

	result["sentiment"] = overallSentiment
	result["confidence"] = confidence
	result["scores"] = sentimentScores
	result["emotions"] = emotions
	result["subjectivity"] = subjectivity
	result["word_count"] = len(words)

	return result
}

// preprocessTextForSentiment preprocesses text for sentiment analysis
func (s *NLPService) preprocessTextForSentiment(content string) []string {
	// Convert to lowercase
	content = strings.ToLower(content)

	// Handle negations by marking them
	content = s.handleNegations(content)

	// Tokenize
	words := strings.Fields(content)

	// Clean tokens
	var cleanWords []string
	for _, word := range words {
		word = strings.Trim(word, ".,!?;:()")
		if len(word) > 0 {
			cleanWords = append(cleanWords, word)
		}
	}

	return cleanWords
}

// handleNegations handles negation patterns in text
func (s *NLPService) handleNegations(content string) string {
	negationWords := []string{"not", "no", "never", "nothing", "nowhere", "neither", "nobody", "none", "hardly", "scarcely", "barely"}

	words := strings.Fields(content)
	result := make([]string, len(words))
	copy(result, words)

	for i, word := range words {
		word = strings.Trim(word, ".,!?;:()")

		// Check if current word is a negation
		isNegation := false
		for _, neg := range negationWords {
			if word == neg {
				isNegation = true
				break
			}
		}

		// If negation found, mark next few words
		if isNegation {
			for j := i + 1; j < len(result) && j < i+4; j++ {
				// Stop at punctuation that ends the clause
				if strings.ContainsAny(result[j], ".!?;") {
					break
				}
				result[j] = "NEG_" + result[j]
			}
		}
	}

	return strings.Join(result, " ")
}

// calculateSentimentScores calculates sentiment scores using lexicon approach
func (s *NLPService) calculateSentimentScores(words []string) map[string]float64 {
	// Comprehensive sentiment lexicon
	positiveWords := map[string]float64{
		"good": 0.7, "great": 0.8, "excellent": 0.9, "amazing": 0.9, "wonderful": 0.8,
		"fantastic": 0.8, "awesome": 0.8, "outstanding": 0.9, "superb": 0.8, "brilliant": 0.8,
		"perfect": 0.9, "beautiful": 0.7, "love": 0.8, "like": 0.5, "enjoy": 0.6,
		"happy": 0.7, "pleased": 0.6, "satisfied": 0.6, "delighted": 0.8, "thrilled": 0.8,
		"effective": 0.6, "efficient": 0.6, "successful": 0.7, "beneficial": 0.6, "positive": 0.6,
		"approve": 0.6, "support": 0.5, "recommend": 0.6, "endorse": 0.6, "favor": 0.5,
		"improve": 0.5, "enhance": 0.5, "strengthen": 0.5, "advance": 0.5, "progress": 0.5,
		"opportunity": 0.4, "benefit": 0.5, "advantage": 0.5, "gain": 0.4, "profit": 0.4,
	}

	negativeWords := map[string]float64{
		"bad": -0.7, "terrible": -0.8, "awful": -0.8, "horrible": -0.8, "disgusting": -0.9,
		"hate": -0.8, "dislike": -0.6, "despise": -0.8, "loathe": -0.8, "detest": -0.8,
		"sad": -0.6, "angry": -0.7, "furious": -0.8, "upset": -0.6, "disappointed": -0.6,
		"frustrated": -0.6, "annoyed": -0.5, "irritated": -0.5, "worried": -0.5, "concerned": -0.4,
		"problem": -0.5, "issue": -0.4, "difficulty": -0.5, "trouble": -0.6, "crisis": -0.7,
		"fail": -0.7, "failure": -0.7, "error": -0.5, "mistake": -0.5, "wrong": -0.5,
		"oppose": -0.6, "reject": -0.6, "deny": -0.5, "refuse": -0.5, "decline": -0.4,
		"damage": -0.6, "harm": -0.6, "hurt": -0.6, "destroy": -0.8, "ruin": -0.7,
		"loss": -0.6, "lose": -0.5, "decrease": -0.4, "reduce": -0.3, "cut": -0.3,
	}

	var positiveScore, negativeScore float64
	totalWords := len(words)

	for _, word := range words {
		// Handle negated words
		isNegated := strings.HasPrefix(word, "NEG_")
		if isNegated {
			word = strings.TrimPrefix(word, "NEG_")
		}

		// Check positive words
		if score, exists := positiveWords[word]; exists {
			if isNegated {
				negativeScore += score // Negated positive becomes negative
			} else {
				positiveScore += score
			}
		}

		// Check negative words
		if score, exists := negativeWords[word]; exists {
			if isNegated {
				positiveScore += -score // Negated negative becomes positive
			} else {
				negativeScore += -score // Convert to positive value for calculation
			}
		}
	}

	// Normalize scores
	if totalWords > 0 {
		positiveScore = positiveScore / float64(totalWords)
		negativeScore = negativeScore / float64(totalWords)
	}

	// Calculate neutral score
	neutralScore := 1.0 - positiveScore - negativeScore
	if neutralScore < 0 {
		neutralScore = 0
	}

	return map[string]float64{
		"positive": positiveScore,
		"negative": negativeScore,
		"neutral":  neutralScore,
	}
}

// determineSentiment determines overall sentiment and confidence
func (s *NLPService) determineSentiment(scores map[string]float64) (string, float64) {
	positive := scores["positive"]
	negative := scores["negative"]
	neutral := scores["neutral"]

	// Find the highest score
	maxScore := positive
	sentiment := "positive"

	if negative > maxScore {
		maxScore = negative
		sentiment = "negative"
	}

	if neutral > maxScore {
		maxScore = neutral
		sentiment = "neutral"
	}

	// Calculate confidence based on the margin between top scores
	var confidence float64
	if sentiment == "positive" {
		confidence = positive - math.Max(negative, neutral)
	} else if sentiment == "negative" {
		confidence = negative - math.Max(positive, neutral)
	} else {
		confidence = neutral - math.Max(positive, negative)
	}

	// Normalize confidence to 0-1 range
	if confidence < 0 {
		confidence = 0
	}
	if confidence > 1 {
		confidence = 1
	}

	return sentiment, confidence
}

// analyzeEmotions analyzes specific emotions in the text
func (s *NLPService) analyzeEmotions(words []string) map[string]float64 {
	emotionLexicon := map[string]map[string]float64{
		"joy": {
			"happy": 0.8, "joy": 0.9, "cheerful": 0.7, "delighted": 0.8, "pleased": 0.6,
			"excited": 0.7, "thrilled": 0.8, "elated": 0.8, "euphoric": 0.9, "blissful": 0.8,
		},
		"anger": {
			"angry": 0.8, "furious": 0.9, "mad": 0.7, "rage": 0.9, "irritated": 0.6,
			"annoyed": 0.5, "frustrated": 0.7, "outraged": 0.8, "livid": 0.9, "irate": 0.8,
		},
		"fear": {
			"afraid": 0.7, "scared": 0.7, "terrified": 0.9, "anxious": 0.6, "worried": 0.5,
			"nervous": 0.5, "panic": 0.8, "dread": 0.7, "alarmed": 0.6, "apprehensive": 0.5,
		},
		"sadness": {
			"sad": 0.7, "depressed": 0.8, "miserable": 0.8, "gloomy": 0.6, "melancholy": 0.7,
			"sorrowful": 0.7, "dejected": 0.7, "despondent": 0.8, "downhearted": 0.6, "blue": 0.5,
		},
		"surprise": {
			"surprised": 0.6, "amazed": 0.7, "astonished": 0.8, "shocked": 0.7, "stunned": 0.7,
			"bewildered": 0.6, "perplexed": 0.5, "confused": 0.5, "puzzled": 0.4, "baffled": 0.5,
		},
		"disgust": {
			"disgusted": 0.8, "revolted": 0.8, "repulsed": 0.8, "sickened": 0.7, "nauseated": 0.7,
			"appalled": 0.7, "horrified": 0.8, "repelled": 0.7, "offended": 0.6, "disturbed": 0.6,
		},
	}

	emotionScores := make(map[string]float64)
	for emotion := range emotionLexicon {
		emotionScores[emotion] = 0.0
	}

	totalWords := len(words)
	if totalWords == 0 {
		return emotionScores
	}

	for _, word := range words {
		// Remove negation prefix for emotion analysis
		word = strings.TrimPrefix(word, "NEG_")

		for emotion, lexicon := range emotionLexicon {
			if score, exists := lexicon[word]; exists {
				emotionScores[emotion] += score
			}
		}
	}

	// Normalize scores
	for emotion := range emotionScores {
		emotionScores[emotion] = emotionScores[emotion] / float64(totalWords)
	}

	return emotionScores
}

// analyzeSubjectivity analyzes the subjectivity vs objectivity of the text
func (s *NLPService) analyzeSubjectivity(words []string) map[string]float64 {
	subjectiveWords := map[string]float64{
		"think": 0.7, "believe": 0.8, "feel": 0.8, "opinion": 0.9, "view": 0.6,
		"personally": 0.8, "probably": 0.6, "maybe": 0.7, "perhaps": 0.7, "seems": 0.6,
		"appears": 0.5, "likely": 0.6, "unlikely": 0.6, "possibly": 0.7, "supposedly": 0.7,
		"allegedly": 0.8, "reportedly": 0.6, "apparently": 0.6, "presumably": 0.7, "arguably": 0.8,
	}

	objectiveWords := map[string]float64{
		"fact": 0.8, "data": 0.7, "evidence": 0.8, "research": 0.7, "study": 0.7,
		"analysis": 0.6, "report": 0.6, "statistics": 0.8, "measurement": 0.7, "observation": 0.7,
		"according": 0.6, "stated": 0.6, "reported": 0.6, "documented": 0.7, "recorded": 0.7,
		"established": 0.7, "confirmed": 0.7, "verified": 0.8, "proven": 0.8, "demonstrated": 0.7,
	}

	var subjectiveScore, objectiveScore float64
	totalWords := len(words)

	for _, word := range words {
		word = strings.TrimPrefix(word, "NEG_")

		if score, exists := subjectiveWords[word]; exists {
			subjectiveScore += score
		}

		if score, exists := objectiveWords[word]; exists {
			objectiveScore += score
		}
	}

	// Normalize scores
	if totalWords > 0 {
		subjectiveScore = subjectiveScore / float64(totalWords)
		objectiveScore = objectiveScore / float64(totalWords)
	}

	// Calculate overall subjectivity (0 = objective, 1 = subjective)
	totalScore := subjectiveScore + objectiveScore
	var subjectivity float64
	if totalScore > 0 {
		subjectivity = subjectiveScore / totalScore
	} else {
		subjectivity = 0.5 // Neutral if no indicators found
	}

	return map[string]float64{
		"subjectivity":     subjectivity,
		"objectivity":      1.0 - subjectivity,
		"subjective_score": subjectiveScore,
		"objective_score":  objectiveScore,
	}
}

// extractNamedEntities simulates named entity recognition
func (s *NLPService) extractNamedEntities(content string) []string {
	var entities []string

	// Patterns for different entity types
	patterns := map[string][]string{
		"organizations": {
			`(?i)\b(department of \w+)`, `(?i)\b(\w+ agency)`, `(?i)\b(\w+ administration)`,
			`(?i)\b(\w+ commission)`, `(?i)\b(\w+ bureau)`, `(?i)\b(\w+ service)`,
		},
		"regulations": {
			`(?i)\b(cfr \d+)`, `(?i)\b(\d+ cfr \d+)`, `(?i)\b(part \d+)`,
			`(?i)\b(section \d+)`, `(?i)\b(subpart \w+)`,
		},
		"dates": {
			`\b\d{1,2}/\d{1,2}/\d{4}\b`, `\b\d{4}-\d{2}-\d{2}\b`,
			`\b(january|february|march|april|may|june|july|august|september|october|november|december) \d{1,2}, \d{4}\b`,
		},
	}

	for _, patternList := range patterns {
		for _, pattern := range patternList {
			re := regexp.MustCompile(pattern)
			matches := re.FindAllString(content, -1)
			for _, match := range matches {
				entities = append(entities, strings.TrimSpace(match))
			}
		}
	}

	return entities
}

// removeDuplicates removes duplicate strings from a slice
func (s *NLPService) removeDuplicates(slice []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// splitIntoSentences splits content into sentences
func (s *NLPService) splitIntoSentences(content string) []string {
	// Simple sentence splitting - in production, use more sophisticated methods
	sentences := strings.Split(content, ".")

	var cleanSentences []string
	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) > 10 { // Filter out very short fragments
			cleanSentences = append(cleanSentences, sentence)
		}
	}

	return cleanSentences
}

// scoreSentencesForSummarization scores sentences for extractive summarization
func (s *NLPService) scoreSentencesForSummarization(sentences []string, fullContent string) map[int]float64 {
	scores := make(map[int]float64)

	// Calculate word frequencies in the full content
	words := strings.Fields(strings.ToLower(fullContent))
	wordFreq := make(map[string]int)
	for _, word := range words {
		word = strings.Trim(word, ".,!?;:()")
		if len(word) > 3 {
			wordFreq[word]++
		}
	}

	// Score each sentence
	for i, sentence := range sentences {
		score := 0.0
		sentenceWords := strings.Fields(strings.ToLower(sentence))

		// Score based on word frequency
		for _, word := range sentenceWords {
			word = strings.Trim(word, ".,!?;:()")
			if freq, exists := wordFreq[word]; exists && len(word) > 3 {
				score += float64(freq)
			}
		}

		// Normalize by sentence length
		if len(sentenceWords) > 0 {
			score = score / float64(len(sentenceWords))
		}

		// Boost score for sentences with important keywords
		importantKeywords := []string{
			"require", "shall", "must", "prohibit", "establish", "implement",
			"effective", "deadline", "penalty", "violation", "compliance",
			"purpose", "objective", "goal", "intent", "scope", "apply",
		}

		lowerSentence := strings.ToLower(sentence)
		for _, keyword := range importantKeywords {
			if strings.Contains(lowerSentence, keyword) {
				score += 2.0
			}
		}

		// Boost score for sentences at the beginning
		if i < 3 {
			score += 1.0
		}

		scores[i] = score
	}

	return scores
}

// selectTopSentences selects the best sentences for summarization
func (s *NLPService) selectTopSentences(sentences []string, scores map[int]float64, maxLength int) []string {
	type sentenceScore struct {
		index int
		score float64
		text  string
	}

	var scoredSentences []sentenceScore
	for i, sentence := range sentences {
		if score, exists := scores[i]; exists {
			scoredSentences = append(scoredSentences, sentenceScore{
				index: i,
				score: score,
				text:  sentence,
			})
		}
	}

	// Sort by score (descending)
	for i := 0; i < len(scoredSentences)-1; i++ {
		for j := i + 1; j < len(scoredSentences); j++ {
			if scoredSentences[i].score < scoredSentences[j].score {
				scoredSentences[i], scoredSentences[j] = scoredSentences[j], scoredSentences[i]
			}
		}
	}

	// Select sentences that fit within maxLength
	var selectedSentences []sentenceScore
	totalLength := 0

	for _, ss := range scoredSentences {
		if totalLength+len(ss.text) <= maxLength {
			selectedSentences = append(selectedSentences, ss)
			totalLength += len(ss.text)
		}
		if totalLength >= int(float64(maxLength)*0.8) {
			break
		}
	}

	// Sort selected sentences by original order
	for i := 0; i < len(selectedSentences)-1; i++ {
		for j := i + 1; j < len(selectedSentences); j++ {
			if selectedSentences[i].index > selectedSentences[j].index {
				selectedSentences[i], selectedSentences[j] = selectedSentences[j], selectedSentences[i]
			}
		}
	}

	// Extract text
	var result []string
	for _, ss := range selectedSentences {
		result = append(result, ss.text)
	}

	return result
}

// extractClassificationFeatures extracts features for document classification
func (s *NLPService) extractClassificationFeatures(content string) map[string]float64 {
	features := make(map[string]float64)

	text := strings.ToLower(content)
	words := strings.Fields(text)

	// Feature categories with their keywords
	featureKeywords := map[string][]string{
		"environmental":      {"environment", "environmental", "pollution", "emission", "waste", "air", "water", "climate"},
		"financial":          {"financial", "banking", "credit", "loan", "investment", "securities", "insurance", "mortgage"},
		"healthcare":         {"health", "healthcare", "medical", "drug", "pharmaceutical", "patient", "hospital", "clinic"},
		"transportation":     {"transportation", "transport", "vehicle", "traffic", "highway", "aviation", "aircraft", "maritime"},
		"energy":             {"energy", "power", "electricity", "electric", "utility", "nuclear", "renewable", "solar"},
		"telecommunications": {"telecommunications", "telecom", "internet", "broadband", "wireless", "phone", "cellular"},
		"agriculture":        {"food", "agriculture", "agricultural", "farming", "farm", "crop", "livestock", "pesticide"},
		"education":          {"education", "educational", "school", "university", "college", "student", "teacher", "academic"},
		"employment":         {"employment", "labor", "worker", "workplace", "job", "wage", "salary", "benefit"},
		"consumer":           {"consumer", "product", "safety", "recall", "warranty", "fraud", "advertising", "marketing"},
		"housing":            {"housing", "construction", "building", "real estate", "property", "zoning", "development"},
		"immigration":        {"immigration", "immigrant", "visa", "citizenship", "border", "refugee", "asylum"},
		"security":           {"security", "national security", "defense", "military", "terrorism", "intelligence", "classified"},
		"trade":              {"trade", "commerce", "import", "export", "tariff", "customs", "international", "business"},
	}

	// Calculate feature scores
	for feature, keywords := range featureKeywords {
		score := 0.0
		for _, keyword := range keywords {
			for _, word := range words {
				if strings.Contains(word, keyword) {
					score += 1.0
				}
			}
		}
		features[feature] = score / float64(len(words)) // Normalize by document length
	}

	return features
}

// classifyUsingFeatures classifies document based on extracted features
func (s *NLPService) classifyUsingFeatures(features map[string]float64) string {
	// Find the feature with the highest score
	maxScore := 0.0
	bestCategory := ""

	categoryMapping := map[string]string{
		"environmental":      "Environmental Protection",
		"financial":          "Financial Services",
		"healthcare":         "Healthcare and Medical",
		"transportation":     "Transportation",
		"energy":             "Energy",
		"telecommunications": "Telecommunications",
		"agriculture":        "Food and Agriculture",
		"education":          "Education",
		"employment":         "Employment and Labor",
		"consumer":           "Consumer Protection",
		"housing":            "Housing and Construction",
		"immigration":        "Immigration",
		"security":           "National Security",
		"trade":              "Trade and Commerce",
	}

	for feature, score := range features {
		if score > maxScore {
			maxScore = score
			bestCategory = feature
		}
	}

	// Return mapped category name or default
	if mappedCategory, exists := categoryMapping[bestCategory]; exists && maxScore > 0.01 {
		return mappedCategory
	}

	return "General Policy"
}

// extractRelativeDates extracts relative date expressions like "30 days from now"
func (s *NLPService) extractRelativeDates(sentence string) []Deadline {
	var deadlines []Deadline

	// Patterns for relative dates
	patterns := []struct {
		regex       string
		description string
	}{
		{`(\d+)\s+days?\s+from\s+now`, "deadline in %s days"},
		{`within\s+(\d+)\s+days?`, "deadline within %s days"},
		{`(\d+)\s+days?\s+after`, "deadline %s days after"},
		{`(\d+)\s+weeks?\s+from\s+now`, "deadline in %s weeks"},
		{`within\s+(\d+)\s+weeks?`, "deadline within %s weeks"},
		{`(\d+)\s+months?\s+from\s+now`, "deadline in %s months"},
		{`within\s+(\d+)\s+months?`, "deadline within %s months"},
	}

	for _, pattern := range patterns {
		re := regexp.MustCompile(pattern.regex)
		matches := re.FindAllStringSubmatch(sentence, -1)

		for _, match := range matches {
			if len(match) >= 2 {
				if num, err := strconv.Atoi(match[1]); err == nil {
					var targetDate time.Time
					var desc string

					if strings.Contains(pattern.regex, "days") {
						targetDate = time.Now().AddDate(0, 0, num)
						desc = fmt.Sprintf(pattern.description, match[1])
					} else if strings.Contains(pattern.regex, "weeks") {
						targetDate = time.Now().AddDate(0, 0, num*7)
						desc = fmt.Sprintf(pattern.description, match[1])
					} else if strings.Contains(pattern.regex, "months") {
						targetDate = time.Now().AddDate(0, num, 0)
						desc = fmt.Sprintf(pattern.description, match[1])
					}

					deadline := Deadline{
						Date:        targetDate,
						Description: desc,
						Context:     sentence,
						Urgency:     "normal",
					}
					deadlines = append(deadlines, deadline)
				}
			}
		}
	}

	return deadlines
}

// analyzeTechnicalTerms performs comprehensive analysis of technical terminology in content
func (s *NLPService) analyzeTechnicalTerms(content string) map[string]interface{} {
	result := make(map[string]interface{})
	lowerContent := strings.ToLower(content)
	words := strings.Fields(lowerContent)

	// Domain-specific terminology dictionaries
	domainDictionaries := map[string][]string{
		"regulatory": {
			"regulation", "compliance", "implementation", "enforcement", "violation",
			"penalty", "standard", "requirement", "procedure", "protocol", "mandate",
			"statute", "provision", "ordinance", "directive", "guideline", "rule",
			"code", "law", "legislation", "act", "bill", "decree", "order", "sanction",
			"authorization", "prohibition", "restriction", "limitation", "exemption",
		},
		"legal": {
			"jurisdiction", "liability", "plaintiff", "defendant", "litigation",
			"adjudication", "precedent", "jurisprudence", "statutory", "judicial",
			"legal", "lawful", "unlawful", "illegal", "legality", "prosecution",
			"defense", "appeal", "verdict", "judgment", "ruling", "opinion", "dissent",
			"concurrence", "injunction", "remedy", "damages", "restitution", "relief",
		},
		"financial": {
			"budget", "cost", "expense", "funding", "allocation", "appropriation",
			"fiscal", "financial", "monetary", "economic", "revenue", "expenditure",
			"disbursement", "reimbursement", "payment", "fee", "fine", "penalty",
			"tax", "subsidy", "grant", "loan", "credit", "debit", "asset", "liability",
		},
		"administrative": {
			"procedure", "process", "protocol", "workflow", "operation", "function",
			"administration", "management", "oversight", "supervision", "coordination",
			"implementation", "execution", "delegation", "authority", "responsibility",
			"accountability", "reporting", "documentation", "record", "file", "form",
		},
		"technical": {
			"specification", "standard", "protocol", "interface", "system", "framework",
			"architecture", "infrastructure", "platform", "application", "software",
			"hardware", "network", "database", "algorithm", "encryption", "authentication",
			"authorization", "validation", "verification", "certification", "accreditation",
		},
	}

	// Count terms by category
	categoryMatches := make(map[string]int)
	totalMatches := 0

	// Initialize category counts
	for category := range domainDictionaries {
		categoryMatches[category] = 0
	}

	// Process each word and check against dictionaries
	for _, word := range words {
		word = strings.Trim(word, ".,!?;:()")
		if len(word) < 3 {
			continue
		}

		for category, terms := range domainDictionaries {
			for _, term := range terms {
				// Exact match
				if word == term {
					categoryMatches[category]++
					totalMatches++
					break
				}

				// Partial match for compound terms
				if len(term) > 7 && strings.Contains(word, term) {
					categoryMatches[category]++
					totalMatches++
					break
				}
			}
		}
	}

	// Check for multi-word terms
	for category, terms := range domainDictionaries {
		for _, term := range terms {
			if strings.Contains(term, " ") {
				if strings.Contains(lowerContent, term) {
					categoryMatches[category]++
					totalMatches++
				}
			}
		}
	}

	// Calculate density and domain specificity
	wordCount := len(words)
	density := float64(totalMatches) / float64(wordCount)

	// Find dominant categories
	dominantCategories := []string{}
	maxCount := 0

	for category, count := range categoryMatches {
		if count > maxCount {
			maxCount = count
			dominantCategories = []string{category}
		} else if count == maxCount && maxCount > 0 {
			dominantCategories = append(dominantCategories, category)
		}
	}

	// Calculate domain specificity (ratio of dominant category to total matches)
	domainSpecificity := 0.0
	if totalMatches > 0 && maxCount > 0 {
		domainSpecificity = float64(maxCount) / float64(totalMatches)
	}

	// Calculate regulatory complexity based on term variety and density
	regulatoryComplexity := 0.0
	if len(dominantCategories) > 0 {
		uniqueTerms := make(map[string]bool)
		for _, category := range dominantCategories {
			for _, term := range domainDictionaries[category] {
				if strings.Contains(lowerContent, term) {
					uniqueTerms[term] = true
				}
			}
		}
		termVariety := len(uniqueTerms)
		regulatoryComplexity = float64(termVariety) * density
	}

	// Prepare result
	result["density"] = density
	result["total_matches"] = totalMatches
	result["categories"] = categoryMatches
	result["dominant_categories"] = dominantCategories
	result["domain_specificity"] = domainSpecificity
	result["regulatory_complexity"] = regulatoryComplexity

	return result
}
