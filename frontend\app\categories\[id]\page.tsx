'use client'

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  FolderIcon,
  TagIcon,
  DocumentTextIcon,
  PencilIcon,
  TrashIcon,
  ArrowLeftIcon,
  EyeIcon,
  BuildingOfficeIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import { Category } from '../../types';

const CategoryDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [category, setCategory] = useState<Category | null>(null);
  const [documents, setDocuments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [documentsLoading, setDocumentsLoading] = useState(false);

  const categoryId = params?.id as string;

  useEffect(() => {
    if (!categoryId) return;

    const fetchCategory = async () => {
      try {
        setLoading(true);
        const response = await apiService.getCategory(parseInt(categoryId));
        setCategory(response.data);

        // Fetch documents in this category
        setDocumentsLoading(true);
        try {
          const documentsResponse = await apiService.getCategoryDocuments(parseInt(categoryId), { per_page: 20 });
          setDocuments(documentsResponse.data || []);
        } catch (err) {
          console.log('Documents not available for this category');
        } finally {
          setDocumentsLoading(false);
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch category');
      } finally {
        setLoading(false);
      }
    };

    fetchCategory();
  }, [categoryId]);

  const handleDelete = async () => {
    if (!category || !confirm('Are you sure you want to delete this category?')) return;
    
    try {
      await apiService.deleteCategory(category.id);
      router.push('/categories');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete category');
    }
  };

  const canEdit = () => {
    if (!user || !category) return false;
    return user.role === 'admin' || user.role === 'editor';
  };

  const canDelete = () => {
    if (!user || !category) return false;
    return user.role === 'admin';
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !category) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Category not found</h3>
            <p className="text-gray-600 mb-4">{error || 'The requested category could not be found.'}</p>
            <Link
              href="/categories"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Categories
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/categories"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Categories
          </Link>
          
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-4">
                <div 
                  className="w-8 h-8 rounded-full flex items-center justify-center"
                  style={{ backgroundColor: category.color || '#3B82F6' }}
                >
                  <FolderIcon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">{category.name}</h1>
                  {category.slug && (
                    <p className="text-sm text-gray-500">/{category.slug}</p>
                  )}
                </div>
              </div>
              
              {category.description && (
                <p className="text-lg text-gray-600 mb-6">{category.description}</p>
              )}
            </div>
            
            {/* Actions */}
            <div className="flex items-center space-x-2 mt-4 lg:mt-0">
              {canEdit() && (
                <Link
                  href={`/categories/${category.id}/edit`}
                  className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                  title="Edit Category"
                >
                  <PencilIcon className="h-5 w-5" />
                </Link>
              )}
              {canDelete() && (
                <button
                  onClick={handleDelete}
                  className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  title="Delete Category"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Documents in this Category */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">
                  Documents in this Category ({category.document_count || 0})
                </h2>
                <Link
                  href={`/documents?category=${parseInt(category.id.toString())}`}
                  className="text-sm text-primary-600 hover:text-primary-700"
                >
                  View all
                </Link>
              </div>
              
              {documentsLoading ? (
                <div className="space-y-4">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                    </div>
                  ))}
                </div>
              ) : documents.length === 0 ? (
                <div className="text-center py-8">
                  <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No documents in this category yet.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {documents.slice(0, 5).map((document) => (
                    <div key={document.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <Link
                            href={`/documents/${document.id}`}
                            className="text-lg font-medium text-gray-900 hover:text-primary-600"
                          >
                            {document.title}
                          </Link>
                          {document.abstract && (
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                              {document.abstract}
                            </p>
                          )}
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            {document.agency && (
                              <span className="flex items-center">
                                <BuildingOfficeIcon className="h-3 w-3 mr-1" />
                                {typeof document.agency === 'string' ? document.agency : document.agency?.name || 'Unknown Agency'}
                              </span>
                            )}
                            {document.publication_date && (
                              <span className="flex items-center">
                                <CalendarIcon className="h-3 w-3 mr-1" />
                                {new Date(document.publication_date).toLocaleDateString()}
                              </span>
                            )}
                            <span className="flex items-center">
                              <EyeIcon className="h-3 w-3 mr-1" />
                              {document.view_count || 0} views
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            document.status === 'published' ? 'bg-green-100 text-green-800' :
                            document.status === 'draft' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {document.status?.toUpperCase()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Subcategories */}
            {category.sub_categories && category.sub_categories.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Subcategories</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {category.sub_categories.map((subcategory) => (
                    <Link
                      key={subcategory.id}
                      href={`/categories/${parseInt(subcategory.id.toString())}`}
                      className="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center">
                        <div 
                          className="w-6 h-6 rounded-full flex items-center justify-center mr-3"
                          style={{ backgroundColor: subcategory.color || '#3B82F6' }}
                        >
                          <FolderIcon className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{subcategory.name}</h3>
                          {subcategory.description && (
                            <p className="text-sm text-gray-600 mt-1">{subcategory.description}</p>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Category Information</h3>
              
              <div className="space-y-4">
                {category.parent_category && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Parent Category</p>
                    <Link 
                      href={`/categories/${category.parent_category.id}`}
                      className="text-sm text-primary-600 hover:text-primary-700"
                    >
                      {category.parent_category.name}
                    </Link>
                  </div>
                )}

                {category.cfr_title && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">CFR Title</p>
                    <p className="text-sm text-gray-600">{category.cfr_title}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-900">Document Count</p>
                  <p className="text-sm text-gray-600">{category.document_count || 0}</p>
                </div>

                {category.sort_order !== undefined && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Sort Order</p>
                    <p className="text-sm text-gray-600">{category.sort_order}</p>
                  </div>
                )}

                <div>
                  <p className="text-sm font-medium text-gray-900">Status</p>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    category.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {category.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>

                {category.created_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Created</p>
                    <p className="text-sm text-gray-600">
                      {new Date(category.created_at).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default CategoryDetailPage;
