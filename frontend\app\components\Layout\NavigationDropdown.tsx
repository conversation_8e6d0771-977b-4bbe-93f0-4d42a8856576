'use client'

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '../../stores/authStore';

interface NavigationItem {
  label: string;
  href: string;
  requiresAuth?: boolean;
  adminOnly?: boolean;
  isEnterprise?: boolean;
}

interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

const navigationSections: NavigationSection[] = [
  {
    title: 'Core Features',
    items: [
      { label: 'Home', href: '/' },
      { label: 'Documents', href: '/documents' },
      { label: 'Regulations', href: '/regulations' },
      { label: 'Agencies', href: '/agencies' },
      { label: 'Categories', href: '/categories' },
      { label: 'Proceedings', href: '/proceedings' },
      { label: 'Summary', href: '/summary' },
      { label: 'Calendar', href: '/calendar' },
      { label: 'Tasks', href: '/tasks', requiresAuth: true },
      { label: 'Dashboard', href: '/dashboard', requiresAuth: true },
      { label: 'Search', href: '/search' },
      { label: 'About', href: '/about' },
    ]
  },
  {
    title: 'Enterprise Management',
    items: [
      { label: 'Enterprise Dashboard', href: '/enterprise', requiresAuth: true, isEnterprise: true },
      { label: 'Content Management', href: '/enterprise/content', requiresAuth: true, isEnterprise: true },
      { label: 'Financial Management', href: '/enterprise/financial', requiresAuth: true, isEnterprise: true },
      { label: 'Compliance & Risk', href: '/enterprise/compliance', requiresAuth: true, isEnterprise: true },
      { label: 'Business Intelligence', href: '/enterprise/bi', requiresAuth: true, isEnterprise: true },
      { label: 'Human Resources', href: '/enterprise/hr', requiresAuth: true, isEnterprise: true },
    ]
  }
];

const NavigationDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { isAuthenticated, user } = useAuthStore();
  const router = useRouter();

  const handleNavigation = (href: string) => {
    router.push(href);
    setIsOpen(false);
  };

  const filteredSections = navigationSections.map(section => ({
    ...section,
    items: section.items.filter(item => {
      if (item.requiresAuth && !isAuthenticated) return false;
      if (item.adminOnly && user?.role !== 'admin') return false;
      return true;
    })
  })).filter(section => section.items.length > 0);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-md"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span>Navigate</span>
        <ChevronDownIcon 
          className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Menu */}
          <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-20 border border-gray-200 dark:border-gray-700">
            <div className="max-h-96 overflow-y-auto">
              {filteredSections.map((section, sectionIndex) => (
                <div key={section.title}>
                  {sectionIndex > 0 && (
                    <div className="border-t border-gray-200 dark:border-gray-600 my-1"></div>
                  )}
                  <div className="px-4 py-2">
                    <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      {section.title}
                    </h3>
                  </div>
                  {section.items.map((item) => (
                    <button
                      key={item.href}
                      onClick={() => handleNavigation(item.href)}
                      className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150 ${
                        item.isEnterprise
                          ? 'text-blue-700 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300'
                          : 'text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400'
                      }`}
                    >
                      {item.isEnterprise && <span className="mr-2">🏢</span>}
                      {item.label}
                    </button>
                  ))}
                </div>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default NavigationDropdown;
