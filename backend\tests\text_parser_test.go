package tests

import (
	"testing"
	"time"

	"federal-register-clone/internal/services"
)

func TestTextParserService(t *testing.T) {
	parser := services.NewTextParserService()

	testCases := []struct {
		name           string
		text           string
		expectedItems  int
		expectedTypes  []string
		expectedTitles []string
	}{
		{
			name: "Simple deadline task",
			text: "no later than 180 days from today, a new review report should be posted",
			expectedItems: 1,
			expectedTypes: []string{"task"},
			expectedTitles: []string{"No later than 180 days from today, a new review report should be posted"},
		},
		{
			name: "Specific date",
			text: "The deadline is 2025-09-21 for submitting comments",
			expectedItems: 1,
			expectedTypes: []string{"task"},
			expectedTitles: []string{"Scheduled event"},
		},
		{
			name: "Add category",
			text: "Please add category Environmental Protection for this document",
			expectedItems: 1,
			expectedTypes: []string{"category"},
			expectedTitles: []string{"Environmental Protection"},
		},
		{
			name: "Add agency",
			text: "We need to add agency EPA to handle this regulation",
			expectedItems: 1,
			expectedTypes: []string{"agency"},
			expectedTitles: []string{"EPA"},
		},
		{
			name: "Complex text with multiple items",
			text: "This regulation must be reviewed no later than 30 days from today. Please add category Safety Standards and add agency OSHA. The public comment period ends 2025-12-15.",
			expectedItems: 4,
			expectedTypes: []string{"task", "category", "agency", "task"},
			expectedTitles: []string{"No later than 30 days from today", "Safety Standards", "OSHA", "Scheduled event"},
		},
		{
			name: "Time limit",
			text: "The hearing is limited to 2 hours limit for public comments",
			expectedItems: 0, // This should not create tasks as it's just a time constraint
			expectedTypes: []string{},
			expectedTitles: []string{},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			baseTime := time.Date(2025, 1, 1, 9, 0, 0, 0, time.UTC)
			items, err := parser.ParseText(tc.text, baseTime)

			if err != nil {
				t.Fatalf("ParseText failed: %v", err)
			}

			if len(items) != tc.expectedItems {
				t.Errorf("Expected %d items, got %d", tc.expectedItems, len(items))
				for i, item := range items {
					t.Logf("Item %d: Type=%s, Title=%s, Confidence=%.2f", i, item.Type, item.Title, item.Confidence)
				}
			}

			for i, expectedType := range tc.expectedTypes {
				if i >= len(items) {
					t.Errorf("Expected item %d with type %s, but only got %d items", i, expectedType, len(items))
					continue
				}
				if items[i].Type != expectedType {
					t.Errorf("Item %d: expected type %s, got %s", i, expectedType, items[i].Type)
				}
			}

			for i, expectedTitle := range tc.expectedTitles {
				if i >= len(items) {
					t.Errorf("Expected item %d with title %s, but only got %d items", i, expectedTitle, len(items))
					continue
				}
				if items[i].Title != expectedTitle {
					t.Errorf("Item %d: expected title %s, got %s", i, expectedTitle, items[i].Title)
				}
			}
		})
	}
}

func TestDateParsing(t *testing.T) {
	parser := services.NewTextParserService()
	baseTime := time.Date(2025, 1, 1, 9, 0, 0, 0, time.UTC)

	testCases := []struct {
		name         string
		text         string
		expectedDate time.Time
	}{
		{
			name: "Days from today",
			text: "30 days from today",
			expectedDate: time.Date(2025, 1, 31, 9, 0, 0, 0, time.UTC),
		},
		{
			name: "Weeks from now",
			text: "2 weeks from now",
			expectedDate: time.Date(2025, 1, 15, 9, 0, 0, 0, time.UTC),
		},
		{
			name: "Specific date",
			text: "2025-06-15",
			expectedDate: time.Date(2025, 6, 15, 9, 0, 0, 0, time.UTC),
		},
		{
			name: "No later than",
			text: "no later than 7 days from today",
			expectedDate: time.Date(2025, 1, 8, 9, 0, 0, 0, time.UTC),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			items, err := parser.ParseText(tc.text, baseTime)
			if err != nil {
				t.Fatalf("ParseText failed: %v", err)
			}

			if len(items) == 0 {
				t.Fatalf("Expected at least one item, got none")
			}

			item := items[0]
			if item.DueDate == nil {
				t.Fatalf("Expected due date to be set, got nil")
			}

			if !item.DueDate.Equal(tc.expectedDate) {
				t.Errorf("Expected due date %v, got %v", tc.expectedDate, *item.DueDate)
			}
		})
	}
}

func TestEntityParsing(t *testing.T) {
	parser := services.NewTextParserService()
	baseTime := time.Date(2025, 1, 1, 9, 0, 0, 0, time.UTC)

	testCases := []struct {
		name         string
		text         string
		expectedType string
		expectedName string
	}{
		{
			name: "Add category",
			text: "add category Environmental Safety",
			expectedType: "category",
			expectedName: "Environmental Safety",
		},
		{
			name: "Add agency",
			text: "add agency Department of Energy",
			expectedType: "agency",
			expectedName: "Department of Energy",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			items, err := parser.ParseText(tc.text, baseTime)
			if err != nil {
				t.Fatalf("ParseText failed: %v", err)
			}

			if len(items) == 0 {
				t.Fatalf("Expected at least one item, got none")
			}

			item := items[0]
			if item.Type != tc.expectedType {
				t.Errorf("Expected type %s, got %s", tc.expectedType, item.Type)
			}

			if item.Title != tc.expectedName {
				t.Errorf("Expected name %s, got %s", tc.expectedName, item.Title)
			}
		})
	}
}

func TestConfidenceScoring(t *testing.T) {
	parser := services.NewTextParserService()
	baseTime := time.Date(2025, 1, 1, 9, 0, 0, 0, time.UTC)

	// Test that more specific patterns have higher confidence
	items1, err := parser.ParseText("2025-06-15", baseTime) // Specific date format
	if err != nil {
		t.Fatalf("ParseText failed: %v", err)
	}

	items2, err := parser.ParseText("no later than 30 days from today", baseTime) // Less specific
	if err != nil {
		t.Fatalf("ParseText failed: %v", err)
	}

	if len(items1) == 0 || len(items2) == 0 {
		t.Fatalf("Expected items from both texts")
	}

	if items1[0].Confidence <= items2[0].Confidence {
		t.Errorf("Expected specific date format to have higher confidence than relative date. Got %.2f vs %.2f", 
			items1[0].Confidence, items2[0].Confidence)
	}
}
