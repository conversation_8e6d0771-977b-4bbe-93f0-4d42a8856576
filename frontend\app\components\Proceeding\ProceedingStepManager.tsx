import React, { useState } from 'react';
import {
  Check<PERSON><PERSON>cleI<PERSON>,
  ClockIcon,
  PlayIcon,
  PauseIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import ProceedingStepCard from './ProceedingStepCard';
import ProceedingStepForm from './ProceedingStepForm';

interface ProceedingStep {
  id: number;
  proceeding_id: number;
  step_order: number;
  step_type: string;
  name: string;
  description: string;
  status: string;
  requires_previous_completion: boolean;
  is_mandatory: boolean;
  allows_parallel_execution: boolean;
  content_type: string;
  text_content?: string;
  voice_recording?: string;
  document_content?: string;
  video_content?: string;
  planned_start_date?: string;
  actual_start_date?: string;
  planned_end_date?: string;
  actual_end_date?: string;
  estimated_duration?: number;
  actual_duration?: number;
  completion_criteria: string;
  completion_evidence?: string;
  completed_at?: string;
  requires_review: boolean;
  review_completed: boolean;
  reviewed_at?: string;
  reviewed_by?: {
    id: number;
    username: string;
    email: string;
  };
  review_comments?: string;
  assigned_to?: {
    id: number;
    username: string;
    email: string;
  };
  owner: {
    id: number;
    username: string;
    email: string;
  };
  completed_by?: {
    id: number;
    username: string;
    email: string;
  };
  priority: string;
  is_optional: boolean;
  is_critical: boolean;
  notes?: string;
  progress_percent: number;
  created_at: string;
  updated_at: string;
}

interface ProceedingStepManagerProps {
  proceedingId: number;
  steps: ProceedingStep[];
  onUpdateStepStatus: (stepId: number, status: string, data?: any) => Promise<void>;
  onRefresh: () => void;
  canEdit?: boolean;
}

const ProceedingStepManager: React.FC<ProceedingStepManagerProps> = ({
  proceedingId,
  steps,
  onUpdateStepStatus,
  onRefresh,
  canEdit = false
}) => {
  const [expandedSteps, setExpandedSteps] = useState<Set<number>>(new Set());
  const [updatingStep, setUpdatingStep] = useState<number | null>(null);
  const [showStepForm, setShowStepForm] = useState(false);
  const [editingStep, setEditingStep] = useState<ProceedingStep | null>(null);
  const [viewingStep, setViewingStep] = useState<ProceedingStep | null>(null);

  const canStartStep = (step: ProceedingStep): boolean => {
    if (!step.requires_previous_completion) return true;
    
    // For administrative proceedings, steps must be completed in sequential order
    const previousSteps = steps.filter(s => s.step_order < step.step_order && s.is_mandatory);
    return previousSteps.every(prevStep => 
      prevStep.status === 'completed'
    );
  };

  const handleStatusChange = async (stepId: number, newStatus: string) => {
    if (!canEdit) return;

    const step = steps.find(s => s.id === stepId);
    if (!step) return;

    // Validate sequential execution for administrative proceedings
    if ((newStatus === 'in_progress' || newStatus === 'completed') && !canStartStep(step)) {
      alert('Previous mandatory steps must be completed before starting this step (sequential execution required)');
      return;
    }

    setUpdatingStep(stepId);
    try {
      await onUpdateStepStatus(stepId, newStatus);
    } finally {
      setUpdatingStep(null);
    }
  };

  const handleEditStep = (step: ProceedingStep) => {
    setEditingStep(step);
    setShowStepForm(true);
  };

  const handleViewStep = (step: ProceedingStep) => {
    setViewingStep(step);
  };

  const handleStepFormSubmit = async (formData: any) => {
    try {
      // Handle step update logic here
      setShowStepForm(false);
      setEditingStep(null);
      onRefresh();
    } catch (error) {
      console.error('Failed to update step:', error);
    }
  };

  const handleStepFormCancel = () => {
    setShowStepForm(false);
    setEditingStep(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'on_hold':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  if (showStepForm) {
    return (
      <div className="space-y-4">
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
            {editingStep ? 'Edit Step' : 'Add New Step'}
          </h3>
          <ProceedingStepForm
            initialData={editingStep || undefined}
            onSubmit={handleStepFormSubmit}
            onCancel={handleStepFormCancel}
            isEdit={!!editingStep}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header with Add Step Button */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
          Administrative Proceeding Steps ({steps.length} total)
        </h3>
        {canEdit && (
          <button
            onClick={() => setShowStepForm(true)}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Step
          </button>
        )}
      </div>

      {/* Progress Overview */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Progress Overview
        </h4>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {['not_started', 'in_progress', 'under_review', 'completed', 'failed'].map(status => {
            const count = steps.filter(s => s.status === status).length;
            return (
              <div key={status} className="text-center">
                <div className={`text-2xl font-bold ${getStatusColor(status).split(' ')[1]}`}>
                  {count}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                  {status.replace('_', ' ')}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Steps List */}
      <div className="space-y-4">
        {steps.length === 0 ? (
          <div className="text-center py-12">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No steps defined</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by adding the first step to this administrative proceeding.
            </p>
            {canEdit && (
              <div className="mt-6">
                <button
                  onClick={() => setShowStepForm(true)}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add First Step
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {steps
              .sort((a, b) => a.step_order - b.step_order)
              .map((step) => {
                const canStart = canStartStep(step);
                return (
                  <ProceedingStepCard
                    key={step.id}
                    step={step}
                    canEdit={canEdit}
                    canStart={canStart}
                    onStatusChange={handleStatusChange}
                    onUpdateStatus={onUpdateStepStatus}
                    onEdit={handleEditStep}
                    onView={handleViewStep}
                  />
                );
              })}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProceedingStepManager;
