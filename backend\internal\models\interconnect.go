package models

import (
	"time"

	"gorm.io/gorm"
)

// Interconnect represents relationships between different entities in the system
type Interconnect struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Source entity
	SourceType string `json:"source_type" gorm:"not null"` // "document", "regulation", "agency", "category", etc.
	SourceID   uint   `json:"source_id" gorm:"not null"`

	// Target entity
	TargetType string `json:"target_type" gorm:"not null"` // "finance", "task", "proceeding", "hr", "bi", etc.
	TargetID   uint   `json:"target_id" gorm:"not null"`

	// Relationship details
	Relationship string `json:"relationship" gorm:"not null"` // "related_to", "depends_on", "impacts", etc.
	Description  string `json:"description" gorm:"type:text"`
	IsActive     bool   `json:"is_active" gorm:"default:true"`

	// Metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ProceedingIntegration represents integration between proceedings and other entities
type ProceedingIntegration struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Proceeding reference
	ProceedingID uint       `json:"proceeding_id" gorm:"not null"`
	Proceeding   Proceeding `json:"proceeding" gorm:"foreignKey:ProceedingID"`

	// Integration details
	IntegrationType string `json:"integration_type" gorm:"not null"` // "linked", "triggered", "dependent"
	ExternalID      string `json:"external_id"`                      // External system ID
	ExternalSystem  string `json:"external_system"`                  // External system name
	Configuration   string `json:"configuration" gorm:"type:text"`   // JSON configuration
	IsActive        bool   `json:"is_active" gorm:"default:true"`

	// Target entity
	EntityType string `json:"entity_type" gorm:"not null"` // "task", "document", "regulation"
	EntityID   uint   `json:"entity_id" gorm:"not null"`

	// Additional details
	Status      string `json:"status" gorm:"default:'active'"` // "active", "inactive", "completed"
	Description string `json:"description" gorm:"type:text"`

	// Metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for Interconnect model
func (Interconnect) TableName() string {
	return "interconnects"
}

// TableName returns the table name for ProceedingIntegration model
func (ProceedingIntegration) TableName() string {
	return "proceeding_integrations"
}
