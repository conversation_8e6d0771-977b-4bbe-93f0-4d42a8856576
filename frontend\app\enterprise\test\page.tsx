'use client';

import React, { useState } from 'react';
import { 
  contentApi, 
  financialApi, 
  complianceApi, 
  biApi, 
  hrApi 
} from '../../services/enterpriseApi';

const EnterpriseTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<{ [key: string]: any }>({});
  const [loading, setLoading] = useState<{ [key: string]: boolean }>({});

  const runTest = async (testName: string, testFunction: () => Promise<any>) => {
    setLoading(prev => ({ ...prev, [testName]: true }));
    try {
      const result = await testFunction();
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { success: true, data: result, timestamp: new Date().toISOString() }
      }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [testName]: { success: false, error: error.message, timestamp: new Date().toISOString() }
      }));
    } finally {
      setLoading(prev => ({ ...prev, [testName]: false }));
    }
  };

  const testSuites = [
    {
      name: 'Content Management',
      tests: [
        {
          name: 'Get Content Repositories',
          test: () => contentApi.getRepositories()
        },
        {
          name: 'Get Content Workflows',
          test: () => contentApi.getWorkflows()
        },
        {
          name: 'Get Workflow Instances',
          test: () => contentApi.getWorkflowInstances()
        }
      ]
    },
    {
      name: 'Financial Management',
      tests: [
        {
          name: 'Get Chart of Accounts',
          test: () => financialApi.getAccounts()
        },
        {
          name: 'Get General Ledger Entries',
          test: () => financialApi.getGLEntries()
        },
        {
          name: 'Get Budget Plans',
          test: () => financialApi.getBudgets()
        },
        {
          name: 'Get Cost Centers',
          test: () => financialApi.getCostCenters()
        },
        {
          name: 'Get Financial Reports',
          test: () => financialApi.getFinancialReports()
        }
      ]
    },
    {
      name: 'Compliance & Risk',
      tests: [
        {
          name: 'Get Compliance Requirements',
          test: () => complianceApi.getRequirements()
        },
        {
          name: 'Get Compliance Assessments',
          test: () => complianceApi.getAssessments()
        },
        {
          name: 'Get Risk Assessments',
          test: () => complianceApi.getRisks()
        },
        {
          name: 'Get Risk Dashboard',
          test: () => complianceApi.getRiskDashboard()
        },
        {
          name: 'Get Compliance Findings',
          test: () => complianceApi.getFindings()
        },
        {
          name: 'Get Policies',
          test: () => complianceApi.getPolicies()
        }
      ]
    },
    {
      name: 'Business Intelligence',
      tests: [
        {
          name: 'Get Data Warehouses',
          test: () => biApi.getDataWarehouses()
        },
        {
          name: 'Get Data Sources',
          test: () => biApi.getDataSources()
        },
        {
          name: 'Get Dashboards',
          test: () => biApi.getDashboards()
        },
        {
          name: 'Get Reports',
          test: () => biApi.getReports()
        },
        {
          name: 'Get KPIs',
          test: () => biApi.getKPIs()
        },
        {
          name: 'Get Data Mining Models',
          test: () => biApi.getDataMiningModels()
        }
      ]
    },
    {
      name: 'Human Resources',
      tests: [
        {
          name: 'Get Employees',
          test: () => hrApi.getEmployees()
        },
        {
          name: 'Get Departments',
          test: () => hrApi.getDepartments()
        },
        {
          name: 'Get Positions',
          test: () => hrApi.getPositions()
        },
        {
          name: 'Get Performance Reviews',
          test: () => hrApi.getPerformanceReviews()
        },
        {
          name: 'Get Trainings',
          test: () => hrApi.getTrainings()
        },
        {
          name: 'Get Training Enrollments',
          test: () => hrApi.getTrainingEnrollments()
        },
        {
          name: 'Get HR Dashboard',
          test: () => hrApi.getHRDashboard()
        }
      ]
    }
  ];

  const runAllTests = async () => {
    for (const suite of testSuites) {
      for (const test of suite.tests) {
        await runTest(`${suite.name} - ${test.name}`, test.test);
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }
  };

  const getTestKey = (suiteName: string, testName: string) => `${suiteName} - ${testName}`;

  const getStatusIcon = (testKey: string) => {
    if (loading[testKey]) return '⏳';
    if (!testResults[testKey]) return '⚪';
    return testResults[testKey].success ? '✅' : '❌';
  };

  const getStatusColor = (testKey: string) => {
    if (loading[testKey]) return 'text-yellow-600';
    if (!testResults[testKey]) return 'text-gray-400';
    return testResults[testKey].success ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Enterprise API Test Suite</h1>
          <p className="mt-2 text-gray-600">
            Comprehensive testing of all enterprise API endpoints
          </p>
        </div>

        {/* Controls */}
        <div className="mb-8 bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Test Controls</h2>
              <p className="text-sm text-gray-600">Run individual tests or all tests at once</p>
            </div>
            <button
              onClick={runAllTests}
              disabled={Object.values(loading).some(Boolean)}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {Object.values(loading).some(Boolean) ? 'Running Tests...' : 'Run All Tests'}
            </button>
          </div>
        </div>

        {/* Test Results */}
        <div className="space-y-6">
          {testSuites.map((suite) => (
            <div key={suite.name} className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">{suite.name}</h3>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {suite.tests.map((test) => {
                    const testKey = getTestKey(suite.name, test.name);
                    const result = testResults[testKey];
                    
                    return (
                      <div
                        key={test.name}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900 text-sm">{test.name}</h4>
                          <span className={`text-lg ${getStatusColor(testKey)}`}>
                            {getStatusIcon(testKey)}
                          </span>
                        </div>
                        
                        {result && (
                          <div className="text-xs text-gray-500 mb-2">
                            {new Date(result.timestamp).toLocaleTimeString()}
                          </div>
                        )}
                        
                        {result && result.success && (
                          <div className="text-xs text-green-600">
                            ✓ Success
                            {result.data?.data && Array.isArray(result.data.data) && (
                              <span className="ml-1">({result.data.data.length} items)</span>
                            )}
                          </div>
                        )}
                        
                        {result && !result.success && (
                          <div className="text-xs text-red-600">
                            ✗ Error: {result.error}
                          </div>
                        )}
                        
                        <button
                          onClick={() => runTest(testKey, test.test)}
                          disabled={loading[testKey]}
                          className="mt-2 w-full bg-gray-100 text-gray-700 px-3 py-1 rounded text-xs hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          {loading[testKey] ? 'Testing...' : 'Run Test'}
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Summary */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {Object.keys(testResults).length}
              </div>
              <div className="text-sm text-gray-600">Total Tests Run</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Object.values(testResults).filter(r => r.success).length}
              </div>
              <div className="text-sm text-gray-600">Passed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {Object.values(testResults).filter(r => !r.success).length}
              </div>
              <div className="text-sm text-gray-600">Failed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {Object.values(loading).filter(Boolean).length}
              </div>
              <div className="text-sm text-gray-600">Running</div>
            </div>
          </div>
        </div>

        {/* API Endpoints Documentation */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Enterprise API Endpoints</h3>
          <div className="text-sm text-gray-600 space-y-2">
            <p><strong>Content Management:</strong> /enterprise/content/*</p>
            <p><strong>Financial Management:</strong> /enterprise/financial/*</p>
            <p><strong>Compliance & Risk:</strong> /enterprise/compliance/*</p>
            <p><strong>Business Intelligence:</strong> /enterprise/bi/*</p>
            <p><strong>Human Resources:</strong> /enterprise/hr/*</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseTestPage;
