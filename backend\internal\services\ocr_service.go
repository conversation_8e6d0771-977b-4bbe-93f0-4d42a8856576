package services

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"federal-register-clone/internal/config"
)

// OCRService handles Optical Character Recognition
type OCRService struct {
	config             *config.Config
	tesseractPath      string
	tempDir            string
	dictionaries       map[string]map[string]bool
	languageDetectors  map[string]*regexp.Regexp
	commonPhrases      map[string][]string
	linguisticPatterns map[string]*regexp.Regexp
}

// NewOCRService creates a new OCR service
func NewOCRService(cfg *config.Config) *OCRService {
	tempDir := "./temp/ocr"
	os.MkdirAll(tempDir, 0755)

	service := &OCRService{
		config:             cfg,
		tesseractPath:      findTesseractPath(),
		tempDir:            tempDir,
		dictionaries:       make(map[string]map[string]bool),
		languageDetectors:  make(map[string]*regexp.Regexp),
		commonPhrases:      make(map[string][]string),
		linguisticPatterns: make(map[string]*regexp.Regexp),
	}

	// Initialize dictionaries and language detection patterns
	service.initializeDictionaries()
	service.initializeLanguageDetectors()
	service.initializeLinguisticPatterns()

	return service
}

// OCRResult represents the result of OCR processing
type OCRResult struct {
	Text           string                 `json:"text"`
	Confidence     float64                `json:"confidence"`
	Language       string                 `json:"language"`
	PageCount      int                    `json:"page_count"`
	WordCount      int                    `json:"word_count"`
	Metadata       map[string]interface{} `json:"metadata"`
	ProcessingTime time.Duration          `json:"processing_time"`
}

// ProcessFile performs OCR on a file
func (s *OCRService) ProcessFile(filePath string) (*OCRResult, error) {
	startTime := time.Now()

	if s.tesseractPath == "" {
		// Fallback to basic text extraction if Tesseract is not available
		return s.fallbackTextExtraction(filePath, startTime)
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}

	// Get file extension
	ext := strings.ToLower(filepath.Ext(filePath))

	switch ext {
	case ".pdf":
		return s.processPDF(filePath, startTime)
	case ".png", ".jpg", ".jpeg", ".tiff", ".bmp":
		return s.processImage(filePath, startTime)
	case ".txt":
		return s.processTextFile(filePath, startTime)
	default:
		return s.fallbackTextExtraction(filePath, startTime)
	}
}

// processPDF processes a PDF file using Tesseract
func (s *OCRService) processPDF(filePath string, startTime time.Time) (*OCRResult, error) {
	// First convert PDF to images using pdftoppm (if available)
	pdfToPpmPath := findPdfToPpmPath()
	if pdfToPpmPath == "" {
		return s.fallbackTextExtraction(filePath, startTime)
	}

	// Create temporary directory for this operation
	tempDir := filepath.Join(s.tempDir, fmt.Sprintf("pdf_%d", time.Now().UnixNano()))
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Convert PDF to images
	outputPrefix := filepath.Join(tempDir, "page")
	cmd := exec.Command(pdfToPpmPath, "-png", filePath, outputPrefix)
	if err := cmd.Run(); err != nil {
		return s.fallbackTextExtraction(filePath, startTime)
	}

	// Find generated image files
	imageFiles, err := filepath.Glob(filepath.Join(tempDir, "page-*.png"))
	if err != nil || len(imageFiles) == 0 {
		return s.fallbackTextExtraction(filePath, startTime)
	}

	// Process each page
	var allText strings.Builder
	totalConfidence := 0.0
	pageCount := len(imageFiles)

	for _, imageFile := range imageFiles {
		result, err := s.processImage(imageFile, startTime)
		if err != nil {
			continue
		}
		allText.WriteString(result.Text)
		allText.WriteString("\n\n")
		totalConfidence += result.Confidence
	}

	text := allText.String()
	avgConfidence := totalConfidence / float64(pageCount)

	return &OCRResult{
		Text:           text,
		Confidence:     avgConfidence,
		Language:       "en",
		PageCount:      pageCount,
		WordCount:      len(strings.Fields(text)),
		Metadata:       map[string]interface{}{"source": "tesseract_pdf"},
		ProcessingTime: time.Since(startTime),
	}, nil
}

// processImage processes an image file using Tesseract
func (s *OCRService) processImage(filePath string, startTime time.Time) (*OCRResult, error) {
	// Create temporary output file
	outputFile := filepath.Join(s.tempDir, fmt.Sprintf("ocr_output_%d", time.Now().UnixNano()))
	defer os.Remove(outputFile + ".txt")

	// Run Tesseract
	cmd := exec.Command(s.tesseractPath, filePath, outputFile, "-l", "eng")
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("tesseract failed: %v, stderr: %s", err, stderr.String())
	}

	// Read the output
	content, err := os.ReadFile(outputFile + ".txt")
	if err != nil {
		return nil, fmt.Errorf("failed to read OCR output: %v", err)
	}

	text := string(content)
	confidence := s.estimateConfidence(text)

	return &OCRResult{
		Text:           text,
		Confidence:     confidence,
		Language:       "en",
		PageCount:      1,
		WordCount:      len(strings.Fields(text)),
		Metadata:       map[string]interface{}{"source": "tesseract_image"},
		ProcessingTime: time.Since(startTime),
	}, nil
}

// processTextFile processes a plain text file
func (s *OCRService) processTextFile(filePath string, startTime time.Time) (*OCRResult, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read text file: %v", err)
	}

	text := string(content)

	return &OCRResult{
		Text:           text,
		Confidence:     1.0, // Text files have perfect confidence
		Language:       "en",
		PageCount:      1,
		WordCount:      len(strings.Fields(text)),
		Metadata:       map[string]interface{}{"source": "text_file"},
		ProcessingTime: time.Since(startTime),
	}, nil
}

// fallbackTextExtraction provides basic text extraction when OCR tools are not available
func (s *OCRService) fallbackTextExtraction(filePath string, startTime time.Time) (*OCRResult, error) {
	// Try to extract basic information from filename and file content
	fileName := filepath.Base(filePath)

	// For demonstration, create a basic result
	text := fmt.Sprintf("Document: %s\nProcessed at: %s\n", fileName, time.Now().Format("2006-01-02 15:04:05"))

	// Try to read file if it's a text file
	if strings.HasSuffix(strings.ToLower(filePath), ".txt") {
		if content, err := os.ReadFile(filePath); err == nil {
			text = string(content)
		}
	}

	return &OCRResult{
		Text:           text,
		Confidence:     0.5, // Lower confidence for fallback
		Language:       "en",
		PageCount:      1,
		WordCount:      len(strings.Fields(text)),
		Metadata:       map[string]interface{}{"source": "fallback_extraction"},
		ProcessingTime: time.Since(startTime),
	}, nil
}

// estimateConfidence estimates OCR confidence based on text characteristics
func (s *OCRService) estimateConfidence(text string) float64 {
	if text == "" {
		return 0.0
	}

	// Basic heuristics for confidence estimation
	confidence := 0.8 // Base confidence

	// Check for common OCR errors
	errorPatterns := []string{
		`[^\w\s\.,!?;:'"()-]`, // Unusual characters
		`\s{3,}`,              // Multiple spaces
		`[a-z][A-Z]`,          // Mixed case within words
	}

	for _, pattern := range errorPatterns {
		if matched, _ := regexp.MatchString(pattern, text); matched {
			confidence -= 0.1
		}
	}

	// Comprehensive dictionary word checking with language-specific analysis
	words := strings.Fields(text)
	if len(words) > 0 {
		// Load appropriate dictionary based on detected language
		dictionary := s.loadLanguageDictionary("en") // Default to English

		// Analyze text for language detection
		detectedLanguage := s.detectLanguage(text)
		if detectedLanguage != "en" && s.hasLanguageDictionary(detectedLanguage) {
			dictionary = s.loadLanguageDictionary(detectedLanguage)
		}

		// Perform comprehensive word validation
		validationResults := s.validateWords(words, dictionary)

		// Apply confidence adjustments based on validation results
		confidence = confidence * validationResults.validRatio

		// Apply additional adjustments based on linguistic patterns
		confidence = s.adjustConfidenceWithLinguisticAnalysis(confidence, text, validationResults)
	}

	if confidence < 0.1 {
		confidence = 0.1
	}
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// findTesseractPath finds the Tesseract executable path
func findTesseractPath() string {
	// Common paths for Tesseract
	paths := []string{
		"tesseract",
		"/usr/bin/tesseract",
		"/usr/local/bin/tesseract",
		"C:\\Program Files\\Tesseract-OCR\\tesseract.exe",
		"C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe",
	}

	for _, path := range paths {
		if _, err := exec.LookPath(path); err == nil {
			return path
		}
	}

	return ""
}

// findPdfToPpmPath finds the pdftoppm executable path
func findPdfToPpmPath() string {
	// Common paths for pdftoppm
	paths := []string{
		"pdftoppm",
		"/usr/bin/pdftoppm",
		"/usr/local/bin/pdftoppm",
	}

	for _, path := range paths {
		if _, err := exec.LookPath(path); err == nil {
			return path
		}
	}

	return ""
}

// ProcessFileAsync processes a file asynchronously
func (s *OCRService) ProcessFileAsync(filePath string, callback func(*OCRResult, error)) {
	go func() {
		result, err := s.ProcessFile(filePath)
		callback(result, err)
	}()
}

// WordValidationResult contains the results of word validation
type WordValidationResult struct {
	validCount   int
	totalCount   int
	validRatio   float64
	invalidWords []string
	suggestions  map[string][]string
}

// initializeDictionaries loads dictionaries for different languages
func (s *OCRService) initializeDictionaries() {
	// Initialize English dictionary (most common words)
	englishDict := make(map[string]bool)

	// Common English words (abbreviated list for demonstration)
	englishWords := []string{
		"the", "be", "to", "of", "and", "a", "in", "that", "have", "I",
		"it", "for", "not", "on", "with", "he", "as", "you", "do", "at",
		"this", "but", "his", "by", "from", "they", "we", "say", "her", "she",
		"or", "an", "will", "my", "one", "all", "would", "there", "their", "what",
		"so", "up", "out", "if", "about", "who", "get", "which", "go", "me",
		"when", "make", "can", "like", "time", "no", "just", "him", "know", "take",
		"people", "into", "year", "your", "good", "some", "could", "them", "see", "other",
		"than", "then", "now", "look", "only", "come", "its", "over", "think", "also",
		"back", "after", "use", "two", "how", "our", "work", "first", "well", "way",
		"even", "new", "want", "because", "any", "these", "give", "day", "most", "us",
	}

	for _, word := range englishWords {
		englishDict[word] = true
	}

	s.dictionaries["en"] = englishDict

	// Add other language dictionaries as needed
	// s.dictionaries["es"] = spanishDict
	// s.dictionaries["fr"] = frenchDict
}

// initializeLanguageDetectors sets up language detection patterns
func (s *OCRService) initializeLanguageDetectors() {
	// English detection pattern (common English words and patterns)
	s.languageDetectors["en"] = regexp.MustCompile(`\b(the|and|of|to|in|that|is|was|for|with|as|on|at|by|an|or|but)\b`)

	// Add other language detectors as needed
	// s.languageDetectors["es"] = regexp.MustCompile(`\b(el|la|los|las|de|en|que|y|a|por|con|para|un|una|es|son)\b`)
	// s.languageDetectors["fr"] = regexp.MustCompile(`\b(le|la|les|des|en|que|et|à|par|avec|pour|un|une|est|sont)\b`)
}

// initializeLinguisticPatterns sets up patterns for linguistic analysis
func (s *OCRService) initializeLinguisticPatterns() {
	// Common sentence structures in English
	s.linguisticPatterns["en_sentence"] = regexp.MustCompile(`[A-Z][^.!?]*[.!?]`)

	// Common paragraph structures
	s.linguisticPatterns["paragraph"] = regexp.MustCompile(`(?m)^.+$\n*`)

	// Common OCR errors
	s.linguisticPatterns["ocr_errors"] = regexp.MustCompile(`[a-z][A-Z]|[A-Za-z][0-9]|[0-9][A-Za-z]`)

	// Initialize common phrases
	s.commonPhrases["en"] = []string{
		"in accordance with",
		"as a result of",
		"with respect to",
		"on the basis of",
		"in addition to",
		"as well as",
		"in order to",
		"due to the fact that",
		"for the purpose of",
		"in the event that",
	}
}

// loadLanguageDictionary loads a dictionary for a specific language
func (s *OCRService) loadLanguageDictionary(lang string) map[string]bool {
	if dict, exists := s.dictionaries[lang]; exists {
		return dict
	}

	// If dictionary doesn't exist, initialize it
	if lang == "en" {
		s.initializeDictionaries()
		return s.dictionaries["en"]
	}

	// Return English as fallback
	if dict, exists := s.dictionaries["en"]; exists {
		return dict
	}

	// Create empty dictionary as last resort
	return make(map[string]bool)
}

// hasLanguageDictionary checks if a dictionary exists for a language
func (s *OCRService) hasLanguageDictionary(lang string) bool {
	_, exists := s.dictionaries[lang]
	return exists
}

// detectLanguage attempts to identify the language of the text
func (s *OCRService) detectLanguage(text string) string {
	if text == "" {
		return "en" // Default to English for empty text
	}

	// Count matches for each language detector
	matches := make(map[string]int)

	for lang, detector := range s.languageDetectors {
		matches[lang] = len(detector.FindAllString(text, -1))
	}

	// Find language with most matches
	bestLang := "en"
	bestCount := 0

	for lang, count := range matches {
		if count > bestCount {
			bestCount = count
			bestLang = lang
		}
	}

	return bestLang
}

// validateWords checks words against a dictionary and returns validation results
func (s *OCRService) validateWords(words []string, dictionary map[string]bool) WordValidationResult {
	result := WordValidationResult{
		totalCount:   len(words),
		validCount:   0,
		invalidWords: []string{},
		suggestions:  make(map[string][]string),
	}

	for _, word := range words {
		// Clean the word
		cleanWord := strings.ToLower(strings.Trim(word, ".,!?;:()\"'"))

		// Skip short words
		if len(cleanWord) < 3 {
			continue
		}

		// Check if word is in dictionary
		if dictionary[cleanWord] {
			result.validCount++
		} else {
			// Check for common OCR errors and try to correct
			correctedWord := s.correctCommonOCRErrors(cleanWord)
			if dictionary[correctedWord] {
				result.validCount++
				result.suggestions[cleanWord] = []string{correctedWord}
			} else {
				result.invalidWords = append(result.invalidWords, cleanWord)

				// Generate suggestions for invalid words
				result.suggestions[cleanWord] = s.generateSuggestions(cleanWord, dictionary)
			}
		}
	}

	// Calculate valid ratio
	if result.totalCount > 0 {
		result.validRatio = float64(result.validCount) / float64(result.totalCount)
	} else {
		result.validRatio = 0
	}

	return result
}

// correctCommonOCRErrors attempts to fix common OCR errors
func (s *OCRService) correctCommonOCRErrors(word string) string {
	// Common OCR substitution errors
	substitutions := map[string]string{
		"0":  "o",
		"1":  "l",
		"5":  "s",
		"8":  "b",
		"rn": "m",
		"cl": "d",
		"vv": "w",
		"ii": "n",
	}

	corrected := word

	for wrong, right := range substitutions {
		corrected = strings.ReplaceAll(corrected, wrong, right)
	}

	return corrected
}

// generateSuggestions generates word suggestions for an invalid word
func (s *OCRService) generateSuggestions(word string, dictionary map[string]bool) []string {
	suggestions := []string{}

	// Only process words of reasonable length
	if len(word) < 3 || len(word) > 20 {
		return suggestions
	}

	// Try common character substitutions
	for i := 0; i < len(word); i++ {
		// Skip if already at max suggestions
		if len(suggestions) >= 3 {
			break
		}

		// Try substituting each character
		for r := 'a'; r <= 'z'; r++ {
			if len(suggestions) >= 3 {
				break
			}

			if word[i] != byte(r) {
				candidate := word[:i] + string(r) + word[i+1:]
				if dictionary[candidate] {
					suggestions = append(suggestions, candidate)
				}
			}
		}
	}

	// Try removing one character
	if len(suggestions) < 3 {
		for i := 0; i < len(word); i++ {
			if len(suggestions) >= 3 {
				break
			}

			candidate := word[:i] + word[i+1:]
			if dictionary[candidate] {
				suggestions = append(suggestions, candidate)
			}
		}
	}

	// Try adding one character
	if len(suggestions) < 3 {
		for i := 0; i <= len(word); i++ {
			if len(suggestions) >= 3 {
				break
			}

			for r := 'a'; r <= 'z'; r++ {
				if len(suggestions) >= 3 {
					break
				}

				candidate := word[:i] + string(r) + word[i:]
				if dictionary[candidate] {
					suggestions = append(suggestions, candidate)
				}
			}
		}
	}

	return suggestions
}

// adjustConfidenceWithLinguisticAnalysis applies linguistic analysis to refine confidence
func (s *OCRService) adjustConfidenceWithLinguisticAnalysis(confidence float64, text string, validation WordValidationResult) float64 {
	adjustedConfidence := confidence

	// Check for proper sentence structure
	sentencePattern := s.linguisticPatterns["en_sentence"]
	sentences := sentencePattern.FindAllString(text, -1)
	sentenceRatio := 0.0
	if len(text) > 0 {
		sentenceRatio = float64(len(strings.Join(sentences, ""))) / float64(len(text))
	}

	// Adjust based on sentence structure
	if sentenceRatio > 0.8 {
		adjustedConfidence += 0.1 // Good sentence structure
	} else if sentenceRatio < 0.4 {
		adjustedConfidence -= 0.1 // Poor sentence structure
	}

	// Check for common phrases
	phraseCount := 0
	for _, phrase := range s.commonPhrases["en"] {
		if strings.Contains(strings.ToLower(text), phrase) {
			phraseCount++
		}
	}

	// Adjust based on common phrases
	if phraseCount > 3 {
		adjustedConfidence += 0.05 // Many common phrases found
	}

	// Check for OCR errors pattern
	errorPattern := s.linguisticPatterns["ocr_errors"]
	errors := errorPattern.FindAllString(text, -1)
	errorRatio := float64(len(errors)) / float64(len(text))

	// Adjust based on error patterns
	if errorRatio > 0.05 {
		adjustedConfidence -= 0.1 // Many error patterns
	}

	// Ensure confidence stays in valid range
	if adjustedConfidence < 0.1 {
		adjustedConfidence = 0.1
	}
	if adjustedConfidence > 1.0 {
		adjustedConfidence = 1.0
	}

	return adjustedConfidence
}
