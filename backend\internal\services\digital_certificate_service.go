package services

import (
	"crypto/rand"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// DigitalCertificateService handles digital certificate operations
type DigitalCertificateService struct {
	db *gorm.DB
}

// NewDigitalCertificateService creates a new digital certificate service
func NewDigitalCertificateService(db *gorm.DB) *DigitalCertificateService {
	return &DigitalCertificateService{db: db}
}

// CreateCertificate creates a new digital certificate
func (s *DigitalCertificateService) CreateCertificate(req CreateCertificateRequest) (*models.DigitalCertificate, error) {
	// Generate serial number and thumbprint
	serialNumber, err := s.generateSerialNumber()
	if err != nil {
		return nil, fmt.Errorf("failed to generate serial number: %w", err)
	}

	thumbprint := s.generateThumbprint(req.CertificateData)

	// If this is set as default, unset other default certificates for the user
	if req.IsDefault {
		s.db.Model(&models.DigitalCertificate{}).
			Where("owner_id = ? AND is_default = ?", req.OwnerID, true).
			Update("is_default", false)
	}

	certificate := &models.DigitalCertificate{
		SerialNumber:       serialNumber,
		Thumbprint:         thumbprint,
		Status:             models.CertificateStatusActive,
		OwnerID:            req.OwnerID,
		Subject:            req.Subject,
		Issuer:             req.Issuer,
		CommonName:         req.CommonName,
		Organization:       req.Organization,
		OrganizationalUnit: req.OrganizationalUnit,
		Country:            req.Country,
		State:              req.State,
		Locality:           req.Locality,
		EmailAddress:       req.EmailAddress,
		NotBefore:          req.NotBefore,
		NotAfter:           req.NotAfter,
		IssuedAt:           time.Now(),
		PublicKey:          req.PublicKey,
		PrivateKey:         req.PrivateKey,
		CertificateData:    req.CertificateData,
		KeyUsage:           req.KeyUsage,
		ExtendedKeyUsage:   req.ExtendedKeyUsage,
		Purpose:            req.Purpose,
		Notes:              req.Notes,
		IsDefault:          req.IsDefault,
		IsActive:           true,
	}

	if err := s.db.Create(certificate).Error; err != nil {
		return nil, fmt.Errorf("failed to create certificate: %w", err)
	}

	return certificate, nil
}

// GetUserCertificates retrieves certificates for a specific user
func (s *DigitalCertificateService) GetUserCertificates(userID uint, status string) ([]models.DigitalCertificate, error) {
	query := s.db.Where("owner_id = ?", userID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	var certificates []models.DigitalCertificate
	if err := query.Order("is_default DESC, created_at DESC").Find(&certificates).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve certificates: %w", err)
	}

	return certificates, nil
}

// GetCertificate retrieves a specific certificate
func (s *DigitalCertificateService) GetCertificate(certificateID, userID uint) (*models.DigitalCertificate, error) {
	var certificate models.DigitalCertificate
	if err := s.db.Where("id = ? AND owner_id = ?", certificateID, userID).First(&certificate).Error; err != nil {
		return nil, fmt.Errorf("certificate not found: %w", err)
	}

	return &certificate, nil
}

// UpdateCertificate updates a certificate
func (s *DigitalCertificateService) UpdateCertificate(certificateID, userID uint, req UpdateCertificateRequest) (*models.DigitalCertificate, error) {
	var certificate models.DigitalCertificate
	if err := s.db.Where("id = ? AND owner_id = ?", certificateID, userID).First(&certificate).Error; err != nil {
		return nil, fmt.Errorf("certificate not found: %w", err)
	}

	// If this is set as default, unset other default certificates for the user
	if req.IsDefault && !certificate.IsDefault {
		s.db.Model(&models.DigitalCertificate{}).
			Where("owner_id = ? AND is_default = ? AND id != ?", userID, true, certificateID).
			Update("is_default", false)
	}

	// Update certificate fields
	certificate.Purpose = req.Purpose
	certificate.Notes = req.Notes
	certificate.IsDefault = req.IsDefault
	certificate.IsActive = req.IsActive

	if err := s.db.Save(&certificate).Error; err != nil {
		return nil, fmt.Errorf("failed to update certificate: %w", err)
	}

	return &certificate, nil
}

// RevokeCertificate revokes a certificate
func (s *DigitalCertificateService) RevokeCertificate(certificateID, userID uint, reason string) error {
	var certificate models.DigitalCertificate
	if err := s.db.Where("id = ? AND owner_id = ?", certificateID, userID).First(&certificate).Error; err != nil {
		return fmt.Errorf("certificate not found: %w", err)
	}

	if certificate.Status == models.CertificateStatusRevoked {
		return fmt.Errorf("certificate is already revoked")
	}

	now := time.Now()
	certificate.Status = models.CertificateStatusRevoked
	certificate.RevokedAt = &now
	certificate.RevokedByID = &userID
	certificate.RevocationReason = reason
	certificate.IsActive = false

	if err := s.db.Save(&certificate).Error; err != nil {
		return fmt.Errorf("failed to revoke certificate: %w", err)
	}

	// Invalidate any pending signatures using this certificate
	s.db.Model(&models.DigitalSignature{}).
		Where("certificate_id = ? AND status = ?", certificateID, models.SignatureStatusPending).
		Updates(map[string]interface{}{
			"status":            models.SignatureStatusInvalid,
			"validation_status": "certificate_revoked",
			"is_valid":          false,
		})

	return nil
}

// ValidateCertificate validates a certificate's current status
func (s *DigitalCertificateService) ValidateCertificate(certificateID uint) (*models.DigitalCertificate, bool, string) {
	var certificate models.DigitalCertificate
	if err := s.db.Where("id = ?", certificateID).First(&certificate).Error; err != nil {
		return nil, false, "certificate_not_found"
	}

	// Check if certificate is active
	if certificate.Status != models.CertificateStatusActive {
		return &certificate, false, string(certificate.Status)
	}

	// Check if certificate has expired
	if time.Now().After(certificate.NotAfter) {
		// Update status to expired
		certificate.Status = models.CertificateStatusExpired
		s.db.Save(&certificate)
		return &certificate, false, "expired"
	}

	// Check if certificate is not yet valid
	if time.Now().Before(certificate.NotBefore) {
		return &certificate, false, "not_yet_valid"
	}

	return &certificate, true, "valid"
}

// GetExpiringCertificates retrieves certificates expiring within the specified days
func (s *DigitalCertificateService) GetExpiringCertificates(days int) ([]models.DigitalCertificate, error) {
	expiryDate := time.Now().AddDate(0, 0, days)

	var certificates []models.DigitalCertificate
	if err := s.db.Preload("Owner").
		Where("status = ? AND not_after <= ? AND not_after > ?",
			models.CertificateStatusActive, expiryDate, time.Now()).
		Order("not_after ASC").
		Find(&certificates).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve expiring certificates: %w", err)
	}

	return certificates, nil
}

// GetCertificateUsageStats retrieves usage statistics for a certificate
func (s *DigitalCertificateService) GetCertificateUsageStats(certificateID uint) (*CertificateUsageStats, error) {
	var certificate models.DigitalCertificate
	if err := s.db.Where("id = ?", certificateID).First(&certificate).Error; err != nil {
		return nil, fmt.Errorf("certificate not found: %w", err)
	}

	var totalSignatures int64
	var validSignatures int64
	var invalidSignatures int64

	s.db.Model(&models.DigitalSignature{}).
		Where("certificate_id = ?", certificateID).
		Count(&totalSignatures)

	s.db.Model(&models.DigitalSignature{}).
		Where("certificate_id = ? AND is_valid = ?", certificateID, true).
		Count(&validSignatures)

	s.db.Model(&models.DigitalSignature{}).
		Where("certificate_id = ? AND is_valid = ?", certificateID, false).
		Count(&invalidSignatures)

	stats := &CertificateUsageStats{
		CertificateID:     certificateID,
		TotalSignatures:   int(totalSignatures),
		ValidSignatures:   int(validSignatures),
		InvalidSignatures: int(invalidSignatures),
		LastUsedAt:        certificate.LastUsedAt,
		DaysUntilExpiry:   int(time.Until(certificate.NotAfter).Hours() / 24),
	}

	return stats, nil
}

// Helper functions

func (s *DigitalCertificateService) generateSerialNumber() (string, error) {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return fmt.Sprintf("CERT-%s", hex.EncodeToString(bytes)), nil
}

func (s *DigitalCertificateService) generateThumbprint(certificateData string) string {
	hasher := sha1.New()
	hasher.Write([]byte(certificateData))
	return fmt.Sprintf("SHA1:%s", hex.EncodeToString(hasher.Sum(nil)))
}

// Request/Response types
type CreateCertificateRequest struct {
	OwnerID            uint      `json:"owner_id"`
	Subject            string    `json:"subject"`
	Issuer             string    `json:"issuer"`
	CommonName         string    `json:"common_name"`
	Organization       string    `json:"organization"`
	OrganizationalUnit string    `json:"organizational_unit"`
	Country            string    `json:"country"`
	State              string    `json:"state"`
	Locality           string    `json:"locality"`
	EmailAddress       string    `json:"email_address"`
	NotBefore          time.Time `json:"not_before"`
	NotAfter           time.Time `json:"not_after"`
	PublicKey          string    `json:"public_key"`
	PrivateKey         string    `json:"private_key"`
	CertificateData    string    `json:"certificate_data"`
	KeyUsage           string    `json:"key_usage"`
	ExtendedKeyUsage   string    `json:"extended_key_usage"`
	Purpose            string    `json:"purpose"`
	Notes              string    `json:"notes"`
	IsDefault          bool      `json:"is_default"`
}

type UpdateCertificateRequest struct {
	Purpose   string `json:"purpose"`
	Notes     string `json:"notes"`
	IsDefault bool   `json:"is_default"`
	IsActive  bool   `json:"is_active"`
}

type CertificateUsageStats struct {
	CertificateID     uint       `json:"certificate_id"`
	TotalSignatures   int        `json:"total_signatures"`
	ValidSignatures   int        `json:"valid_signatures"`
	InvalidSignatures int        `json:"invalid_signatures"`
	LastUsedAt        *time.Time `json:"last_used_at"`
	DaysUntilExpiry   int        `json:"days_until_expiry"`
}
