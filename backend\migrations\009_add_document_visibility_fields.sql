-- Migration 009: Add document visibility and access control fields
-- This migration adds missing fields to the documents table that are defined in the Document model

-- Add visibility and access control fields to documents table
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS visibility_level INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS regulation_count INTEGER DEFAULT 0;

-- Add comment to explain visibility levels
COMMENT ON COLUMN documents.visibility_level IS '1=public, 2=restricted, 3=confidential';
COMMENT ON COLUMN documents.is_public IS 'Whether the document is publicly accessible';
COMMENT ON COLUMN documents.regulation_count IS 'Number of regulations associated with this document';

-- Create document_files table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_files (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    file_name TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT,
    mime_type TEXT,
    checksum TEXT,
    
    -- File metadata
    description TEXT,
    is_public BOOLEAN DEFAULT true,
    is_primary BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    
    -- Upload information
    uploaded_by_id BIGINT NOT NULL REFERENCES users(id),
    
    -- Download tracking
    download_count INTEGER DEFAULT 0
);

-- Create document_comments table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_comments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    -- Commenter information
    commenter_name TEXT NOT NULL,
    commenter_email TEXT,
    organization TEXT,
    
    -- Comment content
    subject TEXT,
    content TEXT NOT NULL,
    
    -- Comment metadata
    is_public BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    ip_address TEXT,
    
    -- Moderation
    is_moderated BOOLEAN DEFAULT false,
    moderated_by BIGINT REFERENCES users(id),
    moderated_at TIMESTAMPTZ
);

-- Create document_reviews table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_reviews (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    reviewer_id BIGINT NOT NULL REFERENCES users(id),
    
    status TEXT, -- pending, approved, rejected, needs_changes
    comments TEXT,
    rating INTEGER, -- 1-5 scale
    
    reviewed_at TIMESTAMPTZ
);

-- Create document_versions table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_versions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    
    version_number INTEGER NOT NULL,
    title TEXT,
    content TEXT,
    change_log TEXT,
    is_current BOOLEAN DEFAULT false,
    
    created_by_id BIGINT NOT NULL REFERENCES users(id)
);

-- Create summaries table if it doesn't exist
CREATE TABLE IF NOT EXISTS summaries (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary_type TEXT NOT NULL, -- document_change, regulation_change, agency_change, category_change
    entity_type TEXT NOT NULL,  -- document, regulation, agency, category
    entity_id BIGINT NOT NULL,
    
    -- Publication control
    is_public BOOLEAN DEFAULT true,
    publication_date TIMESTAMPTZ,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    priority INTEGER DEFAULT 1, -- 1=low, 2=medium, 3=high
    
    -- Content details
    change_description TEXT,
    impact_assessment TEXT,
    related_entities TEXT -- JSON array of related entity references
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_is_public ON documents(is_public);
CREATE INDEX IF NOT EXISTS idx_documents_visibility_level ON documents(visibility_level);
CREATE INDEX IF NOT EXISTS idx_documents_publication_date ON documents(publication_date);
CREATE INDEX IF NOT EXISTS idx_document_files_document_id ON document_files(document_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_document_id ON document_comments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_reviews_document_id ON document_reviews(document_id);
CREATE INDEX IF NOT EXISTS idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX IF NOT EXISTS idx_summaries_entity_type_id ON summaries(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_summaries_is_public ON summaries(is_public);
CREATE INDEX IF NOT EXISTS idx_summaries_publication_date ON summaries(publication_date);
