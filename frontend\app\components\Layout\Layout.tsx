'use client'

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from './Header';
import Footer from './Footer';
import { useAuthStore } from '../../stores/authStore';
import { useUIStore } from '../../stores/uiStore';

interface LayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
  requireAuth?: boolean;
  allowedRoles?: string[];
}

const Layout: React.FC<LayoutProps> = ({
  children,
  title = 'Federal Register Clone',
  description = 'Document Management System',
  requireAuth = false,
  allowedRoles = [],
}) => {
  const router = useRouter();
  const { user, isLoading: authLoading, initializeAuth } = useAuthStore();
  const { globalLoading } = useUIStore();

  // Initialize user on app load
  useEffect(() => {
    initializeAuth();
  }, [initializeAuth]);

  // Handle role-based access control (middleware handles auth redirects)
  useEffect(() => {
    if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {
      router.push('/forbidden');
      return;
    }
  }, [allowedRoles, user, router]);

  // Show loading spinner while auth is initializing
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  // Show loading spinner for role-restricted pages while checking permissions
  if (allowedRoles.length > 0 && (!user || !allowedRoles.includes(user.role))) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        <Header />
        
        <main className="flex-1">
          {globalLoading && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 flex items-center space-x-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="text-gray-900 dark:text-white">Loading...</span>
              </div>
            </div>
          )}
          
          {children}
        </main>

        <Footer />
      </div>
  );
};

export default Layout;
