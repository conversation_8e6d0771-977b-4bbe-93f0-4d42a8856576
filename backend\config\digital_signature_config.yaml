# Advanced Digital Signature System Configuration

# Database Configuration
database:
  host: localhost
  port: 5432
  name: notecontrol_signatures
  user: signature_user
  password: ${DB_PASSWORD}
  ssl_mode: require
  max_connections: 100
  connection_timeout: 30s

# PKI Configuration
pki:
  # Default algorithms
  default_hash_algorithm: sha256
  default_encryption_algorithm: rsa_2048
  default_key_size: 2048
  
  # Certificate Authority
  root_ca:
    common_name: "NoteControl Root CA"
    organization: "NoteControl Inc"
    country: "US"
    validity_period: 3650  # 10 years
    key_algorithm: rsa_4096
    
  intermediate_ca:
    common_name: "NoteControl Intermediate CA"
    organization: "NoteControl Inc"
    country: "US"
    validity_period: 1825  # 5 years
    key_algorithm: rsa_2048

# Hardware Security Module (HSM) Configuration
hsm:
  enabled: true
  provider: "SafeNet"
  pkcs11_library: "/usr/lib/libCryptoki2_64.so"
  slot_id: 0
  token_label: "NoteControl-Token"
  pin: ${HSM_PIN}
  
  # HSM capabilities
  supported_algorithms:
    - rsa_2048
    - rsa_4096
    - ecdsa_p256
    - ecdsa_p384
  
  fips_140_level: 2
  common_criteria_eal: 4

# Timestamp Authority Configuration
timestamp_authorities:
  - name: "Primary TSA"
    url: "http://timestamp.digicert.com"
    protocol: "rfc3161"
    hash_algorithm: "sha256"
    requires_auth: false
    is_active: true
    
  - name: "Backup TSA"
    url: "http://timestamp.globalsign.com/scripts/timstamp.dll"
    protocol: "rfc3161"
    hash_algorithm: "sha256"
    requires_auth: false
    is_active: true

# OCSP Configuration
ocsp:
  enabled: true
  responders:
    - name: "Primary OCSP"
      url: "http://ocsp.digicert.com"
      ca_issuer: "DigiCert"
      is_active: true
      
  cache_duration: 3600  # 1 hour
  timeout: 10s
  retry_attempts: 3

# Certificate Revocation List (CRL)
crl:
  enabled: true
  update_interval: 24h
  cache_duration: 12h
  distribution_points:
    - "http://crl.digicert.com/DigiCertAssuredIDRootCA.crl"

# Biometric Authentication
biometric:
  enabled: true
  supported_types:
    - fingerprint
    - face_id
    - voice
    - retina
    - handwriting
    
  # Security settings
  min_quality_score: 0.8
  min_confidence_score: 0.85
  liveness_detection: true
  anti_spoofing: true
  
  # Template storage
  encryption_algorithm: "aes_256_gcm"
  template_expiry: 365  # days
  max_templates_per_user: 5

# Multi-Factor Authentication
mfa:
  enabled: true
  supported_methods:
    - sms
    - email
    - totp
    - hardware_token
    
  # SMS configuration
  sms:
    provider: "twilio"
    api_key: ${TWILIO_API_KEY}
    from_number: "+**********"
    
  # Email configuration
  email:
    smtp_host: "smtp.gmail.com"
    smtp_port: 587
    username: ${EMAIL_USERNAME}
    password: ${EMAIL_PASSWORD}
    
  # TOTP configuration
  totp:
    issuer: "NoteControl"
    algorithm: "sha1"
    digits: 6
    period: 30

# Blockchain Integration
blockchain:
  enabled: true
  networks:
    ethereum:
      enabled: true
      rpc_url: "https://mainnet.infura.io/v3/${INFURA_PROJECT_ID}"
      chain_id: 1
      contract_address: "0x****************************************"
      private_key: ${ETHEREUM_PRIVATE_KEY}
      gas_limit: 100000
      
    hyperledger:
      enabled: false
      network_config: "/path/to/network-config.yaml"
      channel_name: "signatures"
      chaincode_name: "signature-contract"

# Compliance Standards
compliance:
  eidas:
    enabled: true
    qualified_trust_service_provider: true
    qes_level: "high"
    
  pades:
    enabled: true
    baseline_profile: "B-B"
    long_term_validation: true
    
  fips_140:
    enabled: true
    level: 2
    
  common_criteria:
    enabled: true
    eal_level: 4

# Signature Policies
default_policies:
  low_security:
    signature_type: "simple"
    min_key_size: 1024
    require_certificate: false
    require_timestamp: false
    retention_period: 1
    
  medium_security:
    signature_type: "advanced"
    min_key_size: 2048
    require_certificate: true
    require_timestamp: true
    require_ocsp: true
    retention_period: 5
    
  high_security:
    signature_type: "qualified"
    min_key_size: 4096
    require_certificate: true
    require_timestamp: true
    require_ocsp: true
    require_biometric: true
    require_mfa: true
    require_witness: true
    retention_period: 10
    compliance_standards: ["eidas", "fips_140"]

# Archive Configuration
archive:
  enabled: true
  default_format: "asice"
  storage:
    provider: "s3"
    bucket: "notecontrol-signatures"
    region: "us-east-1"
    access_key: ${AWS_ACCESS_KEY}
    secret_key: ${AWS_SECRET_KEY}
    encryption: "aes_256"
    
  retention:
    default_period: 7  # years
    legal_hold_support: true
    
  integrity_checks:
    enabled: true
    interval: "weekly"
    algorithm: "sha256"

# Monitoring and Logging
monitoring:
  enabled: true
  metrics:
    - signature_count
    - validation_success_rate
    - certificate_expiry_alerts
    - hsm_performance
    - blockchain_transaction_status
    
  alerts:
    email: "<EMAIL>"
    slack_webhook: ${SLACK_WEBHOOK_URL}
    
  health_checks:
    interval: 60s
    endpoints:
      - hsm
      - timestamp_authorities
      - ocsp_responders
      - blockchain_networks

# API Configuration
api:
  rate_limiting:
    enabled: true
    requests_per_hour: 1000
    signature_operations_per_hour: 100
    
  authentication:
    jwt_secret: ${JWT_SECRET}
    token_expiry: 24h
    
  cors:
    enabled: true
    allowed_origins: ["https://app.notecontrol.com"]
    
  webhooks:
    enabled: true
    retry_attempts: 3
    timeout: 30s

# Security Configuration
security:
  encryption:
    at_rest: "aes_256_gcm"
    in_transit: "tls_1_3"
    
  key_management:
    rotation_interval: 90  # days
    backup_enabled: true
    
  access_control:
    rbac_enabled: true
    audit_logging: true
    
  network:
    firewall_enabled: true
    intrusion_detection: true
    ddos_protection: true

# Performance Configuration
performance:
  caching:
    enabled: true
    redis_url: "redis://localhost:6379"
    ttl: 3600  # seconds
    
  connection_pooling:
    max_connections: 100
    idle_timeout: 300s
    
  batch_processing:
    max_batch_size: 1000
    timeout: 300s

# Quantum-Safe Cryptography Configuration
quantum_safe:
  enabled: true
  default_algorithm: "dilithium"
  nist_level: 3
  hybrid_mode: true
  classical_fallback: "rsa_4096"

  algorithms:
    dilithium:
      levels: [2, 3, 5]
      library: "liboqs"
      implementation: "ref"

    kyber:
      levels: [2, 3, 4]
      library: "liboqs"
      implementation: "avx2"

    sphincs:
      variants: ["shake256", "sha256"]
      security_levels: [128, 192, 256]

# Quantum Key Distribution Configuration
quantum_key_distribution:
  enabled: true
  default_protocol: "BB84"

  channels:
    - name: "Primary QKD Channel"
      protocol: "BB84"
      transmitter_id: "QKD-TX-001"
      receiver_id: "QKD-RX-001"
      channel_type: "fiber"
      distance: 50.0
      wavelength: 1550.0
      key_rate: 1000
      qber_threshold: 0.11

  key_management:
    pool_size: 10000
    refresh_interval: 60
    lifetime: 3600
    privacy_amplification: true

# Homomorphic Encryption Configuration
homomorphic_encryption:
  enabled: true
  default_scheme: "CKKS"

  schemes:
    ckks:
      log_n: 14
      log_q: [60, 40, 40, 60]
      scale: 1048576.0
      precision: 40

    bfv:
      log_n: 13
      log_q: [54, 54, 54]
      log_t: 20

  performance:
    parallel_execution: true
    batch_size: 1000
    cache_enabled: true

# Zero-Knowledge Proof Configuration
zero_knowledge_proofs:
  enabled: true
  default_system: "groth16"

  systems:
    groth16:
      curve: "BN254"
      trusted_setup: true
      proof_size: 128

    plonk:
      curve: "BLS12-381"
      universal_setup: true
      proof_size: 320

    stark:
      field: "goldilocks"
      proof_size: 45000
      quantum_secure: true

  circuit_optimization:
    constraint_minimization: true
    gate_optimization: true
    parallel_proving: true

# Secure Multi-Party Computation Configuration
secure_multiparty_computation:
  enabled: true
  default_protocol: "SPDZ"

  protocols:
    spdz:
      security_parameter: 128
      statistical_security: 40
      preprocessing: true

    bgw:
      threshold: 2
      party_count: 3
      field_size: 256

  network:
    topology: "star"
    timeout: 30
    retry_attempts: 3
    encryption: "tls_1_3"

# Trusted Execution Environment Configuration
trusted_execution_environment:
  enabled: true
  default_type: "SGX"

  sgx:
    enclave_size: 134217728  # 128MB
    heap_size: 67108864     # 64MB
    stack_size: 1048576     # 1MB
    remote_attestation: true
    sealed_storage: true

  trustzone:
    secure_world_size: 33554432  # 32MB
    secure_boot: true
    hardware_crypto: true

  attestation:
    service_url: "https://attestation.intel.com"
    spid: ${SGX_SPID}
    subscription_key: ${SGX_SUBSCRIPTION_KEY}

# Advanced Threat Protection Configuration
advanced_threat_protection:
  enabled: true
  engine_type: "hybrid"

  detection:
    malware_detection: true
    phishing_detection: true
    anomaly_detection: true
    behavioral_analysis: true

  machine_learning:
    model_path: "/models/threat_detection.pkl"
    confidence_threshold: 0.85
    training_data_size: 1000000
    auto_retrain: true

  threat_intelligence:
    feeds:
      - name: "CrowdStrike"
        url: "https://api.crowdstrike.com/intel/v1"
        api_key: ${CROWDSTRIKE_API_KEY}
        update_interval: 3600

      - name: "VirusTotal"
        url: "https://www.virustotal.com/vtapi/v2"
        api_key: ${VIRUSTOTAL_API_KEY}
        update_interval: 1800

# Distributed Ledger Technology Configuration
distributed_ledger:
  enabled: true
  default_type: "DAG"

  networks:
    iota:
      network: "mainnet"
      node_url: "https://nodes.iota.org"
      depth: 3
      min_weight_magnitude: 14

    hedera:
      network: "mainnet"
      node_id: "0.0.3"
      account_id: ${HEDERA_ACCOUNT_ID}
      private_key: ${HEDERA_PRIVATE_KEY}

    avalanche:
      network: "mainnet"
      rpc_url: "https://api.avax.network/ext/bc/C/rpc"
      chain_id: 43114

# Cryptographic Oracle Configuration
cryptographic_oracles:
  enabled: true
  default_source: "quantum"

  sources:
    quantum:
      provider: "ANU Quantum Random"
      url: "https://qrng.anu.edu.au/API/jsonI.php"
      entropy_rate: 1000000  # bits per second
      quality_threshold: 0.99

    atmospheric:
      provider: "Random.org"
      url: "https://api.random.org/json-rpc/2/invoke"
      api_key: ${RANDOM_ORG_API_KEY}

  validation:
    fips_140_tests: true
    nist_tests: true
    bias_correction: true
    entropy_estimation: true

# Federated Identity Management Configuration
federated_identity:
  enabled: true
  default_protocol: "SAML"

  providers:
    azure_ad:
      protocol: "SAML"
      entity_id: "https://sts.windows.net/${AZURE_TENANT_ID}/"
      sso_url: "https://login.microsoftonline.com/${AZURE_TENANT_ID}/saml2"
      certificate: ${AZURE_AD_CERTIFICATE}

    okta:
      protocol: "SAML"
      entity_id: "http://www.okta.com/${OKTA_ORG_ID}"
      sso_url: "https://${OKTA_ORG}.okta.com/app/${OKTA_APP_ID}/sso/saml"
      certificate: ${OKTA_CERTIFICATE}

  attribute_mapping:
    email: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/emailaddress"
    name: "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"
    role: "http://schemas.microsoft.com/ws/2008/06/identity/claims/role"

# Development/Testing Configuration
development:
  debug_mode: false
  test_certificates: false
  mock_hsm: false
  mock_blockchain: false
  mock_quantum: false
  mock_tee: false
  log_level: "info"

  quantum_simulation:
    enabled: true
    max_qubits: 30
    noise_model: "depolarizing"
    error_rate: 0.001
