# PowerShell script to fix import paths after moving pages directory

# Get all TypeScript and JavaScript files in the pages directory
$files = Get-ChildItem -Path "pages" -Recurse -Include "*.tsx", "*.ts", "*.jsx", "*.js"

foreach ($file in $files) {
    Write-Host "Processing: $($file.FullName)"

    # Read the file content (compatible with older PowerShell)
    $content = Get-Content -Path $file.FullName | Out-String
    
    # Replace relative imports that need to be updated
    # Single level up (../) becomes (../src/)
    $content = $content -replace "from\s+['""]\.\.\/components\/", "from '../src/components/"
    $content = $content -replace "from\s+['""]\.\.\/services\/", "from '../src/services/"
    $content = $content -replace "from\s+['""]\.\.\/stores\/", "from '../src/stores/"
    $content = $content -replace "from\s+['""]\.\.\/utils\/", "from '../src/utils/"
    $content = $content -replace "from\s+['""]\.\.\/types\/", "from '../src/types/"
    $content = $content -replace "from\s+['""]\.\.\/hooks\/", "from '../src/hooks/"
    $content = $content -replace "from\s+['""]\.\.\/styles\/", "from '../src/styles/"
    
    # Two levels up (../../) becomes (../../src/)
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/components\/", "from '../../src/components/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/services\/", "from '../../src/services/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/stores\/", "from '../../src/stores/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/utils\/", "from '../../src/utils/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/types\/", "from '../../src/types/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/hooks\/", "from '../../src/hooks/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/styles\/", "from '../../src/styles/"
    
    # Three levels up (../../../) becomes (../../../src/)
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/components\/", "from '../../../src/components/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/services\/", "from '../../../src/services/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/stores\/", "from '../../../src/stores/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/utils\/", "from '../../../src/utils/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/types\/", "from '../../../src/types/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/hooks\/", "from '../../../src/hooks/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/styles\/", "from '../../../src/styles/"
    
    # Four levels up (../../../../) becomes (../../../../src/)
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/components\/", "from '../../../../src/components/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/services\/", "from '../../../../src/services/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/stores\/", "from '../../../../src/stores/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/utils\/", "from '../../../../src/utils/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/types\/", "from '../../../../src/types/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/hooks\/", "from '../../../../src/hooks/"
    $content = $content -replace "from\s+['""]\.\.\/\.\.\/\.\.\/\.\.\/styles\/", "from '../../../../src/styles/"
    
    # Also handle import statements (not just from statements)
    $content = $content -replace "import\s+['""]\.\.\/components\/", "import '../src/components/"
    $content = $content -replace "import\s+['""]\.\.\/services\/", "import '../src/services/"
    $content = $content -replace "import\s+['""]\.\.\/stores\/", "import '../src/stores/"
    $content = $content -replace "import\s+['""]\.\.\/utils\/", "import '../src/utils/"
    $content = $content -replace "import\s+['""]\.\.\/types\/", "import '../src/types/"
    $content = $content -replace "import\s+['""]\.\.\/hooks\/", "import '../src/hooks/"
    $content = $content -replace "import\s+['""]\.\.\/styles\/", "import '../src/styles/"
    
    # Write the updated content back to the file (compatible with older PowerShell)
    Set-Content -Path $file.FullName -Value $content
}

Write-Host "Import path fixing completed!"
