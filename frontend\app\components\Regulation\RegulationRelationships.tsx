import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  PlusIcon,
  // PencilIcon,
  TrashIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import {
  // RegulationDocumentRelationship,
  // RegulationAgencyRelationship,
  // RegulationCategoryRelationship,
  RegulationRelationshipsSummary,
  LawsAndRules
} from '../../types';
import regulationApi from '../../services/regulationApi';
import { useAuthStore } from '../../stores/authStore';
import AddRelationshipForm from './AddRelationshipForm';

interface RegulationRelationshipsProps {
  regulation: LawsAndRules;
  onUpdate?: () => void;
}

const RegulationRelationships: React.FC<RegulationRelationshipsProps> = ({
  regulation,
  onUpdate
}) => {
  const { isAuthenticated, user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [relationships, setRelationships] = useState<RegulationRelationshipsSummary | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['documents']));

  // Load relationships
  useEffect(() => {
    loadRelationships();
  }, [regulation.id]);

  const loadRelationships = async () => {
    try {
      setLoading(true);
      const data = await regulationApi.getRegulationRelationships(regulation.id);
      setRelationships(data);
    } catch (error) {
      console.error('Failed to load relationships:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const handleDeleteRelationship = async (type: 'documents' | 'agencies' | 'categories', relationshipId: number) => {
    if (!confirm('Are you sure you want to delete this relationship?')) return;

    try {
      switch (type) {
        case 'documents':
          await regulationApi.deleteRegulationDocumentRelationship(regulation.id, relationshipId);
          break;
        case 'agencies':
          await regulationApi.deleteRegulationAgencyRelationship(regulation.id, relationshipId);
          break;
        case 'categories':
          await regulationApi.deleteRegulationCategoryRelationship(regulation.id, relationshipId);
          break;
      }
      await loadRelationships();
      onUpdate?.();
    } catch (error) {
      console.error('Failed to delete relationship:', error);
      alert('Failed to delete relationship. Please try again.');
    }
  };

  const getRelationshipTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      // Document relationships
      implements: 'Implements',
      based_on: 'Based On',
      amends: 'Amends',
      repeals: 'Repeals',
      references: 'References',
      supersedes: 'Supersedes',
      // Agency relationships
      established_by: 'Established By',
      authorized_by: 'Authorized By',
      modified_by: 'Modified By',
      abolished_by: 'Abolished By',
      // Category relationships
      created_by: 'Created By',
      governed_by: 'Governed By'
    };
    return labels[type] || type;
  };

  const getRelationshipTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      implements: 'bg-green-100 text-green-800',
      based_on: 'bg-blue-100 text-blue-800',
      amends: 'bg-yellow-100 text-yellow-800',
      repeals: 'bg-red-100 text-red-800',
      references: 'bg-gray-100 text-gray-800',
      supersedes: 'bg-purple-100 text-purple-800',
      established_by: 'bg-green-100 text-green-800',
      authorized_by: 'bg-blue-100 text-blue-800',
      modified_by: 'bg-yellow-100 text-yellow-800',
      abolished_by: 'bg-red-100 text-red-800',
      created_by: 'bg-green-100 text-green-800',
      governed_by: 'bg-blue-100 text-blue-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!relationships) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="text-center text-gray-500 dark:text-gray-400">
          <InformationCircleIcon className="h-12 w-12 mx-auto mb-4" />
          <p>Failed to load relationships</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Regulation Relationships
          </h3>
          {isAuthenticated && user?.role && ['admin', 'editor'].includes(user.role) && (
            <button
              onClick={() => setShowAddForm(true)}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add Relationship
            </button>
          )}
        </div>
        
        {/* Summary Stats */}
        <div className="mt-4 grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {relationships.counts.documents}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Documents</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {relationships.counts.agencies}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Agencies</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {relationships.counts.categories}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Categories</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400">
              {relationships.counts.total}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Total</div>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Document Relationships */}
        <div>
          <button
            onClick={() => toggleSection('documents')}
            className="flex items-center w-full text-left"
          >
            {expandedSections.has('documents') ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-400" />
            )}
            <DocumentTextIcon className="h-5 w-5 text-blue-500 ml-2 mr-3" />
            <span className="text-lg font-medium text-gray-900 dark:text-white">
              Document Relationships ({relationships.counts.documents})
            </span>
          </button>

          {expandedSections.has('documents') && (
            <div className="mt-4 ml-10 space-y-3">
              {relationships.document_relationships.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 italic">No document relationships</p>
              ) : (
                relationships.document_relationships.map((rel) => (
                  <div key={rel.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRelationshipTypeColor(rel.relationship_type)}`}>
                          {getRelationshipTypeLabel(rel.relationship_type)}
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {rel.document?.title}
                        </span>
                      </div>
                      {rel.description && (
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{rel.description}</p>
                      )}
                      {rel.effective_date && (
                        <p className="mt-1 text-xs text-gray-400">
                          Effective: {new Date(rel.effective_date).toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    {isAuthenticated && user?.role && ['admin', 'editor'].includes(user.role) && (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleDeleteRelationship('documents', rel.id)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Agency Relationships */}
        <div>
          <button
            onClick={() => toggleSection('agencies')}
            className="flex items-center w-full text-left"
          >
            {expandedSections.has('agencies') ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-400" />
            )}
            <BuildingOfficeIcon className="h-5 w-5 text-green-500 ml-2 mr-3" />
            <span className="text-lg font-medium text-gray-900 dark:text-white">
              Agency Relationships ({relationships.counts.agencies})
            </span>
          </button>

          {expandedSections.has('agencies') && (
            <div className="mt-4 ml-10 space-y-3">
              {relationships.agency_relationships.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 italic">No agency relationships</p>
              ) : (
                relationships.agency_relationships.map((rel) => (
                  <div key={rel.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRelationshipTypeColor(rel.relationship_type)}`}>
                          {getRelationshipTypeLabel(rel.relationship_type)}
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {rel.agency?.name}
                        </span>
                      </div>
                      {rel.description && (
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{rel.description}</p>
                      )}
                      {rel.authority_scope && (
                        <p className="mt-1 text-xs text-gray-400">Authority: {rel.authority_scope}</p>
                      )}
                    </div>
                    {isAuthenticated && user?.role && ['admin', 'editor'].includes(user.role) && (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleDeleteRelationship('agencies', rel.id)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          )}
        </div>

        {/* Category Relationships */}
        <div>
          <button
            onClick={() => toggleSection('categories')}
            className="flex items-center w-full text-left"
          >
            {expandedSections.has('categories') ? (
              <ChevronDownIcon className="h-5 w-5 text-gray-400" />
            ) : (
              <ChevronRightIcon className="h-5 w-5 text-gray-400" />
            )}
            <TagIcon className="h-5 w-5 text-purple-500 ml-2 mr-3" />
            <span className="text-lg font-medium text-gray-900 dark:text-white">
              Category Relationships ({relationships.counts.categories})
            </span>
          </button>

          {expandedSections.has('categories') && (
            <div className="mt-4 ml-10 space-y-3">
              {relationships.category_relationships.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 italic">No category relationships</p>
              ) : (
                relationships.category_relationships.map((rel) => (
                  <div key={rel.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRelationshipTypeColor(rel.relationship_type)}`}>
                          {getRelationshipTypeLabel(rel.relationship_type)}
                        </span>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">
                          {rel.category?.name}
                        </span>
                      </div>
                      {rel.description && (
                        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">{rel.description}</p>
                      )}
                      {rel.scope_definition && (
                        <p className="mt-1 text-xs text-gray-400">Scope: {rel.scope_definition}</p>
                      )}
                    </div>
                    {isAuthenticated && user?.role && ['admin', 'editor'].includes(user.role) && (
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleDeleteRelationship('categories', rel.id)}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>

      {/* Add Relationship Form */}
      {showAddForm && (
        <AddRelationshipForm
          regulationId={regulation.id}
          onClose={() => setShowAddForm(false)}
          onSuccess={() => {
            loadRelationships();
            onUpdate?.();
          }}
        />
      )}
    </div>
  );
};

export default RegulationRelationships;
