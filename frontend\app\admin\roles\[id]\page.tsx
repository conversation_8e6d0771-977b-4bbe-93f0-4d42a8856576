'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  PencilIcon,
  TrashIcon,
  CheckIcon,
  XMarkIcon,
  KeyIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface Role {
  id: number;
  name: string;
  display_name: string;
  description: string;
  is_system_role: boolean;
  created_at: string;
  updated_at: string;
  permissions?: Permission[];
  user_count?: number;
}

interface Permission {
  id: number;
  name: string;
  display_name: string;
  description: string;
  resource: string;
  action: string;
}

const RoleViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const roleId = params.id as string;
  const { user } = useAuthStore();
  const [role, setRole] = useState<Role | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (roleId) {
      fetchRole();
    }
  }, [roleId]);

  const fetchRole = async () => {
    try {
      const response = await apiService.getRole(parseInt(roleId));
      setRole(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch role');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);
      await apiService.deleteRole(parseInt(roleId));
      router.push('/admin/roles');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete role');
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const canEdit = user && user.role === 'admin';
  const canDelete = canEdit && role && !role.is_system_role;

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading role...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !role) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Role Not Found</h1>
            <p className="text-gray-600 mt-2">{error || 'The requested role could not be found.'}</p>
            <Link
              href="/admin/roles"
              className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Roles
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/admin/roles"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Roles
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{role.display_name}</h1>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  role.is_system_role 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {role.is_system_role ? 'System Role' : 'Custom Role'}
                </span>
                {role.user_count !== undefined && (
                  <span className="flex items-center text-sm text-gray-500">
                    <UserGroupIcon className="h-4 w-4 mr-1" />
                    {role.user_count} users
                  </span>
                )}
              </div>
            </div>
            
            {canEdit && (
              <div className="flex space-x-2">
                <Link
                  href={`/admin/roles/${role.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </Link>
                {canDelete && (
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                  >
                    <TrashIcon className="h-4 w-4 mr-2" />
                    Delete
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Role Details */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Role Details</h2>
          </div>
          
          <div className="px-6 py-4 space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Role Name</h3>
                <p className="text-gray-900">{role.name}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Display Name</h3>
                <p className="text-gray-900">{role.display_name}</p>
              </div>
            </div>

            {/* Description */}
            {role.description && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Description</h3>
                <p className="text-gray-900">{role.description}</p>
              </div>
            )}

            {/* Permissions */}
            {role.permissions && role.permissions.length > 0 && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-4 flex items-center">
                  <KeyIcon className="h-4 w-4 mr-2" />
                  Permissions ({role.permissions.length})
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {role.permissions.map((permission) => (
                    <div
                      key={permission.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="text-sm font-medium text-gray-900">
                            {permission.display_name}
                          </h4>
                          <p className="text-xs text-gray-500 mt-1">
                            {permission.resource}:{permission.action}
                          </p>
                          {permission.description && (
                            <p className="text-xs text-gray-600 mt-2">
                              {permission.description}
                            </p>
                          )}
                        </div>
                        <CheckIcon className="h-4 w-4 text-green-600 flex-shrink-0 ml-2" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* System Role Warning */}
            {role.is_system_role && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex">
                  <ShieldCheckIcon className="h-5 w-5 text-blue-400" />
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">System Role</h3>
                    <p className="text-sm text-blue-700 mt-1">
                      This is a system role that cannot be deleted. Permissions may be limited for modification.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Created</h3>
                <p className="text-gray-900">{formatDate(role.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h3>
                <p className="text-gray-900">{formatDate(role.updated_at)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <TrashIcon className="mx-auto h-12 w-12 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900 mt-2">Delete Role</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Are you sure you want to delete this role? This action cannot be undone and will affect all users assigned to this role.
                </p>
                <div className="flex justify-center space-x-3 mt-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                    disabled={deleteLoading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default RoleViewPage;
