package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// ComplianceRequest represents the request structure for compliance records
type ComplianceRequest struct {
	Title           string `json:"title" binding:"required"`
	Description     string `json:"description"`
	ComplianceType  string `json:"compliance_type" binding:"required"`
	Status          string `json:"status"`
	Priority        string `json:"priority"`
	DueDate         string `json:"due_date"`
	ResponsibleUser uint   `json:"responsible_user_id"`
	DocumentID      uint   `json:"document_id"`
	RegulationID    uint   `json:"regulation_id"`
}

// GetComplianceRecords returns all compliance records with pagination
func GetComplianceRecords(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.<PERSON>ult<PERSON>uery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total compliance records
	var total int64
	db.Model(&models.ComplianceAssessment{}).Count(&total)

	// Get compliance records with pagination
	var records []models.ComplianceAssessment
	offset := (page - 1) * perPage
	if err := db.Preload("ResponsibleUser").
		Preload("Document").
		Order("created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&records).Error; err != nil {
		HandleInternalError(c, "Failed to fetch compliance records: "+err.Error())
		return
	}

	// Convert to response format
	recordResponses := make([]gin.H, len(records))
	for i, record := range records {
		recordResponses[i] = gin.H{
			"id":               record.ID,
			"name":             record.Name,
			"description":      record.Description,
			"framework":        record.Framework,
			"status":           record.Status,
			"type":             record.Type,
			"start_date":       record.StartDate,
			"end_date":         record.EndDate,
			"planned_end_date": record.PlannedEndDate,
			"lead_assessor":    record.LeadAssessor,
			"overall_status":   record.OverallStatus,
			"compliance_score": record.ComplianceScore,
			"created_at":       record.CreatedAt,
			"updated_at":       record.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       recordResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetComplianceRecord returns a single compliance record by ID
func GetComplianceRecord(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get compliance record
	var record models.ComplianceAssessment
	if err := db.Preload("ResponsibleUser").
		Preload("Document").
		First(&record, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance record")
			return
		}
		HandleInternalError(c, "Failed to fetch compliance record: "+err.Error())
		return
	}

	response := gin.H{
		"id":               record.ID,
		"name":             record.Name,
		"description":      record.Description,
		"framework":        record.Framework,
		"status":           record.Status,
		"type":             record.Type,
		"start_date":       record.StartDate,
		"end_date":         record.EndDate,
		"planned_end_date": record.PlannedEndDate,
		"lead_assessor":    record.LeadAssessor,
		"overall_status":   record.OverallStatus,
		"compliance_score": record.ComplianceScore,
		"created_at":       record.CreatedAt,
		"updated_at":       record.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance record retrieved successfully",
		Data:    response,
	})
}

// CreateComplianceRecord creates a new compliance record
func CreateComplianceRecord(c *gin.Context) {
	var req ComplianceRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create compliance record
	record := &models.ComplianceAssessment{
		Name:           req.Title,
		Description:    req.Description,
		Framework:      models.ComplianceFramework(req.ComplianceType),
		Status:         req.Status,
		Type:           req.Priority,
		StartDate:      time.Now(),
		PlannedEndDate: time.Now().AddDate(0, 1, 0), // Default to 1 month from now
		LeadAssessorID: 1,                           // Default assessor
	}

	if err := db.Create(record).Error; err != nil {
		HandleInternalError(c, "Failed to create compliance record: "+err.Error())
		return
	}

	// Load related data
	db.Preload("ResponsibleUser").Preload("Document").First(record, record.ID)

	response := gin.H{
		"id":               record.ID,
		"name":             record.Name,
		"description":      record.Description,
		"framework":        record.Framework,
		"status":           record.Status,
		"type":             record.Type,
		"start_date":       record.StartDate,
		"end_date":         record.EndDate,
		"planned_end_date": record.PlannedEndDate,
		"lead_assessor":    record.LeadAssessor,
		"overall_status":   record.OverallStatus,
		"compliance_score": record.ComplianceScore,
		"created_at":       record.CreatedAt,
		"updated_at":       record.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Compliance record created successfully",
		Data:    response,
	})
}

// UpdateComplianceRecord updates an existing compliance record
func UpdateComplianceRecord(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req ComplianceRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing compliance record
	var record models.ComplianceAssessment
	if err := db.First(&record, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance record")
			return
		}
		HandleInternalError(c, "Failed to fetch compliance record: "+err.Error())
		return
	}

	// Update compliance record fields
	record.Name = req.Title
	record.Description = req.Description
	record.Framework = models.ComplianceFramework(req.ComplianceType)
	record.Status = req.Status
	record.Type = req.Priority

	if err := db.Save(&record).Error; err != nil {
		HandleInternalError(c, "Failed to update compliance record: "+err.Error())
		return
	}

	// Load related data
	db.Preload("ResponsibleUser").Preload("Document").First(&record, record.ID)

	response := gin.H{
		"id":               record.ID,
		"name":             record.Name,
		"description":      record.Description,
		"framework":        record.Framework,
		"status":           record.Status,
		"type":             record.Type,
		"start_date":       record.StartDate,
		"end_date":         record.EndDate,
		"planned_end_date": record.PlannedEndDate,
		"lead_assessor":    record.LeadAssessor,
		"overall_status":   record.OverallStatus,
		"compliance_score": record.ComplianceScore,
		"created_at":       record.CreatedAt,
		"updated_at":       record.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance record updated successfully",
		Data:    response,
	})
}

// DeleteComplianceRecord deletes a compliance record
func DeleteComplianceRecord(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if compliance record exists
	var record models.ComplianceAssessment
	if err := db.First(&record, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance record")
			return
		}
		HandleInternalError(c, "Failed to fetch compliance record: "+err.Error())
		return
	}

	// Delete compliance record
	if err := db.Delete(&record).Error; err != nil {
		HandleInternalError(c, "Failed to delete compliance record: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance record deleted successfully",
	})
}

// GetComplianceAudits returns all compliance audits with pagination
func GetComplianceAudits(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total audits
	var total int64
	db.Model(&models.ComplianceAssessment{}).Count(&total)

	// Get audits with pagination
	var audits []models.ComplianceAssessment
	offset := (page - 1) * perPage
	if err := db.Preload("LeadAssessor").
		Preload("Rule").
		Offset(offset).
		Limit(perPage).
		Order("created_at DESC").
		Find(&audits).Error; err != nil {
		HandleInternalError(c, "Failed to fetch compliance audits: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"audits": audits,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}
