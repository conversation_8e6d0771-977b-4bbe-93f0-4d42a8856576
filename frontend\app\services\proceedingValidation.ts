interface ProceedingStep {
  id: number;
  step_order: number;
  step_type: string;
  status: string;
  requires_previous_completion: boolean;
  is_mandatory: boolean;
  allows_parallel_execution: boolean;
  requires_review: boolean;
  review_completed: boolean;
  completion_criteria: string;
  completion_evidence?: string;
}

interface Proceeding {
  id: number;
  status: string;
  requires_mandatory_review: boolean;
  minimum_steps_required: number;
  sequential_execution: boolean;
  proceeding_steps: ProceedingStep[];
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

interface StepTransitionResult {
  canTransition: boolean;
  reason?: string;
  requiredActions?: string[];
}

export class ProceedingValidationService {
  /**
   * Validates if a proceeding meets all administrative requirements
   */
  static validateProceeding(proceeding: Proceeding): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check minimum steps requirement
    if (proceeding.proceeding_steps.length < proceeding.minimum_steps_required) {
      errors.push(`Administrative proceeding must have at least ${proceeding.minimum_steps_required} steps. Currently has ${proceeding.proceeding_steps.length}.`);
    }

    // Check sequential ordering
    const sortedSteps = [...proceeding.proceeding_steps].sort((a, b) => a.step_order - b.step_order);
    for (let i = 0; i < sortedSteps.length; i++) {
      if (sortedSteps[i].step_order !== i + 1) {
        errors.push(`Step ordering is incorrect. Expected step order ${i + 1}, found ${sortedSteps[i].step_order}.`);
      }
    }

    // Check mandatory step types for administrative proceedings
    const requiredStepTypes = [
      'notice_of_intent',
      'notice_proposed_rule', 
      'public_comment',
      'final_rule',
      'regulation_publication'
    ];

    const presentStepTypes = proceeding.proceeding_steps.map(step => step.step_type);
    const missingStepTypes = requiredStepTypes.filter(type => !presentStepTypes.includes(type));
    
    if (missingStepTypes.length > 0) {
      warnings.push(`Missing recommended step types: ${missingStepTypes.join(', ')}`);
    }

    // Check sequential execution compliance
    if (proceeding.sequential_execution) {
      const violations = this.validateSequentialExecution(proceeding.proceeding_steps);
      errors.push(...violations);
    }

    // Check review requirements
    const reviewViolations = this.validateReviewRequirements(proceeding.proceeding_steps);
    errors.push(...reviewViolations);

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validates sequential execution requirements
   */
  static validateSequentialExecution(steps: ProceedingStep[]): string[] {
    const errors: string[] = [];
    const sortedSteps = [...steps].sort((a, b) => a.step_order - b.step_order);

    for (let i = 1; i < sortedSteps.length; i++) {
      const currentStep = sortedSteps[i];
      const previousStep = sortedSteps[i - 1];

      if (currentStep.requires_previous_completion) {
        // Check if previous mandatory steps are completed
        const incompletePreviousSteps = sortedSteps
          .slice(0, i)
          .filter(step => step.is_mandatory && step.status !== 'completed');

        if (incompletePreviousSteps.length > 0) {
          if (currentStep.status === 'in_progress' || currentStep.status === 'completed') {
            errors.push(`Step "${currentStep.step_type}" cannot be started until previous mandatory steps are completed: ${incompletePreviousSteps.map(s => s.step_type).join(', ')}`);
          }
        }
      }
    }

    return errors;
  }

  /**
   * Validates review requirements for administrative proceedings
   */
  static validateReviewRequirements(steps: ProceedingStep[]): string[] {
    const errors: string[] = [];

    steps.forEach(step => {
      if (step.requires_review && step.status === 'completed' && !step.review_completed) {
        errors.push(`Step "${step.step_type}" requires mandatory review before completion.`);
      }
    });

    return errors;
  }

  /**
   * Checks if a step can transition to a new status
   */
  static canTransitionStepStatus(
    step: ProceedingStep, 
    newStatus: string, 
    allSteps: ProceedingStep[]
  ): StepTransitionResult {
    const sortedSteps = [...allSteps].sort((a, b) => a.step_order - b.step_order);
    const stepIndex = sortedSteps.findIndex(s => s.id === step.id);
    
    if (stepIndex === -1) {
      return {
        canTransition: false,
        reason: 'Step not found in proceeding'
      };
    }

    // Check transitions from current status
    switch (step.status) {
      case 'not_started':
        return this.validateFromNotStarted(step, newStatus, sortedSteps, stepIndex);
      
      case 'in_progress':
        return this.validateFromInProgress(step, newStatus, sortedSteps, stepIndex);
      
      case 'under_review':
        return this.validateFromUnderReview(step, newStatus, sortedSteps, stepIndex);
      
      case 'completed':
        return this.validateFromCompleted(step, newStatus, sortedSteps, stepIndex);
      
      case 'failed':
        return this.validateFromFailed(step, newStatus, sortedSteps, stepIndex);
      
      default:
        return {
          canTransition: false,
          reason: `Unknown current status: ${step.status}`
        };
    }
  }

  private static validateFromNotStarted(
    step: ProceedingStep, 
    newStatus: string, 
    allSteps: ProceedingStep[], 
    stepIndex: number
  ): StepTransitionResult {
    if (newStatus === 'in_progress') {
      // Check if previous steps are completed (for sequential execution)
      if (step.requires_previous_completion) {
        const previousSteps = allSteps.slice(0, stepIndex);
        const incompleteMandatorySteps = previousSteps.filter(
          s => s.is_mandatory && s.status !== 'completed'
        );

        if (incompleteMandatorySteps.length > 0) {
          return {
            canTransition: false,
            reason: 'Previous mandatory steps must be completed first',
            requiredActions: incompleteMandatorySteps.map(s => `Complete step: ${s.step_type}`)
          };
        }
      }

      return { canTransition: true };
    }

    if (newStatus === 'skipped') {
      if (step.is_mandatory) {
        return {
          canTransition: false,
          reason: 'Mandatory steps cannot be skipped'
        };
      }
      return { canTransition: true };
    }

    return {
      canTransition: false,
      reason: `Invalid transition from 'not_started' to '${newStatus}'`
    };
  }

  private static validateFromInProgress(
    step: ProceedingStep, 
    newStatus: string, 
    allSteps: ProceedingStep[], 
    stepIndex: number
  ): StepTransitionResult {
    const validTransitions = ['under_review', 'completed', 'on_hold', 'failed'];
    
    if (!validTransitions.includes(newStatus)) {
      return {
        canTransition: false,
        reason: `Invalid transition from 'in_progress' to '${newStatus}'`
      };
    }

    if (newStatus === 'completed' && step.requires_review) {
      return {
        canTransition: false,
        reason: 'Step must go through review before completion',
        requiredActions: ['Submit step for review first']
      };
    }

    if (newStatus === 'completed' && !step.completion_evidence) {
      return {
        canTransition: false,
        reason: 'Completion evidence is required',
        requiredActions: ['Provide completion evidence']
      };
    }

    return { canTransition: true };
  }

  private static validateFromUnderReview(
    step: ProceedingStep, 
    newStatus: string, 
    allSteps: ProceedingStep[], 
    stepIndex: number
  ): StepTransitionResult {
    const validTransitions = ['completed', 'failed', 'in_progress'];
    
    if (!validTransitions.includes(newStatus)) {
      return {
        canTransition: false,
        reason: `Invalid transition from 'under_review' to '${newStatus}'`
      };
    }

    if ((newStatus === 'completed' || newStatus === 'failed') && !step.review_completed) {
      return {
        canTransition: false,
        reason: 'Review must be completed before final status',
        requiredActions: ['Complete mandatory review process']
      };
    }

    return { canTransition: true };
  }

  private static validateFromCompleted(
    step: ProceedingStep, 
    newStatus: string, 
    allSteps: ProceedingStep[], 
    stepIndex: number
  ): StepTransitionResult {
    // Generally, completed steps should not be changed
    // But allow reopening for corrections if no dependent steps are completed
    if (newStatus === 'in_progress') {
      const dependentSteps = allSteps.slice(stepIndex + 1);
      const completedDependentSteps = dependentSteps.filter(s => s.status === 'completed');
      
      if (completedDependentSteps.length > 0) {
        return {
          canTransition: false,
          reason: 'Cannot reopen step with completed dependent steps',
          requiredActions: completedDependentSteps.map(s => `Reopen step: ${s.step_type}`)
        };
      }

      return { canTransition: true };
    }

    return {
      canTransition: false,
      reason: `Invalid transition from 'completed' to '${newStatus}'`
    };
  }

  private static validateFromFailed(
    step: ProceedingStep, 
    newStatus: string, 
    allSteps: ProceedingStep[], 
    stepIndex: number
  ): StepTransitionResult {
    const validTransitions = ['in_progress', 'not_started'];
    
    if (!validTransitions.includes(newStatus)) {
      return {
        canTransition: false,
        reason: `Invalid transition from 'failed' to '${newStatus}'`
      };
    }

    return { canTransition: true };
  }

  /**
   * Gets the next available step that can be started
   */
  static getNextAvailableStep(steps: ProceedingStep[]): ProceedingStep | null {
    const sortedSteps = [...steps].sort((a, b) => a.step_order - b.step_order);
    
    for (const step of sortedSteps) {
      if (step.status === 'not_started') {
        const canStart = this.canTransitionStepStatus(step, 'in_progress', steps);
        if (canStart.canTransition) {
          return step;
        }
      }
    }

    return null;
  }

  /**
   * Calculates proceeding completion percentage
   */
  static calculateCompletionPercentage(steps: ProceedingStep[]): number {
    if (steps.length === 0) return 0;

    const mandatorySteps = steps.filter(step => step.is_mandatory);
    const completedMandatorySteps = mandatorySteps.filter(step => step.status === 'completed');
    
    return Math.round((completedMandatorySteps.length / mandatorySteps.length) * 100);
  }

  /**
   * Checks if proceeding can be marked as completed
   */
  static canCompleteProceeding(proceeding: Proceeding): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // All mandatory steps must be completed
    const mandatorySteps = proceeding.proceeding_steps.filter(step => step.is_mandatory);
    const incompleteMandatorySteps = mandatorySteps.filter(step => step.status !== 'completed');

    if (incompleteMandatorySteps.length > 0) {
      errors.push(`Cannot complete proceeding. Incomplete mandatory steps: ${incompleteMandatorySteps.map(s => s.step_type).join(', ')}`);
    }

    // All required reviews must be completed
    const unreviewed = proceeding.proceeding_steps.filter(
      step => step.requires_review && !step.review_completed && step.status === 'completed'
    );

    if (unreviewed.length > 0) {
      errors.push(`Cannot complete proceeding. Unreviewed steps: ${unreviewed.map(s => s.step_type).join(', ')}`);
    }

    // Check if final regulation publication step exists and is completed
    const regulationPublicationStep = proceeding.proceeding_steps.find(
      step => step.step_type === 'regulation_publication'
    );

    if (!regulationPublicationStep) {
      warnings.push('No regulation publication step found. Consider adding one for complete administrative proceeding.');
    } else if (regulationPublicationStep.status !== 'completed') {
      errors.push('Regulation publication step must be completed before proceeding completion.');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
