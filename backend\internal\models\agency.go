package models

import (
	"time"

	"gorm.io/gorm"
)

// Agency represents a government agency that publishes documents
type Agency struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Basic agency information
	Name        string `json:"name" gorm:"not null"`
	ShortName   string `json:"short_name" gorm:"uniqueIndex"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`

	// Agency details
	Website string `json:"website"`
	Email   string `json:"email"`
	Phone   string `json:"phone"`
	Address string `json:"address"`
	City    string `json:"city"`
	State   string `json:"state"`
	ZipCode string `json:"zip_code"`
	Country string `json:"country" gorm:"default:'US'"`

	// Agency hierarchy
	ParentAgencyID *uint `json:"parent_agency_id"`

	// Agency status and metadata
	IsActive      bool       `json:"is_active" gorm:"default:true"`
	AgencyType    string     `json:"agency_type"` // federal, state, local, independent
	Jurisdiction  string     `json:"jurisdiction"`
	EstablishedAt *time.Time `json:"established_at"`

	// Logo and branding
	LogoURL        string `json:"logo_url"`
	PrimaryColor   string `json:"primary_color"`
	SecondaryColor string `json:"secondary_color"`

	// Statistics (computed fields)
	DocumentCount   int `json:"document_count" gorm:"-"`
	ActiveDocuments int `json:"active_documents" gorm:"-"`
	RecentDocuments int `json:"recent_documents" gorm:"-"`

	// Relationships
	Documents []Document `json:"documents,omitempty" gorm:"foreignKey:AgencyID"`
	Users     []User     `json:"users,omitempty" gorm:"foreignKey:AgencyID"`

	// Regulation relationships
	RegulationCount         int                            `json:"regulation_count" gorm:"default:0"`
	RegulationRelationships []RegulationAgencyRelationship `json:"regulation_relationships,omitempty" gorm:"foreignKey:AgencyID"`
}

// AgencyContact represents contact information for an agency
type AgencyContact struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	AgencyID uint   `json:"agency_id" gorm:"not null"`
	Agency   Agency `json:"agency" gorm:"foreignKey:AgencyID"`

	// Contact information
	Name       string `json:"name" gorm:"not null"`
	Title      string `json:"title"`
	Department string `json:"department"`
	Email      string `json:"email"`
	Phone      string `json:"phone"`
	Fax        string `json:"fax"`
	Address    string `json:"address"`

	// Contact type and purpose
	ContactType string `json:"contact_type"` // primary, media, legal, technical
	Purpose     string `json:"purpose"`      // general, foia, comments, complaints
	IsPublic    bool   `json:"is_public" gorm:"default:true"`
	IsPrimary   bool   `json:"is_primary" gorm:"default:false"`
}

// AgencyCategory represents categories that agencies can be grouped into
type AgencyCategory struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	Name        string `json:"name" gorm:"not null"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	Color       string `json:"color"`
	Icon        string `json:"icon"`
	SortOrder   int    `json:"sort_order" gorm:"default:0"`

	// Relationships
	Agencies []Agency `json:"agencies,omitempty" gorm:"many2many:agency_category_assignments;"`
}

// AgencyCategoryAssignment represents the many-to-many relationship between agencies and categories
type AgencyCategoryAssignment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	AgencyID uint   `json:"agency_id" gorm:"not null"`
	Agency   Agency `json:"agency" gorm:"foreignKey:AgencyID"`

	CategoryID uint           `json:"category_id" gorm:"not null"`
	Category   AgencyCategory `json:"category" gorm:"foreignKey:CategoryID"`

	AssignedByID uint `json:"assigned_by_id"`
	AssignedBy   User `json:"assigned_by" gorm:"foreignKey:AssignedByID"`
}

// TableName returns the table name for Agency model
func (Agency) TableName() string {
	return "agencies"
}

// TableName returns the table name for AgencyContact model
func (AgencyContact) TableName() string {
	return "agency_contacts"
}

// TableName returns the table name for AgencyCategory model
func (AgencyCategory) TableName() string {
	return "agency_categories"
}

// TableName returns the table name for AgencyCategoryAssignment model
func (AgencyCategoryAssignment) TableName() string {
	return "agency_category_assignments"
}
