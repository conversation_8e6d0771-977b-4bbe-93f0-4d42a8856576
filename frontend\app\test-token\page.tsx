'use client';

import React, { useEffect, useState } from 'react';
import apiService from '../services/api';

export default function TestTokenPage() {
  const [results, setResults] = useState<any[]>([]);

  const testEndpoints = [
    '/auth/me',
    '/preloading/public-law-number',
    '/preloading/regulatory-identifier?agency_id=1',
    '/preloading/regulation-docket-number?agency_id=1',
    '/preloading/regulations?agency_id=1',
  ];

  const runTests = async () => {
    const testResults = [];
    
    for (const endpoint of testEndpoints) {
      try {
        console.log(`Testing ${endpoint}...`);
        
        // Get the token that will be sent
        const token = localStorage.getItem('federal_register_token');
        const tokenSegments = token ? token.split('.').length : 0;
        
        const response = await fetch(`http://127.0.0.1:8080/api/v1${endpoint}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });
        
        const result = {
          endpoint,
          status: response.status,
          success: response.ok,
          tokenLength: token?.length || 0,
          tokenSegments,
          tokenPreview: token ? `${token.substring(0, 30)}...` : 'No token',
          error: response.ok ? null : await response.text(),
        };
        
        testResults.push(result);
        console.log(`Result for ${endpoint}:`, result);
      } catch (error: any) {
        testResults.push({
          endpoint,
          status: 'ERROR',
          success: false,
          error: error.message,
          tokenLength: 0,
          tokenSegments: 0,
          tokenPreview: 'Error getting token',
        });
      }
    }
    
    setResults(testResults);
  };

  useEffect(() => {
    runTests();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Token Test Results</h1>
        
        <div className="mb-6">
          <button
            onClick={runTests}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Run Tests Again
          </button>
        </div>

        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Endpoint
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Token Length
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Token Segments
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Token Preview
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Error
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {results.map((result, index) => (
                <tr key={index} className={result.success ? 'bg-green-50' : 'bg-red-50'}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {result.endpoint}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`px-2 py-1 rounded text-xs ${
                      result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {result.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {result.tokenLength}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <span className={`px-2 py-1 rounded text-xs ${
                      result.tokenSegments === 3 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {result.tokenSegments} {result.tokenSegments === 3 ? '✓' : '✗'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500 font-mono text-xs">
                    {result.tokenPreview}
                  </td>
                  <td className="px-6 py-4 text-sm text-red-600">
                    {result.error}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-8 bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Current Token Info</h2>
          <div className="space-y-2 text-sm">
            <div><strong>localStorage Token:</strong></div>
            <div className="bg-gray-100 p-2 rounded text-xs break-all">
              {typeof window !== 'undefined' ? localStorage.getItem('federal_register_token') || 'No token' : 'Server side'}
            </div>
            <div><strong>localStorage Refresh Token:</strong></div>
            <div className="bg-gray-100 p-2 rounded text-xs break-all">
              {typeof window !== 'undefined' ? localStorage.getItem('federal_register_refresh_token') || 'No refresh token' : 'Server side'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
