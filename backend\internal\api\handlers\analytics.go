package handlers

import (
	"errors"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

var analyticsService *services.AnalyticsService

// InitializeAnalyticsService initializes the analytics service
func InitializeAnalyticsService() {
	db := database.GetDB()
	if db != nil {
		analyticsService = services.NewAnalyticsService(db)
	}
}

// GetAnalyticsMetrics returns real-time analytics metrics
func GetAnalyticsMetrics(c *gin.Context) {
	// Initialize service if not already done
	if analyticsService == nil {
		InitializeAnalyticsService()
	}

	if analyticsService == nil {
		HandleInternalError(c, "Analytics service not available")
		return
	}

	// Get system metrics
	metrics, err := analyticsService.GetSystemMetrics()
	if err != nil {
		HandleInternalError(c, "Failed to calculate metrics: "+err.Error())
		return
	}

	c.<PERSON>(http.StatusOK, SuccessResponse{
		Message: "Analytics metrics retrieved successfully",
		Data:    metrics,
	})
}

// GetAnalyticsDashboards returns available dashboards
func GetAnalyticsDashboards(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context for access control
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query - user can see public dashboards or their own dashboards
	query := db.Model(&models.Dashboard{}).
		Preload("Owner").
		Where("is_public = ? OR owner_id = ?", true, userID.(uint))

	// Apply search filters
	if search.Query != "" {
		query = query.Where("dashboard_name ILIKE ? OR description ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Type != "" {
		query = query.Where("category = ?", search.Type)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and get results
	var dashboards []models.Dashboard
	offset := (page.Page - 1) * page.PerPage
	if err := query.Offset(offset).Limit(page.PerPage).
		Order("created_at DESC").Find(&dashboards).Error; err != nil {
		HandleInternalError(c, "Failed to fetch dashboards: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Analytics dashboards retrieved successfully",
		Data: gin.H{
			"dashboards": dashboards,
			"pagination": gin.H{
				"page":     page.Page,
				"per_page": page.PerPage,
				"total":    total,
				"pages":    (total + int64(page.PerPage) - 1) / int64(page.PerPage),
			},
		},
	})
}

// GetAnalyticsDashboard returns a specific dashboard
func GetAnalyticsDashboard(c *gin.Context) {
	dashboardID := c.Param("id")

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse dashboard ID
	id, err := strconv.ParseUint(dashboardID, 10, 32)
	if err != nil {
		HandleBadRequest(c, "Invalid dashboard ID")
		return
	}

	// Query dashboard from database
	var dashboard models.Dashboard
	if err := db.Preload("Owner").First(&dashboard, uint(id)).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			HandleNotFound(c, "Dashboard")
			return
		}
		HandleInternalError(c, "Failed to fetch dashboard: "+err.Error())
		return
	}

	// Check if user has access to this dashboard
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Check access permissions
	if !dashboard.IsPublic && dashboard.OwnerID != userID.(uint) {
		// Check if user is in access list (if implemented)
		HandleUnauthorized(c, "Access denied to this dashboard")
		return
	}

	// Update view statistics
	dashboard.ViewCount++
	now := time.Now()
	dashboard.LastViewed = &now
	db.Save(&dashboard)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Dashboard retrieved successfully",
		Data:    dashboard,
	})
}

// CreateAnalyticsDashboard creates a new dashboard
func CreateAnalyticsDashboard(c *gin.Context) {
	var req struct {
		Name            string `json:"name" binding:"required"`
		Description     string `json:"description"`
		Category        string `json:"category"`
		Layout          string `json:"layout"`
		Widgets         string `json:"widgets"`
		Filters         string `json:"filters"`
		IsPublic        bool   `json:"is_public"`
		RefreshInterval int    `json:"refresh_interval"`
		CacheEnabled    bool   `json:"cache_enabled"`
		CacheDuration   int    `json:"cache_duration"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Set defaults
	if req.RefreshInterval == 0 {
		req.RefreshInterval = 300 // 5 minutes
	}
	if req.CacheDuration == 0 {
		req.CacheDuration = 3600 // 1 hour
	}

	// Create dashboard
	dashboard := models.Dashboard{
		DashboardName:   req.Name,
		Description:     req.Description,
		Category:        req.Category,
		Layout:          req.Layout,
		Widgets:         req.Widgets,
		Filters:         req.Filters,
		IsPublic:        req.IsPublic,
		RefreshInterval: req.RefreshInterval,
		CacheEnabled:    req.CacheEnabled,
		CacheDuration:   req.CacheDuration,
		OwnerID:         userID.(uint),
		IsActive:        true,
	}

	if err := db.Create(&dashboard).Error; err != nil {
		HandleInternalError(c, "Failed to create dashboard: "+err.Error())
		return
	}

	// Load the created dashboard with owner information
	if err := db.Preload("Owner").First(&dashboard, dashboard.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created dashboard: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Dashboard created successfully",
		Data:    dashboard,
	})
}

// RefreshAnalyticsDashboard refreshes dashboard data
func RefreshAnalyticsDashboard(c *gin.Context) {
	dashboardID := c.Param("id")

	// Initialize service if not already done
	if analyticsService == nil {
		InitializeAnalyticsService()
	}

	if analyticsService == nil {
		HandleInternalError(c, "Analytics service not available")
		return
	}

	// For now, we only have one dashboard
	if dashboardID != "1" {
		HandleNotFound(c, "Dashboard")
		return
	}

	// Get fresh dashboard data
	dashboard, err := analyticsService.GetSystemDashboard()
	if err != nil {
		HandleInternalError(c, "Failed to refresh dashboard: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Dashboard refreshed successfully",
		Data:    dashboard,
	})
}

// GetAnalyticsReports returns available reports
func GetAnalyticsReports(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query - user can see their own reports
	query := db.Model(&models.Report{}).
		Preload("CreatedBy").
		Where("created_by_id = ?", userID.(uint))

	// Apply search filters
	if search.Query != "" {
		query = query.Where("report_name ILIKE ? OR description ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Type != "" {
		query = query.Where("report_type = ?", search.Type)
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and get results
	var reports []models.Report
	offset := (page.Page - 1) * page.PerPage
	if err := query.Offset(offset).Limit(page.PerPage).
		Order("created_at DESC").Find(&reports).Error; err != nil {
		HandleInternalError(c, "Failed to fetch reports: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Analytics reports retrieved successfully",
		Data: gin.H{
			"reports": reports,
			"pagination": gin.H{
				"page":     page.Page,
				"per_page": page.PerPage,
				"total":    total,
				"pages":    (total + int64(page.PerPage) - 1) / int64(page.PerPage),
			},
		},
	})
}

// CreateAnalyticsReport creates a new report
func CreateAnalyticsReport(c *gin.Context) {
	var req struct {
		Name           string `json:"name" binding:"required"`
		Description    string `json:"description"`
		ReportType     string `json:"report_type"`
		AnalyticsType  string `json:"analytics_type"`
		Query          string `json:"query"`
		DataSources    string `json:"data_sources"`
		Parameters     string `json:"parameters"`
		OutputFormat   string `json:"output_format"`
		Template       string `json:"template"`
		Visualization  string `json:"visualization"`
		IsScheduled    bool   `json:"is_scheduled"`
		Schedule       string `json:"schedule"`
		Recipients     string `json:"recipients"`
		DeliveryMethod string `json:"delivery_method"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Set defaults
	if req.OutputFormat == "" {
		req.OutputFormat = "pdf"
	}
	if req.DeliveryMethod == "" {
		req.DeliveryMethod = "email"
	}
	if req.AnalyticsType == "" {
		req.AnalyticsType = "descriptive"
	}

	// Calculate next run time if scheduled
	var nextRun *time.Time
	if req.IsScheduled && req.Schedule != "" {
		// For simplicity, schedule for next hour if scheduled
		// In production, you'd parse the cron expression
		next := time.Now().Add(time.Hour)
		nextRun = &next
	}

	// Create report
	report := models.Report{
		ReportName:     req.Name,
		Description:    req.Description,
		ReportType:     req.ReportType,
		AnalyticsType:  models.AnalyticsType(req.AnalyticsType),
		Query:          req.Query,
		DataSources:    req.DataSources,
		Parameters:     req.Parameters,
		OutputFormat:   req.OutputFormat,
		Template:       req.Template,
		Visualization:  req.Visualization,
		IsScheduled:    req.IsScheduled,
		Schedule:       req.Schedule,
		NextRun:        nextRun,
		Recipients:     req.Recipients,
		DeliveryMethod: req.DeliveryMethod,
		CreatedByID:    userID.(uint),
		Status:         "active",
	}

	if err := db.Create(&report).Error; err != nil {
		HandleInternalError(c, "Failed to create report: "+err.Error())
		return
	}

	// Load the created report with creator information
	if err := db.Preload("CreatedBy").First(&report, report.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created report: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Report created successfully",
		Data:    report,
	})
}

// ExecuteAnalyticsReport executes a report
func ExecuteAnalyticsReport(c *gin.Context) {
	reportID := c.Param("id")

	// Initialize service if not already done
	if analyticsService == nil {
		InitializeAnalyticsService()
	}

	if analyticsService == nil {
		HandleInternalError(c, "Analytics service not available")
		return
	}

	// Parse report ID
	id, err := strconv.Atoi(reportID)
	if err != nil {
		HandleBadRequest(c, "Invalid report ID")
		return
	}

	// For demonstration, generate report data based on ID
	var reportData map[string]interface{}

	switch id {
	case 1: // User Activity Report
		metrics, _ := analyticsService.GetSystemMetrics()
		reportData = map[string]interface{}{
			"report_id":    id,
			"generated_at": "2024-01-01T00:00:00Z",
			"period":       "monthly",
			"metrics":      metrics,
			"summary":      "User activity has increased by 15% this month",
		}
	case 2: // Document Management Analytics
		reportData = map[string]interface{}{
			"report_id":    id,
			"generated_at": "2024-01-01T00:00:00Z",
			"period":       "weekly",
			"document_stats": map[string]interface{}{
				"total_documents": 1250,
				"new_documents":   45,
				"modified_docs":   123,
				"accessed_docs":   890,
			},
			"summary": "Document creation rate is stable with increased access patterns",
		}
	default:
		HandleNotFound(c, "Report")
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Report executed successfully",
		Data:    reportData,
	})
}

// GetAnalyticsDashboardByType returns analytics dashboard data based on type
func GetAnalyticsDashboardByType(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get dashboard type from query parameter (default to system)
	dashboardType := c.DefaultQuery("type", "system")

	// Initialize analytics service
	analyticsService := services.NewAnalyticsService(db)

	// Get dashboard data based on type
	dashboard, err := analyticsService.GetDashboardByType(dashboardType)
	if err != nil {
		HandleInternalError(c, "Failed to get dashboard data: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Dashboard data retrieved successfully",
		Data: gin.H{
			"dashboard": dashboard,
			"type":      dashboardType,
		},
	})
}

// GetAnalyticsDashboardTypes returns all available dashboard types
func GetAnalyticsDashboardTypes(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize analytics service
	analyticsService := services.NewAnalyticsService(db)

	// Get all dashboard types
	dashboardTypes := analyticsService.GetAllDashboardTypes()

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Dashboard types retrieved successfully",
		Data: gin.H{
			"types": dashboardTypes,
		},
	})
}

// GetExecutiveDashboard returns executive-level dashboard
func GetExecutiveDashboard(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize analytics service
	analyticsService := services.NewAnalyticsService(db)

	// Get executive dashboard
	dashboard, err := analyticsService.GetExecutiveDashboard()
	if err != nil {
		HandleInternalError(c, "Failed to get executive dashboard: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Executive dashboard retrieved successfully",
		Data:    dashboard,
	})
}

// GetOperationalDashboard returns operational dashboard
func GetOperationalDashboard(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize analytics service
	analyticsService := services.NewAnalyticsService(db)

	// Get operational dashboard
	dashboard, err := analyticsService.GetOperationalDashboard()
	if err != nil {
		HandleInternalError(c, "Failed to get operational dashboard: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Operational dashboard retrieved successfully",
		Data:    dashboard,
	})
}

// GetFinancialDashboard returns financial dashboard
func GetFinancialDashboard(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize analytics service
	analyticsService := services.NewAnalyticsService(db)

	// Get financial dashboard
	dashboard, err := analyticsService.GetFinancialDashboard()
	if err != nil {
		HandleInternalError(c, "Failed to get financial dashboard: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Financial dashboard retrieved successfully",
		Data:    dashboard,
	})
}

// GetComplianceDashboard returns compliance dashboard
func GetComplianceDashboard(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize analytics service
	analyticsService := services.NewAnalyticsService(db)

	// Get compliance dashboard
	dashboard, err := analyticsService.GetComplianceDashboard()
	if err != nil {
		HandleInternalError(c, "Failed to get compliance dashboard: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance dashboard retrieved successfully",
		Data:    dashboard,
	})
}
