# API Configuration
NEXT_PUBLIC_API_URL=http://127.0.0.1:8080
NEXT_PUBLIC_API_TIMEOUT=10000

# Application Configuration
NEXT_PUBLIC_APP_NAME=Federal Register Clone
NEXT_PUBLIC_APP_DESCRIPTION=Document Management System
NEXT_PUBLIC_APP_VERSION=1.0.0

# Environment
NODE_ENV=development
NEXT_PUBLIC_ENVIRONMENT=development

# Authentication
NEXT_PUBLIC_JWT_STORAGE_KEY=federal_register_token
NEXT_PUBLIC_REFRESH_TOKEN_STORAGE_KEY=federal_register_refresh_token

# Features
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ENABLE_ERROR_REPORTING=false
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=false

# Search Configuration
NEXT_PUBLIC_SEARCH_DEBOUNCE_MS=300
NEXT_PUBLIC_SEARCH_MIN_QUERY_LENGTH=2
NEXT_PUBLIC_SEARCH_MAX_RESULTS=100

# Pagination
NEXT_PUBLIC_DEFAULT_PAGE_SIZE=25
NEXT_PUBLIC_MAX_PAGE_SIZE=100

# File Upload
NEXT_PUBLIC_MAX_FILE_SIZE=52428800
NEXT_PUBLIC_ALLOWED_FILE_TYPES=pdf,doc,docx,txt

# UI Configuration
NEXT_PUBLIC_DEFAULT_THEME=light
NEXT_PUBLIC_ENABLE_DARK_MODE=true
NEXT_PUBLIC_DEFAULT_LANGUAGE=en

# Development
NEXT_PUBLIC_ENABLE_DEBUG=true
NEXT_PUBLIC_MOCK_API=false
__NEXT_TEST_MODE=true
