package models

import (
	"time"

	"gorm.io/gorm"
)

// LegacyUserRole represents the legacy role of a user in the system
type LegacyUserRole string

const (
	RoleAdmin     LegacyUserRole = "admin"
	RoleEditor    LegacyUserRole = "editor"
	RoleReviewer  LegacyUserRole = "reviewer"
	RolePublisher LegacyUserRole = "publisher"
	RoleViewer    LegacyUserRole = "viewer"
	RoleViewer1   LegacyUserRole = "viewer_level_1"
	RoleViewer2   LegacyUserRole = "viewer_level_2"
	RoleViewer3   LegacyUserRole = "viewer_level_3"
)

// User represents a user in the system
type User struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Basic user information
	Username     string         `json:"username" gorm:"uniqueIndex;not null"`
	Email        string         `json:"email" gorm:"uniqueIndex;not null"`
	FirstName    string         `json:"first_name"`
	LastName     string         `json:"last_name"`
	PasswordHash string         `json:"-" gorm:"not null"`
	Role         LegacyUserRole `json:"role" gorm:"default:'viewer'"`

	// User status
	IsActive    bool       `json:"is_active" gorm:"default:true"`
	IsVerified  bool       `json:"is_verified" gorm:"default:false"`
	LastLoginAt *time.Time `json:"last_login_at"`

	// Profile information
	Title        string `json:"title"`
	Department   string `json:"department"`
	Organization string `json:"organization"`
	Phone        string `json:"phone"`
	Bio          string `json:"bio"`

	// Relationships
	AgencyID *uint   `json:"agency_id"`
	Agency   *Agency `json:"agency,omitempty" gorm:"foreignKey:AgencyID"`

	// Documents created by this user
	Documents []Document `json:"documents,omitempty" gorm:"foreignKey:CreatedByID"`

	// Document reviews by this user
	DocumentReviews []DocumentReview `json:"document_reviews,omitempty" gorm:"foreignKey:ReviewerID"`
}

// UserSession represents an active user session
type UserSession struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	UserID    uint      `json:"user_id" gorm:"not null"`
	User      User      `json:"user" gorm:"foreignKey:UserID"`
	Token     string    `json:"-" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time `json:"expires_at"`
	IsActive  bool      `json:"is_active" gorm:"default:true"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
}

// UserPreferences represents user preferences and settings
type UserPreferences struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	UserID uint `json:"user_id" gorm:"uniqueIndex;not null"`
	User   User `json:"user" gorm:"foreignKey:UserID"`

	// Notification preferences
	EmailNotifications   bool `json:"email_notifications" gorm:"default:true"`
	DocumentAlerts       bool `json:"document_alerts" gorm:"default:true"`
	WeeklyDigest         bool `json:"weekly_digest" gorm:"default:false"`
	CommentNotifications bool `json:"comment_notifications" gorm:"default:true"`

	// Display preferences
	DocumentsPerPage int    `json:"documents_per_page" gorm:"default:25"`
	DefaultView      string `json:"default_view" gorm:"default:'list'"`
	Theme            string `json:"theme" gorm:"default:'light'"`
	Language         string `json:"language" gorm:"default:'en'"`
	Timezone         string `json:"timezone" gorm:"default:'UTC'"`

	// Search preferences
	DefaultSearchSort   string `json:"default_search_sort" gorm:"default:'relevance'"`
	SaveSearchHistory   bool   `json:"save_search_history" gorm:"default:true"`
	AutoCompleteEnabled bool   `json:"autocomplete_enabled" gorm:"default:true"`
}

// TableName returns the table name for User model
func (User) TableName() string {
	return "users"
}

// TableName returns the table name for UserSession model
func (UserSession) TableName() string {
	return "user_sessions"
}

// TableName returns the table name for UserPreferences model
func (UserPreferences) TableName() string {
	return "user_preferences"
}
