'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  ShieldCheckIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import { complianceApi } from '../../../services/enterpriseApi';
import { RiskAssessment } from '../../../types/enterprise';

const RiskAssessmentsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [risks, setRisks] = useState<RiskAssessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchRisks();
  }, []);

  const fetchRisks = async () => {
    try {
      setLoading(true);
      const response = await complianceApi.getAssessments();
      // Transform ComplianceAssessment to RiskAssessment
      const transformedRisks = (response.data || []).map((assessment: any) => ({
        id: assessment.id,
        risk_code: `RISK-${assessment.id}`,
        risk_title: assessment.assessment_name || 'Unknown Risk',
        risk_description: assessment.description,
        risk_category: assessment.category || 'General',
        inherent_risk: assessment.risk_level || 'medium',
        residual_risk: assessment.risk_level || 'medium',
        likelihood_score: assessment.likelihood_score || 5,
        impact_score: assessment.impact_score || 5,
        risk_owner_id: assessment.assessed_by_id,
        mitigation_strategy: assessment.mitigation_plan,
        review_date: assessment.next_review_date,
        risk_appetite: 'moderate',
        risk_score: (assessment.likelihood_score || 5) * (assessment.impact_score || 5),
        review_frequency: 'quarterly',
        status: 'active',
        created_at: assessment.created_at,
        updated_at: assessment.updated_at
      }));
      setRisks(transformedRisks);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch risk assessments');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this risk assessment?')) return;
    
    try {
      await complianceApi.deleteAssessment(id);
      await fetchRisks(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to delete risk assessment');
    }
  };

  const filteredRisks = risks.filter(risk =>
    risk.risk_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (risk.risk_description || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    (risk.risk_category || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRiskLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskIcon = (level: string) => {
    switch (level.toLowerCase()) {
      case 'critical':
      case 'high':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'medium':
        return <ChartBarIcon className="h-4 w-4" />;
      case 'low':
        return <ShieldCheckIcon className="h-4 w-4" />;
      default:
        return <ChartBarIcon className="h-4 w-4" />;
    }
  };

  if (!user) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be logged in to view risk assessments.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Risk Assessments</h1>
              <p className="text-gray-600 mt-1">Manage organizational risk assessments and mitigation strategies</p>
            </div>
            {(user.role === 'admin' || user.role === 'editor') && (
              <Link
                href="/enterprise/compliance/risks/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Risk Assessment
              </Link>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search risk assessments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading risk assessments...</p>
          </div>
        ) : (
          /* Risk Assessments Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Risk Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Category
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Risk Level
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Impact Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Probability
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredRisks.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                      No risk assessments found.
                      {(user.role === 'admin' || user.role === 'editor') && (
                        <Link
                          href="/enterprise/compliance/risks/new"
                          className="block mt-2 text-primary-600 hover:text-primary-500"
                        >
                          Create your first risk assessment
                        </Link>
                      )}
                    </td>
                  </tr>
                ) : (
                  filteredRisks.map((risk) => (
                    <tr key={risk.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{risk.risk_title}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{risk.risk_description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {risk.risk_category}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskLevelColor(risk.inherent_risk)}`}>
                          {getRiskIcon(risk.inherent_risk)}
                          <span className="ml-1">{risk.inherent_risk}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {risk.impact_score}/10
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {risk.likelihood_score}/10
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          risk.status === 'active' ? 'bg-green-100 text-green-800' : 
                          risk.status === 'mitigated' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {risk.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/enterprise/compliance/risks/${risk.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {(user.role === 'admin' || user.role === 'editor') && (
                            <>
                              <button
                                onClick={() => router.push(`/enterprise/compliance/risks/${risk.id}/edit`)}
                                className="text-yellow-600 hover:text-yellow-900"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(risk.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default RiskAssessmentsPage;
