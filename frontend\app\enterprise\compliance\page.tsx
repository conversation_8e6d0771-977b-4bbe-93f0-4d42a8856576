'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface ComplianceRequirement {
  id: number;
  requirement_code: string;
  title: string;
  framework: string;
  category: string;
  risk_level: string;
  status: string;
  testing_frequency: string;
}

interface RiskAssessment {
  id: number;
  risk_code: string;
  risk_title: string;
  risk_category: string;
  inherent_risk: string;
  residual_risk: string;
  risk_score: number;
  status: string;
}

interface ComplianceAssessment {
  id: number;
  assessment_code: string;
  title: string;
  framework: string;
  status: string;
  overall_score: number;
  compliance_level: string;
  planned_start_date: string;
}

const ComplianceManagementPage: React.FC = () => {
  const router = useRouter();
  const [requirements, setRequirements] = useState<ComplianceRequirement[]>([]);
  const [risks, setRisks] = useState<RiskAssessment[]>([]);
  const [assessments, setAssessments] = useState<ComplianceAssessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('dashboard');

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: '📊' },
    { id: 'requirements', name: 'Requirements', icon: '📋' },
    { id: 'assessments', name: 'Assessments', icon: '🔍' },
    { id: 'risks', name: 'Risk Management', icon: '⚠️' },
    { id: 'policies', name: 'Policies', icon: '📜' },
    { id: 'findings', name: 'Findings', icon: '🔎' }
  ];

  const frameworks = [
    { code: 'sox', name: 'Sarbanes-Oxley Act', color: 'bg-blue-100 text-blue-800' },
    { code: 'gdpr', name: 'GDPR', color: 'bg-green-100 text-green-800' },
    { code: 'hipaa', name: 'HIPAA', color: 'bg-purple-100 text-purple-800' },
    { code: 'pci_dss', name: 'PCI-DSS', color: 'bg-orange-100 text-orange-800' },
    { code: 'iso_27001', name: 'ISO 27001', color: 'bg-indigo-100 text-indigo-800' },
    { code: 'nist', name: 'NIST', color: 'bg-red-100 text-red-800' }
  ];

  useEffect(() => {
    fetchComplianceData();
  }, []);

  const fetchComplianceData = async () => {
    try {
      setLoading(true);
      
      // Mock data for requirements
      const mockRequirements: ComplianceRequirement[] = [
        {
          id: 1,
          requirement_code: 'SOX-404',
          title: 'Internal Control Assessment',
          framework: 'sox',
          category: 'financial',
          risk_level: 'high',
          status: 'active',
          testing_frequency: 'annual'
        },
        {
          id: 2,
          requirement_code: 'GDPR-32',
          title: 'Data Protection by Design',
          framework: 'gdpr',
          category: 'privacy',
          risk_level: 'medium',
          status: 'active',
          testing_frequency: 'quarterly'
        }
      ];

      // Mock data for risks
      const mockRisks: RiskAssessment[] = [
        {
          id: 1,
          risk_code: 'RISK-001',
          risk_title: 'Data Breach Risk',
          risk_category: 'operational',
          inherent_risk: 'high',
          residual_risk: 'medium',
          risk_score: 12,
          status: 'active'
        },
        {
          id: 2,
          risk_code: 'RISK-002',
          risk_title: 'Financial Reporting Risk',
          risk_category: 'financial',
          inherent_risk: 'medium',
          residual_risk: 'low',
          risk_score: 6,
          status: 'active'
        }
      ];

      // Mock data for assessments
      const mockAssessments: ComplianceAssessment[] = [
        {
          id: 1,
          assessment_code: 'ASSESS-SOX-2024-001',
          title: 'SOX Compliance Assessment Q4 2024',
          framework: 'sox',
          status: 'in_progress',
          overall_score: 87.5,
          compliance_level: 'compliant',
          planned_start_date: '2024-10-01'
        }
      ];

      setRequirements(mockRequirements);
      setRisks(mockRisks);
      setAssessments(mockAssessments);
    } catch (err) {
      setError('Failed to load compliance data');
      console.error('Error fetching compliance data:', err);
    } finally {
      setLoading(false);
    }
  };

  const getRiskLevelColor = (level: string): string => {
    const colors: { [key: string]: string } = {
      'low': 'bg-green-100 text-green-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-orange-100 text-orange-800',
      'critical': 'bg-red-100 text-red-800'
    };
    return colors[level] || 'bg-gray-100 text-gray-800';
  };

  const getFrameworkColor = (framework: string): string => {
    const frameworkData = frameworks.find(f => f.code === framework);
    return frameworkData?.color || 'bg-gray-100 text-gray-800';
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">📋</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Requirements</p>
              <p className="text-2xl font-semibold text-gray-900">{requirements.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">✅</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Compliance Score</p>
              <p className="text-2xl font-semibold text-gray-900">87%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">⚠️</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Risks</p>
              <p className="text-2xl font-semibold text-gray-900">{risks.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                <span className="text-white text-sm">🔍</span>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Assessments</p>
              <p className="text-2xl font-semibold text-gray-900">{assessments.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Framework Overview */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Compliance Frameworks</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {frameworks.map((framework) => (
            <div key={framework.code} className="border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${framework.color}`}>
                  {framework.code.toUpperCase()}
                </span>
                <span className="text-sm text-gray-500">Active</span>
              </div>
              <h4 className="font-medium text-gray-900">{framework.name}</h4>
              <p className="text-sm text-gray-600 mt-1">
                {requirements.filter(r => r.framework === framework.code).length} requirements
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderRequirements = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Compliance Requirements</h2>
        <button
          onClick={() => router.push('/enterprise/compliance/requirements/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Add Requirement
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Code
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Framework
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Risk Level
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Testing Frequency
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {requirements.map((requirement) => (
              <tr key={requirement.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {requirement.requirement_code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {requirement.title}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getFrameworkColor(requirement.framework)}`}>
                    {requirement.framework.toUpperCase()}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(requirement.risk_level)}`}>
                    {requirement.risk_level.toUpperCase()}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {requirement.testing_frequency}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => router.push(`/enterprise/compliance/requirements/${requirement.id}`)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    View
                  </button>
                  <button
                    onClick={() => router.push(`/enterprise/compliance/requirements/${requirement.id}/edit`)}
                    className="text-indigo-600 hover:text-indigo-900"
                  >
                    Edit
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderRisks = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Risk Management</h2>
        <button
          onClick={() => router.push('/enterprise/compliance/risks/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Add Risk
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {risks.map((risk) => (
          <div key={risk.id} className="bg-white rounded-lg shadow border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{risk.risk_title}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(risk.residual_risk)}`}>
                {risk.residual_risk.toUpperCase()}
              </span>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Code:</span>
                <span className="text-gray-900 font-medium">{risk.risk_code}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Category:</span>
                <span className="text-gray-900">{risk.risk_category}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Inherent Risk:</span>
                <span className={`font-medium ${getRiskLevelColor(risk.inherent_risk).includes('text-') ? getRiskLevelColor(risk.inherent_risk).split(' ')[1] : 'text-gray-900'}`}>
                  {risk.inherent_risk.toUpperCase()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Risk Score:</span>
                <span className="text-gray-900 font-bold">{risk.risk_score}</span>
              </div>
            </div>

            <div className="mt-4 flex space-x-2">
              <button
                onClick={() => router.push(`/enterprise/compliance/risks/${risk.id}`)}
                className="flex-1 bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm hover:bg-blue-100 transition-colors"
              >
                View Details
              </button>
              <button
                onClick={() => router.push(`/enterprise/compliance/risks/${risk.id}/edit`)}
                className="flex-1 bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-100 transition-colors"
              >
                Edit
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAssessments = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Compliance Assessments</h2>
        <button
          onClick={() => router.push('/enterprise/compliance/assessments/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          New Assessment
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Assessment management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderPolicies = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Policy Management</h2>
        <button
          onClick={() => router.push('/enterprise/compliance/policies/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Policy
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Policy management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderFindings = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Compliance Findings</h2>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Findings management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return renderDashboard();
      case 'requirements':
        return renderRequirements();
      case 'assessments':
        return renderAssessments();
      case 'risks':
        return renderRisks();
      case 'policies':
        return renderPolicies();
      case 'findings':
        return renderFindings();
      default:
        return renderDashboard();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Compliance & Risk Management</h1>
          <p className="mt-2 text-gray-600">
            Manage compliance requirements, risk assessments, policies, and findings
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {renderTabContent()}

        {/* Error Display */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComplianceManagementPage;
