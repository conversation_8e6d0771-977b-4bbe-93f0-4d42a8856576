package services

import (
	"errors"
	"fmt"
	"math"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// TaskPerformanceService handles automatic performance evaluation for tasks
type TaskPerformanceService struct {
	db *gorm.DB
}

// NewTaskPerformanceService creates a new task performance service
func NewTaskPerformanceService() *TaskPerformanceService {
	return &TaskPerformanceService{
		db: database.GetDB(),
	}
}

// PerformanceWeights defines the weights for different performance metrics
type PerformanceWeights struct {
	DeadlineAdherence    float64 // 30% weight
	CompletionEfficiency float64 // 25% weight
	QualityScore         float64 // 25% weight
	PriorityHandling     float64 // 20% weight
}

// DefaultPerformanceWeights returns the default weights for performance calculation
func DefaultPerformanceWeights() PerformanceWeights {
	return PerformanceWeights{
		DeadlineAdherence:    0.30,
		CompletionEfficiency: 0.25,
		QualityScore:         0.25,
		PriorityHandling:     0.20,
	}
}

// CalculateTaskPerformance automatically calculates performance metrics for a task
func (s *TaskPerformanceService) CalculateTaskPerformance(task *models.Task) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Calculate individual performance metrics
	deadlineScore := s.calculateDeadlineAdherenceScore(task)
	efficiencyScore := s.calculateCompletionEfficiencyScore(task)
	qualityScore := s.calculateQualityScore(task)
	priorityScore := s.calculatePriorityHandlingScore(task)

	// Calculate overall performance using weighted average
	weights := DefaultPerformanceWeights()
	overallPerformance := (deadlineScore * weights.DeadlineAdherence) +
		(efficiencyScore * weights.CompletionEfficiency) +
		(qualityScore * weights.QualityScore) +
		(priorityScore * weights.PriorityHandling)

	// Update task with performance metrics
	now := time.Now()
	updates := map[string]interface{}{
		"performance_percentage":   math.Round(overallPerformance*100) / 100,
		"deadline_adherence_score": math.Round(deadlineScore*100) / 100,
		"quality_score":            math.Round(qualityScore*100) / 100,
		"completion_efficiency":    math.Round(efficiencyScore*100) / 100,
		"priority_handling_score":  math.Round(priorityScore*100) / 100,
		"evaluation_date":          &now,
		"is_auto_calculated":       true,
		"performance_notes":        s.generatePerformanceNotes(task, deadlineScore, efficiencyScore, qualityScore, priorityScore),
	}

	err := s.db.Model(task).Where("id = ?", task.ID).Updates(updates).Error
	if err != nil {
		return err
	}

	// Reload task with updated data for sync
	if err := s.db.First(task, task.ID).Error; err != nil {
		return err
	}

	// Sync with finance performance if task is linked to document/regulation
	if err := s.SyncTaskPerformanceWithFinance(task); err != nil {
		// Log error but don't fail the performance calculation
		fmt.Printf("Warning: Failed to sync task performance with finance for task %d: %v\n", task.ID, err)
	}

	return nil
}

// calculateDeadlineAdherenceScore calculates score based on deadline adherence
func (s *TaskPerformanceService) calculateDeadlineAdherenceScore(task *models.Task) float64 {
	if task.DueDate == nil {
		// No deadline set, return neutral score
		return 75.0
	}

	if task.Status != models.TaskStatusCompleted {
		// Task not completed yet
		if time.Now().After(*task.DueDate) {
			// Overdue task
			return 0.0
		}
		// Task still pending/in progress, return neutral score
		return 75.0
	}

	if task.CompletedAt == nil {
		// Completed but no completion time recorded
		return 75.0
	}

	// Calculate days difference between completion and due date
	daysDiff := task.CompletedAt.Sub(*task.DueDate).Hours() / 24

	if daysDiff <= 0 {
		// Completed before or on deadline
		if daysDiff <= -1 {
			// Completed more than 1 day early - excellent
			return 100.0
		}
		// Completed on time
		return 90.0
	}

	// Completed late - decrease score by 10% per day late
	score := 90.0 - (daysDiff * 10.0)
	if score < 0 {
		return 0.0
	}
	return score
}

// calculateCompletionEfficiencyScore calculates efficiency based on time taken
func (s *TaskPerformanceService) calculateCompletionEfficiencyScore(task *models.Task) float64 {
	if task.Status != models.TaskStatusCompleted || task.CompletedAt == nil {
		return 75.0 // Neutral score for incomplete tasks
	}

	// Calculate actual time taken
	var actualHours float64
	if task.StartDate != nil {
		actualHours = task.CompletedAt.Sub(*task.StartDate).Hours()
	} else {
		actualHours = task.CompletedAt.Sub(task.CreatedAt).Hours()
	}

	// Estimate expected time based on task complexity and priority
	expectedHours := s.estimateExpectedCompletionTime(task)

	if expectedHours <= 0 {
		return 75.0 // Neutral score if can't estimate
	}

	// Calculate efficiency ratio
	efficiencyRatio := expectedHours / actualHours

	if efficiencyRatio >= 1.0 {
		// Completed faster than expected
		return math.Min(100.0, 80.0+(efficiencyRatio-1.0)*20.0)
	}

	// Took longer than expected
	score := 80.0 * efficiencyRatio
	return math.Max(0.0, score)
}

// estimateExpectedCompletionTime estimates expected completion time based on task attributes
func (s *TaskPerformanceService) estimateExpectedCompletionTime(task *models.Task) float64 {
	baseHours := 8.0 // Default 8 hours for a task

	// Adjust based on task type
	switch task.Type {
	case models.TaskTypeReview:
		baseHours = 4.0
	case models.TaskTypeDeadline:
		baseHours = 2.0
	case models.TaskTypeHearing:
		baseHours = 16.0
	case models.TaskTypeComment:
		baseHours = 1.0
	case models.TaskTypeGeneral:
		baseHours = 8.0
	case models.TaskTypeReminder:
		baseHours = 0.5
	case models.TaskTypeFollowUp:
		baseHours = 2.0
	}

	// Adjust based on priority
	switch task.Priority {
	case models.TaskPriorityUrgent:
		baseHours *= 0.8 // Urgent tasks expected to be done faster
	case models.TaskPriorityHigh:
		baseHours *= 0.9
	case models.TaskPriorityMedium:
		baseHours *= 1.0
	case models.TaskPriorityLow:
		baseHours *= 1.2
	}

	// Adjust based on duration if specified
	if task.Duration != nil && *task.Duration > 0 {
		specifiedHours := float64(*task.Duration) / 60.0 // Convert minutes to hours
		// Use average of estimated and specified duration
		baseHours = (baseHours + specifiedHours) / 2.0
	}

	return baseHours
}

// calculateQualityScore calculates quality based on various indicators
func (s *TaskPerformanceService) calculateQualityScore(task *models.Task) float64 {
	score := 80.0 // Base quality score

	// Check for task comments (indicates engagement/issues)
	var commentCount int64
	s.db.Model(&models.TaskComment{}).Where("task_id = ?", task.ID).Count(&commentCount)

	if commentCount == 0 {
		// No comments might indicate smooth completion
		score += 10.0
	} else if commentCount <= 2 {
		// Few comments indicate good communication
		score += 5.0
	} else {
		// Many comments might indicate issues
		score -= 5.0
	}

	// Check if task has detailed notes
	if len(task.Notes) > 100 {
		score += 5.0 // Detailed documentation is good
	}

	// Check if task was completed in one go (no status changes back and forth)
	// This would require tracking status history, for now use a simple heuristic
	if task.Status == models.TaskStatusCompleted {
		score += 5.0
	}

	return math.Min(100.0, math.Max(0.0, score))
}

// calculatePriorityHandlingScore calculates score based on priority handling
func (s *TaskPerformanceService) calculatePriorityHandlingScore(task *models.Task) float64 {
	baseScore := 75.0

	// Higher scores for completing high-priority tasks
	switch task.Priority {
	case models.TaskPriorityUrgent:
		if task.Status == models.TaskStatusCompleted {
			baseScore = 95.0
		} else if task.Status == models.TaskStatusOnHold {
			baseScore = 20.0
		}
	case models.TaskPriorityHigh:
		if task.Status == models.TaskStatusCompleted {
			baseScore = 85.0
		} else if task.Status == models.TaskStatusOnHold {
			baseScore = 30.0
		}
	case models.TaskPriorityMedium:
		if task.Status == models.TaskStatusCompleted {
			baseScore = 75.0
		} else if task.Status == models.TaskStatusOnHold {
			baseScore = 50.0
		}
	case models.TaskPriorityLow:
		if task.Status == models.TaskStatusCompleted {
			baseScore = 70.0
		} else if task.Status == models.TaskStatusOnHold {
			baseScore = 60.0
		}
	}

	return baseScore
}

// generatePerformanceNotes generates automatic performance evaluation notes
func (s *TaskPerformanceService) generatePerformanceNotes(task *models.Task, deadline, efficiency, quality, priority float64) string {
	notes := "Automatic Performance Evaluation:\n"

	if deadline >= 90 {
		notes += "• Excellent deadline adherence\n"
	} else if deadline >= 70 {
		notes += "• Good deadline adherence\n"
	} else if deadline >= 50 {
		notes += "• Moderate deadline adherence\n"
	} else {
		notes += "• Poor deadline adherence - improvement needed\n"
	}

	if efficiency >= 90 {
		notes += "• Highly efficient completion\n"
	} else if efficiency >= 70 {
		notes += "• Good completion efficiency\n"
	} else {
		notes += "• Completion efficiency could be improved\n"
	}

	if quality >= 90 {
		notes += "• High quality execution\n"
	} else if quality >= 70 {
		notes += "• Good quality execution\n"
	} else {
		notes += "• Quality could be enhanced\n"
	}

	if priority >= 90 {
		notes += "• Excellent priority handling\n"
	} else if priority >= 70 {
		notes += "• Good priority handling\n"
	} else {
		notes += "• Priority handling needs attention\n"
	}

	return notes
}

// GetTaskPerformanceHistory retrieves performance history for a task
func (s *TaskPerformanceService) GetTaskPerformanceHistory(taskID uint) ([]models.TaskPerformanceHistory, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var history []models.TaskPerformanceHistory
	err := s.db.Where("task_id = ?", taskID).
		Preload("EvaluatedBy").
		Order("evaluation_date DESC").
		Find(&history).Error

	return history, err
}

// SyncTaskPerformanceWithFinance syncs task performance with finance performance evaluation
func (s *TaskPerformanceService) SyncTaskPerformanceWithFinance(task *models.Task) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Only sync if task is linked to document or regulation
	if task.DocumentID == nil && task.RegulationID == nil {
		return nil // No sync needed
	}

	// Get current year for finance performance
	currentYear := time.Now().Year()

	// Check if finance performance record exists
	var existingFinancePerf models.FinancePerformance
	query := s.db.Where("year = ?", currentYear)

	if task.DocumentID != nil {
		query = query.Where("document_id = ?", *task.DocumentID)
	}
	if task.RegulationID != nil {
		query = query.Where("regulation_id = ?", *task.RegulationID)
	}

	err := query.First(&existingFinancePerf).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	// Prepare finance performance data
	financePerf := models.FinancePerformance{
		DocumentID:            task.DocumentID,
		RegulationID:          task.RegulationID,
		Year:                  currentYear,
		PerformancePercentage: task.PerformancePercentage,
		PerformanceNotes:      fmt.Sprintf("Task Performance Sync: %s", task.PerformanceNotes),
		EvaluationDate:        time.Now(),
		EvaluatedByID:         task.EvaluatedByID,
	}

	if errors.Is(err, gorm.ErrRecordNotFound) {
		// Create new finance performance record
		return s.db.Create(&financePerf).Error
	} else {
		// Update existing finance performance record
		return s.db.Model(&existingFinancePerf).Updates(map[string]interface{}{
			"performance_percentage": task.PerformancePercentage,
			"performance_notes":      financePerf.PerformanceNotes,
			"evaluation_date":        financePerf.EvaluationDate,
			"evaluated_by_id":        task.EvaluatedByID,
		}).Error
	}
}

// RecalculateAllTaskPerformances recalculates performance for all completed tasks
func (s *TaskPerformanceService) RecalculateAllTaskPerformances() error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	var tasks []models.Task
	err := s.db.Where("status = ?", models.TaskStatusCompleted).Find(&tasks).Error
	if err != nil {
		return err
	}

	for _, task := range tasks {
		if err := s.CalculateTaskPerformance(&task); err != nil {
			fmt.Printf("Error calculating performance for task %d: %v\n", task.ID, err)
		}
	}

	return nil
}

// TaskStatusHistoryEntry represents a single status change in task history
type TaskStatusHistoryEntry struct {
	ID                uint                 `json:"id" gorm:"primaryKey"`
	TaskID            uint                 `json:"task_id" gorm:"not null;index"`
	Task              *models.Task         `json:"task,omitempty" gorm:"foreignKey:TaskID"`
	PreviousStatus    models.TaskStatus    `json:"previous_status"`
	NewStatus         models.TaskStatus    `json:"new_status" gorm:"not null"`
	PreviousPriority  models.TaskPriority  `json:"previous_priority"`
	NewPriority       models.TaskPriority  `json:"new_priority"`
	ChangedByID       uint                 `json:"changed_by_id" gorm:"not null"`
	ChangedBy         *models.User         `json:"changed_by,omitempty" gorm:"foreignKey:ChangedByID"`
	ChangeReason      string               `json:"change_reason" gorm:"type:text"`
	ChangeType        TaskStatusChangeType `json:"change_type" gorm:"not null"`
	AutomatedChange   bool                 `json:"automated_change" gorm:"default:false"`
	PerformanceImpact string               `json:"performance_impact" gorm:"type:text"`
	Timestamp         time.Time            `json:"timestamp" gorm:"not null;index"`
	Metadata          string               `json:"metadata" gorm:"type:json"` // Additional context as JSON
	CreatedAt         time.Time            `json:"created_at"`
	UpdatedAt         time.Time            `json:"updated_at"`
}

// TaskStatusChangeType defines the type of status change
type TaskStatusChangeType string

const (
	TaskStatusChangeTypeCreated         TaskStatusChangeType = "created"
	TaskStatusChangeTypeUpdated         TaskStatusChangeType = "updated"
	TaskStatusChangeTypeAssigned        TaskStatusChangeType = "assigned"
	TaskStatusChangeTypeStarted         TaskStatusChangeType = "started"
	TaskStatusChangeTypeCompleted       TaskStatusChangeType = "completed"
	TaskStatusChangeTypeCancelled       TaskStatusChangeType = "cancelled"
	TaskStatusChangeTypeReopened        TaskStatusChangeType = "reopened"
	TaskStatusChangeTypePriorityChanged TaskStatusChangeType = "priority_changed"
	TaskStatusChangeTypeDeadlineChanged TaskStatusChangeType = "deadline_changed"
	TaskStatusChangeTypeAssigneeChanged TaskStatusChangeType = "assignee_changed"
	TaskStatusChangeTypeAutomated       TaskStatusChangeType = "automated"
)

// TaskStatusTracker provides comprehensive status tracking functionality
type TaskStatusTracker struct {
	db *gorm.DB
}

// NewTaskStatusTracker creates a new task status tracker
func NewTaskStatusTracker(db *gorm.DB) *TaskStatusTracker {
	return &TaskStatusTracker{db: db}
}

// TrackStatusChange records a status change in the task history
func (t *TaskStatusTracker) TrackStatusChange(taskID uint, previousStatus, newStatus models.TaskStatus,
	changedByID uint, changeReason string, changeType TaskStatusChangeType, automated bool) error {

	if t.db == nil {
		return errors.New("database not initialized")
	}

	// Get current task for additional context
	var task models.Task
	if err := t.db.First(&task, taskID).Error; err != nil {
		return fmt.Errorf("failed to get task: %v", err)
	}

	// Calculate performance impact
	performanceImpact := t.calculatePerformanceImpact(previousStatus, newStatus, &task)

	// Create metadata JSON
	metadataJSON := fmt.Sprintf(`{"task_title":"%s","task_type":"%s","priority":"%s"}`,
		task.Title, task.Type, task.Priority)

	// Create history entry
	historyEntry := TaskStatusHistoryEntry{
		TaskID:            taskID,
		PreviousStatus:    previousStatus,
		NewStatus:         newStatus,
		PreviousPriority:  task.Priority,
		NewPriority:       task.Priority,
		ChangedByID:       changedByID,
		ChangeReason:      changeReason,
		ChangeType:        changeType,
		AutomatedChange:   automated,
		PerformanceImpact: performanceImpact,
		Timestamp:         time.Now(),
		Metadata:          metadataJSON,
	}

	return t.db.Create(&historyEntry).Error
}

// TrackPriorityChange records a priority change in the task history
func (t *TaskStatusTracker) TrackPriorityChange(taskID uint, previousPriority, newPriority models.TaskPriority,
	changedByID uint, changeReason string) error {

	if t.db == nil {
		return errors.New("database not initialized")
	}

	var task models.Task
	if err := t.db.First(&task, taskID).Error; err != nil {
		return fmt.Errorf("failed to get task: %v", err)
	}

	performanceImpact := t.calculatePriorityChangeImpact(previousPriority, newPriority, &task)

	metadataJSON := fmt.Sprintf(`{"previous_priority":"%s","new_priority":"%s","task_title":"%s"}`,
		previousPriority, newPriority, task.Title)

	historyEntry := TaskStatusHistoryEntry{
		TaskID:            taskID,
		PreviousStatus:    task.Status,
		NewStatus:         task.Status,
		PreviousPriority:  previousPriority,
		NewPriority:       newPriority,
		ChangedByID:       changedByID,
		ChangeReason:      changeReason,
		ChangeType:        TaskStatusChangeTypePriorityChanged,
		AutomatedChange:   false,
		PerformanceImpact: performanceImpact,
		Timestamp:         time.Now(),
		Metadata:          metadataJSON,
	}

	return t.db.Create(&historyEntry).Error
}

// GetTaskStatusHistory retrieves the complete status history for a task
func (t *TaskStatusTracker) GetTaskStatusHistory(taskID uint, limit int, offset int) ([]TaskStatusHistoryEntry, error) {
	if t.db == nil {
		return nil, errors.New("database not initialized")
	}

	var history []TaskStatusHistoryEntry
	query := t.db.Where("task_id = ?", taskID).
		Preload("ChangedBy").
		Order("timestamp DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	err := query.Find(&history).Error
	return history, err
}

// GetTaskStatusSummary provides a summary of status changes for a task
func (t *TaskStatusTracker) GetTaskStatusSummary(taskID uint) (map[string]interface{}, error) {
	if t.db == nil {
		return nil, errors.New("database not initialized")
	}

	var history []TaskStatusHistoryEntry
	err := t.db.Where("task_id = ?", taskID).
		Order("timestamp ASC").
		Find(&history).Error

	if err != nil {
		return nil, err
	}

	if len(history) == 0 {
		return map[string]interface{}{
			"total_changes":   0,
			"status_timeline": []interface{}{},
		}, nil
	}

	// TODO: Implement full status summary logic
	return map[string]interface{}{
		"total_changes":   len(history),
		"status_timeline": history,
	}, nil
}

// calculatePerformanceImpact calculates the performance impact of a status change
func (t *TaskStatusTracker) calculatePerformanceImpact(previousStatus, newStatus models.TaskStatus, task *models.Task) string {
	// Define performance impact based on status transitions
	switch {
	case previousStatus == models.TaskStatusPending && newStatus == models.TaskStatusInProgress:
		return "Positive: Task started on time"
	case previousStatus == models.TaskStatusInProgress && newStatus == models.TaskStatusCompleted:
		if task.DueDate != nil && time.Now().Before(*task.DueDate) {
			return "Positive: Task completed before deadline"
		} else if task.DueDate != nil && time.Now().After(*task.DueDate) {
			return "Negative: Task completed after deadline"
		}
		return "Neutral: Task completed"
	case newStatus == models.TaskStatusOnHold:
		return "Neutral: Task put on hold"
	case newStatus == models.TaskStatusCancelled:
		return "Negative: Task was cancelled"
	case previousStatus == models.TaskStatusCompleted && newStatus != models.TaskStatusCompleted:
		return "Negative: Completed task was reopened"
	case previousStatus == models.TaskStatusCancelled && newStatus == models.TaskStatusPending:
		return "Positive: Cancelled task was reactivated"
	default:
		return "Neutral: Status change"
	}
}

// calculatePriorityChangeImpact calculates the performance impact of a priority change
func (t *TaskStatusTracker) calculatePriorityChangeImpact(previousPriority, newPriority models.TaskPriority, task *models.Task) string {
	priorityValues := map[models.TaskPriority]int{
		models.TaskPriorityLow:    1,
		models.TaskPriorityMedium: 2,
		models.TaskPriorityHigh:   3,
		models.TaskPriorityUrgent: 4,
	}

	prevValue := priorityValues[previousPriority]
	newValue := priorityValues[newPriority]

	switch {
	case newValue > prevValue:
		return fmt.Sprintf("Priority increased from %s to %s - may indicate urgency or importance escalation",
			previousPriority, newPriority)
	case newValue < prevValue:
		return fmt.Sprintf("Priority decreased from %s to %s - may indicate reduced urgency or resource reallocation",
			previousPriority, newPriority)
	default:
		return "No priority change impact"
	}
}

// GetTaskStatusAnalytics provides analytics on task status changes across multiple tasks
func (t *TaskStatusTracker) GetTaskStatusAnalytics(userID *uint, dateFrom, dateTo *time.Time) (map[string]interface{}, error) {
	if t.db == nil {
		return nil, errors.New("database not initialized")
	}

	query := t.db.Model(&TaskStatusHistoryEntry{})

	// Apply filters
	if userID != nil {
		query = query.Where("changed_by_id = ?", *userID)
	}
	if dateFrom != nil {
		query = query.Where("timestamp >= ?", *dateFrom)
	}
	if dateTo != nil {
		query = query.Where("timestamp <= ?", *dateTo)
	}

	// Get total count
	var totalChanges int64
	query.Count(&totalChanges)

	// Get change type distribution
	var changeTypeStats []struct {
		ChangeType TaskStatusChangeType `json:"change_type"`
		Count      int64                `json:"count"`
	}
	query.Select("change_type, COUNT(*) as count").
		Group("change_type").
		Scan(&changeTypeStats)

	// Get status transition patterns
	var statusTransitions []struct {
		PreviousStatus models.TaskStatus `json:"previous_status"`
		NewStatus      models.TaskStatus `json:"new_status"`
		Count          int64             `json:"count"`
	}
	query.Select("previous_status, new_status, COUNT(*) as count").
		Group("previous_status, new_status").
		Order("count DESC").
		Limit(20).
		Scan(&statusTransitions)

	// Get automated vs manual changes
	var automationStats []struct {
		AutomatedChange bool  `json:"automated_change"`
		Count           int64 `json:"count"`
	}
	query.Select("automated_change, COUNT(*) as count").
		Group("automated_change").
		Scan(&automationStats)

	// Get most active users
	var userActivity []struct {
		ChangedByID uint  `json:"changed_by_id"`
		Count       int64 `json:"count"`
	}
	query.Select("changed_by_id, COUNT(*) as count").
		Group("changed_by_id").
		Order("count DESC").
		Limit(10).
		Scan(&userActivity)

	return map[string]interface{}{
		"total_changes":      totalChanges,
		"change_type_stats":  changeTypeStats,
		"status_transitions": statusTransitions,
		"automation_stats":   automationStats,
		"user_activity":      userActivity,
		"analysis_period": map[string]interface{}{
			"from": dateFrom,
			"to":   dateTo,
		},
	}, nil
}

// IntegrateWithTaskPerformanceService integrates status tracking with performance evaluation
func (s *TaskPerformanceService) IntegrateWithTaskPerformanceService() *TaskStatusTracker {
	return NewTaskStatusTracker(s.db)
}

// TrackTaskCreation tracks when a task is created
func (s *TaskPerformanceService) TrackTaskCreation(task *models.Task, createdByID uint) error {
	tracker := s.IntegrateWithTaskPerformanceService()
	return tracker.TrackStatusChange(
		task.ID,
		"", // No previous status for new tasks
		task.Status,
		createdByID,
		"Task created",
		TaskStatusChangeTypeCreated,
		false,
	)
}

// TrackTaskCompletion tracks when a task is completed with performance context
func (s *TaskPerformanceService) TrackTaskCompletion(task *models.Task, completedByID uint) error {
	tracker := s.IntegrateWithTaskPerformanceService()

	// Calculate performance context for the change reason
	var changeReason string
	if task.DueDate != nil {
		if time.Now().Before(*task.DueDate) {
			changeReason = "Task completed before deadline"
		} else {
			changeReason = "Task completed after deadline"
		}
	} else {
		changeReason = "Task completed"
	}

	return tracker.TrackStatusChange(
		task.ID,
		models.TaskStatusInProgress, // Assume it was in progress
		models.TaskStatusCompleted,
		completedByID,
		changeReason,
		TaskStatusChangeTypeCompleted,
		false,
	)
}
