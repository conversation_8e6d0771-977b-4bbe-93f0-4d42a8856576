package auth

import (
	"errors"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"fmt"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
)

var jwtSecret []byte

// Claims represents JWT claims
type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// InitializeJWT initializes JWT with secret
func InitializeJWT(secret string) {
	jwtSecret = []byte(secret)
}

// GenerateToken generates a JWT token for a user
func GenerateToken(user *models.User, expiryHours int) (string, error) {
	if len(jwtSecret) == 0 {
		return "", errors.New("JWT secret not initialized")
	}

	claims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     string(user.Role),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour * time.Duration(expiryHours))),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "federal-register-clone",
			Subject:   user.Username,
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(jwtSecret)
}

// ValidateToken validates a JWT token and returns claims
func ValidateToken(tokenString string) (*Claims, error) {
	if len(jwtSecret) == 0 {
		return nil, errors.New("JWT secret not initialized")
	}

	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New("unexpected signing method")
		}
		return jwtSecret, nil
	})

	if err != nil {
		fmt.Printf("DEBUG: Token parse error: %v\n", err)
		fmt.Printf("DEBUG: Token string: '%s'\n", tokenString)
		fmt.Printf("DEBUG: Token length: %d\n", len(tokenString))
		segments := strings.Split(tokenString, ".")
		fmt.Printf("DEBUG: Token segments: %d\n", len(segments))
		for i, segment := range segments {
			fmt.Printf("DEBUG: Segment %d: '%s' (length: %d)\n", i, segment, len(segment))
		}
		return nil, err
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		fmt.Printf("DEBUG: Token valid - UserID: %d, Expires: %v, Current: %v\n",
			claims.UserID, claims.ExpiresAt.Time, time.Now())
		return claims, nil
	}

	fmt.Printf("DEBUG: Token invalid - Valid: %v\n", token.Valid)
	return nil, errors.New("invalid token")
}

// HashPassword hashes a password using bcrypt
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}

// CheckPassword checks if a password matches the hash
func CheckPassword(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

// AuthenticateUser authenticates a user with username/email and password
func AuthenticateUser(identifier, password string) (*models.User, error) {
	db := database.GetDB()
	if db == nil {
		return nil, errors.New("database not initialized")
	}

	var user models.User

	// Try to find user by username or email
	err := db.Where("username = ? OR email = ?", identifier, identifier).First(&user).Error
	if err != nil {
		return nil, errors.New("invalid credentials")
	}

	// Check if user is active
	if !user.IsActive {
		return nil, errors.New("user account is inactive")
	}

	// Check password
	if !CheckPassword(password, user.PasswordHash) {
		return nil, errors.New("invalid credentials")
	}

	// Update last login time
	now := time.Now()
	user.LastLoginAt = &now
	db.Save(&user)

	return &user, nil
}

// GetUserByID retrieves a user by ID
func GetUserByID(userID uint) (*models.User, error) {
	db := database.GetDB()
	if db == nil {
		return nil, errors.New("database not initialized")
	}

	var user models.User
	err := db.Preload("Agency").First(&user, userID).Error
	if err != nil {
		return nil, err
	}

	return &user, nil
}

// CreateUser creates a new user
func CreateUser(user *models.User, password string) error {
	db := database.GetDB()
	if db == nil {
		return errors.New("database not initialized")
	}

	// Hash password
	hashedPassword, err := HashPassword(password)
	if err != nil {
		return err
	}
	user.PasswordHash = hashedPassword

	// Create user
	return db.Create(user).Error
}

// UpdateUser updates a user's profile information
func UpdateUser(user *models.User) error {
	db := database.GetDB()
	if db == nil {
		return errors.New("database not initialized")
	}

	return db.Save(user).Error
}

// UpdateUserPassword updates a user's password
func UpdateUserPassword(userID uint, newPassword string) error {
	db := database.GetDB()
	if db == nil {
		return errors.New("database not initialized")
	}

	// Hash new password
	hashedPassword, err := HashPassword(newPassword)
	if err != nil {
		return err
	}

	// Update password
	return db.Model(&models.User{}).Where("id = ?", userID).Update("password_hash", hashedPassword).Error
}

// CreateUserSession creates a new user session
func CreateUserSession(userID uint, token string, expiresAt time.Time, ipAddress, userAgent string) error {
	db := database.GetDB()
	if db == nil {
		return errors.New("database not initialized")
	}

	session := &models.UserSession{
		UserID:    userID,
		Token:     token,
		ExpiresAt: expiresAt,
		IPAddress: ipAddress,
		UserAgent: userAgent,
		IsActive:  true,
	}

	return db.Create(session).Error
}

// InvalidateUserSession invalidates a user session
func InvalidateUserSession(token string) error {
	db := database.GetDB()
	if db == nil {
		return errors.New("database not initialized")
	}

	return db.Model(&models.UserSession{}).Where("token = ?", token).Update("is_active", false).Error
}

// InvalidateAllUserSessions invalidates all sessions for a user
func InvalidateAllUserSessions(userID uint) error {
	db := database.GetDB()
	if db == nil {
		return errors.New("database not initialized")
	}

	return db.Model(&models.UserSession{}).Where("user_id = ?", userID).Update("is_active", false).Error
}

// IsSessionValid checks if a session is valid
func IsSessionValid(token string) bool {
	db := database.GetDB()
	if db == nil {
		return false
	}

	var session models.UserSession
	err := db.Where("token = ? AND is_active = ? AND expires_at > ?", token, true, time.Now()).First(&session).Error
	return err == nil
}

// CleanupExpiredSessions removes expired sessions
func CleanupExpiredSessions() error {
	db := database.GetDB()
	if db == nil {
		return errors.New("database not initialized")
	}

	return db.Where("expires_at < ?", time.Now()).Delete(&models.UserSession{}).Error
}
