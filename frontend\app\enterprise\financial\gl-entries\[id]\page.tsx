'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../services/enterpriseApi';
import { GeneralLedger } from '../../../../types/enterprise';

const GLEntryViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const entryId = parseInt(params.id as string);
  
  const [entry, setEntry] = useState<GeneralLedger | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (entryId) {
      fetchEntry();
    }
  }, [entryId]);

  const fetchEntry = async () => {
    try {
      setLoading(true);
      const response = await financialApi.getGLEntry(entryId);
      setEntry(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch GL entry');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this GL entry?')) return;
    
    try {
      await financialApi.deleteGLEntry(entryId);
      router.push('/enterprise/financial/gl-entries');
    } catch (err: any) {
      setError(err.message || 'Failed to delete GL entry');
    }
  };

  const handlePost = async () => {
    if (!confirm('Are you sure you want to post this GL entry?')) return;
    
    try {
      await financialApi.postGLEntry(entryId);
      await fetchEntry(); // Refresh the entry
    } catch (err: any) {
      setError(err.message || 'Failed to post GL entry');
    }
  };

  if (loading) return <div className="p-6">Loading GL entry...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!entry) return <div className="p-6">GL entry not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">GL Entry Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/financial/gl-entries')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to GL Entries
          </button>
          {entry.status === 'pending' && (
            <>
              <button
                onClick={() => router.push(`/enterprise/financial/gl-entries/${entryId}/edit`)}
                className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
              >
                Edit Entry
              </button>
              <button
                onClick={handlePost}
                className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
              >
                Post Entry
              </button>
            </>
          )}
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Entry
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{entry.entry_number}</h2>
              <p className="text-sm text-gray-600">
                Transaction Date: {new Date(entry.transaction_date).toLocaleDateString()}
              </p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                entry.status === 'posted' 
                  ? 'bg-green-100 text-green-800'
                  : entry.status === 'pending'
                  ? 'bg-yellow-100 text-yellow-800' 
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {entry.status}
              </span>
            </div>
          </div>
        </div>

        {/* Entry Information */}
        <div className="px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Entry Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Account</label>
              <p className="mt-1 text-sm text-gray-900">
                {entry.account?.account_code} - {entry.account?.account_name}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Debit Amount</label>
              <p className="mt-1 text-sm text-gray-900 font-semibold">
                {entry.currency_code} {entry.debit_amount.toLocaleString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Credit Amount</label>
              <p className="mt-1 text-sm text-gray-900 font-semibold">
                {entry.currency_code} {entry.credit_amount.toLocaleString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Reference Number</label>
              <p className="mt-1 text-sm text-gray-900">{entry.reference_number || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Fiscal Year</label>
              <p className="mt-1 text-sm text-gray-900">{entry.fiscal_year}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Fiscal Period</label>
              <p className="mt-1 text-sm text-gray-900">{entry.fiscal_period}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Source Document Type</label>
              <p className="mt-1 text-sm text-gray-900">{entry.source_document_type || 'N/A'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Reconciliation Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                entry.reconciliation_status === 'reconciled' 
                  ? 'bg-green-100 text-green-800'
                  : entry.reconciliation_status === 'partially_reconciled'
                  ? 'bg-yellow-100 text-yellow-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {entry.reconciliation_status}
              </span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Currency</label>
              <p className="mt-1 text-sm text-gray-900">{entry.currency_code}</p>
            </div>
          </div>

          {entry.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <p className="mt-1 text-sm text-gray-900">{entry.description}</p>
            </div>
          )}
        </div>

        {/* Approval Information */}
        {(entry.approved_by || entry.approved_at) && (
          <div className="px-6 py-4 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Approval Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {entry.approved_by && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Approved By</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {entry.approved_by.first_name} {entry.approved_by.last_name}
                  </p>
                </div>
              )}
              
              {entry.approved_at && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Approved At</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(entry.approved_at).toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(entry.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(entry.updated_at).toLocaleString()}</p>
            </div>
            {entry.created_by && (
              <div>
                <label className="block font-medium">Created By</label>
                <p>{entry.created_by.first_name} {entry.created_by.last_name}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GLEntryViewPage;
