package services

import (
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// PaginationParams represents pagination parameters
type PaginationParams struct {
	Page    int `form:"page" json:"page"`
	PerPage int `form:"per_page" json:"per_page"`
}

// DocumentService handles document-related business logic
type DocumentService struct {
	db *gorm.DB
}

// NewDocumentService creates a new document service
func NewDocumentService() *DocumentService {
	return &DocumentService{
		db: database.GetDB(),
	}
}

// CreateDocument creates a new document
func (s *DocumentService) CreateDocument(document *models.Document) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Auto-generate FR Document Number if not provided
	if document.FRDocumentNumber == "" {
		frNumber, err := s.generateFRDocumentNumber()
		if err != nil {
			return fmt.Errorf("failed to generate FR document number: %w", err)
		}
		document.FRDocumentNumber = frNumber
	}

	// Auto-generate Docket Number if not provided
	if document.DocketNumber == "" {
		docketNumber, err := s.generateDocketNumber(document.AgencyID, document.Type)
		if err != nil {
			return fmt.Errorf("failed to generate docket number: %w", err)
		}
		document.DocketNumber = docketNumber
	}

	// Generate slug from title
	document.Slug = generateSlug(document.Title)

	// Set default values
	if document.Language == "" {
		document.Language = "en"
	}
	if document.Status == "" {
		document.Status = models.StatusDraft
	}

	// Auto-determine status based on publication date
	s.updateDocumentStatus(document)

	// Create the document
	if err := s.db.Create(document).Error; err != nil {
		return fmt.Errorf("failed to create document: %w", err)
	}

	return nil
}

// GetDocuments retrieves documents with pagination and search
func (s *DocumentService) GetDocuments(pagination PaginationParams, searchParams SearchParams) ([]models.Document, int64, error) {
	if s.db == nil {
		return nil, 0, errors.New("database not initialized")
	}

	var documents []models.Document
	var total int64

	query := s.db.Model(&models.Document{}).Preload("Agency").Preload("CreatedBy").Preload("Categories")

	// Apply search filters
	if searchParams.Query != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ? OR abstract ILIKE ?",
			"%"+searchParams.Query+"%", "%"+searchParams.Query+"%", "%"+searchParams.Query+"%")
	}

	if searchParams.Type != "" {
		query = query.Where("type = ?", searchParams.Type)
	}

	if searchParams.Status != "" {
		query = query.Where("status = ?", searchParams.Status)
	}

	if searchParams.AgencyID != 0 {
		query = query.Where("agency_id = ?", searchParams.AgencyID)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count documents: %w", err)
	}

	// Apply pagination
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := query.Offset(offset).Limit(pagination.PerPage).Find(&documents).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get documents: %w", err)
	}

	return documents, total, nil
}

// GetDocument retrieves a document by ID
func (s *DocumentService) GetDocument(id uint) (*models.Document, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var document models.Document
	err := s.db.Preload("Agency").
		Preload("CreatedBy").
		Preload("UpdatedBy").
		Preload("Categories").
		Preload("Tags").
		Preload("Files").
		First(&document, id).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("document not found")
		}
		return nil, fmt.Errorf("failed to get document: %w", err)
	}

	// Increment view count
	s.db.Model(&document).Update("view_count", gorm.Expr("view_count + ?", 1))

	return &document, nil
}

// UpdateDocument updates an existing document
func (s *DocumentService) UpdateDocument(id uint, updates map[string]interface{}) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Update slug if title is being updated
	if title, ok := updates["title"].(string); ok {
		updates["slug"] = generateSlug(title)
	}

	updates["updated_at"] = time.Now()

	result := s.db.Model(&models.Document{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update document: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("document not found")
	}

	return nil
}

// DeleteDocument deletes a document
func (s *DocumentService) DeleteDocument(id uint) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	result := s.db.Delete(&models.Document{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete document: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("document not found")
	}

	return nil
}

// SearchDocuments searches documents with filters
func (s *DocumentService) SearchDocuments(params SearchParams) (*SearchResult, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	query := s.db.Model(&models.Document{}).
		Preload("Agency").
		Preload("CreatedBy").
		Preload("Categories").
		Preload("Tags")

	// Apply filters
	if params.Query != "" {
		// Full-text search
		query = query.Where("to_tsvector('english', title || ' ' || COALESCE(abstract, '') || ' ' || COALESCE(content, '')) @@ plainto_tsquery('english', ?)", params.Query)
	}

	if params.AgencyID != 0 {
		query = query.Where("agency_id = ?", params.AgencyID)
	}

	if params.Type != "" {
		query = query.Where("type = ?", params.Type)
	}

	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	if !params.DateFrom.IsZero() {
		query = query.Where("created_at >= ?", params.DateFrom)
	}

	if !params.DateTo.IsZero() {
		query = query.Where("created_at <= ?", params.DateTo)
	}

	// Apply sorting
	orderClause := "created_at DESC"
	if params.Sort != "" {
		direction := "ASC"
		if params.Order == "desc" {
			direction = "DESC"
		}
		orderClause = fmt.Sprintf("%s %s", params.Sort, direction)
	}
	query = query.Order(orderClause)

	// Count total results
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count documents: %w", err)
	}

	// Apply pagination
	offset := (params.Page - 1) * params.PerPage
	query = query.Offset(offset).Limit(params.PerPage)

	// Execute query
	var documents []models.Document
	if err := query.Find(&documents).Error; err != nil {
		return nil, fmt.Errorf("failed to search documents: %w", err)
	}

	return &SearchResult{
		Documents:  documents,
		Total:      total,
		Page:       params.Page,
		PerPage:    params.PerPage,
		TotalPages: (int(total) + params.PerPage - 1) / params.PerPage,
	}, nil
}

// UploadDocumentFile uploads a file for a document
func (s *DocumentService) UploadDocumentFile(documentID uint, file *multipart.FileHeader, uploadedByID uint, description string) (*models.DocumentFile, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	// Validate file type
	if !isAllowedFileType(file.Filename) {
		return nil, errors.New("file type not allowed")
	}

	// Create upload directory if it doesn't exist
	uploadDir := "./uploads/documents"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create upload directory: %w", err)
	}

	// Generate unique filename
	ext := filepath.Ext(file.Filename)
	filename := fmt.Sprintf("%d_%d%s", documentID, time.Now().Unix(), ext)
	filePath := filepath.Join(uploadDir, filename)

	// Open uploaded file
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open uploaded file: %w", err)
	}
	defer src.Close()

	// Create destination file
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dst.Close()

	// Copy file content
	if _, err := io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("failed to copy file: %w", err)
	}

	// Create file record
	documentFile := &models.DocumentFile{
		DocumentID:   documentID,
		FileName:     filename,
		OriginalName: file.Filename,
		FilePath:     filePath,
		FileType:     strings.TrimPrefix(ext, "."),
		FileSize:     file.Size,
		MimeType:     file.Header.Get("Content-Type"),
		Description:  description,
		IsPublic:     true,
		UploadedByID: uploadedByID,
	}

	if err := s.db.Create(documentFile).Error; err != nil {
		// Clean up file if database insert fails
		os.Remove(filePath)
		return nil, fmt.Errorf("failed to create file record: %w", err)
	}

	return documentFile, nil
}

// UpdateDocumentStatus updates the status of a document
func (s *DocumentService) UpdateDocumentStatus(id uint, status models.DocumentStatus, userID uint) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	// Set specific fields based on status
	switch status {
	case models.StatusApproved:
		updates["approved_at"] = time.Now()
		updates["approved_by_id"] = userID
	case models.StatusPublished:
		updates["published_at"] = time.Now()
		updates["published_by_id"] = userID
	}

	result := s.db.Model(&models.Document{}).Where("id = ?", id).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("failed to update document status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("document not found")
	}

	return nil
}

// SearchParams represents search parameters
type SearchParams struct {
	Query    string
	AgencyID uint
	Type     string
	Status   string
	DateFrom time.Time
	DateTo   time.Time
	Sort     string
	Order    string
	Page     int
	PerPage  int
}

// SearchResult represents search results
type SearchResult struct {
	Documents  []models.Document
	Total      int64
	Page       int
	PerPage    int
	TotalPages int
}

// Helper functions

func generateSlug(title string) string {
	// Simple slug generation - in production, use a proper slug library
	slug := strings.ToLower(title)
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")
	// Remove special characters (basic implementation)
	var result strings.Builder
	for _, r := range slug {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// generateFRDocumentNumber generates a sequential FR document number
// Format: YYYY-NNNNN (e.g., 2025-00001, 2025-00002)
func (s *DocumentService) generateFRDocumentNumber() (string, error) {
	currentYear := time.Now().Year()

	// Get the highest FR document number for the current year
	var maxNumber int
	var lastFRNumber string

	err := s.db.Model(&models.Document{}).
		Where("fr_document_number LIKE ?", fmt.Sprintf("%d-%%", currentYear)).
		Where("fr_document_number != ''").
		Order("fr_document_number DESC").
		Limit(1).
		Pluck("fr_document_number", &lastFRNumber).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return "", fmt.Errorf("failed to get last FR document number: %w", err)
	}

	if lastFRNumber != "" {
		// Extract the number part from the last FR number
		parts := strings.Split(lastFRNumber, "-")
		if len(parts) == 2 {
			if num, err := strconv.Atoi(parts[1]); err == nil {
				maxNumber = num
			}
		}
	}

	// Increment and format
	nextNumber := maxNumber + 1
	return fmt.Sprintf("%d-%05d", currentYear, nextNumber), nil
}

// generateDocketNumber generates a sequential docket number
// Format: AGENCY-TYPE-NNNNN (e.g., DOE-RULE-00001, EPA-PROP-00002)
func (s *DocumentService) generateDocketNumber(agencyID uint, docType models.DocumentType) (string, error) {
	// Get agency short name
	var agency models.Agency
	if err := s.db.First(&agency, agencyID).Error; err != nil {
		return "", fmt.Errorf("failed to get agency: %w", err)
	}

	// Determine type prefix
	var typePrefix string
	switch docType {
	case "rule":
		typePrefix = "RULE"
	case "proposed_rule":
		typePrefix = "PROP"
	case "notice":
		typePrefix = "NOTC"
	case "presidential_document":
		typePrefix = "PRES"
	default:
		typePrefix = "MISC"
	}

	// Get the highest docket number for this agency and type
	var maxNumber int
	var lastDocketNumber string

	docketPrefix := fmt.Sprintf("%s-%s-", agency.ShortName, typePrefix)

	err := s.db.Model(&models.Document{}).
		Where("docket_number LIKE ?", docketPrefix+"%").
		Order("docket_number DESC").
		Limit(1).
		Pluck("docket_number", &lastDocketNumber).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return "", fmt.Errorf("failed to get last docket number: %w", err)
	}

	if lastDocketNumber != "" {
		// Extract the number part from the last docket number
		parts := strings.Split(lastDocketNumber, "-")
		if len(parts) == 3 {
			if num, err := strconv.Atoi(parts[2]); err == nil {
				maxNumber = num
			}
		}
	}

	// Increment and format
	nextNumber := maxNumber + 1
	return fmt.Sprintf("%s%05d", docketPrefix, nextNumber), nil
}

// updateDocumentStatus automatically determines document status based on publication date
func (s *DocumentService) updateDocumentStatus(document *models.Document) {
	now := time.Now()

	// If publication date is set and is in the past or today, mark as published
	if document.PublicationDate != nil && !document.PublicationDate.After(now) {
		document.Status = models.StatusPublished
	} else if document.PublicationDate != nil && document.PublicationDate.After(now) {
		// If publication date is in the future, mark as approved (ready for publication)
		document.Status = models.StatusApproved
	}
	// Otherwise, keep the current status (draft, under_review, etc.)
}

func isAllowedFileType(filename string) bool {
	allowedTypes := []string{".pdf", ".doc", ".docx", ".txt"}
	ext := strings.ToLower(filepath.Ext(filename))

	for _, allowedType := range allowedTypes {
		if ext == allowedType {
			return true
		}
	}
	return false
}
