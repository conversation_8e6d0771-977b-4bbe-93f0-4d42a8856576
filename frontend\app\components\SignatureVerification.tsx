'use client';

import React, { useState } from 'react';
import { DigitalSignature } from '../types';

interface SignatureVerificationProps {
  commentId: number;
  isVerified?: boolean;
  onVerify: (commentId: number) => Promise<DigitalSignature | null>;
  className?: string;
}

export const SignatureVerification: React.FC<SignatureVerificationProps> = ({
  commentId,
  isVerified = false,
  onVerify,
  className = ''
}) => {
  const [signature, setSignature] = useState<DigitalSignature | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const handleVerify = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await onVerify(commentId);
      setSignature(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Verification failed');
    } finally {
      setLoading(false);
    }
  };

  const getVerificationIcon = () => {
    if (!isVerified) return '📝';
    if (signature?.overall_valid) return '✅';
    if (signature?.overall_valid === false) return '❌';
    return '🔍';
  };

  const getVerificationStatus = () => {
    if (!isVerified) return 'Not Signed';
    if (signature?.overall_valid) return 'Valid Signature';
    if (signature?.overall_valid === false) return 'Invalid Signature';
    return 'Verification Pending';
  };

  const getStatusColor = () => {
    if (!isVerified) return 'text-gray-600';
    if (signature?.overall_valid) return 'text-green-600';
    if (signature?.overall_valid === false) return 'text-red-600';
    return 'text-blue-600';
  };

  return (
    <div className={`border rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-lg">{getVerificationIcon()}</span>
          <span className={`font-medium ${getStatusColor()}`}>
            {getVerificationStatus()}
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          {isVerified && !signature && (
            <button
              onClick={handleVerify}
              disabled={loading}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Verifying...' : 'Verify'}
            </button>
          )}
          
          {signature && (
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </button>
          )}
        </div>
      </div>

      {error && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
          <div className="text-red-800 text-sm">
            <strong>Verification Error:</strong> {error}
          </div>
        </div>
      )}

      {signature && showDetails && (
        <div className="mt-4 space-y-3">
          <div className="border-t pt-3">
            <h4 className="font-medium text-gray-900 mb-2">Signature Details</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
              <div>
                <span className="font-medium text-gray-700">Signature ID:</span>
                <div className="text-gray-600 font-mono text-xs">{signature.signature_id}</div>
              </div>
              
              <div>
                <span className="font-medium text-gray-700">Signed At:</span>
                <div className="text-gray-600">{new Date(signature.signed_at).toLocaleString()}</div>
              </div>
              
              <div>
                <span className="font-medium text-gray-700">Signer:</span>
                <div className="text-gray-600">{signature.signer_name}</div>
              </div>
              
              <div>
                <span className="font-medium text-gray-700">Email:</span>
                <div className="text-gray-600">{signature.signer_email}</div>
              </div>
            </div>
          </div>

          {signature.certificate && (
            <div className="border-t pt-3">
              <h4 className="font-medium text-gray-900 mb-2">Certificate Information</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Serial Number:</span>
                  <div className="text-gray-600 font-mono text-xs">{signature.certificate.serial}</div>
                </div>
                
                <div>
                  <span className="font-medium text-gray-700">Status:</span>
                  <div className={`${signature.certificate_valid ? 'text-green-600' : 'text-red-600'}`}>
                    {signature.certificate.status}
                  </div>
                </div>
                
                <div>
                  <span className="font-medium text-gray-700">Subject:</span>
                  <div className="text-gray-600 text-xs">{signature.certificate.subject}</div>
                </div>
                
                <div>
                  <span className="font-medium text-gray-700">Issuer:</span>
                  <div className="text-gray-600 text-xs">{signature.certificate.issuer}</div>
                </div>
                
                <div>
                  <span className="font-medium text-gray-700">Valid From:</span>
                  <div className="text-gray-600">{new Date(signature.certificate.not_before).toLocaleDateString()}</div>
                </div>
                
                <div>
                  <span className="font-medium text-gray-700">Valid Until:</span>
                  <div className="text-gray-600">{new Date(signature.certificate.not_after).toLocaleDateString()}</div>
                </div>
              </div>
            </div>
          )}

          <div className="border-t pt-3">
            <h4 className="font-medium text-gray-900 mb-2">Verification Checks</h4>
            
            <div className="space-y-2">
              {Object.entries(signature.verification_checks).map(([check, passed]) => (
                <div key={check} className="flex items-center space-x-2">
                  <span className={passed ? 'text-green-600' : 'text-red-600'}>
                    {passed ? '✅' : '❌'}
                  </span>
                  <span className="text-sm text-gray-700">
                    {check.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                </div>
              ))}
            </div>
            
            <div className="mt-3 p-3 rounded-lg bg-gray-50">
              <div className="flex items-center space-x-2">
                <span className={signature.overall_valid ? 'text-green-600' : 'text-red-600'}>
                  {signature.overall_valid ? '✅' : '❌'}
                </span>
                <span className="font-medium">
                  Overall Verification: {signature.overall_valid ? 'PASSED' : 'FAILED'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SignatureVerification;
