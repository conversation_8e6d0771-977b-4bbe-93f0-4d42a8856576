'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  MagnifyingGlassIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  ArrowRightIcon,
  ChartBarIcon,
  DocumentPlusIcon,
  UserCircleIcon,
  ClockIcon,
  EyeIcon
} from '@heroicons/react/24/outline';
import Layout from './components/Layout/Layout';
import apiService from './services/api';
import { useAuthStore } from './stores/authStore';
import { Document, Agency, Category } from './types';

const HomePage: React.FC = () => {
  const { isAuthenticated } = useAuthStore();
  const [recentDocuments, setRecentDocuments] = useState<Document[]>([]);
  const [featuredAgencies, setFeaturedAgencies] = useState<Agency[]>([]);
  const [popularCategories, setPopularCategories] = useState<Category[]>([]);
  const [stats, setStats] = useState({
    totalDocuments: 0,
    totalAgencies: 0,
    recentDocuments: 0,
  });
  const [loading, setLoading] = useState(true);

  // Note: Removed auto-redirect to dashboard to prevent redirect loops
  // Users can access home page regardless of authentication status

  useEffect(() => {
    const fetchHomeData = async () => {
      try {
        setLoading(true);
        
        // Fetch recent documents
        const documentsResponse = await apiService.getPublicDocuments({ 
          page: 1, 
          per_page: 6,
          sort: 'created_at',
          order: 'desc'
        });
        setRecentDocuments(documentsResponse.data);
        
        // Fetch featured agencies
        const agenciesResponse = await apiService.getPublicAgencies({ 
          page: 1, 
          per_page: 8 
        });
        setFeaturedAgencies(agenciesResponse.data);
        
        // Fetch popular categories
        const categoriesResponse = await apiService.getPublicCategories();
        setPopularCategories(categoriesResponse.data.slice(0, 6));
        
        // Fetch stats
        const statsResponse = await apiService.getPublicStats();
        setStats(statsResponse);
      } catch (error) {
        console.error('Error fetching home data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHomeData();
  }, []);

  if (loading) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div className="container-custom py-16 lg:py-24">
          <div className="text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              Federal Register Clone
            </h1>
            <p className="text-xl lg:text-2xl mb-8 text-primary-100">
              Your comprehensive document management system
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/search"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors duration-200"
              >
                <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
                Search Documents
              </Link>
              {!isAuthenticated && (
                <Link
                  href="/register"
                  className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-md text-white bg-transparent hover:bg-white hover:text-primary-600 transition-colors duration-200"
                >
                  <UserCircleIcon className="h-5 w-5 mr-2" />
                  Get Started
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="bg-white dark:bg-gray-900 py-12">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg mx-auto mb-4">
                <DocumentTextIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {(stats.totalDocuments || 0).toLocaleString()}
              </div>
              <div className="text-gray-600 dark:text-gray-400">Total Documents</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg mx-auto mb-4">
                <BuildingOfficeIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {(stats.totalAgencies || 0).toLocaleString()}
              </div>
              <div className="text-gray-600 dark:text-gray-400">Government Agencies</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center w-12 h-12 bg-primary-100 dark:bg-primary-900 rounded-lg mx-auto mb-4">
                <ClockIcon className="h-6 w-6 text-primary-600 dark:text-primary-400" />
              </div>
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {(stats.recentDocuments || 0).toLocaleString()}
              </div>
              <div className="text-gray-600 dark:text-gray-400">Recent Updates</div>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Documents Section */}
      <div className="bg-gray-50 dark:bg-gray-800 py-16">
        <div className="container-custom">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Recent Documents
            </h2>
            <Link
              href="/documents"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 font-medium"
            >
              View all documents
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {recentDocuments.map((document) => (
              <div key={document.id} className="document-card p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                      {document.title}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {typeof document.agency === 'string' ? document.agency : document.agency?.name || 'Unknown Agency'}
                    </p>
                  </div>
                  <span className="badge-primary">
                    {document.type}
                  </span>
                </div>
                <p className="text-gray-700 dark:text-gray-300 text-sm mb-4 line-clamp-3">
                  {document.abstract || document.content?.substring(0, 150) + '...'}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {new Date(document.created_at).toLocaleDateString()}
                  </span>
                  <Link
                    href={`/documents/${document.id}`}
                    className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 text-sm font-medium"
                  >
                    <EyeIcon className="h-4 w-4 mr-1" />
                    View
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Featured Agencies Section */}
      <div className="bg-white dark:bg-gray-900 py-16">
        <div className="container-custom">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Government Agencies
            </h2>
            <Link
              href="/agencies"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 font-medium"
            >
              View all agencies
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredAgencies.map((agency) => (
              <Link
                key={agency.id}
                href={`/agencies/${agency.id}`}
                className="agency-card p-6 text-center group"
              >
                <div className="flex items-center justify-center w-16 h-16 bg-primary-100 dark:bg-primary-900 rounded-lg mx-auto mb-4 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors duration-200">
                  <BuildingOfficeIcon className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {agency.name}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                  {agency.description}
                </p>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Popular Categories Section */}
      <div className="bg-gray-50 dark:bg-gray-800 py-16">
        <div className="container-custom">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Popular Categories
            </h2>
            <Link
              href="/categories"
              className="inline-flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 font-medium"
            >
              View all categories
              <ArrowRightIcon className="h-4 w-4 ml-1" />
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {popularCategories.map((category) => (
              <Link
                key={category.id}
                href={`/categories/${category.id}`}
                className="category-card p-6 group"
              >
                <div className="flex items-center mb-4">
                  <div className="flex items-center justify-center w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg mr-4 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors duration-200">
                    <TagIcon className="h-5 w-5 text-primary-600 dark:text-primary-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {category.name}
                  </h3>
                </div>
                <p className="text-gray-700 dark:text-gray-300 text-sm line-clamp-2">
                  {category.description}
                </p>
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions Section */}
      {isAuthenticated && (
        <div className="bg-white dark:bg-gray-900 py-16">
          <div className="container-custom">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
              Quick Actions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Link
                href="/documents/new"
                className="card p-6 text-center hover:shadow-lg transition-shadow duration-200 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg mx-auto mb-4 group-hover:bg-green-200 dark:group-hover:bg-green-800 transition-colors duration-200">
                  <DocumentPlusIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Create Document
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Add a new document to the system
                </p>
              </Link>
              <Link
                href="/dashboard"
                className="card p-6 text-center hover:shadow-lg transition-shadow duration-200 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg mx-auto mb-4 group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors duration-200">
                  <ChartBarIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Dashboard
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  View your analytics and reports
                </p>
              </Link>
              <Link
                href="/profile"
                className="card p-6 text-center hover:shadow-lg transition-shadow duration-200 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg mx-auto mb-4 group-hover:bg-purple-200 dark:group-hover:bg-purple-800 transition-colors duration-200">
                  <UserCircleIcon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Profile
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Manage your account settings
                </p>
              </Link>
              <Link
                href="/search"
                className="card p-6 text-center hover:shadow-lg transition-shadow duration-200 group"
              >
                <div className="flex items-center justify-center w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg mx-auto mb-4 group-hover:bg-yellow-200 dark:group-hover:bg-yellow-800 transition-colors duration-200">
                  <MagnifyingGlassIcon className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Advanced Search
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Find documents with filters
                </p>
              </Link>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default HomePage;
