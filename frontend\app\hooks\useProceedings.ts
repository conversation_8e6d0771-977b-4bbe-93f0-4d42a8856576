import { useState, useEffect, useCallback } from 'react';
import apiService from '../services/api';
import { Proceeding, ProceedingFilters } from '../types';

// Using imported types from ../types

interface UseProceedingsOptions {
  autoLoad?: boolean;
  filters?: ProceedingFilters;
  page?: number;
  perPage?: number;
}

export const useProceedings = (options: UseProceedingsOptions = {}) => {
  const {
    autoLoad = true,
    filters: initialFilters = {},
    page = 1,
    perPage = 20
  } = options;

  const [proceedings, setProceedings] = useState<Proceeding[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState<ProceedingFilters>(initialFilters);
  const [pagination, setPagination] = useState({
    page,
    per_page: perPage,
    total: 0,
    total_pages: 0,
    has_next: false,
    has_prev: false
  });

  const loadProceedings = useCallback(async (customFilters?: ProceedingFilters, customPage?: number, customSearchTerm?: string) => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: customPage || pagination.page,
        per_page: pagination.per_page,
        search: customSearchTerm !== undefined ? customSearchTerm : searchTerm,
        ...filters,
        ...customFilters
      };

      const response = await apiService.getProceedings(params);
      
      setProceedings(response.data);
      setPagination({
        page: response.page,
        per_page: response.per_page,
        total: response.total,
        total_pages: response.total_pages,
        has_next: response.has_next,
        has_prev: response.has_prev
      });
    } catch (err: any) {
      console.error('Error loading proceedings:', err);
      setError(err.response?.data?.message || 'Failed to load proceedings');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.per_page, searchTerm]);

  const createProceeding = useCallback(async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.createProceeding(data);
      
      // Reload proceedings to include the new one
      await loadProceedings();
      
      return response.data;
    } catch (err: any) {
      console.error('Error creating proceeding:', err);
      setError(err.response?.data?.message || 'Failed to create proceeding');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadProceedings]);

  const updateProceeding = useCallback(async (id: number, data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateProceeding(id, data);
      
      // Update the proceeding in the local state
      setProceedings(prev => 
        prev.map(proceeding => 
          proceeding.id === id ? { ...proceeding, ...response.data } : proceeding
        )
      );
      
      return response.data;
    } catch (err: any) {
      console.error('Error updating proceeding:', err);
      setError(err.response?.data?.message || 'Failed to update proceeding');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const deleteProceeding = useCallback(async (id: number) => {
    try {
      setLoading(true);
      setError(null);

      await apiService.deleteProceeding(id);
      
      // Remove the proceeding from local state
      setProceedings(prev => prev.filter(proceeding => proceeding.id !== id));
      
      // If this was the last item on the page and we're not on page 1, go to previous page
      if (proceedings.length === 1 && pagination.page > 1) {
        await loadProceedings({}, pagination.page - 1);
      } else {
        await loadProceedings();
      }
    } catch (err: any) {
      console.error('Error deleting proceeding:', err);
      setError(err.response?.data?.message || 'Failed to delete proceeding');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [proceedings.length, pagination.page, loadProceedings]);

  const refreshProceedings = useCallback(() => {
    return loadProceedings();
  }, [loadProceedings]);

  const setPage = useCallback((newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
    loadProceedings({}, newPage);
  }, [loadProceedings]);

  const updateFilters = useCallback((newFilters: ProceedingFilters) => {
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, page: 1 }));
    loadProceedings(newFilters, 1);
  }, [loadProceedings]);

  useEffect(() => {
    if (autoLoad) {
      loadProceedings();
    }
  }, [autoLoad, loadProceedings]);

  return {
    proceedings,
    loading,
    error,
    pagination,
    filters,
    searchTerm,
    setSearchTerm,
    setFilters: updateFilters,
    setPagination,
    loadProceedings,
    fetchProceedings: loadProceedings,
    createProceeding,
    updateProceeding,
    deleteProceeding,
    refreshProceedings,
    setPage,
    clearError: () => setError(null)
  };
};

export const useProceeding = (id: number | null) => {
  const [proceeding, setProceeding] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadProceeding = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getProceeding(id);
      setProceeding(response.data);
    } catch (err: any) {
      console.error('Error loading proceeding:', err);
      setError(err.response?.data?.message || 'Failed to load proceeding');
    } finally {
      setLoading(false);
    }
  }, [id]);

  const updateProceeding = useCallback(async (data: any) => {
    if (!id) return;

    try {
      setLoading(true);
      setError(null);

      const response = await apiService.updateProceeding(id, data);
      setProceeding(response.data);
      
      return response.data;
    } catch (err: any) {
      console.error('Error updating proceeding:', err);
      setError(err.response?.data?.message || 'Failed to update proceeding');
      throw err;
    } finally {
      setLoading(false);
    }
  }, [id]);

  const updateStepStatus = useCallback(async (stepId: number, status: string, data?: any) => {
    if (!id) return;

    try {
      const response = await apiService.updateStepStatus(id, stepId, {
        status,
        ...data
      });
      
      // Reload the proceeding to get updated step data
      await loadProceeding();
      
      return response.data;
    } catch (err: any) {
      console.error('Error updating step status:', err);
      throw err;
    }
  }, [id, loadProceeding]);

  const addLogEntry = useCallback(async (logData: any) => {
    if (!id) return;

    try {
      const response = await apiService.addProceedingLog(id, logData);
      
      // Reload the proceeding to get updated log data
      await loadProceeding();
      
      return response.data;
    } catch (err: any) {
      console.error('Error adding log entry:', err);
      throw err;
    }
  }, [id, loadProceeding]);

  useEffect(() => {
    if (id) {
      loadProceeding();
    }
  }, [id, loadProceeding]);

  return {
    proceeding,
    loading,
    error,
    loadProceeding,
    updateProceeding,
    updateStepStatus,
    addLogEntry,
    clearError: () => setError(null)
  };
};
