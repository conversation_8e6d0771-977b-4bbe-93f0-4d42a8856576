package services

import (
	"fmt"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// AnalyticsService handles analytics and metrics calculations
type AnalyticsService struct {
	db *gorm.DB
}

// NewAnalyticsService creates a new analytics service
func NewAnalyticsService(db *gorm.DB) *AnalyticsService {
	return &AnalyticsService{db: db}
}

// AnalyticsMetric represents a calculated metric
type AnalyticsMetric struct {
	ID                uint      `json:"id"`
	Name              string    `json:"name"`
	Description       string    `json:"description"`
	MetricType        string    `json:"metric_type"`
	Category          string    `json:"category"`
	CurrentValue      float64   `json:"current_value"`
	PreviousValue     float64   `json:"previous_value"`
	TargetValue       float64   `json:"target_value"`
	Unit              string    `json:"unit"`
	Trend             string    `json:"trend"`
	ThresholdWarning  float64   `json:"threshold_warning"`
	ThresholdCritical float64   `json:"threshold_critical"`
	DataSource        string    `json:"data_source"`
	CalculationMethod string    `json:"calculation_method"`
	IsActive          bool      `json:"is_active"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
	LastCalculated    time.Time `json:"last_calculated"`
}

// DashboardWidget represents a dashboard widget
type DashboardWidget struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Title    string                 `json:"title"`
	Data     map[string]interface{} `json:"data"`
	Config   map[string]interface{} `json:"config"`
	Position map[string]interface{} `json:"position"`
}

// AnalyticsDashboard represents a complete dashboard
type AnalyticsDashboard struct {
	ID            uint              `json:"id"`
	Name          string            `json:"name"`
	Description   string            `json:"description"`
	DashboardType string            `json:"dashboard_type"`
	Widgets       []DashboardWidget `json:"widgets"`
	Layout        map[string]int    `json:"layout"`
	IsPublic      bool              `json:"is_public"`
	CreatedBy     uint              `json:"created_by"`
	CreatedAt     time.Time         `json:"created_at"`
	UpdatedAt     time.Time         `json:"updated_at"`
}

// GetSystemMetrics calculates and returns system-wide metrics
func (s *AnalyticsService) GetSystemMetrics() ([]AnalyticsMetric, error) {
	var metrics []AnalyticsMetric

	// Daily Active Users
	dailyActiveUsers, err := s.calculateDailyActiveUsers()
	if err == nil {
		metrics = append(metrics, AnalyticsMetric{
			ID:                1,
			Name:              "Daily Active Users",
			Description:       "Number of unique users who logged in within the last 24 hours",
			MetricType:        "gauge",
			Category:          "User Engagement",
			CurrentValue:      dailyActiveUsers.Current,
			PreviousValue:     dailyActiveUsers.Previous,
			TargetValue:       1500,
			Unit:              "users",
			Trend:             s.calculateTrend(dailyActiveUsers.Current, dailyActiveUsers.Previous),
			ThresholdWarning:  1000,
			ThresholdCritical: 800,
			DataSource:        "user_sessions",
			CalculationMethod: "COUNT(DISTINCT user_id) WHERE last_login >= NOW() - INTERVAL 1 DAY",
			IsActive:          true,
			LastCalculated:    time.Now(),
		})
	}

	// Document Creation Rate
	docCreationRate, err := s.calculateDocumentCreationRate()
	if err == nil {
		metrics = append(metrics, AnalyticsMetric{
			ID:                2,
			Name:              "Document Creation Rate",
			Description:       "Number of documents created in the last 24 hours",
			MetricType:        "counter",
			Category:          "Content Management",
			CurrentValue:      docCreationRate.Current,
			PreviousValue:     docCreationRate.Previous,
			TargetValue:       50,
			Unit:              "documents",
			Trend:             s.calculateTrend(docCreationRate.Current, docCreationRate.Previous),
			ThresholdWarning:  20,
			ThresholdCritical: 10,
			DataSource:        "documents",
			CalculationMethod: "COUNT(*) WHERE created_at >= NOW() - INTERVAL 1 DAY",
			IsActive:          true,
			LastCalculated:    time.Now(),
		})
	}

	// System Response Time
	responseTime, err := s.calculateAverageResponseTime()
	if err == nil {
		metrics = append(metrics, AnalyticsMetric{
			ID:                3,
			Name:              "System Response Time",
			Description:       "Average API response time in milliseconds",
			MetricType:        "gauge",
			Category:          "System Performance",
			CurrentValue:      responseTime.Current,
			PreviousValue:     responseTime.Previous,
			TargetValue:       200,
			Unit:              "ms",
			Trend:             s.calculateTrend(responseTime.Previous, responseTime.Current), // Inverted for response time
			ThresholdWarning:  500,
			ThresholdCritical: 1000,
			DataSource:        "api_logs",
			CalculationMethod: "AVG(response_time_ms) WHERE timestamp >= NOW() - INTERVAL 1 HOUR",
			IsActive:          true,
			LastCalculated:    time.Now(),
		})
	}

	// Task Completion Rate
	taskCompletionRate, err := s.calculateTaskCompletionRate()
	if err == nil {
		metrics = append(metrics, AnalyticsMetric{
			ID:                4,
			Name:              "Task Completion Rate",
			Description:       "Percentage of tasks completed on time",
			MetricType:        "percentage",
			Category:          "Productivity",
			CurrentValue:      taskCompletionRate.Current,
			PreviousValue:     taskCompletionRate.Previous,
			TargetValue:       85,
			Unit:              "%",
			Trend:             s.calculateTrend(taskCompletionRate.Current, taskCompletionRate.Previous),
			ThresholdWarning:  70,
			ThresholdCritical: 50,
			DataSource:        "tasks",
			CalculationMethod: "COUNT(completed_tasks) / COUNT(total_tasks) * 100",
			IsActive:          true,
			LastCalculated:    time.Now(),
		})
	}

	return metrics, nil
}

// MetricValue represents a metric with current and previous values
type MetricValue struct {
	Current  float64
	Previous float64
}

// calculateDailyActiveUsers calculates daily active users
func (s *AnalyticsService) calculateDailyActiveUsers() (MetricValue, error) {
	var current, previous int64

	// Current day
	today := time.Now().Truncate(24 * time.Hour)
	yesterday := today.AddDate(0, 0, -1)
	_ = yesterday // Used for previous period calculation if needed

	// Count users who logged in today
	err := s.db.Model(&models.User{}).
		Where("last_login_at >= ? AND last_login_at < ?", today, today.AddDate(0, 0, 1)).
		Count(&current).Error
	if err != nil {
		return MetricValue{}, err
	}

	// Count users who logged in yesterday
	err = s.db.Model(&models.User{}).
		Where("last_login_at >= ? AND last_login_at < ?", yesterday, today).
		Count(&previous).Error
	if err != nil {
		return MetricValue{}, err
	}

	return MetricValue{Current: float64(current), Previous: float64(previous)}, nil
}

// calculateDocumentCreationRate calculates document creation rate
func (s *AnalyticsService) calculateDocumentCreationRate() (MetricValue, error) {
	var current, previous int64

	today := time.Now().Truncate(24 * time.Hour)
	yesterday := today.AddDate(0, 0, -1)

	// Documents created today
	err := s.db.Model(&models.Document{}).
		Where("created_at >= ?", today).
		Count(&current).Error
	if err != nil {
		return MetricValue{}, err
	}

	// Documents created yesterday
	err = s.db.Model(&models.Document{}).
		Where("created_at >= ? AND created_at < ?", yesterday, today).
		Count(&previous).Error
	if err != nil {
		return MetricValue{}, err
	}

	return MetricValue{Current: float64(current), Previous: float64(previous)}, nil
}

// calculateAverageResponseTime calculates average response time (simulated)
func (s *AnalyticsService) calculateAverageResponseTime() (MetricValue, error) {
	// Since we don't have actual API logs, we'll simulate this
	// In a real implementation, you would query actual API log tables

	// Simulate response times based on system load
	var userCount int64
	s.db.Model(&models.User{}).Count(&userCount)

	// Base response time + load factor
	baseResponseTime := 150.0
	loadFactor := float64(userCount) / 1000.0 * 50.0 // 50ms per 1000 users

	current := baseResponseTime + loadFactor
	previous := current + (float64(time.Now().Hour()%3)-1)*20 // Simulate variation

	return MetricValue{Current: current, Previous: previous}, nil
}

// calculateTaskCompletionRate calculates task completion rate
func (s *AnalyticsService) calculateTaskCompletionRate() (MetricValue, error) {
	var totalTasks, completedTasks int64
	var totalTasksPrev, completedTasksPrev int64

	today := time.Now().Truncate(24 * time.Hour)
	_ = today // Used for current period calculation

	// Current period (last 7 days)
	weekAgo := today.AddDate(0, 0, -7)

	err := s.db.Model(&models.Task{}).
		Where("created_at >= ?", weekAgo).
		Count(&totalTasks).Error
	if err != nil {
		return MetricValue{}, err
	}

	err = s.db.Model(&models.Task{}).
		Where("created_at >= ? AND status = ?", weekAgo, models.TaskStatusCompleted).
		Count(&completedTasks).Error
	if err != nil {
		return MetricValue{}, err
	}

	// Previous period (8-14 days ago)
	twoWeeksAgo := today.AddDate(0, 0, -14)

	err = s.db.Model(&models.Task{}).
		Where("created_at >= ? AND created_at < ?", twoWeeksAgo, weekAgo).
		Count(&totalTasksPrev).Error
	if err != nil {
		return MetricValue{}, err
	}

	err = s.db.Model(&models.Task{}).
		Where("created_at >= ? AND created_at < ? AND status = ?", twoWeeksAgo, weekAgo, models.TaskStatusCompleted).
		Count(&completedTasksPrev).Error
	if err != nil {
		return MetricValue{}, err
	}

	var current, previous float64
	if totalTasks > 0 {
		current = float64(completedTasks) / float64(totalTasks) * 100
	}
	if totalTasksPrev > 0 {
		previous = float64(completedTasksPrev) / float64(totalTasksPrev) * 100
	}

	return MetricValue{Current: current, Previous: previous}, nil
}

// calculateTrend determines if a metric is trending up, down, or stable
func (s *AnalyticsService) calculateTrend(current, previous float64) string {
	if current > previous*1.05 { // 5% increase threshold
		return "up"
	} else if current < previous*0.95 { // 5% decrease threshold
		return "down"
	}
	return "stable"
}

// GetSystemDashboard returns a complete system dashboard
func (s *AnalyticsService) GetSystemDashboard() (*AnalyticsDashboard, error) {
	// Get real-time metrics
	metrics, err := s.GetSystemMetrics()
	if err != nil {
		return nil, err
	}

	// Create widgets from metrics
	widgets := make([]DashboardWidget, 0, len(metrics))

	for i, metric := range metrics {
		widget := DashboardWidget{
			ID:    fmt.Sprintf("widget_%d", i+1),
			Type:  "metric",
			Title: metric.Name,
			Data: map[string]interface{}{
				"value":  metric.CurrentValue,
				"change": s.calculateChangePercentage(metric.CurrentValue, metric.PreviousValue),
				"trend":  metric.Trend,
				"unit":   metric.Unit,
			},
			Config: map[string]interface{}{
				"color":  s.getMetricColor(metric.Category),
				"format": s.getMetricFormat(metric.MetricType),
			},
			Position: map[string]interface{}{
				"x":      (i % 3) * 4,
				"y":      (i / 3) * 2,
				"width":  4,
				"height": 2,
			},
		}
		widgets = append(widgets, widget)
	}

	dashboard := &AnalyticsDashboard{
		ID:            1,
		Name:          "System Overview Dashboard",
		Description:   "Real-time system metrics and performance indicators",
		DashboardType: "system",
		Widgets:       widgets,
		Layout:        map[string]int{"columns": 3, "rows": 2},
		IsPublic:      true,
		CreatedBy:     1,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	return dashboard, nil
}

// GetExecutiveDashboard returns executive-level dashboard with high-level KPIs
func (s *AnalyticsService) GetExecutiveDashboard() (*AnalyticsDashboard, error) {
	// Executive metrics focus on strategic KPIs
	executiveMetrics := []AnalyticsMetric{
		{
			ID:                1,
			Name:              "Total Revenue",
			Description:       "Total organizational revenue this quarter",
			MetricType:        "currency",
			Category:          "Financial Performance",
			CurrentValue:      2450000.0,
			PreviousValue:     2280000.0,
			TargetValue:       2500000.0,
			Unit:              "USD",
			Trend:             "up",
			ThresholdWarning:  2000000.0,
			ThresholdCritical: 1800000.0,
			DataSource:        "financial_reports",
			IsActive:          true,
		},
		{
			ID:                2,
			Name:              "Operational Efficiency",
			Description:       "Overall operational efficiency score",
			MetricType:        "percentage",
			Category:          "Operations",
			CurrentValue:      87.5,
			PreviousValue:     84.2,
			TargetValue:       90.0,
			Unit:              "%",
			Trend:             "up",
			ThresholdWarning:  80.0,
			ThresholdCritical: 75.0,
			DataSource:        "operational_metrics",
			IsActive:          true,
		},
		{
			ID:                3,
			Name:              "Compliance Score",
			Description:       "Overall regulatory compliance rating",
			MetricType:        "score",
			Category:          "Compliance",
			CurrentValue:      94.8,
			PreviousValue:     92.1,
			TargetValue:       95.0,
			Unit:              "score",
			Trend:             "up",
			ThresholdWarning:  90.0,
			ThresholdCritical: 85.0,
			DataSource:        "compliance_audits",
			IsActive:          true,
		},
		{
			ID:                4,
			Name:              "Employee Satisfaction",
			Description:       "Average employee satisfaction rating",
			MetricType:        "rating",
			Category:          "Human Resources",
			CurrentValue:      4.2,
			PreviousValue:     4.0,
			TargetValue:       4.5,
			Unit:              "rating",
			Trend:             "up",
			ThresholdWarning:  3.5,
			ThresholdCritical: 3.0,
			DataSource:        "hr_surveys",
			IsActive:          true,
		},
	}

	// Create executive widgets
	widgets := s.createExecutiveWidgets(executiveMetrics)

	return &AnalyticsDashboard{
		ID:            2,
		Name:          "Executive Dashboard",
		Description:   "High-level strategic metrics and KPIs for executive decision making",
		DashboardType: "executive",
		Widgets:       widgets,
		Layout:        map[string]int{"columns": 2, "rows": 2},
		IsPublic:      false,
		CreatedBy:     1,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}, nil
}

// GetOperationalDashboard returns operational dashboard with detailed operational metrics
func (s *AnalyticsService) GetOperationalDashboard() (*AnalyticsDashboard, error) {
	// Get real operational metrics
	docCreationRate, _ := s.calculateDocumentCreationRate()
	taskCompletionRate, _ := s.calculateTaskCompletionRate()
	avgResponseTime, _ := s.calculateAverageResponseTime()

	operationalMetrics := []AnalyticsMetric{
		{
			ID:                1,
			Name:              "Document Processing Rate",
			Description:       "Documents processed per hour",
			MetricType:        "rate",
			Category:          "Document Management",
			CurrentValue:      docCreationRate.Current,
			PreviousValue:     docCreationRate.Previous,
			TargetValue:       100.0,
			Unit:              "docs/hour",
			Trend:             s.calculateTrend(docCreationRate.Current, docCreationRate.Previous),
			ThresholdWarning:  50.0,
			ThresholdCritical: 25.0,
			DataSource:        "document_processing",
			IsActive:          true,
		},
		{
			ID:                2,
			Name:              "Task Completion Rate",
			Description:       "Percentage of tasks completed on time",
			MetricType:        "percentage",
			Category:          "Task Management",
			CurrentValue:      taskCompletionRate.Current,
			PreviousValue:     taskCompletionRate.Previous,
			TargetValue:       95.0,
			Unit:              "%",
			Trend:             s.calculateTrend(taskCompletionRate.Current, taskCompletionRate.Previous),
			ThresholdWarning:  85.0,
			ThresholdCritical: 75.0,
			DataSource:        "task_tracking",
			IsActive:          true,
		},
		{
			ID:                3,
			Name:              "System Response Time",
			Description:       "Average API response time",
			MetricType:        "duration",
			Category:          "System Performance",
			CurrentValue:      avgResponseTime.Current,
			PreviousValue:     avgResponseTime.Previous,
			TargetValue:       200.0,
			Unit:              "ms",
			Trend:             s.calculateTrend(avgResponseTime.Previous, avgResponseTime.Current), // Inverted for response time
			ThresholdWarning:  500.0,
			ThresholdCritical: 1000.0,
			DataSource:        "api_logs",
			IsActive:          true,
		},
		{
			ID:                4,
			Name:              "Error Rate",
			Description:       "System error rate percentage",
			MetricType:        "percentage",
			Category:          "System Health",
			CurrentValue:      2.1,
			PreviousValue:     2.8,
			TargetValue:       1.0,
			Unit:              "%",
			Trend:             "down", // Lower is better for error rate
			ThresholdWarning:  5.0,
			ThresholdCritical: 10.0,
			DataSource:        "error_logs",
			IsActive:          true,
		},
	}

	// Create operational widgets
	widgets := s.createOperationalWidgets(operationalMetrics)

	return &AnalyticsDashboard{
		ID:            3,
		Name:          "Operational Dashboard",
		Description:   "Detailed operational metrics and system performance indicators",
		DashboardType: "operational",
		Widgets:       widgets,
		Layout:        map[string]int{"columns": 2, "rows": 3},
		IsPublic:      true,
		CreatedBy:     1,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}, nil
}

// GetFinancialDashboard returns financial dashboard with budget and cost metrics
func (s *AnalyticsService) GetFinancialDashboard() (*AnalyticsDashboard, error) {
	// Calculate financial metrics from database
	var totalBudget, totalActual float64
	s.db.Model(&models.Finance{}).Select("COALESCE(SUM(budget_amount), 0)").Scan(&totalBudget)
	s.db.Model(&models.Finance{}).Select("COALESCE(SUM(actual_amount), 0)").Scan(&totalActual)

	budgetVariance := ((totalActual - totalBudget) / totalBudget) * 100
	if totalBudget == 0 {
		budgetVariance = 0
	}

	financialMetrics := []AnalyticsMetric{
		{
			ID:                1,
			Name:              "Total Budget",
			Description:       "Total allocated budget for current period",
			MetricType:        "currency",
			Category:          "Budget Management",
			CurrentValue:      totalBudget,
			PreviousValue:     totalBudget * 0.95, // Simulate previous period
			TargetValue:       totalBudget,
			Unit:              "USD",
			Trend:             "stable",
			ThresholdWarning:  totalBudget * 0.9,
			ThresholdCritical: totalBudget * 0.8,
			DataSource:        "finance_records",
			IsActive:          true,
		},
		{
			ID:                2,
			Name:              "Actual Spending",
			Description:       "Total actual spending for current period",
			MetricType:        "currency",
			Category:          "Expense Tracking",
			CurrentValue:      totalActual,
			PreviousValue:     totalActual * 0.92,
			TargetValue:       totalBudget * 0.95,
			Unit:              "USD",
			Trend:             s.calculateTrend(totalActual, totalActual*0.92),
			ThresholdWarning:  totalBudget * 0.9,
			ThresholdCritical: totalBudget,
			DataSource:        "finance_records",
			IsActive:          true,
		},
		{
			ID:                3,
			Name:              "Budget Variance",
			Description:       "Percentage variance from budget",
			MetricType:        "percentage",
			Category:          "Budget Analysis",
			CurrentValue:      budgetVariance,
			PreviousValue:     budgetVariance - 2.5,
			TargetValue:       0.0,
			Unit:              "%",
			Trend:             s.calculateTrend(budgetVariance, budgetVariance-2.5),
			ThresholdWarning:  10.0,
			ThresholdCritical: 20.0,
			DataSource:        "finance_analysis",
			IsActive:          true,
		},
		{
			ID:                4,
			Name:              "Cost per Document",
			Description:       "Average cost per processed document",
			MetricType:        "currency",
			Category:          "Cost Efficiency",
			CurrentValue:      125.50,
			PreviousValue:     132.75,
			TargetValue:       120.00,
			Unit:              "USD",
			Trend:             "down", // Lower is better for cost
			ThresholdWarning:  150.00,
			ThresholdCritical: 175.00,
			DataSource:        "cost_analysis",
			IsActive:          true,
		},
	}

	// Create financial widgets
	widgets := s.createFinancialWidgets(financialMetrics)

	return &AnalyticsDashboard{
		ID:            4,
		Name:          "Financial Dashboard",
		Description:   "Budget tracking, expense analysis, and financial performance metrics",
		DashboardType: "financial",
		Widgets:       widgets,
		Layout:        map[string]int{"columns": 2, "rows": 2},
		IsPublic:      false,
		CreatedBy:     1,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}, nil
}

// GetComplianceDashboard returns compliance dashboard with regulatory metrics
func (s *AnalyticsService) GetComplianceDashboard() (*AnalyticsDashboard, error) {
	// Calculate compliance metrics
	var totalRegulations, compliantRegulations int64
	s.db.Model(&models.LawsAndRules{}).Count(&totalRegulations)
	s.db.Model(&models.LawsAndRules{}).Where("status = ?", "compliant").Count(&compliantRegulations)

	complianceRate := float64(0)
	if totalRegulations > 0 {
		complianceRate = (float64(compliantRegulations) / float64(totalRegulations)) * 100
	}

	complianceMetrics := []AnalyticsMetric{
		{
			ID:                1,
			Name:              "Compliance Rate",
			Description:       "Percentage of regulations in compliance",
			MetricType:        "percentage",
			Category:          "Regulatory Compliance",
			CurrentValue:      complianceRate,
			PreviousValue:     complianceRate - 2.1,
			TargetValue:       100.0,
			Unit:              "%",
			Trend:             "up",
			ThresholdWarning:  90.0,
			ThresholdCritical: 85.0,
			DataSource:        "compliance_tracking",
			IsActive:          true,
		},
		{
			ID:                2,
			Name:              "Audit Score",
			Description:       "Latest audit compliance score",
			MetricType:        "score",
			Category:          "Audit Management",
			CurrentValue:      92.5,
			PreviousValue:     89.2,
			TargetValue:       95.0,
			Unit:              "score",
			Trend:             "up",
			ThresholdWarning:  85.0,
			ThresholdCritical: 80.0,
			DataSource:        "audit_reports",
			IsActive:          true,
		},
		{
			ID:                3,
			Name:              "Risk Assessment",
			Description:       "Current organizational risk level",
			MetricType:        "level",
			Category:          "Risk Management",
			CurrentValue:      2.3, // Scale of 1-5, lower is better
			PreviousValue:     2.7,
			TargetValue:       2.0,
			Unit:              "level",
			Trend:             "down", // Lower is better for risk
			ThresholdWarning:  3.0,
			ThresholdCritical: 4.0,
			DataSource:        "risk_assessments",
			IsActive:          true,
		},
		{
			ID:                4,
			Name:              "Policy Updates",
			Description:       "Number of policy updates this month",
			MetricType:        "counter",
			Category:          "Policy Management",
			CurrentValue:      12.0,
			PreviousValue:     8.0,
			TargetValue:       15.0,
			Unit:              "updates",
			Trend:             "up",
			ThresholdWarning:  5.0,
			ThresholdCritical: 3.0,
			DataSource:        "policy_tracking",
			IsActive:          true,
		},
	}

	// Create compliance widgets
	widgets := s.createComplianceWidgets(complianceMetrics)

	return &AnalyticsDashboard{
		ID:            5,
		Name:          "Compliance Dashboard",
		Description:   "Regulatory compliance tracking, audit management, and risk assessment",
		DashboardType: "compliance",
		Widgets:       widgets,
		Layout:        map[string]int{"columns": 2, "rows": 2},
		IsPublic:      false,
		CreatedBy:     1,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}, nil
}

// GetDashboardByType returns a dashboard based on the specified type
func (s *AnalyticsService) GetDashboardByType(dashboardType string) (*AnalyticsDashboard, error) {
	switch dashboardType {
	case "system":
		return s.GetSystemDashboard()
	case "executive":
		return s.GetExecutiveDashboard()
	case "operational":
		return s.GetOperationalDashboard()
	case "financial":
		return s.GetFinancialDashboard()
	case "compliance":
		return s.GetComplianceDashboard()
	default:
		return s.GetSystemDashboard() // Default to system dashboard
	}
}

// GetAllDashboardTypes returns all available dashboard types
func (s *AnalyticsService) GetAllDashboardTypes() []map[string]interface{} {
	return []map[string]interface{}{
		{
			"id":          "system",
			"name":        "System Overview",
			"description": "Real-time system metrics and performance indicators",
			"category":    "technical",
			"icon":        "chart-bar",
			"access":      "public",
		},
		{
			"id":          "executive",
			"name":        "Executive Dashboard",
			"description": "High-level strategic metrics and KPIs",
			"category":    "strategic",
			"icon":        "presentation-chart-line",
			"access":      "restricted",
		},
		{
			"id":          "operational",
			"name":        "Operational Dashboard",
			"description": "Detailed operational metrics and workflows",
			"category":    "operational",
			"icon":        "cog",
			"access":      "public",
		},
		{
			"id":          "financial",
			"name":        "Financial Dashboard",
			"description": "Budget tracking and financial performance",
			"category":    "financial",
			"icon":        "currency-dollar",
			"access":      "restricted",
		},
		{
			"id":          "compliance",
			"name":        "Compliance Dashboard",
			"description": "Regulatory compliance and risk management",
			"category":    "compliance",
			"icon":        "shield-check",
			"access":      "restricted",
		},
	}
}

// Widget creation methods for different dashboard types

// createExecutiveWidgets creates widgets for executive dashboard
func (s *AnalyticsService) createExecutiveWidgets(metrics []AnalyticsMetric) []DashboardWidget {
	widgets := make([]DashboardWidget, 0, len(metrics))

	for i, metric := range metrics {
		widget := DashboardWidget{
			ID:    fmt.Sprintf("exec_widget_%d", i+1),
			Type:  "kpi_card",
			Title: metric.Name,
			Data: map[string]interface{}{
				"value":       metric.CurrentValue,
				"change":      s.calculateChangePercentage(metric.CurrentValue, metric.PreviousValue),
				"trend":       metric.Trend,
				"unit":        metric.Unit,
				"target":      metric.TargetValue,
				"description": metric.Description,
			},
			Config: map[string]interface{}{
				"color":      s.getExecutiveColor(metric.Category),
				"format":     s.getMetricFormat(metric.MetricType),
				"size":       "large",
				"showTrend":  true,
				"showTarget": true,
			},
			Position: map[string]interface{}{
				"x":      (i % 2) * 6,
				"y":      (i / 2) * 3,
				"width":  6,
				"height": 3,
			},
		}
		widgets = append(widgets, widget)
	}

	return widgets
}

// createOperationalWidgets creates widgets for operational dashboard
func (s *AnalyticsService) createOperationalWidgets(metrics []AnalyticsMetric) []DashboardWidget {
	widgets := make([]DashboardWidget, 0, len(metrics)+2) // +2 for additional charts

	// Create metric widgets
	for i, metric := range metrics {
		widget := DashboardWidget{
			ID:    fmt.Sprintf("ops_widget_%d", i+1),
			Type:  "metric_chart",
			Title: metric.Name,
			Data: map[string]interface{}{
				"value":    metric.CurrentValue,
				"change":   s.calculateChangePercentage(metric.CurrentValue, metric.PreviousValue),
				"trend":    metric.Trend,
				"unit":     metric.Unit,
				"warning":  metric.ThresholdWarning,
				"critical": metric.ThresholdCritical,
			},
			Config: map[string]interface{}{
				"color":      s.getOperationalColor(metric.Category),
				"format":     s.getMetricFormat(metric.MetricType),
				"chartType":  "line",
				"showAlerts": true,
				"realTime":   true,
			},
			Position: map[string]interface{}{
				"x":      (i % 2) * 6,
				"y":      (i / 2) * 2,
				"width":  6,
				"height": 2,
			},
		}
		widgets = append(widgets, widget)
	}

	// Add system health widget
	healthWidget := DashboardWidget{
		ID:    "ops_health_widget",
		Type:  "health_status",
		Title: "System Health Overview",
		Data: map[string]interface{}{
			"status":    "healthy",
			"uptime":    "99.9%",
			"services":  []string{"API", "Database", "Cache", "Queue"},
			"alerts":    0,
			"lastCheck": time.Now().Format("15:04:05"),
		},
		Config: map[string]interface{}{
			"color":       "#10B981",
			"showUptime":  true,
			"autoRefresh": true,
		},
		Position: map[string]interface{}{
			"x":      0,
			"y":      4,
			"width":  12,
			"height": 2,
		},
	}
	widgets = append(widgets, healthWidget)

	return widgets
}

// createFinancialWidgets creates widgets for financial dashboard
func (s *AnalyticsService) createFinancialWidgets(metrics []AnalyticsMetric) []DashboardWidget {
	widgets := make([]DashboardWidget, 0, len(metrics)+1)

	// Create financial metric widgets
	for i, metric := range metrics {
		widget := DashboardWidget{
			ID:    fmt.Sprintf("fin_widget_%d", i+1),
			Type:  "financial_card",
			Title: metric.Name,
			Data: map[string]interface{}{
				"value":    metric.CurrentValue,
				"change":   s.calculateChangePercentage(metric.CurrentValue, metric.PreviousValue),
				"trend":    metric.Trend,
				"unit":     metric.Unit,
				"target":   metric.TargetValue,
				"variance": metric.CurrentValue - metric.TargetValue,
			},
			Config: map[string]interface{}{
				"color":        s.getFinancialColor(metric.Category),
				"format":       s.getMetricFormat(metric.MetricType),
				"showVariance": true,
				"currency":     "USD",
			},
			Position: map[string]interface{}{
				"x":      (i % 2) * 6,
				"y":      (i / 2) * 3,
				"width":  6,
				"height": 3,
			},
		}
		widgets = append(widgets, widget)
	}

	// Add budget breakdown chart
	budgetWidget := DashboardWidget{
		ID:    "fin_budget_chart",
		Type:  "budget_breakdown",
		Title: "Budget Allocation",
		Data: map[string]interface{}{
			"categories": []map[string]interface{}{
				{"name": "Operations", "budget": 450000, "actual": 425000},
				{"name": "Technology", "budget": 300000, "actual": 315000},
				{"name": "Personnel", "budget": 800000, "actual": 785000},
				{"name": "Infrastructure", "budget": 200000, "actual": 195000},
			},
		},
		Config: map[string]interface{}{
			"chartType":  "donut",
			"showLegend": true,
			"currency":   "USD",
		},
		Position: map[string]interface{}{
			"x":      0,
			"y":      6,
			"width":  12,
			"height": 4,
		},
	}
	widgets = append(widgets, budgetWidget)

	return widgets
}

// createComplianceWidgets creates widgets for compliance dashboard
func (s *AnalyticsService) createComplianceWidgets(metrics []AnalyticsMetric) []DashboardWidget {
	widgets := make([]DashboardWidget, 0, len(metrics)+1)

	// Create compliance metric widgets
	for i, metric := range metrics {
		widget := DashboardWidget{
			ID:    fmt.Sprintf("comp_widget_%d", i+1),
			Type:  "compliance_gauge",
			Title: metric.Name,
			Data: map[string]interface{}{
				"value":    metric.CurrentValue,
				"change":   s.calculateChangePercentage(metric.CurrentValue, metric.PreviousValue),
				"trend":    metric.Trend,
				"unit":     metric.Unit,
				"target":   metric.TargetValue,
				"warning":  metric.ThresholdWarning,
				"critical": metric.ThresholdCritical,
			},
			Config: map[string]interface{}{
				"color":      s.getComplianceColor(metric.Category),
				"format":     s.getMetricFormat(metric.MetricType),
				"gaugeType":  "arc",
				"showTarget": true,
				"showAlerts": true,
			},
			Position: map[string]interface{}{
				"x":      (i % 2) * 6,
				"y":      (i / 2) * 3,
				"width":  6,
				"height": 3,
			},
		}
		widgets = append(widgets, widget)
	}

	// Add compliance timeline widget
	timelineWidget := DashboardWidget{
		ID:    "comp_timeline",
		Type:  "compliance_timeline",
		Title: "Compliance Timeline",
		Data: map[string]interface{}{
			"events": []map[string]interface{}{
				{"date": "2024-01-15", "event": "SOX Audit Completed", "status": "passed"},
				{"date": "2024-01-20", "event": "GDPR Review", "status": "in_progress"},
				{"date": "2024-02-01", "event": "Policy Update Due", "status": "pending"},
				{"date": "2024-02-15", "event": "Risk Assessment", "status": "scheduled"},
			},
		},
		Config: map[string]interface{}{
			"showStatus": true,
			"autoUpdate": true,
		},
		Position: map[string]interface{}{
			"x":      0,
			"y":      6,
			"width":  12,
			"height": 3,
		},
	}
	widgets = append(widgets, timelineWidget)

	return widgets
}

// Color helper methods for different dashboard types

// getExecutiveColor returns colors for executive dashboard categories
func (s *AnalyticsService) getExecutiveColor(category string) string {
	colors := map[string]string{
		"Financial Performance": "#059669", // Green
		"Operations":            "#DC2626", // Red
		"Compliance":            "#7C3AED", // Purple
		"Human Resources":       "#EA580C", // Orange
		"Strategic":             "#1D4ED8", // Blue
	}

	if color, exists := colors[category]; exists {
		return color
	}
	return "#6B7280" // Default gray
}

// getOperationalColor returns colors for operational dashboard categories
func (s *AnalyticsService) getOperationalColor(category string) string {
	colors := map[string]string{
		"Document Management": "#10B981", // Emerald
		"Task Management":     "#F59E0B", // Amber
		"System Performance":  "#3B82F6", // Blue
		"System Health":       "#EF4444", // Red
		"Workflow":            "#8B5CF6", // Violet
	}

	if color, exists := colors[category]; exists {
		return color
	}
	return "#6B7280" // Default gray
}

// getFinancialColor returns colors for financial dashboard categories
func (s *AnalyticsService) getFinancialColor(category string) string {
	colors := map[string]string{
		"Budget Management": "#059669", // Green
		"Expense Tracking":  "#DC2626", // Red
		"Budget Analysis":   "#7C3AED", // Purple
		"Cost Efficiency":   "#EA580C", // Orange
		"Revenue":           "#1D4ED8", // Blue
	}

	if color, exists := colors[category]; exists {
		return color
	}
	return "#6B7280" // Default gray
}

// getComplianceColor returns colors for compliance dashboard categories
func (s *AnalyticsService) getComplianceColor(category string) string {
	colors := map[string]string{
		"Regulatory Compliance": "#059669", // Green
		"Audit Management":      "#7C3AED", // Purple
		"Risk Management":       "#DC2626", // Red
		"Policy Management":     "#1D4ED8", // Blue
		"Security":              "#EA580C", // Orange
	}

	if color, exists := colors[category]; exists {
		return color
	}
	return "#6B7280" // Default gray
}

// calculateChangePercentage calculates percentage change between two values
func (s *AnalyticsService) calculateChangePercentage(current, previous float64) string {
	if previous == 0 {
		return "+0.0%"
	}

	change := ((current - previous) / previous) * 100
	if change >= 0 {
		return fmt.Sprintf("+%.1f%%", change)
	}
	return fmt.Sprintf("%.1f%%", change)
}

// getMetricColor returns appropriate color for metric category
func (s *AnalyticsService) getMetricColor(category string) string {
	colors := map[string]string{
		"User Engagement":    "blue",
		"Content Management": "green",
		"System Performance": "purple",
		"Productivity":       "orange",
	}

	if color, exists := colors[category]; exists {
		return color
	}
	return "gray"
}

// getMetricFormat returns appropriate format for metric type
func (s *AnalyticsService) getMetricFormat(metricType string) string {
	formats := map[string]string{
		"gauge":      "number",
		"counter":    "number",
		"percentage": "percentage",
	}

	if format, exists := formats[metricType]; exists {
		return format
	}
	return "number"
}
