-- Finance System Migration
-- This migration creates the finances table for budget allocations linked to documents and regulations
-- and performance tracking for budget calculations

CREATE TABLE IF NOT EXISTS finances (
    id SERIAL PRIMARY KEY,
    amount NUMERIC(18,2) NOT NULL,
    year INTEGER NOT NULL,
    description TEXT,
    document_id INTEGER REFERENCES documents(id) ON DELETE SET NULL,
    regulation_id INTEGER REFERENCES laws_and_rules(id) ON DELETE SET NULL,
    budget_type VARCHAR(20) DEFAULT 'original' CHECK (budget_type IN ('original', 'actual')),
    performance_percentage NUMERIC(5,2) DEFAULT 100.00,
    is_auto_calculated BOOLEAN DEFAULT FALSE,
    source_finance_id INTEGER REFERENCES finances(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Performance tracking table
CREATE TABLE IF NOT EXISTS finance_performance (
    id SERIAL PRIMARY KEY,
    document_id INTEGER REFERENCES documents(id) ON DELETE CASCADE,
    regulation_id INTEGER REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    performance_percentage NUMERIC(5,2) NOT NULL DEFAULT 100.00,
    performance_notes TEXT,
    evaluation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    evaluated_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT check_document_or_regulation CHECK (
        (document_id IS NOT NULL AND regulation_id IS NULL) OR
        (document_id IS NULL AND regulation_id IS NOT NULL)
    )
);

-- Budget categories for better organization
CREATE TABLE IF NOT EXISTS finance_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Link finances to categories
ALTER TABLE finances ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES finance_categories(id) ON DELETE SET NULL;

CREATE INDEX IF NOT EXISTS idx_finances_year ON finances(year);
CREATE INDEX IF NOT EXISTS idx_finances_document_id ON finances(document_id);
CREATE INDEX IF NOT EXISTS idx_finances_regulation_id ON finances(regulation_id);
CREATE INDEX IF NOT EXISTS idx_finances_budget_type ON finances(budget_type);
CREATE INDEX IF NOT EXISTS idx_finances_category_id ON finances(category_id);
CREATE INDEX IF NOT EXISTS idx_finances_created_at ON finances(created_at);

CREATE INDEX IF NOT EXISTS idx_finance_performance_document_id ON finance_performance(document_id);
CREATE INDEX IF NOT EXISTS idx_finance_performance_regulation_id ON finance_performance(regulation_id);
CREATE INDEX IF NOT EXISTS idx_finance_performance_year ON finance_performance(year);

-- Trigger to update updated_at on row update
CREATE TRIGGER update_finances_updated_at BEFORE UPDATE ON finances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_finance_performance_updated_at BEFORE UPDATE ON finance_performance FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default finance categories
INSERT INTO finance_categories (name, description, color) VALUES
('Operations', 'Operational expenses and budget', '#3B82F6'),
('Compliance', 'Compliance and regulatory costs', '#10B981'),
('Research', 'Research and development budget', '#8B5CF6'),
('Infrastructure', 'Infrastructure and technology costs', '#F59E0B'),
('Personnel', 'Personnel and staffing costs', '#EF4444'),
('Legal', 'Legal and administrative costs', '#6B7280')
ON CONFLICT DO NOTHING;