'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../services/enterpriseApi';
import { BudgetPlan } from '../../../../types/enterprise';

const BudgetViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const budgetId = parseInt(params.id as string);
  
  const [budget, setBudget] = useState<BudgetPlan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (budgetId) {
      fetchBudget();
    }
  }, [budgetId]);

  const fetchBudget = async () => {
    try {
      setLoading(true);
      const response = await financialApi.getBudget(budgetId);
      setBudget(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch budget');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this budget?')) return;
    
    try {
      await financialApi.deleteBudget(budgetId);
      router.push('/enterprise/financial/budgets');
    } catch (err: any) {
      setError(err.message || 'Failed to delete budget');
    }
  };

  const handleApprove = async () => {
    if (!confirm('Are you sure you want to approve this budget?')) return;
    
    try {
      await financialApi.approveBudget(budgetId);
      await fetchBudget(); // Refresh the budget
    } catch (err: any) {
      setError(err.message || 'Failed to approve budget');
    }
  };

  if (loading) return <div className="p-6">Loading budget...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!budget) return <div className="p-6">Budget not found</div>;

  const utilizationPercentage = budget.planned_amount > 0 ? (budget.actual_amount / budget.planned_amount) * 100 : 0;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Budget Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/financial/budgets')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Budgets
          </button>
          {(budget.status === 'draft' || budget.status === 'rejected') && (
            <button
              onClick={() => router.push(`/enterprise/financial/budgets/${budgetId}/edit`)}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Edit Budget
            </button>
          )}
          {budget.status === 'submitted' && (
            <button
              onClick={handleApprove}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
            >
              Approve Budget
            </button>
          )}
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Budget
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{budget.budget_name}</h2>
              <p className="text-sm text-gray-600">Budget Code: {budget.budget_code}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                budget.status === 'approved' 
                  ? 'bg-green-100 text-green-800'
                  : budget.status === 'submitted'
                  ? 'bg-blue-100 text-blue-800'
                  : budget.status === 'rejected'
                  ? 'bg-red-100 text-red-800'
                  : budget.status === 'active'
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {budget.status}
              </span>
            </div>
          </div>
        </div>

        {/* Budget Summary */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Budget Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                ${budget.planned_amount.toLocaleString()}
              </div>
              <div className="text-sm text-blue-600">Planned Amount</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                ${budget.revised_amount?.toLocaleString() || '0'}
              </div>
              <div className="text-sm text-green-600">Revised Amount</div>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                ${budget.actual_amount.toLocaleString()}
              </div>
              <div className="text-sm text-red-600">Actual Amount</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                ${budget.committed_amount.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Committed Amount</div>
            </div>
          </div>

          {/* Utilization Bar */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Budget Utilization</span>
              <span>{utilizationPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  utilizationPercentage > 90 ? 'bg-red-500' : 
                  utilizationPercentage > 75 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Budget Information */}
        <div className="px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Budget Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Fiscal Year</label>
              <p className="mt-1 text-sm text-gray-900">{budget.fiscal_year}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Budget Type</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{budget.budget_type}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Currency</label>
              <p className="mt-1 text-sm text-gray-900">{budget.currency_code}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Budget Period Start</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(budget.budget_period_start).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Budget Period End</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(budget.budget_period_end).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Account</label>
              <p className="mt-1 text-sm text-gray-900">
                {budget.account?.account_code} - {budget.account?.account_name}
              </p>
            </div>

            {budget.department && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Department</label>
                <p className="mt-1 text-sm text-gray-900">{budget.department}</p>
              </div>
            )}

            {budget.cost_center && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Cost Center</label>
                <p className="mt-1 text-sm text-gray-900">{budget.cost_center}</p>
              </div>
            )}

            {budget.approval_workflow && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Approval Workflow</label>
                <p className="mt-1 text-sm text-gray-900">{budget.approval_workflow}</p>
              </div>
            )}
          </div>

          {budget.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <p className="mt-1 text-sm text-gray-900">{budget.description}</p>
            </div>
          )}
        </div>

        {/* Approval Information */}
        {(budget.approved_by || budget.approved_at) && (
          <div className="px-6 py-4 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Approval Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {budget.approved_by && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Approved By</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {budget.approved_by.first_name} {budget.approved_by.last_name}
                  </p>
                </div>
              )}
              
              {budget.approved_at && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Approved At</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {new Date(budget.approved_at).toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(budget.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(budget.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BudgetViewPage;
