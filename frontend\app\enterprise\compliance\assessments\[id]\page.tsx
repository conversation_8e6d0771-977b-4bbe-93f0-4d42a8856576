'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { complianceApi } from '../../../../services/enterpriseApi';
import { ComplianceAssessment } from '../../../../types/enterprise';

const ComplianceAssessmentViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const assessmentId = parseInt(params.id as string);
  
  const [assessment, setAssessment] = useState<ComplianceAssessment | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (assessmentId) {
      fetchAssessment();
    }
  }, [assessmentId]);

  const fetchAssessment = async () => {
    try {
      setLoading(true);
      const response = await complianceApi.getAssessment(assessmentId);
      setAssessment(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch compliance assessment');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this compliance assessment?')) return;
    
    try {
      await complianceApi.deleteAssessment(assessmentId);
      router.push('/enterprise/compliance/assessments');
    } catch (err: any) {
      setError(err.message || 'Failed to delete compliance assessment');
    }
  };

  const handleStartAssessment = async () => {
    try {
      await complianceApi.startAssessment(assessmentId);
      await fetchAssessment(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Failed to start assessment');
    }
  };

  const handleCompleteAssessment = async () => {
    if (!assessment) return;
    
    const overallScore = prompt('Enter overall score (0-100):');
    const complianceLevel = prompt('Enter compliance level:');
    
    if (overallScore && complianceLevel) {
      try {
        await complianceApi.completeAssessment(assessmentId, {
          overall_score: parseFloat(overallScore),
          compliance_level: complianceLevel
        });
        await fetchAssessment(); // Refresh data
      } catch (err: any) {
        setError(err.message || 'Failed to complete assessment');
      }
    }
  };

  if (loading) return <div className="p-6">Loading compliance assessment...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!assessment) return <div className="p-6">Compliance assessment not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Compliance Assessment Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/compliance/assessments')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Assessments
          </button>
          <button
            onClick={() => router.push(`/enterprise/compliance/assessments/${assessmentId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Assessment
          </button>
          {assessment.status === 'planning' && (
            <button
              onClick={handleStartAssessment}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Start Assessment
            </button>
          )}
          {assessment.status === 'in_progress' && (
            <button
              onClick={handleCompleteAssessment}
              className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700"
            >
              Complete Assessment
            </button>
          )}
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Assessment
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{assessment.title}</h2>
              <p className="text-sm text-gray-600">Code: {assessment.assessment_code}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                assessment.status === 'completed' 
                  ? 'bg-green-100 text-green-800'
                  : assessment.status === 'in_progress'
                  ? 'bg-blue-100 text-blue-800'
                  : assessment.status === 'planning'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {assessment.status.replace('_', ' ')}
              </span>
            </div>
          </div>
        </div>

        {/* Assessment Overview */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Assessment Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {assessment.overall_score !== undefined ? `${assessment.overall_score}%` : 'N/A'}
              </div>
              <div className="text-sm text-blue-600">Overall Score</div>
            </div>
            
            <div className={`p-4 rounded-lg ${
              assessment.priority === 'critical' ? 'bg-red-50' :
              assessment.priority === 'high' ? 'bg-orange-50' :
              assessment.priority === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              <div className={`text-2xl font-bold ${
                assessment.priority === 'critical' ? 'text-red-600' :
                assessment.priority === 'high' ? 'text-orange-600' :
                assessment.priority === 'medium' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {assessment.priority.toUpperCase()}
              </div>
              <div className="text-sm text-gray-600">Priority</div>
            </div>

            <div className={`p-4 rounded-lg ${
              assessment.risk_level === 'critical' ? 'bg-red-50' :
              assessment.risk_level === 'high' ? 'bg-orange-50' :
              assessment.risk_level === 'medium' ? 'bg-yellow-50' : 'bg-green-50'
            }`}>
              <div className={`text-2xl font-bold ${
                assessment.risk_level === 'critical' ? 'text-red-600' :
                assessment.risk_level === 'high' ? 'text-orange-600' :
                assessment.risk_level === 'medium' ? 'text-yellow-600' : 'text-green-600'
              }`}>
                {assessment.risk_level.toUpperCase()}
              </div>
              <div className="text-sm text-gray-600">Risk Level</div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {assessment.findings_count}
              </div>
              <div className="text-sm text-gray-600">Total Findings</div>
            </div>
          </div>
        </div>

        {/* Findings Breakdown */}
        {assessment.findings_count > 0 && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Findings Breakdown</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{assessment.critical_findings}</div>
                <div className="text-sm text-red-600">Critical</div>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{assessment.high_findings}</div>
                <div className="text-sm text-orange-600">High</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{assessment.medium_findings}</div>
                <div className="text-sm text-yellow-600">Medium</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{assessment.low_findings}</div>
                <div className="text-sm text-green-600">Low</div>
              </div>
            </div>
          </div>
        )}

        {/* Assessment Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Assessment Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Framework</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.framework}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Assessment Type</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{assessment.assessment_type}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Compliance Level</label>
              <p className="mt-1 text-sm text-gray-900">{assessment.compliance_level || 'N/A'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Cost</label>
              <p className="mt-1 text-sm text-gray-900">
                {assessment.currency_code} {assessment.cost.toLocaleString()}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Follow-up Required</label>
              <p className="mt-1 text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  assessment.follow_up_required 
                    ? 'bg-yellow-100 text-yellow-800' 
                    : 'bg-green-100 text-green-800'
                }`}>
                  {assessment.follow_up_required ? 'Yes' : 'No'}
                </span>
              </p>
            </div>
          </div>

          {assessment.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {assessment.description}
              </div>
            </div>
          )}
        </div>

        {/* Assessment Details */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Assessment Details</h3>
          
          {assessment.scope && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Scope</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {assessment.scope}
              </div>
            </div>
          )}

          {assessment.methodology && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Methodology</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {assessment.methodology}
              </div>
            </div>
          )}

          {assessment.criteria && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Criteria</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {assessment.criteria}
              </div>
            </div>
          )}

          {assessment.recommendations && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Recommendations</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {assessment.recommendations}
              </div>
            </div>
          )}

          {assessment.action_plan && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Action Plan</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {assessment.action_plan}
              </div>
            </div>
          )}
        </div>

        {/* Important Dates */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Important Dates</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Planned Start</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(assessment.planned_start_date).toLocaleDateString()}
              </p>
            </div>
            
            {assessment.planned_end_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Planned End</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(assessment.planned_end_date).toLocaleDateString()}
                </p>
              </div>
            )}
            
            {assessment.actual_start_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Actual Start</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(assessment.actual_start_date).toLocaleDateString()}
                </p>
              </div>
            )}
            
            {assessment.actual_end_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Actual End</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(assessment.actual_end_date).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(assessment.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(assessment.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceAssessmentViewPage;
