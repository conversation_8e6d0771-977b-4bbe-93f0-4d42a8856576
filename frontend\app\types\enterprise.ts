// Base User interface for relationships
export interface User {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  username?: string;
}

// Enterprise Content Management Types
export interface ContentRepository {
  id: number;
  created_at: string;
  updated_at: string;
  repository_code: string;
  repository_name: string;
  name: string;                    // Alias for repository_name
  description?: string;
  type: string;
  repository_type: string;         // Alias for type
  storage_type: string;
  storage_path?: string;
  storage_config?: string;
  max_size: number;
  current_size: number;
  total_size: number;              // Alias for current_size
  max_files: number;
  current_files: number;
  file_count: number;              // Alias for current_files
  classification: string;
  encryption_key?: string;
  access_policy?: string;
  is_public: boolean;
  versioning_enabled: boolean;
  max_versions: number;
  backup_enabled: boolean;
  backup_schedule: string;
  retention_policy_id?: number;
  compliance_level: string;
  is_active: boolean;
  last_accessed?: string;
  access_count: number;
  metadata?: string;
}

export interface ContentVersion {
  id: number;
  created_at: string;
  updated_at: string;
  document_id: number;
  version_number: string;
  title?: string;
  content?: string;
  content_hash?: string;
  file_size?: number;
  mime_type?: string;
  change_type?: string;
  change_log?: string;
  author_id: number;
  approval_status: string;
  approved_by_id?: number;
  approved_at?: string;
  rejection_reason?: string;
  is_active: boolean;
  published_at?: string;
  deprecated_at?: string;
  metadata?: string;
}

export interface ContentWorkflow {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  type: string;
  steps?: string;
  rules?: string;
  conditions?: string;
  is_parallel: boolean;
  require_all: boolean;
  timeout_hours: number;
  escalation_hours: number;
  trigger_events?: string;
  auto_start: boolean;
  is_active: boolean;
  last_executed?: string;
  execution_count: number;
  success_rate: number;
  metadata?: string;
}

export interface ContentWorkflowInstance {
  id: number;
  created_at: string;
  updated_at: string;
  workflow_id: number;
  document_id: number;
  instance_id: string;
  status: string;
  current_step: number;
  total_steps?: number;
  started_at: string;
  completed_at?: string;
  started_by_id: number;
  step_results?: string;
  error_log?: string;
  timeout_at?: string;
  escalation_at?: string;
  is_escalated: boolean;
  metadata?: string;
}

export interface ContentCollaboration {
  id: number;
  created_at: string;
  updated_at: string;
  document_id: number;
  user_id: number;
  type: string;
  permission: string;
  started_at: string;
  ended_at?: string;
  is_active: boolean;
  session_id?: string;
  last_action?: string;
  action_count: number;
  metadata?: string;
}

// Enterprise Financial Management Types
export interface ChartOfAccounts {
  id: number;
  created_at: string;
  updated_at: string;
  account_code: string;
  account_name: string;
  account_type: string;
  description?: string;
  parent_account_id?: number;
  level: number;
  is_active: boolean;
  is_control_account: boolean;
  allow_posting: boolean;
  normal_balance: string;
  current_balance: number;
  opening_balance: number;
  currency_code: string;
  tax_code?: string;
  reporting_code?: string;
  gl_class?: string;
  metadata?: string;
}

export interface GeneralLedger {
  id: number;
  created_at: string;
  updated_at: string;
  entry_number: string;
  transaction_date: string;
  posting_date: string;
  account_id: number;
  account?: ChartOfAccounts;
  transaction_type: string;
  debit_amount: number;
  credit_amount: number;
  reference_type?: string;
  reference_id?: string;
  reference_number?: string;
  description?: string;
  document_id?: number;
  source_document_id?: number;
  fiscal_year?: number;
  fiscal_period?: number;
  source_document_type?: string;
  reconciliation_status?: string;
  created_by_id: number;
  created_by?: User;
  approved_by_id?: number;
  approved_by?: User;
  approved_at?: string;
  status: string;
  is_reconciled: boolean;
  reconciled_at?: string;
  currency_code: string;
  exchange_rate: number;
  base_currency_amount?: number;
  metadata?: string;
}

export interface BudgetPlan {
  id: number;
  created_at: string;
  updated_at: string;
  budget_code: string;
  budget_name: string;
  description?: string;
  fiscal_year: number;
  start_date: string;
  end_date: string;
  budget_period_start: string; // Alias for start_date
  budget_period_end: string;   // Alias for end_date
  budget_type: string;
  budget_category?: string;
  planned_amount: number;
  total_amount: number;        // Alias for planned_amount
  revised_amount?: number;
  allocated_amount?: number;   // Additional field
  actual_amount: number;
  spent_amount: number;        // Alias for actual_amount
  committed_amount: number;
  remaining_amount: number;    // Calculated field
  variance_amount: number;
  variance_percent: number;
  currency_code: string;       // Additional field
  account_id: number;
  account?: ChartOfAccounts;
  department_id?: number;
  department?: string;         // Additional field for display
  cost_center_id?: number;
  cost_center?: string;        // Additional field for display
  project_id?: number;
  status: string;
  submitted_at?: string;
  approved_by_id?: number;
  approved_by?: User;
  approved_at?: string;
  approval_workflow?: string;  // Additional field
  alert_threshold: number;
  is_alert_enabled: boolean;
  last_alert_sent?: string;
  metadata?: string;
}

export interface CostCenter {
  id: number;
  created_at: string;
  updated_at: string;
  cost_center_code: string;
  cost_center_name: string;
  description?: string;
  department?: string;
  location?: string;
  parent_cost_center_id?: number;
  parent_cost_center?: CostCenter;
  level: number;
  manager_id?: number;
  manager?: Employee;
  budget_amount: number;
  actual_amount: number;
  committed_amount: number;
  currency_code: string;
  allocation_method: string;
  cost_allocation_method: string;
  allocation_base?: string;
  allocation_rate: number;
  responsibility_center_type?: string;
  gl_account_range_start?: string;
  gl_account_range_end?: string;
  is_active: boolean;
  last_reviewed?: string;
  metadata?: string;
}

export interface FinancialReport {
  id: number;
  created_at: string;
  updated_at: string;
  report_code: string;
  report_name: string;
  report_type: string;
  description?: string;
  period_type: string;
  period_start: string;
  period_end: string;
  start_date: string;
  end_date: string;
  fiscal_year?: number;
  fiscal_period?: number;
  currency_code?: string;
  format: string;
  generated_at?: string;
  generated_by_id?: number;
  generated_by?: User;
  report_data?: string;
  report_format: string;
  file_path?: string;
  file_size?: number;
  file_hash?: string;
  status: string;
  reviewed_by_id?: number;
  reviewed_by?: User;
  reviewed_at?: string;
  distribution_list?: string;
  published_at?: string;
  template_id?: number;
  parameters?: string;
  filters?: string;
  sort_order?: string;
  grouping?: string;
  metadata?: string;
}

// Enterprise Compliance & Risk Management Types
export interface ComplianceRequirement {
  id: number;
  created_at: string;
  updated_at: string;
  requirement_code: string;
  requirement_name: string;
  title: string;
  description?: string;
  framework: string;
  regulatory_framework?: string;
  authority?: string;
  category?: string;
  subcategory?: string;
  priority: string;
  risk_level: string;
  business_impact: string;
  control_type?: string;
  implementation?: string;
  implementation_cost: number;
  penalty_amount: number;
  currency_code: string;
  testing_method?: string;
  testing_frequency: string;
  review_frequency: string;
  effective_date?: string;
  due_date?: string;
  next_review_date?: string;
  expiration_date?: string;
  status: string;
  owner_id?: number;
  responsible_party?: string;
  approval_required: boolean;
  automated_monitoring: boolean;
  compliance_criteria?: string;
  evidence_requirements?: string;
  tags?: string;
  metadata?: string;
}

export interface ComplianceAssessment {
  id: number;
  created_at: string;
  updated_at: string;
  assessment_code: string;
  title: string;
  description?: string;
  assessment_type: string;
  framework: string;
  scope?: string;
  methodology?: string;
  criteria?: string;
  priority: string;
  risk_level: string;
  planned_start_date?: string;
  planned_end_date?: string;
  actual_start_date?: string;
  actual_end_date?: string;
  next_assessment_date?: string;
  lead_assessor_id: number;
  assessor_id?: number;
  reviewer_id?: number;
  assessment_team?: string;
  status: string;
  overall_score?: number;
  compliance_level?: string;
  findings_count: number;
  critical_count: number;
  critical_findings: number;  // Alias for critical_count
  high_count: number;
  high_findings: number;      // Alias for high_count
  medium_count: number;
  medium_findings: number;    // Alias for medium_count
  low_count: number;
  low_findings: number;       // Alias for low_count
  recommendations?: string;
  action_plan?: string;
  follow_up_required: boolean;
  cost: number;
  currency_code: string;
  report_generated: boolean;
  report_path?: string;
  report_date?: string;
  metadata?: string;
}

export interface ComplianceFinding {
  id: number;
  created_at: string;
  updated_at: string;
  finding_code: string;
  title: string;
  description?: string;
  assessment_id: number;
  requirement_id: number;
  finding_type: string;
  risk_level: string;
  impact?: string;
  likelihood: string;
  evidence?: string;
  document_ids?: string;
  recommendation?: string;
  remediation_plan?: string;
  target_date?: string;
  responsible_id?: number;
  status: string;
  resolution_date?: string;
  resolution_notes?: string;
  verified_by_id?: number;
  verified_at?: string;
  metadata?: string;
}

export interface RiskAssessment {
  id: number;
  created_at: string;
  updated_at: string;
  risk_code: string;
  risk_title: string;
  risk_description?: string;
  risk_category?: string;
  inherent_risk: string;
  residual_risk: string;
  risk_appetite: string;
  impact_score: number;
  likelihood_score: number;
  risk_score: number;
  risk_owner_id: number;
  treatment_strategy?: string;
  mitigation_plan?: string;
  last_review_date?: string;
  next_review_date?: string;
  review_frequency: string;
  status: string;
  metadata?: string;
}

export interface PolicyManagement {
  id: number;
  created_at: string;
  updated_at: string;
  policy_code: string;
  policy_title: string;
  policy_type: string;
  description?: string;
  content?: string;
  version: string;
  parent_policy_id?: number;
  framework?: string;
  requirement_ids?: string;
  owner_id: number;
  approver_id?: number;
  approved_at?: string;
  effective_date?: string;
  expiration_date?: string;
  review_date?: string;
  review_frequency: string;
  status: string;
  is_published: boolean;
  published_at?: string;
  requires_training: boolean;
  requires_acknowledgment: boolean;
  metadata?: string;
}

// Enterprise Business Intelligence Types
export interface DataWarehouse {
  id: number;
  created_at: string;
  updated_at: string;
  warehouse_name: string;
  description?: string;
  database_type: string;
  connection_string?: string;
  schema_name: string;
  storage_size: number;
  max_connections: number;
  query_timeout: number;
  retention_period: number;
  archival_enabled: boolean;
  compression_type: string;
  encryption_enabled: boolean;
  access_policy?: string;
  is_active: boolean;
  last_maintenance?: string;
  next_maintenance?: string;
  query_count: number;
  avg_query_time: number;
  last_optimized?: string;
  metadata?: string;
}

export interface DataSource {
  id: number;
  created_at: string;
  updated_at: string;
  source_name: string;
  description?: string;
  source_type: string;
  connection_string?: string;
  auth_method: string;
  credentials?: string;
  schema?: string;
  table_mapping?: string;
  field_mapping?: string;
  sync_enabled: boolean;
  sync_frequency: string;
  last_sync_at?: string;
  next_sync_at?: string;
  quality_rules?: string;
  error_threshold: number;
  last_quality_check?: string;
  quality_score: number;
  is_active: boolean;
  record_count: number;
  last_record_at?: string;
  metadata?: string;
}

export interface Dashboard {
  id: number;
  created_at: string;
  updated_at: string;
  dashboard_code: string;
  dashboard_name: string;
  description?: string;
  category: string;
  dashboard_type: string;
  layout?: string;
  layout_config?: string;
  widgets?: string;
  filters?: string;
  is_public: boolean;
  share_token?: string;
  access_list?: string;
  shared_with?: string;
  refresh_interval: number;
  cache_enabled: boolean;
  cache_duration: number;
  last_refreshed?: string;
  owner_id: number;
  view_count: number;
  last_viewed?: string;
  avg_load_time: number;
  is_active: boolean;
  tags?: string;
  metadata?: string;
}

export interface Report {
  id: number;
  created_at: string;
  updated_at: string;
  report_code: string;
  report_name: string;
  description?: string;
  category: string;
  report_type: string;
  analytics_type: string;
  data_source_id?: number;
  query?: string;
  query_definition?: string;
  data_sources?: string;
  parameters?: string;
  output_format: string;
  template?: string;
  visualization?: string;
  is_scheduled: boolean;
  is_public: boolean;
  is_active: boolean;
  schedule?: string;
  next_run?: string;
  last_run?: string;
  last_executed?: string;
  recipients?: string;
  delivery_method: string;
  created_by_id: number;
  owner_id: number;
  execution_count: number;
  avg_execution_time: number;
  file_size?: number;
  retention_days: number;
  last_error?: string;
  status: string;
  metadata?: string;
}

export interface KPI {
  id: number;
  created_at: string;
  updated_at: string;
  kpi_name: string;
  description?: string;
  category?: string;
  formula?: string;
  data_source?: string;
  unit?: string;
  target_value?: number;
  min_threshold?: number;
  max_threshold?: number;
  critical_min?: number;
  critical_max?: number;
  current_value: number;
  previous_value: number;
  trend: string;
  last_calculated?: string;
  update_frequency: string;
  next_update?: string;
  owner_id: number;
  alert_enabled: boolean;
  alert_threshold?: number;
  last_alert?: string;
  is_active: boolean;
  metadata?: string;
}

export interface DataMining {
  id: number;
  created_at: string;
  updated_at: string;
  model_name: string;
  description?: string;
  model_type: string;
  algorithm?: string;
  analytics_type: string;
  parameters?: string;
  features?: string;
  training_data?: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  trained_at?: string;
  deployed_at?: string;
  last_retrained?: string;
  retraining_frequency: string;
  model_path?: string;
  model_version: string;
  model_size: number;
  prediction_count: number;
  last_prediction?: string;
  avg_response_time: number;
  created_by_id: number;
  status: string;
  metadata?: string;
}

// Enterprise Human Resources Types
export interface Employee {
  id: number;
  created_at: string;
  updated_at: string;
  employee_id: string;
  user_id: number;
  first_name: string;
  last_name: string;
  middle_name?: string;
  date_of_birth?: string;
  gender?: string;
  marital_status?: string;
  nationality?: string;
  email: string;              // Work email
  phone?: string;             // Work phone
  personal_email?: string;
  personal_phone?: string;
  emergency_contact?: string;
  address?: string;
  hire_date: string;
  employment_type: string;
  employment_status: string;
  termination_date?: string;
  termination_reason?: string;
  probation_end_date?: string;
  job_title: string;
  job_description?: string;
  department_id?: number;
  department?: Department;    // Relationship
  manager_id?: number;
  manager?: Employee;         // Relationship
  position_id?: number;
  position?: Position;        // Relationship
  base_salary: number;
  currency: string;
  pay_frequency: string;
  work_schedule?: string;
  time_zone: string;
  work_location?: string;
  performance_rating: number;
  last_review_date?: string;
  next_review_date?: string;
  vacation_days: number;
  sick_days: number;
  personal_days: number;
  security_clearance?: string;
  access_level: string;
  metadata?: string;
}

export interface Department {
  id: number;
  created_at: string;
  updated_at: string;
  department_code: string;
  name: string;
  department_name: string;   // Alias for name
  manager?: Employee;        // Relationship
  parent_department?: Department; // Relationship
  description?: string;
  parent_department_id?: number;
  level: number;
  manager_id?: number;
  budget_amount: number;
  actual_spend: number;
  actual_amount: number;     // Alias for actual_spend
  cost_center_id?: number;
  cost_center_code?: string;
  location?: string;
  office_space?: string;
  phone?: string;
  email?: string;
  currency_code: string;
  established_date?: string;
  is_active: boolean;
  employee_count: number;
  metadata?: string;
}

export interface Position {
  id: number;
  created_at: string;
  updated_at: string;
  position_code: string;
  title: string;
  position_title: string;    // Alias for title
  description?: string;
  level?: string;
  grade?: string;
  job_family?: string;
  department_id: number;
  department?: Department;       // Relationship
  reports_to_id?: number;
  requirements?: string;
  qualifications?: string;
  skills?: string;
  min_salary: number;
  max_salary: number;
  currency: string;
  is_active: boolean;
  is_approved: boolean;
  headcount_limit: number;
  current_count: number;
  metadata?: string;
}

export interface PerformanceReview {
  id: number;
  created_at: string;
  updated_at: string;
  review_id: string;
  employee_id: number;
  reviewer_id: number;
  review_period: string;
  start_date: string;
  end_date: string;
  review_date: string;
  review_type: string;
  review_cycle?: string;
  overall_rating: number;
  goal_achievement: number;
  competency_rating?: string;
  achievements?: string;
  areas_for_improvement?: string;
  goals?: string;
  development_plan?: string;
  employee_comments?: string;
  manager_comments?: string;
  hr_comments?: string;
  status: string;
  submitted_at?: string;
  approved_at?: string;
  completed_at?: string;
  salary_increase: number;
  bonus_amount: number;
  promotion_recommended: boolean;
  metadata?: string;
}

export interface Training {
  id: number;
  created_at: string;
  updated_at: string;
  training_code: string;
  title: string;
  description?: string;
  category?: string;
  training_type: string;
  duration: number;
  max_participants: number;
  curriculum?: string;
  materials?: string;
  prerequisites?: string;
  instructor_id?: number;
  provider?: string;
  start_date?: string;
  end_date?: string;
  schedule?: string;
  location?: string;
  certification_offered: boolean;
  certification_valid: number;
  compliance_required: boolean;
  cost: number;
  currency: string;
  budget_code?: string;
  status: string;
  enrollment_count: number;
  completion_rate: number;
  metadata?: string;
}

export interface TrainingEnrollment {
  id: number;
  created_at: string;
  updated_at: string;
  employee_id: number;
  training_id: number;
  enrolled_at: string;
  enrolled_by_id: number;
  status: string;
  started_at?: string;
  completed_at?: string;
  progress_percent: number;
  score: number;
  passing_score: number;
  attempts: number;
  certificate_issued: boolean;
  certificate_number?: string;
  certificate_expiry?: string;
  feedback?: string;
  rating: number;
  metadata?: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Filter and Search Types
export interface FilterOptions {
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
  search?: string;
  [key: string]: any;
}

// Enterprise Dashboard Types
export interface EnterpriseStats {
  content_repositories: number;
  active_workflows: number;
  compliance_score: number;
  risk_assessments: number;
  financial_reports: number;
  employees: number;
  trainings: number;
  dashboards: number;
  total_documents: number;
  active_users: number;
}
