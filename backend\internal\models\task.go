package models

import (
	"time"

	"gorm.io/gorm"
)

// TaskType represents the type of task
type TaskType string

const (
	TaskTypeReview   TaskType = "review"
	TaskTypeDeadline TaskType = "deadline"
	TaskTypeHearing  TaskType = "hearing"
	TaskTypeComment  TaskType = "comment"
	TaskTypeGeneral  TaskType = "general"
	TaskTypeReminder TaskType = "reminder"
	TaskTypeFollowUp TaskType = "follow_up"
)

// TaskStatus represents the status of a task
type TaskStatus string

const (
	TaskStatusPending    TaskStatus = "pending"
	TaskStatusInProgress TaskStatus = "in_progress"
	TaskStatusCompleted  TaskStatus = "completed"
	TaskStatusCancelled  TaskStatus = "cancelled"
	TaskStatusOnHold     TaskStatus = "on_hold"
)

// TaskPriority represents the priority level of a task
type TaskPriority string

const (
	TaskPriorityLow    TaskPriority = "low"
	TaskPriorityMedium TaskPriority = "medium"
	TaskPriorityHigh   TaskPriority = "high"
	TaskPriorityUrgent TaskPriority = "urgent"
)

// Task represents a calendar task or event
type Task struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Basic task information
	Title       string       `json:"title" gorm:"not null"`
	Description string       `json:"description" gorm:"type:text"`
	Type        TaskType     `json:"type" gorm:"not null"`
	Status      TaskStatus   `json:"status" gorm:"default:'pending'"`
	Priority    TaskPriority `json:"priority" gorm:"default:'medium'"`

	// Timing information
	DueDate   *time.Time `json:"due_date"`
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`
	Duration  *int       `json:"duration"` // Duration in minutes
	IsAllDay  bool       `json:"is_all_day" gorm:"default:false"`
	TimeZone  string     `json:"time_zone" gorm:"default:'UTC'"`

	// Recurrence information
	IsRecurring    bool       `json:"is_recurring" gorm:"default:false"`
	RecurrenceRule string     `json:"recurrence_rule"` // RRULE format
	RecurrenceEnd  *time.Time `json:"recurrence_end"`
	ParentTaskID   *uint      `json:"parent_task_id"` // For recurring task instances
	ParentTask     *Task      `json:"parent_task,omitempty" gorm:"foreignKey:ParentTaskID"`

	// Source information (what triggered this task)
	SourceType     string `json:"source_type"` // "document", "regulation", "manual", "parsed_text"
	SourceID       *uint  `json:"source_id"`   // ID of the source entity
	SourceText     string `json:"source_text"` // Original text that generated this task
	ParsedFromText bool   `json:"parsed_from_text" gorm:"default:false"`

	// Relationships
	AssignedToID *uint `json:"assigned_to_id"`
	AssignedTo   *User `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToID"`
	CreatedByID  uint  `json:"created_by_id" gorm:"not null"`
	CreatedBy    User  `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Related entities
	DocumentID   *uint         `json:"document_id"`
	Document     *Document     `json:"document,omitempty" gorm:"foreignKey:DocumentID"`
	RegulationID *uint         `json:"regulation_id"`
	Regulation   *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`
	AgencyID     *uint         `json:"agency_id"`
	Agency       *Agency       `json:"agency,omitempty" gorm:"foreignKey:AgencyID"`
	CategoryID   *uint         `json:"category_id"`
	Category     *Category     `json:"category,omitempty" gorm:"foreignKey:CategoryID"`

	// Notification and reminder settings
	ReminderEnabled  bool       `json:"reminder_enabled" gorm:"default:false"`
	ReminderTime     *time.Time `json:"reminder_time"`
	NotificationSent bool       `json:"notification_sent" gorm:"default:false"`

	// Additional metadata
	Location    string     `json:"location"`
	URL         string     `json:"url"`
	Notes       string     `json:"notes" gorm:"type:text"`
	Tags        string     `json:"tags"` // Comma-separated tags
	IsPublic    bool       `json:"is_public" gorm:"default:false"`
	CompletedAt *time.Time `json:"completed_at"`
	CompletedBy *uint      `json:"completed_by"`

	// Performance Evaluation (matching finance performance structure)
	PerformancePercentage  float64    `json:"performance_percentage" gorm:"default:0.00"`
	DeadlineAdherenceScore float64    `json:"deadline_adherence_score" gorm:"default:0.00"`
	QualityScore           float64    `json:"quality_score" gorm:"default:0.00"`
	CompletionEfficiency   float64    `json:"completion_efficiency" gorm:"default:0.00"`
	PriorityHandlingScore  float64    `json:"priority_handling_score" gorm:"default:0.00"`
	PerformanceNotes       string     `json:"performance_notes" gorm:"type:text"`
	EvaluationDate         *time.Time `json:"evaluation_date"`
	IsAutoCalculated       bool       `json:"is_auto_calculated" gorm:"default:true"`
	EvaluatedByID          *uint      `json:"evaluated_by_id"`
	EvaluatedBy            *User      `json:"evaluated_by,omitempty" gorm:"foreignKey:EvaluatedByID;references:ID"`
}

// TaskAttachment represents file attachments for tasks
type TaskAttachment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	TaskID     uint   `json:"task_id" gorm:"not null"`
	Task       Task   `json:"task" gorm:"foreignKey:TaskID"`
	FileName   string `json:"file_name" gorm:"not null"`
	FilePath   string `json:"file_path" gorm:"not null"`
	FileSize   int64  `json:"file_size"`
	MimeType   string `json:"mime_type"`
	UploadedBy uint   `json:"uploaded_by" gorm:"not null"`
	Uploader   User   `json:"uploader" gorm:"foreignKey:UploadedBy;references:ID"`
}

// TaskComment represents comments on tasks
type TaskComment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	TaskID   uint   `json:"task_id" gorm:"not null"`
	Task     Task   `json:"task" gorm:"foreignKey:TaskID"`
	Content  string `json:"content" gorm:"type:text;not null"`
	AuthorID uint   `json:"author_id" gorm:"not null"`
	Author   User   `json:"author" gorm:"foreignKey:AuthorID;references:ID"`
}

// TaskPerformanceHistory tracks performance evaluation changes over time
type TaskPerformanceHistory struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	TaskID                 uint      `json:"task_id" gorm:"not null"`
	Task                   Task      `json:"task" gorm:"foreignKey:TaskID"`
	PerformancePercentage  float64   `json:"performance_percentage" gorm:"not null"`
	DeadlineAdherenceScore float64   `json:"deadline_adherence_score" gorm:"not null"`
	QualityScore           float64   `json:"quality_score" gorm:"not null"`
	CompletionEfficiency   float64   `json:"completion_efficiency" gorm:"not null"`
	PriorityHandlingScore  float64   `json:"priority_handling_score" gorm:"not null"`
	PerformanceNotes       string    `json:"performance_notes" gorm:"type:text"`
	EvaluationDate         time.Time `json:"evaluation_date" gorm:"not null"`
	IsAutoCalculated       bool      `json:"is_auto_calculated" gorm:"default:true"`
	EvaluatedByID          *uint     `json:"evaluated_by_id"`
	EvaluatedBy            *User     `json:"evaluated_by,omitempty" gorm:"foreignKey:EvaluatedByID;references:ID"`
	ChangeReason           string    `json:"change_reason" gorm:"type:text"`
}
