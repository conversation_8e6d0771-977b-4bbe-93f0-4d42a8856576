package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GetTaskPerformance returns performance metrics for tasks
func GetTaskPerformance(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get task statistics using raw SQL
	var totalTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE deleted_at IS NULL").Scan(&totalTasks)

	var completedTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE status = ? AND deleted_at IS NULL", "completed").Scan(&completedTasks)

	var pendingTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE status = ? AND deleted_at IS NULL", "pending").Scan(&pendingTasks)

	var inProgressTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE status = ? AND deleted_at IS NULL", "in_progress").Scan(&inProgressTasks)

	var overdueTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE status != ? AND due_date < NOW() AND deleted_at IS NULL", "completed").Scan(&overdueTasks)

	// Calculate completion rate
	var completionRate float64
	if totalTasks > 0 {
		completionRate = float64(completedTasks) / float64(totalTasks) * 100
	}

	// Get performance by user
	var userPerformance []gin.H
	var users []models.User
	db.Find(&users)

	for _, user := range users {
		var userTotalTasks int64
		db.Model(&models.Task{}).Where("assigned_to = ?", user.ID).Count(&userTotalTasks)

		var userCompletedTasks int64
		db.Model(&models.Task{}).Where("assigned_to = ? AND status = ?", user.ID, "completed").Count(&userCompletedTasks)

		var userCompletionRate float64
		if userTotalTasks > 0 {
			userCompletionRate = float64(userCompletedTasks) / float64(userTotalTasks) * 100
		}

		userPerformance = append(userPerformance, gin.H{
			"user_id":         user.ID,
			"user_name":       user.FirstName + " " + user.LastName,
			"total_tasks":     userTotalTasks,
			"completed_tasks": userCompletedTasks,
			"completion_rate": userCompletionRate,
		})
	}

	// Get performance by category
	var categoryPerformance []gin.H
	var categories []models.Category
	db.Find(&categories)

	for _, category := range categories {
		var categoryTotalTasks int64
		db.Model(&models.Task{}).Where("category_id = ?", category.ID).Count(&categoryTotalTasks)

		var categoryCompletedTasks int64
		db.Model(&models.Task{}).Where("category_id = ? AND status = ?", category.ID, "completed").Count(&categoryCompletedTasks)

		var categoryCompletionRate float64
		if categoryTotalTasks > 0 {
			categoryCompletionRate = float64(categoryCompletedTasks) / float64(categoryTotalTasks) * 100
		}

		categoryPerformance = append(categoryPerformance, gin.H{
			"category_id":     category.ID,
			"category_name":   category.Name,
			"total_tasks":     categoryTotalTasks,
			"completed_tasks": categoryCompletedTasks,
			"completion_rate": categoryCompletionRate,
		})
	}

	response := gin.H{
		"overall": gin.H{
			"total_tasks":       totalTasks,
			"completed_tasks":   completedTasks,
			"pending_tasks":     pendingTasks,
			"in_progress_tasks": inProgressTasks,
			"overdue_tasks":     overdueTasks,
			"completion_rate":   completionRate,
		},
		"by_user":     userPerformance,
		"by_category": categoryPerformance,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performance metrics retrieved successfully",
		Data:    response,
	})
}

// GetUserTaskPerformance returns performance metrics for a specific user
func GetUserTaskPerformance(c *gin.Context) {
	userID, valid := ValidateID(c, "user_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify user exists
	var user models.User
	if err := db.First(&user, userID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Get user task statistics
	var totalTasks int64
	db.Model(&models.Task{}).Where("assigned_to = ?", userID).Count(&totalTasks)

	var completedTasks int64
	db.Model(&models.Task{}).Where("assigned_to = ? AND status = ?", userID, "completed").Count(&completedTasks)

	var pendingTasks int64
	db.Model(&models.Task{}).Where("assigned_to = ? AND status = ?", userID, "pending").Count(&pendingTasks)

	var inProgressTasks int64
	db.Model(&models.Task{}).Where("assigned_to = ? AND status = ?", userID, "in_progress").Count(&inProgressTasks)

	var overdueTasks int64
	db.Model(&models.Task{}).Where("assigned_to = ? AND status != ? AND due_date < NOW()", userID, "completed").Count(&overdueTasks)

	// Calculate completion rate
	var completionRate float64
	if totalTasks > 0 {
		completionRate = float64(completedTasks) / float64(totalTasks) * 100
	}

	// Get recent tasks
	var recentTasks []models.Task
	db.Where("assigned_to = ?", userID).
		Order("created_at DESC").
		Limit(10).
		Find(&recentTasks)

	recentTaskResponses := make([]gin.H, len(recentTasks))
	for i, task := range recentTasks {
		recentTaskResponses[i] = gin.H{
			"id":         task.ID,
			"title":      task.Title,
			"status":     task.Status,
			"priority":   task.Priority,
			"due_date":   task.DueDate,
			"created_at": task.CreatedAt,
		}
	}

	response := gin.H{
		"user": gin.H{
			"id":   user.ID,
			"name": user.FirstName + " " + user.LastName,
		},
		"performance": gin.H{
			"total_tasks":       totalTasks,
			"completed_tasks":   completedTasks,
			"pending_tasks":     pendingTasks,
			"in_progress_tasks": inProgressTasks,
			"overdue_tasks":     overdueTasks,
			"completion_rate":   completionRate,
		},
		"recent_tasks": recentTaskResponses,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User task performance retrieved successfully",
		Data:    response,
	})
}

// GetTaskPerformanceDashboard returns dashboard data for task performance
func GetTaskPerformanceDashboard(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get overall statistics using raw SQL to avoid GORM model issues
	var totalTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE deleted_at IS NULL").Scan(&totalTasks)

	var completedTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE status = ? AND deleted_at IS NULL", "completed").Scan(&completedTasks)

	var pendingTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE status = ? AND deleted_at IS NULL", "pending").Scan(&pendingTasks)

	var inProgressTasks int64
	db.Model(&models.Task{}).Where("status = ?", "in_progress").Count(&inProgressTasks)

	// Calculate completion rate
	var completionRate float64
	if totalTasks > 0 {
		completionRate = float64(completedTasks) / float64(totalTasks) * 100
	}

	response := gin.H{
		"total_tasks":       totalTasks,
		"completed_tasks":   completedTasks,
		"pending_tasks":     pendingTasks,
		"in_progress_tasks": inProgressTasks,
		"completion_rate":   completionRate,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performance dashboard retrieved successfully",
		Data:    response,
	})
}

// GetTaskPerformanceMetrics returns detailed metrics for task performance
func GetTaskPerformanceMetrics(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get task metrics by priority using raw SQL
	var highPriorityTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE priority = ? AND deleted_at IS NULL", "high").Scan(&highPriorityTasks)

	var mediumPriorityTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE priority = ? AND deleted_at IS NULL", "medium").Scan(&mediumPriorityTasks)

	var lowPriorityTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE priority = ? AND deleted_at IS NULL", "low").Scan(&lowPriorityTasks)

	// Get overdue tasks
	var overdueTasks int64
	db.Raw("SELECT COUNT(*) FROM tasks WHERE status != ? AND due_date < NOW() AND deleted_at IS NULL", "completed").Scan(&overdueTasks)

	response := gin.H{
		"priority_metrics": gin.H{
			"high":   highPriorityTasks,
			"medium": mediumPriorityTasks,
			"low":    lowPriorityTasks,
		},
		"overdue_tasks": overdueTasks,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performance metrics retrieved successfully",
		Data:    response,
	})
}
