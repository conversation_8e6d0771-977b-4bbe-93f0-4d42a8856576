'use client';

import React, { useState } from 'react';
import Layout from '../components/Layout/Layout';
import apiService from '../services/api';
import { contentApi, financialApi, complianceApi, biApi, hrApi } from '../services/enterpriseApi';

interface TestSuite {
  name: string;
  tests: TestCase[];
}

interface TestCase {
  name: string;
  description: string;
  testFunction: () => Promise<any>;
  category: string;
  critical: boolean;
}

interface TestResult {
  suiteName: string;
  testName: string;
  status: 'pending' | 'success' | 'error' | 'skipped';
  duration?: number;
  error?: string;
  response?: any;
}

const FinalIntegrationTestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');
  const [summary, setSummary] = useState({
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0
  });

  const testSuites: TestSuite[] = [
    {
      name: 'Authentication & User Management',
      tests: [
        {
          name: 'Get Current User',
          description: 'Test user authentication and profile retrieval',
          testFunction: () => apiService.getCurrentUser(),
          category: 'auth',
          critical: true
        },
        {
          name: 'Get User Permissions',
          description: 'Test user permission retrieval',
          testFunction: () => apiService.getUserPermissions(),
          category: 'auth',
          critical: true
        },
        {
          name: 'Get User Stats',
          description: 'Test user statistics',
          testFunction: () => apiService.getUserStats(),
          category: 'auth',
          critical: false
        }
      ]
    },
    {
      name: 'Document Management',
      tests: [
        {
          name: 'Get Documents',
          description: 'Test document listing',
          testFunction: () => apiService.getDocuments({ page: 1, per_page: 5 }),
          category: 'documents',
          critical: true
        },
        {
          name: 'Search Documents',
          description: 'Test document search functionality',
          testFunction: () => apiService.searchDocuments({ query: 'test', page: 1, per_page: 5 }),
          category: 'documents',
          critical: true
        },
        {
          name: 'Get Public Documents',
          description: 'Test public document access',
          testFunction: () => apiService.getPublicDocuments({ page: 1, per_page: 5 }),
          category: 'documents',
          critical: true
        },
        {
          name: 'Get Document Defaults',
          description: 'Test document preloading',
          testFunction: () => apiService.getDocumentDefaults(),
          category: 'documents',
          critical: false
        }
      ]
    },
    {
      name: 'Agency & Category Management',
      tests: [
        {
          name: 'Get Agencies',
          description: 'Test agency listing',
          testFunction: () => apiService.getAgencies({ page: 1, per_page: 5 }),
          category: 'agencies',
          critical: true
        },
        {
          name: 'Get Public Agencies',
          description: 'Test public agency access',
          testFunction: () => apiService.getPublicAgencies({ page: 1, per_page: 5 }),
          category: 'agencies',
          critical: true
        },
        {
          name: 'Get Categories',
          description: 'Test category listing',
          testFunction: () => apiService.getCategories({ page: 1, per_page: 5 }),
          category: 'categories',
          critical: true
        },
        {
          name: 'Get Agency Defaults',
          description: 'Test agency preloading',
          testFunction: () => apiService.getAgencyDefaults(),
          category: 'agencies',
          critical: false
        }
      ]
    },
    {
      name: 'Regulation Management',
      tests: [
        {
          name: 'Get Regulations',
          description: 'Test regulation listing',
          testFunction: () => apiService.getRegulations({ page: 1, per_page: 5 }),
          category: 'regulations',
          critical: true
        },
        {
          name: 'Get Public Regulations',
          description: 'Test public regulation access',
          testFunction: () => apiService.getPublicRegulations({ page: 1, per_page: 5 }),
          category: 'regulations',
          critical: true
        },
        {
          name: 'Get Regulation Defaults',
          description: 'Test regulation preloading',
          testFunction: () => apiService.getRegulationDefaults(),
          category: 'regulations',
          critical: false
        }
      ]
    },
    {
      name: 'Task & Proceeding Management',
      tests: [
        {
          name: 'Get Tasks',
          description: 'Test task listing',
          testFunction: () => apiService.getTasks({ page: 1, per_page: 5 }),
          category: 'tasks',
          critical: true
        },
        {
          name: 'Get Proceedings',
          description: 'Test proceeding listing',
          testFunction: () => apiService.getProceedings({ page: 1, per_page: 5 }),
          category: 'proceedings',
          critical: true
        },
        {
          name: 'Get Proceeding Defaults',
          description: 'Test proceeding preloading',
          testFunction: () => apiService.getProceedingDefaults(),
          category: 'proceedings',
          critical: false
        }
      ]
    },
    {
      name: 'Calendar & Summary',
      tests: [
        {
          name: 'Get Calendar',
          description: 'Test calendar functionality',
          testFunction: () => apiService.getCalendar(),
          category: 'calendar',
          critical: true
        },
        {
          name: 'Get Calendar Stats',
          description: 'Test calendar statistics',
          testFunction: () => apiService.getCalendarStats(),
          category: 'calendar',
          critical: false
        },
        {
          name: 'Get Summaries',
          description: 'Test summary listing',
          testFunction: () => apiService.getSummaries({ page: 1, per_page: 5 }),
          category: 'summaries',
          critical: true
        }
      ]
    },
    {
      name: 'Finance Management',
      tests: [
        {
          name: 'Get Finance Entries',
          description: 'Test finance listing',
          testFunction: () => apiService.getFinances({ page: 1, per_page: 5 }),
          category: 'finance',
          critical: true
        },
        {
          name: 'Get Finance Defaults',
          description: 'Test finance preloading',
          testFunction: () => apiService.getFinanceDefaults(),
          category: 'finance',
          critical: false
        }
      ]
    },
    {
      name: 'Digital Signatures & Certificates',
      tests: [
        {
          name: 'Get User Signatures',
          description: 'Test digital signature retrieval',
          testFunction: () => apiService.getUserSignatures(),
          category: 'signatures',
          critical: false
        },
        {
          name: 'Get Certificates',
          description: 'Test certificate management',
          testFunction: () => apiService.getCertificates(),
          category: 'certificates',
          critical: false
        }
      ]
    },
    {
      name: 'Analytics & Statistics',
      tests: [
        {
          name: 'Get Public Stats',
          description: 'Test public statistics',
          testFunction: () => apiService.getPublicStats(),
          category: 'analytics',
          critical: true
        },
        {
          name: 'Get Dashboard Stats',
          description: 'Test dashboard statistics',
          testFunction: () => apiService.getDashboardStats(),
          category: 'analytics',
          critical: true
        },
        {
          name: 'Get Advanced Analytics',
          description: 'Test advanced analytics',
          testFunction: () => apiService.getAdvancedAnalytics(),
          category: 'analytics',
          critical: false
        }
      ]
    },
    {
      name: 'Enterprise Content Management',
      tests: [
        {
          name: 'Get Content Repositories',
          description: 'Test content repository access',
          testFunction: () => contentApi.getRepositories(),
          category: 'enterprise',
          critical: false
        },
        {
          name: 'Get Content Workflows',
          description: 'Test content workflow access',
          testFunction: () => contentApi.getWorkflows(),
          category: 'enterprise',
          critical: false
        }
      ]
    },
    {
      name: 'Enterprise Financial Management',
      tests: [
        {
          name: 'Get Chart of Accounts',
          description: 'Test financial account access',
          testFunction: () => financialApi.getAccounts(),
          category: 'enterprise',
          critical: false
        },
        {
          name: 'Get GL Entries',
          description: 'Test general ledger access',
          testFunction: () => financialApi.getGLEntries(),
          category: 'enterprise',
          critical: false
        }
      ]
    },
    {
      name: 'Utility Functions',
      tests: [
        {
          name: 'Generate Slug',
          description: 'Test slug generation',
          testFunction: () => apiService.generateSlug('Test Document Title'),
          category: 'utilities',
          critical: false
        },
        {
          name: 'Generate FR Number',
          description: 'Test FR number generation',
          testFunction: () => apiService.generateFRNumber(),
          category: 'utilities',
          critical: false
        },
        {
          name: 'Get Public Law Number',
          description: 'Test public law number generation',
          testFunction: () => apiService.getPublicLawNumber(),
          category: 'utilities',
          critical: false
        }
      ]
    }
  ];

  const updateTestResult = (suiteName: string, testName: string, result: Partial<TestResult>) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.suiteName === suiteName && r.testName === testName);
      if (existing) {
        return prev.map(r => 
          r.suiteName === suiteName && r.testName === testName 
            ? { ...r, ...result }
            : r
        );
      } else {
        return [...prev, { suiteName, testName, status: 'pending', ...result }];
      }
    });
  };

  const runTest = async (suite: TestSuite, test: TestCase) => {
    const startTime = Date.now();
    setCurrentTest(`${suite.name} - ${test.name}`);
    updateTestResult(suite.name, test.name, { status: 'pending' });

    try {
      const response = await test.testFunction();
      const duration = Date.now() - startTime;
      updateTestResult(suite.name, test.name, {
        status: 'success',
        response,
        duration
      });
      console.log(`✅ ${suite.name} - ${test.name}`, response);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateTestResult(suite.name, test.name, {
        status: 'error',
        error: error.message || 'Unknown error',
        duration
      });
      console.error(`❌ ${suite.name} - ${test.name}:`, error);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setCurrentTest('');

    try {
      for (const suite of testSuites) {
        for (const test of suite.tests) {
          await runTest(suite, test);
          // Small delay between tests to avoid overwhelming the server
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Calculate summary
      const results = testResults;
      const total = results.length;
      const passed = results.filter(r => r.status === 'success').length;
      const failed = results.filter(r => r.status === 'error').length;
      const skipped = results.filter(r => r.status === 'skipped').length;

      setSummary({ total, passed, failed, skipped });
      console.log('🎉 All integration tests completed!');
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      setIsRunning(false);
      setCurrentTest('');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'pending': return 'text-yellow-600';
      case 'skipped': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      case 'skipped': return '⏭️';
      default: return '⚪';
    }
  };

  const criticalTests = testResults.filter(r => {
    const suite = testSuites.find(s => s.name === r.suiteName);
    const test = suite?.tests.find(t => t.name === r.testName);
    return test?.critical;
  });

  const criticalPassed = criticalTests.filter(r => r.status === 'success').length;
  const criticalFailed = criticalTests.filter(r => r.status === 'error').length;

  return (
    <Layout title="Final Integration Testing" requireAuth={true}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Final Integration Testing
          </h1>

          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">Test Suite Control</h2>
                <p className="text-gray-600">
                  Comprehensive testing of all backend-frontend API connections
                </p>
                {currentTest && (
                  <p className="text-sm text-blue-600 mt-2">
                    Currently running: {currentTest}
                  </p>
                )}
              </div>

              <button
                onClick={runAllTests}
                disabled={isRunning}
                className={`px-6 py-3 rounded-md font-medium ${
                  isRunning
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                } text-white transition-colors`}
              >
                {isRunning ? 'Running Tests...' : 'Run All Tests'}
              </button>
            </div>

            {/* Summary */}
            {testResults.length > 0 && (
              <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{summary.total}</div>
                  <div className="text-sm text-blue-800">Total Tests</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{summary.passed}</div>
                  <div className="text-sm text-green-800">Passed</div>
                </div>
                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{summary.failed}</div>
                  <div className="text-sm text-red-800">Failed</div>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {criticalPassed}/{criticalTests.length}
                  </div>
                  <div className="text-sm text-yellow-800">Critical Passed</div>
                </div>
              </div>
            )}
          </div>

          {/* Test Results by Suite */}
          {testSuites.map((suite) => {
            const suiteResults = testResults.filter(r => r.suiteName === suite.name);
            if (suiteResults.length === 0) return null;

            const suitePassed = suiteResults.filter(r => r.status === 'success').length;
            const suiteFailed = suiteResults.filter(r => r.status === 'error').length;

            return (
              <div key={suite.name} className="bg-white rounded-lg shadow-md mb-6">
                <div className="px-6 py-4 bg-gray-50 border-b">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {suite.name}
                    </h3>
                    <div className="text-sm text-gray-600">
                      {suitePassed}/{suiteResults.length} passed
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="space-y-3">
                    {suiteResults.map((result, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border-2 ${
                          result.status === 'success'
                            ? 'border-green-200 bg-green-50'
                            : result.status === 'error'
                            ? 'border-red-200 bg-red-50'
                            : 'border-yellow-200 bg-yellow-50'
                        }`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <span className="mr-2">
                                {getStatusIcon(result.status)}
                              </span>
                              <span className="font-medium text-gray-900">
                                {result.testName}
                              </span>
                              {suite.tests.find(t => t.name === result.testName)?.critical && (
                                <span className="ml-2 px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded">
                                  Critical
                                </span>
                              )}
                            </div>
                            <p className="text-sm text-gray-600 mt-1">
                              {suite.tests.find(t => t.name === result.testName)?.description}
                            </p>
                            {result.duration && (
                              <p className="text-xs text-gray-500 mt-1">
                                Duration: {result.duration}ms
                              </p>
                            )}
                            {result.error && (
                              <p className="text-sm text-red-600 mt-1">
                                Error: {result.error}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            );
          })}

          {/* Instructions */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Integration Test Results
            </h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• ✅ Green tests indicate successful backend-frontend API connections</li>
              <li>• ❌ Red tests indicate failed API connections that need investigation</li>
              <li>• 🔶 Critical tests are essential for core functionality</li>
              <li>• All tests run against the live backend API endpoints</li>
              <li>• Check browser console for detailed request/response logs</li>
              <li>• Ensure backend server is running on port 8080 before testing</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FinalIntegrationTestPage;
