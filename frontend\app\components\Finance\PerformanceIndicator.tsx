'use client';

import React from 'react';
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon
} from '@heroicons/react/24/outline';

interface PerformanceIndicatorProps {
  percentage: number;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showTrend?: boolean;
  previousPercentage?: number;
  className?: string;
}

const PerformanceIndicator: React.FC<PerformanceIndicatorProps> = ({
  percentage,
  size = 'md',
  showIcon = true,
  showTrend = false,
  previousPercentage,
  className = ''
}) => {
  const getPerformanceLevel = (perf: number) => {
    if (perf >= 80) return 'excellent';
    if (perf >= 60) return 'good';
    if (perf >= 40) return 'fair';
    return 'poor';
  };

  const getPerformanceConfig = (level: string) => {
    switch (level) {
      case 'excellent':
        return {
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          borderColor: 'border-green-200',
          icon: CheckCircleIcon,
          label: 'Excellent'
        };
      case 'good':
        return {
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          borderColor: 'border-blue-200',
          icon: CheckCircleIcon,
          label: 'Good'
        };
      case 'fair':
        return {
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          borderColor: 'border-yellow-200',
          icon: ExclamationTriangleIcon,
          label: 'Fair'
        };
      case 'poor':
        return {
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          borderColor: 'border-red-200',
          icon: XCircleIcon,
          label: 'Poor'
        };
      default:
        return {
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200',
          icon: ExclamationTriangleIcon,
          label: 'Unknown'
        };
    }
  };

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          container: 'px-2 py-1',
          text: 'text-xs',
          icon: 'h-3 w-3'
        };
      case 'lg':
        return {
          container: 'px-4 py-2',
          text: 'text-base',
          icon: 'h-6 w-6'
        };
      default: // md
        return {
          container: 'px-3 py-1.5',
          text: 'text-sm',
          icon: 'h-4 w-4'
        };
    }
  };

  const getTrend = () => {
    if (!showTrend || previousPercentage === undefined) return null;
    
    const diff = percentage - previousPercentage;
    const isPositive = diff > 0;
    const isNeutral = Math.abs(diff) < 0.1;

    if (isNeutral) return null;

    return {
      isPositive,
      diff: Math.abs(diff),
      icon: isPositive ? ArrowTrendingUpIcon : ArrowTrendingDownIcon,
      color: isPositive ? 'text-green-500' : 'text-red-500'
    };
  };

  const level = getPerformanceLevel(percentage);
  const config = getPerformanceConfig(level);
  const sizeClasses = getSizeClasses(size);
  const trend = getTrend();

  return (
    <div className={`inline-flex items-center space-x-1 ${className}`}>
      {/* Main Performance Badge */}
      <span
        className={`
          inline-flex items-center rounded-full font-medium border
          ${config.color} ${config.bgColor} ${config.borderColor}
          ${sizeClasses.container} ${sizeClasses.text}
        `}
      >
        {showIcon && (
          <config.icon className={`${sizeClasses.icon} mr-1`} />
        )}
        {percentage.toFixed(1)}%
      </span>

      {/* Trend Indicator */}
      {trend && (
        <span className={`inline-flex items-center ${trend.color} ${sizeClasses.text}`}>
          <trend.icon className={`${sizeClasses.icon} mr-0.5`} />
          {trend.diff.toFixed(1)}%
        </span>
      )}
    </div>
  );
};

// Progress Bar Component
interface PerformanceProgressBarProps {
  percentage: number;
  height?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  showPercentage?: boolean;
  className?: string;
}

export const PerformanceProgressBar: React.FC<PerformanceProgressBarProps> = ({
  percentage,
  height = 'md',
  showLabel = false,
  showPercentage = true,
  className = ''
}) => {
  const getHeightClass = (h: string) => {
    switch (h) {
      case 'sm': return 'h-2';
      case 'lg': return 'h-6';
      default: return 'h-4';
    }
  };

  const getBarColor = (perf: number) => {
    if (perf >= 80) return 'bg-green-500';
    if (perf >= 60) return 'bg-blue-500';
    if (perf >= 40) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const heightClass = getHeightClass(height);
  const barColor = getBarColor(percentage);
  const clampedPercentage = Math.min(Math.max(percentage, 0), 100);

  return (
    <div className={`w-full ${className}`}>
      {showLabel && (
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700">Performance</span>
          {showPercentage && (
            <span className="text-sm text-gray-500">{percentage.toFixed(1)}%</span>
          )}
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full ${heightClass}`}>
        <div
          className={`${barColor} ${heightClass} rounded-full transition-all duration-300 ease-in-out`}
          style={{ width: `${clampedPercentage}%` }}
        />
      </div>
    </div>
  );
};

// Performance Gauge Component (Circular)
interface PerformanceGaugeProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  showLabel?: boolean;
  className?: string;
}

export const PerformanceGauge: React.FC<PerformanceGaugeProps> = ({
  percentage,
  size = 120,
  strokeWidth = 8,
  showLabel = true,
  className = ''
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (percentage / 100) * circumference;

  const getStrokeColor = (perf: number) => {
    if (perf >= 80) return '#10B981'; // green-500
    if (perf >= 60) return '#3B82F6'; // blue-500
    if (perf >= 40) return '#F59E0B'; // yellow-500
    return '#EF4444'; // red-500
  };

  const strokeColor = getStrokeColor(percentage);

  return (
    <div className={`inline-flex flex-col items-center ${className}`}>
      <div className="relative">
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#E5E7EB"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            className="transition-all duration-500 ease-in-out"
          />
        </svg>
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-2xl font-bold text-gray-900">
            {percentage.toFixed(0)}%
          </span>
        </div>
      </div>
      {showLabel && (
        <span className="mt-2 text-sm font-medium text-gray-700">
          Performance
        </span>
      )}
    </div>
  );
};

export default PerformanceIndicator;
