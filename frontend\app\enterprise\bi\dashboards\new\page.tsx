'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { biApi } from '../../../../services/enterpriseApi';

interface Dashboard {
  id: number;
  created_at: string;
  updated_at: string;
  dashboard_code: string;
  dashboard_name: string;
  description?: string;
  category: string;
  dashboard_type: string;
  layout_config?: string;
  refresh_interval: number;
  is_public: boolean;
  is_active: boolean;
  owner_id: number;
  shared_with?: string;
  tags?: string;
  metadata?: string;
}

const NewDashboardPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<Dashboard>>({
    dashboard_code: '',
    dashboard_name: '',
    description: '',
    category: 'operational',
    dashboard_type: 'standard',
    layout_config: '',
    refresh_interval: 300,
    is_public: false,
    is_active: true,
    owner_id: 1,
    shared_with: '',
    tags: '',
    metadata: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await biApi.createDashboard(formData);
      router.push('/enterprise/bi/dashboards');
    } catch (err: any) {
      setError(err.message || 'Failed to create dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create New Dashboard</h1>
        <button
          onClick={() => router.push('/enterprise/bi/dashboards')}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Back to Dashboards
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Dashboard Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dashboard Code *
            </label>
            <input
              type="text"
              name="dashboard_code"
              value={formData.dashboard_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., DASH-2025-001"
            />
          </div>

          {/* Dashboard Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dashboard Name *
            </label>
            <input
              type="text"
              name="dashboard_name"
              value={formData.dashboard_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Sales Performance Dashboard"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="financial">Financial</option>
              <option value="operational">Operational</option>
              <option value="sales">Sales</option>
              <option value="marketing">Marketing</option>
              <option value="hr">Human Resources</option>
              <option value="compliance">Compliance</option>
              <option value="executive">Executive</option>
              <option value="custom">Custom</option>
            </select>
          </div>

          {/* Dashboard Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dashboard Type *
            </label>
            <select
              name="dashboard_type"
              value={formData.dashboard_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="standard">Standard</option>
              <option value="executive">Executive</option>
              <option value="operational">Operational</option>
              <option value="analytical">Analytical</option>
              <option value="real_time">Real-time</option>
              <option value="mobile">Mobile</option>
            </select>
          </div>

          {/* Refresh Interval */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Refresh Interval (seconds)
            </label>
            <select
              name="refresh_interval"
              value={formData.refresh_interval}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={60}>1 minute</option>
              <option value={300}>5 minutes</option>
              <option value={600}>10 minutes</option>
              <option value={1800}>30 minutes</option>
              <option value={3600}>1 hour</option>
              <option value={21600}>6 hours</option>
              <option value={86400}>24 hours</option>
            </select>
          </div>

          {/* Owner ID */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Owner ID *
            </label>
            <input
              type="number"
              name="owner_id"
              value={formData.owner_id}
              onChange={handleChange}
              required
              min="1"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Dashboard description and purpose..."
          />
        </div>

        {/* Layout Configuration */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Layout Configuration (JSON)
          </label>
          <textarea
            name="layout_config"
            value={formData.layout_config}
            onChange={handleChange}
            rows={4}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder='{"columns": 3, "rows": 4, "widgets": []}'
          />
        </div>

        {/* Shared With */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Shared With (User IDs, comma-separated)
          </label>
          <input
            type="text"
            name="shared_with"
            value={formData.shared_with}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., 1,2,3"
          />
        </div>

        {/* Tags */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tags (comma-separated)
          </label>
          <input
            type="text"
            name="tags"
            value={formData.tags}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="e.g., sales, performance, kpi"
          />
        </div>

        {/* Checkboxes */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_public"
              checked={formData.is_public}
              onChange={handleChange}
              className="mr-2"
            />
            Public Dashboard
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleChange}
              className="mr-2"
            />
            Active Dashboard
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push('/enterprise/bi/dashboards')}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Dashboard'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NewDashboardPage;
