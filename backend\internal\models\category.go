package models

import (
	"time"

	"gorm.io/gorm"
)

// Category represents a document category (similar to Federal Register subjects)
type Category struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Basic category information
	Name        string `json:"name" gorm:"not null"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`

	// Category hierarchy
	ParentCategoryID *uint      `json:"parent_category_id"`
	ParentCategory   *Category  `json:"parent_category,omitempty" gorm:"foreignKey:ParentCategoryID"`
	SubCategories    []Category `json:"sub_categories,omitempty" gorm:"foreignKey:ParentCategoryID"`

	// Category metadata
	Color     string `json:"color"`
	Icon      string `json:"icon"`
	SortOrder int    `json:"sort_order" gorm:"default:0"`
	IsActive  bool   `json:"is_active" gorm:"default:true"`

	// Category statistics
	DocumentCount int `json:"document_count" gorm:"-"`

	// Relationships
	Documents []Document `json:"documents,omitempty" gorm:"many2many:document_category_assignments;"`

	// Regulation relationships
	RegulationCount         int                              `json:"regulation_count" gorm:"default:0"`
	RegulationRelationships []RegulationCategoryRelationship `json:"regulation_relationships,omitempty" gorm:"foreignKey:CategoryID"`
}

// Tag represents a document tag for flexible categorization
type Tag struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	Color       string `json:"color"`

	// Tag metadata
	UsageCount int  `json:"usage_count" gorm:"default:0"`
	IsActive   bool `json:"is_active" gorm:"default:true"`

	// Tag creator
	CreatedByID *uint `json:"created_by_id"`
	CreatedBy   *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`

	// Relationships
	Documents []Document `json:"documents,omitempty" gorm:"many2many:document_tag_assignments;"`
}

// DocumentCategoryAssignment represents the many-to-many relationship between documents and categories
type DocumentCategoryAssignment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	CategoryID uint     `json:"category_id" gorm:"not null"`
	Category   Category `json:"category" gorm:"foreignKey:CategoryID"`

	AssignedByID *uint `json:"assigned_by_id"`
	AssignedBy   *User `json:"assigned_by,omitempty" gorm:"foreignKey:AssignedByID"`
}

// DocumentTagAssignment represents the many-to-many relationship between documents and tags
type DocumentTagAssignment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	TagID uint `json:"tag_id" gorm:"not null"`
	Tag   Tag  `json:"tag" gorm:"foreignKey:TagID"`

	AssignedByID *uint `json:"assigned_by_id"`
	AssignedBy   *User `json:"assigned_by,omitempty" gorm:"foreignKey:AssignedByID"`
}

// Subject represents Federal Register subjects (predefined categories)
type Subject struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Slug        string `json:"slug" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	CFRTitle    string `json:"cfr_title"` // Related CFR title

	// Subject hierarchy
	ParentSubjectID *uint     `json:"parent_subject_id"`
	ParentSubject   *Subject  `json:"parent_subject,omitempty" gorm:"foreignKey:ParentSubjectID"`
	SubSubjects     []Subject `json:"sub_subjects,omitempty" gorm:"foreignKey:ParentSubjectID"`

	// Subject metadata
	SortOrder int  `json:"sort_order" gorm:"default:0"`
	IsActive  bool `json:"is_active" gorm:"default:true"`

	// Statistics
	DocumentCount int `json:"document_count" gorm:"-"`

	// Relationships
	Documents []Document `json:"documents,omitempty" gorm:"many2many:document_subject_assignments;"`
}

// DocumentSubjectAssignment represents the many-to-many relationship between documents and subjects
type DocumentSubjectAssignment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	SubjectID uint    `json:"subject_id" gorm:"not null"`
	Subject   Subject `json:"subject" gorm:"foreignKey:SubjectID"`

	AssignedByID *uint `json:"assigned_by_id"`
	AssignedBy   *User `json:"assigned_by,omitempty" gorm:"foreignKey:AssignedByID"`
}

// TableName returns the table name for Category model
func (Category) TableName() string {
	return "categories"
}

// TableName returns the table name for Tag model
func (Tag) TableName() string {
	return "tags"
}

// TableName returns the table name for DocumentCategoryAssignment model
func (DocumentCategoryAssignment) TableName() string {
	return "document_category_assignments"
}

// TableName returns the table name for DocumentTagAssignment model
func (DocumentTagAssignment) TableName() string {
	return "document_tag_assignments"
}

// TableName returns the table name for Subject model
func (Subject) TableName() string {
	return "subjects"
}

// TableName returns the table name for DocumentSubjectAssignment model
func (DocumentSubjectAssignment) TableName() string {
	return "document_subject_assignments"
}
