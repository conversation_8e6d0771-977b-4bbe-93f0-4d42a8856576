'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { Summary as ImportedSummary } from '../../types';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  CalendarIcon,
  GlobeAltIcon,
  StarIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  LockClosedIcon,
  LinkIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import MarkdownRenderer from '../../components/MarkdownRenderer/MarkdownRenderer';

// Using imported Summary type

const SummaryViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const summaryId = params.id as string;
  const { user } = useAuthStore();
  const [summary, setSummary] = useState<ImportedSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (summaryId) {
      fetchSummary();
    }
  }, [summaryId]);

  const fetchSummary = async () => {
    try {
      const response = await apiService.getSummary(parseInt(summaryId));
      setSummary(response);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch summary');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);
      await apiService.deleteSummary(parseInt(summaryId));
      router.push('/summary');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete summary');
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSummaryTypeColor = (type: string) => {
    switch (type) {
      case 'news':
        return 'bg-blue-100 text-blue-800';
      case 'analysis':
        return 'bg-purple-100 text-purple-800';
      case 'update':
        return 'bg-green-100 text-green-800';
      case 'alert':
        return 'bg-red-100 text-red-800';
      case 'report':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string | number) => {
    const priorityStr = typeof priority === 'number' ?
      (priority >= 4 ? 'urgent' : priority >= 3 ? 'high' : priority >= 2 ? 'medium' : 'low') :
      priority;
    switch (priorityStr) {
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canEdit = user && (user.role === 'admin' || (summary?.created_by?.id === user.id));

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading summary...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !summary) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Summary Not Found</h1>
            <p className="text-gray-600 mt-2">{error || 'The requested summary could not be found.'}</p>
            <Link
              href="/summary"
              className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Summaries
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/summary"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Summaries
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{summary.title}</h1>
              <div className="flex items-center mt-2 space-x-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSummaryTypeColor(summary.summary_type)}`}>
                  {summary.summary_type.charAt(0).toUpperCase() + summary.summary_type.slice(1)}
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(summary.priority)}`}>
                  {typeof summary.priority === 'number' ?
                    (summary.priority >= 4 ? 'Urgent' : summary.priority >= 3 ? 'High' : summary.priority >= 2 ? 'Medium' : 'Low') :
                    (summary.priority as string).charAt(0).toUpperCase() + (summary.priority as string).slice(1)} Priority
                </span>
                <span className="flex items-center text-sm text-gray-500">
                  {summary.is_public ? (
                    <>
                      <GlobeAltIcon className="h-4 w-4 mr-1" />
                      Public
                    </>
                  ) : (
                    <>
                      <LockClosedIcon className="h-4 w-4 mr-1" />
                      Private
                    </>
                  )}
                </span>
                {summary.is_featured && (
                  <span className="flex items-center text-sm text-yellow-600">
                    <StarIcon className="h-4 w-4 mr-1" />
                    Featured
                  </span>
                )}
              </div>
            </div>
            
            {canEdit && (
              <div className="flex space-x-2">
                <Link
                  href={`/summary/${summary.id}/edit`}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </Link>
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Summary Details */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Summary Details</h2>
          </div>
          
          <div className="px-6 py-4 space-y-6">
            {/* Abstract */}
            {summary.abstract && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Abstract</h3>
                <p className="text-gray-900 bg-gray-50 p-4 rounded-md">{summary.abstract}</p>
              </div>
            )}

            {/* Content */}
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">Content</h3>
              <div className="prose max-w-none">
                <MarkdownRenderer content={summary.abstract} />
              </div>
            </div>

            {/* Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Entity Type</h3>
                <p className="text-gray-900">{summary.entity_type.charAt(0).toUpperCase() + summary.entity_type.slice(1)}</p>
              </div>

              {summary.entity_id && (
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Entity ID</h3>
                  <p className="text-gray-900">{summary.entity_id}</p>
                </div>
              )}

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Action Type</h3>
                <p className="text-gray-900">{summary.action_type.charAt(0).toUpperCase() + summary.action_type.slice(1)}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Publication Date
                </h3>
                <p className="text-gray-900">{formatDate(summary.publication_date)}</p>
              </div>
            </div>

            {/* Tags */}
            {summary.tags && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <TagIcon className="h-4 w-4 mr-2" />
                  Tags
                </h3>
                <div className="flex flex-wrap gap-2">
                  {summary.tags.split(',').map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {tag.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* External Link */}
            {summary.external_link && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <LinkIcon className="h-4 w-4 mr-2" />
                  External Link
                </h3>
                <a
                  href={summary.external_link}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary-600 hover:text-primary-500 underline"
                >
                  {summary.external_link}
                </a>
              </div>
            )}

            {/* Related Agency */}
            {summary.agency && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                  <BuildingOfficeIcon className="h-4 w-4 mr-2" />
                  Related Agency
                </h3>
                <Link
                  href={`/agencies/${parseInt(summary.agency.id.toString())}`}
                  className="text-primary-600 hover:text-primary-500 underline"
                >
                  {summary.agency.name}
                </Link>
              </div>
            )}

            {/* Related Category */}
            {summary.category && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Related Category</h3>
                <Link
                  href={`/categories/${summary.category.id}`}
                  className="text-primary-600 hover:text-primary-500 underline"
                >
                  {summary.category.name}
                </Link>
              </div>
            )}

            {/* Created By */}
            {summary.created_by && (
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Created By</h3>
                <p className="text-gray-900">
                  {summary.created_by.first_name} {summary.created_by.last_name} (@{summary.created_by.username})
                </p>
              </div>
            )}

            {/* Timestamps */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4 border-t border-gray-200">
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Created</h3>
                <p className="text-gray-900">{formatDate(summary.created_at)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h3>
                <p className="text-gray-900">{formatDate(summary.updated_at)}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <TrashIcon className="mx-auto h-12 w-12 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900 mt-2">Delete Summary</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Are you sure you want to delete this summary? This action cannot be undone.
                </p>
                <div className="flex justify-center space-x-3 mt-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                    disabled={deleteLoading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SummaryViewPage;
