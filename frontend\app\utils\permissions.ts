import { User, UserRole, Document } from '../types';

/**
 * Get the viewer level for a user role
 * @param role User role
 * @returns Viewer level (1-3)
 */
export function getUserViewerLevel(role: UserRole): number {
  switch (role) {
    case 'viewer_level_1':
      return 1; // Can only view public documents
    case 'viewer_level_2':
      return 2; // Can view public and restricted documents
    case 'viewer_level_3':
    case 'viewer':
      return 3; // Can view all documents including confidential
    case 'editor':
    case 'reviewer':
    case 'publisher':
    case 'admin':
      return 3; // Higher roles can view all documents
    default:
      return 1; // Default to lowest level
  }
}

/**
 * Check if a user can view a specific document based on visibility level
 * @param user User object (null for non-authenticated users)
 * @param document Document object
 * @returns True if user can view the document
 */
export function canViewDocument(user: User | null, document: Document): boolean {
  // Non-authenticated users can only view public documents
  if (!user) {
    return document.is_public && document.visibility_level === 1;
  }

  // Admin can view all documents
  if (user.role === 'admin') {
    return true;
  }

  // Check visibility level against user role
  const userViewerLevel = getUserViewerLevel(user.role);
  
  // User can view documents at or below their viewer level
  return document.visibility_level <= userViewerLevel;
}

/**
 * Get the display name for a viewer level
 * @param level Viewer level (1-3)
 * @returns Display name
 */
export function getViewerLevelDisplayName(level: number): string {
  switch (level) {
    case 1:
      return 'Public';
    case 2:
      return 'Restricted';
    case 3:
      return 'Confidential';
    default:
      return 'Unknown';
  }
}

/**
 * Get the display name for a user role
 * @param role User role
 * @returns Display name
 */
export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'admin':
      return 'Administrator';
    case 'editor':
      return 'Editor';
    case 'reviewer':
      return 'Reviewer';
    case 'publisher':
      return 'Publisher';
    case 'viewer':
      return 'Viewer (Legacy)';
    case 'viewer_level_1':
      return 'Viewer Level 1 (Public Only)';
    case 'viewer_level_2':
      return 'Viewer Level 2 (Public + Restricted)';
    case 'viewer_level_3':
      return 'Viewer Level 3 (All Documents)';
    default:
      return 'Unknown Role';
  }
}

/**
 * Check if a user has sufficient permissions for a specific action
 * @param user User object
 * @param action Action to check
 * @returns True if user has permission
 */
export function hasPermission(user: User | null, action: string): boolean {
  if (!user) return false;

  const roleHierarchy: Record<UserRole, number> = {
    'viewer_level_1': 1,
    'viewer_level_2': 2,
    'viewer_level_3': 3,
    'viewer': 3,
    'editor': 4,
    'reviewer': 5,
    'publisher': 6,
    'moderator': 6,
    'admin': 7,
  };

  const actionRequirements: Record<string, number> = {
    'view_public': 1,
    'view_restricted': 2,
    'view_confidential': 3,
    'create_document': 4,
    'edit_document': 4,
    'review_document': 5,
    'publish_document': 6,
    'admin_access': 7,
  };

  const userLevel = roleHierarchy[user.role] || 1;
  const requiredLevel = actionRequirements[action] || 7;

  return userLevel >= requiredLevel;
}

/**
 * Filter documents based on user's viewer level
 * @param documents Array of documents
 * @param user User object (null for non-authenticated users)
 * @returns Filtered array of documents
 */
export function filterDocumentsByViewerLevel(documents: Document[], user: User | null): Document[] {
  return documents.filter(document => canViewDocument(user, document));
}

/**
 * Check if a user can access a specific route
 * @param user User object (null for non-authenticated users)
 * @param route Route path
 * @returns True if user can access the route
 */
export function canAccessRoute(user: User | null, route: string): boolean {
  if (!user) {
    // Public routes that don't require authentication
    const publicRoutes = [
      '/',
      '/about',
      '/contact',
      '/help',
      '/search',
      '/documents',
      '/regulations',
      '/agencies',
      '/categories',
      '/summary',
      '/calendar',
      '/proceedings',
      '/login',
      '/register',
      '/forgot-password',
      '/reset-password',
      '/verify-email'
    ];

    return publicRoutes.some(publicRoute =>
      route === publicRoute ||
      (publicRoute !== '/' && route.startsWith(publicRoute))
    );
  }

  // Route-specific permission requirements
  const routePermissions: Record<string, string> = {
    '/dashboard': 'view_public',
    '/profile': 'view_public',
    '/admin': 'admin_access',
    '/admin/documents': 'edit_document',
    '/admin/categories': 'edit_document',
    '/admin/agencies': 'admin_access',
    '/admin/proceedings': 'admin_access',
    '/admin/users': 'admin_access',
    '/admin/roles': 'admin_access',
    '/admin/analytics': 'admin_access',
  };

  // Check if route requires specific permission
  for (const [routePath, permission] of Object.entries(routePermissions)) {
    if (route.startsWith(routePath)) {
      return hasPermission(user, permission);
    }
  }

  // Default to allowing access for authenticated users to unspecified routes
  return true;
}

/**
 * Check if a navigation item should be shown to the user
 * @param user User object (null for non-authenticated users)
 * @param navItem Navigation item identifier
 * @returns True if navigation item should be shown
 */
export function shouldShowNavItem(user: User | null, navItem: string): boolean {
  if (!user) return false;

  const navPermissions: Record<string, string> = {
    'dashboard': 'view_public',
    'management': 'edit_document',
    'admin': 'admin_access',
    'documents': 'edit_document',
    'categories': 'edit_document',
    'agencies': 'admin_access',
    'proceedings': 'admin_access',
    'users': 'admin_access',
    'roles': 'admin_access',
    'analytics': 'admin_access',
  };

  const requiredPermission = navPermissions[navItem];
  if (!requiredPermission) return true; // Public nav item

  return hasPermission(user, requiredPermission);
}
