-- Advanced Workflow Engine Migration
-- This migration creates tables for sophisticated workflow automation with conditional routing,
-- parallel approvals, escalation rules, and SLA tracking

-- Workflow Templates Table
CREATE TABLE IF NOT EXISTS workflow_templates (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Template identification
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(255),
    version VARCHAR(50) DEFAULT '1.0',
    type VARCHAR(50) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Template configuration
    default_sla_hours INTEGER DEFAULT 24,
    allow_parallel_steps BOOLEAN DEFAULT FALSE,
    require_all_approvals BOOLEAN DEFAULT TRUE,
    auto_advance_on_approval BOOLEAN DEFAULT TRUE,
    enable_escalation BOOLEAN DEFAULT TRUE,
    escalation_hours INTEGER DEFAULT 48,
    
    -- Template metadata
    created_by_id INTEGER NOT NULL REFERENCES users(id),
    tags TEXT,
    
    -- Template definition (JSON)
    step_definitions TEXT,
    condition_rules TEXT,
    escalation_rules TEXT,
    notification_rules TEXT,
    
    -- Usage statistics
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    success_rate DECIMAL(5,2) DEFAULT 0,
    avg_completion_hours INTEGER DEFAULT 0
);

-- Workflow Instances Table
CREATE TABLE IF NOT EXISTS workflow_instances (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Instance identification
    instance_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'draft',
    priority INTEGER DEFAULT 3,
    
    -- Template relationship
    template_id INTEGER NOT NULL REFERENCES workflow_templates(id),
    
    -- Target entity
    entity_type VARCHAR(100) NOT NULL,
    entity_id INTEGER NOT NULL,
    
    -- Workflow progress
    current_step_id INTEGER,
    completed_steps INTEGER DEFAULT 0,
    total_steps INTEGER NOT NULL,
    progress_percent INTEGER DEFAULT 0,
    
    -- Timing and SLA
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    due_at TIMESTAMP WITH TIME ZONE,
    sla_hours INTEGER,
    is_overdue BOOLEAN DEFAULT FALSE,
    escalation_level INTEGER DEFAULT 0,
    
    -- Workflow context
    initiated_by_id INTEGER NOT NULL REFERENCES users(id),
    assigned_to_id INTEGER REFERENCES users(id),
    
    -- Workflow data (JSON)
    input_data TEXT,
    current_data TEXT,
    output_data TEXT,
    variables TEXT,
    
    -- Metadata
    notes TEXT,
    cancellation_reason VARCHAR(500)
);

-- Workflow Steps Table
CREATE TABLE IF NOT EXISTS workflow_steps (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Step identification
    step_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    step_order INTEGER NOT NULL,
    
    -- Workflow relationship
    instance_id INTEGER NOT NULL REFERENCES workflow_instances(id) ON DELETE CASCADE,
    
    -- Step configuration
    is_required BOOLEAN DEFAULT TRUE,
    allow_skip BOOLEAN DEFAULT FALSE,
    require_comment BOOLEAN DEFAULT FALSE,
    auto_complete BOOLEAN DEFAULT FALSE,
    parallel_group VARCHAR(100),
    
    -- Conditions and rules (JSON)
    pre_conditions TEXT,
    post_conditions TEXT,
    validation_rules TEXT,
    action_definition TEXT,
    
    -- Assignment and approval
    assigned_to_id INTEGER REFERENCES users(id),
    assigned_group_id INTEGER,
    required_approvers INTEGER DEFAULT 1,
    received_approvals INTEGER DEFAULT 0,
    
    -- Timing and SLA
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    due_at TIMESTAMP WITH TIME ZONE,
    sla_hours INTEGER DEFAULT 24,
    is_overdue BOOLEAN DEFAULT FALSE,
    
    -- Step execution
    attempt_count INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    last_error TEXT,
    completed_by_id INTEGER REFERENCES users(id),
    completion_notes TEXT,
    
    -- Step data
    input_data TEXT,
    output_data TEXT
);

-- Workflow Approvals Table
CREATE TABLE IF NOT EXISTS workflow_approvals (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Approval identification
    step_id INTEGER NOT NULL REFERENCES workflow_steps(id) ON DELETE CASCADE,
    
    -- Approver information
    approver_id INTEGER NOT NULL REFERENCES users(id),
    approver_role VARCHAR(255),
    approval_order INTEGER DEFAULT 1,
    
    -- Approval details
    status VARCHAR(50) DEFAULT 'pending',
    decision VARCHAR(50),
    comments TEXT,
    approved_at TIMESTAMP WITH TIME ZONE,
    requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    due_at TIMESTAMP WITH TIME ZONE,
    
    -- Delegation
    delegated_to_id INTEGER REFERENCES users(id),
    delegated_at TIMESTAMP WITH TIME ZONE,
    delegation_reason TEXT,
    
    -- Metadata
    ip_address INET,
    user_agent TEXT,
    approval_data TEXT
);

-- Workflow Escalations Table
CREATE TABLE IF NOT EXISTS workflow_escalations (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Escalation target
    instance_id INTEGER REFERENCES workflow_instances(id) ON DELETE CASCADE,
    step_id INTEGER REFERENCES workflow_steps(id) ON DELETE CASCADE,
    
    -- Escalation configuration
    trigger_type VARCHAR(50) NOT NULL,
    trigger_after INTEGER,
    escalation_level INTEGER NOT NULL,
    max_level INTEGER DEFAULT 3,
    
    -- Escalation targets
    escalate_to_id INTEGER REFERENCES users(id),
    escalate_to_group VARCHAR(255),
    escalate_to_role VARCHAR(255),
    
    -- Escalation execution
    is_triggered BOOLEAN DEFAULT FALSE,
    triggered_at TIMESTAMP WITH TIME ZONE,
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolution VARCHAR(500),
    escalated_by_id INTEGER REFERENCES users(id),
    
    -- Escalation actions (JSON)
    actions TEXT,
    notification_template TEXT,
    
    -- Metadata
    reason VARCHAR(500),
    notes TEXT
);

-- Workflow Notifications Table
CREATE TABLE IF NOT EXISTS workflow_notifications (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Notification target
    instance_id INTEGER NOT NULL REFERENCES workflow_instances(id) ON DELETE CASCADE,
    step_id INTEGER REFERENCES workflow_steps(id) ON DELETE CASCADE,
    
    -- Notification details
    type VARCHAR(50) NOT NULL,
    event VARCHAR(100) NOT NULL,
    recipients TEXT,
    subject VARCHAR(500),
    message TEXT,
    template VARCHAR(255),
    
    -- Delivery tracking
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    failure_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    
    -- Metadata
    notification_data TEXT,
    external_id VARCHAR(255)
);

-- Workflow Audit Logs Table
CREATE TABLE IF NOT EXISTS workflow_audit_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit target
    instance_id INTEGER NOT NULL REFERENCES workflow_instances(id) ON DELETE CASCADE,
    step_id INTEGER REFERENCES workflow_steps(id) ON DELETE CASCADE,
    
    -- Audit details
    action VARCHAR(255) NOT NULL,
    description TEXT,
    user_id INTEGER REFERENCES users(id),
    
    -- Technical details
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    
    -- Change tracking
    old_values TEXT,
    new_values TEXT,
    change_set TEXT,
    
    -- Additional metadata
    metadata TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workflow_templates_type ON workflow_templates(type);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_is_active ON workflow_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_created_by_id ON workflow_templates(created_by_id);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_category ON workflow_templates(category);
CREATE INDEX IF NOT EXISTS idx_workflow_templates_deleted_at ON workflow_templates(deleted_at);

CREATE INDEX IF NOT EXISTS idx_workflow_instances_instance_id ON workflow_instances(instance_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_template_id ON workflow_instances(template_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_entity ON workflow_instances(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_initiated_by_id ON workflow_instances(initiated_by_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_assigned_to_id ON workflow_instances(assigned_to_id);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_due_at ON workflow_instances(due_at);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_is_overdue ON workflow_instances(is_overdue);
CREATE INDEX IF NOT EXISTS idx_workflow_instances_deleted_at ON workflow_instances(deleted_at);

CREATE INDEX IF NOT EXISTS idx_workflow_steps_instance_id ON workflow_steps(instance_id);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_status ON workflow_steps(status);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_type ON workflow_steps(type);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_assigned_to_id ON workflow_steps(assigned_to_id);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_step_order ON workflow_steps(step_order);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_due_at ON workflow_steps(due_at);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_is_overdue ON workflow_steps(is_overdue);
CREATE INDEX IF NOT EXISTS idx_workflow_steps_deleted_at ON workflow_steps(deleted_at);

CREATE INDEX IF NOT EXISTS idx_workflow_approvals_step_id ON workflow_approvals(step_id);
CREATE INDEX IF NOT EXISTS idx_workflow_approvals_approver_id ON workflow_approvals(approver_id);
CREATE INDEX IF NOT EXISTS idx_workflow_approvals_status ON workflow_approvals(status);
CREATE INDEX IF NOT EXISTS idx_workflow_approvals_due_at ON workflow_approvals(due_at);
CREATE INDEX IF NOT EXISTS idx_workflow_approvals_deleted_at ON workflow_approvals(deleted_at);

CREATE INDEX IF NOT EXISTS idx_workflow_escalations_instance_id ON workflow_escalations(instance_id);
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_step_id ON workflow_escalations(step_id);
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_trigger_type ON workflow_escalations(trigger_type);
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_is_triggered ON workflow_escalations(is_triggered);
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_escalate_to_id ON workflow_escalations(escalate_to_id);
CREATE INDEX IF NOT EXISTS idx_workflow_escalations_deleted_at ON workflow_escalations(deleted_at);

CREATE INDEX IF NOT EXISTS idx_workflow_notifications_instance_id ON workflow_notifications(instance_id);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_step_id ON workflow_notifications(step_id);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_type ON workflow_notifications(type);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_status ON workflow_notifications(status);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_event ON workflow_notifications(event);
CREATE INDEX IF NOT EXISTS idx_workflow_notifications_deleted_at ON workflow_notifications(deleted_at);

CREATE INDEX IF NOT EXISTS idx_workflow_audit_logs_instance_id ON workflow_audit_logs(instance_id);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_logs_step_id ON workflow_audit_logs(step_id);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_logs_action ON workflow_audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_logs_user_id ON workflow_audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_logs_created_at ON workflow_audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_workflow_audit_logs_deleted_at ON workflow_audit_logs(deleted_at);

-- Add foreign key constraint for current_step_id
ALTER TABLE workflow_instances ADD CONSTRAINT fk_workflow_instances_current_step 
    FOREIGN KEY (current_step_id) REFERENCES workflow_steps(id);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_workflow_templates_updated_at BEFORE UPDATE ON workflow_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_instances_updated_at BEFORE UPDATE ON workflow_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_steps_updated_at BEFORE UPDATE ON workflow_steps FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_approvals_updated_at BEFORE UPDATE ON workflow_approvals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_escalations_updated_at BEFORE UPDATE ON workflow_escalations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_notifications_updated_at BEFORE UPDATE ON workflow_notifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_audit_logs_updated_at BEFORE UPDATE ON workflow_audit_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample workflow templates
INSERT INTO workflow_templates (
    name,
    description,
    category,
    type,
    default_sla_hours,
    allow_parallel_steps,
    require_all_approvals,
    enable_escalation,
    escalation_hours,
    created_by_id,
    step_definitions,
    escalation_rules
) VALUES 
(
    'Document Review and Approval',
    'Standard document review workflow with sequential approvals',
    'Document Management',
    'sequential',
    48,
    FALSE,
    TRUE,
    TRUE,
    72,
    1,
    '[{"id":"review","name":"Initial Review","type":"review","order":1},{"id":"approve","name":"Final Approval","type":"approval","order":2}]',
    '[{"level":1,"trigger_after":48,"escalate_to_role":"manager"},{"level":2,"trigger_after":72,"escalate_to_role":"director"}]'
),
(
    'Parallel Approval Workflow',
    'Workflow with parallel approval steps for faster processing',
    'Document Management',
    'parallel',
    24,
    TRUE,
    FALSE,
    TRUE,
    48,
    1,
    '[{"id":"legal_review","name":"Legal Review","type":"approval","order":1,"parallel_group":"approvals"},{"id":"technical_review","name":"Technical Review","type":"approval","order":1,"parallel_group":"approvals"},{"id":"final_sign","name":"Final Signature","type":"approval","order":2}]',
    '[{"level":1,"trigger_after":24,"escalate_to_role":"senior_manager"}]'
),
(
    'Conditional Document Processing',
    'Workflow with conditional routing based on document type and content',
    'Document Management',
    'conditional',
    36,
    TRUE,
    TRUE,
    TRUE,
    60,
    1,
    '[{"id":"classify","name":"Document Classification","type":"action","order":1},{"id":"route","name":"Conditional Routing","type":"condition","order":2},{"id":"standard_review","name":"Standard Review","type":"review","order":3,"condition":"document_type==standard"},{"id":"legal_review","name":"Legal Review","type":"review","order":3,"condition":"document_type==legal"}]',
    '[{"level":1,"trigger_after":36,"escalate_to_role":"workflow_admin"}]'
) ON CONFLICT DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE workflow_templates IS 'Reusable workflow templates with step definitions and rules';
COMMENT ON TABLE workflow_instances IS 'Active workflow instances with progress tracking and SLA monitoring';
COMMENT ON TABLE workflow_steps IS 'Individual steps within workflow instances with approval and execution tracking';
COMMENT ON TABLE workflow_approvals IS 'Approval requests and responses within workflow steps';
COMMENT ON TABLE workflow_escalations IS 'Escalation rules and triggered escalations for overdue workflows';
COMMENT ON TABLE workflow_notifications IS 'Notifications sent during workflow execution';
COMMENT ON TABLE workflow_audit_logs IS 'Complete audit trail for all workflow operations';
