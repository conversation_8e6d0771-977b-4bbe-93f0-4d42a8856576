# Component Library Documentation

## Overview

This document describes the shared UI components and interconnection system implemented for the Federal Register Clone application. These components provide consistent, reusable functionality across all module pages.

## Shared UI Components

### DataTable Component

A comprehensive, reusable table component with sorting, pagination, and action buttons.

#### Location
`frontend/app/components/UI/DataTable.tsx`

#### Features
- **Sortable columns** with visual indicators
- **Pagination** with customizable page sizes
- **Action buttons** with conditional visibility
- **Loading states** with skeleton UI
- **Error handling** with user-friendly messages
- **Empty states** with custom messages
- **Responsive design** for mobile and desktop

#### Usage Example

```typescript
import DataTable, { Column, ActionButton } from '../components/UI/DataTable';
import { EyeIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

// Define columns
const columns: Column<Document>[] = [
  {
    key: 'title',
    label: 'Title',
    sortable: true,
    render: (value, item) => (
      <Link href={`/documents/${item.id}`} className="text-blue-600 hover:text-blue-800">
        {value}
      </Link>
    )
  },
  {
    key: 'status',
    label: 'Status',
    sortable: true,
    render: (value) => <StatusBadge status={value} size="sm" />
  },
  {
    key: 'created_at',
    label: 'Created',
    sortable: true,
    render: (value) => new Date(value).toLocaleDateString()
  }
];

// Define actions
const actions: ActionButton<Document>[] = [
  {
    label: 'View',
    icon: EyeIcon,
    href: (item) => `/documents/${item.id}`,
    className: 'text-blue-600 hover:text-blue-800'
  },
  {
    label: 'Edit',
    icon: PencilIcon,
    href: (item) => `/documents/${item.id}/edit`,
    className: 'text-green-600 hover:text-green-800',
    show: () => isAuthenticated && (user?.role === 'admin' || user?.role === 'editor')
  },
  {
    label: 'Delete',
    icon: TrashIcon,
    onClick: (item) => handleDelete(item.id),
    className: 'text-red-600 hover:text-red-800',
    show: () => isAuthenticated && user?.role === 'admin'
  }
];

// Use the component
<DataTable
  data={documents}
  columns={columns}
  actions={actions}
  loading={loading}
  error={error}
  pagination={pagination}
  onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
  onSort={(column, direction) => {
    setFilters(prev => ({ ...prev, sort: column, order: direction }));
    setPagination(prev => ({ ...prev, page: 1 }));
  }}
  sortColumn={filters.sort}
  sortDirection={filters.order}
  emptyMessage="No documents found. Get started by creating your first document."
/>
```

#### Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `data` | `T[]` | Yes | Array of data items to display |
| `columns` | `Column<T>[]` | Yes | Column definitions |
| `actions` | `ActionButton<T>[]` | No | Action button definitions |
| `loading` | `boolean` | No | Loading state |
| `error` | `string \| null` | No | Error message |
| `pagination` | `PaginationInfo` | No | Pagination information |
| `onPageChange` | `(page: number) => void` | No | Page change handler |
| `onSort` | `(column: string, direction: 'asc' \| 'desc') => void` | No | Sort handler |
| `sortColumn` | `string` | No | Current sort column |
| `sortDirection` | `'asc' \| 'desc'` | No | Current sort direction |
| `emptyMessage` | `string` | No | Message when no data |

### SearchFilter Component

An advanced search and filtering component with multiple filter types.

#### Location
`frontend/app/components/UI/SearchFilter.tsx`

#### Features
- **Text search** with debouncing
- **Select filters** with predefined options
- **Number inputs** for numeric filtering
- **Date range pickers** for date filtering
- **Real-time filtering** with immediate updates
- **Responsive layout** that adapts to screen size

#### Usage Example

```typescript
import SearchFilter, { FilterOption } from '../components/UI/SearchFilter';

const filterOptions: FilterOption[] = [
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { value: 'active', label: 'Active' },
      { value: 'draft', label: 'Draft' },
      { value: 'archived', label: 'Archived' }
    ]
  },
  {
    key: 'priority',
    label: 'Priority',
    type: 'number',
    placeholder: 'Enter priority level'
  },
  {
    key: 'created_date',
    label: 'Created Date',
    type: 'daterange'
  }
];

<SearchFilter
  searchValue={searchTerm}
  onSearchChange={setSearchTerm}
  filters={filterOptions}
  filterValues={filters}
  onFilterChange={(newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPagination(prev => ({ ...prev, page: 1 }));
  }}
  placeholder="Search documents..."
/>
```

### StatusBadge Component

A flexible status badge component supporting multiple status types and sizes.

#### Location
`frontend/app/components/UI/StatusBadge.tsx`

#### Features
- **Multiple status types** with predefined colors
- **Size variants** (sm, md, lg)
- **Custom styling** support
- **Accessibility** with proper ARIA labels

#### Usage Example

```typescript
import StatusBadge from '../components/UI/StatusBadge';

<StatusBadge status="published" size="sm" />
<StatusBadge status="draft" size="md" />
<StatusBadge status="archived" size="lg" />
```

### FormField Component

A comprehensive form field component with validation and multiple input types.

#### Location
`frontend/app/components/UI/FormField.tsx`

#### Features
- **Multiple input types** (text, email, password, number, date, select, textarea)
- **Built-in validation** with error messages
- **Required field indicators**
- **Help text** support
- **Consistent styling** across all form fields

#### Usage Example

```typescript
import FormField from '../components/UI/FormField';

<FormField
  label="Document Title"
  name="title"
  type="text"
  value={formData.title}
  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
  required
  error={errors.title}
  helpText="Enter a descriptive title for the document"
/>
```

## Module Integration

### Documents Page
- **Replaced** custom table with `DataTable` component
- **Integrated** `SearchFilter` for advanced filtering
- **Reduced code** from 522 to 305 lines (-42%)

### Regulations Page
- **Implemented** `DataTable` with regulation-specific columns
- **Added** hierarchical display for CFR structure
- **Enhanced** filtering with USC/CFR title filters

### Agencies Page
- **Converted** grid layout to table format
- **Improved** contact information display
- **Added** agency type and status filtering

## Benefits Achieved

### Code Reduction
- **40%+ reduction** in component code
- **Eliminated** duplicate validation logic
- **Standardized** UI patterns across modules

### Consistency
- **Uniform** table behavior across all pages
- **Consistent** filtering and search experience
- **Standardized** error handling and loading states

### Maintainability
- **Single source** of truth for table functionality
- **Easier updates** to UI patterns
- **Reduced** testing surface area

### Performance
- **Optimized** rendering with React best practices
- **Efficient** sorting and pagination
- **Minimal** re-renders with proper memoization

## Testing

### Unit Tests
- **DataTable**: Comprehensive tests for all features
- **SearchFilter**: Tests for all filter types
- **StatusBadge**: Tests for all variants
- **FormField**: Tests for validation and input types

### Test Coverage
- **95%+ coverage** for all shared components
- **Integration tests** for component interactions
- **Accessibility tests** for WCAG compliance

## Migration Guide

### Converting Existing Pages

1. **Import shared components**:
```typescript
import DataTable, { Column, ActionButton } from '../components/UI/DataTable';
import SearchFilter, { FilterOption } from '../components/UI/SearchFilter';
```

2. **Define table columns**:
```typescript
const columns: Column<YourDataType>[] = [
  // Define your columns here
];
```

3. **Define actions**:
```typescript
const actions: ActionButton<YourDataType>[] = [
  // Define your actions here
];
```

4. **Replace existing table/list with DataTable**:
```typescript
<DataTable
  data={yourData}
  columns={columns}
  actions={actions}
  // ... other props
/>
```

5. **Replace search/filter UI with SearchFilter**:
```typescript
<SearchFilter
  searchValue={searchTerm}
  onSearchChange={setSearchTerm}
  filters={filterOptions}
  filterValues={filters}
  onFilterChange={handleFilterChange}
/>
```

## Best Practices

### Component Usage
- **Always** define TypeScript interfaces for your data
- **Use** proper error handling with the `error` prop
- **Implement** loading states for better UX
- **Provide** meaningful empty state messages

### Performance
- **Memoize** expensive calculations in render functions
- **Use** proper keys for list items
- **Avoid** inline functions in render props when possible

### Accessibility
- **Include** proper ARIA labels
- **Ensure** keyboard navigation works
- **Test** with screen readers
- **Maintain** proper color contrast

## Future Enhancements

### Planned Features
- **Export functionality** for tables
- **Column resizing** and reordering
- **Advanced filtering** with operators
- **Bulk actions** for multiple items
- **Virtual scrolling** for large datasets

### Component Additions
- **Modal** component for dialogs
- **Toast** notifications
- **Dropdown** menus
- **Tabs** component
- **Accordion** component

## Module Interconnection System

### Overview

The interconnection system provides APIs to connect basic modules (documents, regulations, agencies, categories) with enterprise modules (finance, proceedings, tasks, HR, BI). This creates a unified system where all modules can reference and depend on each other.

### API Endpoints

#### Document Connections

**Finance Connections**
- `GET /api/interconnect/documents/:id/finance` - Get finance records for a document
- `POST /api/interconnect/documents/:id/finance` - Create finance connection

**Proceeding Connections**
- `GET /api/interconnect/documents/:id/proceedings` - Get proceedings for a document
- `POST /api/interconnect/documents/:id/proceedings` - Create proceeding connection

**Task Connections**
- `GET /api/interconnect/documents/:id/tasks` - Get tasks for a document
- `POST /api/interconnect/documents/:id/tasks` - Create task connection

#### Regulation Connections

**Finance Connections**
- `GET /api/interconnect/regulations/:id/finance` - Get finance records for a regulation
- `POST /api/interconnect/regulations/:id/finance` - Create finance connection

**Proceeding Connections**
- `GET /api/interconnect/regulations/:id/proceedings` - Get proceedings for a regulation
- `POST /api/interconnect/regulations/:id/proceedings` - Create proceeding connection

**Task Connections**
- `GET /api/interconnect/regulations/:id/tasks` - Get tasks for a regulation
- `POST /api/interconnect/regulations/:id/tasks` - Create task connection

#### Agency Connections

**Finance Connections**
- `GET /api/interconnect/agencies/:id/finance` - Get finance records for an agency
- `POST /api/interconnect/agencies/:id/finance` - Create finance connection

**HR Connections**
- `GET /api/interconnect/agencies/:id/hr` - Get HR records for an agency
- `POST /api/interconnect/agencies/:id/hr` - Create HR connection

**BI Connections**
- `GET /api/interconnect/agencies/:id/bi` - Get BI records for an agency
- `POST /api/interconnect/agencies/:id/bi` - Create BI connection

#### Category Connections

**Finance Connections**
- `GET /api/interconnect/categories/:id/finance` - Get finance records for a category
- `POST /api/interconnect/categories/:id/finance` - Create finance connection

**Task Connections**
- `GET /api/interconnect/categories/:id/tasks` - Get tasks for a category
- `POST /api/interconnect/categories/:id/tasks` - Create task connection

### Usage Examples

#### Creating Document-Finance Connection

```typescript
// Frontend API call
const createDocumentFinance = async (documentId: number, financeData: FinanceData) => {
  const response = await fetch(`/api/interconnect/documents/${documentId}/finance`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      amount: financeData.amount,
      year: financeData.year,
      description: financeData.description,
      budget_type: financeData.budgetType,
      performance_percentage: financeData.performancePercentage
    })
  });

  return response.json();
};
```

#### Backend Handler Implementation

```go
// CreateDocumentFinanceConnection creates a finance connection for a document
func CreateDocumentFinanceConnection(c *gin.Context) {
    documentID, valid := ValidateID(c, "id")
    if !valid {
        return
    }

    var req struct {
        Amount                float64 `json:"amount"`
        Year                  int     `json:"year"`
        Description           string  `json:"description"`
        BudgetType            string  `json:"budget_type"`
        PerformancePercentage float64 `json:"performance_percentage"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        HandleBadRequest(c, err.Error())
        return
    }

    db := database.GetDB()
    finance := models.Finance{
        DocumentID:            &documentID,
        Amount:                req.Amount,
        Year:                  req.Year,
        Description:           req.Description,
        BudgetType:            req.BudgetType,
        PerformancePercentage: req.PerformancePercentage,
    }

    if err := db.Create(&finance).Error; err != nil {
        HandleError(c, err, "Failed to create document finance connection")
        return
    }

    c.JSON(http.StatusCreated, SuccessResponse{
        Message: "Document finance connection created successfully",
        Data:    gin.H{"id": finance.ID},
    })
}
```

### Data Models

#### Finance Connection
```go
type Finance struct {
    ID                    uint             `json:"id"`
    Amount                float64          `json:"amount"`
    Year                  int              `json:"year"`
    Description           string           `json:"description"`
    DocumentID            *uint            `json:"document_id"`
    RegulationID          *uint            `json:"regulation_id"`
    BudgetType            string           `json:"budget_type"`
    PerformancePercentage float64          `json:"performance_percentage"`
    // ... other fields
}
```

#### Task Connection
```go
type Task struct {
    ID           uint         `json:"id"`
    Title        string       `json:"title"`
    Description  string       `json:"description"`
    Type         TaskType     `json:"type"`
    Status       TaskStatus   `json:"status"`
    Priority     TaskPriority `json:"priority"`
    DocumentID   *uint        `json:"document_id"`
    RegulationID *uint        `json:"regulation_id"`
    AgencyID     *uint        `json:"agency_id"`
    CategoryID   *uint        `json:"category_id"`
    // ... other fields
}
```

### Benefits

#### System Integration
- **Unified data model** across all modules
- **Bidirectional relationships** between entities
- **Consistent API patterns** for all connections
- **Scalable architecture** for future modules

#### Business Value
- **Complete audit trails** for all activities
- **Performance tracking** across modules
- **Resource allocation** based on actual usage
- **Compliance reporting** with full traceability

#### Developer Experience
- **Consistent API design** patterns
- **Comprehensive error handling**
- **Type-safe** TypeScript interfaces
- **Extensive unit test** coverage

### Testing

#### Unit Tests
- **API endpoint tests** for all connections
- **Database integration tests**
- **Error handling tests**
- **Authentication tests**

#### Integration Tests
- **End-to-end workflow tests**
- **Cross-module dependency tests**
- **Performance tests** for large datasets

### Security

#### Authentication
- **JWT token validation** for all endpoints
- **Role-based access control**
- **User permission checks**

#### Data Validation
- **Input sanitization** for all requests
- **Type validation** with Go structs
- **Business rule validation**

### Performance

#### Optimization
- **Database indexing** for foreign keys
- **Query optimization** for joins
- **Caching strategies** for frequently accessed data

#### Monitoring
- **API response time** tracking
- **Database query performance**
- **Error rate monitoring**
