'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PresentationChartBarIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon,
  ArrowDownTrayIcon,
  ChartBarIcon,
  TableCellsIcon,
  ChartPieIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../../components/Layout/Layout';
import { useAuthStore } from '../../../../stores/authStore';
import apiService from '../../../../services/api';

interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'pie' | 'line' | 'bar';
  title: string;
  data: any;
  config: any;
  position: { x: number; y: number; width: number; height: number };
}

interface AnalyticsDashboard {
  id: number;
  name: string;
  description: string;
  dashboard_type: string;
  widgets: DashboardWidget[];
  layout: any;
  is_public: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

const DashboardViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const dashboardId = params.id as string;
  const { user } = useAuthStore();
  const [dashboard, setDashboard] = useState<AnalyticsDashboard | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (dashboardId) {
      fetchDashboard();
    }
  }, [dashboardId]);

  const fetchDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch dashboard data from analytics API
      const response = await apiService.get<{
        success: boolean;
        message: string;
        data: AnalyticsDashboard;
      }>(`/analytics/dashboards/${dashboardId}`);

      if (response.success && response.data) {
        setDashboard(response.data);
      } else {
        throw new Error(response.message || 'Failed to fetch dashboard');
      }
    } catch (err: any) {
      console.error('Error fetching dashboard:', err);
      setError(err.response?.data?.message || err.message || 'Failed to load dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setDeleteLoading(true);
      await apiService.delete(`/analytics/dashboards/${dashboardId}`);
      router.push('/admin/analytics/dashboards');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete dashboard');
    } finally {
      setDeleteLoading(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleExport = async () => {
    try {
      const response = await apiService.get(`/analytics/dashboards/${dashboardId}/export`);
      // Create and download the exported file
      const blob = new Blob([JSON.stringify(response, null, 2)], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `dashboard-${dashboardId}-export.json`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      alert('Dashboard exported successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to export dashboard');
    }
  };

  const handleRefresh = async () => {
    try {
      await apiService.post(`/analytics/dashboards/${dashboardId}/refresh`);
      await fetchDashboard(); // Reload the dashboard data
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to refresh dashboard');
    }
  };

  const getWidgetIcon = (type: string) => {
    switch (type) {
      case 'chart':
      case 'line':
      case 'bar':
        return <ChartBarIcon className="h-5 w-5" />;
      case 'pie':
        return <ChartPieIcon className="h-5 w-5" />;
      case 'table':
        return <TableCellsIcon className="h-5 w-5" />;
      default:
        return <PresentationChartBarIcon className="h-5 w-5" />;
    }
  };

  const renderWidget = (widget: DashboardWidget) => {
    switch (widget.type) {
      case 'metric':
        return (
          <div className="text-center">
            <div className="text-3xl font-bold text-gray-900 mb-2">
              {widget.data.value.toLocaleString()}
            </div>
            <div className={`text-sm font-medium ${
              widget.data.trend === 'up' ? 'text-green-600' : 
              widget.data.trend === 'down' ? 'text-red-600' : 'text-gray-600'
            }`}>
              {widget.data.change}
            </div>
          </div>
        );
      case 'chart':
      case 'line':
      case 'bar':
        return (
          <div className="h-full flex items-center justify-center bg-gray-50 rounded">
            <div className="text-center text-gray-500">
              <ChartBarIcon className="h-12 w-12 mx-auto mb-2" />
              <p>Chart Visualization</p>
              <p className="text-xs">({widget.data.datasets[0].data.length} data points)</p>
            </div>
          </div>
        );
      case 'pie':
        return (
          <div className="h-full flex items-center justify-center bg-gray-50 rounded">
            <div className="text-center text-gray-500">
              <ChartPieIcon className="h-12 w-12 mx-auto mb-2" />
              <p>Pie Chart</p>
              <p className="text-xs">({widget.data.labels.length} categories)</p>
            </div>
          </div>
        );
      case 'table':
        return (
          <div className="h-full flex items-center justify-center bg-gray-50 rounded">
            <div className="text-center text-gray-500">
              <TableCellsIcon className="h-12 w-12 mx-auto mb-2" />
              <p>Data Table</p>
            </div>
          </div>
        );
      default:
        return (
          <div className="h-full flex items-center justify-center bg-gray-50 rounded">
            <div className="text-center text-gray-500">
              <PresentationChartBarIcon className="h-12 w-12 mx-auto mb-2" />
              <p>Widget</p>
            </div>
          </div>
        );
    }
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view analytics dashboards.</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !dashboard) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Dashboard Not Found</h1>
            <p className="text-gray-600 mt-2">{error || 'The requested dashboard could not be found.'}</p>
            <Link
              href="/admin/analytics/dashboards"
              className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Dashboards
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/admin/analytics/dashboards"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Dashboards
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{dashboard.name}</h1>
              <div className="flex items-center mt-2 space-x-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {dashboard.dashboard_type}
                </span>
                {dashboard.is_public && (
                  <span className="flex items-center text-sm text-green-600">
                    <ShareIcon className="h-4 w-4 mr-1" />
                    Public
                  </span>
                )}
                <span className="text-sm text-gray-500">
                  {dashboard.widgets.length} widgets
                </span>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <button
                onClick={handleExport}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                Export
              </button>
              <Link
                href={`/admin/analytics/dashboards/${dashboard.id}/edit`}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit
              </Link>
              <button
                onClick={() => setShowDeleteConfirm(true)}
                className="inline-flex items-center px-3 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-white hover:bg-red-50"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                Delete
              </button>
            </div>
          </div>
        </div>

        {/* Dashboard Description */}
        {dashboard.description && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-gray-700">{dashboard.description}</p>
          </div>
        )}

        {/* Dashboard Grid */}
        <div className="grid grid-cols-12 gap-4 auto-rows-fr">
          {dashboard.widgets.map((widget) => (
            <div
              key={widget.id}
              className="bg-white rounded-lg shadow p-4 border"
              style={{
                gridColumn: `span ${widget.position.width}`,
                gridRow: `span ${widget.position.height}`,
                minHeight: `${widget.position.height * 100}px`
              }}
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  {getWidgetIcon(widget.type)}
                  <span className="ml-2">{widget.title}</span>
                </h3>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {widget.type}
                </span>
              </div>
              <div className="h-full">
                {renderWidget(widget)}
              </div>
            </div>
          ))}
        </div>

        {/* Dashboard Info */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Dashboard Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Layout</h4>
              <p className="text-gray-900">{dashboard.layout.columns} × {dashboard.layout.rows} grid</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Created</h4>
              <p className="text-gray-900">{new Date(dashboard.created_at).toLocaleDateString()}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Last Updated</h4>
              <p className="text-gray-900">{new Date(dashboard.updated_at).toLocaleDateString()}</p>
            </div>
          </div>
        </div>

        {/* Delete Confirmation Modal */}
        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
              <div className="mt-3 text-center">
                <TrashIcon className="mx-auto h-12 w-12 text-red-600" />
                <h3 className="text-lg font-medium text-gray-900 mt-2">Delete Dashboard</h3>
                <p className="text-sm text-gray-500 mt-2">
                  Are you sure you want to delete this dashboard? This action cannot be undone.
                </p>
                <div className="flex justify-center space-x-3 mt-4">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                    disabled={deleteLoading}
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    disabled={deleteLoading}
                  >
                    {deleteLoading ? 'Deleting...' : 'Delete'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default DashboardViewPage;
