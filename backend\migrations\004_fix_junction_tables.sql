-- Fix junction tables to match GORM models
-- Add missing columns for soft deletes and proper primary keys

-- Fix document_category_assignments table
ALTER TABLE document_category_assignments 
DROP CONSTRAINT IF EXISTS document_category_assignments_pkey;

ALTER TABLE document_category_assignments 
ADD COLUMN IF NOT EXISTS id BIGSERIAL;

ALTER TABLE document_category_assignments 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

ALTER TABLE document_category_assignments 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

ALTER TABLE document_category_assignments 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ;

ALTER TABLE document_category_assignments 
ADD COLUMN IF NOT EXISTS assigned_by_id BIGINT REFERENCES users(id);

-- Set the new primary key
ALTER TABLE document_category_assignments 
ADD PRIMARY KEY (id);

-- Add unique constraint for the combination
ALTER TABLE document_category_assignments 
ADD CONSTRAINT unique_document_category UNIQUE (document_id, category_id);

-- Fix document_tag_assignments table
ALTER TABLE document_tag_assignments 
DROP CONSTRAINT IF EXISTS document_tag_assignments_pkey;

ALTER TABLE document_tag_assignments 
ADD COLUMN IF NOT EXISTS id BIGSERIAL;

ALTER TABLE document_tag_assignments 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

ALTER TABLE document_tag_assignments 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

ALTER TABLE document_tag_assignments 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ;

ALTER TABLE document_tag_assignments 
ADD COLUMN IF NOT EXISTS assigned_by_id BIGINT REFERENCES users(id);

-- Set the new primary key
ALTER TABLE document_tag_assignments 
ADD PRIMARY KEY (id);

-- Add unique constraint for the combination
ALTER TABLE document_tag_assignments 
ADD CONSTRAINT unique_document_tag UNIQUE (document_id, tag_id);

-- Fix document_subject_assignments table
ALTER TABLE document_subject_assignments 
DROP CONSTRAINT IF EXISTS document_subject_assignments_pkey;

ALTER TABLE document_subject_assignments 
ADD COLUMN IF NOT EXISTS id BIGSERIAL;

ALTER TABLE document_subject_assignments 
ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

ALTER TABLE document_subject_assignments 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW();

ALTER TABLE document_subject_assignments 
ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMPTZ;

ALTER TABLE document_subject_assignments 
ADD COLUMN IF NOT EXISTS assigned_by_id BIGINT REFERENCES users(id);

-- Set the new primary key
ALTER TABLE document_subject_assignments 
ADD PRIMARY KEY (id);

-- Add unique constraint for the combination
ALTER TABLE document_subject_assignments 
ADD CONSTRAINT unique_document_subject UNIQUE (document_id, subject_id);

-- Create indexes for soft deletes
CREATE INDEX IF NOT EXISTS idx_doc_cat_assignments_deleted_at ON document_category_assignments(deleted_at);
CREATE INDEX IF NOT EXISTS idx_doc_tag_assignments_deleted_at ON document_tag_assignments(deleted_at);
CREATE INDEX IF NOT EXISTS idx_doc_subject_assignments_deleted_at ON document_subject_assignments(deleted_at);
