package tests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

type DocumentProcessingServiceTestSuite struct {
	suite.Suite
	db                        *gorm.DB
	documentProcessingService *services.DocumentProcessingService
	testDocument              *models.Document
}

func TestDocumentProcessingServiceSuite(t *testing.T) {
	suite.Run(t, new(DocumentProcessingServiceTestSuite))
}

func (suite *DocumentProcessingServiceTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Set global database
	database.DB = db
	suite.db = db

	// Run migrations
	err = db.AutoMigrate(
		&models.User{},
		&models.Document{},
		&models.DocumentProcessingJob{},
		&models.DocumentClassification{},
		&models.DocumentMetadata{},
		&models.DocumentEntity{},
		&models.Agency{},
	)
	suite.Require().NoError(err)

	// Initialize service
	suite.documentProcessingService = services.NewDocumentProcessingService(db)

	// Create test data
	suite.createTestData()
}

func (suite *DocumentProcessingServiceTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *DocumentProcessingServiceTestSuite) createTestData() {
	// Create test user
	user := &models.User{
		Email:    "<EMAIL>",
		Username: "testuser",
		Role:     models.RoleEditor,
	}
	suite.db.Create(user)

	// Create test agency
	agency := &models.Agency{
		Name:        "Test Agency",
		Acronym:     "TA",
		Description: "Test Agency for testing",
	}
	suite.db.Create(agency)

	// Create test document
	suite.testDocument = &models.Document{
		Title:       "Test Document",
		Content:     "This is a test document for processing validation and classification testing.",
		Type:        "regulation",
		Status:      "draft",
		AgencyID:    &agency.ID,
		CreatedByID: user.ID,
	}
	suite.db.Create(suite.testDocument)
}

func (suite *DocumentProcessingServiceTestSuite) TestCreateProcessingJob() {
	testCases := []struct {
		name           string
		documentID     uint
		processingType models.ProcessingType
		expectError    bool
	}{
		{
			name:           "Valid OCR job",
			documentID:     suite.testDocument.ID,
			processingType: models.ProcessingTypeOCR,
			expectError:    false,
		},
		{
			name:           "Valid metadata extraction job",
			documentID:     suite.testDocument.ID,
			processingType: models.ProcessingTypeMetadataExtract,
			expectError:    false,
		},
		{
			name:           "Valid classification job",
			documentID:     suite.testDocument.ID,
			processingType: models.ProcessingTypeClassification,
			expectError:    false,
		},
		{
			name:           "Invalid document ID",
			documentID:     99999,
			processingType: models.ProcessingTypeOCR,
			expectError:    true,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			jobID, err := suite.documentProcessingService.CreateProcessingJob(
				tc.documentID,
				tc.processingType,
				map[string]interface{}{"test": "parameter"},
			)

			if tc.expectError {
				assert.Error(suite.T(), err)
				assert.Empty(suite.T(), jobID)
			} else {
				assert.NoError(suite.T(), err)
				assert.NotEmpty(suite.T(), jobID)

				// Verify job was created in database
				var job models.DocumentProcessingJob
				err = suite.db.Where("job_id = ?", jobID).First(&job).Error
				assert.NoError(suite.T(), err)
				assert.Equal(suite.T(), tc.documentID, job.DocumentID)
				assert.Equal(suite.T(), tc.processingType, job.Type)
				assert.Equal(suite.T(), models.ProcessingStatusPending, job.Status)
			}
		})
	}
}

func (suite *DocumentProcessingServiceTestSuite) TestGetJobStatus() {
	// Create a test job
	jobID, err := suite.documentProcessingService.CreateProcessingJob(
		suite.testDocument.ID,
		models.ProcessingTypeOCR,
		map[string]interface{}{},
	)
	suite.Require().NoError(err)

	testCases := []struct {
		name        string
		jobID       string
		expectError bool
	}{
		{
			name:        "Valid job ID",
			jobID:       jobID,
			expectError: false,
		},
		{
			name:        "Invalid job ID",
			jobID:       "invalid-job-id",
			expectError: true,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			status, err := suite.documentProcessingService.GetJobStatus(tc.jobID)

			if tc.expectError {
				assert.Error(suite.T(), err)
				assert.Nil(suite.T(), status)
			} else {
				assert.NoError(suite.T(), err)
				assert.NotNil(suite.T(), status)
				assert.Equal(suite.T(), tc.jobID, status.JobID)
				assert.Equal(suite.T(), models.ProcessingStatusPending, status.Status)
			}
		})
	}
}

func (suite *DocumentProcessingServiceTestSuite) TestCompleteProcessingJob() {
	// Create a test job
	jobID, err := suite.documentProcessingService.CreateProcessingJob(
		suite.testDocument.ID,
		models.ProcessingTypeOCR,
		map[string]interface{}{},
	)
	suite.Require().NoError(err)

	// Test completing the job
	results := services.ProcessingResults{
		ExtractedText:  "Extracted text content",
		Confidence:     0.95,
		PageCount:      1,
		WordCount:      3,
		CharacterCount: 22,
		ExtractedMetadata: []services.MetadataResult{
			{
				Type:       "processing",
				Key:        "confidence",
				Value:      "0.95",
				Confidence: 0.95,
				Source:     "ocr_engine",
			},
		},
		OutputData: "Extracted text content",
	}

	err = suite.documentProcessingService.CompleteProcessingJob(jobID, results)
	assert.NoError(suite.T(), err)

	// Verify job status was updated
	status, err := suite.documentProcessingService.GetJobStatus(jobID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.ProcessingStatusCompleted, status.Status)
	assert.NotNil(suite.T(), status.CompletedAt)
	assert.Equal(suite.T(), "Extracted text content", status.OutputData)
}

func (suite *DocumentProcessingServiceTestSuite) TestFailProcessingJob() {
	// Create a test job
	jobID, err := suite.documentProcessingService.CreateProcessingJob(
		suite.testDocument.ID,
		models.ProcessingTypeOCR,
		map[string]interface{}{},
	)
	suite.Require().NoError(err)

	// Test failing the job
	errorMessage := "Processing failed due to invalid file format"
	errorDetails := "Detailed error information"

	err = suite.documentProcessingService.FailProcessingJob(jobID, errorMessage, errorDetails)
	assert.NoError(suite.T(), err)

	// Verify job status was updated
	status, err := suite.documentProcessingService.GetJobStatus(jobID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.ProcessingStatusFailed, status.Status)
	assert.NotNil(suite.T(), status.CompletedAt)
	assert.Equal(suite.T(), errorMessage, status.ErrorMessage)
	assert.Equal(suite.T(), errorDetails, status.ErrorDetails)
}

func (suite *DocumentProcessingServiceTestSuite) TestGetJobHistory() {
	// Create multiple test jobs
	jobIDs := make([]string, 3)
	for i := 0; i < 3; i++ {
		jobID, err := suite.documentProcessingService.CreateProcessingJob(
			suite.testDocument.ID,
			models.ProcessingTypeOCR,
			map[string]interface{}{},
		)
		suite.Require().NoError(err)
		jobIDs[i] = jobID
	}

	// Complete one job
	results := services.ProcessingResults{
		ExtractedText: "Test content",
		Confidence:    0.9,
		OutputData:    "Test content",
	}
	err := suite.documentProcessingService.CompleteProcessingJob(jobIDs[0], results)
	suite.Require().NoError(err)

	// Fail one job
	err = suite.documentProcessingService.FailProcessingJob(jobIDs[1], "Test error", "")
	suite.Require().NoError(err)

	// Get job history
	history, err := suite.documentProcessingService.GetJobHistory(suite.testDocument.ID, 10, 0)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), history, 3)

	// Verify jobs are ordered by creation time (newest first)
	for i := 0; i < len(history)-1; i++ {
		assert.True(suite.T(), history[i].CreatedAt.After(history[i+1].CreatedAt) ||
			history[i].CreatedAt.Equal(history[i+1].CreatedAt))
	}

	// Verify different statuses
	statuses := make(map[models.ProcessingStatus]bool)
	for _, job := range history {
		statuses[job.Status] = true
	}
	assert.True(suite.T(), statuses[models.ProcessingStatusCompleted])
	assert.True(suite.T(), statuses[models.ProcessingStatusFailed])
	assert.True(suite.T(), statuses[models.ProcessingStatusPending])
}

func (suite *DocumentProcessingServiceTestSuite) TestCancelProcessingJob() {
	// Create a test job
	jobID, err := suite.documentProcessingService.CreateProcessingJob(
		suite.testDocument.ID,
		models.ProcessingTypeOCR,
		map[string]interface{}{},
	)
	suite.Require().NoError(err)

	// Cancel the job
	err = suite.documentProcessingService.CancelProcessingJob(jobID)
	assert.NoError(suite.T(), err)

	// Verify job status was updated
	status, err := suite.documentProcessingService.GetJobStatus(jobID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), models.ProcessingStatusCancelled, status.Status)
	assert.NotNil(suite.T(), status.CompletedAt)
}

func (suite *DocumentProcessingServiceTestSuite) TestGetActiveJobs() {
	// Create multiple jobs with different statuses
	activeJobID, err := suite.documentProcessingService.CreateProcessingJob(
		suite.testDocument.ID,
		models.ProcessingTypeOCR,
		map[string]interface{}{},
	)
	suite.Require().NoError(err)

	completedJobID, err := suite.documentProcessingService.CreateProcessingJob(
		suite.testDocument.ID,
		models.ProcessingTypeMetadataExtract,
		map[string]interface{}{},
	)
	suite.Require().NoError(err)

	// Complete one job
	results := services.ProcessingResults{
		ExtractedText: "Test content",
		Confidence:    0.9,
		OutputData:    "Test content",
	}
	err = suite.documentProcessingService.CompleteProcessingJob(completedJobID, results)
	suite.Require().NoError(err)

	// Get active jobs
	activeJobs, err := suite.documentProcessingService.GetActiveJobs()
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), activeJobs)

	// Verify only pending/processing jobs are returned
	foundActiveJob := false
	for _, job := range activeJobs {
		assert.True(suite.T(), job.Status == models.ProcessingStatusPending ||
			job.Status == models.ProcessingStatusProcessing)
		if job.JobID == activeJobID {
			foundActiveJob = true
		}
		// Should not find completed job
		assert.NotEqual(suite.T(), completedJobID, job.JobID)
	}
	assert.True(suite.T(), foundActiveJob, "Should find the active job")
}

func (suite *DocumentProcessingServiceTestSuite) TestProcessingJobTimeout() {
	// Create a job and manually set it to processing with old timestamp
	jobID, err := suite.documentProcessingService.CreateProcessingJob(
		suite.testDocument.ID,
		models.ProcessingTypeOCR,
		map[string]interface{}{},
	)
	suite.Require().NoError(err)

	// Manually update job to processing status with old timestamp
	oldTime := time.Now().Add(-2 * time.Hour)
	err = suite.db.Model(&models.DocumentProcessingJob{}).
		Where("job_id = ?", jobID).
		Updates(map[string]interface{}{
			"status":     models.ProcessingStatusProcessing,
			"started_at": &oldTime,
		}).Error
	suite.Require().NoError(err)

	// Test timeout detection (this would typically be done by a background process)
	var job models.DocumentProcessingJob
	err = suite.db.Where("job_id = ?", jobID).First(&job).Error
	suite.Require().NoError(err)

	// Verify job is in processing state and started more than 1 hour ago
	assert.Equal(suite.T(), models.ProcessingStatusProcessing, job.Status)
	assert.True(suite.T(), time.Since(*job.StartedAt) > time.Hour)
}
