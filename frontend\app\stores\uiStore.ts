import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface UIState {
  // Theme
  theme: 'light' | 'dark';
  
  // Layout
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  
  // Loading states
  globalLoading: boolean;
  
  // Error handling
  error: string | null;
  
  // Search
  searchQuery: string;
  searchFilters: {
    agency?: string;
    category?: string;
    type?: string;
    status?: string;
    dateFrom?: string;
    dateTo?: string;
  };
  
  // Pagination
  currentPage: number;
  itemsPerPage: number;
  
  // Actions
  setTheme: (theme: 'light' | 'dark') => void;
  toggleTheme: () => void;
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  setMobileMenuOpen: (open: boolean) => void;
  toggleMobileMenu: () => void;
  setGlobalLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  setSearchQuery: (query: string) => void;
  setSearchFilters: (filters: Partial<UIState['searchFilters']>) => void;
  clearSearchFilters: () => void;
  setCurrentPage: (page: number) => void;
  setItemsPerPage: (itemsPerPage: number) => void;
  resetPagination: () => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      // Initial state
      theme: 'light',
      sidebarOpen: true,
      mobileMenuOpen: false,
      globalLoading: false,
      error: null,
      searchQuery: '',
      searchFilters: {},
      currentPage: 1,
      itemsPerPage: parseInt(process.env.NEXT_PUBLIC_DEFAULT_PAGE_SIZE || '25'),

      // Theme actions
      setTheme: (theme) => {
        set({ theme });
        
        // Apply theme to document
        if (typeof window !== 'undefined') {
          const root = window.document.documentElement;
          root.classList.remove('light', 'dark');
          root.classList.add(theme);
        }
      },

      toggleTheme: () => {
        const { theme, setTheme } = get();
        setTheme(theme === 'light' ? 'dark' : 'light');
      },

      // Layout actions
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      
      toggleSidebar: () => {
        const { sidebarOpen } = get();
        set({ sidebarOpen: !sidebarOpen });
      },

      setMobileMenuOpen: (open) => set({ mobileMenuOpen: open }),
      
      toggleMobileMenu: () => {
        const { mobileMenuOpen } = get();
        set({ mobileMenuOpen: !mobileMenuOpen });
      },

      // Loading actions
      setGlobalLoading: (loading) => set({ globalLoading: loading }),

      // Error actions
      setError: (error) => set({ error }),
      
      clearError: () => set({ error: null }),

      // Search actions
      setSearchQuery: (query) => {
        set({ searchQuery: query, currentPage: 1 });
      },

      setSearchFilters: (filters) => {
        const { searchFilters } = get();
        set({ 
          searchFilters: { ...searchFilters, ...filters },
          currentPage: 1 
        });
      },

      clearSearchFilters: () => {
        set({ 
          searchFilters: {},
          searchQuery: '',
          currentPage: 1 
        });
      },

      // Pagination actions
      setCurrentPage: (page) => set({ currentPage: page }),
      
      setItemsPerPage: (itemsPerPage) => {
        set({ itemsPerPage, currentPage: 1 });
      },

      resetPagination: () => set({ currentPage: 1 }),
    }),
    {
      name: 'ui-storage',
      partialize: (state) => ({
        theme: state.theme,
        sidebarOpen: state.sidebarOpen,
        itemsPerPage: state.itemsPerPage,
      }),
    }
  )
);

// Initialize theme on load
if (typeof window !== 'undefined') {
  const storedTheme = localStorage.getItem('ui-storage');
  if (storedTheme) {
    try {
      const parsed = JSON.parse(storedTheme);
      const theme = parsed.state?.theme || 'light';
      const root = window.document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(theme);
    } catch (error) {
      console.error('Failed to parse stored theme:', error);
    }
  }
}
