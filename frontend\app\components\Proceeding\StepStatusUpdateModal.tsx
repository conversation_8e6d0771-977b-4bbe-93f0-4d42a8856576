import React, { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface StepStatusUpdateModalProps {
  step: any;
  isOpen: boolean;
  onClose: () => void;
  onUpdate: (stepId: number, status: string, data: any) => Promise<void>;
}

const StepStatusUpdateModal: React.FC<StepStatusUpdateModalProps> = ({
  step,
  isOpen,
  onClose,
  onUpdate
}) => {
  const [status, setStatus] = useState(step?.status || 'not_started');
  const [progressPercent, setProgressPercent] = useState(step?.progress_percent || 0);
  const [completionEvidence, setCompletionEvidence] = useState(step?.completion_evidence || '');
  const [notes, setNotes] = useState(step?.notes || '');
  const [actualStartDate, setActualStartDate] = useState(
    step?.actual_start_date ? new Date(step.actual_start_date).toISOString().slice(0, 16) : ''
  );
  const [actualEndDate, setActualEndDate] = useState(
    step?.actual_end_date ? new Date(step.actual_end_date).toISOString().slice(0, 16) : ''
  );
  const [submitting, setSubmitting] = useState(false);

  if (!isOpen || !step) return null;

  const statusOptions = [
    { value: 'not_started', label: 'Not Started', color: 'gray' },
    { value: 'in_progress', label: 'In Progress', color: 'blue' },
    { value: 'under_review', label: 'Under Review', color: 'yellow' },
    { value: 'completed', label: 'Completed', color: 'green' },
    { value: 'failed', label: 'Failed', color: 'red' },
    { value: 'on_hold', label: 'On Hold', color: 'gray' },
    { value: 'skipped', label: 'Skipped', color: 'purple' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);
      
      const updateData: any = {
        status,
        progress_percent: progressPercent,
        notes
      };

      if (completionEvidence.trim()) {
        updateData.completion_evidence = completionEvidence;
      }

      if (actualStartDate) {
        updateData.actual_start_date = actualStartDate;
      }

      if (actualEndDate) {
        updateData.actual_end_date = actualEndDate;
      }

      // Auto-set progress based on status
      if (status === 'completed') {
        updateData.progress_percent = 100;
      } else if (status === 'not_started') {
        updateData.progress_percent = 0;
      }

      await onUpdate(step.id, status, updateData);
      onClose();
    } catch (error) {
      console.error('Failed to update step status:', error);
      alert('Failed to update step status. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getStatusColor = (statusValue: string) => {
    const option = statusOptions.find(opt => opt.value === statusValue);
    return option?.color || 'gray';
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Update Step Status: {step.name}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Step Info */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Step Order:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{step.step_order}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Type:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">{step.step_type}</span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Mandatory:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">
                  {step.is_mandatory ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Critical:</span>
                <span className="ml-2 text-gray-600 dark:text-gray-400">
                  {step.is_critical ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>

          {/* Status Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Status
            </label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              required
            >
              {statusOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Progress Percentage */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Progress Percentage
            </label>
            <div className="flex items-center space-x-4">
              <input
                type="range"
                min="0"
                max="100"
                value={progressPercent}
                onChange={(e) => setProgressPercent(Number(e.target.value))}
                className="flex-1"
                disabled={status === 'completed' || status === 'not_started'}
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 w-12">
                {progressPercent}%
              </span>
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Actual Start Date
              </label>
              <input
                type="datetime-local"
                value={actualStartDate}
                onChange={(e) => setActualStartDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Actual End Date
              </label>
              <input
                type="datetime-local"
                value={actualEndDate}
                onChange={(e) => setActualEndDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                disabled={status !== 'completed'}
              />
            </div>
          </div>

          {/* Completion Evidence */}
          {(status === 'completed' || status === 'under_review') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Completion Evidence
              </label>
              <textarea
                value={completionEvidence}
                onChange={(e) => setCompletionEvidence(e.target.value)}
                rows={3}
                placeholder="Provide evidence or documentation of completion..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                required={status === 'completed'}
              />
            </div>
          )}

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notes
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              placeholder="Add any additional notes or comments..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? 'Updating...' : 'Update Status'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StepStatusUpdateModal;
