package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/auth"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// UserRequest represents the request structure for creating users
type UserRequest struct {
	FirstName string `json:"first_name" binding:"required"`
	LastName  string `json:"last_name" binding:"required"`
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password,omitempty"`
	Role      string `json:"role"`
	IsActive  bool   `json:"is_active"`
	Language  string `json:"language"`
	Timezone  string `json:"timezone"`
	AgencyID  uint   `json:"agency_id"`
}

// UpdateUserRequest represents the request structure for updating users (partial updates allowed)
type UpdateUserRequest struct {
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Email     string `json:"email" binding:"omitempty,email"`
	Password  string `json:"password,omitempty"`
	Role      string `json:"role"`
	Title     string `json:"title"`
	IsActive  *bool  `json:"is_active"` // Pointer to allow distinguishing between false and not provided
	Language  string `json:"language"`
	Timezone  string `json:"timezone"`
	AgencyID  *uint  `json:"agency_id"` // Pointer to allow distinguishing between 0 and not provided
}

// GetUsers returns all users with pagination
func GetUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total users
	var total int64
	db.Model(&models.User{}).Count(&total)

	// Get users with pagination
	var users []models.User
	offset := (page - 1) * perPage
	if err := db.Preload("Agency").
		Order("first_name ASC, last_name ASC").
		Limit(perPage).
		Offset(offset).
		Find(&users).Error; err != nil {
		HandleInternalError(c, "Failed to fetch users: "+err.Error())
		return
	}

	// Convert to response format (exclude password)
	userResponses := make([]gin.H, len(users))
	for i, user := range users {
		userResponses[i] = gin.H{
			"id":           user.ID,
			"username":     user.Username,
			"email":        user.Email,
			"first_name":   user.FirstName,
			"last_name":    user.LastName,
			"role":         user.Role,
			"is_active":    user.IsActive,
			"is_verified":  user.IsVerified,
			"last_login":   user.LastLoginAt,
			"title":        user.Title,
			"department":   user.Department,
			"organization": user.Organization,
			"phone":        user.Phone,
			"bio":          user.Bio,
			"created_at":   user.CreatedAt,
			"updated_at":   user.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       userResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetUser returns a single user by ID
func GetUser(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user
	var user models.User
	if err := db.Preload("Agency").First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	response := gin.H{
		"id":           user.ID,
		"username":     user.Username,
		"email":        user.Email,
		"first_name":   user.FirstName,
		"last_name":    user.LastName,
		"role":         user.Role,
		"is_active":    user.IsActive,
		"is_verified":  user.IsVerified,
		"last_login":   user.LastLoginAt,
		"title":        user.Title,
		"department":   user.Department,
		"organization": user.Organization,
		"phone":        user.Phone,
		"bio":          user.Bio,
		"created_at":   user.CreatedAt,
		"updated_at":   user.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User retrieved successfully",
		Data:    response,
	})
}

// CreateUser creates a new user with comprehensive validation
func CreateUser(c *gin.Context) {
	var req UserRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if email already exists
	var existingUser models.User
	if err := db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Email already exists",
			Message: "A user with this email already exists",
		})
		return
	}

	// Create user
	user := &models.User{
		Username:     req.Email, // Use email as username
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Email:        req.Email,
		Role:         models.LegacyUserRole(req.Role),
		IsActive:     req.IsActive,
		Title:        req.FirstName + " " + req.LastName, // Default title
		Department:   "General",                          // Default department
		Organization: "System",                           // Default organization
		AgencyID:     &req.AgencyID,
	}

	// Hash password if provided
	if req.Password != "" {
		hashedPassword, err := auth.HashPassword(req.Password)
		if err != nil {
			HandleInternalError(c, "Failed to hash password: "+err.Error())
			return
		}
		user.PasswordHash = hashedPassword
	}

	if err := db.Create(user).Error; err != nil {
		HandleInternalError(c, "Failed to create user: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Agency").First(user, user.ID)

	response := gin.H{
		"id":           user.ID,
		"username":     user.Username,
		"email":        user.Email,
		"first_name":   user.FirstName,
		"last_name":    user.LastName,
		"role":         user.Role,
		"is_active":    user.IsActive,
		"is_verified":  user.IsVerified,
		"last_login":   user.LastLoginAt,
		"title":        user.Title,
		"department":   user.Department,
		"organization": user.Organization,
		"phone":        user.Phone,
		"bio":          user.Bio,
		"agency":       user.Agency,
		"created_at":   user.CreatedAt,
		"updated_at":   user.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "User created successfully",
		Data:    response,
	})
}

// UpdateUser updates an existing user
func UpdateUser(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req UpdateUserRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing user
	var user models.User
	if err := db.First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Check if email already exists (excluding current user)
	var existingUser models.User
	if err := db.Where("email = ? AND id != ?", req.Email, id).First(&existingUser).Error; err == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Email already exists",
			Message: "A user with this email already exists",
		})
		return
	}

	// Update user fields (only update fields that are provided)
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}
	if req.LastName != "" {
		user.LastName = req.LastName
	}
	if req.Email != "" {
		user.Email = req.Email
	}
	if req.Role != "" {
		user.Role = models.LegacyUserRole(req.Role)
	}
	if req.Title != "" {
		user.Title = req.Title
	}
	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}
	if req.AgencyID != nil {
		user.AgencyID = req.AgencyID
	}

	// Update password if provided
	if req.Password != "" {
		hashedPassword, err := auth.HashPassword(req.Password)
		if err != nil {
			HandleInternalError(c, "Failed to hash password: "+err.Error())
			return
		}
		user.PasswordHash = hashedPassword
	}

	if err := db.Save(&user).Error; err != nil {
		HandleInternalError(c, "Failed to update user: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Agency").First(&user, user.ID)

	response := gin.H{
		"id":           user.ID,
		"username":     user.Username,
		"email":        user.Email,
		"first_name":   user.FirstName,
		"last_name":    user.LastName,
		"role":         user.Role,
		"is_active":    user.IsActive,
		"is_verified":  user.IsVerified,
		"last_login":   user.LastLoginAt,
		"title":        user.Title,
		"department":   user.Department,
		"organization": user.Organization,
		"phone":        user.Phone,
		"bio":          user.Bio,
		"agency":       user.Agency,
		"created_at":   user.CreatedAt,
		"updated_at":   user.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User updated successfully",
		Data:    response,
	})
}

// DeleteUser deletes a user
func DeleteUser(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if user exists
	var user models.User
	if err := db.First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Delete user
	if err := db.Delete(&user).Error; err != nil {
		HandleInternalError(c, "Failed to delete user: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User deleted successfully",
	})
}

// GetUserPreferences returns user preferences
func GetUserPreferences(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user preferences
	var preferences models.UserPreferences
	if err := db.Preload("User").Where("user_id = ?", id).First(&preferences).Error; err != nil {
		if err.Error() == "record not found" {
			// Create default preferences if they don't exist
			preferences = models.UserPreferences{
				UserID:               id,
				EmailNotifications:   true,
				DocumentAlerts:       true,
				WeeklyDigest:         false,
				CommentNotifications: true,
				DocumentsPerPage:     25,
				DefaultView:          "list",
				Theme:                "light",
				Language:             "en",
				Timezone:             "UTC",
				DefaultSearchSort:    "relevance",
				SaveSearchHistory:    true,
				AutoCompleteEnabled:  true,
			}

			if err := db.Create(&preferences).Error; err != nil {
				HandleInternalError(c, "Failed to create default preferences: "+err.Error())
				return
			}

			// Load the created preferences with user
			if err := db.Preload("User").First(&preferences, preferences.ID).Error; err != nil {
				HandleInternalError(c, "Failed to load created preferences: "+err.Error())
				return
			}
		} else {
			HandleInternalError(c, "Failed to fetch user preferences: "+err.Error())
			return
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User preferences retrieved successfully",
		Data:    preferences,
	})
}

// UpdateUserPreferences updates user preferences
func UpdateUserPreferences(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		// Notification preferences
		EmailNotifications   *bool `json:"email_notifications"`
		DocumentAlerts       *bool `json:"document_alerts"`
		WeeklyDigest         *bool `json:"weekly_digest"`
		CommentNotifications *bool `json:"comment_notifications"`

		// Display preferences
		DocumentsPerPage *int    `json:"documents_per_page"`
		DefaultView      *string `json:"default_view"`
		Theme            *string `json:"theme"`
		Language         *string `json:"language"`
		Timezone         *string `json:"timezone"`

		// Search preferences
		DefaultSearchSort   *string `json:"default_search_sort"`
		SaveSearchHistory   *bool   `json:"save_search_history"`
		AutoCompleteEnabled *bool   `json:"autocomplete_enabled"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing preferences
	var preferences models.UserPreferences
	if err := db.Where("user_id = ?", id).First(&preferences).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User preferences")
			return
		}
		HandleInternalError(c, "Failed to fetch user preferences: "+err.Error())
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.EmailNotifications != nil {
		updates["email_notifications"] = *req.EmailNotifications
	}
	if req.DocumentAlerts != nil {
		updates["document_alerts"] = *req.DocumentAlerts
	}
	if req.WeeklyDigest != nil {
		updates["weekly_digest"] = *req.WeeklyDigest
	}
	if req.CommentNotifications != nil {
		updates["comment_notifications"] = *req.CommentNotifications
	}
	if req.DocumentsPerPage != nil {
		if *req.DocumentsPerPage > 0 && *req.DocumentsPerPage <= 100 {
			updates["documents_per_page"] = *req.DocumentsPerPage
		}
	}
	if req.DefaultView != nil {
		updates["default_view"] = *req.DefaultView
	}
	if req.Theme != nil {
		updates["theme"] = *req.Theme
	}
	if req.Language != nil {
		updates["language"] = *req.Language
	}
	if req.Timezone != nil {
		updates["timezone"] = *req.Timezone
	}
	if req.DefaultSearchSort != nil {
		updates["default_search_sort"] = *req.DefaultSearchSort
	}
	if req.SaveSearchHistory != nil {
		updates["save_search_history"] = *req.SaveSearchHistory
	}
	if req.AutoCompleteEnabled != nil {
		updates["autocomplete_enabled"] = *req.AutoCompleteEnabled
	}

	// Update preferences
	if err := db.Model(&preferences).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update user preferences: "+err.Error())
		return
	}

	// Load updated preferences with user
	if err := db.Preload("User").First(&preferences, preferences.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated preferences: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User preferences updated successfully",
		Data:    preferences,
	})
}

// GetUserSettings returns user settings
func GetUserSettings(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user with preferences
	var user models.User
	if err := db.Preload("Agency").First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Get user preferences
	var preferences models.UserPreferences
	if err := db.Where("user_id = ?", id).First(&preferences).Error; err != nil {
		if err.Error() == "record not found" {
			// Create default preferences if they don't exist
			preferences = models.UserPreferences{
				UserID:               id,
				EmailNotifications:   true,
				DocumentAlerts:       true,
				WeeklyDigest:         false,
				CommentNotifications: true,
				DocumentsPerPage:     25,
				DefaultView:          "list",
				Theme:                "light",
				Language:             "en",
				Timezone:             "UTC",
				DefaultSearchSort:    "relevance",
				SaveSearchHistory:    true,
				AutoCompleteEnabled:  true,
			}

			if err := db.Create(&preferences).Error; err != nil {
				HandleInternalError(c, "Failed to create default preferences: "+err.Error())
				return
			}
		} else {
			HandleInternalError(c, "Failed to fetch user preferences: "+err.Error())
			return
		}
	}

	// Combine user and preferences data
	settings := gin.H{
		"user": gin.H{
			"id":           user.ID,
			"username":     user.Username,
			"email":        user.Email,
			"first_name":   user.FirstName,
			"last_name":    user.LastName,
			"title":        user.Title,
			"department":   user.Department,
			"organization": user.Organization,
			"phone":        user.Phone,
			"bio":          user.Bio,
			"role":         user.Role,
			"is_active":    user.IsActive,
			"is_verified":  user.IsVerified,
			"last_login":   user.LastLoginAt,
			"agency":       user.Agency,
			"created_at":   user.CreatedAt,
			"updated_at":   user.UpdatedAt,
		},
		"preferences": preferences,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User settings retrieved successfully",
		Data:    settings,
	})
}

// UpdateUserSettings updates user settings
func UpdateUserSettings(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		// User profile updates
		FirstName    *string `json:"first_name"`
		LastName     *string `json:"last_name"`
		Title        *string `json:"title"`
		Department   *string `json:"department"`
		Organization *string `json:"organization"`
		Phone        *string `json:"phone"`
		Bio          *string `json:"bio"`

		// Preference updates
		Preferences *struct {
			EmailNotifications   *bool   `json:"email_notifications"`
			DocumentAlerts       *bool   `json:"document_alerts"`
			WeeklyDigest         *bool   `json:"weekly_digest"`
			CommentNotifications *bool   `json:"comment_notifications"`
			DocumentsPerPage     *int    `json:"documents_per_page"`
			DefaultView          *string `json:"default_view"`
			Theme                *string `json:"theme"`
			Language             *string `json:"language"`
			Timezone             *string `json:"timezone"`
			DefaultSearchSort    *string `json:"default_search_sort"`
			SaveSearchHistory    *bool   `json:"save_search_history"`
			AutoCompleteEnabled  *bool   `json:"autocomplete_enabled"`
		} `json:"preferences"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing user
	var user models.User
	if err := db.First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Update user profile if provided
	userUpdates := make(map[string]interface{})
	if req.FirstName != nil {
		userUpdates["first_name"] = *req.FirstName
	}
	if req.LastName != nil {
		userUpdates["last_name"] = *req.LastName
	}
	if req.Title != nil {
		userUpdates["title"] = *req.Title
	}
	if req.Department != nil {
		userUpdates["department"] = *req.Department
	}
	if req.Organization != nil {
		userUpdates["organization"] = *req.Organization
	}
	if req.Phone != nil {
		userUpdates["phone"] = *req.Phone
	}
	if req.Bio != nil {
		userUpdates["bio"] = *req.Bio
	}

	if len(userUpdates) > 0 {
		if err := db.Model(&user).Updates(userUpdates).Error; err != nil {
			HandleInternalError(c, "Failed to update user profile: "+err.Error())
			return
		}
	}

	// Update preferences if provided
	if req.Preferences != nil {
		var preferences models.UserPreferences
		if err := db.Where("user_id = ?", id).First(&preferences).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "User preferences")
				return
			}
			HandleInternalError(c, "Failed to fetch user preferences: "+err.Error())
			return
		}

		prefUpdates := make(map[string]interface{})
		if req.Preferences.EmailNotifications != nil {
			prefUpdates["email_notifications"] = *req.Preferences.EmailNotifications
		}
		if req.Preferences.DocumentAlerts != nil {
			prefUpdates["document_alerts"] = *req.Preferences.DocumentAlerts
		}
		if req.Preferences.WeeklyDigest != nil {
			prefUpdates["weekly_digest"] = *req.Preferences.WeeklyDigest
		}
		if req.Preferences.CommentNotifications != nil {
			prefUpdates["comment_notifications"] = *req.Preferences.CommentNotifications
		}
		if req.Preferences.DocumentsPerPage != nil {
			if *req.Preferences.DocumentsPerPage > 0 && *req.Preferences.DocumentsPerPage <= 100 {
				prefUpdates["documents_per_page"] = *req.Preferences.DocumentsPerPage
			}
		}
		if req.Preferences.DefaultView != nil {
			prefUpdates["default_view"] = *req.Preferences.DefaultView
		}
		if req.Preferences.Theme != nil {
			prefUpdates["theme"] = *req.Preferences.Theme
		}
		if req.Preferences.Language != nil {
			prefUpdates["language"] = *req.Preferences.Language
		}
		if req.Preferences.Timezone != nil {
			prefUpdates["timezone"] = *req.Preferences.Timezone
		}
		if req.Preferences.DefaultSearchSort != nil {
			prefUpdates["default_search_sort"] = *req.Preferences.DefaultSearchSort
		}
		if req.Preferences.SaveSearchHistory != nil {
			prefUpdates["save_search_history"] = *req.Preferences.SaveSearchHistory
		}
		if req.Preferences.AutoCompleteEnabled != nil {
			prefUpdates["autocomplete_enabled"] = *req.Preferences.AutoCompleteEnabled
		}

		if len(prefUpdates) > 0 {
			if err := db.Model(&preferences).Updates(prefUpdates).Error; err != nil {
				HandleInternalError(c, "Failed to update user preferences: "+err.Error())
				return
			}
		}
	}

	// Return updated settings
	GetUserSettings(c)
}

// GetCurrentUserPreferences returns current user preferences
func GetCurrentUserPreferences(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user preferences
	var preferences models.UserPreferences
	if err := db.Preload("User").Where("user_id = ?", userID).First(&preferences).Error; err != nil {
		if err.Error() == "record not found" {
			// Create default preferences if they don't exist
			preferences = models.UserPreferences{
				UserID:               *userID.(*uint),
				EmailNotifications:   true,
				DocumentAlerts:       true,
				WeeklyDigest:         false,
				CommentNotifications: true,
				DocumentsPerPage:     25,
				DefaultView:          "list",
				Theme:                "light",
				Language:             "en",
				Timezone:             "UTC",
				DefaultSearchSort:    "relevance",
				SaveSearchHistory:    true,
				AutoCompleteEnabled:  true,
			}

			if err := db.Create(&preferences).Error; err != nil {
				HandleInternalError(c, "Failed to create default preferences: "+err.Error())
				return
			}

			// Load the created preferences with user
			if err := db.Preload("User").First(&preferences, preferences.ID).Error; err != nil {
				HandleInternalError(c, "Failed to load created preferences: "+err.Error())
				return
			}
		} else {
			HandleInternalError(c, "Failed to fetch user preferences: "+err.Error())
			return
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Current user preferences retrieved successfully",
		Data:    preferences,
	})
}

// UpdateCurrentUserPreferences updates current user preferences
func UpdateCurrentUserPreferences(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	var req struct {
		// Notification preferences
		EmailNotifications   *bool `json:"email_notifications"`
		DocumentAlerts       *bool `json:"document_alerts"`
		WeeklyDigest         *bool `json:"weekly_digest"`
		CommentNotifications *bool `json:"comment_notifications"`

		// Display preferences
		DocumentsPerPage *int    `json:"documents_per_page"`
		DefaultView      *string `json:"default_view"`
		Theme            *string `json:"theme"`
		Language         *string `json:"language"`
		Timezone         *string `json:"timezone"`

		// Search preferences
		DefaultSearchSort   *string `json:"default_search_sort"`
		SaveSearchHistory   *bool   `json:"save_search_history"`
		AutoCompleteEnabled *bool   `json:"autocomplete_enabled"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing preferences
	var preferences models.UserPreferences
	if err := db.Where("user_id = ?", userID).First(&preferences).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User preferences")
			return
		}
		HandleInternalError(c, "Failed to fetch user preferences: "+err.Error())
		return
	}

	// Update fields if provided
	updates := make(map[string]interface{})
	if req.EmailNotifications != nil {
		updates["email_notifications"] = *req.EmailNotifications
	}
	if req.DocumentAlerts != nil {
		updates["document_alerts"] = *req.DocumentAlerts
	}
	if req.WeeklyDigest != nil {
		updates["weekly_digest"] = *req.WeeklyDigest
	}
	if req.CommentNotifications != nil {
		updates["comment_notifications"] = *req.CommentNotifications
	}
	if req.DocumentsPerPage != nil {
		if *req.DocumentsPerPage > 0 && *req.DocumentsPerPage <= 100 {
			updates["documents_per_page"] = *req.DocumentsPerPage
		}
	}
	if req.DefaultView != nil {
		updates["default_view"] = *req.DefaultView
	}
	if req.Theme != nil {
		updates["theme"] = *req.Theme
	}
	if req.Language != nil {
		updates["language"] = *req.Language
	}
	if req.Timezone != nil {
		updates["timezone"] = *req.Timezone
	}
	if req.DefaultSearchSort != nil {
		updates["default_search_sort"] = *req.DefaultSearchSort
	}
	if req.SaveSearchHistory != nil {
		updates["save_search_history"] = *req.SaveSearchHistory
	}
	if req.AutoCompleteEnabled != nil {
		updates["autocomplete_enabled"] = *req.AutoCompleteEnabled
	}

	// Update preferences
	if err := db.Model(&preferences).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update user preferences: "+err.Error())
		return
	}

	// Load updated preferences with user
	if err := db.Preload("User").First(&preferences, preferences.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated preferences: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Current user preferences updated successfully",
		Data:    preferences,
	})
}

// GetUserDashboardStats returns user dashboard statistics
func GetUserDashboardStats(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify user exists
	var user models.User
	if err := db.First(&user, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "User")
			return
		}
		HandleInternalError(c, "Failed to fetch user: "+err.Error())
		return
	}

	// Get document statistics
	var documentStats struct {
		TotalDocuments int64 `json:"total_documents"`
		DraftDocuments int64 `json:"draft_documents"`
		PublishedDocs  int64 `json:"published_documents"`
		ReviewedDocs   int64 `json:"reviewed_documents"`
	}

	// Count documents created by user
	db.Model(&models.Document{}).Where("created_by_id = ?", id).Count(&documentStats.TotalDocuments)
	db.Model(&models.Document{}).Where("created_by_id = ? AND status = ?", id, "draft").Count(&documentStats.DraftDocuments)
	db.Model(&models.Document{}).Where("created_by_id = ? AND status = ?", id, "published").Count(&documentStats.PublishedDocs)

	// Count documents reviewed by user
	db.Model(&models.DocumentReview{}).Where("reviewer_id = ?", id).Count(&documentStats.ReviewedDocs)

	// Get recent activity (last 30 days)
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	var recentActivity struct {
		RecentDocuments int64 `json:"recent_documents"`
		RecentReviews   int64 `json:"recent_reviews"`
		RecentComments  int64 `json:"recent_comments"`
	}

	db.Model(&models.Document{}).Where("created_by_id = ? AND created_at >= ?", id, thirtyDaysAgo).Count(&recentActivity.RecentDocuments)
	db.Model(&models.DocumentReview{}).Where("reviewer_id = ? AND created_at >= ?", id, thirtyDaysAgo).Count(&recentActivity.RecentReviews)
	// Count document comments by commenter email (assuming user email matches commenter email)
	db.Model(&models.DocumentComment{}).Where("commenter_email = ? AND created_at >= ?", user.Email, thirtyDaysAgo).Count(&recentActivity.RecentComments)

	// Get user sessions count (active sessions)
	var activeSessions int64
	db.Model(&models.UserSession{}).Where("user_id = ? AND is_active = ? AND expires_at > ?", id, true, time.Now()).Count(&activeSessions)

	// Get user preferences
	var preferences models.UserPreferences
	hasPreferences := true
	if err := db.Where("user_id = ?", id).First(&preferences).Error; err != nil {
		hasPreferences = false
	}

	// Compile dashboard statistics
	dashboardStats := gin.H{
		"user_info": gin.H{
			"id":          user.ID,
			"username":    user.Username,
			"email":       user.Email,
			"first_name":  user.FirstName,
			"last_name":   user.LastName,
			"role":        user.Role,
			"is_active":   user.IsActive,
			"is_verified": user.IsVerified,
			"last_login":  user.LastLoginAt,
			"created_at":  user.CreatedAt,
		},
		"document_stats":  documentStats,
		"recent_activity": recentActivity,
		"active_sessions": activeSessions,
		"has_preferences": hasPreferences,
		"generated_at":    time.Now(),
	}

	if hasPreferences {
		dashboardStats["preferences"] = preferences
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User dashboard statistics retrieved successfully",
		Data:    dashboardStats,
	})
}
