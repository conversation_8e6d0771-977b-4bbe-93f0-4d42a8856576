import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';

interface ProceedingStep {
  name: string;
  description: string;
  step_order: number;
  step_type: string;
  requires_previous_completion?: boolean;
  is_mandatory?: boolean;
  allows_parallel_execution?: boolean;
  content_type?: string;
  text_content?: string;
  voice_recording?: string;
  document_content?: string;
  planned_start_date?: string;
  planned_end_date?: string;
  estimated_duration?: number;
  completion_criteria: string;
  assigned_to_id?: number;
  priority: string;
  is_optional?: boolean;
  is_critical?: boolean;
  notes: string;
}

interface ProceedingFormData {
  // Basic information
  name: string;
  description: string;
  objective: string;
  priority: string;

  // Unique identification
  initiation_date?: string;

  // Administrative proceeding requirements
  legal_authority: string;
  regulatory_impact: string;
  public_interest: string;
  stakeholder_analysis: string;

  // Timing information
  planned_start_date?: string;
  actual_start_date?: string;
  planned_end_date?: string;
  actual_end_date?: string;
  estimated_duration?: number;
  actual_duration?: number;

  // Review requirements
  review_required?: boolean;
  review_scheduled?: boolean;
  review_date?: string;
  review_completed?: boolean;
  review_completed_at?: string;
  review_report_id?: number;
  critical_milestones?: string;

  // Existing directives consideration
  existing_directives_reviewed?: boolean;
  referenced_rules: string;
  referenced_orders: string;
  referenced_directives: string;
  conflict_analysis: string;
  integration_plan: string;

  // IFR Integration
  requires_ifr?: boolean;
  ifr_triggered?: boolean;
  ifr_description: string;
  ifr_document_id?: number;

  // Relationships
  initiated_by_id?: number;
  owner_id: number;
  agency_id?: number;
  category_id?: number;

  // Progress tracking
  total_steps?: number;
  completed_steps?: number;
  progress_percent?: number;
  current_step_order?: number;

  // Additional metadata
  tags: string;
  is_public?: boolean;
  notes: string;
  attachments?: string;
  external_refs?: string;

  // Steps
  proceeding_steps: ProceedingStep[];
}

interface ProceedingFormProps {
  initialData?: Partial<ProceedingFormData>;
  onSubmit: (data: ProceedingFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  isEdit?: boolean;
}

const ProceedingForm: React.FC<ProceedingFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  isEdit = false
}) => {
  const { user } = useAuthStore();
  const [formData, setFormData] = useState<ProceedingFormData>({
    // Basic information
    name: '',
    description: '',
    objective: '',
    priority: 'medium',

    // Unique identification
    initiation_date: '',

    // Administrative proceeding requirements
    legal_authority: '',
    regulatory_impact: '',
    public_interest: '',
    stakeholder_analysis: '',

    // Timing information
    planned_start_date: '',
    actual_start_date: '',
    planned_end_date: '',
    actual_end_date: '',
    estimated_duration: undefined,
    actual_duration: undefined,

    // Review requirements
    review_required: true,
    review_scheduled: false,
    review_date: '',
    review_completed: false,
    review_completed_at: '',
    review_report_id: undefined,
    critical_milestones: '',

    // Existing directives consideration
    existing_directives_reviewed: false,
    referenced_rules: '',
    referenced_orders: '',
    referenced_directives: '',
    conflict_analysis: '',
    integration_plan: '',

    // IFR Integration
    requires_ifr: false,
    ifr_triggered: false,
    ifr_description: '',
    ifr_document_id: undefined,

    // Relationships
    initiated_by_id: undefined,
    owner_id: user?.id || 1, // Set from current user
    agency_id: undefined,
    category_id: undefined,

    // Progress tracking
    total_steps: 0,
    completed_steps: 0,
    progress_percent: 0,
    current_step_order: undefined,

    // Additional metadata
    tags: '',
    is_public: false,
    notes: '',
    attachments: '',
    external_refs: '',
    proceeding_steps: [
      {
        name: 'Notice of Intent',
        description: 'Initial announcement of the administrative proceeding',
        step_order: 1,
        step_type: 'notice_of_intent',
        completion_criteria: 'Notice published and stakeholders notified',
        priority: 'high',
        notes: '',
        requires_previous_completion: false,
        is_mandatory: true,
        allows_parallel_execution: false,
        content_type: 'text'
      },
      {
        name: 'Notice of Proposed Rulemaking',
        description: 'Formal proposal with detailed regulatory text',
        step_order: 2,
        step_type: 'notice_proposed_rule',
        completion_criteria: 'NPRM published with proposed rule text',
        priority: 'high',
        notes: '',
        requires_previous_completion: true,
        is_mandatory: true,
        allows_parallel_execution: false,
        content_type: 'text'
      },
      {
        name: 'Public Comment Period',
        description: 'Collection and analysis of stakeholder input',
        step_order: 3,
        step_type: 'public_comment',
        completion_criteria: 'Comment period closed and analysis completed',
        priority: 'high',
        notes: '',
        requires_previous_completion: true,
        is_mandatory: true,
        allows_parallel_execution: false,
        content_type: 'text'
      },
      {
        name: 'Final Rule Development',
        description: 'Analysis of comments and finalization of rule',
        step_order: 4,
        step_type: 'final_rule',
        completion_criteria: 'Final rule text completed and approved',
        priority: 'high',
        notes: '',
        requires_previous_completion: true,
        is_mandatory: true,
        allows_parallel_execution: false,
        content_type: 'text'
      },
      {
        name: 'Regulation Publication',
        description: 'Official publication and implementation',
        step_order: 5,
        step_type: 'regulation_publication',
        completion_criteria: 'Regulation officially published and effective',
        priority: 'high',
        notes: '',
        requires_previous_completion: true,
        is_mandatory: true,
        allows_parallel_execution: false,
        content_type: 'text'
      },

    ]
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({ ...prev, ...initialData }));
    } else if (!isEdit) {
      // Load default values for new proceeding
      loadDefaultValues();
    }
  }, [initialData, isEdit]);

  // Load default values from preloading API
  const loadDefaultValues = async () => {
    try {
      const response = await apiService.getProceedingDefaults();
      const defaults = response.data;

      // Format dates for input fields (YYYY-MM-DD)
      const formatDate = (dateString: string) => {
        if (!dateString) return '';
        return new Date(dateString).toISOString().split('T')[0];
      };

      setFormData(prev => ({
        ...prev,
        initiation_date: formatDate(defaults.initiation_date),
        planned_start_date: formatDate(defaults.planned_start_date),
        planned_end_date: formatDate(defaults.planned_end_date),
        priority: defaults.priority || 'medium',
        review_required: defaults.requires_mandatory_review !== undefined ? defaults.requires_mandatory_review : true,
        is_public: defaults.is_public !== undefined ? defaults.is_public : false,
        total_steps: defaults.total_steps || 0,
        completed_steps: defaults.completed_steps || 0,
        progress_percent: defaults.progress_percent || 0,
      }));
    } catch (err) {
      console.error('Error loading default values:', err);
    }
  };

  const handleInputChange = (field: keyof ProceedingFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }

    // Regenerate unique ID when name changes
    if (field === 'name' && value && !isEdit) {
      regenerateUniqueId(value);
    }
  };

  // Regenerate unique ID based on name
  const regenerateUniqueId = async (name: string) => {
    try {
      const response = await fetch(`/api/v1/preloading/proceedings?name=${encodeURIComponent(name)}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const defaults = await response.json();
        // The unique ID is generated on the backend, we don't need to set it in the form
        // It will be generated when the proceeding is created
      }
    } catch (err) {
      console.error('Error regenerating unique ID:', err);
    }
  };

  const handleStepChange = (index: number, field: keyof ProceedingStep, value: any) => {
    setFormData(prev => ({
      ...prev,
      proceeding_steps: prev.proceeding_steps.map((step, i) =>
        i === index ? { ...step, [field]: value } : step
      )
    }));
  };

  const addStep = () => {
    const newStep: ProceedingStep = {
      name: '',
      description: '',
      step_order: formData.proceeding_steps.length + 1,
      step_type: 'custom',
      completion_criteria: '',
      priority: 'medium',
      notes: '',
      requires_previous_completion: true,
      is_mandatory: true,
      allows_parallel_execution: false,
      content_type: 'text'
    };
    setFormData(prev => ({
      ...prev,
      proceeding_steps: [...prev.proceeding_steps, newStep]
    }));
  };

  const removeStep = (index: number) => {
    if (formData.proceeding_steps.length <= 5) {
      alert('Administrative proceeding must have at least 5 sequential steps');
      return;
    }

    setFormData(prev => ({
      ...prev,
      proceeding_steps: prev.proceeding_steps
        .filter((_, i) => i !== index)
        .map((step, i) => ({ ...step, step_order: i + 1 }))
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (!formData.objective.trim()) {
      newErrors.objective = 'Objective is required';
    }

    if (!formData.legal_authority.trim()) {
      newErrors.legal_authority = 'Legal authority is required for administrative proceedings';
    }

    if (formData.proceeding_steps.length < 5) {
      newErrors.proceeding_steps = 'Must have at least 5 sequential steps for administrative proceedings';
    }

    // Validate each step
    formData.proceeding_steps.forEach((step, index) => {
      if (!step.name.trim()) {
        newErrors[`step_${index}_name`] = `Step ${index + 1} name is required`;
      }
      if (!step.description.trim()) {
        newErrors[`step_${index}_description`] = `Step ${index + 1} description is required`;
      }
      if (!step.step_type.trim()) {
        newErrors[`step_${index}_step_type`] = `Step ${index + 1} type is required`;
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Basic Information */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Basic Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter proceeding name"
            />
            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Priority
            </label>
            <select
              value={formData.priority}
              onChange={(e) => handleInputChange('priority', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="urgent">Urgent</option>
              <option value="critical">Critical</option>
            </select>
          </div>
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Enter proceeding description"
          />
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Objective *
          </label>
          <textarea
            value={formData.objective}
            onChange={(e) => handleInputChange('objective', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Enter proceeding objective"
          />
          {errors.objective && <p className="mt-1 text-sm text-red-600">{errors.objective}</p>}
        </div>
      </div>

      {/* Administrative Proceeding Requirements */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Administrative Proceeding Requirements
        </h3>

        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Legal Authority *
            </label>
            <textarea
              value={formData.legal_authority}
              onChange={(e) => handleInputChange('legal_authority', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Describe the legal basis and authority for this administrative proceeding"
            />
            {errors.legal_authority && <p className="mt-1 text-sm text-red-600">{errors.legal_authority}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Regulatory Impact
            </label>
            <textarea
              value={formData.regulatory_impact}
              onChange={(e) => handleInputChange('regulatory_impact', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Describe the expected regulatory impact and scope"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Public Interest
            </label>
            <textarea
              value={formData.public_interest}
              onChange={(e) => handleInputChange('public_interest', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Explain how this proceeding serves the public interest"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Stakeholder Analysis
            </label>
            <textarea
              value={formData.stakeholder_analysis}
              onChange={(e) => handleInputChange('stakeholder_analysis', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Identify and analyze affected stakeholders and parties"
            />
          </div>
        </div>
      </div>

      {/* Primary Steps (Requirement 3) */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Primary Steps (Requirement 3)
          </h3>
          <button
            type="button"
            onClick={addStep}
            className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Add Step
          </button>
        </div>

        {errors.primary_steps && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{errors.primary_steps}</p>
          </div>
        )}

        <div className="space-y-4">
          {formData.proceeding_steps.map((step, index) => (
            <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-md font-medium text-gray-900 dark:text-white">
                  Step {index + 1}
                </h4>
                {formData.proceeding_steps.length > 5 && (
                  <button
                    type="button"
                    onClick={() => removeStep(index)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Step Name *
                  </label>
                  <input
                    type="text"
                    value={step.name}
                    onChange={(e) => handleStepChange(index, 'name', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Enter step name"
                  />
                  {errors[`step_${index}_name`] && (
                    <p className="mt-1 text-sm text-red-600">{errors[`step_${index}_name`]}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Priority
                  </label>
                  <select
                    value={step.priority}
                    onChange={(e) => handleStepChange(index, 'priority', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="urgent">Urgent</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  value={step.description}
                  onChange={(e) => handleStepChange(index, 'description', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter step description"
                />
                {errors[`step_${index}_description`] && (
                  <p className="mt-1 text-sm text-red-600">{errors[`step_${index}_description`]}</p>
                )}
              </div>

              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Completion Criteria
                </label>
                <textarea
                  value={step.completion_criteria}
                  onChange={(e) => handleStepChange(index, 'completion_criteria', e.target.value)}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                  placeholder="Enter completion criteria"
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Saving...' : (isEdit ? 'Update Proceeding' : 'Create Proceeding')}
        </button>
      </div>
    </form>
  );
};

export default ProceedingForm;
