package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// DigitalSignatureRequest represents the request structure for digital signatures
type DigitalSignatureRequest struct {
	DocumentID       uint       `json:"document_id" binding:"required"`
	CertificateID    *uint      `json:"certificate_id"`
	Type             string     `json:"type" binding:"required"` // "simple", "advanced", "qualified"
	SignatureData    string     `json:"signature_data" binding:"required"`
	SignatureMethod  string     `json:"signature_method"`
	HashAlgorithm    string     `json:"hash_algorithm"`
	SignedAt         *time.Time `json:"signed_at"`
	SignerName       string     `json:"signer_name" binding:"required"`
	SignerEmail      string     `json:"signer_email" binding:"required"`
	SignerTitle      string     `json:"signer_title"`
	ValidationStatus string     `json:"validation_status"`
	RequestedByID    uint       `json:"requested_by_id"`
}

// generateSignatureID generates a unique signature ID
func generateSignatureID() string {
	return fmt.Sprintf("SIG-%d", time.Now().Unix())
}

// GetDigitalSignatures returns all digital signatures
func GetDigitalSignatures(c *gin.Context) {
	pagination := GetPaginationParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.DigitalSignature{}).
		Preload("Document").
		Preload("Certificate")

	// Apply filters
	if documentID := c.Query("document_id"); documentID != "" {
		query = query.Where("document_id = ?", documentID)
	}

	if certificateID := c.Query("certificate_id"); certificateID != "" {
		query = query.Where("certificate_id = ?", certificateID)
	}

	if signatureType := c.Query("signature_type"); signatureType != "" {
		query = query.Where("signature_type = ?", signatureType)
	}

	if validStr := c.Query("is_valid"); validStr != "" {
		if valid, err := strconv.ParseBool(validStr); err == nil {
			query = query.Where("is_valid = ?", valid)
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		HandleInternalError(c, "Failed to count signatures: "+err.Error())
		return
	}

	// Apply pagination and sorting
	offset := (pagination.Page - 1) * pagination.PerPage
	query = query.Offset(offset).Limit(pagination.PerPage)

	sortBy := c.DefaultQuery("sort_by", "signed_at")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	if sortOrder != "asc" && sortOrder != "desc" {
		sortOrder = "desc"
	}
	query = query.Order(sortBy + " " + sortOrder)

	// Execute query
	var signatures []models.DigitalSignature
	if err := query.Find(&signatures).Error; err != nil {
		HandleInternalError(c, "Failed to fetch signatures: "+err.Error())
		return
	}

	// Convert to response format
	signatureResponses := make([]gin.H, len(signatures))
	for i, sig := range signatures {
		signatureResponses[i] = gin.H{
			"id":                sig.ID,
			"signature_id":      sig.SignatureID,
			"document_id":       sig.DocumentID,
			"certificate_id":    sig.CertificateID,
			"type":              sig.Type,
			"signature_data":    sig.SignatureData,
			"hash_algorithm":    sig.HashAlgorithm,
			"signed_at":         sig.SignedAt,
			"signer_name":       sig.SignerName,
			"signer_email":      sig.SignerEmail,
			"signer_title":      sig.SignerTitle,
			"is_valid":          sig.IsValid,
			"validation_status": sig.ValidationStatus,
			"signature_method":  sig.SignatureMethod,
			"created_at":        sig.CreatedAt,
			"updated_at":        sig.UpdatedAt,
		}

		if sig.Document.ID != 0 {
			signatureResponses[i]["document"] = gin.H{
				"id":    sig.Document.ID,
				"title": sig.Document.Title,
			}
		}

		if sig.Certificate != nil && sig.Certificate.ID != 0 {
			signatureResponses[i]["certificate"] = gin.H{
				"id":            sig.Certificate.ID,
				"serial_number": sig.Certificate.SerialNumber,
				"subject":       sig.Certificate.Subject,
				"issuer":        sig.Certificate.Issuer,
			}
		}
	}

	response := CreatePaginationResponse(signatureResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// GetDigitalSignature returns a specific digital signature
func GetDigitalSignature(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var signature models.DigitalSignature
	err := db.Preload("Document").
		Preload("Certificate").
		First(&signature, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			HandleNotFound(c, "Digital signature")
		} else {
			HandleInternalError(c, "Failed to retrieve signature: "+err.Error())
		}
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Digital signature retrieved successfully",
		Data:    signature,
	})
}

// CreateDigitalSignature creates a new digital signature
func CreateDigitalSignature(c *gin.Context) {
	var req DigitalSignatureRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Validate signature type
	validTypes := []string{"simple", "advanced", "qualified", "biometric", "multi_factor", "blockchain", "quantum_safe"}
	isValidType := false
	for _, validType := range validTypes {
		if req.Type == validType {
			isValidType = true
			break
		}
	}
	if !isValidType {
		HandleBadRequest(c, "Invalid signature type. Must be one of: simple, advanced, qualified, biometric, multi_factor, blockchain, quantum_safe")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, req.DocumentID).Error; err != nil {
		HandleBadRequest(c, "Invalid document ID")
		return
	}

	// Verify certificate exists
	var certificate models.DigitalCertificate
	if err := db.First(&certificate, req.CertificateID).Error; err != nil {
		HandleBadRequest(c, "Invalid certificate ID")
		return
	}

	// Generate unique signature ID
	signatureID := fmt.Sprintf("SIG-%d-%d", req.DocumentID, time.Now().Unix())

	// Create signature
	signature := &models.DigitalSignature{
		SignatureID:      signatureID, // Proper ID generation
		DocumentID:       req.DocumentID,
		CertificateID:    req.CertificateID,
		Type:             models.SignatureType(req.Type),
		SignatureData:    req.SignatureData,
		SignatureMethod:  req.SignatureMethod,
		HashAlgorithm:    models.HashAlgorithm(req.HashAlgorithm),
		SignedAt:         req.SignedAt,
		SignerID:         *userID.(*uint), // Get from authenticated user
		SignerName:       req.SignerName,
		SignerEmail:      req.SignerEmail,
		SignerTitle:      req.SignerTitle,
		ValidationStatus: req.ValidationStatus,
		RequestedByID:    req.RequestedByID,
		RequestedAt:      time.Now(),
		Status:           models.SignatureStatusPending,
	}

	if err := db.Create(signature).Error; err != nil {
		HandleInternalError(c, "Failed to create signature: "+err.Error())
		return
	}

	// Load relationships for response
	if err := db.Preload("Document").Preload("Certificate").First(signature, signature.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load signature details: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Digital signature created successfully",
		Data:    signature,
	})
}

// UpdateDigitalSignature updates an existing digital signature
func UpdateDigitalSignature(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req DigitalSignatureRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Find existing signature
	var signature models.DigitalSignature
	err := db.First(&signature, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			HandleNotFound(c, "Digital signature")
		} else {
			HandleInternalError(c, "Failed to find signature: "+err.Error())
		}
		return
	}

	// Update signature fields
	signature.DocumentID = req.DocumentID
	signature.CertificateID = req.CertificateID
	signature.Type = models.SignatureType(req.Type)
	signature.SignatureData = req.SignatureData
	signature.SignatureMethod = req.SignatureMethod
	signature.HashAlgorithm = models.HashAlgorithm(req.HashAlgorithm)
	signature.SignedAt = req.SignedAt
	signature.SignerName = req.SignerName
	signature.SignerEmail = req.SignerEmail
	signature.SignerTitle = req.SignerTitle
	signature.ValidationStatus = req.ValidationStatus

	if err := db.Save(&signature).Error; err != nil {
		HandleInternalError(c, "Failed to update signature: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Digital signature updated successfully",
		Data:    signature,
	})
}

// DeleteDigitalSignature deletes a digital signature
func DeleteDigitalSignature(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Find existing signature
	var signature models.DigitalSignature
	err := db.First(&signature, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			HandleNotFound(c, "Digital signature")
		} else {
			HandleInternalError(c, "Failed to find signature: "+err.Error())
		}
		return
	}

	// Delete the signature
	if err := db.Delete(&signature).Error; err != nil {
		HandleInternalError(c, "Failed to delete signature: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Digital signature deleted successfully",
	})
}
