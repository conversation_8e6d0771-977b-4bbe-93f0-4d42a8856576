<!DOCTYPE html>
<html>
<head>
    <title>Frontend Comprehensive Testing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; }
        .test-button { background-color: #007bff; color: white; border: none; border-radius: 3px; }
        .test-button:hover { background-color: #0056b3; }
        .auth-section { background-color: #f8f9fa; }
        .crud-section { background-color: #e9ecef; }
        .page-section { background-color: #f1f3f4; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Frontend Comprehensive Testing Dashboard</h1>
    
    <!-- Authentication Testing -->
    <div class="test-section auth-section">
        <h2>🔐 Authentication Testing</h2>
        <button class="test-button" onclick="testLogin()">Test Login</button>
        <button class="test-button" onclick="checkAuthStatus()">Check Auth Status</button>
        <button class="test-button" onclick="testLogout()">Test Logout</button>
        <div id="authResults"></div>
    </div>

    <!-- Page Loading Testing -->
    <div class="test-section page-section">
        <h2>📄 Page Loading Testing</h2>
        <button class="test-button" onclick="testAllPages()">Test All Pages</button>
        <button class="test-button" onclick="testAdminPages()">Test Admin Pages</button>
        <div id="pageResults"></div>
    </div>

    <!-- CRUD Operations Testing -->
    <div class="test-section crud-section">
        <h2>⚙️ CRUD Operations Testing</h2>
        <button class="test-button" onclick="testDocumentCRUD()">Test Documents CRUD</button>
        <button class="test-button" onclick="testAgencyCRUD()">Test Agencies CRUD</button>
        <button class="test-button" onclick="testCategoryCRUD()">Test Categories CRUD</button>
        <button class="test-button" onclick="testRegulationCRUD()">Test Regulations CRUD</button>
        <button class="test-button" onclick="testTaskCRUD()">Test Tasks CRUD</button>
        <div id="crudResults"></div>
    </div>

    <!-- API Testing -->
    <div class="test-section">
        <h2>🌐 API Connectivity Testing</h2>
        <button class="test-button" onclick="testAPIEndpoints()">Test API Endpoints</button>
        <div id="apiResults"></div>
    </div>

    <!-- Type Checking -->
    <div class="test-section">
        <h2>🔍 Type Checking</h2>
        <button class="test-button" onclick="runTypeCheck()">Run Type Check</button>
        <div id="typeResults"></div>
    </div>

    <script>
        const API_URL = 'http://127.0.0.1:8080';
        const FRONTEND_URL = 'http://127.0.0.1:3000';
        
        // Test pages that should be accessible
        const PUBLIC_PAGES = [
            '/',
            '/login',
            '/register',
            '/documents',
            '/agencies',
            '/categories',
            '/regulations',
            '/about',
            '/features'
        ];

        const PROTECTED_PAGES = [
            '/dashboard',
            '/profile',
            '/tasks',
            '/calendar',
            '/summary'
        ];

        const ADMIN_PAGES = [
            '/admin/agencies',
            '/admin/categories',
            '/admin/regulations/new',
            '/tasks/new'
        ];

        // Authentication Testing
        async function testLogin() {
            const resultDiv = document.getElementById('authResults');
            resultDiv.innerHTML = '<div class="test-result info">Testing login...</div>';
            
            try {
                const response = await fetch(`${API_URL}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        identifier: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    localStorage.setItem('federal_register_token', data.token);
                    if (data.refresh_token) {
                        localStorage.setItem('federal_register_refresh_token', data.refresh_token);
                    }
                    resultDiv.innerHTML += '<div class="test-result success">✅ Login successful! Token stored.</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">❌ Login failed: ${data.error || data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">❌ Login error: ${error.message}</div>`;
            }
        }

        async function checkAuthStatus() {
            const resultDiv = document.getElementById('authResults');
            const token = localStorage.getItem('federal_register_token');
            
            if (!token) {
                resultDiv.innerHTML += '<div class="test-result warning">⚠️ No token found. Please login first.</div>';
                return;
            }

            try {
                const response = await fetch(`${API_URL}/api/v1/auth/me`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML += `<div class="test-result success">✅ Auth status: ${data.username} (${data.role})</div>`;
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">❌ Auth check failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">❌ Auth check error: ${error.message}</div>`;
            }
        }

        function testLogout() {
            localStorage.removeItem('federal_register_token');
            localStorage.removeItem('federal_register_refresh_token');
            document.getElementById('authResults').innerHTML += '<div class="test-result info">🔓 Logged out. Tokens cleared.</div>';
        }

        // Page Testing
        async function testAllPages() {
            const resultDiv = document.getElementById('pageResults');
            resultDiv.innerHTML = '<div class="test-result info">Testing all pages...</div>';
            
            const allPages = [...PUBLIC_PAGES, ...PROTECTED_PAGES];
            
            for (const page of allPages) {
                try {
                    const response = await fetch(`${FRONTEND_URL}${page}`);
                    const status = response.status;
                    
                    if (status === 200) {
                        resultDiv.innerHTML += `<div class="test-result success">✅ ${page} - OK (${status})</div>`;
                    } else if (status === 404) {
                        resultDiv.innerHTML += `<div class="test-result error">❌ ${page} - Not Found (${status})</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="test-result warning">⚠️ ${page} - Status: ${status}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<div class="test-result error">❌ ${page} - Error: ${error.message}</div>`;
                }
            }
        }

        async function testAdminPages() {
            const resultDiv = document.getElementById('pageResults');
            resultDiv.innerHTML += '<div class="test-result info">Testing admin pages...</div>';
            
            for (const page of ADMIN_PAGES) {
                try {
                    const response = await fetch(`${FRONTEND_URL}${page}`);
                    const status = response.status;
                    
                    if (status === 200) {
                        resultDiv.innerHTML += `<div class="test-result success">✅ ${page} - OK (${status})</div>`;
                    } else if (status === 404) {
                        resultDiv.innerHTML += `<div class="test-result error">❌ ${page} - Not Found (${status})</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="test-result warning">⚠️ ${page} - Status: ${status}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<div class="test-result error">❌ ${page} - Error: ${error.message}</div>`;
                }
            }
        }

        // CRUD Testing
        async function testDocumentCRUD() {
            const resultDiv = document.getElementById('crudResults');
            resultDiv.innerHTML += '<div class="test-result info">Testing Documents CRUD...</div>';
            
            const token = localStorage.getItem('federal_register_token');
            if (!token) {
                resultDiv.innerHTML += '<div class="test-result error">❌ No auth token. Please login first.</div>';
                return;
            }

            try {
                // Test GET documents
                const getResponse = await fetch(`${API_URL}/api/v1/public/documents`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (getResponse.ok) {
                    resultDiv.innerHTML += '<div class="test-result success">✅ Documents GET - OK</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">❌ Documents GET failed: ${getResponse.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">❌ Documents test error: ${error.message}</div>`;
            }
        }

        async function testAgencyCRUD() {
            const resultDiv = document.getElementById('crudResults');
            resultDiv.innerHTML += '<div class="test-result info">Testing Agencies CRUD...</div>';
            
            const token = localStorage.getItem('federal_register_token');
            if (!token) {
                resultDiv.innerHTML += '<div class="test-result error">❌ No auth token. Please login first.</div>';
                return;
            }

            try {
                const getResponse = await fetch(`${API_URL}/api/v1/agencies`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (getResponse.ok) {
                    resultDiv.innerHTML += '<div class="test-result success">✅ Agencies GET - OK</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">❌ Agencies GET failed: ${getResponse.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">❌ Agencies test error: ${error.message}</div>`;
            }
        }

        async function testCategoryCRUD() {
            const resultDiv = document.getElementById('crudResults');
            resultDiv.innerHTML += '<div class="test-result info">Testing Categories CRUD...</div>';
            
            const token = localStorage.getItem('federal_register_token');
            if (!token) {
                resultDiv.innerHTML += '<div class="test-result error">❌ No auth token. Please login first.</div>';
                return;
            }

            try {
                const getResponse = await fetch(`${API_URL}/api/v1/categories`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (getResponse.ok) {
                    resultDiv.innerHTML += '<div class="test-result success">✅ Categories GET - OK</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">❌ Categories GET failed: ${getResponse.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">❌ Categories test error: ${error.message}</div>`;
            }
        }

        async function testRegulationCRUD() {
            const resultDiv = document.getElementById('crudResults');
            resultDiv.innerHTML += '<div class="test-result info">Testing Regulations CRUD...</div>';
            
            const token = localStorage.getItem('federal_register_token');
            if (!token) {
                resultDiv.innerHTML += '<div class="test-result error">❌ No auth token. Please login first.</div>';
                return;
            }

            try {
                const getResponse = await fetch(`${API_URL}/api/v1/regulations`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (getResponse.ok) {
                    resultDiv.innerHTML += '<div class="test-result success">✅ Regulations GET - OK</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">❌ Regulations GET failed: ${getResponse.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">❌ Regulations test error: ${error.message}</div>`;
            }
        }

        async function testTaskCRUD() {
            const resultDiv = document.getElementById('crudResults');
            resultDiv.innerHTML += '<div class="test-result info">Testing Tasks CRUD...</div>';
            
            const token = localStorage.getItem('federal_register_token');
            if (!token) {
                resultDiv.innerHTML += '<div class="test-result error">❌ No auth token. Please login first.</div>';
                return;
            }

            try {
                const getResponse = await fetch(`${API_URL}/api/v1/tasks`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                if (getResponse.ok) {
                    resultDiv.innerHTML += '<div class="test-result success">✅ Tasks GET - OK</div>';
                } else {
                    resultDiv.innerHTML += `<div class="test-result error">❌ Tasks GET failed: ${getResponse.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML += `<div class="test-result error">❌ Tasks test error: ${error.message}</div>`;
            }
        }

        // API Testing
        async function testAPIEndpoints() {
            const resultDiv = document.getElementById('apiResults');
            resultDiv.innerHTML = '<div class="test-result info">Testing API endpoints...</div>';
            
            const endpoints = [
                '/api/v1/public/documents',
                '/api/v1/agencies',
                '/api/v1/categories',
                '/api/v1/regulations'
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_URL}${endpoint}`);
                    
                    if (response.ok) {
                        resultDiv.innerHTML += `<div class="test-result success">✅ ${endpoint} - OK (${response.status})</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="test-result error">❌ ${endpoint} - Failed (${response.status})</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<div class="test-result error">❌ ${endpoint} - Error: ${error.message}</div>`;
                }
            }
        }

        // Type Checking (simulated)
        function runTypeCheck() {
            const resultDiv = document.getElementById('typeResults');
            resultDiv.innerHTML = '<div class="test-result info">Type checking requires running `npm run type-check` in the frontend directory.</div>';
            resultDiv.innerHTML += '<div class="test-result warning">⚠️ This is a manual step that needs to be run in the terminal.</div>';
        }

        // Auto-run basic tests on page load
        window.onload = function() {
            checkAuthStatus();
        };
    </script>
</body>
</html>
