package models

import (
	"time"

	"gorm.io/gorm"
)

// ContactForm represents a contact form submission
type ContactForm struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Contact information
	Name    string `json:"name" gorm:"not null"`
	Email   string `json:"email" gorm:"not null"`
	Subject string `json:"subject" gorm:"not null"`
	Message string `json:"message" gorm:"type:text;not null"`

	// Submission details
	Status    string `json:"status" gorm:"default:'received'"` // received, in_progress, resolved, closed
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`

	// Response tracking
	ResponseSent   bool       `json:"response_sent" gorm:"default:false"`
	ResponseSentAt *time.Time `json:"response_sent_at"`
	ResponseBy     string     `json:"response_by"`
	ResponseText   string     `json:"response_text" gorm:"type:text"`

	// Priority and categorization
	Priority string `json:"priority" gorm:"default:'normal'"` // low, normal, high, urgent
	Category string `json:"category"`                         // general, technical, billing, complaint, etc.
	Tags     string `json:"tags" gorm:"type:text"`            // JSON array of tags

	// Assignment and tracking
	AssignedToID *uint `json:"assigned_to_id"`
	AssignedTo   *User `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToID"`
	AssignedAt   *time.Time `json:"assigned_at"`

	// Follow-up
	FollowUpRequired bool       `json:"follow_up_required" gorm:"default:false"`
	FollowUpDate     *time.Time `json:"follow_up_date"`
	FollowUpNotes    string     `json:"follow_up_notes" gorm:"type:text"`

	// Resolution
	ResolvedAt    *time.Time `json:"resolved_at"`
	ResolvedByID  *uint      `json:"resolved_by_id"`
	ResolvedBy    *User      `json:"resolved_by,omitempty" gorm:"foreignKey:ResolvedByID"`
	Resolution    string     `json:"resolution" gorm:"type:text"`
	Satisfaction  *int       `json:"satisfaction"` // 1-5 rating if provided

	// Additional metadata
	Source       string `json:"source" gorm:"default:'web'"` // web, email, phone, chat
	ReferenceID  string `json:"reference_id"`                // External reference ID
	Attachments  string `json:"attachments" gorm:"type:text"` // JSON array of attachment URLs
	InternalNotes string `json:"internal_notes" gorm:"type:text"`
}

// ContactFormResponse represents a response to a contact form
type ContactFormResponse struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Reference to contact form
	ContactFormID uint        `json:"contact_form_id" gorm:"not null"`
	ContactForm   ContactForm `json:"contact_form" gorm:"foreignKey:ContactFormID"`

	// Response details
	ResponseText string `json:"response_text" gorm:"type:text;not null"`
	ResponseType string `json:"response_type" gorm:"default:'email'"` // email, phone, in_person

	// Response metadata
	SentByID   uint `json:"sent_by_id" gorm:"not null"`
	SentBy     User `json:"sent_by" gorm:"foreignKey:SentByID"`
	SentAt     time.Time `json:"sent_at"`
	DeliveredAt *time.Time `json:"delivered_at"`
	ReadAt      *time.Time `json:"read_at"`

	// Email tracking (if applicable)
	EmailMessageID string `json:"email_message_id"`
	EmailSubject   string `json:"email_subject"`
	EmailTo        string `json:"email_to"`
	EmailCC        string `json:"email_cc"`
	EmailBCC       string `json:"email_bcc"`

	// Status tracking
	Status       string `json:"status" gorm:"default:'sent'"` // sent, delivered, read, bounced, failed
	ErrorMessage string `json:"error_message"`
	RetryCount   int    `json:"retry_count" gorm:"default:0"`
	LastRetryAt  *time.Time `json:"last_retry_at"`

	// Additional metadata
	Attachments string `json:"attachments" gorm:"type:text"` // JSON array of attachment URLs
	Notes       string `json:"notes" gorm:"type:text"`
}

// ContactFormTemplate represents email templates for contact form responses
type ContactFormTemplate struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Template identification
	Name        string `json:"name" gorm:"not null;uniqueIndex"`
	Description string `json:"description"`
	Category    string `json:"category"` // acknowledgment, response, follow_up, resolution

	// Template content
	Subject string `json:"subject" gorm:"not null"`
	Body    string `json:"body" gorm:"type:text;not null"`
	Format  string `json:"format" gorm:"default:'html'"` // html, text

	// Template configuration
	IsActive    bool   `json:"is_active" gorm:"default:true"`
	IsDefault   bool   `json:"is_default" gorm:"default:false"`
	Variables   string `json:"variables" gorm:"type:text"` // JSON array of available variables
	Language    string `json:"language" gorm:"default:'en'"`

	// Usage tracking
	UsageCount  int        `json:"usage_count" gorm:"default:0"`
	LastUsedAt  *time.Time `json:"last_used_at"`

	// Owner and permissions
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
}
