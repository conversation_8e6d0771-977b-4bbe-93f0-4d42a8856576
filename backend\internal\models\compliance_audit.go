package models

import (
	"time"

	"gorm.io/gorm"
)

// ComplianceFramework represents different regulatory frameworks
type ComplianceFramework string

const (
	FrameworkSOX     ComplianceFramework = "SOX"     // Sarbanes-Oxley Act
	FrameworkGDPR    ComplianceFramework = "GDPR"    // General Data Protection Regulation
	FrameworkHIPAA   ComplianceFramework = "HIPAA"   // Health Insurance Portability and Accountability Act
	FrameworkFISMA   ComplianceFramework = "FISMA"   // Federal Information Security Management Act
	FrameworkISO27001 ComplianceFramework = "ISO27001" // ISO 27001 Information Security
	FrameworkNIST    ComplianceFramework = "NIST"    // NIST Cybersecurity Framework
	FrameworkPCI     ComplianceFramework = "PCI"     // Payment Card Industry
	FrameworkFERPA   ComplianceFramework = "FERPA"   // Family Educational Rights and Privacy Act
	FrameworkCCPA    ComplianceFramework = "CCPA"    // California Consumer Privacy Act
	FrameworkCustom  ComplianceFramework = "CUSTOM"  // Custom compliance framework
)

// AuditEventType represents the type of audit event
type AuditEventType string

const (
	EventTypeCreate   AuditEventType = "create"
	EventTypeRead     AuditEventType = "read"
	EventTypeUpdate   AuditEventType = "update"
	EventTypeDelete   AuditEventType = "delete"
	EventTypeLogin    AuditEventType = "login"
	EventTypeLogout   AuditEventType = "logout"
	EventTypeAccess   AuditEventType = "access"
	EventTypeExport   AuditEventType = "export"
	EventTypeImport   AuditEventType = "import"
	EventTypeApprove  AuditEventType = "approve"
	EventTypeReject   AuditEventType = "reject"
	EventTypeSign     AuditEventType = "sign"
	EventTypeArchive  AuditEventType = "archive"
	EventTypeRestore  AuditEventType = "restore"
	EventTypeShare    AuditEventType = "share"
	EventTypeDownload AuditEventType = "download"
	EventTypePrint    AuditEventType = "print"
	EventTypeConfig   AuditEventType = "config"
	EventTypeAdmin    AuditEventType = "admin"
)

// RiskLevel represents the risk level of an audit event
type RiskLevel string

const (
	RiskLevelLow      RiskLevel = "low"
	RiskLevelMedium   RiskLevel = "medium"
	RiskLevelHigh     RiskLevel = "high"
	RiskLevelCritical RiskLevel = "critical"
)

// ComplianceStatus represents the compliance status
type ComplianceStatus string

const (
	ComplianceStatusCompliant    ComplianceStatus = "compliant"
	ComplianceStatusNonCompliant ComplianceStatus = "non_compliant"
	ComplianceStatusPartial      ComplianceStatus = "partial"
	ComplianceStatusUnknown      ComplianceStatus = "unknown"
	ComplianceStatusReview       ComplianceStatus = "review"
)

// AuditLog represents a comprehensive audit log entry
type AuditLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Event identification
	EventID     string         `json:"event_id" gorm:"uniqueIndex;not null"`
	EventType   AuditEventType `json:"event_type" gorm:"not null"`
	EventName   string         `json:"event_name" gorm:"not null"`
	Description string         `json:"description" gorm:"type:text"`
	Category    string         `json:"category"`

	// User and session information
	UserID       *uint  `json:"user_id"`
	User         *User  `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Username     string `json:"username"`
	UserRole     string `json:"user_role"`
	SessionID    string `json:"session_id"`
	ImpersonatedByID *uint `json:"impersonated_by_id"`
	ImpersonatedBy   *User `json:"impersonated_by,omitempty" gorm:"foreignKey:ImpersonatedByID"`

	// Target resource information
	ResourceType string `json:"resource_type"`                           // "document", "user", "system", etc.
	ResourceID   string `json:"resource_id"`                             // ID of the affected resource
	ResourceName string `json:"resource_name"`                           // Name/title of the resource
	ParentResourceType string `json:"parent_resource_type"`               // Parent resource type
	ParentResourceID   string `json:"parent_resource_id"`                 // Parent resource ID

	// Technical details
	IPAddress     string `json:"ip_address"`
	UserAgent     string `json:"user_agent"`
	RequestMethod string `json:"request_method"`                         // HTTP method
	RequestURL    string `json:"request_url"`                            // Request URL
	ResponseCode  int    `json:"response_code"`                          // HTTP response code
	RequestSize   int64  `json:"request_size"`                           // Request size in bytes
	ResponseSize  int64  `json:"response_size"`                          // Response size in bytes

	// Change tracking
	OldValues    string `json:"old_values" gorm:"type:text"`             // JSON of old values
	NewValues    string `json:"new_values" gorm:"type:text"`             // JSON of new values
	ChangedFields string `json:"changed_fields" gorm:"type:text"`        // JSON array of changed fields
	ChangeReason string `json:"change_reason"`                           // Reason for change

	// Risk and compliance
	RiskLevel        RiskLevel `json:"risk_level" gorm:"default:'low'"`
	ComplianceFrameworks string `json:"compliance_frameworks" gorm:"type:text"` // JSON array of applicable frameworks
	RequiresReview   bool      `json:"requires_review" gorm:"default:false"`
	ReviewedAt       *time.Time `json:"reviewed_at"`
	ReviewedByID     *uint     `json:"reviewed_by_id"`
	ReviewedBy       *User     `json:"reviewed_by,omitempty" gorm:"foreignKey:ReviewedByID"`
	ReviewNotes      string    `json:"review_notes" gorm:"type:text"`

	// Geolocation and device
	Country       string `json:"country"`
	Region        string `json:"region"`
	City          string `json:"city"`
	Timezone      string `json:"timezone"`
	DeviceType    string `json:"device_type"`                            // "desktop", "mobile", "tablet"
	OperatingSystem string `json:"operating_system"`
	Browser       string `json:"browser"`

	// Additional context
	Tags         string `json:"tags" gorm:"type:text"`                   // JSON array of tags
	Metadata     string `json:"metadata" gorm:"type:text"`               // JSON additional metadata
	CorrelationID string `json:"correlation_id"`                        // For correlating related events
	TraceID      string `json:"trace_id"`                               // Distributed tracing ID

	// Data classification
	DataClassification string `json:"data_classification"`               // "public", "internal", "confidential", "restricted"
	ContainsPII        bool   `json:"contains_pii" gorm:"default:false"`
	ContainsPHI        bool   `json:"contains_phi" gorm:"default:false"`
	ContainsFinancial  bool   `json:"contains_financial" gorm:"default:false"`

	// Retention and archival
	RetentionPeriod int        `json:"retention_period_days" gorm:"default:2555"` // 7 years default
	ArchiveAfter    int        `json:"archive_after_days" gorm:"default:365"`     // 1 year default
	ArchivedAt      *time.Time `json:"archived_at"`
	PurgeAfter      *time.Time `json:"purge_after"`
}

// ComplianceRule represents a compliance rule or requirement
type ComplianceRule struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Rule identification
	RuleID      string              `json:"rule_id" gorm:"uniqueIndex;not null"`
	Name        string              `json:"name" gorm:"not null"`
	Description string              `json:"description" gorm:"type:text"`
	Framework   ComplianceFramework `json:"framework" gorm:"not null"`
	Category    string              `json:"category"`
	Subcategory string              `json:"subcategory"`

	// Rule definition
	RuleType        string `json:"rule_type"`                            // "policy", "control", "requirement"
	Severity        string `json:"severity"`                             // "low", "medium", "high", "critical"
	Priority        int    `json:"priority" gorm:"default:3"`
	IsActive        bool   `json:"is_active" gorm:"default:true"`
	IsAutomated     bool   `json:"is_automated" gorm:"default:false"`

	// Rule logic
	Conditions      string `json:"conditions" gorm:"type:text"`          // JSON rule conditions
	Actions         string `json:"actions" gorm:"type:text"`             // JSON actions to take
	ValidationLogic string `json:"validation_logic" gorm:"type:text"`    // JSON validation logic
	Remediation     string `json:"remediation" gorm:"type:text"`         // Remediation steps

	// Applicability
	AppliesTo       string `json:"applies_to" gorm:"type:text"`          // JSON array of resource types
	ExcludeFrom     string `json:"exclude_from" gorm:"type:text"`        // JSON array of exclusions
	EffectiveDate   time.Time `json:"effective_date"`
	ExpirationDate  *time.Time `json:"expiration_date"`

	// Compliance tracking
	LastChecked     *time.Time `json:"last_checked"`
	NextCheck       *time.Time `json:"next_check"`
	CheckFrequency  int        `json:"check_frequency_hours" gorm:"default:24"`
	ViolationCount  int        `json:"violation_count" gorm:"default:0"`
	ComplianceRate  float64    `json:"compliance_rate" gorm:"default:0"`

	// Rule ownership
	OwnerID         uint   `json:"owner_id" gorm:"not null"`
	Owner           User   `json:"owner" gorm:"foreignKey:OwnerID"`
	ResponsibleTeam string `json:"responsible_team"`
	ContactEmail    string `json:"contact_email"`

	// Documentation
	ReferenceURL    string `json:"reference_url"`
	Documentation   string `json:"documentation" gorm:"type:text"`
	Examples        string `json:"examples" gorm:"type:text"`
	Tags            string `json:"tags" gorm:"type:text"`                // JSON array of tags

	// Relationships
	Violations []ComplianceViolation `json:"violations,omitempty" gorm:"foreignKey:RuleID"`
	Assessments []ComplianceAssessment `json:"assessments,omitempty" gorm:"foreignKey:RuleID"`
}

// ComplianceViolation represents a compliance rule violation
type ComplianceViolation struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Violation identification
	ViolationID string `json:"violation_id" gorm:"uniqueIndex;not null"`
	RuleID      uint   `json:"rule_id" gorm:"not null"`
	Rule        ComplianceRule `json:"rule" gorm:"foreignKey:RuleID"`

	// Violation details
	Title       string    `json:"title" gorm:"not null"`
	Description string    `json:"description" gorm:"type:text"`
	Severity    RiskLevel `json:"severity" gorm:"not null"`
	Status      string    `json:"status" gorm:"default:'open'"`         // "open", "investigating", "resolved", "false_positive"

	// Affected resource
	ResourceType string `json:"resource_type" gorm:"not null"`
	ResourceID   string `json:"resource_id" gorm:"not null"`
	ResourceName string `json:"resource_name"`

	// Detection information
	DetectedAt     time.Time `json:"detected_at"`
	DetectedByID   *uint     `json:"detected_by_id"`
	DetectedBy     *User     `json:"detected_by,omitempty" gorm:"foreignKey:DetectedByID"`
	DetectionMethod string   `json:"detection_method"`                  // "automated", "manual", "audit"
	AuditLogID     *uint     `json:"audit_log_id"`
	AuditLog       *AuditLog `json:"audit_log,omitempty" gorm:"foreignKey:AuditLogID"`

	// Resolution tracking
	AssignedToID   *uint      `json:"assigned_to_id"`
	AssignedTo     *User      `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToID"`
	ResolvedAt     *time.Time `json:"resolved_at"`
	ResolvedByID   *uint      `json:"resolved_by_id"`
	ResolvedBy     *User      `json:"resolved_by,omitempty" gorm:"foreignKey:ResolvedByID"`
	Resolution     string     `json:"resolution" gorm:"type:text"`
	ResolutionNotes string    `json:"resolution_notes" gorm:"type:text"`

	// Impact assessment
	ImpactLevel     string `json:"impact_level"`                        // "low", "medium", "high", "critical"
	BusinessImpact  string `json:"business_impact" gorm:"type:text"`
	TechnicalImpact string `json:"technical_impact" gorm:"type:text"`
	AffectedUsers   int    `json:"affected_users" gorm:"default:0"`
	DataExposed     bool   `json:"data_exposed" gorm:"default:false"`

	// Remediation
	RemediationSteps   string     `json:"remediation_steps" gorm:"type:text"`
	RemediationDueDate *time.Time `json:"remediation_due_date"`
	PreventiveMeasures string     `json:"preventive_measures" gorm:"type:text"`

	// Additional context
	Evidence    string `json:"evidence" gorm:"type:text"`               // JSON evidence data
	Attachments string `json:"attachments" gorm:"type:text"`            // JSON array of attachment URLs
	Tags        string `json:"tags" gorm:"type:text"`                   // JSON array of tags
	Notes       string `json:"notes" gorm:"type:text"`
}

// ComplianceAssessment represents a compliance assessment or audit
type ComplianceAssessment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Assessment identification
	AssessmentID string              `json:"assessment_id" gorm:"uniqueIndex;not null"`
	Name         string              `json:"name" gorm:"not null"`
	Description  string              `json:"description" gorm:"type:text"`
	Framework    ComplianceFramework `json:"framework" gorm:"not null"`
	Type         string              `json:"type"`                        // "internal", "external", "self", "third_party"

	// Assessment scope
	Scope           string     `json:"scope" gorm:"type:text"`           // JSON scope definition
	StartDate       time.Time  `json:"start_date"`
	EndDate         *time.Time `json:"end_date"`
	PlannedEndDate  time.Time  `json:"planned_end_date"`
	Status          string     `json:"status" gorm:"default:'planning'"` // "planning", "in_progress", "completed", "cancelled"

	// Assessment team
	LeadAssessorID  uint   `json:"lead_assessor_id" gorm:"not null"`
	LeadAssessor    User   `json:"lead_assessor" gorm:"foreignKey:LeadAssessorID"`
	AssessmentTeam  string `json:"assessment_team" gorm:"type:text"`     // JSON array of team member IDs
	ExternalAuditor string `json:"external_auditor"`

	// Rules and controls
	RuleID          *uint           `json:"rule_id"`
	Rule            *ComplianceRule `json:"rule,omitempty" gorm:"foreignKey:RuleID"`
	RulesAssessed   string          `json:"rules_assessed" gorm:"type:text"`      // JSON array of rule IDs
	ControlsTested  string          `json:"controls_tested" gorm:"type:text"`     // JSON array of controls

	// Results
	OverallStatus   ComplianceStatus `json:"overall_status" gorm:"default:'unknown'"`
	ComplianceScore float64          `json:"compliance_score" gorm:"default:0"`    // 0-100
	PassedControls  int              `json:"passed_controls" gorm:"default:0"`
	FailedControls  int              `json:"failed_controls" gorm:"default:0"`
	TotalControls   int              `json:"total_controls" gorm:"default:0"`

	// Findings
	CriticalFindings int    `json:"critical_findings" gorm:"default:0"`
	HighFindings     int    `json:"high_findings" gorm:"default:0"`
	MediumFindings   int    `json:"medium_findings" gorm:"default:0"`
	LowFindings      int    `json:"low_findings" gorm:"default:0"`
	Recommendations  string `json:"recommendations" gorm:"type:text"`
	ActionItems      string `json:"action_items" gorm:"type:text"`          // JSON array of action items

	// Documentation
	ReportURL       string `json:"report_url"`
	EvidenceURL     string `json:"evidence_url"`
	CertificateURL  string `json:"certificate_url"`
	ExecutiveSummary string `json:"executive_summary" gorm:"type:text"`
	TechnicalFindings string `json:"technical_findings" gorm:"type:text"`

	// Follow-up
	NextAssessmentDate *time.Time `json:"next_assessment_date"`
	FollowUpRequired   bool       `json:"follow_up_required" gorm:"default:false"`
	FollowUpDate       *time.Time `json:"follow_up_date"`
	FollowUpNotes      string     `json:"follow_up_notes" gorm:"type:text"`

	// Additional metadata
	Tags     string `json:"tags" gorm:"type:text"`                        // JSON array of tags
	Metadata string `json:"metadata" gorm:"type:text"`                    // JSON additional metadata
	Cost     float64 `json:"cost" gorm:"default:0"`                       // Assessment cost
	Currency string `json:"currency" gorm:"default:'USD'"`
}

// ComplianceReport represents a compliance report
type ComplianceReport struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Report identification
	ReportID    string              `json:"report_id" gorm:"uniqueIndex;not null"`
	Name        string              `json:"name" gorm:"not null"`
	Description string              `json:"description" gorm:"type:text"`
	Framework   ComplianceFramework `json:"framework" gorm:"not null"`
	Type        string              `json:"type"`                        // "periodic", "ad_hoc", "incident", "audit"

	// Report period
	PeriodStart time.Time  `json:"period_start"`
	PeriodEnd   time.Time  `json:"period_end"`
	GeneratedAt time.Time  `json:"generated_at"`
	ValidUntil  *time.Time `json:"valid_until"`

	// Report scope
	Scope       string `json:"scope" gorm:"type:text"`                   // JSON scope definition
	Departments string `json:"departments" gorm:"type:text"`             // JSON array of departments
	Systems     string `json:"systems" gorm:"type:text"`                 // JSON array of systems

	// Report data
	TotalEvents      int     `json:"total_events" gorm:"default:0"`
	ComplianceScore  float64 `json:"compliance_score" gorm:"default:0"`   // 0-100
	ViolationCount   int     `json:"violation_count" gorm:"default:0"`
	ResolvedViolations int   `json:"resolved_violations" gorm:"default:0"`
	OpenViolations   int     `json:"open_violations" gorm:"default:0"`

	// Risk metrics
	CriticalRisks int `json:"critical_risks" gorm:"default:0"`
	HighRisks     int `json:"high_risks" gorm:"default:0"`
	MediumRisks   int `json:"medium_risks" gorm:"default:0"`
	LowRisks      int `json:"low_risks" gorm:"default:0"`

	// Report content
	ExecutiveSummary string `json:"executive_summary" gorm:"type:text"`
	KeyFindings      string `json:"key_findings" gorm:"type:text"`
	Recommendations  string `json:"recommendations" gorm:"type:text"`
	ActionPlan       string `json:"action_plan" gorm:"type:text"`
	DetailedData     string `json:"detailed_data" gorm:"type:text"`       // JSON detailed report data

	// Report generation
	GeneratedByID   uint   `json:"generated_by_id" gorm:"not null"`
	GeneratedBy     User   `json:"generated_by" gorm:"foreignKey:GeneratedByID"`
	GenerationTime  int    `json:"generation_time_ms" gorm:"default:0"`
	ReportFormat    string `json:"report_format" gorm:"default:'json'"`   // "json", "pdf", "excel", "csv"
	ReportURL       string `json:"report_url"`
	ReportSize      int64  `json:"report_size" gorm:"default:0"`

	// Distribution
	Recipients      string `json:"recipients" gorm:"type:text"`           // JSON array of recipient emails
	DistributedAt   *time.Time `json:"distributed_at"`
	DistributionMethod string `json:"distribution_method"`                // "email", "portal", "api"

	// Approval and review
	RequiresApproval bool       `json:"requires_approval" gorm:"default:false"`
	ApprovedAt       *time.Time `json:"approved_at"`
	ApprovedByID     *uint      `json:"approved_by_id"`
	ApprovedBy       *User      `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`
	ReviewNotes      string     `json:"review_notes" gorm:"type:text"`

	// Additional metadata
	Tags     string `json:"tags" gorm:"type:text"`                        // JSON array of tags
	Metadata string `json:"metadata" gorm:"type:text"`                    // JSON additional metadata
	Version  string `json:"version" gorm:"default:'1.0'"`
	IsPublic bool   `json:"is_public" gorm:"default:false"`
}

// TableName returns the table name for AuditLog model
func (AuditLog) TableName() string {
	return "audit_logs"
}

// TableName returns the table name for ComplianceRule model
func (ComplianceRule) TableName() string {
	return "compliance_rules"
}

// TableName returns the table name for ComplianceViolation model
func (ComplianceViolation) TableName() string {
	return "compliance_violations"
}

// TableName returns the table name for ComplianceAssessment model
func (ComplianceAssessment) TableName() string {
	return "compliance_assessments"
}

// TableName returns the table name for ComplianceReport model
func (ComplianceReport) TableName() string {
	return "compliance_reports"
}
