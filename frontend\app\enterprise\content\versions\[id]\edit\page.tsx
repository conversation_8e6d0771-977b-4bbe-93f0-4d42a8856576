'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  TagIcon,
  FolderIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../../../components/Layout/Layout';
import { useAuthStore } from '../../../../../stores/authStore';
import { contentApi } from '../../../../../services/enterpriseApi';
import { ContentVersion, ContentRepository } from '../../../../../types/enterprise';

interface ContentVersionFormData {
  repository_id: number;
  version_number: string;
  file_name: string;
  content: string;
  metadata: any;
  approval_status: 'pending' | 'approved' | 'rejected';
}

const EditContentVersionPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const versionId = params.id as string;
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [error, setError] = useState('');
  const [repositories, setRepositories] = useState<ContentRepository[]>([]);

  const [formData, setFormData] = useState<ContentVersionFormData>({
    repository_id: 0,
    version_number: '',
    file_name: '',
    content: '',
    metadata: {},
    approval_status: 'pending'
  });

  useEffect(() => {
    if (versionId) {
      fetchVersion();
      fetchRepositories();
    }
  }, [versionId]);

  const fetchVersion = async () => {
    try {
      const response = await contentApi.getVersion(parseInt(versionId));
      const version = response.data;
      
      setFormData({
        repository_id: version.document_id || 0,
        version_number: version.version_number || '',
        file_name: version.title || '',
        content: version.content || '',
        metadata: version.metadata ? JSON.parse(version.metadata) : {},
        approval_status: (version.approval_status as 'pending' | 'approved' | 'rejected') || 'pending'
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch content version');
    } finally {
      setInitialLoading(false);
    }
  };

  const fetchRepositories = async () => {
    try {
      const response = await contentApi.getRepositories();
      setRepositories(response.data || []);
    } catch (err) {
      console.error('Failed to fetch repositories:', err);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'repository_id' ? parseInt(value) : value
    }));
  };

  const handleMetadataChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    try {
      const metadata = JSON.parse(e.target.value);
      setFormData(prev => ({ ...prev, metadata }));
    } catch (err) {
      // Keep the raw text if it's not valid JSON
      setFormData(prev => ({ ...prev, metadata: e.target.value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.version_number.trim() || !formData.file_name.trim()) {
      setError('Version number and file name are required');
      return;
    }

    try {
      setLoading(true);
      setError('');
      
      await contentApi.updateVersion(parseInt(versionId), formData);
      router.push('/enterprise/content/versions');
    } catch (err: any) {
      setError(err.message || 'Failed to update content version');
    } finally {
      setLoading(false);
    }
  };

  if (!user || (user.role !== 'admin' && user.role !== 'editor')) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin or editor to edit content versions.</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (initialLoading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading content version...</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/enterprise/content/versions"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Content Versions
          </Link>
          
          <h1 className="text-3xl font-bold text-gray-900">Edit Content Version</h1>
          <p className="text-gray-600 mt-1">Update content version details</p>
        </div>

        {/* Form */}
        <div className="max-w-2xl">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            {/* Basic Information */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="repository_id" className="block text-sm font-medium text-gray-700 mb-2">
                    <FolderIcon className="h-4 w-4 inline mr-1" />
                    Repository *
                  </label>
                  <select
                    id="repository_id"
                    name="repository_id"
                    value={formData.repository_id}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select a repository</option>
                    {repositories.map((repo) => (
                      <option key={repo.id} value={repo.id}>
                        {repo.repository_name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="version_number" className="block text-sm font-medium text-gray-700 mb-2">
                    Version Number *
                  </label>
                  <input
                    type="text"
                    id="version_number"
                    name="version_number"
                    value={formData.version_number}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="e.g., 1.0.0"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="file_name" className="block text-sm font-medium text-gray-700 mb-2">
                    File Name *
                  </label>
                  <input
                    type="text"
                    id="file_name"
                    name="file_name"
                    value={formData.file_name}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                    placeholder="Enter file name"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="approval_status" className="block text-sm font-medium text-gray-700 mb-2">
                    Approval Status
                  </label>
                  <select
                    id="approval_status"
                    name="approval_status"
                    value={formData.approval_status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Content</h3>
              
              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                  <DocumentTextIcon className="h-4 w-4 inline mr-1" />
                  Content
                </label>
                <textarea
                  id="content"
                  name="content"
                  value={formData.content}
                  onChange={handleInputChange}
                  rows={10}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Enter content"
                />
              </div>
            </div>

            {/* Metadata */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Metadata</h3>
              
              <div>
                <label htmlFor="metadata" className="block text-sm font-medium text-gray-700 mb-2">
                  <TagIcon className="h-4 w-4 inline mr-1" />
                  Metadata (JSON)
                </label>
                <textarea
                  id="metadata"
                  name="metadata"
                  value={typeof formData.metadata === 'string' ? formData.metadata : JSON.stringify(formData.metadata, null, 2)}
                  onChange={handleMetadataChange}
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent font-mono text-sm"
                  placeholder='{"key": "value"}'
                />
                <p className="text-xs text-gray-500 mt-1">Enter metadata as JSON format</p>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3">
              <Link
                href="/enterprise/content/versions"
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {loading ? 'Updating...' : 'Update Version'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default EditContentVersionPage;
