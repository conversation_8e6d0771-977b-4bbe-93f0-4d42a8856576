package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GetReverseRegulationRelationships returns reverse relationships for a regulation
func GetReverseRegulationRelationships(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get all relationships where this regulation is the target
	var incomingRelationships []models.RegulationRelationship
	db.Preload("SourceRegulation").
		Where("target_regulation_id = ? AND is_active = ?", regulationID, true).
		Find(&incomingRelationships)

	// Group by relationship type for better organization
	relationshipMap := make(map[string][]gin.H)

	for _, rel := range incomingRelationships {
		relType := rel.RelationshipType
		if relationshipMap[relType] == nil {
			relationshipMap[relType] = make([]gin.H, 0)
		}

		relationshipMap[relType] = append(relationshipMap[relType], gin.H{
			"id":                rel.ID,
			"source_regulation": gin.H{
				"id":          rel.SourceRegulation.ID,
				"title":       rel.SourceRegulation.Title,
				"short_title": rel.SourceRegulation.ShortTitle,
				"type":        rel.SourceRegulation.Type,
				"status":      rel.SourceRegulation.Status,
			},
			"description": rel.Description,
			"created_at":  rel.CreatedAt,
		})
	}

	// Convert map to array format for consistent response
	relationshipTypes := make([]gin.H, 0)
	for relType, relationships := range relationshipMap {
		relationshipTypes = append(relationshipTypes, gin.H{
			"relationship_type": relType,
			"count":             len(relationships),
			"relationships":     relationships,
		})
	}

	response := gin.H{
		"regulation_id":      regulationID,
		"regulation_title":   regulation.Title,
		"total_relationships": len(incomingRelationships),
		"relationship_types":  relationshipTypes,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Reverse regulation relationships retrieved successfully",
		Data:    response,
	})
}

// GetRegulationImpactAnalysis analyzes the impact of a regulation based on its relationships
func GetRegulationImpactAnalysis(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Count outgoing relationships (regulations this one affects)
	var outgoingCount int64
	db.Model(&models.RegulationRelationship{}).
		Where("source_regulation_id = ? AND is_active = ?", regulationID, true).
		Count(&outgoingCount)

	// Count incoming relationships (regulations that affect this one)
	var incomingCount int64
	db.Model(&models.RegulationRelationship{}).
		Where("target_regulation_id = ? AND is_active = ?", regulationID, true).
		Count(&incomingCount)

	// Get relationship type breakdown for outgoing
	var outgoingTypes []struct {
		RelationshipType string `json:"relationship_type"`
		Count            int64  `json:"count"`
	}
	db.Model(&models.RegulationRelationship{}).
		Select("relationship_type, COUNT(*) as count").
		Where("source_regulation_id = ? AND is_active = ?", regulationID, true).
		Group("relationship_type").
		Find(&outgoingTypes)

	// Get relationship type breakdown for incoming
	var incomingTypes []struct {
		RelationshipType string `json:"relationship_type"`
		Count            int64  `json:"count"`
	}
	db.Model(&models.RegulationRelationship{}).
		Select("relationship_type, COUNT(*) as count").
		Where("target_regulation_id = ? AND is_active = ?", regulationID, true).
		Group("relationship_type").
		Find(&incomingTypes)

	// Calculate impact score (simple algorithm)
	impactScore := float64(outgoingCount*2 + incomingCount) // Outgoing relationships weighted more heavily

	var impactLevel string
	switch {
	case impactScore >= 20:
		impactLevel = "Very High"
	case impactScore >= 10:
		impactLevel = "High"
	case impactScore >= 5:
		impactLevel = "Medium"
	case impactScore > 0:
		impactLevel = "Low"
	default:
		impactLevel = "None"
	}

	response := gin.H{
		"regulation": gin.H{
			"id":          regulation.ID,
			"title":       regulation.Title,
			"short_title": regulation.ShortTitle,
			"type":        regulation.Type,
			"status":      regulation.Status,
		},
		"impact_analysis": gin.H{
			"impact_score":       impactScore,
			"impact_level":       impactLevel,
			"outgoing_count":     outgoingCount,
			"incoming_count":     incomingCount,
			"total_relationships": outgoingCount + incomingCount,
		},
		"outgoing_breakdown": outgoingTypes,
		"incoming_breakdown": incomingTypes,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation impact analysis retrieved successfully",
		Data:    response,
	})
}

// GetRegulationDependencyTree returns a tree structure of regulation dependencies
func GetRegulationDependencyTree(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get direct dependencies (regulations this one depends on)
	var dependencies []models.RegulationRelationship
	db.Preload("SourceRegulation").
		Where("target_regulation_id = ? AND is_active = ? AND relationship_type IN (?)", 
			regulationID, true, []string{"depends_on", "references", "implements"}).
		Find(&dependencies)

	// Get direct dependents (regulations that depend on this one)
	var dependents []models.RegulationRelationship
	db.Preload("TargetRegulation").
		Where("source_regulation_id = ? AND is_active = ? AND relationship_type IN (?)", 
			regulationID, true, []string{"depends_on", "references", "implements"}).
		Find(&dependents)

	// Convert to tree structure
	dependencyNodes := make([]gin.H, len(dependencies))
	for i, dep := range dependencies {
		dependencyNodes[i] = gin.H{
			"regulation": gin.H{
				"id":          dep.SourceRegulation.ID,
				"title":       dep.SourceRegulation.Title,
				"short_title": dep.SourceRegulation.ShortTitle,
				"type":        dep.SourceRegulation.Type,
				"status":      dep.SourceRegulation.Status,
			},
			"relationship_type": dep.RelationshipType,
			"description":       dep.Description,
		}
	}

	dependentNodes := make([]gin.H, len(dependents))
	for i, dep := range dependents {
		dependentNodes[i] = gin.H{
			"regulation": gin.H{
				"id":          dep.TargetRegulation.ID,
				"title":       dep.TargetRegulation.Title,
				"short_title": dep.TargetRegulation.ShortTitle,
				"type":        dep.TargetRegulation.Type,
				"status":      dep.TargetRegulation.Status,
			},
			"relationship_type": dep.RelationshipType,
			"description":       dep.Description,
		}
	}

	response := gin.H{
		"root_regulation": gin.H{
			"id":          regulation.ID,
			"title":       regulation.Title,
			"short_title": regulation.ShortTitle,
			"type":        regulation.Type,
			"status":      regulation.Status,
		},
		"dependencies": dependencyNodes,
		"dependents":   dependentNodes,
		"summary": gin.H{
			"dependency_count": len(dependencies),
			"dependent_count":  len(dependents),
		},
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation dependency tree retrieved successfully",
		Data:    response,
	})
}
