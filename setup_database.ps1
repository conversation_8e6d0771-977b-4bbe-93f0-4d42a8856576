# PowerShell script to set up PostgreSQL for NoteControl
# Run this script in PowerShell as Administrator

Write-Host "Setting up PostgreSQL for NoteControl Federal Register Clone..." -ForegroundColor Green

# Check if PostgreSQL is installed
$pgPath = Get-Command psql -ErrorAction SilentlyContinue
if (-not $pgPath) {
    Write-Host "PostgreSQL is not installed or not in PATH." -ForegroundColor Red
    Write-Host "Please install PostgreSQL from: https://www.postgresql.org/download/windows/" -ForegroundColor Yellow
    Write-Host "Make sure to add PostgreSQL bin directory to your PATH." -ForegroundColor Yellow
    exit 1
}

Write-Host "PostgreSQL found at: $($pgPath.Source)" -ForegroundColor Green

# Check if PostgreSQL service is running
$pgService = Get-Service -Name "postgresql*" -ErrorAction SilentlyContinue
if ($pgService) {
    if ($pgService.Status -ne "Running") {
        Write-Host "Starting PostgreSQL service..." -ForegroundColor Yellow
        Start-Service $pgService.Name
    }
    Write-Host "PostgreSQL service is running." -ForegroundColor Green
} else {
    Write-Host "PostgreSQL service not found. Please ensure PostgreSQL is properly installed." -ForegroundColor Red
    exit 1
}

# Prompt for postgres superuser password
$postgresPassword = Read-Host "Enter the password for PostgreSQL superuser (postgres)" -AsSecureString
$postgresPasswordPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($postgresPassword))

# Test connection to PostgreSQL
Write-Host "Testing connection to PostgreSQL..." -ForegroundColor Yellow
$env:PGPASSWORD = $postgresPasswordPlain
$testConnection = psql -h localhost -U postgres -d postgres -c "SELECT version();" 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to connect to PostgreSQL. Please check your password and try again." -ForegroundColor Red
    Write-Host "Error: $testConnection" -ForegroundColor Red
    exit 1
}

Write-Host "Successfully connected to PostgreSQL!" -ForegroundColor Green

# Create the database and user
Write-Host "Creating database and setting up user..." -ForegroundColor Yellow
$setupResult = psql -h localhost -U postgres -d postgres -f setup_postgresql.sql 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Failed to set up database. Error:" -ForegroundColor Red
    Write-Host $setupResult -ForegroundColor Red
    exit 1
}

Write-Host "Database setup completed successfully!" -ForegroundColor Green

# Test connection to the new database
Write-Host "Testing connection to the new database..." -ForegroundColor Yellow
$env:PGPASSWORD = "NoteControl2024!"
$testNewDB = psql -h localhost -U postgres -d federal_register_db -c "SELECT current_database();" 2>&1
if ($LASTEXITCODE -ne 0) {
    Write-Host "Warning: Could not connect to the new database with the application user." -ForegroundColor Yellow
    Write-Host "Error: $testNewDB" -ForegroundColor Yellow
} else {
    Write-Host "Successfully connected to federal_register_db!" -ForegroundColor Green
}

# Clean up environment variable
Remove-Item Env:PGPASSWORD -ErrorAction SilentlyContinue

Write-Host "`nSetup Summary:" -ForegroundColor Cyan
Write-Host "- Database: federal_register_db" -ForegroundColor White
Write-Host "- User: postgres" -ForegroundColor White
Write-Host "- Password: NoteControl2024!" -ForegroundColor White
Write-Host "- Host: localhost" -ForegroundColor White
Write-Host "- Port: 5432" -ForegroundColor White

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Navigate to the backend directory: cd backend" -ForegroundColor White
Write-Host "2. Run the Go server: go run cmd/server/main.go" -ForegroundColor White
Write-Host "3. The server will automatically create tables and run migrations" -ForegroundColor White

Write-Host "`nDatabase setup completed! You can now start the backend server." -ForegroundColor Green
