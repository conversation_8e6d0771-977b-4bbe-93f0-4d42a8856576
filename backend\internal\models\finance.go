package models

import (
	"time"

	"gorm.io/gorm"
)

type Finance struct {
	ID                    uint             `json:"id" gorm:"primaryKey"`
	Amount                float64          `json:"amount" gorm:"not null"`
	Year                  int              `json:"year" gorm:"not null"`
	Description           string           `json:"description"`
	DocumentID            *uint            `json:"document_id"`
	Document              *Document        `json:"document,omitempty" gorm:"foreignKey:DocumentID"`
	RegulationID          *uint            `json:"regulation_id"`
	Regulation            *LawsAndRules    `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`
	BudgetType            string           `json:"budget_type" gorm:"default:'original'"` // 'original' or 'actual'
	PerformancePercentage float64          `json:"performance_percentage" gorm:"default:100.00"`
	IsAutoCalculated      bool             `json:"is_auto_calculated" gorm:"default:false"`
	SourceFinanceID       *uint            `json:"source_finance_id"`
	SourceFinance         *Finance         `json:"source_finance,omitempty" gorm:"foreignKey:SourceFinanceID"`
	CategoryID            *uint            `json:"category_id"`
	Category              *FinanceCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	CreatedAt             time.Time        `json:"created_at"`
	UpdatedAt             time.Time        `json:"updated_at"`
	DeletedAt             gorm.DeletedAt   `json:"-" gorm:"index"`
}

type FinancePerformance struct {
	ID                    uint          `json:"id" gorm:"primaryKey"`
	DocumentID            *uint         `json:"document_id"`
	Document              *Document     `json:"document,omitempty" gorm:"foreignKey:DocumentID"`
	RegulationID          *uint         `json:"regulation_id"`
	Regulation            *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`
	Year                  int           `json:"year" gorm:"not null"`
	PerformancePercentage float64       `json:"performance_percentage" gorm:"not null;default:100.00"`
	PerformanceNotes      string        `json:"performance_notes"`
	EvaluationDate        time.Time     `json:"evaluation_date" gorm:"default:CURRENT_TIMESTAMP"`
	EvaluatedByID         *uint         `json:"evaluated_by_id"`
	EvaluatedBy           *User         `json:"evaluated_by,omitempty" gorm:"foreignKey:EvaluatedByID"`
	CreatedAt             time.Time     `json:"created_at"`
	UpdatedAt             time.Time     `json:"updated_at"`
}

type FinanceCategory struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"not null"`
	Description string    `json:"description"`
	Color       string    `json:"color" gorm:"default:'#3B82F6'"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func (Finance) TableName() string {
	return "finances"
}

func (FinancePerformance) TableName() string {
	return "finance_performance"
}

func (FinanceCategory) TableName() string {
	return "finance_categories"
}
