/**
 * Frontend Preloading Test Script
 * 
 * This script tests the preloading functionality in the frontend forms.
 * Run this in the browser console on each form page to validate preloading.
 */

class PreloadingTester {
  constructor() {
    this.baseURL = '/api/v1/preloading';
    this.token = localStorage.getItem('token');
    this.results = [];
  }

  async runAllTests() {
    console.log('🧪 Starting Frontend Preloading Tests...');
    console.log('=====================================');

    await this.testDocumentPreloading();
    await this.testRegulationPreloading();
    await this.testAgencyPreloading();
    await this.testCategoryPreloading();
    await this.testProceedingPreloading();
    await this.testFinancePreloading();
    await this.testUtilityEndpoints();

    this.printSummary();
  }

  async testDocumentPreloading() {
    console.log('\n📄 Testing Document Preloading...');
    
    try {
      const response = await fetch(`${this.baseURL}/documents`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Document defaults loaded:', data);
        
        // Validate required fields
        const requiredFields = ['publication_date', 'effective_date', 'comment_due_date', 'fr_document_number'];
        const missingFields = requiredFields.filter(field => !data[field]);
        
        if (missingFields.length === 0) {
          console.log('✅ All required document fields present');
          this.results.push({ test: 'Document Preloading', status: 'PASS' });
        } else {
          console.log('❌ Missing required fields:', missingFields);
          this.results.push({ test: 'Document Preloading', status: 'FAIL', error: `Missing fields: ${missingFields.join(', ')}` });
        }

        // Test date calculations
        const pubDate = new Date(data.publication_date);
        const effDate = new Date(data.effective_date);
        const commentDate = new Date(data.comment_due_date);
        
        const daysDiffEff = Math.round((effDate - pubDate) / (1000 * 60 * 60 * 24));
        const daysDiffComment = Math.round((commentDate - pubDate) / (1000 * 60 * 60 * 24));
        
        if (daysDiffEff === 30 && daysDiffComment === 60) {
          console.log('✅ Date calculations correct (30 days effective, 60 days comment)');
        } else {
          console.log(`❌ Date calculations incorrect: ${daysDiffEff} days effective, ${daysDiffComment} days comment`);
        }

      } else {
        console.log('❌ Failed to load document defaults:', response.status);
        this.results.push({ test: 'Document Preloading', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing document preloading:', error);
      this.results.push({ test: 'Document Preloading', status: 'ERROR', error: error.message });
    }
  }

  async testRegulationPreloading() {
    console.log('\n📋 Testing Regulation Preloading...');
    
    try {
      const response = await fetch(`${this.baseURL}/regulations`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Regulation defaults loaded:', data);
        
        const requiredFields = ['enactment_date', 'publication_date', 'effective_date', 'version_number'];
        const missingFields = requiredFields.filter(field => !data[field]);
        
        if (missingFields.length === 0) {
          console.log('✅ All required regulation fields present');
          this.results.push({ test: 'Regulation Preloading', status: 'PASS' });
        } else {
          console.log('❌ Missing required fields:', missingFields);
          this.results.push({ test: 'Regulation Preloading', status: 'FAIL', error: `Missing fields: ${missingFields.join(', ')}` });
        }
      } else {
        console.log('❌ Failed to load regulation defaults:', response.status);
        this.results.push({ test: 'Regulation Preloading', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing regulation preloading:', error);
      this.results.push({ test: 'Regulation Preloading', status: 'ERROR', error: error.message });
    }
  }

  async testAgencyPreloading() {
    console.log('\n🏛️ Testing Agency Preloading...');
    
    try {
      const response = await fetch(`${this.baseURL}/agencies`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Agency defaults loaded:', data);
        
        const requiredFields = ['established_at', 'country', 'agency_type'];
        const missingFields = requiredFields.filter(field => !data[field]);
        
        if (missingFields.length === 0) {
          console.log('✅ All required agency fields present');
          this.results.push({ test: 'Agency Preloading', status: 'PASS' });
        } else {
          console.log('❌ Missing required fields:', missingFields);
          this.results.push({ test: 'Agency Preloading', status: 'FAIL', error: `Missing fields: ${missingFields.join(', ')}` });
        }
      } else {
        console.log('❌ Failed to load agency defaults:', response.status);
        this.results.push({ test: 'Agency Preloading', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing agency preloading:', error);
      this.results.push({ test: 'Agency Preloading', status: 'ERROR', error: error.message });
    }
  }

  async testCategoryPreloading() {
    console.log('\n📁 Testing Category Preloading...');
    
    try {
      const response = await fetch(`${this.baseURL}/categories`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Category defaults loaded:', data);
        
        const requiredFields = ['color', 'sort_order'];
        const missingFields = requiredFields.filter(field => data[field] === undefined);
        
        if (missingFields.length === 0) {
          console.log('✅ All required category fields present');
          this.results.push({ test: 'Category Preloading', status: 'PASS' });
        } else {
          console.log('❌ Missing required fields:', missingFields);
          this.results.push({ test: 'Category Preloading', status: 'FAIL', error: `Missing fields: ${missingFields.join(', ')}` });
        }
      } else {
        console.log('❌ Failed to load category defaults:', response.status);
        this.results.push({ test: 'Category Preloading', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing category preloading:', error);
      this.results.push({ test: 'Category Preloading', status: 'ERROR', error: error.message });
    }
  }

  async testProceedingPreloading() {
    console.log('\n⚖️ Testing Proceeding Preloading...');
    
    try {
      const response = await fetch(`${this.baseURL}/proceedings?name=Test Proceeding`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Proceeding defaults loaded:', data);
        
        const requiredFields = ['initiation_date', 'planned_start_date', 'planned_end_date', 'unique_id'];
        const missingFields = requiredFields.filter(field => !data[field]);
        
        if (missingFields.length === 0) {
          console.log('✅ All required proceeding fields present');
          this.results.push({ test: 'Proceeding Preloading', status: 'PASS' });
        } else {
          console.log('❌ Missing required fields:', missingFields);
          this.results.push({ test: 'Proceeding Preloading', status: 'FAIL', error: `Missing fields: ${missingFields.join(', ')}` });
        }
      } else {
        console.log('❌ Failed to load proceeding defaults:', response.status);
        this.results.push({ test: 'Proceeding Preloading', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing proceeding preloading:', error);
      this.results.push({ test: 'Proceeding Preloading', status: 'ERROR', error: error.message });
    }
  }

  async testFinancePreloading() {
    console.log('\n💰 Testing Finance Preloading...');
    
    try {
      const response = await fetch(`${this.baseURL}/finances`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Finance defaults loaded:', data);
        
        const currentYear = new Date().getFullYear();
        if (data.year === currentYear) {
          console.log('✅ Year correctly set to current year');
          this.results.push({ test: 'Finance Preloading', status: 'PASS' });
        } else {
          console.log(`❌ Year incorrect: Expected ${currentYear}, got ${data.year}`);
          this.results.push({ test: 'Finance Preloading', status: 'FAIL', error: `Incorrect year: ${data.year}` });
        }
      } else {
        console.log('❌ Failed to load finance defaults:', response.status);
        this.results.push({ test: 'Finance Preloading', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing finance preloading:', error);
      this.results.push({ test: 'Finance Preloading', status: 'ERROR', error: error.message });
    }
  }

  async testUtilityEndpoints() {
    console.log('\n🔧 Testing Utility Endpoints...');
    
    // Test slug generation
    try {
      const response = await fetch(`${this.baseURL}/slug?text=Test Agency Name`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Slug generation working:', data);
        
        if (data.slug === 'test-agency-name') {
          console.log('✅ Slug generation correct');
          this.results.push({ test: 'Slug Generation', status: 'PASS' });
        } else {
          console.log(`❌ Slug generation incorrect: Expected 'test-agency-name', got '${data.slug}'`);
          this.results.push({ test: 'Slug Generation', status: 'FAIL', error: `Incorrect slug: ${data.slug}` });
        }
      } else {
        console.log('❌ Failed to generate slug:', response.status);
        this.results.push({ test: 'Slug Generation', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing slug generation:', error);
      this.results.push({ test: 'Slug Generation', status: 'ERROR', error: error.message });
    }

    // Test FR number generation
    try {
      const response = await fetch(`${this.baseURL}/fr-number`, {
        headers: { 'Authorization': `Bearer ${this.token}` }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ FR number generation working:', data);
        
        const currentYear = new Date().getFullYear();
        if (data.fr_document_number.startsWith(currentYear.toString())) {
          console.log('✅ FR number format correct');
          this.results.push({ test: 'FR Number Generation', status: 'PASS' });
        } else {
          console.log(`❌ FR number format incorrect: ${data.fr_document_number}`);
          this.results.push({ test: 'FR Number Generation', status: 'FAIL', error: `Incorrect format: ${data.fr_document_number}` });
        }
      } else {
        console.log('❌ Failed to generate FR number:', response.status);
        this.results.push({ test: 'FR Number Generation', status: 'FAIL', error: `HTTP ${response.status}` });
      }
    } catch (error) {
      console.log('❌ Error testing FR number generation:', error);
      this.results.push({ test: 'FR Number Generation', status: 'ERROR', error: error.message });
    }
  }

  printSummary() {
    console.log('\n📊 Test Summary');
    console.log('================');
    
    const passed = this.results.filter(r => r.status === 'PASS').length;
    const failed = this.results.filter(r => r.status === 'FAIL').length;
    const errors = this.results.filter(r => r.status === 'ERROR').length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`💥 Errors: ${errors}`);
    console.log(`📈 Total: ${this.results.length}`);
    
    if (failed > 0 || errors > 0) {
      console.log('\n❌ Failed/Error Tests:');
      this.results
        .filter(r => r.status !== 'PASS')
        .forEach(r => console.log(`   - ${r.test}: ${r.error || 'Unknown error'}`));
    }
    
    console.log('\n🎯 Overall Status:', passed === this.results.length ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED');
  }
}

// Usage instructions
console.log(`
🧪 Frontend Preloading Test Script Loaded!

To run tests:
1. Make sure you're logged in and have a valid token
2. Run: const tester = new PreloadingTester(); tester.runAllTests();

Or test individual components:
- tester.testDocumentPreloading()
- tester.testRegulationPreloading()
- tester.testAgencyPreloading()
- tester.testCategoryPreloading()
- tester.testProceedingPreloading()
- tester.testFinancePreloading()
- tester.testUtilityEndpoints()
`);

// Auto-export for easy access
window.PreloadingTester = PreloadingTester;
