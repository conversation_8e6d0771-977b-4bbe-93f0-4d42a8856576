'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { hrApi } from '../../../../../services/enterpriseApi';
import { Department, Employee } from '../../../../../types/enterprise';

const EditDepartmentPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const departmentId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [formData, setFormData] = useState<Partial<Department>>({
    department_code: '',
    department_name: '',
    name: '',
    description: '',
    location: '',
    phone: '',
    email: '',
    cost_center_code: '',
    budget_amount: 0,
    actual_amount: 0,
    currency_code: 'USD',
    manager_id: undefined,
    parent_department_id: undefined,
    level: 1,
    employee_count: 0,
    is_active: true,
    established_date: '',
    metadata: ''
  });

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
      fetchEmployees();
    }
  }, [departmentId]);

  const fetchDepartment = async () => {
    try {
      setFetchLoading(true);
      const response = await hrApi.getDepartment(departmentId);
      const department = response.data;
      setFormData({
        ...department,
        established_date: department.established_date?.split('T')[0] || '',
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch department');
    } finally {
      setFetchLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await hrApi.getEmployees();
      setEmployees(response.data);
    } catch (err: any) {
      console.log('Failed to fetch employees:', err.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Ensure name matches department_name
      const submitData = {
        ...formData,
        name: formData.department_name
      };
      await hrApi.updateDepartment(departmentId, submitData);
      router.push(`/enterprise/hr/departments/${departmentId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update department');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  if (fetchLoading) return <div className="p-6">Loading department...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Department</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/hr/departments/${departmentId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Department Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department Code *
            </label>
            <input
              type="text"
              name="department_code"
              value={formData.department_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Department Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department Name *
            </label>
            <input
              type="text"
              name="department_name"
              value={formData.department_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Location
            </label>
            <input
              type="text"
              name="location"
              value={formData.location}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Manager */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department Manager
            </label>
            <select
              name="manager_id"
              value={formData.manager_id || ''}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Manager</option>
              {employees.map((employee) => (
                <option key={employee.id} value={employee.id}>
                  {employee.first_name} {employee.last_name} - {employee.job_title}
                </option>
              ))}
            </select>
          </div>

          {/* Budget Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Amount
            </label>
            <input
              type="number"
              name="budget_amount"
              value={formData.budget_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Actual Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Actual Amount
            </label>
            <input
              type="number"
              name="actual_amount"
              value={formData.actual_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Active Status */}
        <div className="mt-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleChange}
              className="mr-2"
            />
            Department is Active
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/hr/departments/${departmentId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Department'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditDepartmentPage;
