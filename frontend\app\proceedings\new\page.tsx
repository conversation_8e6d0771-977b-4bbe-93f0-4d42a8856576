'use client'

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentPlusIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import ProceedingForm from '../../components/Proceeding/ProceedingForm';
import apiService from '../../services/api';

const NewProceedingPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (formData: any) => {
    try {
      setLoading(true);
      setError('');
      
      const response = await apiService.createProceeding(formData);
      router.push(`/proceedings/${response.data.id}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create proceeding');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/proceedings');
  };

  // Check permissions
  if (!user || (user.role !== 'admin' && user.role !== 'editor')) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <DocumentPlusIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-600 mb-4">
              You don't have permission to create proceedings.
            </p>
            <Link
              href="/proceedings"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Proceedings
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <Link
            href="/proceedings"
            className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">New Proceeding</h1>
            <p className="text-gray-600 mt-1">Create a new formal proceeding</p>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md">
          <div className="p-6">
            <ProceedingForm
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              isLoading={loading}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default NewProceedingPage;
