package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"federal-register-clone/internal/auth"
	"federal-register-clone/internal/models"

	"github.com/gin-gonic/gin"
)

// AuthRequired middleware checks for valid JWT token
func AuthRequired() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Authorization header is required",
			})
			c.Abort()
			return
		}

		// Check if the header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header must start with 'Bearer '",
			})
			c.Abort()
			return
		}

		// Extract the token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Token is required",
			})
			c.Abort()
			return
		}

		// Validate the token
		claims, err := auth.ValidateToken(token)
		if err != nil {
			c.J<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Invalid token",
			})
			c.Abort()
			return
		}

		// Get user from database
		user, err := auth.GetUserByID(claims.UserID)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not found",
			})
			c.Abort()
			return
		}

		// Check if user is active
		if !user.IsActive {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User account is inactive",
			})
			c.Abort()
			return
		}

		// Set user in context
		c.Set("user", user)
		c.Set("userID", user.ID)
		c.Set("user_id", user.ID) // For backward compatibility
		c.Set("userRole", user.Role)

		c.Next()
	}
}

// RoleRequired middleware checks if user has required role
func RoleRequired(requiredRole string) gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			fmt.Printf("DEBUG: RoleRequired - User not found in context\n")
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		userModel, ok := user.(*models.User)
		if !ok {
			fmt.Printf("DEBUG: RoleRequired - Invalid user data type\n")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid user data",
			})
			c.Abort()
			return
		}

		fmt.Printf("DEBUG: RoleRequired - User role: %s, Required role: %s\n", userModel.Role, requiredRole)

		// Check role hierarchy
		if !hasRequiredRole(string(userModel.Role), requiredRole) {
			fmt.Printf("DEBUG: RoleRequired - Role check failed\n")
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Insufficient permissions",
			})
			c.Abort()
			return
		}

		fmt.Printf("DEBUG: RoleRequired - Role check passed\n")
		c.Next()
	}
}

// hasRequiredRole checks if user role has sufficient permissions
func hasRequiredRole(userRole, requiredRole string) bool {
	// Define role hierarchy (higher roles include lower role permissions)
	roleHierarchy := map[string]int{
		"viewer_level_1": 1,
		"viewer_level_2": 2,
		"viewer_level_3": 3,
		"viewer":         3, // Legacy viewer role maps to level 3
		"editor":         4,
		"reviewer":       5,
		"publisher":      6,
		"admin":          7,
	}

	userLevel, userExists := roleHierarchy[userRole]
	requiredLevel, requiredExists := roleHierarchy[requiredRole]

	if !userExists || !requiredExists {
		return false
	}

	return userLevel >= requiredLevel
}

// OptionalAuth middleware that doesn't require authentication but sets user if token is provided
func OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Check if the header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.Next()
			return
		}

		// Extract the token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.Next()
			return
		}

		// Validate the token
		claims, err := auth.ValidateToken(token)
		if err != nil {
			c.Next()
			return
		}

		// Get user from database
		user, err := auth.GetUserByID(claims.UserID)
		if err != nil {
			c.Next()
			return
		}

		// Check if user is active
		if !user.IsActive {
			c.Next()
			return
		}

		// Set user in context
		c.Set("user", user)
		c.Set("userID", user.ID)
		c.Set("userRole", user.Role)

		c.Next()
	}
}

// GetCurrentUser returns the current authenticated user from context
func GetCurrentUser(c *gin.Context) (*models.User, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}

	userModel, ok := user.(*models.User)
	if !ok {
		return nil, false
	}

	return userModel, true
}

// GetCurrentUserID returns the current authenticated user ID from context
func GetCurrentUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("userID")
	if !exists {
		return 0, false
	}

	id, ok := userID.(uint)
	if !ok {
		return 0, false
	}

	return id, true
}

// IsAdmin checks if the current user is an admin
func IsAdmin(c *gin.Context) bool {
	user, exists := GetCurrentUser(c)
	if !exists {
		return false
	}

	return user.Role == models.RoleAdmin
}

// CanEditDocument checks if the current user can edit a specific document
func CanEditDocument(c *gin.Context, document *models.Document) bool {
	user, exists := GetCurrentUser(c)
	if !exists {
		return false
	}

	// Admin can edit any document
	if user.Role == models.RoleAdmin {
		return true
	}

	// Editor can edit documents they created or documents from their agency
	if user.Role == models.RoleEditor {
		if document.CreatedByID == user.ID {
			return true
		}
		if user.AgencyID != nil && document.AgencyID == *user.AgencyID {
			return true
		}
	}

	return false
}

// CanViewDocument checks if the current user can view a specific document based on visibility level
func CanViewDocument(c *gin.Context, document *models.Document) bool {
	user, exists := GetCurrentUser(c)
	if !exists {
		// Non-authenticated users can only view public documents
		return document.IsPublic && document.VisibilityLevel == 1
	}

	// Admin can view all documents
	if user.Role == models.RoleAdmin {
		return true
	}

	// Check visibility level against user role
	userViewerLevel := getUserViewerLevel(string(user.Role))

	// User can view documents at or below their viewer level
	return document.VisibilityLevel <= userViewerLevel
}

// getUserViewerLevel returns the viewer level for a user role
func getUserViewerLevel(userRole string) int {
	switch userRole {
	case "viewer_level_1":
		return 1 // Can only view public documents
	case "viewer_level_2":
		return 2 // Can view public and restricted documents
	case "viewer_level_3", "viewer":
		return 3 // Can view all documents including confidential
	case "editor", "reviewer", "publisher", "admin":
		return 3 // Higher roles can view all documents
	default:
		return 1 // Default to lowest level
	}
}
