'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { hrApi } from '../../../../services/enterpriseApi';
import { Department } from '../../../../types/enterprise';

const DepartmentViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const departmentId = parseInt(params.id as string);
  
  const [department, setDepartment] = useState<Department | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (departmentId) {
      fetchDepartment();
    }
  }, [departmentId]);

  const fetchDepartment = async () => {
    try {
      setLoading(true);
      const response = await hrApi.getDepartment(departmentId);
      setDepartment(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch department');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this department?')) return;
    
    try {
      await hrApi.deleteDepartment(departmentId);
      router.push('/enterprise/hr/departments');
    } catch (err: any) {
      setError(err.message || 'Failed to delete department');
    }
  };

  if (loading) return <div className="p-6">Loading department...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!department) return <div className="p-6">Department not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Department Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/hr/departments')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Departments
          </button>
          <button
            onClick={() => router.push(`/enterprise/hr/departments/${departmentId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Department
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Department
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{department.department_name}</h2>
              <p className="text-sm text-gray-600">Code: {department.department_code}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                department.is_active 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {department.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>

        {/* Department Overview */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Department Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {department.employee_count || 0}
              </div>
              <div className="text-sm text-blue-600">Employees</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {department.currency_code} {department.budget_amount.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">Budget</div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {department.currency_code} {department.actual_amount.toLocaleString()}
              </div>
              <div className="text-sm text-orange-600">Actual Spend</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                Level {department.level}
              </div>
              <div className="text-sm text-purple-600">Department Level</div>
            </div>
          </div>
        </div>

        {/* Department Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Department Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Department Manager</label>
              <p className="mt-1 text-sm text-gray-900">
                {department.manager ? 
                  `${department.manager.first_name} ${department.manager.last_name}` : 
                  'N/A'
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Location</label>
              <p className="mt-1 text-sm text-gray-900">{department.location || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Phone</label>
              <p className="mt-1 text-sm text-gray-900">{department.phone || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Email</label>
              <p className="mt-1 text-sm text-gray-900">{department.email || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Cost Center Code</label>
              <p className="mt-1 text-sm text-gray-900">{department.cost_center_code || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Established Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {department.established_date ? 
                  new Date(department.established_date).toLocaleDateString() : 
                  'N/A'
                }
              </p>
            </div>
          </div>

          {department.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {department.description}
              </div>
            </div>
          )}
        </div>

        {/* Budget Analysis */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Budget Analysis</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Budget Utilization</label>
              <div className="mt-2">
                <div className="bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${department.budget_amount > 0 ? 
                        Math.min((department.actual_amount / department.budget_amount) * 100, 100) : 
                        0}%` 
                    }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {department.budget_amount > 0 ? 
                    `${((department.actual_amount / department.budget_amount) * 100).toFixed(1)}%` : 
                    '0%'
                  } utilized
                </p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Remaining Budget</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {department.currency_code} {(department.budget_amount - department.actual_amount).toLocaleString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Budget per Employee</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {department.currency_code} {department.employee_count > 0 ? 
                  (department.budget_amount / department.employee_count).toLocaleString() : 
                  '0'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Hierarchy Information */}
        {department.parent_department_id && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Hierarchy</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Parent Department</label>
                <p className="mt-1 text-sm text-gray-900">
                  {department.parent_department?.department_name || 'N/A'}
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Department Level</label>
                <p className="mt-1 text-sm text-gray-900">Level {department.level}</p>
              </div>
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(department.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(department.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DepartmentViewPage;
