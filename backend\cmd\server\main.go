package main

import (
	"fmt"
	"log"
	"net/http"

	"federal-register-clone/internal/api"
	"federal-register-clone/internal/api/handlers"
	"federal-register-clone/internal/auth"
	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/services"

	"github.com/gin-gonic/gin"
)

// @title Federal Register Clone API
// @version 1.0
// @description A comprehensive document management service inspired by the Federal Register
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Set Gin mode
	gin.SetMode(cfg.Server.Mode)

	// Initialize database
	dbConfig := database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	}

	if err := database.Initialize(dbConfig); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Run database migrations
	if err := database.Migrate(); err != nil {
		log.Fatalf("Failed to run database migrations: %v", err)
	}

	// Initialize JWT
	auth.InitializeJWT(cfg.JWT.Secret)

	// Initialize email service
	handlers.InitializeEmailService(cfg)

	// Initialize document services
	handlers.InitializeDocumentServices(cfg)

	// Initialize analytics service
	handlers.InitializeAnalyticsService()

	// Initialize HR notification service
	handlers.InitializeHRNotificationService(cfg)

	// Initialize router
	router := api.SetupRouter(cfg)

	// Start task performance scheduler (runs every 30 minutes)
	services.StartGlobalTaskPerformanceScheduler(30)
	log.Println("Task performance scheduler started")

	// Start server
	address := fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port)
	log.Printf("Starting server on %s", address)

	server := &http.Server{
		Addr:    address,
		Handler: router,
	}

	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.Fatalf("Failed to start server: %v", err)
	}
}
