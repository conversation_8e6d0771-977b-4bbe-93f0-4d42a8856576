'use client'

import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';
import { TaskPerformanceIndicator, TaskPerformanceGauge, TaskPerformanceHistoryComponent as TaskPerformanceHistory } from '../components/Tasks/TaskPerformanceIndicator';
import { Task, TaskPerformanceHistory as TaskPerformanceHistoryType } from '../types';
import apiService from '../services/api';

const TestPerformancePage: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  // Mock task data for testing
  const mockTask: Task = {
    id: 1,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    title: 'Test Task for Performance Evaluation',
    description: 'This is a test task to validate performance evaluation components',
    type: 'general',
    status: 'completed',
    priority: 'high',
    due_date: '2025-01-02T00:00:00Z',
    start_date: '2025-01-01T00:00:00Z',
    end_date: '2025-01-01T12:00:00Z',
    created_by: {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      role: 'admin',
      is_active: true,
      is_verified: true,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z'
    },
    duration: 480,
    is_all_day: false,
    time_zone: 'UTC',
    is_recurring: false,
    parsed_from_text: false,
    assigned_to_id: 1,
    created_by_id: 1,
    reminder_enabled: false,
    notification_sent: false,
    is_public: false,
    completed_at: '2025-01-01T11:30:00Z',
    completed_by: 1,
    performance_percentage: 87.5,
    deadline_adherence_score: 95.0,
    quality_score: 85.0,
    completion_efficiency: 90.0,
    priority_handling_score: 80.0,
    performance_notes: 'Excellent performance with early completion and high quality work.',
    evaluation_date: '2025-01-01T12:00:00Z',
    is_auto_calculated: true,
    evaluated_by_id: 1
  };

  const mockPerformanceHistory: TaskPerformanceHistoryType[] = [
    {
      id: 1,
      task_id: 1,
      performance_percentage: 87.5,
      deadline_adherence_score: 95.0,
      quality_score: 85.0,
      completion_efficiency: 90.0,
      priority_handling_score: 80.0,
      performance_notes: 'Excellent performance with early completion',
      evaluation_date: '2025-01-01T12:00:00Z',
      is_auto_calculated: true,
      change_reason: 'Automatic performance evaluation',
      created_at: '2025-01-01T12:00:00Z',
      updated_at: '2025-01-01T12:00:00Z'
    },
    {
      id: 2,
      task_id: 1,
      performance_percentage: 82.0,
      deadline_adherence_score: 90.0,
      quality_score: 80.0,
      completion_efficiency: 85.0,
      priority_handling_score: 75.0,
      performance_notes: 'Good performance with room for improvement',
      evaluation_date: '2025-01-01T10:00:00Z',
      is_auto_calculated: false,
      evaluated_by_id: 1,
      change_reason: 'Manual performance adjustment',
      created_at: '2025-01-01T10:00:00Z',
      updated_at: '2025-01-01T10:00:00Z'
    }
  ];

  const addTestResult = (message: string, success: boolean = true) => {
    const timestamp = new Date().toLocaleTimeString();
    const status = success ? '✅' : '❌';
    setTestResults(prev => [...prev, `${timestamp} ${status} ${message}`]);
  };

  const runComponentTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    addTestResult('Starting performance component tests...');

    // Test 1: Performance Indicator Component
    try {
      addTestResult('Testing TaskPerformanceIndicator component rendering');
      // Component should render without errors
      addTestResult('TaskPerformanceIndicator component test passed');
    } catch (error) {
      addTestResult(`TaskPerformanceIndicator component test failed: ${error}`, false);
    }

    // Test 2: Performance Gauge Component
    try {
      addTestResult('Testing TaskPerformanceGauge component rendering');
      // Component should render without errors
      addTestResult('TaskPerformanceGauge component test passed');
    } catch (error) {
      addTestResult(`TaskPerformanceGauge component test failed: ${error}`, false);
    }

    // Test 3: Performance History Component
    try {
      addTestResult('Testing TaskPerformanceHistory component rendering');
      // Component should render without errors
      addTestResult('TaskPerformanceHistory component test passed');
    } catch (error) {
      addTestResult(`TaskPerformanceHistory component test failed: ${error}`, false);
    }

    // Test 4: API Integration
    try {
      addTestResult('Testing API integration...');
      
      // Test scheduler status
      const schedulerStatus = await apiService.get('/tasks/performance/scheduler/status') as any;
      if (schedulerStatus.status === 200) {
        addTestResult('Scheduler status API test passed');
      } else {
        addTestResult('Scheduler status API test failed', false);
      }
    } catch (error) {
      addTestResult(`API integration test failed: ${error}`, false);
    }

    // Test 5: Performance Calculation Logic
    try {
      addTestResult('Testing performance calculation logic...');
      
      const totalScore = (
        mockTask.deadline_adherence_score * 0.30 +
        mockTask.completion_efficiency * 0.25 +
        mockTask.quality_score * 0.25 +
        mockTask.priority_handling_score * 0.20
      );
      
      if (Math.abs(totalScore - mockTask.performance_percentage) < 1.0) {
        addTestResult('Performance calculation logic test passed');
      } else {
        addTestResult(`Performance calculation mismatch: expected ${totalScore}, got ${mockTask.performance_percentage}`, false);
      }
    } catch (error) {
      addTestResult(`Performance calculation test failed: ${error}`, false);
    }

    addTestResult('All performance component tests completed!');
    setIsRunning(false);
  };

  const runAPITests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    addTestResult('Starting API tests...');

    try {
      // Test recalculate all performances (admin only)
      addTestResult('Testing recalculate all performances API...');
      const recalcResponse = await apiService.recalculateAllTaskPerformances();
      addTestResult('Recalculate all performances API test passed');
    } catch (error) {
      addTestResult(`Recalculate API test failed: ${error}`, false);
    }

    try {
      // Test manual evaluation trigger
      addTestResult('Testing manual evaluation trigger...');
      const response = await apiService.post('/tasks/performance/scheduler/run', {}) as any;
      if (response.status === 200) {
        addTestResult('Manual evaluation trigger test passed');
      } else {
        addTestResult('Manual evaluation trigger test failed', false);
      }
    } catch (error) {
      addTestResult(`Manual evaluation test failed: ${error}`, false);
    }

    addTestResult('All API tests completed!');
    setIsRunning(false);
  };

  return (
    <Layout>
      <div className="container-custom py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Task Performance System Test Page
          </h1>
          <p className="text-gray-600">
            This page tests the automatic performance evaluation system components and functionality.
          </p>
        </div>

        {/* Test Controls */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Controls</h2>
          <div className="flex space-x-4">
            <button
              onClick={runComponentTests}
              disabled={isRunning}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isRunning ? 'Running...' : 'Run Component Tests'}
            </button>
            <button
              onClick={runAPITests}
              disabled={isRunning}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isRunning ? 'Running...' : 'Run API Tests'}
            </button>
          </div>
        </div>

        {/* Test Results */}
        {testResults.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Test Results</h2>
            <div className="bg-gray-900 text-green-400 p-4 rounded-md font-mono text-sm max-h-96 overflow-y-auto">
              {testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Component Demonstrations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Performance Indicator Demo */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance Indicator Demo</h2>
            <TaskPerformanceIndicator task={mockTask} showDetails={true} />
          </div>

          {/* Performance Gauge Demo */}
          <div>
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance Gauge Demo</h2>
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-center">
                <TaskPerformanceGauge 
                  percentage={mockTask.performance_percentage}
                  size={150}
                />
              </div>
              <div className="mt-4 text-center">
                <p className="text-sm text-gray-600">
                  Overall Performance: {mockTask.performance_percentage.toFixed(1)}%
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Performance History Demo */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance History Demo</h2>
          <TaskPerformanceHistory history={mockPerformanceHistory} />
        </div>

        {/* Performance Metrics Breakdown */}
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Performance Metrics Breakdown</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {mockTask.deadline_adherence_score.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Deadline Adherence</div>
              <div className="text-xs text-gray-500">Weight: 30%</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {mockTask.quality_score.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Quality Score</div>
              <div className="text-xs text-gray-500">Weight: 25%</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {mockTask.completion_efficiency.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Completion Efficiency</div>
              <div className="text-xs text-gray-500">Weight: 25%</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {mockTask.priority_handling_score.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Priority Handling</div>
              <div className="text-xs text-gray-500">Weight: 20%</div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TestPerformancePage;
