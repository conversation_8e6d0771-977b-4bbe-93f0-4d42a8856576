# Frontend Error Capture System

This comprehensive error capture system is designed to catch and log all types of frontend errors that occur in your Next.js application, including those that might not be visible in the server logs.

## 🚨 What It Captures

### Error Types
- **React Errors**: Component crashes, render errors, lifecycle errors
- **Axios/Network Errors**: API request failures, network timeouts, HTTP errors
- **Runtime Errors**: JavaScript exceptions, null pointer errors, undefined variables
- **Promise Rejections**: Unhandled promise rejections
- **Console Errors**: All console.error and console.warn calls
- **Performance Issues**: Long tasks, slow page loads, high memory usage

### Error Severity Levels
- **Critical**: Server errors (5xx), application crashes
- **High**: Client errors (4xx), React component errors
- **Medium**: Warnings, performance issues
- **Low**: Minor issues, informational errors

## 📁 Files Overview

### Core Components
- `src/utils/errorLogger.ts` - Main error logging utility
- `src/utils/errorCapture.ts` - Enhanced error capture with monitoring
- `src/components/ErrorBoundary/ErrorBoundary.tsx` - React error boundary
- `src/components/ErrorDashboard/ErrorDashboard.tsx` - Visual error dashboard
- `error-capture-script.js` - Standalone script for any page

### Integration
- `src/pages/_app.tsx` - Updated to include error boundaries and initialization
- `src/services/api.ts` - Enhanced with axios error logging

## 🚀 How to Use

### 1. Automatic Error Capture
The system is automatically initialized when the app starts. All errors are captured automatically.

### 2. Error Dashboard (Development Only)
- Look for the red "Errors" button in the bottom-right corner
- Click to open the comprehensive error dashboard
- Filter by type, severity, or search terms
- Export errors to JSON file
- Clear all errors

### 3. Console Commands
Press `Ctrl+Shift+E` to view error summary in console

### 4. Manual Error Logging
```typescript
import { logError } from '../utils/errorLogger';

logError({
  type: 'runtime',
  message: 'Custom error message',
  severity: 'high',
  context: { customData: 'value' }
});
```

### 5. Testing Errors
Visit `/error-test` page to test all error types and verify the capture system works.

## 🔧 Standalone Script Usage

For debugging any page (even non-Next.js), copy and paste the content of `error-capture-script.js` into the browser console.

### Available Commands
```javascript
// View all captured errors
errorCapture.getErrors()

// Show error summary
errorCapture.getSummary()

// Export errors to file
errorCapture.exportErrors()

// Clear all errors
errorCapture.clearErrors()

// Log a test error
errorCapture.logTestError('My test error')
```

### Keyboard Shortcuts
- `Ctrl+Shift+E` - Show error summary
- `Ctrl+Shift+X` - Export errors to file
- `Ctrl+Shift+C` - Clear all errors

## 📊 Error Data Structure

Each captured error includes:
```typescript
{
  id: string;              // Unique error ID
  timestamp: string;       // ISO timestamp
  type: 'react' | 'axios' | 'runtime' | 'promise';
  message: string;         // Error message
  stack?: string;          // Stack trace
  url: string;             // Page URL where error occurred
  userAgent: string;       // Browser info
  sessionId: string;       // Session identifier
  severity: 'low' | 'medium' | 'high' | 'critical';
  component?: string;      // React component name (if applicable)
  request?: {              // HTTP request details (if applicable)
    method: string;
    url: string;
    data: any;
    headers: any;
  };
  response?: {             // HTTP response details (if applicable)
    status: number;
    statusText: string;
    data: any;
  };
  context?: any;           // Additional context data
}
```

## 🔍 Monitoring Features

### Performance Monitoring
- Detects tasks longer than 50ms
- Monitors page load times
- Tracks memory usage

### Network Monitoring
- Captures all fetch() requests
- Monitors XMLHttpRequest calls
- Logs failed HTTP responses

### React Monitoring
- Error boundaries at multiple levels
- Component-specific error tracking
- Props and state capture on errors

## 💾 Data Persistence

- Errors are stored in memory (last 1000 errors)
- Recent errors saved to localStorage (last 100 errors)
- Critical errors can be sent to server endpoint
- Export functionality for offline analysis

## 🛠️ Configuration

### Environment Variables
```env
# Enable/disable error dashboard (default: development only)
NEXT_PUBLIC_SHOW_ERROR_DASHBOARD=true

# Error logging endpoint (optional)
NEXT_PUBLIC_ERROR_ENDPOINT=/api/v1/errors
```

### Customization
You can customize the error logger by modifying:
- Maximum stored errors (default: 1000)
- Severity thresholds
- Performance monitoring thresholds
- Console styling

## 🚨 Production Considerations

### Security
- Error dashboard is disabled in production by default
- Sensitive data should be filtered from error logs
- Consider implementing server-side error aggregation

### Performance
- Error capture has minimal performance impact
- Memory usage is controlled with error limits
- Performance monitoring can be disabled if needed

## 📈 Best Practices

1. **Regular Monitoring**: Check the error dashboard regularly during development
2. **Error Classification**: Use appropriate severity levels for different error types
3. **Context Data**: Include relevant context when manually logging errors
4. **Testing**: Use the error test page to verify capture functionality
5. **Export Analysis**: Export and analyze error patterns for debugging

## 🐛 Troubleshooting

### Common Issues
1. **Errors not appearing**: Check if error capture is initialized in _app.tsx
2. **Dashboard not visible**: Ensure you're in development mode
3. **Console errors**: Check browser console for initialization messages
4. **Missing stack traces**: Some errors may not have stack traces available

### Debug Mode
Enable verbose logging by setting:
```javascript
localStorage.setItem('debug_error_capture', 'true');
```

## 📞 Support

If you encounter issues with the error capture system:
1. Check the browser console for initialization messages
2. Verify all files are properly imported
3. Test with the `/error-test` page
4. Use the standalone script for comparison

The error capture system provides comprehensive visibility into all frontend issues, helping you identify and fix problems that might otherwise go unnoticed.
