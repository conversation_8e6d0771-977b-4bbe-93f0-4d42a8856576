'use client';

import React, { useState, useEffect } from 'react';
import {
  ArrowRightIcon,
  DocumentTextIcon,
  ScaleIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  TagIcon,
  EyeIcon,
  PlusIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import apiService from '../../services/api';

interface EntityRelationship {
  id: number;
  source_entity_type: string;
  source_entity_id: number;
  source_entity_title: string;
  target_entity_type: string;
  target_entity_id: number;
  target_entity_title: string;
  relationship_type: string;
  description: string;
  is_active: boolean;
  created_at: string;
}

interface RelationshipViewerProps {
  entityType: string;
  entityId: number;
  entityTitle: string;
}

const RelationshipViewer: React.FC<RelationshipViewerProps> = ({
  entityType,
  entityId,
  entityTitle
}) => {
  const [relationships, setRelationships] = useState<EntityRelationship[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newRelationship, setNewRelationship] = useState({
    target_entity_type: '',
    target_entity_id: '',
    relationship_type: '',
    description: ''
  });

  useEffect(() => {
    fetchRelationships();
  }, [entityType, entityId]);

  const fetchRelationships = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(`/api/relationships/${entityType}/${entityId}`) as any;
      setRelationships(response.data);
    } catch (error) {
      console.error('Failed to fetch relationships:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEntityIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <DocumentTextIcon className="w-5 h-5" />;
      case 'regulation':
        return <ScaleIcon className="w-5 h-5" />;
      case 'task':
        return <ClipboardDocumentListIcon className="w-5 h-5" />;
      case 'finance':
        return <CurrencyDollarIcon className="w-5 h-5" />;
      case 'agency':
        return <BuildingOfficeIcon className="w-5 h-5" />;
      case 'category':
        return <TagIcon className="w-5 h-5" />;
      default:
        return <DocumentTextIcon className="w-5 h-5" />;
    }
  };

  const getEntityColor = (type: string) => {
    switch (type) {
      case 'document':
        return 'text-blue-600 bg-blue-100';
      case 'regulation':
        return 'text-green-600 bg-green-100';
      case 'task':
        return 'text-yellow-600 bg-yellow-100';
      case 'finance':
        return 'text-purple-600 bg-purple-100';
      case 'agency':
        return 'text-indigo-600 bg-indigo-100';
      case 'category':
        return 'text-pink-600 bg-pink-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getRelationshipTypeColor = (type: string) => {
    switch (type) {
      case 'implements':
        return 'bg-green-100 text-green-800';
      case 'references':
        return 'bg-blue-100 text-blue-800';
      case 'depends_on':
        return 'bg-yellow-100 text-yellow-800';
      case 'generates':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleAddRelationship = async () => {
    try {
      await apiService.post('/api/relationships', {
        source_entity_type: entityType,
        source_entity_id: entityId,
        ...newRelationship
      });
      
      setShowAddForm(false);
      setNewRelationship({
        target_entity_type: '',
        target_entity_id: '',
        relationship_type: '',
        description: ''
      });
      
      fetchRelationships();
    } catch (error) {
      console.error('Failed to create relationship:', error);
    }
  };

  const handleDeleteRelationship = async (relationshipId: number) => {
    try {
      await apiService.delete(`/api/relationships/${relationshipId}`);
      fetchRelationships();
    } catch (error) {
      console.error('Failed to delete relationship:', error);
    }
  };

  const RelationshipCard: React.FC<{ relationship: EntityRelationship }> = ({ relationship }) => (
    <div className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {/* Source Entity */}
          <div className={`p-2 rounded-lg ${getEntityColor(relationship.source_entity_type)}`}>
            {getEntityIcon(relationship.source_entity_type)}
          </div>
          
          {/* Relationship Arrow */}
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRelationshipTypeColor(relationship.relationship_type)}`}>
              {relationship.relationship_type}
            </span>
            <ArrowRightIcon className="w-4 h-4 text-gray-400" />
          </div>
          
          {/* Target Entity */}
          <div className={`p-2 rounded-lg ${getEntityColor(relationship.target_entity_type)}`}>
            {getEntityIcon(relationship.target_entity_type)}
          </div>
        </div>
        
        <button
          onClick={() => handleDeleteRelationship(relationship.id)}
          className="text-red-500 hover:text-red-700"
        >
          <XMarkIcon className="w-4 h-4" />
        </button>
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between text-sm">
          <span className="font-medium text-gray-900">
            {relationship.source_entity_title}
          </span>
          <span className="font-medium text-gray-900">
            {relationship.target_entity_title}
          </span>
        </div>
        
        {relationship.description && (
          <p className="text-sm text-gray-600">{relationship.description}</p>
        )}
        
        <p className="text-xs text-gray-400">
          Created: {new Date(relationship.created_at).toLocaleDateString()}
        </p>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Relationships for {entityTitle}
        </h3>
        <button
          onClick={() => setShowAddForm(true)}
          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <PlusIcon className="w-4 h-4 mr-1" />
          Add Relationship
        </button>
      </div>

      {/* Add Relationship Form */}
      {showAddForm && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="font-medium text-gray-900 mb-3">Add New Relationship</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Target Entity Type
              </label>
              <select
                value={newRelationship.target_entity_type}
                onChange={(e) => setNewRelationship({
                  ...newRelationship,
                  target_entity_type: e.target.value
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">Select type...</option>
                <option value="document">Document</option>
                <option value="regulation">Regulation</option>
                <option value="task">Task</option>
                <option value="finance">Finance</option>
                <option value="agency">Agency</option>
                <option value="category">Category</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Target Entity ID
              </label>
              <input
                type="number"
                value={newRelationship.target_entity_id}
                onChange={(e) => setNewRelationship({
                  ...newRelationship,
                  target_entity_id: e.target.value
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="Enter ID..."
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Relationship Type
              </label>
              <select
                value={newRelationship.relationship_type}
                onChange={(e) => setNewRelationship({
                  ...newRelationship,
                  relationship_type: e.target.value
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="">Select type...</option>
                <option value="implements">Implements</option>
                <option value="references">References</option>
                <option value="depends_on">Depends On</option>
                <option value="generates">Generates</option>
                <option value="related_to">Related To</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <input
                type="text"
                value={newRelationship.description}
                onChange={(e) => setNewRelationship({
                  ...newRelationship,
                  description: e.target.value
                })}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
                placeholder="Optional description..."
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 mt-4">
            <button
              onClick={() => setShowAddForm(false)}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleAddRelationship}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
            >
              Add Relationship
            </button>
          </div>
        </div>
      )}

      {/* Relationships List */}
      <div className="space-y-3">
        {relationships.length > 0 ? (
          relationships.map((relationship) => (
            <RelationshipCard key={relationship.id} relationship={relationship} />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            <EyeIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
            <p>No relationships found</p>
            <p className="text-sm">Add relationships to see connections between entities</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RelationshipViewer;
