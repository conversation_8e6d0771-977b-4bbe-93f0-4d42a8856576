package main

import (
	"fmt"
	"log"
	"os"

	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database connection
	dbConfig := database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	}

	if err := database.Initialize(dbConfig); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	db := database.GetDB()

	// Execute the migration statements individually
	statements := []string{
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS performance_percentage NUMERIC(5,2) DEFAULT 0.00;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS deadline_adherence_score NUMERIC(5,2) DEFAULT 0.00;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS quality_score NUMERIC(5,2) DEFAULT 0.00;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS completion_efficiency NUMERIC(5,2) DEFAULT 0.00;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS priority_handling_score NUMERIC(5,2) DEFAULT 0.00;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS performance_notes TEXT;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS evaluation_date TIMESTAMP WITH TIME ZONE;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS is_auto_calculated BOOLEAN DEFAULT true;",
		"ALTER TABLE tasks ADD COLUMN IF NOT EXISTS evaluated_by_id BIGINT REFERENCES users(id) ON DELETE SET NULL;",
		`CREATE TABLE IF NOT EXISTS task_performance_history (
			id BIGSERIAL PRIMARY KEY,
			created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
			deleted_at TIMESTAMP WITH TIME ZONE,
			task_id BIGINT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
			performance_percentage NUMERIC(5,2) NOT NULL DEFAULT 0.00,
			deadline_adherence_score NUMERIC(5,2) NOT NULL DEFAULT 0.00,
			quality_score NUMERIC(5,2) NOT NULL DEFAULT 0.00,
			completion_efficiency NUMERIC(5,2) NOT NULL DEFAULT 0.00,
			priority_handling_score NUMERIC(5,2) NOT NULL DEFAULT 0.00,
			performance_notes TEXT,
			evaluation_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
			is_auto_calculated BOOLEAN DEFAULT true,
			evaluated_by_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
			change_reason TEXT,
			CONSTRAINT check_performance_percentage CHECK (performance_percentage >= 0 AND performance_percentage <= 100),
			CONSTRAINT check_deadline_adherence_score CHECK (deadline_adherence_score >= 0 AND deadline_adherence_score <= 100),
			CONSTRAINT check_quality_score CHECK (quality_score >= 0 AND quality_score <= 100),
			CONSTRAINT check_completion_efficiency CHECK (completion_efficiency >= 0 AND completion_efficiency <= 100),
			CONSTRAINT check_priority_handling_score CHECK (priority_handling_score >= 0 AND priority_handling_score <= 100)
		);`,
		"CREATE INDEX IF NOT EXISTS idx_tasks_performance_percentage ON tasks(performance_percentage);",
		"CREATE INDEX IF NOT EXISTS idx_tasks_evaluation_date ON tasks(evaluation_date);",
		"CREATE INDEX IF NOT EXISTS idx_tasks_is_auto_calculated ON tasks(is_auto_calculated);",
		"CREATE INDEX IF NOT EXISTS idx_task_performance_history_task_id ON task_performance_history(task_id);",
		"CREATE INDEX IF NOT EXISTS idx_task_performance_history_evaluation_date ON task_performance_history(evaluation_date);",
		"CREATE INDEX IF NOT EXISTS idx_task_performance_history_deleted_at ON task_performance_history(deleted_at);",
	}

	for i, stmt := range statements {
		fmt.Printf("Executing statement %d/%d...\n", i+1, len(statements))
		if err := db.Exec(stmt).Error; err != nil {
			fmt.Printf("Warning: Statement %d failed: %v\n", i+1, err)
			// Continue with other statements
		}
	}

	fmt.Println("Task performance evaluation migration completed successfully!")
	os.Exit(0)
}
