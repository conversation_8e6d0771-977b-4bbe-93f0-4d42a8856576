package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
)

func main() {
	// Connect to database
	db, err := sql.Open("postgres", "host=localhost user=postgres password=postgres dbname=federal_register_db sslmode=disable")
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Create laws_and_rules table
	lawsAndRulesSQL := `
	CREATE TABLE IF NOT EXISTS laws_and_rules (
		id BIGSERIAL PRIMARY KEY,
		created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
		updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
		deleted_at TIMESTAMPTZ,
		title TEXT NOT NULL,
		slug TEXT NOT NULL UNIQUE,
		description TEXT,
		cfr_title TEXT,
		cfr_part TEXT,
		cfr_section TEXT,
		effective_date TIMESTAMPTZ,
		status TEXT DEFAULT 'draft',
		agency_id BIGINT REFERENCES agencies(id),
		created_by_id BIGINT REFERENCES users(id),
		is_significant BOOLEAN DEFAULT false,
		regulatory_identifier TEXT,
		docket_number TEXT,
		parent_regulation_id BIGINT REFERENCES laws_and_rules(id),
		version_number TEXT DEFAULT '1.0.0',
		current_version_id BIGINT
	);`

	_, err = db.Exec(lawsAndRulesSQL)
	if err != nil {
		log.Fatal("Failed to create laws_and_rules table:", err)
	}
	fmt.Println("Created laws_and_rules table")

	// Create regulation_document_versions table
	versionsSQL := `
	CREATE TABLE IF NOT EXISTS regulation_document_versions (
		id BIGSERIAL PRIMARY KEY,
		created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
		updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
		deleted_at TIMESTAMPTZ,
		law_rule_id BIGINT REFERENCES laws_and_rules(id) ON DELETE CASCADE,
		version_number TEXT NOT NULL,
		title TEXT NOT NULL,
		content TEXT,
		change_summary TEXT,
		effective_date TIMESTAMPTZ,
		status TEXT DEFAULT 'draft',
		created_by_id BIGINT REFERENCES users(id),
		approved_at TIMESTAMPTZ,
		approved_by_id BIGINT REFERENCES users(id),
		published_at TIMESTAMPTZ,
		published_by_id BIGINT REFERENCES users(id),
		is_current BOOLEAN DEFAULT false,
		UNIQUE(law_rule_id, version_number)
	);`

	_, err = db.Exec(versionsSQL)
	if err != nil {
		log.Fatal("Failed to create regulation_document_versions table:", err)
	}
	fmt.Println("Created regulation_document_versions table")

	// Insert sample data
	sampleDataSQL := `
	INSERT INTO laws_and_rules (title, slug, description, cfr_title, cfr_part, effective_date, status, agency_id, created_by_id, is_significant) VALUES
	('Environmental Protection Standards', 'environmental-protection-standards', 'Standards for air quality and emissions control', '40', '60', '2024-01-15', 'effective', 1, 1, true),
	('Healthcare Data Privacy Rules', 'healthcare-data-privacy-rules', 'Privacy and security rules for healthcare data', '45', '164', '2024-02-01', 'effective', 1, 1, false)
	ON CONFLICT (slug) DO NOTHING;

	INSERT INTO regulation_document_versions (law_rule_id, version_number, title, content, effective_date, status, created_by_id, is_current) VALUES
	(1, '1.0.0', 'Environmental Protection Standards v1.0', 'Initial version of environmental protection standards...', '2024-01-15', 'published', 1, true),
	(2, '1.0.0', 'Healthcare Data Privacy Rules v1.0', 'Initial version of healthcare data privacy rules...', '2024-02-01', 'published', 1, true)
	ON CONFLICT (law_rule_id, version_number) DO NOTHING;`

	_, err = db.Exec(sampleDataSQL)
	if err != nil {
		log.Fatal("Failed to insert sample data:", err)
	}
	fmt.Println("Inserted sample regulation data")

	fmt.Println("Regulation tables created successfully!")
}
