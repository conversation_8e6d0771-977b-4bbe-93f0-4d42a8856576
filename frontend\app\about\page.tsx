'use client'

import React from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  ClockIcon,
  GlobeAltIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';

const AboutPage: React.FC = () => {
  const features = [
    {
      icon: DocumentTextIcon,
      title: 'Document Management',
      description: 'Comprehensive document lifecycle management with version control, approval workflows, and automated publishing.'
    },
    {
      icon: BuildingOfficeIcon,
      title: 'Agency Management',
      description: 'Organize and manage federal agencies with detailed profiles, contact information, and document associations.'
    },
    {
      icon: UserGroupIcon,
      title: 'Role-Based Access',
      description: 'Secure access control with customizable roles and permissions for different user types and responsibilities.'
    },
    {
      icon: ShieldCheckIcon,
      title: 'Compliance Ready',
      description: 'Built-in compliance features for federal regulations, audit trails, and regulatory requirements.'
    },
    {
      icon: ClockIcon,
      title: 'Calendar Integration',
      description: 'Track important dates, deadlines, public hearings, and comment periods with integrated calendar views.'
    },
    {
      icon: GlobeAltIcon,
      title: 'Public Access',
      description: 'Public-facing interface for citizens to access published documents, submit comments, and track proceedings.'
    }
  ];

  const stats = [
    { label: 'Documents Managed', value: '10,000+' },
    { label: 'Federal Agencies', value: '500+' },
    { label: 'Active Users', value: '1,000+' },
    { label: 'Uptime', value: '99.9%' }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div className="container-custom py-16 lg:py-24">
          <div className="text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              About Federal Register Clone
            </h1>
            <p className="text-xl lg:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
              A comprehensive document management system designed for federal agencies, 
              regulatory bodies, and public access to government information.
            </p>
          </div>
        </div>
      </div>

      {/* Mission Section */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
            <p className="text-lg text-gray-600 mb-8">
              To provide a modern, efficient, and transparent platform for managing federal 
              documents and regulations, ensuring public access to government information 
              while maintaining the highest standards of security and compliance.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-primary-600 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gray-50 py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Key Features</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our platform provides comprehensive tools for document management, 
              regulatory compliance, and public engagement.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0">
                    <feature.icon className="h-8 w-8 text-primary-600" />
                  </div>
                  <h3 className="ml-3 text-lg font-semibold text-gray-900">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* How It Works Section */}
      <div className="py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">How It Works</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our streamlined process ensures efficient document management from creation to publication.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-primary-600">1</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Create & Draft</h3>
              <p className="text-gray-600">
                Authors create and draft documents using our intuitive editor with 
                markdown support and collaborative features.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-primary-600">2</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Review & Approve</h3>
              <p className="text-gray-600">
                Documents go through structured review and approval workflows 
                with role-based permissions and audit trails.
              </p>
            </div>
            
            <div className="text-center">
              <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-primary-600">3</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">Publish & Share</h3>
              <p className="text-gray-600">
                Approved documents are published to the public portal with 
                automatic notifications and comment period management.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Technology Section */}
      <div className="bg-gray-50 py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Built with Modern Technology</h2>
              <p className="text-lg text-gray-600">
                Our platform leverages cutting-edge technologies to ensure performance, 
                security, and scalability.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Frontend Technologies</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">Next.js 14 with App Router</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">TypeScript for type safety</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">Tailwind CSS for styling</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">Zustand for state management</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">React Hook Form for forms</span>
                  </li>
                </ul>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Backend Technologies</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">Go with Gin framework</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">PostgreSQL database</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">JWT authentication</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">RESTful API design</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
                    <span className="text-gray-700">Swagger documentation</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Security & Compliance Section */}
      <div className="py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Security & Compliance</h2>
            <p className="text-lg text-gray-600 mb-8">
              We prioritize security and compliance with federal standards and regulations.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow-md p-6">
                <ShieldCheckIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Data Encryption</h3>
                <p className="text-sm text-gray-600">End-to-end encryption for all data transmission and storage</p>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <UserGroupIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Access Control</h3>
                <p className="text-sm text-gray-600">Role-based access control with multi-factor authentication</p>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <DocumentTextIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Audit Trails</h3>
                <p className="text-sm text-gray-600">Comprehensive logging and audit trails for all activities</p>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <CheckCircleIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Compliance</h3>
                <p className="text-sm text-gray-600">Built to meet federal security and privacy requirements</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action Section */}
      <div className="bg-primary-600 py-16">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join federal agencies and organizations already using our platform 
            to streamline their document management processes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors duration-200"
            >
              Get Started
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </Link>
            <Link
              href="/features"
              className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-md text-white bg-transparent hover:bg-white hover:text-primary-600 transition-colors duration-200"
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AboutPage;
