'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { complianceApi } from '../../../../services/enterpriseApi';
import { ComplianceAssessment } from '../../../../types/enterprise';

const NewComplianceAssessmentPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<ComplianceAssessment>>({
    assessment_code: '',
    title: '',
    description: '',
    framework: '',
    assessment_type: 'internal',
    scope: '',
    methodology: '',
    criteria: '',
    status: 'planning',
    priority: 'medium',
    risk_level: 'medium',
    planned_start_date: new Date().toISOString().split('T')[0],
    planned_end_date: '',
    actual_start_date: '',
    actual_end_date: '',
    assessor_id: undefined,
    reviewer_id: undefined,
    overall_score: undefined,
    compliance_level: '',
    findings_count: 0,
    critical_findings: 0,
    high_findings: 0,
    medium_findings: 0,
    low_findings: 0,
    recommendations: '',
    action_plan: '',
    follow_up_required: false,
    next_assessment_date: '',
    cost: 0,
    currency_code: 'USD',
    metadata: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await complianceApi.createAssessment(formData);
      router.push('/enterprise/compliance/assessments');
    } catch (err: any) {
      setError(err.message || 'Failed to create compliance assessment');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create New Compliance Assessment</h1>
        <button
          onClick={() => router.push('/enterprise/compliance/assessments')}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Back to Assessments
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Assessment Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Assessment Code *
            </label>
            <input
              type="text"
              name="assessment_code"
              value={formData.assessment_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., ASS-2025-001"
            />
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., GDPR Compliance Assessment"
            />
          </div>

          {/* Framework */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Framework *
            </label>
            <select
              name="framework"
              value={formData.framework}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Framework</option>
              <option value="GDPR">GDPR</option>
              <option value="SOX">SOX</option>
              <option value="HIPAA">HIPAA</option>
              <option value="ISO27001">ISO 27001</option>
              <option value="PCI-DSS">PCI-DSS</option>
              <option value="NIST">NIST</option>
              <option value="Custom">Custom</option>
            </select>
          </div>

          {/* Assessment Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Assessment Type *
            </label>
            <select
              name="assessment_type"
              value={formData.assessment_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="internal">Internal</option>
              <option value="external">External</option>
              <option value="self">Self Assessment</option>
              <option value="third_party">Third Party</option>
            </select>
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority
            </label>
            <select
              name="priority"
              value={formData.priority}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Risk Level */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Risk Level
            </label>
            <select
              name="risk_level"
              value={formData.risk_level}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="planning">Planning</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          {/* Planned Start Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Planned Start Date *
            </label>
            <input
              type="date"
              name="planned_start_date"
              value={formData.planned_start_date}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Planned End Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Planned End Date
            </label>
            <input
              type="date"
              name="planned_end_date"
              value={formData.planned_end_date}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Next Assessment Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Next Assessment Date
            </label>
            <input
              type="date"
              name="next_assessment_date"
              value={formData.next_assessment_date}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Cost */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost
            </label>
            <input
              type="number"
              name="cost"
              value={formData.cost}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency Code
            </label>
            <input
              type="text"
              name="currency_code"
              value={formData.currency_code}
              onChange={handleChange}
              maxLength={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="USD"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Assessment description..."
          />
        </div>

        {/* Scope */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Scope
          </label>
          <textarea
            name="scope"
            value={formData.scope}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Assessment scope and boundaries..."
          />
        </div>

        {/* Methodology */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Methodology
          </label>
          <textarea
            name="methodology"
            value={formData.methodology}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Assessment methodology and approach..."
          />
        </div>

        {/* Criteria */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Criteria
          </label>
          <textarea
            name="criteria"
            value={formData.criteria}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Assessment criteria and standards..."
          />
        </div>

        {/* Follow-up Required */}
        <div className="mt-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="follow_up_required"
              checked={formData.follow_up_required}
              onChange={handleChange}
              className="mr-2"
            />
            Follow-up Required
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push('/enterprise/compliance/assessments')}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Assessment'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NewComplianceAssessmentPage;
