'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import {
  CheckCircleIcon,
  XMarkIcon,
  EnvelopeIcon,
  ClockIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';

const VerifyEmailPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user, isAuthenticated } = useAuthStore();
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error' | 'expired'>('pending');
  const [error, setError] = useState('');
  const [resendLoading, setResendLoading] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  useEffect(() => {
    const token = searchParams?.get('token');
    const email = searchParams?.get('email');
    
    if (token && email) {
      verifyEmail(token, email);
    } else if (isAuthenticated && user && !user.is_verified) {
      // User is logged in but not verified, show resend option
      setVerificationStatus('error');
      setError('Your email address has not been verified yet.');
    } else if (isAuthenticated && user && user.is_verified) {
      // User is already verified, redirect to dashboard
      router.push('/dashboard');
    } else {
      setVerificationStatus('error');
      setError('Invalid verification link. Please check your email for the correct link.');
    }
  }, [searchParams, isAuthenticated, user, router]);

  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const verifyEmail = async (token: string, email: string) => {
    try {
      await apiService.verifyEmail(token, email);
      setVerificationStatus('success');
      
      // If user is logged in, update their verification status
      if (isAuthenticated) {
        // Refresh user data to reflect verification
        window.location.reload();
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.message || 'Email verification failed';
      setError(errorMessage);
      
      if (errorMessage.includes('expired') || errorMessage.includes('invalid')) {
        setVerificationStatus('expired');
      } else {
        setVerificationStatus('error');
      }
    }
  };

  const handleResendVerification = async () => {
    if (!user?.email && !searchParams?.get('email')) {
      setError('No email address found. Please log in and try again.');
      return;
    }

    try {
      setResendLoading(true);
      setError('');
      
      const email = user?.email || searchParams?.get('email');
      await apiService.resendVerificationEmail(email!);
      
      setResendSuccess(true);
      setResendCooldown(60); // 60 second cooldown
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to resend verification email');
    } finally {
      setResendLoading(false);
    }
  };

  const renderContent = () => {
    switch (verificationStatus) {
      case 'pending':
        return (
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Verifying Your Email</h2>
            <p className="text-gray-600">Please wait while we verify your email address...</p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <CheckCircleIcon className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Email Verified Successfully!</h2>
            <p className="text-gray-600 mb-6">
              Your email address has been verified. You can now access all features of your account.
            </p>
            <div className="space-y-3">
              <Link
                href="/dashboard"
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Go to Dashboard
              </Link>
              {!isAuthenticated && (
                <Link
                  href="/login"
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        );

      case 'expired':
        return (
          <div className="text-center">
            <ClockIcon className="h-16 w-16 text-orange-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Verification Link Expired</h2>
            <p className="text-gray-600 mb-6">
              The verification link has expired. Please request a new verification email.
            </p>
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <XMarkIcon className="h-5 w-5 text-red-400" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-red-800">{error}</p>
                  </div>
                </div>
              </div>
            )}
            <div className="space-y-3">
              <button
                onClick={handleResendVerification}
                disabled={resendLoading || resendCooldown > 0}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {resendLoading ? (
                  <>
                    <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : resendCooldown > 0 ? (
                  `Resend in ${resendCooldown}s`
                ) : (
                  'Send New Verification Email'
                )}
              </button>
              <Link
                href="/login"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Back to Login
              </Link>
            </div>
          </div>
        );

      case 'error':
      default:
        return (
          <div className="text-center">
            <XMarkIcon className="h-16 w-16 text-red-600 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Verification Failed</h2>
            <p className="text-gray-600 mb-6">
              {error || 'There was an error verifying your email address.'}
            </p>
            
            {resendSuccess && (
              <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                <div className="flex">
                  <CheckCircleIcon className="h-5 w-5 text-green-400" />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-green-800">
                      Verification email sent! Please check your inbox and spam folder.
                    </p>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-3">
              {(user?.email || searchParams?.get('email')) && (
                <button
                  onClick={handleResendVerification}
                  disabled={resendLoading || resendCooldown > 0}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {resendLoading ? (
                    <>
                      <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : resendCooldown > 0 ? (
                    `Resend in ${resendCooldown}s`
                  ) : (
                    'Resend Verification Email'
                  )}
                </button>
              )}
              <Link
                href="/register"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Create New Account
              </Link>
              <Link
                href="/login"
                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Back to Login
              </Link>
            </div>
          </div>
        );
    }
  };

  return (
    <Layout>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
              <EnvelopeIcon className="h-6 w-6 text-primary-600" />
            </div>
          </div>
          
          {renderContent()}

          {/* Help Text */}
          <div className="bg-gray-50 rounded-md p-4 mt-8">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Need Help?</h4>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Check your spam/junk folder for the verification email</li>
              <li>• Make sure you're using the latest verification link</li>
              <li>• Verification links expire after 24 hours</li>
              <li>• Contact support if you continue having issues</li>
            </ul>
          </div>

          <div className="text-center">
            <Link
              href="/contact"
              className="text-sm text-primary-600 hover:text-primary-500"
            >
              Contact Support
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default VerifyEmailPage;
