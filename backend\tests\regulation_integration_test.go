package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"federal-register-clone/internal/api"
	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type RegulationIntegrationTestSuite struct {
	suite.Suite
	router     *gin.Engine
	testUser   *models.User
	testAgency *models.Agency
	authToken  string
}

func (suite *RegulationIntegrationTestSuite) SetupSuite() {
	// Initialize database connection (assumes PostgreSQL is running)
	dbConfig := database.Config{
		Host:     "localhost",
		Port:     "5432",
		User:     "postgres",
		Password: "password",
		DBName:   "federal_register_test",
		SSLMode:  "disable",
	}
	err := database.Initialize(dbConfig)
	if err != nil {
		suite.T().Skip("Database not available for integration tests")
		return
	}

	// Setup router
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:             "test-secret",
			ExpiryHours:        1,
			RefreshExpiryHours: 24,
		},
	}
	suite.router = api.SetupRouter(cfg)

	// Create test data
	suite.createTestData()
}

func (suite *RegulationIntegrationTestSuite) createTestData() {
	// Create test agency
	agency := &models.Agency{
		Name:        "Integration Test Agency",
		ShortName:   "ITA",
		Slug:        "integration-test-agency",
		Description: "Test agency for integration testing",
		IsActive:    true,
	}
	err := database.DB.Create(agency).Error
	if err != nil {
		// Agency might already exist, try to find it
		err = database.DB.Where("slug = ?", "integration-test-agency").First(agency).Error
		suite.Require().NoError(err)
	}
	suite.testAgency = agency

	// Create test user
	user := &models.User{
		Username:   "integrationuser",
		Email:      "<EMAIL>",
		FirstName:  "Integration",
		LastName:   "User",
		Role:       "editor",
		IsActive:   true,
		IsVerified: true,
	}
	err = database.DB.Create(user).Error
	if err != nil {
		// User might already exist, try to find it
		err = database.DB.Where("email = ?", "<EMAIL>").First(user).Error
		suite.Require().NoError(err)
	}
	suite.testUser = user

	// Create auth token for testing protected endpoints
	suite.authToken = "Bearer test-token" // In real tests, generate proper JWT
}

func (suite *RegulationIntegrationTestSuite) TearDownTest() {
	// Clean up test data after each test
	database.DB.Where("title LIKE ?", "Integration Test%").Delete(&models.LawsAndRules{})
}

func (suite *RegulationIntegrationTestSuite) TestCreateRegulationEndpoint() {
	// Test data
	effectiveDate := time.Now().AddDate(0, 1, 0) // 1 month from now
	regulationData := map[string]interface{}{
		"title":          "Integration Test Environmental Protection Standards",
		"short_title":    "Integration EPA Standards",
		"type":           "rule",
		"cfr_title":      "40",
		"agency_id":      suite.testAgency.ID,
		"description":    "Integration test comprehensive environmental protection standards",
		"effective_date": effectiveDate.Format("2006-01-02"),
		"is_significant": true,
		"initial_chunks": []map[string]interface{}{
			{
				"chunk_type":       "chapter",
				"number":           "I",
				"title":            "Environmental Protection Agency",
				"chunk_identifier": "INTEGRATION-CHAPTER-I",
				"order_in_parent":  1,
			},
		},
	}

	jsonData, _ := json.Marshal(regulationData)
	req, _ := http.NewRequest("POST", "/api/v1/regulations", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", suite.authToken)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "data")

	// Verify regulation was created in database
	var regulation models.LawsAndRules
	regulationID := uint(response["data"].(map[string]interface{})["id"].(float64))
	err = database.DB.First(&regulation, regulationID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Integration Test Environmental Protection Standards", regulation.Title)
	assert.Equal(suite.T(), models.RegulationType("rule"), regulation.Type)
}

func (suite *RegulationIntegrationTestSuite) TestGetPublicRegulationsEndpoint() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:         "Integration Public Test Regulation",
		ShortTitle:    "IPTR",
		Type:          "rule",
		Status:        "effective",
		CFRTitle:      "40",
		AgencyID:      suite.testAgency.ID,
		CreatedByID:   suite.testUser.ID,
		Description:   "An integration test regulation for public access",
		EffectiveDate: &time.Time{},
		IsSignificant: false,
	}
	err := database.DB.Create(regulation).Error
	suite.Require().NoError(err)

	// Test GET /api/v1/public/regulations
	req, _ := http.NewRequest("GET", "/api/v1/public/regulations", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "data")

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(data), 1)

	// Find our test regulation in the results
	found := false
	for _, item := range data {
		reg := item.(map[string]interface{})
		if reg["title"].(string) == "Integration Public Test Regulation" {
			found = true
			assert.Equal(suite.T(), "rule", reg["type"])
			assert.Equal(suite.T(), "effective", reg["status"])
			break
		}
	}
	assert.True(suite.T(), found, "Test regulation should be found in public regulations list")
}

func (suite *RegulationIntegrationTestSuite) TestGetPublicRegulationEndpoint() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:         "Integration Detailed Test Regulation",
		ShortTitle:    "IDTR",
		Type:          "regulation",
		Status:        "effective",
		CFRTitle:      "47",
		AgencyID:      suite.testAgency.ID,
		CreatedByID:   suite.testUser.ID,
		Description:   "An integration detailed test regulation with hierarchical structure",
		EffectiveDate: &time.Time{},
		IsSignificant: true,
	}
	err := database.DB.Create(regulation).Error
	suite.Require().NoError(err)

	// Test GET /api/v1/public/regulations/:id
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/public/regulations/%d", regulation.ID), nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	// Verify response
	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), response, "regulation")
	assert.Contains(suite.T(), response, "chunks")

	regulationData := response["regulation"].(map[string]interface{})
	assert.Equal(suite.T(), "Integration Detailed Test Regulation", regulationData["title"])
}

func (suite *RegulationIntegrationTestSuite) TestRegulationFilteringEndpoint() {
	// Create regulations with different types
	regulations := []*models.LawsAndRules{
		{
			Title:       "Integration Rule Type Regulation",
			Type:        "rule",
			Status:      "effective",
			AgencyID:    suite.testAgency.ID,
			CreatedByID: suite.testUser.ID,
		},
		{
			Title:       "Integration Law Type Regulation",
			Type:        "law",
			Status:      "effective",
			AgencyID:    suite.testAgency.ID,
			CreatedByID: suite.testUser.ID,
		},
	}

	for _, reg := range regulations {
		err := database.DB.Create(reg).Error
		suite.Require().NoError(err)
	}

	// Test filtering by type
	req, _ := http.NewRequest("GET", "/api/v1/public/regulations?type=rule", nil)
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	data := response["data"].([]interface{})
	// Should have at least the rule type regulation we created
	assert.GreaterOrEqual(suite.T(), len(data), 1)

	// Verify all returned regulations are of type "rule"
	for _, item := range data {
		reg := item.(map[string]interface{})
		assert.Equal(suite.T(), "rule", reg["type"])
	}
}

func TestRegulationIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RegulationIntegrationTestSuite))
}
