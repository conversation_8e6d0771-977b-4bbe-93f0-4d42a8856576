package services

import (
	"fmt"
	"html"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ValidationService provides comprehensive data validation and sanitization
type ValidationService struct {
	config ValidationConfig
}

// ValidationConfig configures validation behavior
type ValidationConfig struct {
	EnableXSSProtection          bool     `json:"enable_xss_protection"`
	EnableSQLInjectionPrevention bool     `json:"enable_sql_injection_prevention"`
	EnableCSRFProtection         bool     `json:"enable_csrf_protection"`
	MaxStringLength              int      `json:"max_string_length"`
	MaxFileSize                  int64    `json:"max_file_size"`
	AllowedFileTypes             []string `json:"allowed_file_types"`
	StrictValidation             bool     `json:"strict_validation"`
}

// ValidationResult represents the result of validation
type ValidationResult struct {
	IsValid       bool                   `json:"is_valid"`
	Errors        []ValidationError      `json:"errors"`
	Warnings      []ValidationWarning    `json:"warnings"`
	SanitizedData map[string]interface{} `json:"sanitized_data"`
}

// ValidationError represents a validation error
type ValidationError struct {
	Field      string      `json:"field"`
	Code       string      `json:"code"`
	Message    string      `json:"message"`
	Value      interface{} `json:"value,omitempty"`
	Constraint string      `json:"constraint,omitempty"`
}

// ValidationWarning represents a validation warning
type ValidationWarning struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Action  string `json:"action"`
}

// ValidationRule represents a validation rule
type ValidationRule struct {
	Field     string                  `json:"field"`
	Type      string                  `json:"type"`
	Required  bool                    `json:"required"`
	MinLength *int                    `json:"min_length,omitempty"`
	MaxLength *int                    `json:"max_length,omitempty"`
	Pattern   *string                 `json:"pattern,omitempty"`
	Min       *float64                `json:"min,omitempty"`
	Max       *float64                `json:"max,omitempty"`
	Enum      []string                `json:"enum,omitempty"`
	Custom    func(interface{}) error `json:"-"`
}

// NewValidationService creates a new validation service
func NewValidationService() *ValidationService {
	return &ValidationService{
		config: ValidationConfig{
			EnableXSSProtection:          true,
			EnableSQLInjectionPrevention: true,
			EnableCSRFProtection:         true,
			MaxStringLength:              10000,
			MaxFileSize:                  10 * 1024 * 1024, // 10MB
			AllowedFileTypes:             []string{"pdf", "doc", "docx", "txt", "jpg", "png", "gif"},
			StrictValidation:             true,
		},
	}
}

// ValidateAndSanitize validates and sanitizes input data
func (v *ValidationService) ValidateAndSanitize(data map[string]interface{}, rules []ValidationRule) *ValidationResult {
	result := &ValidationResult{
		IsValid:       true,
		Errors:        []ValidationError{},
		Warnings:      []ValidationWarning{},
		SanitizedData: make(map[string]interface{}),
	}

	// Create rule map for quick lookup
	ruleMap := make(map[string]ValidationRule)
	for _, rule := range rules {
		ruleMap[rule.Field] = rule
	}

	// Validate and sanitize each field
	for field, value := range data {
		rule, hasRule := ruleMap[field]

		// Sanitize the value
		sanitizedValue := v.sanitizeValue(value, field)
		result.SanitizedData[field] = sanitizedValue

		// Validate if rule exists
		if hasRule {
			errors := v.validateField(field, sanitizedValue, rule)
			result.Errors = append(result.Errors, errors...)
		} else if v.config.StrictValidation {
			// Warn about unknown fields in strict mode
			result.Warnings = append(result.Warnings, ValidationWarning{
				Field:   field,
				Message: "Unknown field",
				Action:  "Field not defined in validation rules",
			})
		}
	}

	// Check for missing required fields
	for _, rule := range rules {
		if rule.Required {
			if _, exists := data[rule.Field]; !exists {
				result.Errors = append(result.Errors, ValidationError{
					Field:   rule.Field,
					Code:    "REQUIRED",
					Message: "Field is required",
				})
			}
		}
	}

	// Set overall validity
	result.IsValid = len(result.Errors) == 0

	return result
}

// sanitizeValue sanitizes a single value
func (v *ValidationService) sanitizeValue(value interface{}, field string) interface{} {
	switch val := value.(type) {
	case string:
		return v.sanitizeString(val)
	case []string:
		sanitized := make([]string, len(val))
		for i, s := range val {
			sanitized[i] = v.sanitizeString(s)
		}
		return sanitized
	default:
		return value
	}
}

// sanitizeString sanitizes string input
func (v *ValidationService) sanitizeString(input string) string {
	// Trim whitespace
	sanitized := strings.TrimSpace(input)

	// XSS Protection
	if v.config.EnableXSSProtection {
		sanitized = v.preventXSS(sanitized)
	}

	// SQL Injection Prevention
	if v.config.EnableSQLInjectionPrevention {
		sanitized = v.preventSQLInjection(sanitized)
	}

	// Length limitation
	if len(sanitized) > v.config.MaxStringLength {
		sanitized = sanitized[:v.config.MaxStringLength]
	}

	return sanitized
}

// preventXSS prevents XSS attacks
func (v *ValidationService) preventXSS(input string) string {
	// HTML escape
	escaped := html.EscapeString(input)

	// Remove dangerous patterns
	dangerousPatterns := []string{
		`<script[^>]*>.*?</script>`,
		`javascript:`,
		`vbscript:`,
		`onload=`,
		`onerror=`,
		`onclick=`,
		`onmouseover=`,
		`<iframe[^>]*>.*?</iframe>`,
		`<object[^>]*>.*?</object>`,
		`<embed[^>]*>.*?</embed>`,
	}

	for _, pattern := range dangerousPatterns {
		// Safely compile the regex pattern
		re, err := regexp.Compile(`(?i)` + pattern)
		if err != nil {
			// Skip malformed patterns and log the error
			continue
		}
		escaped = re.ReplaceAllString(escaped, "")
	}

	return escaped
}

// preventSQLInjection prevents SQL injection attacks
func (v *ValidationService) preventSQLInjection(input string) string {
	// Remove or escape dangerous SQL patterns
	sqlPatterns := []string{
		`'`,
		`"`,
		`;`,
		`--`,
		`/*`,
		`*/`,
		`xp_`,
		`sp_`,
		`\bUNION\b`,
		`\bSELECT\b`,
		`\bINSERT\b`,
		`\bUPDATE\b`,
		`\bDELETE\b`,
		`\bDROP\b`,
		`\bCREATE\b`,
		`\bALTER\b`,
		`\bEXEC\b`,
		`\bEXECUTE\b`,
	}

	sanitized := input
	for _, pattern := range sqlPatterns {
		// Safely compile the regex pattern
		re, err := regexp.Compile(`(?i)` + pattern)
		if err != nil {
			// Skip malformed patterns
			continue
		}
		sanitized = re.ReplaceAllString(sanitized, "")
	}

	return sanitized
}

// validateField validates a single field
func (v *ValidationService) validateField(field string, value interface{}, rule ValidationRule) []ValidationError {
	var errors []ValidationError

	// Type validation
	if !v.validateType(value, rule.Type) {
		errors = append(errors, ValidationError{
			Field:   field,
			Code:    "INVALID_TYPE",
			Message: fmt.Sprintf("Expected type %s", rule.Type),
			Value:   value,
		})
		return errors // Stop validation if type is wrong
	}

	// String-specific validations
	if strVal, ok := value.(string); ok {
		errors = append(errors, v.validateString(field, strVal, rule)...)
	}

	// Numeric validations
	if numVal, ok := v.toFloat64(value); ok {
		errors = append(errors, v.validateNumeric(field, numVal, rule)...)
	}

	// Enum validation
	if len(rule.Enum) > 0 {
		if !v.validateEnum(value, rule.Enum) {
			errors = append(errors, ValidationError{
				Field:      field,
				Code:       "INVALID_ENUM",
				Message:    fmt.Sprintf("Value must be one of: %v", rule.Enum),
				Value:      value,
				Constraint: strings.Join(rule.Enum, ", "),
			})
		}
	}

	// Pattern validation
	if rule.Pattern != nil {
		if strVal, ok := value.(string); ok {
			// Safely compile and match the regex pattern
			matched, err := regexp.MatchString(*rule.Pattern, strVal)
			if err != nil {
				// Handle malformed regex pattern
				errors = append(errors, ValidationError{
					Field:      field,
					Code:       "INVALID_PATTERN",
					Message:    "Invalid regex pattern in validation rule",
					Value:      value,
					Constraint: *rule.Pattern,
				})
			} else if !matched {
				errors = append(errors, ValidationError{
					Field:      field,
					Code:       "PATTERN_MISMATCH",
					Message:    "Value does not match required pattern",
					Value:      value,
					Constraint: *rule.Pattern,
				})
			}
		}
	}

	// Custom validation
	if rule.Custom != nil {
		if err := rule.Custom(value); err != nil {
			errors = append(errors, ValidationError{
				Field:   field,
				Code:    "CUSTOM_VALIDATION",
				Message: err.Error(),
				Value:   value,
			})
		}
	}

	return errors
}

// validateType validates the data type
func (v *ValidationService) validateType(value interface{}, expectedType string) bool {
	switch expectedType {
	case "string":
		_, ok := value.(string)
		return ok
	case "int", "integer":
		switch value.(type) {
		case int, int32, int64, float64:
			return true
		case string:
			_, err := strconv.Atoi(value.(string))
			return err == nil
		}
		return false
	case "float", "number":
		switch value.(type) {
		case float64, float32, int, int32, int64:
			return true
		case string:
			_, err := strconv.ParseFloat(value.(string), 64)
			return err == nil
		}
		return false
	case "bool", "boolean":
		_, ok := value.(bool)
		if !ok {
			if str, isStr := value.(string); isStr {
				_, err := strconv.ParseBool(str)
				return err == nil
			}
		}
		return ok
	case "date", "datetime":
		if str, ok := value.(string); ok {
			_, err := time.Parse("2006-01-02", str)
			if err != nil {
				_, err = time.Parse("2006-01-02T15:04:05Z", str)
			}
			return err == nil
		}
		return false
	case "email":
		if str, ok := value.(string); ok {
			return v.isValidEmail(str)
		}
		return false
	case "url":
		if str, ok := value.(string); ok {
			_, err := url.ParseRequestURI(str)
			return err == nil
		}
		return false
	default:
		return true // Unknown types pass validation
	}
}

// validateString validates string-specific constraints
func (v *ValidationService) validateString(field, value string, rule ValidationRule) []ValidationError {
	var errors []ValidationError

	// Length validations
	if rule.MinLength != nil && len(value) < *rule.MinLength {
		errors = append(errors, ValidationError{
			Field:      field,
			Code:       "MIN_LENGTH",
			Message:    fmt.Sprintf("Minimum length is %d", *rule.MinLength),
			Value:      value,
			Constraint: fmt.Sprintf("min_length:%d", *rule.MinLength),
		})
	}

	if rule.MaxLength != nil && len(value) > *rule.MaxLength {
		errors = append(errors, ValidationError{
			Field:      field,
			Code:       "MAX_LENGTH",
			Message:    fmt.Sprintf("Maximum length is %d", *rule.MaxLength),
			Value:      value,
			Constraint: fmt.Sprintf("max_length:%d", *rule.MaxLength),
		})
	}

	return errors
}

// validateNumeric validates numeric constraints
func (v *ValidationService) validateNumeric(field string, value float64, rule ValidationRule) []ValidationError {
	var errors []ValidationError

	if rule.Min != nil && value < *rule.Min {
		errors = append(errors, ValidationError{
			Field:      field,
			Code:       "MIN_VALUE",
			Message:    fmt.Sprintf("Minimum value is %f", *rule.Min),
			Value:      value,
			Constraint: fmt.Sprintf("min:%f", *rule.Min),
		})
	}

	if rule.Max != nil && value > *rule.Max {
		errors = append(errors, ValidationError{
			Field:      field,
			Code:       "MAX_VALUE",
			Message:    fmt.Sprintf("Maximum value is %f", *rule.Max),
			Value:      value,
			Constraint: fmt.Sprintf("max:%f", *rule.Max),
		})
	}

	return errors
}

// validateEnum validates enum constraints
func (v *ValidationService) validateEnum(value interface{}, enum []string) bool {
	strValue := fmt.Sprintf("%v", value)
	for _, allowed := range enum {
		if strValue == allowed {
			return true
		}
	}
	return false
}

// Helper methods

// toFloat64 converts various numeric types to float64
func (v *ValidationService) toFloat64(value interface{}) (float64, bool) {
	switch val := value.(type) {
	case float64:
		return val, true
	case float32:
		return float64(val), true
	case int:
		return float64(val), true
	case int32:
		return float64(val), true
	case int64:
		return float64(val), true
	case string:
		if f, err := strconv.ParseFloat(val, 64); err == nil {
			return f, true
		}
	}
	return 0, false
}

// isValidEmail validates email format
func (v *ValidationService) isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// ValidateGinContext validates Gin context data
func (v *ValidationService) ValidateGinContext(c *gin.Context, rules []ValidationRule) *ValidationResult {
	// Extract data from various sources
	data := make(map[string]interface{})

	// Query parameters
	for key, values := range c.Request.URL.Query() {
		if len(values) == 1 {
			data[key] = values[0]
		} else {
			data[key] = values
		}
	}

	// Form data
	if c.Request.Form != nil {
		for key, values := range c.Request.Form {
			if len(values) == 1 {
				data[key] = values[0]
			} else {
				data[key] = values
			}
		}
	}

	// JSON body (if applicable)
	if c.Request.Header.Get("Content-Type") == "application/json" {
		var jsonData map[string]interface{}
		if err := c.ShouldBindJSON(&jsonData); err == nil {
			for key, value := range jsonData {
				data[key] = value
			}
		}
	}

	return v.ValidateAndSanitize(data, rules)
}

// CreateCommonValidationRules creates common validation rules for different entity types
func (v *ValidationService) CreateCommonValidationRules(entityType string) []ValidationRule {
	switch entityType {
	case "user":
		return []ValidationRule{
			{Field: "username", Type: "string", Required: true, MinLength: intPtr(3), MaxLength: intPtr(50), Pattern: stringPtr("^[a-zA-Z0-9_]+$")},
			{Field: "email", Type: "email", Required: true, MaxLength: intPtr(255)},
			{Field: "password", Type: "string", Required: true, MinLength: intPtr(8), MaxLength: intPtr(128)},
			{Field: "first_name", Type: "string", Required: true, MinLength: intPtr(1), MaxLength: intPtr(100)},
			{Field: "last_name", Type: "string", Required: true, MinLength: intPtr(1), MaxLength: intPtr(100)},
			{Field: "phone", Type: "string", Required: false, Pattern: stringPtr(`^\+?[1-9]\d{1,14}$`)},
		}
	case "document":
		return []ValidationRule{
			{Field: "title", Type: "string", Required: true, MinLength: intPtr(1), MaxLength: intPtr(500)},
			{Field: "content", Type: "string", Required: false, MinLength: intPtr(1)},
			{Field: "category_id", Type: "integer", Required: false, Min: float64Ptr(1)},
			{Field: "agency_id", Type: "integer", Required: true, Min: float64Ptr(1)},
			{Field: "status", Type: "string", Required: false, Enum: []string{"draft", "published", "archived"}},
			{Field: "effective_date", Type: "date", Required: false},
			{Field: "expiration_date", Type: "date", Required: false},
		}
	case "comment":
		return []ValidationRule{
			{Field: "content", Type: "string", Required: true, MinLength: intPtr(1), MaxLength: intPtr(5000)},
			{Field: "document_id", Type: "integer", Required: true, Min: float64Ptr(1)},
			{Field: "parent_id", Type: "integer", Required: false, Min: float64Ptr(1)},
		}
	case "agency":
		return []ValidationRule{
			{Field: "name", Type: "string", Required: true, MinLength: intPtr(1), MaxLength: intPtr(255)},
			{Field: "description", Type: "string", Required: false, MaxLength: intPtr(2000)},
			{Field: "website", Type: "url", Required: false},
			{Field: "contact_email", Type: "email", Required: false},
		}
	default:
		return []ValidationRule{}
	}
}

// Middleware for automatic validation
func (v *ValidationService) ValidationMiddleware(entityType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		rules := v.CreateCommonValidationRules(entityType)
		result := v.ValidateGinContext(c, rules)

		if !result.IsValid {
			c.JSON(400, gin.H{
				"error":   "Validation failed",
				"details": result.Errors,
			})
			c.Abort()
			return
		}

		// Store sanitized data in context
		c.Set("sanitized_data", result.SanitizedData)
		c.Next()
	}
}

// Helper functions for creating pointers
func intPtr(i int) *int {
	return &i
}

func stringPtr(s string) *string {
	return &s
}

func float64Ptr(f float64) *float64 {
	return &f
}
