-- Fix missing proceeding_documents table
-- This table is required for proceeding-document relationships

-- Junction table for proceeding-document relationships
CREATE TABLE IF NOT EXISTS proceeding_documents (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "source", "output", "reference"
    notes TEXT,
    PRIMARY KEY (proceeding_id, document_id)
);

-- Junction table for proceeding-task relationships (in case it's also missing)
CREATE TABLE IF NOT EXISTS proceeding_tasks (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    task_id INTEGER NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "generated_by", "triggers", "blocks"
    notes TEXT,
    PRIMARY KEY (proceeding_id, task_id)
);

-- Junction table for proceeding-regulation relationships (in case it's also missing)
CREATE TABLE IF NOT EXISTS proceeding_regulations (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    regulation_id INTEGER NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "source", "implements", "modifies"
    notes TEXT,
    PRIMARY KEY (proceeding_id, regulation_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_proceeding_documents_proceeding_id ON proceeding_documents(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_documents_document_id ON proceeding_documents(document_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_documents_relationship_type ON proceeding_documents(relationship_type);

CREATE INDEX IF NOT EXISTS idx_proceeding_tasks_proceeding_id ON proceeding_tasks(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_tasks_task_id ON proceeding_tasks(task_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_tasks_relationship_type ON proceeding_tasks(relationship_type);

CREATE INDEX IF NOT EXISTS idx_proceeding_regulations_proceeding_id ON proceeding_regulations(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_regulations_regulation_id ON proceeding_regulations(regulation_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_regulations_relationship_type ON proceeding_regulations(relationship_type);
