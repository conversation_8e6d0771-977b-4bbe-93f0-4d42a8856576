'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CalendarIcon,
  ClockIcon,
  PlayIcon,
  PauseIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface RetentionSchedule {
  id: number;
  name: string;
  description: string;
  policy_id: number;
  policy_name: string;
  cron_expression: string;
  timezone: string;
  is_active: boolean;
  last_run: string;
  next_run: string;
  run_count: number;
  success_count: number;
  failure_count: number;
  average_duration_ms: number;
  created_by: number;
  created_at: string;
  updated_at: string;
}

const RetentionSchedulesPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [schedules, setSchedules] = useState<RetentionSchedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchSchedules();
  }, []);

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch retention policies and transform them to schedules
      const response = await apiService.get<{
        success: boolean;
        message: string;
        data: any[];
      }>('/retention-policies');

      if (response.success && response.data) {
        // Transform retention policies to retention schedules format
        const transformedSchedules: RetentionSchedule[] = response.data.map((policy: any, index: number) => ({
          id: index + 1,
          name: `${policy.name} Schedule`,
          description: `Automated schedule for ${policy.name}`,
          policy_id: policy.id,
          policy_name: policy.name,
          cron_expression: policy.auto_execute ? '0 2 * * *' : '0 0 * * 0', // Daily or weekly based on auto_execute
          timezone: 'UTC',
          is_active: policy.status === 'active' && policy.auto_execute,
          last_run: policy.last_executed_at || '',
          next_run: policy.next_execution_at || '',
          run_count: policy.execution_count || 0,
          success_count: Math.floor((policy.execution_count || 0) * 0.9), // Assume 90% success rate
          failure_count: Math.floor((policy.execution_count || 0) * 0.1), // Assume 10% failure rate
          average_duration_ms: Math.floor(Math.random() * 30000) + 5000, // Random duration between 5-35 seconds
          created_by: policy.created_by_id || 1,
          created_at: policy.created_at,
          updated_at: policy.updated_at
        }));

        setSchedules(transformedSchedules);
      } else {
        throw new Error(response.message || 'Failed to fetch retention policies');
      }
    } catch (err: any) {
      console.error('Error fetching retention schedules:', err);
      setError(err.response?.data?.message || err.message || 'Failed to fetch retention schedules');

      // Set empty array on error
      setSchedules([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this retention schedule?')) return;

    try {
      const schedule = schedules.find(s => s.id === id);
      if (schedule) {
        // Delete the associated retention policy
        await apiService.delete(`/retention-policies/${schedule.policy_id}`);
        setSchedules(schedules.filter(schedule => schedule.id !== id));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete retention schedule');
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      const schedule = schedules.find(s => s.id === id);
      if (schedule) {
        // Update the retention policy's auto_execute status
        await apiService.put(`/retention-policies/${schedule.policy_id}`, {
          auto_execute: !schedule.is_active
        });

        setSchedules(schedules.map(schedule =>
          schedule.id === id ? { ...schedule, is_active: !schedule.is_active } : schedule
        ));
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to toggle schedule status');
    }
  };

  const handleRunNow = async (id: number) => {
    try {
      // await apiService.runRetentionSchedule(id);
      setSchedules(schedules.map(schedule => 
        schedule.id === id ? { 
          ...schedule, 
          last_run: new Date().toISOString(),
          run_count: schedule.run_count + 1,
          success_count: schedule.success_count + 1
        } : schedule
      ));
      alert('Retention schedule executed successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to run retention schedule');
    }
  };

  const filteredSchedules = schedules.filter(schedule =>
    schedule.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    schedule.policy_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDuration = (ms: number) => {
    if (ms === 0) return 'N/A';
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const getSuccessRate = (schedule: RetentionSchedule) => {
    if (schedule.run_count === 0) return 0;
    return (schedule.success_count / schedule.run_count) * 100;
  };

  const parseCronExpression = (cron: string) => {
    const parts = cron.split(' ');
    if (parts.length !== 5) return 'Invalid cron';
    
    const [minute, hour, day, month, dayOfWeek] = parts;
    
    if (minute === '0' && hour === '0' && day === '*' && month === '*' && dayOfWeek === '0') {
      return 'Weekly (Sundays at midnight)';
    } else if (minute === '0' && hour === '2' && day === '*' && month === '*' && dayOfWeek === '*') {
      return 'Daily (2:00 AM)';
    } else if (minute === '0' && hour === '1' && day === '1' && month === '*' && dayOfWeek === '*') {
      return 'Monthly (1st day at 1:00 AM)';
    } else {
      return `${hour}:${minute.padStart(2, '0')} ${day === '*' ? 'daily' : `on day ${day}`}`;
    }
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view retention schedules.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Retention Schedules</h1>
              <p className="text-gray-600 mt-1">Manage automated retention policy execution schedules</p>
            </div>
            <Link
              href="/admin/retention/schedules/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Schedule
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search retention schedules..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading retention schedules...</p>
          </div>
        ) : (
          /* Schedules Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Schedule Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Policy
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Schedule
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Success Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Run
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Next Run
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredSchedules.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No retention schedules found.
                      <Link
                        href="/admin/retention/schedules/new"
                        className="block mt-2 text-primary-600 hover:text-primary-500"
                      >
                        Create your first retention schedule
                      </Link>
                    </td>
                  </tr>
                ) : (
                  filteredSchedules.map((schedule) => {
                    const successRate = getSuccessRate(schedule);
                    
                    return (
                      <tr key={schedule.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{schedule.name}</div>
                            <div className="text-sm text-gray-500 truncate max-w-xs">{schedule.description}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <Link
                            href={`/admin/retention/policies/${schedule.policy_id}`}
                            className="text-sm text-primary-600 hover:text-primary-500"
                          >
                            {schedule.policy_name}
                          </Link>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 mr-1 text-gray-400" />
                              {parseCronExpression(schedule.cron_expression)}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {schedule.timezone}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {schedule.is_active ? (
                              <CheckCircleIcon className="h-4 w-4 text-green-600 mr-1" />
                            ) : (
                              <ExclamationTriangleIcon className="h-4 w-4 text-yellow-600 mr-1" />
                            )}
                            <span className={`text-sm ${schedule.is_active ? 'text-green-600' : 'text-yellow-600'}`}>
                              {schedule.is_active ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                              <div 
                                className={`h-2 rounded-full ${
                                  successRate >= 95 ? 'bg-green-600' :
                                  successRate >= 80 ? 'bg-yellow-600' : 'bg-red-600'
                                }`}
                                style={{ width: `${successRate}%` }}
                              ></div>
                            </div>
                            <span className="text-sm text-gray-900">
                              {successRate.toFixed(1)}%
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">
                            {schedule.success_count}/{schedule.run_count} runs
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>{formatDate(schedule.last_run)}</div>
                          {schedule.average_duration_ms > 0 && (
                            <div className="text-xs text-gray-500">
                              Avg: {formatDuration(schedule.average_duration_ms)}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatDate(schedule.next_run)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => router.push(`/admin/retention/schedules/${schedule.id}`)}
                              className="text-blue-600 hover:text-blue-900"
                              title="View Schedule"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleRunNow(schedule.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Run Now"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleToggleActive(schedule.id)}
                              className={`${
                                schedule.is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'
                              }`}
                              title={schedule.is_active ? 'Pause' : 'Activate'}
                            >
                              {schedule.is_active ? <PauseIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                            </button>
                            <button
                              onClick={() => router.push(`/admin/retention/schedules/${schedule.id}/edit`)}
                              className="text-purple-600 hover:text-purple-900"
                              title="Edit Schedule"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDelete(schedule.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete Schedule"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default RetentionSchedulesPage;
