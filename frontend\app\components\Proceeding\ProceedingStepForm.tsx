'use client'

import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  MicrophoneIcon,
  VideoCameraIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';

interface ProceedingStepFormData {
  name: string;
  description: string;
  step_type: string;
  content_type: string;
  text_content: string;
  voice_recording: string;
  document_content: string;
  video_content: string;
  planned_start_date: string;
  planned_end_date: string;
  estimated_duration: number | undefined;
  completion_criteria: string;
  requires_previous_completion: boolean;
  is_mandatory: boolean;
  allows_parallel_execution: boolean;
  requires_review: boolean;
  assigned_to_id: number | undefined;
  priority: string;
  is_optional: boolean;
  is_critical: boolean;
  notes: string;
}

interface ProceedingStepFormProps {
  initialData?: Partial<ProceedingStepFormData>;
  onSubmit: (data: ProceedingStepFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
  isEdit?: boolean;
}

const stepTypeOptions = [
  { value: 'notice_of_intent', label: 'Notice of Intent' },
  { value: 'advance_notice', label: 'Advance Notice' },
  { value: 'notice_proposed_rule', label: 'Notice of Proposed Rulemaking' },
  { value: 'public_comment', label: 'Public Comment Period' },
  { value: 'supplemental_notice', label: 'Supplemental Notice' },
  { value: 'final_rule', label: 'Final Rule' },
  { value: 'interim_final_rule', label: 'Interim Final Rule' },
  { value: 'regulation_publication', label: 'Regulation Publication' },
  { value: 'correcting_amendment', label: 'Correcting Amendment' },
  { value: 'custom', label: 'Custom Step' }
];

const contentTypeOptions = [
  { value: 'text', label: 'Text Content', icon: DocumentTextIcon },
  { value: 'voice', label: 'Voice Recording', icon: MicrophoneIcon },
  { value: 'document', label: 'Document', icon: DocumentTextIcon },
  { value: 'video', label: 'Video Content', icon: VideoCameraIcon },
  { value: 'mixed', label: 'Mixed Content', icon: PhotoIcon }
];

const priorityOptions = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'critical', label: 'Critical' },
  { value: 'urgent', label: 'Urgent' }
];

const ProceedingStepForm: React.FC<ProceedingStepFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  isEdit = false
}) => {
  const [formData, setFormData] = useState<ProceedingStepFormData>({
    name: '',
    description: '',
    step_type: 'custom',
    content_type: 'text',
    text_content: '',
    voice_recording: '',
    document_content: '',
    video_content: '',
    planned_start_date: '',
    planned_end_date: '',
    estimated_duration: undefined,
    completion_criteria: '',
    requires_previous_completion: true,
    is_mandatory: true,
    allows_parallel_execution: false,
    requires_review: true,
    assigned_to_id: undefined,
    priority: 'medium',
    is_optional: false,
    is_critical: false,
    notes: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }));
    }
  }, [initialData]);

  const handleInputChange = (field: keyof ProceedingStepFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Step name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Step description is required';
    }

    if (!formData.step_type) {
      newErrors.step_type = 'Step type is required';
    }

    if (!formData.completion_criteria.trim()) {
      newErrors.completion_criteria = 'Completion criteria is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onSubmit(formData);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Step Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Step Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter step name"
            />
            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Step Type *
            </label>
            <select
              value={formData.step_type}
              onChange={(e) => handleInputChange('step_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            >
              {stepTypeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.step_type && <p className="mt-1 text-sm text-red-600">{errors.step_type}</p>}
          </div>
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Describe the purpose and scope of this step"
          />
          {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
        </div>
      </div>

      {/* Content Management */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Content Management
        </h3>
        
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Content Type
          </label>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            {contentTypeOptions.map(option => {
              const IconComponent = option.icon;
              return (
                <label
                  key={option.value}
                  className={`relative flex items-center justify-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                    formData.content_type === option.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900'
                      : 'border-gray-300 dark:border-gray-600'
                  }`}
                >
                  <input
                    type="radio"
                    name="content_type"
                    value={option.value}
                    checked={formData.content_type === option.value}
                    onChange={(e) => handleInputChange('content_type', e.target.value)}
                    className="sr-only"
                  />
                  <div className="text-center">
                    <IconComponent className="h-6 w-6 mx-auto mb-1 text-gray-600 dark:text-gray-400" />
                    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
                      {option.label}
                    </span>
                  </div>
                </label>
              );
            })}
          </div>
        </div>

        {/* Content Fields based on type */}
        {formData.content_type === 'text' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Text Content
            </label>
            <textarea
              value={formData.text_content}
              onChange={(e) => handleInputChange('text_content', e.target.value)}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter the text content for this step"
            />
          </div>
        )}

        {formData.content_type === 'voice' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Voice Recording
            </label>
            <input
              type="file"
              accept="audio/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleInputChange('voice_recording', file.name);
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <p className="mt-1 text-sm text-gray-500">
              Upload an audio file for voice content
            </p>
          </div>
        )}

        {formData.content_type === 'document' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Document Content
            </label>
            <textarea
              value={formData.document_content}
              onChange={(e) => handleInputChange('document_content', e.target.value)}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Enter document content or reference"
            />
          </div>
        )}

        {formData.content_type === 'video' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Video Content
            </label>
            <input
              type="file"
              accept="video/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleInputChange('video_content', file.name);
                }
              }}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
            <p className="mt-1 text-sm text-gray-500">
              Upload a video file for video content
            </p>
          </div>
        )}
      </div>

      {/* Timing and Requirements */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Timing and Requirements
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Planned Start Date
            </label>
            <input
              type="date"
              value={formData.planned_start_date}
              onChange={(e) => handleInputChange('planned_start_date', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Planned End Date
            </label>
            <input
              type="date"
              value={formData.planned_end_date}
              onChange={(e) => handleInputChange('planned_end_date', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Estimated Duration (days)
            </label>
            <input
              type="number"
              min="1"
              value={formData.estimated_duration || ''}
              onChange={(e) => handleInputChange('estimated_duration', e.target.value ? parseInt(e.target.value) : undefined)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
              placeholder="Days"
            />
          </div>
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Completion Criteria *
          </label>
          <textarea
            value={formData.completion_criteria}
            onChange={(e) => handleInputChange('completion_criteria', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
            placeholder="Define what constitutes completion of this step"
          />
          {errors.completion_criteria && <p className="mt-1 text-sm text-red-600">{errors.completion_criteria}</p>}
        </div>

        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Priority
          </label>
          <select
            value={formData.priority}
            onChange={(e) => handleInputChange('priority', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          >
            {priorityOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Step Configuration */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Step Configuration
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              type="checkbox"
              id="requires_previous_completion"
              checked={formData.requires_previous_completion}
              onChange={(e) => handleInputChange('requires_previous_completion', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="requires_previous_completion" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Requires previous step completion (Sequential execution)
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_mandatory"
              checked={formData.is_mandatory}
              onChange={(e) => handleInputChange('is_mandatory', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_mandatory" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Mandatory step (Required for proceeding completion)
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="allows_parallel_execution"
              checked={formData.allows_parallel_execution}
              onChange={(e) => handleInputChange('allows_parallel_execution', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="allows_parallel_execution" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Allows parallel execution with other steps
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="requires_review"
              checked={formData.requires_review}
              onChange={(e) => handleInputChange('requires_review', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="requires_review" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Requires mandatory review before completion
            </label>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_critical"
              checked={formData.is_critical}
              onChange={(e) => handleInputChange('is_critical', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="is_critical" className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
              Critical step (High importance)
            </label>
          </div>
        </div>
      </div>

      {/* Notes */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
          Additional Notes
        </h3>
        
        <textarea
          value={formData.notes}
          onChange={(e) => handleInputChange('notes', e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
          placeholder="Add any additional notes or instructions for this step"
        />
      </div>

      {/* Form Actions */}
      <div className="flex justify-end space-x-3">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isLoading}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          {isLoading ? 'Saving...' : isEdit ? 'Update Step' : 'Create Step'}
        </button>
      </div>
    </form>
  );
};

export default ProceedingStepForm;
