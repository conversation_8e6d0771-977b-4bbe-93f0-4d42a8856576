'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  PauseIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  UserGroupIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface SignatureWorkflow {
  id: number;
  name: string;
  description: string;
  template_id: number;
  template_name: string;
  document_id?: number;
  document_name?: string;
  status: 'draft' | 'active' | 'pending' | 'completed' | 'cancelled' | 'expired';
  current_step: number;
  total_steps: number;
  signers: any[];
  deadline: string;
  created_by: number;
  created_by_name: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  progress_percentage: number;
}

const SignatureWorkflowsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [workflows, setWorkflows] = useState<SignatureWorkflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchWorkflows();
  }, []);

  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch signature workflows from signatures API
      const response = await apiService.get<{
        success: boolean;
        message: string;
        data: any[];
      }>('/signatures/my');

      if (response.success && response.data) {
        // Transform signatures to signature workflows format
        const transformedWorkflows: SignatureWorkflow[] = response.data.map((signature: any, index: number) => ({
          id: signature.signature_id || index + 1,
          name: `Signature Workflow ${signature.signature_id || index + 1}`,
          description: `Digital signature workflow for ${signature.document?.title || 'document'}`,
          template_id: 1,
          template_name: 'Digital Signature Template',
          document_id: signature.document_id,
          document_name: signature.document?.title || 'Unknown Document',
          status: 'active' as const,
          current_step: signature.status === 'signed' ? 2 : 1,
          total_steps: 2,
          signers: [
            {
              id: signature.signer_id || 1,
              name: signature.signer_name || 'Unknown Signer',
              role: 'Digital Signer',
              status: signature.status === 'signed' ? 'completed' : 'pending',
              signed_at: signature.signed_at
            }
          ],
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          created_by: signature.requested_by_id || 1,
          created_by_name: signature.requested_by?.name || 'System Admin',
          created_at: signature.created_at,
          updated_at: signature.updated_at,
          completed_at: signature.signed_at,
          progress_percentage: signature.status === 'signed' ? 100 : 50
        }));

        setWorkflows(transformedWorkflows);
      } else {
        throw new Error(response.message || 'Failed to fetch signature workflows');
      }
    } catch (err: any) {
      console.error('Error fetching signature workflows:', err);
      setError(err.response?.data?.message || err.message || 'Failed to fetch signature workflows');

      // Set empty array on error
      setWorkflows([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchWorkflowsWithMockData = async () => {
    try {
      setLoading(true);
      setError('');

      // Mock data for signature workflows
      const mockWorkflows = [
        {
          id: 1,
          name: 'Q4 Budget Approval Workflow',
          description: 'Approval workflow for Q4 budget allocation document',
          template_id: 2,
          template_name: 'Financial Authorization Template',
          document_id: 123,
          document_name: 'Q4_Budget_2024.pdf',
          status: 'pending',
          current_step: 2,
          total_steps: 3,
          signers: [
            { id: 1, name: 'John Manager', role: 'Department Manager', status: 'completed', signed_at: new Date().toISOString() },
            { id: 2, name: 'Jane Accountant', role: 'Accountant Review', status: 'pending', signed_at: null },
            { id: 3, name: 'Bob CFO', role: 'CFO Approval', status: 'waiting', signed_at: null }
          ],
          deadline: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          created_by: 1,
          created_by_name: 'John Admin',
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          progress_percentage: 66
        },
        {
          id: 2,
          name: 'Policy Document Approval',
          description: 'Multi-step approval for new company policy document',
          template_id: 1,
          template_name: 'Document Approval Template',
          document_id: 124,
          document_name: 'Remote_Work_Policy_v2.pdf',
          status: 'completed',
          current_step: 3,
          total_steps: 3,
          signers: [
            { id: 4, name: 'Alice Reviewer', role: 'Reviewed By', status: 'completed', signed_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() },
            { id: 5, name: 'Charlie Approver', role: 'Approved By', status: 'completed', signed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() },
            { id: 6, name: 'Diana Witness', role: 'Witnessed By', status: 'completed', signed_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() }
          ],
          deadline: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          created_by: 2,
          created_by_name: 'Jane Legal',
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          completed_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          progress_percentage: 100
        },
        {
          id: 3,
          name: 'Vendor Contract Execution',
          description: 'Multi-party contract signing with external vendor',
          template_id: 3,
          template_name: 'Contract Execution Template',
          document_id: 125,
          document_name: 'Vendor_Contract_ABC_Corp.pdf',
          status: 'active',
          current_step: 1,
          total_steps: 4,
          signers: [
            { id: 7, name: 'Legal Team', role: 'Legal Counsel', status: 'pending', signed_at: null },
            { id: 8, name: 'Company CEO', role: 'Party A Signature', status: 'waiting', signed_at: null },
            { id: 9, name: 'ABC Corp Rep', role: 'Party B Signature', status: 'waiting', signed_at: null },
            { id: 10, name: 'Notary Service', role: 'Notary Public', status: 'waiting', signed_at: null }
          ],
          deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          created_by: 1,
          created_by_name: 'John Admin',
          created_at: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          progress_percentage: 25
        }
      ];
      setWorkflows(mockWorkflows as SignatureWorkflow[]);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch signature workflows');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this signature workflow?')) return;
    
    try {
      // await apiService.deleteSignatureWorkflow(id);
      setWorkflows(workflows.filter(workflow => workflow.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete signature workflow');
    }
  };

  const handleCancel = async (id: number) => {
    if (!confirm('Are you sure you want to cancel this workflow?')) return;
    
    try {
      // await apiService.cancelSignatureWorkflow(id);
      setWorkflows(workflows.map(workflow => 
        workflow.id === id ? { ...workflow, status: 'cancelled' as const } : workflow
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to cancel workflow');
    }
  };

  const handleResend = async (id: number) => {
    try {
      // await apiService.resendSignatureWorkflow(id);
      alert('Signature reminders sent successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to resend workflow');
    }
  };

  const filteredWorkflows = workflows.filter(workflow =>
    workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.template_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'active':
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'active':
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      case 'cancelled':
      case 'expired':
        return <XCircleIcon className="h-4 w-4" />;
      case 'draft':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isOverdue = (deadline: string, status: string) => {
    return status !== 'completed' && status !== 'cancelled' && new Date(deadline) < new Date();
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view signature workflows.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Signature Workflows</h1>
              <p className="text-gray-600 mt-1">Monitor and manage digital signature workflows and approvals</p>
            </div>
            <Link
              href="/admin/signatures/workflows/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Workflow
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search signature workflows..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading signature workflows...</p>
          </div>
        ) : (
          /* Workflows Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Workflow Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Template
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Signers
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deadline
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredWorkflows.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No signature workflows found.
                      <Link
                        href="/admin/signatures/workflows/new"
                        className="block mt-2 text-primary-600 hover:text-primary-500"
                      >
                        Create your first signature workflow
                      </Link>
                    </td>
                  </tr>
                ) : (
                  filteredWorkflows.map((workflow) => (
                    <tr key={workflow.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{workflow.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{workflow.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {workflow.document_name ? (
                          <div className="flex items-center">
                            <DocumentIcon className="h-4 w-4 text-gray-400 mr-2" />
                            <Link
                              href={`/documents/${workflow.document_id}`}
                              className="text-sm text-primary-600 hover:text-primary-500 truncate max-w-xs"
                            >
                              {workflow.document_name}
                            </Link>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">No document</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <Link
                          href={`/admin/signatures/templates/${workflow.template_id}`}
                          className="text-sm text-primary-600 hover:text-primary-500"
                        >
                          {workflow.template_name}
                        </Link>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(workflow.status)}`}>
                          {getStatusIcon(workflow.status)}
                          <span className="ml-1">{workflow.status}</span>
                        </span>
                        {isOverdue(workflow.deadline, workflow.status) && (
                          <div className="text-xs text-red-600 mt-1">Overdue</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className={`h-2 rounded-full ${
                                workflow.progress_percentage === 100 ? 'bg-green-600' :
                                workflow.progress_percentage >= 50 ? 'bg-blue-600' : 'bg-yellow-600'
                              }`}
                              style={{ width: `${workflow.progress_percentage}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">{workflow.progress_percentage}%</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          Step {workflow.current_step} of {workflow.total_steps}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <UserGroupIcon className="h-4 w-4 text-gray-400 mr-1" />
                          <span className="text-sm text-gray-900">{workflow.signers.length}</span>
                        </div>
                        <div className="text-xs text-gray-500">
                          {workflow.signers.filter(s => s.status === 'completed').length} signed
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm ${isOverdue(workflow.deadline, workflow.status) ? 'text-red-600' : 'text-gray-900'}`}>
                          {formatDate(workflow.deadline)}
                        </div>
                        {workflow.completed_at && (
                          <div className="text-xs text-green-600">
                            Completed: {formatDate(workflow.completed_at)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/signatures/workflows/${workflow.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                            title="View Workflow"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {(workflow.status === 'active' || workflow.status === 'pending') && (
                            <button
                              onClick={() => handleResend(workflow.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Resend Reminders"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </button>
                          )}
                          {workflow.status !== 'completed' && workflow.status !== 'cancelled' && (
                            <button
                              onClick={() => handleCancel(workflow.id)}
                              className="text-yellow-600 hover:text-yellow-900"
                              title="Cancel Workflow"
                            >
                              <PauseIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => router.push(`/admin/signatures/workflows/${workflow.id}/edit`)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Edit Workflow"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(workflow.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Workflow"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SignatureWorkflowsPage;
