package services

import (
	"fmt"
	"log"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// TaskPerformanceScheduler handles automatic performance evaluation triggers
type TaskPerformanceScheduler struct {
	db                 *gorm.DB
	performanceService *TaskPerformanceService
	ticker             *time.Ticker
	stopChannel        chan bool
	isRunning          bool
}

// NewTaskPerformanceScheduler creates a new task performance scheduler
func NewTaskPerformanceScheduler() *TaskPerformanceScheduler {
	return &TaskPerformanceScheduler{
		db:                 database.GetDB(),
		performanceService: NewTaskPerformanceService(),
		stopChannel:        make(chan bool),
		isRunning:          false,
	}
}

// Start begins the automatic performance evaluation scheduler
func (s *TaskPerformanceScheduler) Start(intervalMinutes int) {
	if s.isRunning {
		log.Println("Task performance scheduler is already running")
		return
	}

	s.isRunning = true
	s.ticker = time.NewTicker(time.Duration(intervalMinutes) * time.Minute)

	log.Printf("Starting task performance scheduler with %d minute intervals", intervalMinutes)

	go func() {
		// Run initial evaluation
		s.evaluateTaskPerformances()

		for {
			select {
			case <-s.ticker.C:
				s.evaluateTaskPerformances()
			case <-s.stopChannel:
				s.ticker.Stop()
				s.isRunning = false
				log.Println("Task performance scheduler stopped")
				return
			}
		}
	}()
}

// Stop stops the automatic performance evaluation scheduler
func (s *TaskPerformanceScheduler) Stop() {
	if !s.isRunning {
		return
	}

	s.stopChannel <- true
}

// evaluateTaskPerformances performs automatic performance evaluation
func (s *TaskPerformanceScheduler) evaluateTaskPerformances() {
	log.Println("Running automatic task performance evaluation...")

	// 1. Update overdue tasks
	if err := s.updateOverdueTasks(); err != nil {
		log.Printf("Error updating overdue tasks: %v", err)
	}

	// 2. Recalculate performance for recently completed tasks
	if err := s.recalculateRecentCompletedTasks(); err != nil {
		log.Printf("Error recalculating recent completed tasks: %v", err)
	}

	// 3. Update performance for tasks approaching deadlines
	if err := s.updateTasksApproachingDeadlines(); err != nil {
		log.Printf("Error updating tasks approaching deadlines: %v", err)
	}

	log.Println("Automatic task performance evaluation completed")
}

// updateOverdueTasks marks tasks as overdue and updates their performance
func (s *TaskPerformanceScheduler) updateOverdueTasks() error {
	if s.db == nil {
		return fmt.Errorf("database not initialized")
	}

	now := time.Now()

	// Find tasks that are past due but not marked as on hold or completed
	var overdueTasks []models.Task
	err := s.db.Where("due_date < ? AND status NOT IN (?, ?, ?)",
		now, models.TaskStatusCompleted, models.TaskStatusOnHold, models.TaskStatusCancelled).
		Find(&overdueTasks).Error

	if err != nil {
		return err
	}

	log.Printf("Found %d overdue tasks to update", len(overdueTasks))

	for _, task := range overdueTasks {
		// Update task status to on hold
		err := s.db.Model(&task).Where("id = ?", task.ID).Updates(map[string]interface{}{
			"status": models.TaskStatusOnHold,
		}).Error

		if err != nil {
			log.Printf("Error updating task %d to overdue status: %v", task.ID, err)
			continue
		}

		// Recalculate performance for overdue task
		if err := s.performanceService.CalculateTaskPerformance(&task); err != nil {
			log.Printf("Error calculating performance for overdue task %d: %v", task.ID, err)
		}
	}

	return nil
}

// recalculateRecentCompletedTasks recalculates performance for recently completed tasks
func (s *TaskPerformanceScheduler) recalculateRecentCompletedTasks() error {
	if s.db == nil {
		return fmt.Errorf("database not initialized")
	}

	// Find tasks completed in the last 24 hours that might need performance recalculation
	yesterday := time.Now().Add(-24 * time.Hour)

	var recentTasks []models.Task
	err := s.db.Where("status = ? AND completed_at > ? AND (evaluation_date IS NULL OR evaluation_date < completed_at)",
		models.TaskStatusCompleted, yesterday).
		Find(&recentTasks).Error

	if err != nil {
		return err
	}

	log.Printf("Found %d recently completed tasks to recalculate", len(recentTasks))

	for _, task := range recentTasks {
		if err := s.performanceService.CalculateTaskPerformance(&task); err != nil {
			log.Printf("Error recalculating performance for task %d: %v", task.ID, err)
		}
	}

	return nil
}

// updateTasksApproachingDeadlines updates performance for tasks approaching deadlines
func (s *TaskPerformanceScheduler) updateTasksApproachingDeadlines() error {
	if s.db == nil {
		return fmt.Errorf("database not initialized")
	}

	// Find tasks with deadlines in the next 24 hours
	tomorrow := time.Now().Add(24 * time.Hour)
	now := time.Now()

	var approachingTasks []models.Task
	err := s.db.Where("due_date BETWEEN ? AND ? AND status IN (?, ?)",
		now, tomorrow, models.TaskStatusPending, models.TaskStatusInProgress).
		Find(&approachingTasks).Error

	if err != nil {
		return err
	}

	log.Printf("Found %d tasks approaching deadlines", len(approachingTasks))

	for _, task := range approachingTasks {
		// Update performance to reflect urgency
		if err := s.performanceService.CalculateTaskPerformance(&task); err != nil {
			log.Printf("Error calculating performance for approaching task %d: %v", task.ID, err)
		}
	}

	return nil
}

// GetSchedulerStatus returns the current status of the scheduler
func (s *TaskPerformanceScheduler) GetSchedulerStatus() map[string]interface{} {
	return map[string]interface{}{
		"is_running": s.isRunning,
		"last_run":   time.Now().Format(time.RFC3339),
	}
}

// RunManualEvaluation runs a manual performance evaluation cycle
func (s *TaskPerformanceScheduler) RunManualEvaluation() error {
	log.Println("Running manual task performance evaluation...")
	s.evaluateTaskPerformances()
	return nil
}

// Global scheduler instance
var globalTaskPerformanceScheduler *TaskPerformanceScheduler

// StartGlobalTaskPerformanceScheduler starts the global task performance scheduler
func StartGlobalTaskPerformanceScheduler(intervalMinutes int) {
	if globalTaskPerformanceScheduler == nil {
		globalTaskPerformanceScheduler = NewTaskPerformanceScheduler()
	}
	globalTaskPerformanceScheduler.Start(intervalMinutes)
}

// StopGlobalTaskPerformanceScheduler stops the global task performance scheduler
func StopGlobalTaskPerformanceScheduler() {
	if globalTaskPerformanceScheduler != nil {
		globalTaskPerformanceScheduler.Stop()
	}
}

// GetGlobalTaskPerformanceScheduler returns the global scheduler instance
func GetGlobalTaskPerformanceScheduler() *TaskPerformanceScheduler {
	if globalTaskPerformanceScheduler == nil {
		globalTaskPerformanceScheduler = NewTaskPerformanceScheduler()
	}
	return globalTaskPerformanceScheduler
}
