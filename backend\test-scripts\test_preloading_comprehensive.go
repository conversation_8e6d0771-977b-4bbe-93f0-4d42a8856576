package main

import (
	"fmt"
	"log"
	"time"

	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/services"
)

func testPreloadingMain() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize database
	dbConfig := database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	}

	if err := database.Initialize(dbConfig); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Test preloading service
	preloadingService := services.NewPreloadingService(database.GetDB())

	fmt.Println("=== COMPREHENSIVE PRELOADING FUNCTIONALITY TEST ===")
	fmt.Println()

	// Test Document Defaults
	fmt.Println("1. Testing Document Defaults...")
	testDocumentDefaults(preloadingService)
	fmt.Println()

	// Test Regulation Defaults
	fmt.Println("2. Testing Regulation Defaults...")
	testRegulationDefaults(preloadingService)
	fmt.Println()

	// Test Agency Defaults
	fmt.Println("3. Testing Agency Defaults...")
	testAgencyDefaults(preloadingService)
	fmt.Println()

	// Test Category Defaults
	fmt.Println("4. Testing Category Defaults...")
	testCategoryDefaults(preloadingService)
	fmt.Println()

	// Test Proceeding Defaults
	fmt.Println("5. Testing Proceeding Defaults...")
	testProceedingDefaults(preloadingService)
	fmt.Println()

	// Test Finance Defaults
	fmt.Println("6. Testing Finance Defaults...")
	testFinanceDefaults(preloadingService)
	fmt.Println()

	// Test Slug Generation
	fmt.Println("7. Testing Slug Generation...")
	testSlugGeneration(preloadingService)
	fmt.Println()

	// Test API Endpoints
	fmt.Println("8. Testing API Endpoints...")
	testAPIEndpoints()
	fmt.Println()

	fmt.Println("=== ALL TESTS COMPLETED ===")
}

func testDocumentDefaults(service *services.PreloadingService) {
	// Test with no agency/category
	defaults, err := service.GetDocumentDefaults(nil, nil)
	if err != nil {
		fmt.Printf("❌ Error getting document defaults: %v\n", err)
		return
	}

	fmt.Printf("✅ Document defaults generated successfully\n")
	fmt.Printf("   - Publication Date: %v\n", defaults.PublicationDate)
	fmt.Printf("   - Effective Date: %v\n", defaults.EffectiveDate)
	fmt.Printf("   - Comment Due Date: %v\n", defaults.CommentDueDate)
	fmt.Printf("   - FR Document Number: %s\n", defaults.FRDocumentNumber)
	fmt.Printf("   - Language: %s\n", defaults.Language)
	fmt.Printf("   - Status: %s\n", defaults.Status)

	// Validate date logic
	if defaults.PublicationDate != nil && defaults.EffectiveDate != nil {
		pubDate := *defaults.PublicationDate
		effDate := *defaults.EffectiveDate
		daysDiff := int(effDate.Sub(pubDate).Hours() / 24)
		if daysDiff == 30 {
			fmt.Printf("✅ Date calculation correct: Effective date is 30 days after publication\n")
		} else {
			fmt.Printf("❌ Date calculation incorrect: Expected 30 days, got %d days\n", daysDiff)
		}
	}
}

func testRegulationDefaults(service *services.PreloadingService) {
	defaults, err := service.GetRegulationDefaults(nil, nil)
	if err != nil {
		fmt.Printf("❌ Error getting regulation defaults: %v\n", err)
		return
	}

	fmt.Printf("✅ Regulation defaults generated successfully\n")
	fmt.Printf("   - Enactment Date: %v\n", defaults.EnactmentDate)
	fmt.Printf("   - Publication Date: %v\n", defaults.PublicationDate)
	fmt.Printf("   - Effective Date: %v\n", defaults.EffectiveDate)
	fmt.Printf("   - Version Number: %s\n", defaults.VersionNumber)
	fmt.Printf("   - Status: %s\n", defaults.Status)
	fmt.Printf("   - Type: %s\n", defaults.Type)
	fmt.Printf("   - Hierarchy Level: %s\n", defaults.HierarchyLevel)
}

func testAgencyDefaults(service *services.PreloadingService) {
	defaults := service.GetAgencyDefaults()

	fmt.Printf("✅ Agency defaults generated successfully\n")
	fmt.Printf("   - Established At: %v\n", defaults.EstablishedAt)
	fmt.Printf("   - Country: %s\n", defaults.Country)
	fmt.Printf("   - Is Active: %t\n", defaults.IsActive)
	fmt.Printf("   - Agency Type: %s\n", defaults.AgencyType)
	fmt.Printf("   - Primary Color: %s\n", defaults.PrimaryColor)
	fmt.Printf("   - Secondary Color: %s\n", defaults.SecondaryColor)
}

func testCategoryDefaults(service *services.PreloadingService) {
	defaults, err := service.GetCategoryDefaults()
	if err != nil {
		fmt.Printf("❌ Error getting category defaults: %v\n", err)
		return
	}

	fmt.Printf("✅ Category defaults generated successfully\n")
	fmt.Printf("   - Color: %s\n", defaults.Color)
	fmt.Printf("   - Sort Order: %d\n", defaults.SortOrder)
	fmt.Printf("   - Is Active: %t\n", defaults.IsActive)
}

func testProceedingDefaults(service *services.PreloadingService) {
	testName := "Test Proceeding"
	defaults := service.GetProceedingDefaults(testName)

	fmt.Printf("✅ Proceeding defaults generated successfully\n")
	fmt.Printf("   - Initiation Date: %v\n", defaults.InitiationDate)
	fmt.Printf("   - Planned Start Date: %v\n", defaults.PlannedStartDate)
	fmt.Printf("   - Planned End Date: %v\n", defaults.PlannedEndDate)
	fmt.Printf("   - Unique ID: %s\n", defaults.UniqueID)
	fmt.Printf("   - Status: %s\n", defaults.Status)
	fmt.Printf("   - Priority: %s\n", defaults.Priority)
	fmt.Printf("   - Requires Mandatory Review: %t\n", defaults.RequiresMandatoryReview)
	fmt.Printf("   - Minimum Steps Required: %d\n", defaults.MinimumStepsRequired)

	// Validate unique ID format
	expectedPrefix := fmt.Sprintf("%s %s", testName, time.Now().Format("2006-01-02"))
	if defaults.UniqueID == expectedPrefix {
		fmt.Printf("✅ Unique ID format correct\n")
	} else {
		fmt.Printf("❌ Unique ID format incorrect: Expected '%s', got '%s'\n", expectedPrefix, defaults.UniqueID)
	}
}

func testFinanceDefaults(service *services.PreloadingService) {
	defaults := service.GetFinanceDefaults()

	fmt.Printf("✅ Finance defaults generated successfully\n")
	fmt.Printf("   - Year: %d\n", defaults.Year)
	fmt.Printf("   - Budget Type: %s\n", defaults.BudgetType)
	fmt.Printf("   - Performance Percentage: %.2f\n", defaults.PerformancePercentage)
	fmt.Printf("   - Is Auto Calculated: %t\n", defaults.IsAutoCalculated)

	// Validate current year
	currentYear := time.Now().Year()
	if defaults.Year == currentYear {
		fmt.Printf("✅ Year correctly set to current year\n")
	} else {
		fmt.Printf("❌ Year incorrect: Expected %d, got %d\n", currentYear, defaults.Year)
	}
}

func testSlugGeneration(service *services.PreloadingService) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"Environmental Protection Agency", "environmental-protection-agency"},
		{"Department of Health & Human Services", "department-of-health-human-services"},
		{"Test Category 123", "test-category-123"},
		{"Special Characters!@#$%", "special-characters"},
	}

	for _, tc := range testCases {
		result := service.GenerateSlug(tc.input)
		if result == tc.expected {
			fmt.Printf("✅ Slug generation correct: '%s' -> '%s'\n", tc.input, result)
		} else {
			fmt.Printf("❌ Slug generation incorrect: '%s' -> Expected '%s', got '%s'\n", tc.input, tc.expected, result)
		}
	}
}

func testAPIEndpoints() {
	baseURL := "http://127.0.0.1:8080/api/v1/preloading"

	// Note: This would require a running server and authentication token
	// For now, just print what would be tested
	endpoints := []string{
		"/documents",
		"/regulations",
		"/agencies",
		"/categories",
		"/proceedings",
		"/finances",
		"/slug?text=test",
		"/fr-number",
		"/docket-number?agency_id=1",
	}

	fmt.Printf("API endpoints to test (requires running server):\n")
	for _, endpoint := range endpoints {
		fmt.Printf("   - GET %s%s\n", baseURL, endpoint)
	}

	fmt.Printf("✅ API endpoint list generated\n")
}
