/**
 * Proceeding Validation Utilities
 * Implements the 7 key requirements for proceedings
 */

interface PrimaryStep {
  id: number;
  step_order: number;
  name: string;
  description: string;
  status: string;
  can_start_concurrently: boolean;
  previous_step_required: boolean;
  blocks_next_step: boolean;
  is_optional: boolean;
  is_critical: boolean;
  completion_criteria: string;
}

interface Proceeding {
  id: number;
  name: string;
  objective: string;
  status: string;
  prp_alignment: string;
  existing_directives_reviewed: boolean;
  primary_steps: PrimaryStep[];
}

/**
 * Validates that a proceeding meets the minimum complexity requirement (requirement 3)
 */
export const validateMinimumSteps = (steps: PrimaryStep[]): { valid: boolean; message?: string } => {
  if (steps.length < 5) {
    return {
      valid: false,
      message: `Proceeding must have at least 5 primary steps (requirement 3). Current count: ${steps.length}`
    };
  }
  return { valid: true };
};

/**
 * Validates sequential execution rules (requirement 2)
 */
export const validateSequentialExecution = (
  steps: PrimaryStep[],
  targetStepOrder: number,
  newStatus: string
): { valid: boolean; message?: string } => {
  // Only validate for status changes that start or complete steps
  if (newStatus !== 'in_progress' && newStatus !== 'completed') {
    return { valid: true };
  }

  const targetStep = steps.find(step => step.step_order === targetStepOrder);
  if (!targetStep) {
    return { valid: false, message: 'Step not found' };
  }

  // If this step doesn't require previous step completion, allow it
  if (!targetStep.previous_step_required) {
    return { valid: true };
  }

  // Check all previous steps
  for (let i = 1; i < targetStepOrder; i++) {
    const prevStep = steps.find(step => step.step_order === i);
    if (!prevStep) continue;

    // If previous step blocks next step and is not completed, reject
    if (prevStep.blocks_next_step && 
        prevStep.status !== 'completed' && 
        prevStep.status !== 'skipped') {
      return {
        valid: false,
        message: `Step ${i} must be completed before step ${targetStepOrder} can start (requirement 2: sequential execution)`
      };
    }
  }

  return { valid: true };
};

/**
 * Validates PRP alignment requirement (requirement 6)
 */
export const validatePRPAlignment = (proceeding: Partial<Proceeding>): { valid: boolean; message?: string } => {
  if (!proceeding.prp_alignment || proceeding.prp_alignment.trim() === '') {
    return {
      valid: false,
      message: 'PRP alignment description is required (requirement 6)'
    };
  }

  // Additional validation could include:
  // - Checking if referenced PRP sections exist
  // - Validating format of PRP section references
  // - Ensuring new PRP elements are properly defined

  return { valid: true };
};

/**
 * Validates existing directives review requirement (requirement 7)
 */
export const validateExistingDirectives = (proceeding: Partial<Proceeding>): { valid: boolean; message?: string } => {
  if (proceeding.status === 'active' && !proceeding.existing_directives_reviewed) {
    return {
      valid: false,
      message: 'Existing directives must be reviewed before proceeding activation (requirement 7)'
    };
  }

  return { valid: true };
};

/**
 * Validates that all critical steps are completed before proceeding completion
 */
export const validateCriticalStepsCompletion = (steps: PrimaryStep[]): { valid: boolean; message?: string } => {
  const incompleteCriticalSteps = steps.filter(step => 
    step.is_critical && 
    step.status !== 'completed' && 
    step.status !== 'skipped'
  );

  if (incompleteCriticalSteps.length > 0) {
    const stepNames = incompleteCriticalSteps.map(step => `"${step.name}"`).join(', ');
    return {
      valid: false,
      message: `All critical steps must be completed before proceeding can be completed. Incomplete critical steps: ${stepNames}`
    };
  }

  return { valid: true };
};

/**
 * Validates proceeding activation requirements
 */
export const validateProceedingActivation = (proceeding: Proceeding): { valid: boolean; messages: string[] } => {
  const messages: string[] = [];

  // Check minimum steps (requirement 3)
  const stepsValidation = validateMinimumSteps(proceeding.primary_steps);
  if (!stepsValidation.valid) {
    messages.push(stepsValidation.message!);
  }

  // Check PRP alignment (requirement 6)
  const prpValidation = validatePRPAlignment(proceeding);
  if (!prpValidation.valid) {
    messages.push(prpValidation.message!);
  }

  // Check existing directives (requirement 7)
  const directivesValidation = validateExistingDirectives(proceeding);
  if (!directivesValidation.valid) {
    messages.push(directivesValidation.message!);
  }

  return {
    valid: messages.length === 0,
    messages
  };
};

/**
 * Validates proceeding completion requirements
 */
export const validateProceedingCompletion = (proceeding: Proceeding): { valid: boolean; messages: string[] } => {
  const messages: string[] = [];

  // Check that all critical steps are completed
  const criticalStepsValidation = validateCriticalStepsCompletion(proceeding.primary_steps);
  if (!criticalStepsValidation.valid) {
    messages.push(criticalStepsValidation.message!);
  }

  // Check that at least 80% of non-optional steps are completed
  const nonOptionalSteps = proceeding.primary_steps.filter(step => !step.is_optional);
  const completedNonOptionalSteps = nonOptionalSteps.filter(step => 
    step.status === 'completed' || step.status === 'skipped'
  );
  
  const completionRate = nonOptionalSteps.length > 0 
    ? (completedNonOptionalSteps.length / nonOptionalSteps.length) * 100 
    : 100;

  if (completionRate < 80) {
    messages.push(`At least 80% of non-optional steps must be completed. Current completion rate: ${Math.round(completionRate)}%`);
  }

  return {
    valid: messages.length === 0,
    messages
  };
};

/**
 * Gets the next available step that can be started
 */
export const getNextAvailableStep = (steps: PrimaryStep[]): PrimaryStep | null => {
  // Sort steps by order
  const sortedSteps = [...steps].sort((a, b) => a.step_order - b.step_order);

  for (const step of sortedSteps) {
    // Skip if already started or completed
    if (step.status !== 'not_started') continue;

    // Check if this step can be started
    const validation = validateSequentialExecution(steps, step.step_order, 'in_progress');
    if (validation.valid) {
      return step;
    }
  }

  return null;
};

/**
 * Calculates proceeding progress
 */
export const calculateProgress = (steps: PrimaryStep[]): {
  totalSteps: number;
  completedSteps: number;
  progressPercent: number;
  currentStepOrder: number | null;
} => {
  const totalSteps = steps.length;
  const completedSteps = steps.filter(step => 
    step.status === 'completed' || step.status === 'skipped'
  ).length;
  
  const progressPercent = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;
  
  // Find current step (first non-completed step)
  const sortedSteps = [...steps].sort((a, b) => a.step_order - b.step_order);
  const currentStep = sortedSteps.find(step => 
    step.status === 'not_started' || step.status === 'in_progress' || step.status === 'blocked'
  );
  
  return {
    totalSteps,
    completedSteps,
    progressPercent,
    currentStepOrder: currentStep?.step_order || null
  };
};

/**
 * Validates step completion criteria
 */
export const validateStepCompletion = (step: PrimaryStep, evidence?: string): { valid: boolean; message?: string } => {
  if (!step.completion_criteria || step.completion_criteria.trim() === '') {
    return { valid: true }; // No specific criteria defined
  }

  // If completion criteria is defined but no evidence provided
  if (step.completion_criteria && (!evidence || evidence.trim() === '')) {
    return {
      valid: false,
      message: 'Completion evidence is required based on the defined completion criteria'
    };
  }

  return { valid: true };
};

/**
 * Gets step status display information
 */
export const getStepStatusInfo = (status: string): {
  label: string;
  color: string;
  icon: string;
  canTransitionTo: string[];
} => {
  const statusMap = {
    'not_started': {
      label: 'Not Started',
      color: 'gray',
      icon: 'clock',
      canTransitionTo: ['in_progress', 'skipped', 'cancelled']
    },
    'in_progress': {
      label: 'In Progress',
      color: 'blue',
      icon: 'play',
      canTransitionTo: ['completed', 'blocked', 'cancelled']
    },
    'completed': {
      label: 'Completed',
      color: 'green',
      icon: 'check',
      canTransitionTo: ['in_progress'] // Allow reopening if needed
    },
    'blocked': {
      label: 'Blocked',
      color: 'red',
      icon: 'exclamation',
      canTransitionTo: ['in_progress', 'cancelled']
    },
    'skipped': {
      label: 'Skipped',
      color: 'gray',
      icon: 'x',
      canTransitionTo: ['in_progress'] // Allow unskipping
    },
    'cancelled': {
      label: 'Cancelled',
      color: 'red',
      icon: 'x',
      canTransitionTo: ['not_started', 'in_progress']
    }
  };

  return statusMap[status as keyof typeof statusMap] || statusMap.not_started;
};

/**
 * Validates unique identification requirement (requirement 4)
 */
export const validateUniqueIdentification = (name: string, initiationDate: Date): string => {
  const formattedDate = initiationDate.toISOString().split('T')[0]; // YYYY-MM-DD format
  return `${name} ${formattedDate}`;
};
