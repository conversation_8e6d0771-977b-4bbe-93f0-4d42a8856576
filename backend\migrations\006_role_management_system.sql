-- Role Management System Migration
-- This migration creates a flexible role-based access control system

-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(150) NOT NULL,
    description TEXT,
    resource VARCHAR(50) NOT NULL, -- e.g., 'documents', 'agencies', 'users'
    action VARCHAR(50) NOT NULL,   -- e.g., 'create', 'read', 'update', 'delete'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);

-- Create user_roles junction table (many-to-many relationship)
CREATE TABLE IF NOT EXISTS user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by INTEGER REFERENCES users(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);

-- Insert system roles
INSERT INTO roles (name, display_name, description, is_system_role) VALUES
('admin', 'Administrator', 'Full system access with all permissions', TRUE),
('editor', 'Editor', 'Can create, edit, and manage documents and regulations', TRUE),
('viewer', 'Viewer', 'Read-only access to public and assigned content', TRUE),
('agency_manager', 'Agency Manager', 'Can manage agency information and agency-specific content', TRUE),
('category_manager', 'Category Manager', 'Can manage categories and category-specific content', TRUE),
('publisher', 'Publisher', 'Can publish and approve documents for public release', TRUE),
('reviewer', 'Reviewer', 'Can review and provide feedback on documents', TRUE)
ON CONFLICT (name) DO NOTHING;

-- Insert permissions
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
-- User management
('users.create', 'Create Users', 'Create new user accounts', 'users', 'create'),
('users.read', 'View Users', 'View user information', 'users', 'read'),
('users.update', 'Update Users', 'Edit user information', 'users', 'update'),
('users.delete', 'Delete Users', 'Delete user accounts', 'users', 'delete'),
('users.manage_roles', 'Manage User Roles', 'Assign and remove user roles', 'users', 'manage_roles'),

-- Document management
('documents.create', 'Create Documents', 'Create new documents', 'documents', 'create'),
('documents.read', 'View Documents', 'View documents', 'documents', 'read'),
('documents.update', 'Update Documents', 'Edit documents', 'documents', 'update'),
('documents.delete', 'Delete Documents', 'Delete documents', 'documents', 'delete'),
('documents.publish', 'Publish Documents', 'Publish documents for public access', 'documents', 'publish'),
('documents.approve', 'Approve Documents', 'Approve documents for publication', 'documents', 'approve'),
('documents.review', 'Review Documents', 'Review and comment on documents', 'documents', 'review'),

-- Agency management
('agencies.create', 'Create Agencies', 'Create new agencies', 'agencies', 'create'),
('agencies.read', 'View Agencies', 'View agency information', 'agencies', 'read'),
('agencies.update', 'Update Agencies', 'Edit agency information', 'agencies', 'update'),
('agencies.delete', 'Delete Agencies', 'Delete agencies', 'agencies', 'delete'),
('agencies.manage', 'Manage Agency Content', 'Manage agency-specific content', 'agencies', 'manage'),

-- Category management
('categories.create', 'Create Categories', 'Create new categories', 'categories', 'create'),
('categories.read', 'View Categories', 'View categories', 'categories', 'read'),
('categories.update', 'Update Categories', 'Edit categories', 'categories', 'update'),
('categories.delete', 'Delete Categories', 'Delete categories', 'categories', 'delete'),
('categories.manage', 'Manage Category Content', 'Manage category-specific content', 'categories', 'manage'),

-- Regulation management
('regulations.create', 'Create Regulations', 'Create new regulations', 'regulations', 'create'),
('regulations.read', 'View Regulations', 'View regulations', 'regulations', 'read'),
('regulations.update', 'Update Regulations', 'Edit regulations', 'regulations', 'update'),
('regulations.delete', 'Delete Regulations', 'Delete regulations', 'regulations', 'delete'),
('regulations.publish', 'Publish Regulations', 'Publish regulation versions', 'regulations', 'publish'),
('regulations.amend', 'Amend Regulations', 'Create amendments to regulations', 'regulations', 'amend'),

-- System management
('system.admin', 'System Administration', 'Full system administration access', 'system', 'admin'),
('system.analytics', 'View Analytics', 'Access system analytics and reports', 'system', 'analytics'),
('system.settings', 'Manage Settings', 'Manage system settings', 'system', 'settings')
ON CONFLICT (name) DO NOTHING;

-- Assign permissions to roles
-- Admin role gets all permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Editor role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'editor' AND p.name IN (
    'documents.create', 'documents.read', 'documents.update', 'documents.review',
    'regulations.create', 'regulations.read', 'regulations.update', 'regulations.amend',
    'agencies.read', 'categories.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Viewer role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'viewer' AND p.name IN (
    'documents.read', 'regulations.read', 'agencies.read', 'categories.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Agency Manager role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'agency_manager' AND p.name IN (
    'agencies.create', 'agencies.read', 'agencies.update', 'agencies.manage',
    'documents.create', 'documents.read', 'documents.update',
    'regulations.create', 'regulations.read', 'regulations.update'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Category Manager role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'category_manager' AND p.name IN (
    'categories.create', 'categories.read', 'categories.update', 'categories.manage',
    'documents.create', 'documents.read', 'documents.update'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Publisher role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'publisher' AND p.name IN (
    'documents.read', 'documents.publish', 'documents.approve',
    'regulations.read', 'regulations.publish'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Reviewer role permissions
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'reviewer' AND p.name IN (
    'documents.read', 'documents.review', 'regulations.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Create default admin user if not exists
DO $$
DECLARE
    admin_user_id INTEGER;
    admin_role_id INTEGER;
BEGIN
    -- Check if admin user exists
    SELECT id INTO admin_user_id FROM users WHERE email = '<EMAIL>' LIMIT 1;
    
    -- Create admin user if not exists
    IF admin_user_id IS NULL THEN
        INSERT INTO users (username, email, first_name, last_name, role, is_active, is_verified, password_hash)
        VALUES ('admin', '<EMAIL>', 'System', 'Administrator', 'admin', TRUE, TRUE, '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi') -- password: password
        RETURNING id INTO admin_user_id;
    END IF;
    
    -- Get admin role ID
    SELECT id INTO admin_role_id FROM roles WHERE name = 'admin' LIMIT 1;
    
    -- Assign admin role to admin user
    IF admin_role_id IS NOT NULL AND admin_user_id IS NOT NULL THEN
        INSERT INTO user_roles (user_id, role_id, assigned_by)
        VALUES (admin_user_id, admin_role_id, admin_user_id)
        ON CONFLICT (user_id, role_id) DO NOTHING;
    END IF;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_active ON user_roles(is_active);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_permissions_resource_action ON permissions(resource, action);
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);
CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(name);

-- Add triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_roles_updated_at BEFORE UPDATE ON user_roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
