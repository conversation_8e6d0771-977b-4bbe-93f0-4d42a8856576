import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

interface MarkdownRendererProps {
  content: string | any; // Allow any type, will be converted to string safely
  className?: string;
  fallbackText?: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  className = "prose prose-lg max-w-none",
  fallbackText = "No content available"
}) => {
  // Ensure content is a string and handle various data types
  const safeContent = React.useMemo(() => {
    if (!content) return '';

    // If content is already a string, use it
    if (typeof content === 'string') {
      return content.trim();
    }

    // If content is an object, try to stringify it
    if (typeof content === 'object') {
      try {
        return JSON.stringify(content, null, 2);
      } catch {
        return String(content);
      }
    }

    // For any other type, convert to string
    return String(content).trim();
  }, [content]);

  if (!safeContent) {
    return (
      <div className="text-gray-500 italic">
        {fallbackText}
      </div>
    );
  }

  try {
    return (
      <div className={className}>
        <ReactMarkdown remarkPlugins={[remarkGfm]}>
          {safeContent}
        </ReactMarkdown>
      </div>
    );
  } catch (error) {
    console.error('MarkdownRenderer error:', error);
    // Fallback to plain text if markdown parsing fails
    return (
      <div className={className}>
        <pre className="whitespace-pre-wrap">{safeContent}</pre>
      </div>
    );
  }
};

export default MarkdownRenderer;
