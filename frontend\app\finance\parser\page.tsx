'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../stores/authStore';
import Layout from '../../components/Layout/Layout';
import IntelligentFinanceParser from '../../components/Finance/IntelligentFinanceParser';
import financeApi from '../../services/financeApi';
import apiService from '../../services/api';
import {
  CreateFinanceRequest,
  Document,
  LawsAndRules
} from '../../types';
import {
  DocumentTextIcon,
  ScaleIcon,
  SparklesIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

const FinanceParserPage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [selectedSource, setSelectedSource] = useState<{
    type: 'document' | 'regulation';
    id: number;
    title: string;
    content: string;
  } | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [regulations, setRegulations] = useState<LawsAndRules[]>([]);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const currentYear = new Date().getFullYear();

  useEffect(() => {
    if (isAuthenticated) {
      fetchSources();
    }
  }, [isAuthenticated]);

  const fetchSources = async () => {
    try {
      setLoading(true);
      const [documentsData, regulationsData] = await Promise.all([
        apiService.getDocuments({ per_page: 50 }),
        apiService.get('/public/regulations?per_page=50')
      ]);

      setDocuments(documentsData.data);
      setRegulations((regulationsData as any)?.data?.data || []);
    } catch (err) {
      console.error('Failed to fetch sources:', err);
      setError('Failed to load documents and regulations');
    } finally {
      setLoading(false);
    }
  };

  const handleSourceSelect = (type: 'document' | 'regulation', item: Document | LawsAndRules) => {
    setSelectedSource({
      type,
      id: item.id,
      title: item.title,
      content: item.content || ''
    });
    setSuccess(null);
    setError(null);
  };

  const handleFinancesExtracted = async (finances: CreateFinanceRequest[]) => {
    try {
      setLoading(true);
      setError(null);
      
      // Create all finance entries
      const promises = finances.map(finance => financeApi.createFinance(finance));
      await Promise.all(promises);
      
      setSuccess(`Successfully created ${finances.length} finance entries from parsed content!`);
      
      // Clear selection after successful creation
      setTimeout(() => {
        setSelectedSource(null);
        setSuccess(null);
      }, 3000);
      
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create finance entries');
    } finally {
      setLoading(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600">Please log in to access the finance parser.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Intelligent Finance Parser
          </h1>
          <p className="text-gray-600">
            Automatically extract financial information from documents and regulations using AI-powered parsing.
          </p>
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-red-800">{error}</div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Source Selection */}
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                Select Source to Parse
              </h2>
              
              {/* Documents */}
              <div className="mb-6">
                <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                  <DocumentTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                  Documents
                </h3>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {documents.map(doc => (
                    <button
                      key={`doc-${doc.id}`}
                      onClick={() => handleSourceSelect('document', doc)}
                      className={`w-full text-left p-3 border rounded-lg hover:bg-gray-50 transition-colors ${
                        selectedSource?.type === 'document' && selectedSource?.id === doc.id
                          ? 'border-blue-300 bg-blue-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="font-medium text-gray-900 truncate">
                        {doc.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {doc.type} • {doc.agency ? (typeof doc.agency === 'string' ? doc.agency : doc.agency.name) : 'No agency'}
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Regulations */}
              <div>
                <h3 className="text-md font-medium text-gray-700 mb-3 flex items-center">
                  <ScaleIcon className="h-5 w-5 text-purple-500 mr-2" />
                  Regulations
                </h3>
                <div className="space-y-2 max-h-64 overflow-y-auto">
                  {regulations.map(reg => (
                    <button
                      key={`reg-${reg.id}`}
                      onClick={() => handleSourceSelect('regulation', reg)}
                      className={`w-full text-left p-3 border rounded-lg hover:bg-gray-50 transition-colors ${
                        selectedSource?.type === 'regulation' && selectedSource?.id === reg.id
                          ? 'border-purple-300 bg-purple-50'
                          : 'border-gray-200'
                      }`}
                    >
                      <div className="font-medium text-gray-900 truncate">
                        {reg.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {reg.type} • {reg.agency?.name || 'No agency'}
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Selected Source Preview */}
            {selectedSource && (
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-3">
                  Selected Source
                </h3>
                <div className="flex items-center mb-3">
                  {selectedSource.type === 'document' ? (
                    <DocumentTextIcon className="h-5 w-5 text-blue-500 mr-2" />
                  ) : (
                    <ScaleIcon className="h-5 w-5 text-purple-500 mr-2" />
                  )}
                  <span className="font-medium text-gray-900">
                    {selectedSource.title}
                  </span>
                </div>
                <div className="text-sm text-gray-600 bg-gray-50 rounded p-3 max-h-32 overflow-y-auto">
                  {selectedSource.content ? (
                    selectedSource.content.substring(0, 500) + 
                    (selectedSource.content.length > 500 ? '...' : '')
                  ) : (
                    <span className="italic">No content available</span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Parser Interface */}
          <div>
            {selectedSource ? (
              <IntelligentFinanceParser
                content={selectedSource.content}
                sourceType={selectedSource.type}
                sourceId={selectedSource.id}
                year={currentYear}
                onFinancesExtracted={handleFinancesExtracted}
              />
            ) : (
              <div className="bg-white shadow rounded-lg p-6">
                <div className="text-center py-12">
                  <SparklesIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    No source selected
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Select a document or regulation from the left panel to start parsing financial content.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-blue-900 mb-3">
            How the Intelligent Finance Parser Works
          </h3>
          <div className="text-sm text-blue-800 space-y-2">
            <p>
              • <strong>Pattern Recognition:</strong> Uses advanced regex patterns to identify financial amounts, budget allocations, costs, and expenses.
            </p>
            <p>
              • <strong>Context Analysis:</strong> Analyzes surrounding text to determine confidence levels and suggest appropriate categories.
            </p>
            <p>
              • <strong>Smart Categorization:</strong> Automatically suggests finance categories based on keywords (Research, Compliance, Infrastructure, etc.).
            </p>
            <p>
              • <strong>Confidence Scoring:</strong> Each extracted amount receives a confidence score based on context and pattern matching.
            </p>
            <p>
              • <strong>Batch Creation:</strong> Select multiple extracted amounts to create finance entries in bulk.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FinanceParserPage;
