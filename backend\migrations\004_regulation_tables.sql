-- Regulation Tables Migration
-- Creates all tables needed for the hierarchical regulation system

-- Create laws_and_rules table (main regulation table)
CREATE TABLE laws_and_rules (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Basic information
    title TEXT NOT NULL,
    short_title TEXT,
    type TEXT NOT NULL CHECK (type IN ('law', 'rule', 'regulation', 'code')),
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'under_review', 'approved', 'published', 'effective', 'terminated', 'archived')),
    current_document_version_id BIGINT,
    
    -- Legal identifiers
    public_law_number TEXT,
    regulatory_identifier TEXT, -- RIN
    cfr_title TEXT,
    usc_title TEXT,
    docket_number TEXT,
    
    -- Dates
    enactment_date TIMESTAMPTZ,
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    publication_date TIMESTAMPTZ,
    
    -- Relationships
    agency_id BIGINT NOT NULL REFERENCES agencies(id),
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    
    -- Metadata
    description TEXT,
    notes TEXT,
    is_significant BOOLEAN DEFAULT false
);

-- Create regulation_document_versions table
CREATE TABLE regulation_document_versions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Version information
    law_rule_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    version_number TEXT NOT NULL,
    
    -- Dates
    publication_date TIMESTAMPTZ,
    effective_date TIMESTAMPTZ,
    
    -- Status and metadata
    is_official BOOLEAN DEFAULT false,
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    notes TEXT,
    summary_of_changes TEXT
);

-- Create regulation_chunks table (hierarchical content structure)
CREATE TABLE regulation_chunks (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Hierarchy
    law_rule_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    parent_chunk_id BIGINT REFERENCES regulation_chunks(id),
    order_in_parent INTEGER DEFAULT 0,
    chunk_type TEXT NOT NULL CHECK (chunk_type IN ('title', 'division', 'chapter', 'subtitle', 'section', 'subsection', 'paragraph', 'clause', 'subclause')),
    
    -- Identification
    chunk_identifier TEXT NOT NULL UNIQUE,
    number TEXT,
    title TEXT,
    
    -- Current content version
    current_chunk_content_version_id BIGINT
);

-- Create regulation_chunk_content_versions table
CREATE TABLE regulation_chunk_content_versions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Content
    chunk_id BIGINT NOT NULL REFERENCES regulation_chunks(id) ON DELETE CASCADE,
    content TEXT,
    version_number INTEGER NOT NULL,
    is_current BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    
    -- Metadata
    modified_by_id BIGINT NOT NULL REFERENCES users(id),
    change_description TEXT
);

-- Create regulation_document_version_chunk_maps table
CREATE TABLE regulation_document_version_chunk_maps (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    document_version_id BIGINT NOT NULL REFERENCES regulation_document_versions(id) ON DELETE CASCADE,
    chunk_id BIGINT NOT NULL REFERENCES regulation_chunks(id) ON DELETE CASCADE,
    chunk_content_version_id BIGINT NOT NULL REFERENCES regulation_chunk_content_versions(id) ON DELETE CASCADE,
    
    UNIQUE(document_version_id, chunk_id)
);

-- Create regulation_relationships table
CREATE TABLE regulation_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Source (the regulation that creates the relationship)
    source_law_rule_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    source_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Target (the regulation being referenced)
    target_law_rule_id BIGINT NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    target_chunk_id BIGINT REFERENCES regulation_chunks(id),
    
    -- Relationship details
    relationship_type TEXT NOT NULL CHECK (relationship_type IN ('amends', 'repeals', 'refers_to', 'supersedes', 'implements')),
    effective_date_of_relationship TIMESTAMPTZ,
    description TEXT,
    
    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id)
);

-- Add foreign key constraint for current_document_version_id
ALTER TABLE laws_and_rules 
ADD CONSTRAINT fk_laws_and_rules_current_document_version 
FOREIGN KEY (current_document_version_id) REFERENCES regulation_document_versions(id);

-- Add foreign key constraint for current_chunk_content_version_id
ALTER TABLE regulation_chunks 
ADD CONSTRAINT fk_regulation_chunks_current_content_version 
FOREIGN KEY (current_chunk_content_version_id) REFERENCES regulation_chunk_content_versions(id);

-- Create indexes for better performance
CREATE INDEX idx_laws_and_rules_agency_id ON laws_and_rules(agency_id);
CREATE INDEX idx_laws_and_rules_status ON laws_and_rules(status);
CREATE INDEX idx_laws_and_rules_type ON laws_and_rules(type);
CREATE INDEX idx_laws_and_rules_effective_date ON laws_and_rules(effective_date);
CREATE INDEX idx_laws_and_rules_cfr_title ON laws_and_rules(cfr_title);

CREATE INDEX idx_regulation_document_versions_law_rule_id ON regulation_document_versions(law_rule_id);
CREATE INDEX idx_regulation_document_versions_version_number ON regulation_document_versions(version_number);
CREATE INDEX idx_regulation_document_versions_is_official ON regulation_document_versions(is_official);

CREATE INDEX idx_regulation_chunks_law_rule_id ON regulation_chunks(law_rule_id);
CREATE INDEX idx_regulation_chunks_parent_chunk_id ON regulation_chunks(parent_chunk_id);
CREATE INDEX idx_regulation_chunks_chunk_type ON regulation_chunks(chunk_type);
CREATE INDEX idx_regulation_chunks_chunk_identifier ON regulation_chunks(chunk_identifier);

CREATE INDEX idx_regulation_chunk_content_versions_chunk_id ON regulation_chunk_content_versions(chunk_id);
CREATE INDEX idx_regulation_chunk_content_versions_is_current ON regulation_chunk_content_versions(is_current);

CREATE INDEX idx_regulation_relationships_source_law_rule_id ON regulation_relationships(source_law_rule_id);
CREATE INDEX idx_regulation_relationships_target_law_rule_id ON regulation_relationships(target_law_rule_id);
CREATE INDEX idx_regulation_relationships_relationship_type ON regulation_relationships(relationship_type);
