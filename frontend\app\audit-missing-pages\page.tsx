'use client';

import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout/Layout';

interface PageCheck {
  path: string;
  category: string;
  exists: boolean;
  status?: number;
  error?: string;
  description: string;
}

const MissingPagesAuditPage: React.FC = () => {
  const [pageChecks, setPageChecks] = useState<PageCheck[]>([]);
  const [isChecking, setIsChecking] = useState(false);
  const [summary, setSummary] = useState({ total: 0, existing: 0, missing: 0 });

  // Comprehensive list of all pages that should exist based on navigation and backend APIs
  const expectedPages: Omit<PageCheck, 'exists' | 'status' | 'error'>[] = [
    // Core Pages
    { path: '/', category: 'Core', description: 'Home page' },
    { path: '/about', category: 'Core', description: 'About page' },
    { path: '/contact', category: 'Core', description: 'Contact page' },
    { path: '/help', category: 'Core', description: 'Help page' },
    { path: '/privacy', category: 'Core', description: 'Privacy policy' },
    { path: '/features', category: 'Core', description: 'Features page' },
    { path: '/search', category: 'Core', description: 'Search page' },
    
    // Authentication Pages
    { path: '/login', category: 'Auth', description: 'Login page' },
    { path: '/register', category: 'Auth', description: 'Registration page' },
    { path: '/forgot-password', category: 'Auth', description: 'Forgot password page' },
    { path: '/reset-password', category: 'Auth', description: 'Reset password page' },
    { path: '/verify-email', category: 'Auth', description: 'Email verification page' },
    
    // User Pages
    { path: '/dashboard', category: 'User', description: 'User dashboard' },
    { path: '/profile', category: 'User', description: 'User profile' },
    { path: '/settings', category: 'User', description: 'User settings' },
    
    // Document Management
    { path: '/documents', category: 'Documents', description: 'Documents listing' },
    { path: '/documents/new', category: 'Documents', description: 'Create new document' },
    { path: '/documents/[id]', category: 'Documents', description: 'View document details' },
    { path: '/documents/[id]/edit', category: 'Documents', description: 'Edit document' },
    
    // Regulations
    { path: '/regulations', category: 'Regulations', description: 'Regulations listing' },
    { path: '/regulations/new', category: 'Regulations', description: 'Create new regulation' },
    { path: '/regulations/[id]', category: 'Regulations', description: 'View regulation details' },
    { path: '/regulations/[id]/edit', category: 'Regulations', description: 'Edit regulation' },
    { path: '/regulations/full', category: 'Regulations', description: 'Full regulation view' },
    
    // Agencies
    { path: '/agencies', category: 'Agencies', description: 'Agencies listing' },
    { path: '/agencies/new', category: 'Agencies', description: 'Create new agency' },
    { path: '/agencies/[id]', category: 'Agencies', description: 'View agency details' },
    { path: '/agencies/[id]/edit', category: 'Agencies', description: 'Edit agency' },
    
    // Categories
    { path: '/categories', category: 'Categories', description: 'Categories listing' },
    { path: '/categories/new', category: 'Categories', description: 'Create new category' },
    { path: '/categories/[id]', category: 'Categories', description: 'View category details' },
    { path: '/categories/[id]/edit', category: 'Categories', description: 'Edit category' },
    
    // Tasks
    { path: '/tasks', category: 'Tasks', description: 'Tasks listing' },
    { path: '/tasks/new', category: 'Tasks', description: 'Create new task' },
    { path: '/tasks/[id]', category: 'Tasks', description: 'View task details' },
    { path: '/tasks/[id]/edit', category: 'Tasks', description: 'Edit task' },
    
    // Proceedings
    { path: '/proceedings', category: 'Proceedings', description: 'Proceedings listing' },
    { path: '/proceedings/new', category: 'Proceedings', description: 'Create new proceeding' },
    { path: '/proceedings/[id]', category: 'Proceedings', description: 'View proceeding details' },
    { path: '/proceedings/[id]/edit', category: 'Proceedings', description: 'Edit proceeding' },
    
    // Calendar
    { path: '/calendar', category: 'Calendar', description: 'Calendar view' },
    { path: '/calendar/new', category: 'Calendar', description: 'Create calendar event' },
    { path: '/calendar/[id]', category: 'Calendar', description: 'View calendar event' },
    { path: '/calendar/[id]/edit', category: 'Calendar', description: 'Edit calendar event' },
    
    // Summary
    { path: '/summary', category: 'Summary', description: 'Summary listing' },
    { path: '/summary/new', category: 'Summary', description: 'Create new summary' },
    { path: '/summary/[id]', category: 'Summary', description: 'View summary details' },
    { path: '/summary/[id]/edit', category: 'Summary', description: 'Edit summary' },
    
    // Finance
    { path: '/finance', category: 'Finance', description: 'Finance listing' },
    { path: '/finance/new', category: 'Finance', description: 'Create new finance entry' },
    { path: '/finance/[id]', category: 'Finance', description: 'View finance details' },
    { path: '/finance/[id]/edit', category: 'Finance', description: 'Edit finance entry' },
    { path: '/finance/parser', category: 'Finance', description: 'Finance parser tool' },
    
    // Admin Pages
    { path: '/admin', category: 'Admin', description: 'Admin dashboard' },
    { path: '/admin/users', category: 'Admin', description: 'User management' },
    { path: '/admin/roles', category: 'Admin', description: 'Role management' },
    { path: '/admin/agencies', category: 'Admin', description: 'Agency management' },
    { path: '/admin/categories', category: 'Admin', description: 'Category management' },
    { path: '/admin/documents', category: 'Admin', description: 'Document management' },
    { path: '/admin/regulations', category: 'Admin', description: 'Regulation management' },
    { path: '/admin/proceedings', category: 'Admin', description: 'Proceeding management' },
    { path: '/admin/analytics', category: 'Admin', description: 'Analytics dashboard' },
    { path: '/admin/system', category: 'Admin', description: 'System management' },
    { path: '/admin/signatures', category: 'Admin', description: 'Digital signature management' },
    { path: '/admin/retention', category: 'Admin', description: 'Retention policy management' },
    { path: '/admin/processing', category: 'Admin', description: 'Document processing management' },
    
    // Enterprise Pages
    { path: '/enterprise', category: 'Enterprise', description: 'Enterprise dashboard' },
    { path: '/enterprise/content', category: 'Enterprise', description: 'Content management' },
    { path: '/enterprise/financial', category: 'Enterprise', description: 'Financial management' },
    { path: '/enterprise/compliance', category: 'Enterprise', description: 'Compliance & risk management' },
    { path: '/enterprise/bi', category: 'Enterprise', description: 'Business intelligence' },
    { path: '/enterprise/hr', category: 'Enterprise', description: 'Human resources' },
    { path: '/enterprise/test', category: 'Enterprise', description: 'Enterprise testing' },
    { path: '/enterprise/validation', category: 'Enterprise', description: 'Enterprise validation' },
    { path: '/enterprise/integration-test', category: 'Enterprise', description: 'Enterprise integration test' },
    
    // Testing Pages
    { path: '/test-frontend', category: 'Testing', description: 'Frontend testing page' },
    { path: '/test-performance', category: 'Testing', description: 'Performance testing page' },
    { path: '/test-token', category: 'Testing', description: 'Token testing page' },
    { path: '/test-comprehensive-api', category: 'Testing', description: 'Comprehensive API testing page' },
    { path: '/audit-missing-pages', category: 'Testing', description: 'Missing pages audit (this page)' },
    
    // Error Pages
    { path: '/forbidden', category: 'Error', description: 'Forbidden access page' },
    { path: '/unauthorized', category: 'Error', description: 'Unauthorized access page' },
    { path: '/maintenance', category: 'Error', description: 'Maintenance page' },
    { path: '/offline', category: 'Error', description: 'Offline page' },
    { path: '/status', category: 'Error', description: 'Status page' },
    
    // Debug Pages
    { path: '/debug-auth', category: 'Debug', description: 'Authentication debugging' },
  ];

  const checkPageExists = async (path: string): Promise<{ exists: boolean; status?: number; error?: string }> => {
    try {
      // For dynamic routes, we'll check if the directory structure exists
      if (path.includes('[id]')) {
        const basePath = path.replace('/[id]', '');
        const response = await fetch(basePath);
        return { exists: response.status !== 404, status: response.status };
      }
      
      const response = await fetch(path);
      return { exists: response.status !== 404, status: response.status };
    } catch (error: any) {
      return { exists: false, error: error.message };
    }
  };

  const runPageAudit = async () => {
    setIsChecking(true);
    setPageChecks([]);
    
    const results: PageCheck[] = [];
    
    for (const page of expectedPages) {
      const result = await checkPageExists(page.path);
      results.push({
        ...page,
        exists: result.exists,
        status: result.status,
        error: result.error
      });
    }
    
    setPageChecks(results);
    
    const total = results.length;
    const existing = results.filter(r => r.exists).length;
    const missing = total - existing;
    
    setSummary({ total, existing, missing });
    setIsChecking(false);
  };

  useEffect(() => {
    runPageAudit();
  }, []);

  const groupedPages = pageChecks.reduce((acc, page) => {
    if (!acc[page.category]) {
      acc[page.category] = [];
    }
    acc[page.category].push(page);
    return acc;
  }, {} as Record<string, PageCheck[]>);

  const getStatusColor = (exists: boolean, status?: number) => {
    if (!exists) return 'text-red-600';
    if (status === 200) return 'text-green-600';
    if (status && status >= 300 && status < 400) return 'text-yellow-600';
    return 'text-blue-600';
  };

  const getStatusIcon = (exists: boolean, status?: number) => {
    if (!exists) return '❌';
    if (status === 200) return '✅';
    if (status && status >= 300 && status < 400) return '⚠️';
    return '🔍';
  };

  return (
    <Layout title="Missing Pages Audit" requireAuth={true}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Missing Pages Audit
          </h1>

          {/* Summary */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Summary</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{summary.total}</div>
                <div className="text-sm text-blue-800">Total Expected Pages</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{summary.existing}</div>
                <div className="text-sm text-green-800">Existing Pages</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{summary.missing}</div>
                <div className="text-sm text-red-800">Missing Pages</div>
              </div>
            </div>
            
            <div className="mt-4">
              <button
                onClick={runPageAudit}
                disabled={isChecking}
                className={`px-4 py-2 rounded-md font-medium ${
                  isChecking
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                } text-white transition-colors`}
              >
                {isChecking ? 'Checking Pages...' : 'Re-run Audit'}
              </button>
            </div>
          </div>

          {/* Results by Category */}
          {Object.entries(groupedPages).map(([category, pages]) => (
            <div key={category} className="bg-white rounded-lg shadow-md mb-6">
              <div className="px-6 py-4 bg-gray-50 border-b">
                <h3 className="text-lg font-semibold text-gray-900">
                  {category} ({pages.filter(p => p.exists).length}/{pages.length})
                </h3>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {pages.map((page, index) => (
                    <div
                      key={index}
                      className={`p-4 rounded-lg border-2 ${
                        page.exists
                          ? 'border-green-200 bg-green-50'
                          : 'border-red-200 bg-red-50'
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center">
                            <span className="mr-2">
                              {getStatusIcon(page.exists, page.status)}
                            </span>
                            <code className="text-sm font-mono text-gray-800">
                              {page.path}
                            </code>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {page.description}
                          </p>
                          {page.status && (
                            <p className={`text-xs mt-1 ${getStatusColor(page.exists, page.status)}`}>
                              Status: {page.status}
                            </p>
                          )}
                          {page.error && (
                            <p className="text-xs text-red-600 mt-1">
                              Error: {page.error}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}

          {/* Instructions */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Next Steps
            </h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• ✅ Green pages exist and are accessible</li>
              <li>• ❌ Red pages are missing and need to be created</li>
              <li>• ⚠️ Yellow pages exist but may have issues (redirects, etc.)</li>
              <li>• Focus on creating missing pages in Core, Documents, and Admin categories first</li>
              <li>• Dynamic routes [id] are checked by testing their base paths</li>
              <li>• Enterprise and Testing pages can be created later as needed</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default MissingPagesAuditPage;
