'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { hrApi } from '../../../../services/enterpriseApi';
import { Position } from '../../../../types/enterprise';

const PositionViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const positionId = parseInt(params.id as string);
  
  const [position, setPosition] = useState<Position | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (positionId) {
      fetchPosition();
    }
  }, [positionId]);

  const fetchPosition = async () => {
    try {
      setLoading(true);
      const response = await hrApi.getPosition(positionId);
      setPosition(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch position');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this position?')) return;
    
    try {
      await hrApi.deletePosition(positionId);
      router.push('/enterprise/hr/positions');
    } catch (err: any) {
      setError(err.message || 'Failed to delete position');
    }
  };

  const handleApprove = async () => {
    try {
      await hrApi.approvePosition(positionId);
      await fetchPosition(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Failed to approve position');
    }
  };

  if (loading) return <div className="p-6">Loading position...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!position) return <div className="p-6">Position not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Position Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/hr/positions')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Positions
          </button>
          <button
            onClick={() => router.push(`/enterprise/hr/positions/${positionId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Position
          </button>
          {!position.is_approved && (
            <button
              onClick={handleApprove}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Approve Position
            </button>
          )}
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Position
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{position.position_title}</h2>
              <p className="text-sm text-gray-600">Code: {position.position_code}</p>
              <p className="text-sm text-gray-600">Department: {position.department?.department_name || 'N/A'}</p>
            </div>
            <div className="text-right">
              <div className="flex flex-col space-y-2">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  position.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {position.is_active ? 'Active' : 'Inactive'}
                </span>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  position.is_approved 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {position.is_approved ? 'Approved' : 'Pending Approval'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Position Overview */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Position Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 capitalize">
                {position.level || 'N/A'}
              </div>
              <div className="text-sm text-blue-600">Level</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {position.currency} {position.min_salary.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">Min Salary</div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {position.currency} {position.max_salary.toLocaleString()}
              </div>
              <div className="text-sm text-orange-600">Max Salary</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {position.current_count} / {position.headcount_limit}
              </div>
              <div className="text-sm text-purple-600">Headcount</div>
            </div>
          </div>
        </div>

        {/* Position Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Position Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Grade</label>
              <p className="mt-1 text-sm text-gray-900">{position.grade || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Job Family</label>
              <p className="mt-1 text-sm text-gray-900">{position.job_family || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Reports To ID</label>
              <p className="mt-1 text-sm text-gray-900">{position.reports_to_id || 'N/A'}</p>
            </div>
          </div>

          {position.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {position.description}
              </div>
            </div>
          )}
        </div>

        {/* Requirements and Qualifications */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Requirements and Qualifications</h3>
          
          {position.requirements && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Requirements</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {position.requirements}
              </div>
            </div>
          )}

          {position.qualifications && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Qualifications</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {position.qualifications}
              </div>
            </div>
          )}

          {position.skills && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700">Skills</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {position.skills}
              </div>
            </div>
          )}
        </div>

        {/* Salary Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Salary Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Salary Range</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {position.currency} {position.min_salary.toLocaleString()} - {position.max_salary.toLocaleString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Midpoint</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {position.currency} {((position.min_salary + position.max_salary) / 2).toLocaleString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Range Spread</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {position.max_salary > 0 ? 
                  `${(((position.max_salary - position.min_salary) / position.min_salary) * 100).toFixed(1)}%` : 
                  'N/A'
                }
              </p>
            </div>
          </div>
        </div>

        {/* Headcount Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Headcount Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Headcount Limit</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">{position.headcount_limit}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Current Count</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">{position.current_count}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Available Positions</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {position.headcount_limit - position.current_count}
              </p>
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700">Utilization</label>
            <div className="mt-2">
              <div className="bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ 
                    width: `${position.headcount_limit > 0 ? 
                      Math.min((position.current_count / position.headcount_limit) * 100, 100) : 
                      0}%` 
                  }}
                ></div>
              </div>
              <p className="text-sm text-gray-600 mt-1">
                {position.headcount_limit > 0 ? 
                  `${((position.current_count / position.headcount_limit) * 100).toFixed(1)}%` : 
                  '0%'
                } utilized
              </p>
            </div>
          </div>
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(position.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(position.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PositionViewPage;
