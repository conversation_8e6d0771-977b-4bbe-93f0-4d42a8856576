package tests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

type TaskStatusTrackingTestSuite struct {
	suite.Suite
	db                     *gorm.DB
	taskPerformanceService *services.TaskPerformanceService
	taskStatusTracker      *services.TaskStatusTracker
	testTask               *models.Task
	testUser               *models.User
}

func TestTaskStatusTrackingTestSuite(t *testing.T) {
	suite.Run(t, new(TaskStatusTrackingTestSuite))
}

func (suite *TaskStatusTrackingTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Set global database
	database.DB = db
	suite.db = db

	// Run migrations
	err = db.AutoMigrate(
		&models.User{},
		&models.Task{},
		&services.TaskStatusHistoryEntry{},
	)
	suite.Require().NoError(err)

	// Initialize services
	suite.taskPerformanceService = services.NewTaskPerformanceService()
	suite.taskStatusTracker = services.NewTaskStatusTracker(db)

	// Create test data
	suite.createTestData()
}

func (suite *TaskStatusTrackingTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *TaskStatusTrackingTestSuite) createTestData() {
	// Create test user
	suite.testUser = &models.User{
		Email:    "<EMAIL>",
		Username: "testuser",
		Role:     models.RoleEditor,
	}
	suite.db.Create(suite.testUser)

	// Create test task
	dueDate := time.Now().Add(24 * time.Hour)
	suite.testTask = &models.Task{
		Title:       "Test Task for Status Tracking",
		Description: "This is a test task for status tracking validation",
		Type:        models.TaskTypeGeneral,
		Status:      models.TaskStatusPending,
		Priority:    models.TaskPriorityMedium,
		DueDate:     &dueDate,
		CreatedByID: suite.testUser.ID,
	}
	suite.db.Create(suite.testTask)
}

func (suite *TaskStatusTrackingTestSuite) TestTrackStatusChange() {
	testCases := []struct {
		name           string
		previousStatus models.TaskStatus
		newStatus      models.TaskStatus
		changeReason   string
		changeType     services.TaskStatusChangeType
		automated      bool
	}{
		{
			name:           "Task started",
			previousStatus: models.TaskStatusPending,
			newStatus:      models.TaskStatusInProgress,
			changeReason:   "User started working on task",
			changeType:     services.TaskStatusChangeTypeStarted,
			automated:      false,
		},
		{
			name:           "Task completed",
			previousStatus: models.TaskStatusInProgress,
			newStatus:      models.TaskStatusCompleted,
			changeReason:   "Task completed successfully",
			changeType:     services.TaskStatusChangeTypeCompleted,
			automated:      false,
		},
		{
			name:           "Automated overdue",
			previousStatus: models.TaskStatusInProgress,
			newStatus:      models.TaskStatusOnHold,
			changeReason:   "Task automatically marked as overdue",
			changeType:     services.TaskStatusChangeTypeAutomated,
			automated:      true,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			err := suite.taskStatusTracker.TrackStatusChange(
				suite.testTask.ID,
				tc.previousStatus,
				tc.newStatus,
				suite.testUser.ID,
				tc.changeReason,
				tc.changeType,
				tc.automated,
			)

			assert.NoError(suite.T(), err)

			// Verify the status change was recorded
			var historyEntry services.TaskStatusHistoryEntry
			err = suite.db.Where("task_id = ? AND new_status = ?",
				suite.testTask.ID, tc.newStatus).
				Order("timestamp DESC").
				First(&historyEntry).Error

			assert.NoError(suite.T(), err)
			assert.Equal(suite.T(), tc.previousStatus, historyEntry.PreviousStatus)
			assert.Equal(suite.T(), tc.newStatus, historyEntry.NewStatus)
			assert.Equal(suite.T(), tc.changeReason, historyEntry.ChangeReason)
			assert.Equal(suite.T(), tc.changeType, historyEntry.ChangeType)
			assert.Equal(suite.T(), tc.automated, historyEntry.AutomatedChange)
			assert.Equal(suite.T(), suite.testUser.ID, historyEntry.ChangedByID)
			assert.NotEmpty(suite.T(), historyEntry.PerformanceImpact)
		})
	}
}

func (suite *TaskStatusTrackingTestSuite) TestTrackPriorityChange() {
	testCases := []struct {
		name             string
		previousPriority models.TaskPriority
		newPriority      models.TaskPriority
		changeReason     string
	}{
		{
			name:             "Priority increased",
			previousPriority: models.TaskPriorityMedium,
			newPriority:      models.TaskPriorityHigh,
			changeReason:     "Task priority increased due to urgency",
		},
		{
			name:             "Priority decreased",
			previousPriority: models.TaskPriorityHigh,
			newPriority:      models.TaskPriorityLow,
			changeReason:     "Task priority decreased after reassessment",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			err := suite.taskStatusTracker.TrackPriorityChange(
				suite.testTask.ID,
				tc.previousPriority,
				tc.newPriority,
				suite.testUser.ID,
				tc.changeReason,
			)

			assert.NoError(suite.T(), err)

			// Verify the priority change was recorded
			var historyEntry services.TaskStatusHistoryEntry
			err = suite.db.Where("task_id = ? AND change_type = ? AND new_priority = ?",
				suite.testTask.ID, services.TaskStatusChangeTypePriorityChanged, tc.newPriority).
				Order("timestamp DESC").
				First(&historyEntry).Error

			assert.NoError(suite.T(), err)
			assert.Equal(suite.T(), tc.previousPriority, historyEntry.PreviousPriority)
			assert.Equal(suite.T(), tc.newPriority, historyEntry.NewPriority)
			assert.Equal(suite.T(), tc.changeReason, historyEntry.ChangeReason)
			assert.Equal(suite.T(), services.TaskStatusChangeTypePriorityChanged, historyEntry.ChangeType)
			assert.False(suite.T(), historyEntry.AutomatedChange)
			assert.NotEmpty(suite.T(), historyEntry.PerformanceImpact)
		})
	}
}

func (suite *TaskStatusTrackingTestSuite) TestGetTaskStatusHistory() {
	// Create multiple status changes
	statusChanges := []struct {
		previousStatus models.TaskStatus
		newStatus      models.TaskStatus
		changeType     services.TaskStatusChangeType
	}{
		{models.TaskStatusPending, models.TaskStatusInProgress, services.TaskStatusChangeTypeStarted},
		{models.TaskStatusInProgress, models.TaskStatusCompleted, services.TaskStatusChangeTypeCompleted},
		{models.TaskStatusCompleted, models.TaskStatusInProgress, services.TaskStatusChangeTypeReopened},
	}

	for _, change := range statusChanges {
		err := suite.taskStatusTracker.TrackStatusChange(
			suite.testTask.ID,
			change.previousStatus,
			change.newStatus,
			suite.testUser.ID,
			"Test status change",
			change.changeType,
			false,
		)
		suite.Require().NoError(err)
		time.Sleep(10 * time.Millisecond) // Ensure different timestamps
	}

	// Test getting history with pagination
	history, err := suite.taskStatusTracker.GetTaskStatusHistory(suite.testTask.ID, 10, 0)
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), history)

	// Verify history is ordered by timestamp (newest first)
	for i := 0; i < len(history)-1; i++ {
		assert.True(suite.T(), history[i].Timestamp.After(history[i+1].Timestamp) ||
			history[i].Timestamp.Equal(history[i+1].Timestamp))
	}

	// Test pagination
	limitedHistory, err := suite.taskStatusTracker.GetTaskStatusHistory(suite.testTask.ID, 2, 0)
	assert.NoError(suite.T(), err)
	assert.LessOrEqual(suite.T(), len(limitedHistory), 2)

	// Test offset
	offsetHistory, err := suite.taskStatusTracker.GetTaskStatusHistory(suite.testTask.ID, 10, 1)
	assert.NoError(suite.T(), err)
	if len(history) > 1 {
		assert.Equal(suite.T(), history[1].ID, offsetHistory[0].ID)
	}
}

func (suite *TaskStatusTrackingTestSuite) TestGetTaskStatusSummary() {
	// Create some status changes
	statusChanges := []models.TaskStatus{
		models.TaskStatusInProgress,
		models.TaskStatusCompleted,
		models.TaskStatusInProgress,
		models.TaskStatusCompleted,
	}

	previousStatus := models.TaskStatusPending
	for _, newStatus := range statusChanges {
		err := suite.taskStatusTracker.TrackStatusChange(
			suite.testTask.ID,
			previousStatus,
			newStatus,
			suite.testUser.ID,
			"Test change",
			services.TaskStatusChangeTypeUpdated,
			false,
		)
		suite.Require().NoError(err)
		previousStatus = newStatus
		time.Sleep(10 * time.Millisecond)
	}

	// Get summary
	summary, err := suite.taskStatusTracker.GetTaskStatusSummary(suite.testTask.ID)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), summary)

	// Verify summary structure
	assert.Contains(suite.T(), summary, "task_id")
	assert.Contains(suite.T(), summary, "total_changes")
	assert.Contains(suite.T(), summary, "status_counts")
	assert.Contains(suite.T(), summary, "change_type_counts")
	assert.Contains(suite.T(), summary, "automated_changes")
	assert.Contains(suite.T(), summary, "manual_changes")
	assert.Contains(suite.T(), summary, "status_timeline")

	// Verify data
	assert.Equal(suite.T(), suite.testTask.ID, summary["task_id"])
	assert.Greater(suite.T(), summary["total_changes"], 0)
	assert.Equal(suite.T(), 0, summary["automated_changes"]) // All changes were manual
	assert.Equal(suite.T(), summary["total_changes"], summary["manual_changes"])

	// Verify timeline
	timeline := summary["status_timeline"].([]map[string]interface{})
	assert.NotEmpty(suite.T(), timeline)

	// Verify timeline entries have required fields
	for _, entry := range timeline {
		assert.Contains(suite.T(), entry, "timestamp")
		assert.Contains(suite.T(), entry, "previous_status")
		assert.Contains(suite.T(), entry, "new_status")
		assert.Contains(suite.T(), entry, "change_type")
		assert.Contains(suite.T(), entry, "performance_impact")
		assert.Contains(suite.T(), entry, "duration_in_status")
	}
}

func (suite *TaskStatusTrackingTestSuite) TestGetTaskStatusAnalytics() {
	// Create status changes for analytics
	for i := 0; i < 5; i++ {
		err := suite.taskStatusTracker.TrackStatusChange(
			suite.testTask.ID,
			models.TaskStatusPending,
			models.TaskStatusInProgress,
			suite.testUser.ID,
			"Analytics test change",
			services.TaskStatusChangeTypeStarted,
			i%2 == 0, // Alternate between automated and manual
		)
		suite.Require().NoError(err)
	}

	// Test analytics without filters
	analytics, err := suite.taskStatusTracker.GetTaskStatusAnalytics(nil, nil, nil)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), analytics)

	// Verify analytics structure
	assert.Contains(suite.T(), analytics, "total_changes")
	assert.Contains(suite.T(), analytics, "change_type_stats")
	assert.Contains(suite.T(), analytics, "status_transitions")
	assert.Contains(suite.T(), analytics, "automation_stats")
	assert.Contains(suite.T(), analytics, "user_activity")

	// Test analytics with user filter
	userAnalytics, err := suite.taskStatusTracker.GetTaskStatusAnalytics(&suite.testUser.ID, nil, nil)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), userAnalytics)

	// Test analytics with date filter
	yesterday := time.Now().Add(-24 * time.Hour)
	tomorrow := time.Now().Add(24 * time.Hour)
	dateAnalytics, err := suite.taskStatusTracker.GetTaskStatusAnalytics(nil, &yesterday, &tomorrow)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), dateAnalytics)
}

func (suite *TaskStatusTrackingTestSuite) TestIntegrationWithTaskPerformanceService() {
	// Test task creation tracking
	err := suite.taskPerformanceService.TrackTaskCreation(suite.testTask, suite.testUser.ID)
	assert.NoError(suite.T(), err)

	// Verify creation was tracked
	var creationEntry services.TaskStatusHistoryEntry
	err = suite.db.Where("task_id = ? AND change_type = ?",
		suite.testTask.ID, services.TaskStatusChangeTypeCreated).
		First(&creationEntry).Error
	assert.NoError(suite.T(), err)

	// Test task completion tracking
	suite.testTask.Status = models.TaskStatusCompleted
	suite.testTask.CompletedAt = &[]time.Time{time.Now()}[0]

	err = suite.taskPerformanceService.TrackTaskCompletion(suite.testTask, suite.testUser.ID)
	assert.NoError(suite.T(), err)

	// Verify completion was tracked
	var completionEntry services.TaskStatusHistoryEntry
	err = suite.db.Where("task_id = ? AND change_type = ?",
		suite.testTask.ID, services.TaskStatusChangeTypeCompleted).
		First(&completionEntry).Error
	assert.NoError(suite.T(), err)
	assert.Contains(suite.T(), completionEntry.ChangeReason, "completed")
}
