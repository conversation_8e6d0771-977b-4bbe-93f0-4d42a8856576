'use client';

import React from 'react';
import { DigitalCertificate } from '../types';

interface CertificateSelectorProps {
  certificates: DigitalCertificate[];
  selectedCertificate: DigitalCertificate | null;
  onSelect: (certificate: DigitalCertificate | null) => void;
  loading?: boolean;
  error?: string | null;
  formatDisplayName: (certificate: DigitalCertificate) => string;
  getStatusDisplay: (certificate: DigitalCertificate) => { status: string; color: string; icon: string };
  isExpiringSoon: (certificate: DigitalCertificate) => boolean;
  className?: string;
}

export const CertificateSelector: React.FC<CertificateSelectorProps> = ({
  certificates,
  selectedCertificate,
  onSelect,
  loading = false,
  error = null,
  formatDisplayName,
  getStatusDisplay,
  isExpiringSoon,
  className = ''
}) => {
  if (loading) {
    return (
      <div className={`p-4 border rounded-lg ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 border border-red-300 rounded-lg bg-red-50 ${className}`}>
        <div className="text-red-800">
          <h3 className="font-medium">Certificate Loading Error</h3>
          <p className="text-sm mt-1">{error}</p>
        </div>
      </div>
    );
  }

  if (certificates.length === 0) {
    return (
      <div className={`p-4 border border-yellow-300 rounded-lg bg-yellow-50 ${className}`}>
        <div className="text-yellow-800">
          <h3 className="font-medium">No Certificates Available</h3>
          <p className="text-sm mt-1">
            You don't have any digital certificates available for signing. 
            Please contact your administrator to obtain a certificate.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        Select Digital Certificate for Signing
      </label>
      
      <div className="space-y-2">
        {/* Option to not use digital signature */}
        <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
          <input
            type="radio"
            name="certificate"
            checked={selectedCertificate === null}
            onChange={() => onSelect(null)}
            className="mr-3"
          />
          <div>
            <div className="font-medium text-gray-900">No Digital Signature</div>
            <div className="text-sm text-gray-500">Submit comment without digital signature</div>
          </div>
        </label>

        {/* Certificate options */}
        {certificates.map((certificate) => {
          const statusDisplay = getStatusDisplay(certificate);
          const expiringSoon = isExpiringSoon(certificate);
          const isSelected = selectedCertificate?.id === certificate.id;
          
          return (
            <label
              key={certificate.id}
              className={`flex items-center p-3 border rounded-lg cursor-pointer transition-colors ${
                isSelected 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-300 hover:bg-gray-50'
              }`}
            >
              <input
                type="radio"
                name="certificate"
                checked={isSelected}
                onChange={() => onSelect(certificate)}
                className="mr-3"
              />
              
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <div className="font-medium text-gray-900">
                    {formatDisplayName(certificate)}
                    {certificate.is_default && (
                      <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        Default
                      </span>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span
                      className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
                        statusDisplay.color === 'green' 
                          ? 'bg-green-100 text-green-800'
                          : statusDisplay.color === 'red'
                          ? 'bg-red-100 text-red-800'
                          : statusDisplay.color === 'orange'
                          ? 'bg-orange-100 text-orange-800'
                          : statusDisplay.color === 'blue'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      <span className="mr-1">{statusDisplay.icon}</span>
                      {statusDisplay.status}
                    </span>
                    
                    {expiringSoon && (
                      <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-yellow-100 text-yellow-800 rounded-full">
                        ⚠ Expires Soon
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="mt-1 text-sm text-gray-500">
                  <div>Serial: {certificate.serial}</div>
                  <div>Issuer: {certificate.issuer}</div>
                  <div>
                    Valid: {new Date(certificate.not_before).toLocaleDateString()} - {new Date(certificate.not_after).toLocaleDateString()}
                  </div>
                </div>
              </div>
            </label>
          );
        })}
      </div>
      
      {selectedCertificate && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="text-sm text-blue-800">
            <strong>Selected Certificate:</strong> {formatDisplayName(selectedCertificate)}
            <br />
            Your comment will be digitally signed with this certificate to verify your identity.
          </div>
        </div>
      )}
    </div>
  );
};

export default CertificateSelector;
