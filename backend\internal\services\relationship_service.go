package services

import (
	"fmt"
	"log"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// RelationshipService manages entity relationships and dependencies
type RelationshipService struct {
	db *gorm.DB
}

// NewRelationshipService creates a new relationship service
func NewRelationshipService(db *gorm.DB) *RelationshipService {
	return &RelationshipService{db: db}
}

// EntityRelationship represents a generic relationship between entities
type EntityRelationship struct {
	ID               uint                            `json:"id" gorm:"primaryKey"`
	CreatedAt        time.Time                       `json:"created_at"`
	UpdatedAt        time.Time                       `json:"updated_at"`
	SourceEntityType string                          `json:"source_entity_type" gorm:"not null"`
	SourceEntityID   uint                            `json:"source_entity_id" gorm:"not null"`
	TargetEntityType string                          `json:"target_entity_type" gorm:"not null"`
	TargetEntityID   uint                            `json:"target_entity_id" gorm:"not null"`
	RelationshipType models.DocumentRelationshipType `json:"relationship_type" gorm:"not null"`
	Description      string                          `json:"description"`
	IsActive         bool                            `json:"is_active" gorm:"default:true"`
	CreatedByID      uint                            `json:"created_by_id" gorm:"not null"`
	CreatedBy        models.User                     `json:"created_by" gorm:"foreignKey:CreatedByID"`
}

// CreateDocumentRegulationRelationship creates a relationship between document and regulation
func (s *RelationshipService) CreateDocumentRegulationRelationship(documentID, regulationID uint, relationshipType models.DocumentRelationshipType, userID uint, description string) error {
	// Check if relationship already exists
	var existing models.RegulationDocumentRelationship
	err := s.db.Where("document_id = ? AND regulation_id = ? AND relationship_type = ? AND is_active = ?",
		documentID, regulationID, relationshipType, true).First(&existing).Error

	if err == nil {
		return fmt.Errorf("active relationship of this type already exists")
	}

	// Create new relationship
	relationship := &models.RegulationDocumentRelationship{
		DocumentID:       documentID,
		RegulationID:     regulationID,
		RelationshipType: relationshipType,
		Description:      description,
		CreatedByID:      userID,
		IsActive:         true,
	}

	return s.db.Create(relationship).Error
}

// CreateDocumentFinanceRelationship links documents to finance records
func (s *RelationshipService) CreateDocumentFinanceRelationship(documentID, financeID uint, userID uint) error {
	// Update finance record to link to document
	return s.db.Model(&models.Finance{}).Where("id = ?", financeID).Update("document_id", documentID).Error
}

// CreateRegulationFinanceRelationship links regulations to finance records
func (s *RelationshipService) CreateRegulationFinanceRelationship(regulationID, financeID uint, userID uint) error {
	// Update finance record to link to regulation
	return s.db.Model(&models.Finance{}).Where("id = ?", financeID).Update("regulation_id", regulationID).Error
}

// CreateTaskDocumentRelationship links tasks to documents
func (s *RelationshipService) CreateTaskDocumentRelationship(taskID, documentID uint, userID uint) error {
	// Update task to link to document
	return s.db.Model(&models.Task{}).Where("id = ?", taskID).Updates(map[string]interface{}{
		"document_id": documentID,
		"source_type": "document",
		"source_id":   documentID,
	}).Error
}

// CreateTaskRegulationRelationship links tasks to regulations
func (s *RelationshipService) CreateTaskRegulationRelationship(taskID, regulationID uint, userID uint) error {
	// Update task to link to regulation
	return s.db.Model(&models.Task{}).Where("id = ?", taskID).Updates(map[string]interface{}{
		"regulation_id": regulationID,
		"source_type":   "regulation",
		"source_id":     regulationID,
	}).Error
}

// AutoCreateRelationships automatically creates relationships based on content similarity
func (s *RelationshipService) AutoCreateRelationships(entityType string, entityID uint, userID uint) error {
	switch entityType {
	case "document":
		return s.autoCreateDocumentRelationships(entityID, userID)
	case "regulation":
		return s.autoCreateRegulationRelationships(entityID, userID)
	case "task":
		return s.autoCreateTaskRelationships(entityID, userID)
	case "finance":
		return s.autoCreateFinanceRelationships(entityID, userID)
	}
	return nil
}

// autoCreateDocumentRelationships finds and creates relationships for a document
func (s *RelationshipService) autoCreateDocumentRelationships(documentID uint, userID uint) error {
	var document models.Document
	if err := s.db.Preload("Categories").First(&document, documentID).Error; err != nil {
		return err
	}

	// Find related regulations based on content similarity
	nlpService := NewNLPService()
	relatedRegulations := nlpService.FindRelatedRegulations(document.Content, s.db)

	for _, regulation := range relatedRegulations {
		// Create relationship if it doesn't exist
		s.CreateDocumentRegulationRelationship(documentID, regulation.ID, models.DocumentRelationshipReferences, userID, "Auto-generated based on content similarity")
	}

	// Find related documents in same category
	if len(document.Categories) > 0 {
		var relatedDocs []models.Document
		s.db.Joins("JOIN document_category_assignments dca ON documents.id = dca.document_id").
			Where("dca.category_id = ? AND documents.id != ?", document.Categories[0].ID, documentID).
			Limit(5).Find(&relatedDocs)

		// Create parent-child relationships for related documents
		for _, relatedDoc := range relatedDocs {
			s.db.Model(&models.Document{}).Where("id = ?", documentID).Update("parent_document_id", relatedDoc.ID)
			break // Only set one parent
		}
	}

	return nil
}

// autoCreateRegulationRelationships finds and creates relationships for a regulation
func (s *RelationshipService) autoCreateRegulationRelationships(regulationID uint, userID uint) error {
	var regulation models.LawsAndRules
	if err := s.db.First(&regulation, regulationID).Error; err != nil {
		return err
	}

	// Find related documents
	var relatedDocs []models.Document
	s.db.Where("agency_id = ? AND content ILIKE ?", regulation.AgencyID, "%"+regulation.Title+"%").
		Limit(5).Find(&relatedDocs)

	for _, doc := range relatedDocs {
		s.CreateDocumentRegulationRelationship(doc.ID, regulationID, models.DocumentRelationshipImplements, userID, "Auto-generated based on agency and content match")
	}

	return nil
}

// autoCreateTaskRelationships finds and creates relationships for a task
func (s *RelationshipService) autoCreateTaskRelationships(taskID uint, userID uint) error {
	var task models.Task
	if err := s.db.First(&task, taskID).Error; err != nil {
		return err
	}

	// If task doesn't have source relationships, try to find them
	if task.SourceType == "" || task.SourceID == nil {
		// Look for documents or regulations mentioned in task title/description
		searchText := task.Title + " " + task.Description

		// Find related documents
		var relatedDocs []models.Document
		s.db.Where("title ILIKE ? OR content ILIKE ?", "%"+searchText+"%", "%"+searchText+"%").
			Limit(3).Find(&relatedDocs)

		if len(relatedDocs) > 0 {
			s.CreateTaskDocumentRelationship(taskID, relatedDocs[0].ID, userID)
		}

		// Find related regulations
		var relatedRegs []models.LawsAndRules
		s.db.Where("title ILIKE ? OR description ILIKE ?", "%"+searchText+"%", "%"+searchText+"%").
			Limit(3).Find(&relatedRegs)

		if len(relatedRegs) > 0 {
			s.CreateTaskRegulationRelationship(taskID, relatedRegs[0].ID, userID)
		}
	}

	return nil
}

// autoCreateFinanceRelationships finds and creates relationships for a finance record
func (s *RelationshipService) autoCreateFinanceRelationships(financeID uint, userID uint) error {
	var finance models.Finance
	if err := s.db.First(&finance, financeID).Error; err != nil {
		return err
	}

	// If finance record doesn't have document/regulation links, try to find them
	if finance.DocumentID == nil && finance.RegulationID == nil {
		searchText := finance.Description

		// Find related documents
		var relatedDocs []models.Document
		s.db.Where("title ILIKE ? OR content ILIKE ?", "%"+searchText+"%", "%"+searchText+"%").
			Limit(3).Find(&relatedDocs)

		if len(relatedDocs) > 0 {
			s.CreateDocumentFinanceRelationship(relatedDocs[0].ID, financeID, userID)
		}

		// Find related regulations
		var relatedRegs []models.LawsAndRules
		s.db.Where("title ILIKE ? OR description ILIKE ?", "%"+searchText+"%", "%"+searchText+"%").
			Limit(3).Find(&relatedRegs)

		if len(relatedRegs) > 0 {
			s.CreateRegulationFinanceRelationship(relatedRegs[0].ID, financeID, userID)
		}
	}

	return nil
}

// GetEntityRelationships returns all relationships for a given entity
func (s *RelationshipService) GetEntityRelationships(entityType string, entityID uint) (map[string]interface{}, error) {
	relationships := make(map[string]interface{})

	switch entityType {
	case "document":
		// Get document relationships
		var docRelationships []models.RegulationDocumentRelationship
		s.db.Preload("Regulation").Where("document_id = ? AND is_active = ?", entityID, true).Find(&docRelationships)
		relationships["regulations"] = docRelationships

		// Get related tasks
		var tasks []models.Task
		s.db.Where("document_id = ?", entityID).Find(&tasks)
		relationships["tasks"] = tasks

		// Get related finance records
		var finances []models.Finance
		s.db.Where("document_id = ?", entityID).Find(&finances)
		relationships["finances"] = finances

	case "regulation":
		// Get regulation relationships
		var regRelationships []models.RegulationDocumentRelationship
		s.db.Preload("Document").Where("regulation_id = ? AND is_active = ?", entityID, true).Find(&regRelationships)
		relationships["documents"] = regRelationships

		// Get related tasks
		var tasks []models.Task
		s.db.Where("regulation_id = ?", entityID).Find(&tasks)
		relationships["tasks"] = tasks

		// Get related finance records
		var finances []models.Finance
		s.db.Where("regulation_id = ?", entityID).Find(&finances)
		relationships["finances"] = finances

	case "task":
		// Get task relationships
		var task models.Task
		s.db.Preload("Document").Preload("Agency").First(&task, entityID)
		relationships["source_document"] = task.Document
		relationships["source_agency"] = task.Agency

	case "finance":
		// Get finance relationships
		var finance models.Finance
		s.db.Preload("Document").Preload("Regulation").First(&finance, entityID)
		relationships["document"] = finance.Document
		relationships["regulation"] = finance.Regulation
	}

	return relationships, nil
}

// ValidateRelationship checks if a relationship is valid and doesn't create cycles
func (s *RelationshipService) ValidateRelationship(sourceType string, sourceID uint, targetType string, targetID uint, relationshipType string) error {
	// Prevent self-relationships
	if sourceType == targetType && sourceID == targetID {
		return fmt.Errorf("cannot create relationship with self")
	}

	// Comprehensive circular relationship detection using graph traversal
	if err := s.detectCircularRelationships(sourceType, sourceID, targetType, targetID, relationshipType); err != nil {
		return err
	}

	// Validate relationship type compatibility
	if err := s.validateRelationshipTypeCompatibility(sourceType, targetType, relationshipType); err != nil {
		return err
	}

	// Check relationship constraints and business rules
	if err := s.validateRelationshipConstraints(sourceType, sourceID, targetType, targetID, relationshipType); err != nil {
		return err
	}

	// Validate entity existence and accessibility
	if err := s.validateEntityExistence(sourceType, sourceID, targetType, targetID); err != nil {
		return err
	}

	return nil
}

// CleanupOrphanedRelationships removes relationships where source or target entities no longer exist
func (s *RelationshipService) CleanupOrphanedRelationships() error {
	// Clean up document-regulation relationships
	s.db.Exec(`
		DELETE FROM regulation_document_relationships 
		WHERE document_id NOT IN (SELECT id FROM documents) 
		   OR regulation_id NOT IN (SELECT id FROM laws_and_rules)
	`)

	// Clean up task relationships
	s.db.Exec(`
		UPDATE tasks SET document_id = NULL 
		WHERE document_id IS NOT NULL 
		  AND document_id NOT IN (SELECT id FROM documents)
	`)

	s.db.Exec(`
		UPDATE tasks SET regulation_id = NULL 
		WHERE regulation_id IS NOT NULL 
		  AND regulation_id NOT IN (SELECT id FROM laws_and_rules)
	`)

	// Clean up finance relationships
	s.db.Exec(`
		UPDATE finances SET document_id = NULL 
		WHERE document_id IS NOT NULL 
		  AND document_id NOT IN (SELECT id FROM documents)
	`)

	s.db.Exec(`
		UPDATE finances SET regulation_id = NULL 
		WHERE regulation_id IS NOT NULL 
		  AND regulation_id NOT IN (SELECT id FROM laws_and_rules)
	`)

	log.Println("Cleaned up orphaned relationships")
	return nil
}

// detectCircularRelationships performs comprehensive cycle detection in the relationship graph
func (s *RelationshipService) detectCircularRelationships(sourceType string, sourceID uint, targetType string, targetID uint, relationshipType string) error {
	// Only check for cycles in hierarchical relationships
	hierarchicalTypes := map[string]bool{
		"parent_child":       true,
		"contains":           true,
		"part_of":            true,
		"depends_on":         true,
		"supersedes":         true,
		"derived_from":       true,
		"implements":         true,
		"parent_document_id": true, // Special case for document parent-child relationship
	}

	// Skip cycle detection for non-hierarchical relationships
	if !hierarchicalTypes[relationshipType] {
		return nil
	}

	// Skip cycle detection for different entity types (can't form a cycle)
	if sourceType != targetType {
		return nil
	}

	// Perform depth-first search to detect cycles
	visited := make(map[uint]bool)
	path := make(map[uint]bool)

	return s.dfsDetectCycle(targetType, targetID, sourceID, visited, path, relationshipType)
}

// dfsDetectCycle performs depth-first search to detect cycles in the relationship graph
func (s *RelationshipService) dfsDetectCycle(entityType string, currentID uint, targetID uint, visited map[uint]bool, path map[uint]bool, relationshipType string) error {
	// If we've reached the target, we've found a cycle
	if currentID == targetID {
		return fmt.Errorf("circular relationship detected: entity %d would form a cycle", targetID)
	}

	// Mark current node as visited and add to current path
	visited[currentID] = true
	path[currentID] = true

	// Get all related entities based on entity type
	relatedIDs, err := s.getRelatedEntities(entityType, currentID, relationshipType)
	if err != nil {
		return nil // Ignore errors in cycle detection
	}

	// Check all related entities for cycles
	for _, relatedID := range relatedIDs {
		if !visited[relatedID] {
			// If not visited, recursively check for cycles
			if err := s.dfsDetectCycle(entityType, relatedID, targetID, visited, path, relationshipType); err != nil {
				return err
			}
		} else if path[relatedID] {
			// If already in current path, we've found a cycle
			return fmt.Errorf("circular relationship detected: entity %d would form a cycle", relatedID)
		}
	}

	// Remove current node from path (backtrack)
	path[currentID] = false

	return nil
}

// getRelatedEntities returns all entities related to the given entity
func (s *RelationshipService) getRelatedEntities(entityType string, entityID uint, relationshipType string) ([]uint, error) {
	var relatedIDs []uint

	switch entityType {
	case "document":
		// Get parent document relationship
		var documents []models.Document
		if err := s.db.Where("parent_document_id = ?", entityID).Find(&documents).Error; err != nil {
			return nil, err
		}

		for _, doc := range documents {
			relatedIDs = append(relatedIDs, doc.ID)
		}

	case "regulation":
		// Get regulation relationships based on relationship type
		var relationships []models.RegulationRelationship

		// Different queries based on relationship direction
		if relationshipType == "depends_on" || relationshipType == "implements" || relationshipType == "derived_from" {
			// Forward relationships (source -> target)
			if err := s.db.Where("source_regulation_id = ? AND is_active = ?", entityID, true).
				Find(&relationships).Error; err != nil {
				return nil, err
			}

			for _, rel := range relationships {
				relatedIDs = append(relatedIDs, rel.TargetRegulationID)
			}
		} else {
			// Backward relationships (target -> source)
			if err := s.db.Where("target_regulation_id = ? AND is_active = ?", entityID, true).
				Find(&relationships).Error; err != nil {
				return nil, err
			}

			for _, rel := range relationships {
				relatedIDs = append(relatedIDs, rel.SourceRegulationID)
			}
		}

	case "category":
		// Get parent-child category relationships
		var categories []models.Category
		if err := s.db.Where("parent_category_id = ?", entityID).Find(&categories).Error; err != nil {
			return nil, err
		}

		for _, cat := range categories {
			relatedIDs = append(relatedIDs, cat.ID)
		}
	}

	return relatedIDs, nil
}

// validateRelationshipTypeCompatibility checks if the relationship type is valid for the given entity types
func (s *RelationshipService) validateRelationshipTypeCompatibility(sourceType string, targetType string, relationshipType string) error {
	// Define valid relationship types for each entity type pair
	validRelationships := map[string]map[string][]string{
		"document": {
			"document": {
				"parent_child", "references", "supersedes", "amends", "related_to",
			},
			"regulation": {
				"implements", "references", "affected_by", "complies_with",
			},
			"category": {
				"belongs_to", "categorized_as",
			},
		},
		"regulation": {
			"regulation": {
				"depends_on", "references", "supersedes", "amends", "implements", "derived_from",
			},
			"document": {
				"implemented_by", "referenced_by", "affects", "compliance_required",
			},
			"agency": {
				"issued_by", "enforced_by", "administered_by",
			},
			"category": {
				"belongs_to", "categorized_as",
			},
		},
		"category": {
			"category": {
				"parent_child", "related_to",
			},
		},
	}

	// Check if the relationship type is valid for the given entity types
	if validTypes, exists := validRelationships[sourceType][targetType]; exists {
		for _, validType := range validTypes {
			if validType == relationshipType {
				return nil // Valid relationship type
			}
		}
		return fmt.Errorf("invalid relationship type '%s' for %s -> %s relationship", relationshipType, sourceType, targetType)
	}

	return fmt.Errorf("relationships between %s and %s are not supported", sourceType, targetType)
}

// validateRelationshipConstraints checks business rules and constraints for the relationship
func (s *RelationshipService) validateRelationshipConstraints(sourceType string, sourceID uint, targetType string, targetID uint, relationshipType string) error {
	// Check for existing relationships of the same type
	if err := s.checkExistingRelationships(sourceType, sourceID, targetType, targetID, relationshipType); err != nil {
		return err
	}

	// Check entity-specific constraints
	switch sourceType {
	case "document":
		if targetType == "document" && relationshipType == "parent_child" {
			// Check maximum nesting level for documents (prevent deep hierarchies)
			level, err := s.getDocumentHierarchyLevel(sourceID)
			if err != nil {
				return err
			}

			if level >= 5 { // Maximum allowed nesting level
				return fmt.Errorf("maximum document hierarchy nesting level reached (5)")
			}
		}

	case "regulation":
		if targetType == "regulation" && relationshipType == "supersedes" {
			// Check if the target regulation is already superseded
			var count int64
			s.db.Model(&models.RegulationRelationship{}).
				Where("target_regulation_id = ? AND relationship_type = ? AND is_active = ?",
					targetID, "supersedes", true).
				Count(&count)

			if count > 0 {
				return fmt.Errorf("target regulation is already superseded by another regulation")
			}
		}
	}

	return nil
}

// checkExistingRelationships checks if a relationship of the same type already exists
func (s *RelationshipService) checkExistingRelationships(sourceType string, sourceID uint, targetType string, targetID uint, relationshipType string) error {
	// Check for existing relationships based on entity types
	switch {
	case sourceType == "document" && targetType == "document":
		if relationshipType == "parent_child" {
			// Check if document already has a parent
			var document models.Document
			if err := s.db.First(&document, sourceID).Error; err != nil {
				return err
			}

			if document.ParentDocumentID != nil && *document.ParentDocumentID != targetID {
				return fmt.Errorf("document already has a different parent document")
			}
		}

	case sourceType == "document" && targetType == "regulation":
		// Check for existing document-regulation relationship
		var count int64
		s.db.Model(&models.RegulationDocumentRelationship{}).
			Where("document_id = ? AND regulation_id = ? AND relationship_type = ? AND is_active = ?",
				sourceID, targetID, relationshipType, true).
			Count(&count)

		if count > 0 {
			return fmt.Errorf("active relationship of this type already exists between document and regulation")
		}

	case sourceType == "regulation" && targetType == "regulation":
		// Check for existing regulation-regulation relationship
		var count int64
		s.db.Model(&models.RegulationRelationship{}).
			Where("source_regulation_id = ? AND target_regulation_id = ? AND relationship_type = ? AND is_active = ?",
				sourceID, targetID, relationshipType, true).
			Count(&count)

		if count > 0 {
			return fmt.Errorf("active relationship of this type already exists between regulations")
		}
	}

	return nil
}

// getDocumentHierarchyLevel calculates the nesting level of a document in the hierarchy
func (s *RelationshipService) getDocumentHierarchyLevel(documentID uint) (int, error) {
	level := 0
	currentID := documentID
	visited := make(map[uint]bool) // Prevent infinite loops

	for {
		// Prevent cycles
		if visited[currentID] {
			return level, fmt.Errorf("cycle detected in document hierarchy")
		}
		visited[currentID] = true

		// Get parent document
		var document models.Document
		if err := s.db.First(&document, currentID).Error; err != nil {
			return level, err
		}

		// If no parent, we've reached the top
		if document.ParentDocumentID == nil {
			break
		}

		// Move up to parent
		currentID = *document.ParentDocumentID
		level++

		// Safety check for deep hierarchies
		if level > 10 {
			return level, fmt.Errorf("hierarchy too deep, possible cycle")
		}
	}

	return level, nil
}

// validateEntityExistence checks if both entities exist and are accessible
func (s *RelationshipService) validateEntityExistence(sourceType string, sourceID uint, targetType string, targetID uint) error {
	// Check source entity
	if err := s.checkEntityExists(sourceType, sourceID); err != nil {
		return fmt.Errorf("source entity error: %w", err)
	}

	// Check target entity
	if err := s.checkEntityExists(targetType, targetID); err != nil {
		return fmt.Errorf("target entity error: %w", err)
	}

	return nil
}

// checkEntityExists verifies that an entity exists in the database
func (s *RelationshipService) checkEntityExists(entityType string, entityID uint) error {
	switch entityType {
	case "document":
		var document models.Document
		if err := s.db.First(&document, entityID).Error; err != nil {
			return fmt.Errorf("document with ID %d not found", entityID)
		}

		// Check if document is active
		if document.Status != "active" && document.Status != "published" {
			return fmt.Errorf("document with ID %d is not active", entityID)
		}

	case "regulation":
		var regulation models.LawsAndRules
		if err := s.db.First(&regulation, entityID).Error; err != nil {
			return fmt.Errorf("regulation with ID %d not found", entityID)
		}

		// Check if regulation is active
		if regulation.Status != "published" && regulation.Status != "effective" {
			return fmt.Errorf("regulation with ID %d is not active (status: %s)", entityID, regulation.Status)
		}

	case "category":
		var category models.Category
		if err := s.db.First(&category, entityID).Error; err != nil {
			return fmt.Errorf("category with ID %d not found", entityID)
		}

	case "agency":
		var agency models.Agency
		if err := s.db.First(&agency, entityID).Error; err != nil {
			return fmt.Errorf("agency with ID %d not found", entityID)
		}

	default:
		return fmt.Errorf("unsupported entity type: %s", entityType)
	}

	return nil
}
