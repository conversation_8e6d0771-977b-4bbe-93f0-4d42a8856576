'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import financeApi from '../../services/financeApi';
import apiService from '../../services/api';
import {
  CreateFinanceRequest,
  UpdateFinanceRequest,
  Finance,
  FinanceCategory,
  Document,
  LawsAndRules
} from '../../types';

interface FinanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  finance?: Finance;
  year?: number;
}

const FinanceModal: React.FC<FinanceModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  finance,
  year = new Date().getFullYear()
}) => {
  const [formData, setFormData] = useState<CreateFinanceRequest>({
    amount: 0,
    year,
    description: '',
    budget_type: 'original',
    performance_percentage: 100
  });
  const [categories, setCategories] = useState<FinanceCategory[]>([]);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [regulations, setRegulations] = useState<LawsAndRules[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditing = !!finance;

  useEffect(() => {
    if (isOpen) {
      fetchData();
      if (finance) {
        setFormData({
          amount: finance.amount,
          year: finance.year,
          description: finance.description,
          document_id: finance.document_id,
          regulation_id: finance.regulation_id,
          budget_type: finance.budget_type,
          performance_percentage: finance.performance_percentage,
          category_id: finance.category_id
        });
      } else {
        // Load default values for new finance record
        loadDefaultValues();
      }
      setError(null);
    }
  }, [isOpen, finance, year]);

  // Load default values from preloading API
  const loadDefaultValues = async () => {
    try {
      const response = await fetch('/api/v1/preloading/finances', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (response.ok) {
        const defaults = await response.json();

        setFormData({
          amount: 0,
          year: defaults.year || new Date().getFullYear(),
          description: '',
          budget_type: defaults.budget_type || 'original',
          performance_percentage: defaults.performance_percentage || 100
        });
      } else {
        // Fallback to hardcoded defaults
        setFormData({
          amount: 0,
          year,
          description: '',
          budget_type: 'original',
          performance_percentage: 100
        });
      }
    } catch (err) {
      console.error('Error loading default values:', err);
      // Fallback to hardcoded defaults
      setFormData({
        amount: 0,
        year,
        description: '',
        budget_type: 'original',
        performance_percentage: 100
      });
    }
  };

  const fetchData = async () => {
    try {
      const [categoriesData, documentsData, regulationsData] = await Promise.all([
        financeApi.getFinanceCategories(),
        apiService.getDocuments({ per_page: 100 }),
        apiService.get('/public/regulations?per_page=100')
      ]);

      setCategories(categoriesData);
      setDocuments(documentsData.data);
      setRegulations((regulationsData as any)?.data?.data || []);
    } catch (err) {
      console.error('Failed to fetch data:', err);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.amount <= 0) {
      setError('Amount must be greater than 0');
      return;
    }

    if (!formData.document_id && !formData.regulation_id) {
      setError('Please select either a document or regulation');
      return;
    }

    if (formData.document_id && formData.regulation_id) {
      setError('Please select either a document OR regulation, not both');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      if (isEditing && finance) {
        await financeApi.updateFinance(finance.id, formData as UpdateFinanceRequest);
      } else {
        await financeApi.createFinance(formData);
      }
      
      onSuccess();
      onClose();
    } catch (err: any) {
      setError(err.response?.data?.message || `Failed to ${isEditing ? 'update' : 'create'} finance entry`);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // Handle special cases
    if (name === 'amount' || name === 'performance_percentage') {
      setFormData(prev => ({
        ...prev,
        [name]: parseFloat(value) || 0
      }));
    } else if (name === 'year') {
      setFormData(prev => ({
        ...prev,
        [name]: parseInt(value) || new Date().getFullYear()
      }));
    } else if (name === 'document_id') {
      setFormData(prev => ({
        ...prev,
        document_id: value ? parseInt(value) : undefined,
        regulation_id: undefined // Clear regulation when document is selected
      }));
    } else if (name === 'regulation_id') {
      setFormData(prev => ({
        ...prev,
        regulation_id: value ? parseInt(value) : undefined,
        document_id: undefined // Clear document when regulation is selected
      }));
    } else if (name === 'category_id') {
      setFormData(prev => ({
        ...prev,
        category_id: value ? parseInt(value) : undefined
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                {isEditing ? 'Edit Finance Entry' : 'Add Finance Entry'}
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Error message */}
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                <div className="text-red-800 text-sm">{error}</div>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Amount */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount *
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <span className="text-gray-500 sm:text-sm">$</span>
                  </div>
                  <input
                    type="number"
                    name="amount"
                    value={formData.amount}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    required
                    className="w-full pl-7 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </div>

              {/* Year */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Year *
                </label>
                <input
                  type="number"
                  name="year"
                  value={formData.year}
                  onChange={handleChange}
                  min="2020"
                  max="2030"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Budget Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Budget Type *
                </label>
                <select
                  name="budget_type"
                  value={formData.budget_type}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="original">Original Budget</option>
                  <option value="actual">Actual Budget</option>
                </select>
              </div>

              {/* Document Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Document
                </label>
                <select
                  name="document_id"
                  value={formData.document_id || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select a document...</option>
                  {documents.map(doc => (
                    <option key={doc.id} value={doc.id}>
                      {doc.title}
                    </option>
                  ))}
                </select>
              </div>

              {/* Regulation Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Regulation
                </label>
                <select
                  name="regulation_id"
                  value={formData.regulation_id || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select a regulation...</option>
                  {regulations.map(reg => (
                    <option key={reg.id} value={reg.id}>
                      {reg.title}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  name="category_id"
                  value={formData.category_id || ''}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Select a category...</option>
                  {categories.map(cat => (
                    <option key={cat.id} value={cat.id}>
                      {cat.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Enter description..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Saving...' : (isEditing ? 'Update' : 'Create')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinanceModal;
