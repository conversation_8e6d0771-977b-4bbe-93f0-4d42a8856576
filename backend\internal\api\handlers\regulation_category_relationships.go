package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// RegulationCategoryRelationshipRequest represents the request structure for regulation-category relationships
type RegulationCategoryRelationshipRequest struct {
	RegulationID uint `json:"regulation_id" binding:"required"`
	CategoryID   uint `json:"category_id" binding:"required"`
}

// GetRegulationCategories returns categories for a regulation
func GetRegulationCategories(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get categories for this regulation
	var categories []models.Category
	if err := db.Joins("JOIN regulation_category_assignments ON categories.id = regulation_category_assignments.category_id").
		Where("regulation_category_assignments.regulation_id = ?", regulationID).
		Find(&categories).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation categories: "+err.Error())
		return
	}

	// Convert to response format
	categoryResponses := make([]gin.H, len(categories))
	for i, category := range categories {
		categoryResponses[i] = gin.H{
			"id":          category.ID,
			"name":        category.Name,
			"slug":        category.Slug,
			"description": category.Description,
			"color":       category.Color,
			"icon":        category.Icon,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation categories retrieved successfully",
		Data:    categoryResponses,
	})
}

// AddRegulationCategory adds a category to a regulation
func AddRegulationCategory(c *gin.Context) {
	var req RegulationCategoryRelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid regulation",
				Message: "Regulation not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Verify category exists
	var category models.Category
	if err := db.First(&category, req.CategoryID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid category",
				Message: "Category not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch category: "+err.Error())
		return
	}

	// Check if relationship already exists
	var existingRelationship models.RegulationCategoryAssignment
	if err := db.Where("regulation_id = ? AND category_id = ?", req.RegulationID, req.CategoryID).
		First(&existingRelationship).Error; err == nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Relationship already exists",
			Message: "This category is already assigned to the regulation",
		})
		return
	}

	// Create relationship
	relationship := &models.RegulationCategoryAssignment{
		RegulationID: req.RegulationID,
		CategoryID:   req.CategoryID,
	}

	if err := db.Create(relationship).Error; err != nil {
		HandleInternalError(c, "Failed to add regulation category: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Category added to regulation successfully",
		Data: gin.H{
			"regulation_id": req.RegulationID,
			"category_id":   req.CategoryID,
		},
	})
}

// RemoveRegulationCategory removes a category from a regulation
func RemoveRegulationCategory(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	categoryID, valid := ValidateID(c, "category_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Find the relationship
	var relationship models.RegulationCategoryAssignment
	if err := db.Where("regulation_id = ? AND category_id = ?", regulationID, categoryID).
		First(&relationship).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation category relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation category relationship: "+err.Error())
		return
	}

	// Delete the relationship
	if err := db.Delete(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to remove regulation category: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category removed from regulation successfully",
	})
}

// GetCategoryRegulations returns regulations for a category
func GetCategoryRegulations(c *gin.Context) {
	categoryID, valid := ValidateID(c, "category_id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify category exists
	var category models.Category
	if err := db.First(&category, categoryID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Category")
			return
		}
		HandleInternalError(c, "Failed to fetch category: "+err.Error())
		return
	}

	// Get regulations for this category
	var regulations []models.LawsAndRules
	if err := db.Joins("JOIN regulation_category_assignments ON laws_and_rules.id = regulation_category_assignments.regulation_id").
		Where("regulation_category_assignments.category_id = ?", categoryID).
		Find(&regulations).Error; err != nil {
		HandleInternalError(c, "Failed to fetch category regulations: "+err.Error())
		return
	}

	// Convert to response format
	regulationResponses := make([]gin.H, len(regulations))
	for i, regulation := range regulations {
		regulationResponses[i] = gin.H{
			"id":          regulation.ID,
			"title":       regulation.Title,
			"short_title": regulation.ShortTitle,
			"description": regulation.Description,
			"type":        regulation.Type,
			"status":      regulation.Status,
			"created_at":  regulation.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category regulations retrieved successfully",
		Data:    regulationResponses,
	})
}
