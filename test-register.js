// Test script to register a user
const axios = require('axios');

async function registerTestUser() {
  try {
    const response = await axios.post('http://localhost:8080/api/v1/auth/register', {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'Test',
      last_name: 'User',
      title: 'Editor',
      organization: 'Test Organization'
    });
    
    console.log('Registration successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Registration failed:', error.response?.data || error.message);
    return null;
  }
}

async function loginTestUser() {
  try {
    const response = await axios.post('http://localhost:8080/api/v1/auth/login', {
      identifier: 'testuser',
      password: 'password123'
    });
    
    console.log('Login successful:', response.data);
    return response.data;
  } catch (error) {
    console.error('Login failed:', error.response?.data || error.message);
    return null;
  }
}

async function main() {
  console.log('Testing user registration and login...');
  
  // Try to register
  const registerResult = await registerTestUser();
  
  if (registerResult) {
    console.log('Registration successful, now testing login...');
    const loginResult = await loginTestUser();
    
    if (loginResult) {
      console.log('Login successful! Token:', loginResult.token);
    }
  } else {
    console.log('Registration failed, trying to login with existing user...');
    const loginResult = await loginTestUser();
    
    if (loginResult) {
      console.log('Login successful with existing user! Token:', loginResult.token);
    }
  }
}

main();
