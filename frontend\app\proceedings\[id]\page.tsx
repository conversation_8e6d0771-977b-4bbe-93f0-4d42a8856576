'use client'

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  ClockIcon,
  CalendarIcon,
  UserIcon,
  BuildingOfficeIcon,
  TagIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
  PlayIcon,
  PauseIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import ProceedingStepManager from '../../components/Proceeding/ProceedingStepManager';
import ProceedingRelationships from '../../components/Proceeding/ProceedingRelationships';
import apiService from '../../services/api';

interface Proceeding {
  id: number;
  name: string;
  description?: string;
  objective: string;
  status: string;
  priority: string;
  unique_id: string;
  initiation_date: string;
  planned_start_date?: string;
  planned_end_date?: string;
  progress_percent: number;
  total_steps: number;
  completed_steps: number;
  current_step_order?: number;
  owner: {
    id: number;
    username: string;
    email: string;
  };
  agency?: {
    id: number;
    name: string;
  };
  category?: {
    id: number;
    name: string;
  };
  created_at: string;
  updated_at: string;
}

const ProceedingDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user } = useAuthStore();
  const [proceeding, setProceeding] = useState<Proceeding | null>(null);
  const [steps, setSteps] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [stepsLoading, setStepsLoading] = useState(false);
  const [error, setError] = useState('');

  const proceedingId = parseInt(params.id as string);

  useEffect(() => {
    fetchProceeding();
    fetchSteps();
  }, [proceedingId]);

  const fetchProceeding = async () => {
    try {
      setLoading(true);
      const response = await apiService.getProceeding(proceedingId);
      setProceeding(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch proceeding');
    } finally {
      setLoading(false);
    }
  };

  const fetchSteps = async () => {
    try {
      setStepsLoading(true);
      const response = await apiService.getProceedingSteps(proceedingId);
      setSteps(response.data || []);
    } catch (err: any) {
      console.error('Failed to fetch proceeding steps:', err);
      setSteps([]);
    } finally {
      setStepsLoading(false);
    }
  };

  const handleUpdateStepStatus = async (stepId: number, status: string, additionalData?: any) => {
    try {
      const updateData = {
        status,
        ...additionalData
      };

      await apiService.updateStepStatus(proceedingId, stepId, updateData);

      // Refresh both proceeding and steps data to get updated progress
      await Promise.all([fetchProceeding(), fetchSteps()]);

      // Show success message
      console.log(`Step ${stepId} status updated to ${status}`);
    } catch (err: any) {
      console.error('Failed to update step status:', err);
      throw err; // Re-throw to let the component handle the error
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this proceeding?')) return;
    
    try {
      await apiService.deleteProceeding(proceedingId);
      router.push('/proceedings');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete proceeding');
    }
  };

  const canEdit = () => {
    if (!user || !proceeding) return false;
    return user.role === 'admin' || 
           (user.role === 'editor' && proceeding.owner?.id === user.id);
  };

  const canDelete = () => {
    if (!user) return false;
    return user.role === 'admin';
  };

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="h-6 bg-gray-200 rounded mb-4"></div>
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded mb-4 w-2/3"></div>
              <div className="h-32 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !proceeding) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <ExclamationCircleIcon className="h-12 w-12 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Error</h3>
            <p className="text-gray-600 mb-4">{error || 'Proceeding not found'}</p>
            <Link
              href="/proceedings"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Proceedings
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <Link
              href="/proceedings"
              className="mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{proceeding.name}</h1>
              <p className="text-gray-600 mt-1">Proceeding ID: {proceeding.unique_id}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {canEdit() && (
              <Link
                href={`/proceedings/${proceeding.id}/edit`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit
              </Link>
            )}
            {canDelete() && (
              <button
                onClick={handleDelete}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                Delete
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Basic Information */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Overview</h2>
              
              <div className="flex items-center gap-2 mb-4">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(proceeding.status)}`}>
                  {proceeding.status?.toUpperCase() || 'UNKNOWN'}
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(proceeding.priority)}`}>
                  {proceeding.priority?.toUpperCase() || 'UNKNOWN'}
                </span>
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{proceeding.progress_percent}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className="bg-primary-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${proceeding.progress_percent}%` }}
                  ></div>
                </div>
                <div className="text-sm text-gray-500 mt-1">
                  {proceeding.completed_steps} of {proceeding.total_steps} steps completed
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-1">Objective</h3>
                  <p className="text-gray-600">{proceeding.objective}</p>
                </div>
                
                {proceeding.description && (
                  <div>
                    <h3 className="text-sm font-medium text-gray-900 mb-1">Description</h3>
                    <p className="text-gray-600">{proceeding.description}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Step Management */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Steps</h2>
              {stepsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading steps...</span>
                </div>
              ) : (
                <ProceedingStepManager
                  proceedingId={proceeding.id}
                  steps={steps}
                  onUpdateStepStatus={handleUpdateStepStatus}
                  onRefresh={() => {
                    fetchProceeding();
                    fetchSteps();
                  }}
                  canEdit={true}
                />
              )}
            </div>

            {/* Relationships */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Related Items</h2>
              <ProceedingRelationships proceedingId={proceeding.id} />
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Details */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Details</h3>
              <div className="space-y-3">
                {proceeding.owner && (
                  <div className="flex items-center text-sm">
                    <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Owner:</span>
                    <span className="ml-2 font-medium">{proceeding.owner.username}</span>
                  </div>
                )}
                
                {proceeding.agency && (
                  <div className="flex items-center text-sm">
                    <BuildingOfficeIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Agency:</span>
                    <span className="ml-2 font-medium">{proceeding.agency.name}</span>
                  </div>
                )}
                
                {proceeding.category && (
                  <div className="flex items-center text-sm">
                    <TagIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Category:</span>
                    <span className="ml-2 font-medium">{proceeding.category.name}</span>
                  </div>
                )}
                
                <div className="flex items-center text-sm">
                  <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Initiated:</span>
                  <span className="ml-2 font-medium">
                    {new Date(proceeding.initiation_date).toLocaleDateString()}
                  </span>
                </div>
                
                {proceeding.planned_start_date && (
                  <div className="flex items-center text-sm">
                    <PlayIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Planned Start:</span>
                    <span className="ml-2 font-medium">
                      {new Date(proceeding.planned_start_date).toLocaleDateString()}
                    </span>
                  </div>
                )}
                
                {proceeding.planned_end_date && (
                  <div className="flex items-center text-sm">
                    <PauseIcon className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Planned End:</span>
                    <span className="ml-2 font-medium">
                      {new Date(proceeding.planned_end_date).toLocaleDateString()}
                    </span>
                  </div>
                )}
                
                <div className="flex items-center text-sm">
                  <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Created:</span>
                  <span className="ml-2 font-medium">
                    {new Date(proceeding.created_at).toLocaleDateString()}
                  </span>
                </div>
                
                <div className="flex items-center text-sm">
                  <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Updated:</span>
                  <span className="ml-2 font-medium">
                    {new Date(proceeding.updated_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ProceedingDetailPage;
