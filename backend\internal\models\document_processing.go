package models

import (
	"time"

	"gorm.io/gorm"
)

// ProcessingStatus represents the status of document processing
type ProcessingStatus string

const (
	ProcessingStatusPending    ProcessingStatus = "pending"
	ProcessingStatusProcessing ProcessingStatus = "processing"
	ProcessingStatusCompleted  ProcessingStatus = "completed"
	ProcessingStatusFailed     ProcessingStatus = "failed"
	ProcessingStatusSkipped    ProcessingStatus = "skipped"
)

// ProcessingType represents the type of document processing
type ProcessingType string

const (
	ProcessingTypeOCR               ProcessingType = "ocr"
	ProcessingTypeMetadataExtract   ProcessingType = "metadata_extraction"
	ProcessingTypeClassification    ProcessingType = "classification"
	ProcessingTypeEntityExtraction  ProcessingType = "entity_extraction"
	ProcessingTypeLanguageDetect    ProcessingType = "language_detection"
	ProcessingTypeSentimentAnalysis ProcessingType = "sentiment_analysis"
	ProcessingTypeKeywordExtract    ProcessingType = "keyword_extraction"
	ProcessingTypeSummary           ProcessingType = "summary_generation"
)

// OCREngine represents different OCR engines
type OCREngine string

const (
	OCREngineTesseract OCREngine = "tesseract"
	OCREngineAzure     OCREngine = "azure_cognitive"
	OCREngineAWS       OCREngine = "aws_textract"
	OCREngineGoogle    OCREngine = "google_vision"
	OCREngineCustom    OCREngine = "custom"
)

// DocumentProcessingJob represents a document processing job
type DocumentProcessingJob struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Job identification
	JobID    string           `json:"job_id" gorm:"uniqueIndex;not null"`
	Name     string           `json:"name" gorm:"not null"`
	Type     ProcessingType   `json:"type" gorm:"not null"`
	Status   ProcessingStatus `json:"status" gorm:"default:'pending'"`
	Priority int              `json:"priority" gorm:"default:3"` // 1=High, 2=Medium, 3=Low

	// Document relationship
	DocumentID uint          `json:"document_id" gorm:"not null"`
	Document   Document      `json:"document" gorm:"foreignKey:DocumentID"`
	FileID     *uint         `json:"file_id"`
	File       *DocumentFile `json:"file,omitempty" gorm:"foreignKey:FileID"`

	// Processing configuration
	Engine        string `json:"engine"`                         // OCR engine or processing service
	Configuration string `json:"configuration" gorm:"type:text"` // JSON configuration
	InputFormat   string `json:"input_format"`                   // Input file format
	OutputFormat  string `json:"output_format"`                  // Desired output format
	Language      string `json:"language" gorm:"default:'en'"`   // Document language

	// Processing parameters
	OCRConfidence   float64 `json:"ocr_confidence" gorm:"default:0.8"`
	ExtractImages   bool    `json:"extract_images" gorm:"default:false"`
	ExtractTables   bool    `json:"extract_tables" gorm:"default:false"`
	ExtractMetadata bool    `json:"extract_metadata" gorm:"default:true"`
	PreprocessImage bool    `json:"preprocess_image" gorm:"default:true"`

	// Execution tracking
	StartedAt      *time.Time `json:"started_at"`
	CompletedAt    *time.Time `json:"completed_at"`
	ProcessingTime int        `json:"processing_time_ms" gorm:"default:0"`
	RetryCount     int        `json:"retry_count" gorm:"default:0"`
	MaxRetries     int        `json:"max_retries" gorm:"default:3"`

	// Results
	ExtractedText  string  `json:"extracted_text" gorm:"type:text"`
	Confidence     float64 `json:"confidence" gorm:"default:0"`
	PageCount      int     `json:"page_count" gorm:"default:0"`
	WordCount      int     `json:"word_count" gorm:"default:0"`
	CharacterCount int     `json:"character_count" gorm:"default:0"`

	// Error handling
	ErrorMessage string `json:"error_message"`
	ErrorDetails string `json:"error_details" gorm:"type:text"`
	LastError    string `json:"last_error"`

	// Job context
	CreatedByID   uint  `json:"created_by_id" gorm:"not null"`
	CreatedBy     User  `json:"created_by" gorm:"foreignKey:CreatedByID"`
	ProcessedByID *uint `json:"processed_by_id"`
	ProcessedBy   *User `json:"processed_by,omitempty" gorm:"foreignKey:ProcessedByID"`

	// Output data
	OutputData  string `json:"output_data" gorm:"type:text"`  // JSON processing results
	OutputFiles string `json:"output_files" gorm:"type:text"` // JSON array of output file paths

	// Relationships
	ExtractedMetadata []ExtractedMetadata `json:"extracted_metadata,omitempty" gorm:"foreignKey:JobID"`
	ProcessingLogs    []ProcessingLog     `json:"processing_logs,omitempty" gorm:"foreignKey:JobID"`
}

// ExtractedMetadata represents metadata extracted from documents
type ExtractedMetadata struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Metadata identification
	JobID uint                  `json:"job_id" gorm:"not null"`
	Job   DocumentProcessingJob `json:"job" gorm:"foreignKey:JobID"`

	// Document relationship
	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	// Metadata details
	MetadataType string  `json:"metadata_type" gorm:"not null"` // "title", "author", "date", "entity", etc.
	Key          string  `json:"key" gorm:"not null"`           // Metadata key
	Value        string  `json:"value" gorm:"type:text"`        // Metadata value
	Confidence   float64 `json:"confidence" gorm:"default:0"`   // Extraction confidence
	Source       string  `json:"source"`                        // Source of metadata (OCR, file properties, etc.)

	// Location information
	PageNumber *int     `json:"page_number"` // Page where found
	XPosition  *float64 `json:"x_position"`  // X coordinate
	YPosition  *float64 `json:"y_position"`  // Y coordinate
	Width      *float64 `json:"width"`       // Bounding box width
	Height     *float64 `json:"height"`      // Bounding box height

	// Validation and verification
	IsVerified   bool       `json:"is_verified" gorm:"default:false"`
	VerifiedAt   *time.Time `json:"verified_at"`
	VerifiedByID *uint      `json:"verified_by_id"`
	VerifiedBy   *User      `json:"verified_by,omitempty" gorm:"foreignKey:VerifiedByID"`

	// Additional context
	Context  string `json:"context" gorm:"type:text"` // Surrounding text context
	Category string `json:"category"`                 // Metadata category
	Tags     string `json:"tags" gorm:"type:text"`    // JSON array of tags
	Notes    string `json:"notes" gorm:"type:text"`
}

// DocumentClassification represents automatic document classification
type DocumentClassification struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Classification identification
	JobID uint                  `json:"job_id" gorm:"not null"`
	Job   DocumentProcessingJob `json:"job" gorm:"foreignKey:JobID"`

	// Document relationship
	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	// Classification results
	PredictedType     string  `json:"predicted_type" gorm:"not null"`
	Confidence        float64 `json:"confidence" gorm:"not null"`
	PredictedCategory string  `json:"predicted_category"`
	PredictedAgency   string  `json:"predicted_agency"`

	// Classification details
	ClassificationModel string `json:"classification_model"`
	ModelVersion        string `json:"model_version"`
	Features            string `json:"features" gorm:"type:text"`      // JSON extracted features
	Probabilities       string `json:"probabilities" gorm:"type:text"` // JSON class probabilities

	// Alternative predictions
	AlternativePredictions string `json:"alternative_predictions" gorm:"type:text"` // JSON array

	// Validation
	IsAccurate    bool       `json:"is_accurate"`
	ValidatedAt   *time.Time `json:"validated_at"`
	ValidatedByID *uint      `json:"validated_by_id"`
	ValidatedBy   *User      `json:"validated_by,omitempty" gorm:"foreignKey:ValidatedByID"`

	// Feedback for model improvement
	FeedbackProvided bool   `json:"feedback_provided" gorm:"default:false"`
	FeedbackNotes    string `json:"feedback_notes" gorm:"type:text"`
}

// ExtractedEntity represents entities extracted from documents
type ExtractedEntity struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Entity identification
	JobID uint                  `json:"job_id" gorm:"not null"`
	Job   DocumentProcessingJob `json:"job" gorm:"foreignKey:JobID"`

	// Document relationship
	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	// Entity details
	EntityType      string  `json:"entity_type" gorm:"not null"`  // "PERSON", "ORG", "DATE", "MONEY", etc.
	EntityValue     string  `json:"entity_value" gorm:"not null"` // Extracted entity text
	NormalizedValue string  `json:"normalized_value"`             // Normalized/standardized value
	Confidence      float64 `json:"confidence" gorm:"not null"`

	// Location in document
	PageNumber  *int     `json:"page_number"`
	StartOffset int      `json:"start_offset"` // Character offset in text
	EndOffset   int      `json:"end_offset"`   // Character offset in text
	XPosition   *float64 `json:"x_position"`
	YPosition   *float64 `json:"y_position"`
	Width       *float64 `json:"width"`
	Height      *float64 `json:"height"`

	// Context and relationships
	Context         string `json:"context" gorm:"type:text"`          // Surrounding text
	RelatedEntities string `json:"related_entities" gorm:"type:text"` // JSON array of related entities

	// Validation
	IsVerified   bool       `json:"is_verified" gorm:"default:false"`
	VerifiedAt   *time.Time `json:"verified_at"`
	VerifiedByID *uint      `json:"verified_by_id"`
	VerifiedBy   *User      `json:"verified_by,omitempty" gorm:"foreignKey:VerifiedByID"`
}

// ProcessingLog represents logs from document processing operations
type ProcessingLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Log identification
	JobID uint                  `json:"job_id" gorm:"not null"`
	Job   DocumentProcessingJob `json:"job" gorm:"foreignKey:JobID"`

	// Log details
	Level     string `json:"level" gorm:"not null"` // "info", "warning", "error", "debug"
	Message   string `json:"message" gorm:"not null"`
	Component string `json:"component"` // Processing component that logged
	Step      string `json:"step"`      // Processing step

	// Timing
	Timestamp time.Time `json:"timestamp"`
	Duration  int       `json:"duration_ms" gorm:"default:0"` // Step duration in milliseconds

	// Additional context
	Details    string `json:"details" gorm:"type:text"`     // JSON additional details
	StackTrace string `json:"stack_trace" gorm:"type:text"` // Error stack trace if applicable

	// Performance metrics
	MemoryUsage int64   `json:"memory_usage" gorm:"default:0"` // Memory usage in bytes
	CPUUsage    float64 `json:"cpu_usage" gorm:"default:0"`    // CPU usage percentage
}

// ProcessingTemplate represents reusable processing configurations
type ProcessingTemplate struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Template identification
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`
	Category    string `json:"category"`
	Version     string `json:"version" gorm:"default:'1.0'"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// Template configuration
	ProcessingTypes string `json:"processing_types" gorm:"type:text"` // JSON array of processing types
	DefaultEngine   string `json:"default_engine"`
	Configuration   string `json:"configuration" gorm:"type:text"` // JSON configuration

	// File type support
	SupportedFormats string `json:"supported_formats" gorm:"type:text"` // JSON array of supported formats
	MaxFileSize      int64  `json:"max_file_size" gorm:"default:0"`     // Maximum file size in bytes

	// Processing parameters
	DefaultLanguage   string  `json:"default_language" gorm:"default:'en'"`
	DefaultConfidence float64 `json:"default_confidence" gorm:"default:0.8"`
	AutoClassify      bool    `json:"auto_classify" gorm:"default:true"`
	ExtractEntities   bool    `json:"extract_entities" gorm:"default:true"`
	GenerateSummary   bool    `json:"generate_summary" gorm:"default:false"`

	// Template ownership
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Usage statistics
	UsageCount        int        `json:"usage_count" gorm:"default:0"`
	LastUsedAt        *time.Time `json:"last_used_at"`
	SuccessRate       float64    `json:"success_rate" gorm:"default:0"`
	AvgProcessingTime int        `json:"avg_processing_time_ms" gorm:"column:avg_processing_time_ms;default:0"`
}

// TableName returns the table name for DocumentProcessingJob model
func (DocumentProcessingJob) TableName() string {
	return "document_processing_jobs"
}

// TableName returns the table name for ExtractedMetadata model
func (ExtractedMetadata) TableName() string {
	return "extracted_metadata"
}

// TableName returns the table name for DocumentClassification model
func (DocumentClassification) TableName() string {
	return "document_classifications"
}

// TableName returns the table name for ProcessingTemplate model
func (ProcessingTemplate) TableName() string {
	return "processing_templates"
}

// TableName returns the table name for ExtractedEntity model
func (ExtractedEntity) TableName() string {
	return "extracted_entities"
}

// TableName returns the table name for ProcessingLog model
func (ProcessingLog) TableName() string {
	return "processing_logs"
}
