package tests

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

type AnalyticsServiceTestSuite struct {
	suite.Suite
	db               *gorm.DB
	analyticsService *services.AnalyticsService
}

func TestAnalyticsServiceSuite(t *testing.T) {
	suite.Run(t, new(AnalyticsServiceTestSuite))
}

func (suite *AnalyticsServiceTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Set global database
	database.DB = db
	suite.db = db

	// Run migrations
	err = db.AutoMigrate(
		&models.User{},
		&models.Document{},
		&models.Task{},
		&models.Finance{},
		&models.LawsAndRules{},
		&models.Agency{},
	)
	suite.Require().NoError(err)

	// Initialize service
	suite.analyticsService = services.NewAnalyticsService(db)

	// Create test data
	suite.createTestData()
}

func (suite *AnalyticsServiceTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *AnalyticsServiceTestSuite) createTestData() {
	// Create test users with recent login times
	users := []models.User{
		{
			Email:       "<EMAIL>",
			Username:    "user1",
			Role:        models.RoleEditor,
			LastLoginAt: &[]time.Time{time.Now().Add(-2 * time.Hour)}[0],
		},
		{
			Email:       "<EMAIL>",
			Username:    "user2",
			Role:        models.RoleViewer,
			LastLoginAt: &[]time.Time{time.Now().Add(-1 * time.Hour)}[0],
		},
		{
			Email:       "<EMAIL>",
			Username:    "user3",
			Role:        models.RoleAdmin,
			LastLoginAt: &[]time.Time{time.Now().Add(-25 * time.Hour)}[0], // Yesterday
		},
	}

	for _, user := range users {
		suite.db.Create(&user)
	}

	// Create test documents
	documents := []models.Document{
		{
			Title:       "Test Document 1",
			Content:     "Content 1",
			Type:        "regulation",
			Status:      "published",
			CreatedByID: users[0].ID,
		},
		{
			Title:       "Test Document 2",
			Content:     "Content 2",
			Type:        "notice",
			Status:      "draft",
			CreatedByID: users[1].ID,
		},
	}

	for _, doc := range documents {
		suite.db.Create(&doc)
	}

	// Create test tasks
	tasks := []models.Task{
		{
			Title:        "Test Task 1",
			Description:  "Task 1 description",
			Status:       models.TaskStatusCompleted,
			DueDate:      &[]time.Time{time.Now().Add(24 * time.Hour)}[0],
			AssignedToID: &users[0].ID,
		},
		{
			Title:        "Test Task 2",
			Description:  "Task 2 description",
			Status:       models.TaskStatusInProgress,
			DueDate:      &[]time.Time{time.Now().Add(-24 * time.Hour)}[0], // Overdue
			AssignedToID: &users[1].ID,
		},
	}

	for _, task := range tasks {
		suite.db.Create(&task)
	}

	// Create test finance records
	finances := []models.Finance{
		{
			Title:        "Budget Item 1",
			BudgetAmount: 10000.0,
			ActualAmount: 9500.0,
			Category:     "Operations",
		},
		{
			Title:        "Budget Item 2",
			BudgetAmount: 5000.0,
			ActualAmount: 5200.0,
			Category:     "Technology",
		},
	}

	for _, finance := range finances {
		suite.db.Create(&finance)
	}

	// Create test regulations
	regulations := []models.LawsAndRules{
		{
			Title:   "Test Regulation 1",
			Content: "Regulation content 1",
			Status:  "active",
		},
		{
			Title:   "Test Regulation 2",
			Content: "Regulation content 2",
			Status:  "compliant",
		},
	}

	for _, reg := range regulations {
		suite.db.Create(&reg)
	}
}

func (suite *AnalyticsServiceTestSuite) TestGetSystemMetrics() {
	metrics, err := suite.analyticsService.GetSystemMetrics()

	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), metrics)

	// Check that we have the expected metrics
	metricNames := make(map[string]bool)
	for _, metric := range metrics {
		metricNames[metric.Name] = true

		// Verify metric structure
		assert.NotEmpty(suite.T(), metric.Name)
		assert.NotEmpty(suite.T(), metric.Description)
		assert.NotEmpty(suite.T(), metric.MetricType)
		assert.NotEmpty(suite.T(), metric.Category)
		assert.NotEmpty(suite.T(), metric.Unit)
		assert.NotEmpty(suite.T(), metric.Trend)
		assert.True(suite.T(), metric.IsActive)

		// Verify numeric values are reasonable
		assert.GreaterOrEqual(suite.T(), metric.CurrentValue, 0.0)
		assert.GreaterOrEqual(suite.T(), metric.PreviousValue, 0.0)
		assert.GreaterOrEqual(suite.T(), metric.TargetValue, 0.0)
	}

	// Check for expected metric types
	expectedMetrics := []string{
		"Daily Active Users",
		"Document Creation Rate",
		"Average Response Time",
		"Task Completion Rate",
	}

	for _, expected := range expectedMetrics {
		assert.True(suite.T(), metricNames[expected],
			"Should include metric: %s", expected)
	}
}

func (suite *AnalyticsServiceTestSuite) TestGetSystemDashboard() {
	dashboard, err := suite.analyticsService.GetSystemDashboard()

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), dashboard)

	// Verify dashboard structure
	assert.Equal(suite.T(), uint(1), dashboard.ID)
	assert.Equal(suite.T(), "System Overview Dashboard", dashboard.Name)
	assert.Equal(suite.T(), "system", dashboard.DashboardType)
	assert.True(suite.T(), dashboard.IsPublic)
	assert.NotEmpty(suite.T(), dashboard.Widgets)

	// Verify widgets
	for _, widget := range dashboard.Widgets {
		assert.NotEmpty(suite.T(), widget.ID)
		assert.NotEmpty(suite.T(), widget.Type)
		assert.NotEmpty(suite.T(), widget.Title)
		assert.NotNil(suite.T(), widget.Data)
		assert.NotNil(suite.T(), widget.Config)
		assert.NotNil(suite.T(), widget.Position)
	}
}

func (suite *AnalyticsServiceTestSuite) TestGetExecutiveDashboard() {
	dashboard, err := suite.analyticsService.GetExecutiveDashboard()

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), dashboard)

	// Verify executive dashboard specifics
	assert.Equal(suite.T(), uint(2), dashboard.ID)
	assert.Equal(suite.T(), "Executive Dashboard", dashboard.Name)
	assert.Equal(suite.T(), "executive", dashboard.DashboardType)
	assert.False(suite.T(), dashboard.IsPublic) // Executive dashboard should be restricted
	assert.NotEmpty(suite.T(), dashboard.Widgets)

	// Verify executive-specific widgets
	for _, widget := range dashboard.Widgets {
		assert.Equal(suite.T(), "kpi_card", widget.Type)
		assert.Contains(suite.T(), widget.ID, "exec_widget_")

		// Verify executive widget data structure
		data := widget.Data.(map[string]interface{})
		assert.Contains(suite.T(), data, "value")
		assert.Contains(suite.T(), data, "target")
		assert.Contains(suite.T(), data, "description")
	}
}

func (suite *AnalyticsServiceTestSuite) TestGetOperationalDashboard() {
	dashboard, err := suite.analyticsService.GetOperationalDashboard()

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), dashboard)

	// Verify operational dashboard specifics
	assert.Equal(suite.T(), uint(3), dashboard.ID)
	assert.Equal(suite.T(), "Operational Dashboard", dashboard.Name)
	assert.Equal(suite.T(), "operational", dashboard.DashboardType)
	assert.True(suite.T(), dashboard.IsPublic)
	assert.NotEmpty(suite.T(), dashboard.Widgets)

	// Should have metric widgets plus health widget
	metricWidgets := 0
	healthWidgets := 0

	for _, widget := range dashboard.Widgets {
		if widget.Type == "metric_chart" {
			metricWidgets++
		} else if widget.Type == "health_status" {
			healthWidgets++
		}
	}

	assert.Greater(suite.T(), metricWidgets, 0, "Should have metric widgets")
	assert.Equal(suite.T(), 1, healthWidgets, "Should have one health widget")
}

func (suite *AnalyticsServiceTestSuite) TestGetFinancialDashboard() {
	dashboard, err := suite.analyticsService.GetFinancialDashboard()

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), dashboard)

	// Verify financial dashboard specifics
	assert.Equal(suite.T(), uint(4), dashboard.ID)
	assert.Equal(suite.T(), "Financial Dashboard", dashboard.Name)
	assert.Equal(suite.T(), "financial", dashboard.DashboardType)
	assert.False(suite.T(), dashboard.IsPublic) // Financial should be restricted
	assert.NotEmpty(suite.T(), dashboard.Widgets)

	// Should have financial widgets plus budget breakdown
	financialWidgets := 0
	budgetWidgets := 0

	for _, widget := range dashboard.Widgets {
		if widget.Type == "financial_card" {
			financialWidgets++
		} else if widget.Type == "budget_breakdown" {
			budgetWidgets++
		}
	}

	assert.Greater(suite.T(), financialWidgets, 0, "Should have financial widgets")
	assert.Equal(suite.T(), 1, budgetWidgets, "Should have one budget breakdown widget")
}

func (suite *AnalyticsServiceTestSuite) TestGetComplianceDashboard() {
	dashboard, err := suite.analyticsService.GetComplianceDashboard()

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), dashboard)

	// Verify compliance dashboard specifics
	assert.Equal(suite.T(), uint(5), dashboard.ID)
	assert.Equal(suite.T(), "Compliance Dashboard", dashboard.Name)
	assert.Equal(suite.T(), "compliance", dashboard.DashboardType)
	assert.False(suite.T(), dashboard.IsPublic) // Compliance should be restricted
	assert.NotEmpty(suite.T(), dashboard.Widgets)

	// Should have compliance widgets plus timeline
	complianceWidgets := 0
	timelineWidgets := 0

	for _, widget := range dashboard.Widgets {
		if widget.Type == "compliance_gauge" {
			complianceWidgets++
		} else if widget.Type == "compliance_timeline" {
			timelineWidgets++
		}
	}

	assert.Greater(suite.T(), complianceWidgets, 0, "Should have compliance widgets")
	assert.Equal(suite.T(), 1, timelineWidgets, "Should have one timeline widget")
}

func (suite *AnalyticsServiceTestSuite) TestGetDashboardByType() {
	testCases := []struct {
		name          string
		dashboardType string
		expectedID    uint
		expectedName  string
	}{
		{
			name:          "System dashboard",
			dashboardType: "system",
			expectedID:    1,
			expectedName:  "System Overview Dashboard",
		},
		{
			name:          "Executive dashboard",
			dashboardType: "executive",
			expectedID:    2,
			expectedName:  "Executive Dashboard",
		},
		{
			name:          "Operational dashboard",
			dashboardType: "operational",
			expectedID:    3,
			expectedName:  "Operational Dashboard",
		},
		{
			name:          "Financial dashboard",
			dashboardType: "financial",
			expectedID:    4,
			expectedName:  "Financial Dashboard",
		},
		{
			name:          "Compliance dashboard",
			dashboardType: "compliance",
			expectedID:    5,
			expectedName:  "Compliance Dashboard",
		},
		{
			name:          "Invalid type defaults to system",
			dashboardType: "invalid",
			expectedID:    1,
			expectedName:  "System Overview Dashboard",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			dashboard, err := suite.analyticsService.GetDashboardByType(tc.dashboardType)

			assert.NoError(suite.T(), err)
			assert.NotNil(suite.T(), dashboard)
			assert.Equal(suite.T(), tc.expectedID, dashboard.ID)
			assert.Equal(suite.T(), tc.expectedName, dashboard.Name)
		})
	}
}

func (suite *AnalyticsServiceTestSuite) TestGetAllDashboardTypes() {
	dashboardTypes := suite.analyticsService.GetAllDashboardTypes()

	assert.NotEmpty(suite.T(), dashboardTypes)
	assert.Len(suite.T(), dashboardTypes, 5) // Should have 5 dashboard types

	// Verify structure of dashboard types
	expectedTypes := []string{"system", "executive", "operational", "financial", "compliance"}
	foundTypes := make(map[string]bool)

	for _, dashboardType := range dashboardTypes {
		// Verify required fields
		assert.Contains(suite.T(), dashboardType, "id")
		assert.Contains(suite.T(), dashboardType, "name")
		assert.Contains(suite.T(), dashboardType, "description")
		assert.Contains(suite.T(), dashboardType, "category")
		assert.Contains(suite.T(), dashboardType, "icon")
		assert.Contains(suite.T(), dashboardType, "access")

		// Track found types
		id := dashboardType["id"].(string)
		foundTypes[id] = true

		// Verify access levels
		access := dashboardType["access"].(string)
		assert.True(suite.T(), access == "public" || access == "restricted")
	}

	// Verify all expected types are present
	for _, expectedType := range expectedTypes {
		assert.True(suite.T(), foundTypes[expectedType],
			"Should include dashboard type: %s", expectedType)
	}
}

func (suite *AnalyticsServiceTestSuite) TestMetricsIntegration() {
	// Test that metrics are properly calculated and integrated
	metrics, err := suite.analyticsService.GetSystemMetrics()
	assert.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), metrics)

	// Verify that metrics contain real data based on our test setup
	for _, metric := range metrics {
		// All metrics should have valid values
		assert.GreaterOrEqual(suite.T(), metric.CurrentValue, 0.0)
		assert.GreaterOrEqual(suite.T(), metric.PreviousValue, 0.0)
		assert.GreaterOrEqual(suite.T(), metric.TargetValue, 0.0)

		// Verify trend calculation
		assert.Contains(suite.T(), []string{"up", "down", "stable"}, metric.Trend)

		// Verify thresholds are set
		assert.Greater(suite.T(), metric.ThresholdWarning, 0.0)
		assert.Greater(suite.T(), metric.ThresholdCritical, 0.0)
	}
}
