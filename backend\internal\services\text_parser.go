package services

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"federal-register-clone/internal/models"
)

// ParsedItem represents an item extracted from text
type ParsedItem struct {
	Type        string                 `json:"type"`   // "task", "agency", "category", "date"
	Action      string                 `json:"action"` // "create", "add", "schedule"
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	DueDate     *time.Time             `json:"due_date,omitempty"`
	StartDate   *time.Time             `json:"start_date,omitempty"`
	EndDate     *time.Time             `json:"end_date,omitempty"`
	Duration    *int                   `json:"duration,omitempty"` // in minutes
	Priority    models.TaskPriority    `json:"priority"`
	TaskType    models.TaskType        `json:"task_type"`
	SourceText  string                 `json:"source_text"`
	Confidence  float64                `json:"confidence"` // 0.0 to 1.0
	Metadata    map[string]interface{} `json:"metadata"`
}

// TextParserService handles intelligent text parsing
type TextParserService struct {
	// Date patterns
	datePatterns []DatePattern
	// Time patterns
	timePatterns []TimePattern
	// Task patterns
	taskPatterns []TaskPattern
	// Entity patterns
	entityPatterns []EntityPattern
}

type DatePattern struct {
	Pattern     *regexp.Regexp
	Handler     func(matches []string, baseTime time.Time) (*time.Time, error)
	Description string
	Confidence  float64
}

type TimePattern struct {
	Pattern     *regexp.Regexp
	Handler     func(matches []string) (int, error) // returns duration in minutes
	Description string
	Confidence  float64
}

type TaskPattern struct {
	Pattern     *regexp.Regexp
	TaskType    models.TaskType
	Priority    models.TaskPriority
	Description string
	Confidence  float64
}

type EntityPattern struct {
	Pattern     *regexp.Regexp
	EntityType  string // "agency", "category"
	Handler     func(matches []string) (string, map[string]interface{})
	Description string
	Confidence  float64
}

// NewTextParserService creates a new text parser service
func NewTextParserService() *TextParserService {
	service := &TextParserService{}
	service.initializePatterns()
	return service
}

// ParseText analyzes text and extracts actionable items
func (s *TextParserService) ParseText(text string, baseTime time.Time) ([]ParsedItem, error) {
	var items []ParsedItem

	// Normalize text
	normalizedText := strings.ToLower(strings.TrimSpace(text))

	// Parse dates and times
	dateItems := s.parseDates(normalizedText, baseTime)
	items = append(items, dateItems...)

	// Parse tasks and actions
	taskItems := s.parseTasks(normalizedText, baseTime)
	items = append(items, taskItems...)

	// Parse entity references
	entityItems := s.parseEntities(normalizedText)
	items = append(items, entityItems...)

	// Merge related items (e.g., combine date with task)
	mergedItems := s.mergeRelatedItems(items, normalizedText)

	return mergedItems, nil
}

// initializePatterns sets up all the regex patterns and handlers
func (s *TextParserService) initializePatterns() {
	s.initializeDatePatterns()
	s.initializeTimePatterns()
	s.initializeTaskPatterns()
	s.initializeEntityPatterns()
}

// initializeDatePatterns sets up date parsing patterns
func (s *TextParserService) initializeDatePatterns() {
	s.datePatterns = []DatePattern{
		// Relative dates
		{
			Pattern: regexp.MustCompile(`(\d+)\s+days?\s+from\s+(now|today)`),
			Handler: func(matches []string, baseTime time.Time) (*time.Time, error) {
				days, err := strconv.Atoi(matches[1])
				if err != nil {
					return nil, err
				}
				result := baseTime.AddDate(0, 0, days)
				return &result, nil
			},
			Description: "X days from now/today",
			Confidence:  0.9,
		},
		{
			Pattern: regexp.MustCompile(`(\d+)\s+weeks?\s+from\s+(now|today)`),
			Handler: func(matches []string, baseTime time.Time) (*time.Time, error) {
				weeks, err := strconv.Atoi(matches[1])
				if err != nil {
					return nil, err
				}
				result := baseTime.AddDate(0, 0, weeks*7)
				return &result, nil
			},
			Description: "X weeks from now/today",
			Confidence:  0.9,
		},
		{
			Pattern: regexp.MustCompile(`(\d+)\s+months?\s+from\s+(now|today)`),
			Handler: func(matches []string, baseTime time.Time) (*time.Time, error) {
				months, err := strconv.Atoi(matches[1])
				if err != nil {
					return nil, err
				}
				result := baseTime.AddDate(0, months, 0)
				return &result, nil
			},
			Description: "X months from now/today",
			Confidence:  0.9,
		},
		// Specific dates
		{
			Pattern: regexp.MustCompile(`(\d{4})-(\d{1,2})-(\d{1,2})`),
			Handler: func(matches []string, baseTime time.Time) (*time.Time, error) {
				year, _ := strconv.Atoi(matches[1])
				month, _ := strconv.Atoi(matches[2])
				day, _ := strconv.Atoi(matches[3])
				result := time.Date(year, time.Month(month), day, 9, 0, 0, 0, baseTime.Location())
				return &result, nil
			},
			Description: "YYYY-MM-DD format",
			Confidence:  0.95,
		},
		// "no later than X days"
		{
			Pattern: regexp.MustCompile(`no\s+later\s+than\s+(\d+)\s+days?\s+from\s+(now|today)`),
			Handler: func(matches []string, baseTime time.Time) (*time.Time, error) {
				days, err := strconv.Atoi(matches[1])
				if err != nil {
					return nil, err
				}
				result := baseTime.AddDate(0, 0, days)
				return &result, nil
			},
			Description: "no later than X days from now",
			Confidence:  0.85,
		},
	}
}

// initializeTimePatterns sets up time duration patterns
func (s *TextParserService) initializeTimePatterns() {
	s.timePatterns = []TimePattern{
		{
			Pattern: regexp.MustCompile(`(\d+)\s+hours?\s+limit`),
			Handler: func(matches []string) (int, error) {
				hours, err := strconv.Atoi(matches[1])
				if err != nil {
					return 0, err
				}
				return hours * 60, nil // convert to minutes
			},
			Description: "X hours limit",
			Confidence:  0.8,
		},
		{
			Pattern: regexp.MustCompile(`(\d+)\s+minutes?\s+limit`),
			Handler: func(matches []string) (int, error) {
				minutes, err := strconv.Atoi(matches[1])
				if err != nil {
					return 0, err
				}
				return minutes, nil
			},
			Description: "X minutes limit",
			Confidence:  0.8,
		},
	}
}

// initializeTaskPatterns sets up task identification patterns
func (s *TextParserService) initializeTaskPatterns() {
	s.taskPatterns = []TaskPattern{
		{
			Pattern:     regexp.MustCompile(`(review\s+report|report\s+review)\s+should\s+be\s+(posted|published|submitted)`),
			TaskType:    models.TaskTypeReview,
			Priority:    models.TaskPriorityMedium,
			Description: "Review report task",
			Confidence:  0.85,
		},
		{
			Pattern:     regexp.MustCompile(`(comment\s+period|public\s+comment)\s+(ends|closes|deadline)`),
			TaskType:    models.TaskTypeComment,
			Priority:    models.TaskPriorityHigh,
			Description: "Comment period deadline",
			Confidence:  0.9,
		},
		{
			Pattern:     regexp.MustCompile(`(public\s+hearing|hearing)\s+(scheduled|planned)`),
			TaskType:    models.TaskTypeHearing,
			Priority:    models.TaskPriorityHigh,
			Description: "Public hearing event",
			Confidence:  0.9,
		},
		{
			Pattern:     regexp.MustCompile(`(deadline|due\s+date|must\s+be\s+completed)`),
			TaskType:    models.TaskTypeDeadline,
			Priority:    models.TaskPriorityHigh,
			Description: "General deadline",
			Confidence:  0.7,
		},
	}
}

// initializeEntityPatterns sets up entity reference patterns
func (s *TextParserService) initializeEntityPatterns() {
	s.entityPatterns = []EntityPattern{
		{
			Pattern:    regexp.MustCompile(`add\s+category\s+([a-zA-Z\s]+)`),
			EntityType: "category",
			Handler: func(matches []string) (string, map[string]interface{}) {
				name := strings.TrimSpace(matches[1])
				return name, map[string]interface{}{
					"action": "create",
					"name":   name,
				}
			},
			Description: "Add category reference",
			Confidence:  0.8,
		},
		{
			Pattern:    regexp.MustCompile(`add\s+agency\s+([a-zA-Z\s]+)`),
			EntityType: "agency",
			Handler: func(matches []string) (string, map[string]interface{}) {
				name := strings.TrimSpace(matches[1])
				return name, map[string]interface{}{
					"action": "create",
					"name":   name,
				}
			},
			Description: "Add agency reference",
			Confidence:  0.8,
		},
	}
}

// parseDates extracts date-related information from text
func (s *TextParserService) parseDates(text string, baseTime time.Time) []ParsedItem {
	var items []ParsedItem

	for _, pattern := range s.datePatterns {
		matches := pattern.Pattern.FindAllStringSubmatch(text, -1)
		for _, match := range matches {
			if date, err := pattern.Handler(match, baseTime); err == nil {
				item := ParsedItem{
					Type:        "date",
					Action:      "schedule",
					DueDate:     date,
					SourceText:  match[0],
					Confidence:  pattern.Confidence,
					Description: pattern.Description,
					Metadata: map[string]interface{}{
						"pattern_type": pattern.Description,
					},
				}
				items = append(items, item)
			}
		}
	}

	return items
}

// parseTasks extracts task-related information from text
func (s *TextParserService) parseTasks(text string, baseTime time.Time) []ParsedItem {
	var items []ParsedItem

	for _, pattern := range s.taskPatterns {
		matches := pattern.Pattern.FindAllStringSubmatch(text, -1)
		for _, match := range matches {
			// Extract title from the match
			title := s.extractTaskTitle(match[0])

			item := ParsedItem{
				Type:        "task",
				Action:      "create",
				Title:       title,
				Description: fmt.Sprintf("Task extracted from text: %s", match[0]),
				TaskType:    pattern.TaskType,
				Priority:    pattern.Priority,
				SourceText:  match[0],
				Confidence:  pattern.Confidence,
				Metadata: map[string]interface{}{
					"pattern_type": pattern.Description,
					"task_type":    string(pattern.TaskType),
				},
			}
			items = append(items, item)
		}
	}

	return items
}

// parseEntities extracts entity references from text
func (s *TextParserService) parseEntities(text string) []ParsedItem {
	var items []ParsedItem

	for _, pattern := range s.entityPatterns {
		matches := pattern.Pattern.FindAllStringSubmatch(text, -1)
		for _, match := range matches {
			name, metadata := pattern.Handler(match)

			item := ParsedItem{
				Type:        pattern.EntityType,
				Action:      "create",
				Title:       name,
				Description: fmt.Sprintf("Create %s: %s", pattern.EntityType, name),
				SourceText:  match[0],
				Confidence:  pattern.Confidence,
				Metadata:    metadata,
			}
			items = append(items, item)
		}
	}

	return items
}

// mergeRelatedItems combines related parsed items
func (s *TextParserService) mergeRelatedItems(items []ParsedItem, text string) []ParsedItem {
	var merged []ParsedItem

	// Group items by proximity in text
	for i, item := range items {
		if item.Type == "task" {
			// Look for nearby date items
			for j, dateItem := range items {
				if j != i && dateItem.Type == "date" {
					// Check if they're close in the text
					if s.areItemsRelated(item.SourceText, dateItem.SourceText, text) {
						// Merge date into task
						item.DueDate = dateItem.DueDate
						item.StartDate = dateItem.StartDate
						item.EndDate = dateItem.EndDate
						item.Confidence = (item.Confidence + dateItem.Confidence) / 2
						break
					}
				}
			}
			merged = append(merged, item)
		} else if item.Type == "date" {
			// Check if this date wasn't already merged with a task
			alreadyMerged := false
			for _, mergedItem := range merged {
				if mergedItem.Type == "task" && s.areItemsRelated(item.SourceText, mergedItem.SourceText, text) {
					alreadyMerged = true
					break
				}
			}
			if !alreadyMerged {
				// Convert standalone date to a general task
				item.Type = "task"
				item.TaskType = models.TaskTypeGeneral
				item.Priority = models.TaskPriorityMedium
				if item.Title == "" {
					item.Title = "Scheduled event"
				}
				merged = append(merged, item)
			}
		} else {
			// Entity items (agency, category) are kept as-is
			merged = append(merged, item)
		}
	}

	return merged
}

// areItemsRelated checks if two text snippets are related based on proximity
func (s *TextParserService) areItemsRelated(text1, text2, fullText string) bool {
	index1 := strings.Index(fullText, text1)
	index2 := strings.Index(fullText, text2)

	if index1 == -1 || index2 == -1 {
		return false
	}

	// Consider items related if they're within 100 characters of each other
	distance := index1 - index2
	if distance < 0 {
		distance = -distance
	}

	return distance <= 100
}

// extractTaskTitle extracts a meaningful title from task text
func (s *TextParserService) extractTaskTitle(text string) string {
	// Remove common prefixes and suffixes
	title := strings.TrimSpace(text)

	// Capitalize first letter
	if len(title) > 0 {
		title = strings.ToUpper(string(title[0])) + title[1:]
	}

	// Limit length
	if len(title) > 100 {
		title = title[:97] + "..."
	}

	return title
}
