package api

import (
	"federal-register-clone/internal/api/handlers"
	"federal-register-clone/internal/api/middleware"
	"federal-register-clone/internal/config"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
)

// SetupRouter sets up the Gin router with all routes and middleware
func SetupRouter(cfg *config.Config) *gin.Engine {
	router := gin.New()

	// Global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS(cfg.CORS))
	// Apply more generous rate limiting globally
	router.Use(middleware.CustomRateLimit(1000, 100)) // 1000 requests per minute, burst of 100

	// Health check endpoint
	router.GET("/health", handlers.HealthCheck)

	// Swagger documentation
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication routes (no auth required)
		auth := v1.Group("/auth")
		{
			auth.POST("/login", handlers.Login)
			auth.POST("/register", handlers.Register)
			auth.POST("/refresh", handlers.RefreshToken)
			auth.POST("/logout", middleware.AuthRequired(), handlers.Logout)
			auth.POST("/forgot-password", handlers.ForgotPassword)
			auth.POST("/reset-password", handlers.ResetPassword)

			// Protected auth routes
			auth.GET("/me", middleware.AuthRequired(), handlers.GetCurrentUser)
			auth.PUT("/profile", middleware.AuthRequired(), handlers.UpdateProfile)
			auth.GET("/permissions", middleware.AuthRequired(), handlers.GetUserPermissions)
			auth.GET("/stats", middleware.AuthRequired(), handlers.GetUserStats)
			auth.POST("/change-password", middleware.AuthRequired(), handlers.ChangePassword)
		}

		// Public routes (no auth required)
		public := v1.Group("/public")
		{
			// Public document access
			public.GET("/documents", handlers.GetPublicDocuments)
			public.GET("/documents/:id", handlers.GetPublicDocument)
			public.GET("/documents/search", handlers.SearchPublicDocuments)

			// Public statistics
			public.GET("/stats", handlers.GetPublicStats)

			// Public agency information
			public.GET("/agencies", handlers.GetPublicAgencies)
			public.GET("/agencies/:id", handlers.GetPublicAgency)
			public.GET("/agencies/:id/documents", handlers.GetAgencyDocuments)

			// Public categories and subjects
			public.GET("/categories", handlers.GetPublicCategories)
			public.GET("/categories/:id", handlers.GetPublicCategory)
			public.GET("/categories/:id/documents", handlers.GetCategoryDocuments)
			public.GET("/subjects", handlers.GetPublicSubjects)
			public.GET("/subjects/:id/documents", handlers.GetSubjectDocuments)
			// Public calendar
			public.GET("/calendar", handlers.GetCalendar)
			public.GET("/calendar/stats", handlers.GetCalendarStats)

			// Public tags
			public.GET("/tags", handlers.GetPublicTags)
			public.GET("/tags/:id/documents", handlers.GetTagDocuments)

			// Document files (public access)
			public.GET("/documents/:id/files", handlers.GetDocumentFiles)
			public.GET("/files/:id", handlers.GetDocumentFile)
			public.GET("/files/:id/download", handlers.DownloadFile)

			// Public comments
			public.POST("/documents/:id/comments", handlers.CreatePublicComment)
			public.GET("/documents/:id/comments", handlers.GetDocumentComments)

			// Public regulations
			public.GET("/regulations", handlers.GetPublicRegulations)
			public.GET("/regulations/:id", handlers.GetPublicRegulation)
			public.GET("/regulations/:id/versions", handlers.GetPublicRegulationVersions)

			// Public summaries
			public.GET("/summaries", handlers.GetPublicSummaries)
		}

		// General search endpoint (accessible to both authenticated and unauthenticated users)
		v1.GET("/search", handlers.GeneralSearch)

		// Protected routes (auth required)
		protected := v1.Group("/")
		protected.Use(middleware.AuthRequired())
		{
			// Preloading endpoints for form defaults
			preloading := protected.Group("/preloading")
			{
				preloading.GET("/documents", handlers.GetDocumentDefaults)
				preloading.GET("/regulations", handlers.GetRegulationDefaults)
				preloading.GET("/agencies", handlers.GetAgencyDefaults)
				preloading.GET("/categories", handlers.GetCategoryDefaults)
				preloading.GET("/proceedings", handlers.GetProceedingDefaults)
				preloading.GET("/finances", handlers.GetFinanceDefaults)
				preloading.GET("/slug", handlers.GenerateSlug)
				preloading.GET("/fr-number", handlers.GenerateFRNumber)
				preloading.GET("/docket-number", handlers.GenerateDocketNumber)
				preloading.GET("/public-law-number", handlers.GetPublicLawNumber)
				preloading.GET("/regulatory-identifier", handlers.GetRegulatoryIdentifier)
				preloading.GET("/regulation-docket-number", handlers.GetRegulationDocketNumber)
			}
			// Document management
			documents := protected.Group("/documents")
			{
				documents.GET("", handlers.GetDocuments)
				documents.POST("", middleware.RoleRequired("editor"), handlers.CreateDocument)
				documents.GET("/:id", handlers.GetDocument)
				documents.POST("/:id/view", handlers.TrackDocumentView)
				documents.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateDocument)
				documents.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteDocument)
				documents.GET("/search", handlers.SearchDocuments)

				// Document workflow
				documents.POST("/:id/submit", middleware.RoleRequired("editor"), handlers.SubmitDocument)
				documents.POST("/:id/approve", middleware.RoleRequired("reviewer"), handlers.ApproveDocument)
				documents.POST("/:id/publish", middleware.RoleRequired("publisher"), handlers.PublishDocument)
				documents.POST("/:id/withdraw", middleware.RoleRequired("admin"), handlers.WithdrawDocument)

				// Document files
				documents.GET("/:id/files", handlers.GetDocumentFiles)
				documents.POST("/:id/files", middleware.RoleRequired("editor"), handlers.UploadDocumentFile)
				documents.PUT("/files/:id", middleware.RoleRequired("editor"), handlers.UpdateDocumentFile)
				documents.DELETE("/files/:fileId", middleware.RoleRequired("editor"), handlers.DeleteDocumentFile)

				// Document reviews
				documents.POST("/:id/reviews", middleware.RoleRequired("reviewer"), handlers.CreateDocumentReview)
				documents.GET("/:id/reviews", handlers.GetDocumentReviews)

				// Document relationships
				documents.GET("/:id/categories", handlers.GetDocumentCategoryRelationships)
				documents.GET("/:id/agency", handlers.GetDocumentAgencyRelationships)

				// Document comments
				documents.POST("/:id/comments", handlers.CreateComment)
				documents.GET("/:id/comments", handlers.GetDocumentComments)

				// Document versions
				documents.GET("/:id/versions", handlers.GetDocumentVersions)
				documents.POST("/:id/versions", middleware.RoleRequired("editor"), handlers.CreateDocumentVersion)

				// Document regulation relationships
				documents.GET("/:id/regulations", handlers.GetDocumentRegulations)
			}

			// Document review management (individual reviews)
			reviews := protected.Group("/reviews")
			{
				reviews.GET("/:id", middleware.RoleRequired("reviewer"), handlers.GetDocumentReview)
				reviews.PUT("/:id", middleware.RoleRequired("reviewer"), handlers.UpdateDocumentReview)
				reviews.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteDocumentReview)
			}

			// Document version management (individual versions)
			versions := protected.Group("/versions")
			{
				versions.GET("/:id", handlers.GetDocumentVersion)
				versions.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateDocumentVersion)
				versions.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteDocumentVersion)
			}

			// Agency management
			agencies := protected.Group("/agencies")
			{
				agencies.GET("", handlers.GetAgencies)
				agencies.POST("", middleware.RoleRequired("admin"), handlers.CreateAgency)
				agencies.GET("/:id", handlers.GetAgency)
				agencies.PUT("/:id", middleware.RoleRequired("admin"), handlers.UpdateAgency)
				agencies.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteAgency)

				// Agency documents
				agencies.GET("/:id/documents", handlers.GetAgencyDocuments)

				// Agency contacts
				agencies.POST("/:id/contacts", middleware.RoleRequired("admin"), handlers.CreateAgencyContact)
				agencies.GET("/:id/contacts", handlers.GetAgencyContacts)
				agencies.GET("/contacts/:contactId", handlers.GetAgencyContact)
				agencies.PUT("/contacts/:contactId", middleware.RoleRequired("admin"), handlers.UpdateAgencyContact)
				agencies.DELETE("/contacts/:contactId", middleware.RoleRequired("admin"), handlers.DeleteAgencyContact)

				// Agency regulation relationships
				agencies.GET("/:id/regulations", handlers.GetAgencyRegulations)
			}

			// Category management
			categories := protected.Group("/categories")
			{
				categories.GET("", handlers.GetCategories)
				categories.POST("", middleware.RoleRequired("editor"), handlers.CreateCategory)
				categories.GET("/:id", handlers.GetCategory)
				categories.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateCategory)
				categories.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteCategory)

				// Category documents
				categories.GET("/:id/documents", handlers.GetCategoryDocuments)

				// Category regulation relationships
				categories.GET("/:id/regulations", handlers.GetCategoryRegulations)
			}

			// Tag management
			tags := protected.Group("/tags")
			{
				tags.GET("", handlers.GetTags)
				tags.POST("", middleware.RoleRequired("editor"), handlers.CreateTag)
				tags.GET("/:id", handlers.GetTag)
				tags.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateTag)
				tags.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteTag)
			}

			// Subject management
			subjects := protected.Group("/subjects")
			{
				subjects.GET("", handlers.GetSubjects)
				subjects.POST("", middleware.RoleRequired("admin"), handlers.CreateSubject)
				subjects.GET("/:id", handlers.GetSubject)
				subjects.PUT("/:id", middleware.RoleRequired("admin"), handlers.UpdateSubject)
				subjects.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteSubject)
			}

			// User management
			users := protected.Group("/users")
			{
				users.GET("", middleware.RoleRequired("admin"), handlers.GetUsers)
				users.POST("", middleware.RoleRequired("admin"), handlers.CreateUser)
				users.GET("/:id", middleware.RoleRequired("admin"), handlers.GetUser)
				users.PUT("/:id", middleware.RoleRequired("admin"), handlers.UpdateUser)
				users.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteUser)

				// User preferences
				users.GET("/:id/preferences", handlers.GetUserPreferences)
				users.PUT("/:id/preferences", handlers.UpdateUserPreferences)
			}

			// User settings and preferences (current user)
			userSettings := protected.Group("/user")
			{
				userSettings.GET("/settings", handlers.GetUserSettings)
				userSettings.PUT("/settings", handlers.UpdateUserSettings)
				userSettings.GET("/preferences", handlers.GetCurrentUserPreferences)
				userSettings.PUT("/preferences", handlers.UpdateCurrentUserPreferences)
				userSettings.GET("/dashboard-stats", handlers.GetUserStats)
			}

			// Contact and system endpoints
			protected.POST("/contact", handlers.SubmitContactForm)
			protected.GET("/system/status", handlers.GetSystemStatus)

			// Module interconnection endpoints
			interconnect := protected.Group("/interconnect")
			{
				// Document-Enterprise connections
				interconnect.GET("/documents/:id/finance", handlers.GetDocumentFinanceConnections)
				interconnect.POST("/documents/:id/finance", handlers.CreateDocumentFinanceConnection)
				interconnect.GET("/documents/:id/proceedings", handlers.GetDocumentProceedingConnections)
				interconnect.POST("/documents/:id/proceedings", handlers.CreateDocumentProceedingConnection)
				interconnect.GET("/documents/:id/tasks", handlers.GetDocumentTaskConnections)
				interconnect.POST("/documents/:id/tasks", handlers.CreateDocumentTaskConnection)

				// Regulation-Enterprise connections
				interconnect.GET("/regulations/:id/finance", handlers.GetRegulationFinanceConnections)
				interconnect.POST("/regulations/:id/finance", handlers.CreateRegulationFinanceConnection)
				interconnect.GET("/regulations/:id/proceedings", handlers.GetRegulationProceedingConnections)
				interconnect.POST("/regulations/:id/proceedings", handlers.CreateRegulationProceedingConnection)
				interconnect.GET("/regulations/:id/tasks", handlers.GetRegulationTaskConnections)
				interconnect.POST("/regulations/:id/tasks", handlers.CreateRegulationTaskConnection)

				// Agency-Enterprise connections
				interconnect.GET("/agencies/:id/finance", handlers.GetAgencyFinanceConnections)
				interconnect.POST("/agencies/:id/finance", handlers.CreateAgencyFinanceConnection)
				interconnect.GET("/agencies/:id/hr", handlers.GetAgencyHRConnections)
				interconnect.POST("/agencies/:id/hr", handlers.CreateAgencyHRConnection)
				interconnect.GET("/agencies/:id/bi", handlers.GetAgencyBIConnections)
				interconnect.POST("/agencies/:id/bi", handlers.CreateAgencyBIConnection)

				// Category-Enterprise connections
				interconnect.GET("/categories/:id/finance", handlers.GetCategoryFinanceConnections)
				interconnect.POST("/categories/:id/finance", handlers.CreateCategoryFinanceConnection)
				interconnect.GET("/categories/:id/tasks", handlers.GetCategoryTaskConnections)
				interconnect.POST("/categories/:id/tasks", handlers.CreateCategoryTaskConnection)
			}

			// Regulation management
			regulations := protected.Group("/regulations")
			{
				regulations.GET("", handlers.GetRegulations)
				regulations.POST("", middleware.RoleRequired("editor"), handlers.CreateRegulation)
				regulations.GET("/:id", handlers.GetRegulation) // Use authenticated handler
				regulations.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateRegulation)
				regulations.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteRegulation)
				regulations.GET("/:id/versions", handlers.GetPublicRegulationVersions) // Reuse public handler
				// Regulation chunks
				regulations.GET("/:id/chunks", handlers.GetRegulationChunks)
				regulations.POST("/:id/chunks", middleware.RoleRequired("editor"), handlers.CreateRegulationChunk)
				regulations.POST("/:id/chunks/:chunk_id/amend", middleware.RoleRequired("editor"), handlers.AmendRegulationChunk)
				regulations.PUT("/:id/chunks/:chunk_id", middleware.RoleRequired("editor"), handlers.UpdateRegulationChunk)
				regulations.POST("/:id/publish", middleware.RoleRequired("publisher"), handlers.PublishRegulationVersion)

				// Regulation structured content
				regulations.GET("/:id/structured-content", handlers.GetStructuredContent)
				regulations.POST("/:id/structured-content", middleware.RoleRequired("editor"), handlers.AddStructuredContent)
				regulations.PUT("/:id/structured-content", middleware.RoleRequired("editor"), handlers.UpdateStructuredContent)
				regulations.DELETE("/:id/structured-content", middleware.RoleRequired("editor"), handlers.DeleteStructuredContent)
				regulations.POST("/:id/generate-structure", middleware.RoleRequired("editor"), handlers.GenerateDefaultStructuredContent)

				// Regulation relationship management
				regulations.GET("/:id/relationships", handlers.GetRegulationRelationshipsSummary)

				// Document relationships
				regulations.GET("/:id/documents", handlers.GetRegulationDocuments)
				regulations.POST("/:id/documents", middleware.RoleRequired("editor"), handlers.CreateRegulationDocumentRelationship)
				regulations.PUT("/:id/documents/:relationshipId", middleware.RoleRequired("editor"), handlers.UpdateRegulationDocumentRelationship)
				regulations.DELETE("/:id/documents/:relationshipId", middleware.RoleRequired("admin"), handlers.DeleteRegulationDocumentRelationship)

				// Agency relationships
				regulations.GET("/:id/agencies", handlers.GetRegulationAgencies)
				regulations.POST("/:id/agencies", middleware.RoleRequired("editor"), handlers.CreateRegulationAgencyRelationship)
				regulations.PUT("/:id/agencies/:relationshipId", middleware.RoleRequired("editor"), handlers.UpdateRegulationAgencyRelationship)
				regulations.DELETE("/:id/agencies/:relationshipId", middleware.RoleRequired("admin"), handlers.DeleteRegulationAgencyRelationship)

				// Category relationships
				regulations.GET("/:id/categories", handlers.GetRegulationCategories)
				regulations.POST("/:id/categories", middleware.RoleRequired("editor"), handlers.CreateRegulationCategoryRelationship)
				regulations.PUT("/:id/categories/:relationshipId", middleware.RoleRequired("editor"), handlers.UpdateRegulationCategoryRelationship)
				regulations.DELETE("/:id/categories/:relationshipId", middleware.RoleRequired("admin"), handlers.DeleteRegulationCategoryRelationship)
			}

			// Regulation chunk management (individual chunks)
			chunks := protected.Group("/chunks")
			{
				chunks.GET("/:id", handlers.GetRegulationChunk)
				chunks.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateRegulationChunkMetadata)
				chunks.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteRegulationChunk)
			}

			// Calendar management
			calendar := protected.Group("/calendar")
			{
				calendar.GET("", handlers.GetCalendar)
				calendar.GET("/stats", handlers.GetCalendarStats)

				// Calendar events (manual events)
				calendar.POST("/events", middleware.RoleRequired("editor"), handlers.CreateCalendarEvent)
				calendar.GET("/events/:id", handlers.GetCalendarEvent)
				calendar.PUT("/events/:id", middleware.RoleRequired("editor"), handlers.UpdateCalendarEvent)
				calendar.DELETE("/events/:id", middleware.RoleRequired("admin"), handlers.DeleteCalendarEvent)
			}

			// Task management
			tasks := protected.Group("/tasks")
			{
				tasks.GET("", handlers.GetTasks)
				tasks.POST("", handlers.CreateTask)
				tasks.GET("/:id", handlers.GetTask)
				tasks.PUT("/:id", handlers.UpdateTask)
				tasks.DELETE("/:id", handlers.DeleteTask)
				tasks.POST("/:id/complete", handlers.CompleteTask)
				tasks.POST("/parse-text", handlers.ParseText)

				// Task comments
				tasks.GET("/:id/comments", handlers.GetTaskComments)
				tasks.POST("/:id/comments", handlers.CreateTaskComment)

				// Task performance evaluation
				tasks.GET("/:id/performance", handlers.GetTaskPerformance)
				tasks.POST("/:id/performance/calculate", handlers.CalculateTaskPerformance)
				tasks.PUT("/:id/performance", middleware.RoleRequired("editor"), handlers.UpdateTaskPerformance)
				tasks.GET("/:id/performance/history", handlers.GetTaskPerformanceHistory)
				tasks.POST("/performance/recalculate-all", middleware.RoleRequired("admin"), handlers.RecalculateAllTaskPerformances)
				tasks.GET("/performance/scheduler/status", handlers.GetTaskPerformanceSchedulerStatus)
				tasks.POST("/performance/scheduler/run", middleware.RoleRequired("admin"), handlers.RunManualTaskPerformanceEvaluation)

				// Task performance dashboard and metrics
				tasks.GET("/performance/dashboard", handlers.GetTaskPerformanceDashboard)
				tasks.GET("/performance/metrics", handlers.GetTaskPerformanceMetrics)
			}

			// Task comment management (individual comments)
			taskComments := protected.Group("/task-comments")
			{
				taskComments.GET("/:id", handlers.GetTaskComment)
				taskComments.PUT("/:id", handlers.UpdateTaskComment)
				taskComments.DELETE("/:id", handlers.DeleteTaskComment)
			}

			// Proceeding management
			proceedings := protected.Group("/proceedings")
			{
				proceedings.GET("", handlers.GetProceedings)
				proceedings.POST("", middleware.RoleRequired("editor"), handlers.CreateProceeding)
				proceedings.GET("/:id", handlers.GetProceeding)
				proceedings.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateProceeding)
				proceedings.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteProceeding)

				// Primary step management
				proceedings.GET("/:id/steps", handlers.GetProceedingSteps)
				proceedings.PUT("/:id/steps/:step_id/status", middleware.RoleRequired("editor"), handlers.UpdateStepStatus)

				// Log entry management
				proceedings.POST("/:id/logs", middleware.RoleRequired("editor"), handlers.AddLogEntry)

				// Integration with existing systems
				proceedings.GET("/:id/relationships", handlers.GetProceedingRelationships)
				proceedings.POST("/:id/link-task", middleware.RoleRequired("editor"), handlers.LinkProceedingToTask)
				proceedings.POST("/:id/link-document", middleware.RoleRequired("editor"), handlers.LinkProceedingToDocument)
				proceedings.POST("/:id/link-regulation", middleware.RoleRequired("editor"), handlers.LinkProceedingToRegulation)
				proceedings.POST("/:id/trigger-review", middleware.RoleRequired("editor"), handlers.TriggerReviewReport)
			}

			// Summary management
			summaries := protected.Group("/summaries")
			{
				summaries.GET("", handlers.GetSummaries)
				summaries.POST("", middleware.RoleRequired("editor"), handlers.CreateSummary)
				summaries.GET("/:id", handlers.GetSummary)
				summaries.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateSummary)
				summaries.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteSummary)
			}

			// Comment management
			comments := protected.Group("/comments")
			{
				comments.GET("/:id", handlers.GetComment)
				comments.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateComment)
				comments.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteComment)
			}

			// Analytics and reporting
			analytics := protected.Group("/analytics")
			analytics.Use(middleware.RoleRequired("admin"))
			{
				// Legacy dashboard stats
				analytics.GET("/dashboard", handlers.GetDashboardStats)
				analytics.GET("/documents/stats", handlers.GetDocumentStats)
				analytics.GET("/agencies/stats", handlers.GetAgencyStats)
				analytics.GET("/users/stats", handlers.GetUserStats)
				analytics.GET("/relationships/stats", handlers.GetRelationshipStats)

				// New analytics endpoints
				analytics.GET("/metrics", handlers.GetAnalyticsMetrics)
				analytics.GET("/dashboards", handlers.GetAnalyticsDashboards)
				analytics.POST("/dashboards", handlers.CreateAnalyticsDashboard)
				analytics.GET("/dashboards/:id", handlers.GetAnalyticsDashboard)
				analytics.POST("/dashboards/:id/refresh", handlers.RefreshAnalyticsDashboard)
				analytics.GET("/reports", handlers.GetAnalyticsReports)
				analytics.POST("/reports", handlers.CreateAnalyticsReport)
				analytics.POST("/reports/:id/execute", handlers.ExecuteAnalyticsReport)
			}

			// Role management (admin only)
			admin := protected.Group("/admin")
			admin.Use(middleware.RoleRequired("admin"))
			{
				// Role management
				admin.GET("/roles", handlers.GetRoles)
				admin.POST("/roles", handlers.CreateRole)
				admin.GET("/roles/:id", handlers.GetRole)
				admin.PUT("/roles/:id", handlers.UpdateRole)
				admin.DELETE("/roles/:id", handlers.DeleteRole)

				// Permission management
				admin.GET("/permissions", handlers.GetPermissions)

				// User role management
				admin.GET("/user-roles", handlers.GetUserRoles)
				admin.POST("/users/:user_id/roles", handlers.AssignUserRoles)
				admin.DELETE("/users/:user_id/roles/:role_id", handlers.RemoveUserRole)
				admin.GET("/users/:user_id/permissions", handlers.GetUserPermissionsAdmin)
			}

			// Digital Signature routes
			signatures := protected.Group("/signatures")
			{
				signatures.POST("/request", handlers.CreateSignatureRequest)
				signatures.POST("/:signature_id/sign", handlers.SignDocument)
				signatures.POST("/:signature_id/reject", handlers.RejectSignature)
				signatures.POST("/:signature_id/validate", handlers.ValidateSignature)
				signatures.GET("/my", handlers.GetUserSignatures)
			}

			// Digital Certificate routes
			certificates := protected.Group("/certificates")
			{
				certificates.POST("", handlers.CreateCertificate)
				certificates.GET("", handlers.GetCertificates)
				certificates.GET("/:id", handlers.GetCertificate)
				certificates.PUT("/:id", handlers.UpdateCertificate)
				certificates.POST("/:id/revoke", handlers.RevokeCertificate)
			}

			// Document signature routes
			protected.GET("/documents/:id/signatures", handlers.GetDocumentSignatures)

			// Retention Policy routes
			retentionPolicies := protected.Group("/retention-policies")
			{
				retentionPolicies.POST("", handlers.CreateRetentionPolicy)
				retentionPolicies.GET("", handlers.GetRetentionPolicies)
				retentionPolicies.GET("/:id", handlers.GetRetentionPolicy)
				retentionPolicies.PUT("/:id", handlers.UpdateRetentionPolicy)
				retentionPolicies.DELETE("/:id", handlers.DeleteRetentionPolicy)
				retentionPolicies.POST("/assign", handlers.AssignRetentionPolicy)
				retentionPolicies.POST("/:id/execute", handlers.ExecuteRetentionPolicy)
			}

			// Document retention status routes
			protected.GET("/documents/:id/retention-status", handlers.GetDocumentRetentionStatus)

			// Document Processing routes
			processing := protected.Group("/processing")
			{
				processing.POST("/jobs", handlers.CreateProcessingJob)
				processing.GET("/jobs", handlers.GetProcessingJobs)
				processing.GET("/jobs/:job_id", handlers.GetProcessingJob)
				processing.POST("/jobs/:job_id/start", handlers.StartProcessingJob)
				processing.POST("/templates", handlers.CreateProcessingTemplate)
			}

			// Document analysis routes
			protected.GET("/documents/:id/metadata", handlers.GetDocumentMetadata)
			protected.GET("/documents/:id/classification", handlers.GetDocumentClassification)
			protected.GET("/documents/:id/entities", handlers.GetDocumentEntities)

			// Auto-generation routes
			autoGeneration := protected.Group("/auto-generation")
			{
				autoGeneration.GET("/configs", handlers.GetAutoGenerationConfigs)
				autoGeneration.PUT("/configs", handlers.UpdateAutoGenerationConfigs)
				autoGeneration.POST("/generate", handlers.TriggerAutoGeneration)
				autoGeneration.GET("/stats", handlers.GetAutoGenerationStats)
			}

			// Finance routes
			finance := protected.Group("/finance")
			{
				// Finance CRUD operations
				finance.GET("", handlers.GetFinances)
				finance.POST("", middleware.RoleRequired("editor"), handlers.CreateFinance)
				finance.GET("/:id", handlers.GetFinance)
				finance.PUT("/:id", middleware.RoleRequired("editor"), handlers.UpdateFinance)
				finance.DELETE("/:id", middleware.RoleRequired("admin"), handlers.DeleteFinance)

				// Finance aggregation and summary
				finance.GET("/year/:year/aggregate", handlers.AggregateFinanceByYear)
				finance.GET("/year/:year/summary", handlers.GetBudgetSummary)

				// Performance management
				finance.POST("/performance", middleware.RoleRequired("editor"), handlers.CreatePerformance)
				finance.GET("/performance", handlers.GetPerformances)

				// Finance categories
				finance.GET("/categories", handlers.GetFinanceCategories)
				finance.POST("/categories", middleware.RoleRequired("admin"), handlers.CreateFinanceCategory)
			}

			// Enterprise Content Management routes
			ecm := protected.Group("/enterprise/content")
			{
				// Content Repository Management
				ecm.GET("/repositories", handlers.GetContentRepositories)
				ecm.POST("/repositories", middleware.RoleRequired("admin"), handlers.CreateContentRepository)
				ecm.GET("/repositories/:id", handlers.GetContentRepository)
				ecm.PUT("/repositories/:id", middleware.RoleRequired("admin"), handlers.UpdateContentRepository)

				// Content Version Management
				ecm.POST("/versions", middleware.RoleRequired("editor"), handlers.CreateContentVersion)
				ecm.GET("/documents/:documentId/versions", handlers.GetContentVersions)
				ecm.PUT("/versions/:id/approve", middleware.RoleRequired("reviewer"), handlers.ApproveContentVersion)

				// Content Workflow Management
				ecm.GET("/workflows", handlers.GetContentWorkflows)
				ecm.POST("/workflows", middleware.RoleRequired("admin"), handlers.CreateContentWorkflow)
				ecm.POST("/workflows/:workflowId/start", middleware.RoleRequired("editor"), handlers.StartContentWorkflow)
				ecm.GET("/workflow-instances", handlers.GetWorkflowInstances)

				// Content Collaboration
				ecm.POST("/collaborations", middleware.RoleRequired("editor"), handlers.CreateCollaboration)
				ecm.GET("/documents/:documentId/collaborations", handlers.GetActiveCollaborations)
			}

			// Enterprise Financial Management routes
			efm := protected.Group("/enterprise/financial")
			{
				// Chart of Accounts
				efm.GET("/accounts", handlers.GetChartOfAccounts)
				efm.POST("/accounts", middleware.RoleRequired("admin"), handlers.CreateAccount)
				efm.GET("/accounts/:id", handlers.GetAccount)
				efm.PUT("/accounts/:id", middleware.RoleRequired("admin"), handlers.UpdateAccount)
				efm.DELETE("/accounts/:id", middleware.RoleRequired("admin"), handlers.DeleteAccount)

				// General Ledger
				efm.GET("/gl-entries", handlers.GetGLEntries)
				efm.POST("/gl-entries", middleware.RoleRequired("editor"), handlers.CreateGLEntry)
				efm.GET("/gl-entries/:id", handlers.GetGLEntry)
				efm.PUT("/gl-entries/:id", middleware.RoleRequired("editor"), handlers.UpdateGLEntry)
				efm.DELETE("/gl-entries/:id", middleware.RoleRequired("admin"), handlers.DeleteGLEntry)
				efm.POST("/gl-entries/:id/post", middleware.RoleRequired("reviewer"), handlers.PostGLEntry)

				// Budget Management
				efm.GET("/budgets", handlers.GetBudgets)
				efm.POST("/budgets", middleware.RoleRequired("editor"), handlers.CreateBudget)
				efm.GET("/budgets/:id", handlers.GetBudget)
				efm.PUT("/budgets/:id", middleware.RoleRequired("editor"), handlers.UpdateBudget)
				efm.DELETE("/budgets/:id", middleware.RoleRequired("admin"), handlers.DeleteBudget)
				efm.POST("/budgets/:id/approve", middleware.RoleRequired("reviewer"), handlers.ApproveBudget)
				efm.GET("/budgets/variance-analysis", handlers.GetBudgetVarianceAnalysis)

				// Cost Centers
				efm.GET("/cost-centers", handlers.GetCostCenters)
				efm.POST("/cost-centers", middleware.RoleRequired("admin"), handlers.CreateCostCenter)
				efm.GET("/cost-centers/:id", handlers.GetCostCenter)
				efm.PUT("/cost-centers/:id", middleware.RoleRequired("admin"), handlers.UpdateCostCenter)
				efm.DELETE("/cost-centers/:id", middleware.RoleRequired("admin"), handlers.DeleteCostCenter)

				// Financial Reports
				efm.GET("/reports", handlers.GetFinancialReports)
				efm.POST("/reports", middleware.RoleRequired("editor"), handlers.GenerateFinancialReport)
				efm.GET("/reports/:id", handlers.GetFinancialReport)
				efm.PUT("/reports/:id", middleware.RoleRequired("editor"), handlers.UpdateFinancialReport)
				efm.DELETE("/reports/:id", middleware.RoleRequired("admin"), handlers.DeleteFinancialReport)

				// Expense Management
				efm.GET("/expenses", handlers.GetFinancialExpenses)
			}

			// Enterprise Compliance Management routes
			compliance := protected.Group("/enterprise/compliance")
			{
				// Compliance Requirements
				compliance.GET("/requirements", handlers.GetComplianceRequirements)
				compliance.POST("/requirements", middleware.RoleRequired("admin"), handlers.CreateComplianceRequirement)
				compliance.GET("/requirements/:id", handlers.GetComplianceRequirement)
				compliance.PUT("/requirements/:id", middleware.RoleRequired("admin"), handlers.UpdateComplianceRequirement)

				// Compliance Assessments
				compliance.GET("/assessments", handlers.GetComplianceAssessments)
				compliance.POST("/assessments", middleware.RoleRequired("editor"), handlers.CreateComplianceAssessment)
				compliance.POST("/assessments/:id/start", middleware.RoleRequired("reviewer"), handlers.StartComplianceAssessment)
				compliance.POST("/assessments/:id/complete", middleware.RoleRequired("reviewer"), handlers.CompleteComplianceAssessment)

				// Compliance Findings
				compliance.GET("/findings", handlers.GetComplianceFindings)
				compliance.POST("/findings", middleware.RoleRequired("editor"), handlers.CreateComplianceFinding)
				compliance.PUT("/findings/:id/status", middleware.RoleRequired("editor"), handlers.UpdateFindingStatus)

				// Risk Assessments
				compliance.GET("/risks", handlers.GetRiskAssessments)
				compliance.POST("/risks", middleware.RoleRequired("editor"), handlers.CreateRiskAssessment)
				compliance.GET("/risks/dashboard", handlers.GetRiskDashboard)

				// Policy Management
				compliance.GET("/policies", handlers.GetPolicies)
				compliance.POST("/policies", middleware.RoleRequired("editor"), handlers.CreatePolicy)
				compliance.POST("/policies/:id/approve", middleware.RoleRequired("reviewer"), handlers.ApprovePolicy)

				// Audit Management
				compliance.GET("/audits", handlers.GetComplianceAudits)
			}

			// Enterprise Business Intelligence routes
			bi := protected.Group("/enterprise/bi")
			{
				// Data Warehouse Management
				bi.GET("/warehouses", handlers.GetDataWarehouses)
				bi.POST("/warehouses", middleware.RoleRequired("admin"), handlers.CreateDataWarehouse)

				// Data Source Management
				bi.GET("/data-sources", handlers.GetDataSources)
				bi.POST("/data-sources", middleware.RoleRequired("admin"), handlers.CreateDataSource)
				bi.POST("/data-sources/:id/sync", middleware.RoleRequired("editor"), handlers.SyncDataSource)

				// Dashboard Management
				bi.GET("/dashboards", handlers.GetDashboards)
				bi.POST("/dashboards", middleware.RoleRequired("editor"), handlers.CreateDashboard)
				bi.GET("/dashboards/:id", handlers.GetDashboard)
				bi.POST("/dashboards/:id/refresh", handlers.RefreshDashboard)

				// Report Management
				bi.GET("/reports", handlers.GetReports)
				bi.POST("/reports", middleware.RoleRequired("editor"), handlers.CreateReport)
				bi.POST("/reports/:id/execute", handlers.ExecuteReport)

				// KPI Management
				bi.GET("/kpis", handlers.GetKPIs)
				bi.POST("/kpis", middleware.RoleRequired("editor"), handlers.CreateKPI)
				bi.PUT("/kpis/:id/value", middleware.RoleRequired("editor"), handlers.UpdateKPIValue)

				// Data Mining
				bi.GET("/data-mining", handlers.GetDataMiningModels)
				bi.POST("/data-mining", middleware.RoleRequired("admin"), handlers.CreateDataMiningModel)
			}

			// Enterprise Human Resources routes
			hr := protected.Group("/enterprise/hr")
			{
				// Employee Management
				hr.GET("/employees", handlers.GetEmployees)
				hr.POST("/employees", middleware.RoleRequired("admin"), handlers.CreateEmployee)
				hr.GET("/employees/:id", handlers.GetEmployee)
				hr.PUT("/employees/:id", middleware.RoleRequired("admin"), handlers.UpdateEmployee)
				hr.POST("/employees/:id/terminate", middleware.RoleRequired("admin"), handlers.TerminateEmployee)

				// Department Management
				hr.GET("/departments", handlers.GetDepartments)
				hr.POST("/departments", middleware.RoleRequired("admin"), handlers.CreateDepartment)

				// Position Management
				hr.GET("/positions", handlers.GetPositions)
				hr.POST("/positions", middleware.RoleRequired("admin"), handlers.CreatePosition)
				hr.POST("/positions/:id/approve", middleware.RoleRequired("admin"), handlers.ApprovePosition)

				// Performance Review Management
				hr.GET("/performance-reviews", handlers.GetPerformanceReviews)
				hr.POST("/performance-reviews", middleware.RoleRequired("editor"), handlers.CreatePerformanceReview)
				hr.POST("/performance-reviews/:id/submit", middleware.RoleRequired("editor"), handlers.SubmitPerformanceReview)
				hr.POST("/performance-reviews/:id/approve", middleware.RoleRequired("reviewer"), handlers.ApprovePerformanceReview)

				// Training Management
				hr.GET("/trainings", handlers.GetTrainings)
				hr.POST("/trainings", middleware.RoleRequired("admin"), handlers.CreateTraining)
				hr.POST("/trainings/:trainingId/enroll", middleware.RoleRequired("editor"), handlers.EnrollInTraining)
				hr.GET("/training-enrollments", handlers.GetTrainingEnrollments)
				hr.POST("/training-enrollments/:id/complete", middleware.RoleRequired("editor"), handlers.CompleteTraining)

				// HR Analytics
				hr.GET("/dashboard", handlers.GetHRDashboard)
			}
		}
	}

	return router
}
