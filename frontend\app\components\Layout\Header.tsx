'use client'

import React from 'react';
import Link from 'next/link';
import { useRouter, usePathname } from 'next/navigation';
import {
  Bars3Icon,
  XMarkIcon,
  MagnifyingGlassIcon,
  UserCircleIcon,
  SunIcon,
  MoonIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { useAuthStore } from '../../stores/authStore';
import { useUIStore } from '../../stores/uiStore';
import NavigationDropdown from './NavigationDropdown';

const Header: React.FC = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isAuthenticated, logout } = useAuthStore();
  const { 
    theme, 
    toggleTheme, 
    mobileMenuOpen, 
    toggleMobileMenu,
    searchQuery,
    setSearchQuery 
  } = useUIStore();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <header className="header-nav sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Navigation */}
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Link href="/" className="flex items-center">
                <div className="h-8 w-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">FR</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                  Federal Register
                </span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:ml-8 md:flex md:items-center md:space-x-4">
              {/* Quick access to most important pages */}
              <Link href="/documents" className="nav-link">
                Documents
              </Link>
              <Link href="/search" className="nav-link">
                Search
              </Link>
              {isAuthenticated && (
                <Link href="/dashboard" className="nav-link">
                  Dashboard
                </Link>
              )}

              {/* Navigation dropdown for all other pages */}
              <NavigationDropdown />
            </nav>
          </div>

          {/* Search Bar */}
          <div className="flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search documents..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </form>
          </div>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            {/* Theme toggle */}
            <button
              onClick={toggleTheme}
              className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-md"
              aria-label="Toggle theme"
            >
              {theme === 'light' ? (
                <MoonIcon className="h-5 w-5" />
              ) : (
                <SunIcon className="h-5 w-5" />
              )}
            </button>

            {/* User menu */}
            {isAuthenticated ? (
              <div className="relative">
                <div className="flex items-center space-x-3">
                  <span className="hidden md:block text-sm text-gray-700 dark:text-gray-300">
                    {user?.first_name} {user?.last_name}
                  </span>
                  <div className="flex items-center space-x-4">
                    {/* Documents Navigation */}
                    <div className="relative group">
                      <button className="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium flex items-center">
                        Documents
                        <ChevronDownIcon className="ml-1 h-4 w-4" />
                      </button>
                      <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                        <Link href="/documents" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                          Browse Documents
                        </Link>
                        <Link href="/documents/new" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                          Create Document
                        </Link>
                        <Link href="/search" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                          Advanced Search
                        </Link>
                      </div>
                    </div>

                    {/* Management Navigation - Role-based access */}
                    {(user?.role === 'admin' || user?.role === 'editor' || user?.role === 'publisher' || user?.role === 'reviewer') && (
                      <div className="relative group">
                        <button className="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium flex items-center">
                          Management
                          <ChevronDownIcon className="ml-1 h-4 w-4" />
                        </button>
                        <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                          {/* Editor and above can manage documents and categories */}
                          {(user?.role === 'admin' || user?.role === 'editor' || user?.role === 'publisher') && (
                            <>
                              <Link href="/admin/documents" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Manage Documents
                              </Link>
                              <Link href="/admin/categories" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Manage Categories
                              </Link>
                              <Link href="/finance" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Finance Management
                              </Link>
                              <Link href="/finance/parser" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Finance Parser
                              </Link>
                            </>
                          )}

                          {/* Admin-only features */}
                          {user?.role === 'admin' && (
                            <>
                              <div className="border-t border-gray-200 dark:border-gray-600 my-1"></div>
                              <div className="px-4 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                Admin Only
                              </div>
                              <Link href="/admin" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Admin Dashboard
                              </Link>
                              <Link href="/admin/agencies" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Manage Agencies
                              </Link>
                              <Link href="/admin/proceedings" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Manage Proceedings
                              </Link>
                              <Link href="/admin/users" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                User Management
                              </Link>
                              <Link href="/admin/roles" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                Role Management
                              </Link>
                            </>
                          )}
                        </div>
                      </div>
                    )}

                    {/* User Avatar and Menu */}
                    <div className="relative group">
                      <button className="flex items-center space-x-2 text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium">
                        <div className="h-8 w-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {user?.first_name?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
                        </div>
                        <span className="hidden md:block">
                          {user?.first_name ? `${user.first_name} ${user.last_name || ''}`.trim() : user?.email}
                        </span>
                        <ChevronDownIcon className="ml-1 h-4 w-4" />
                      </button>
                      <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                        <Link href="/profile" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                          My Profile
                        </Link>
                        <Link href="/change-password" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                          Change Password
                        </Link>
                        <Link href="/dashboard" className="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                          Dashboard
                        </Link>
                        <div className="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                        <button
                          onClick={handleLogout}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          Sign Out
                        </button>
                      </div>
                    </div>
                  </div>
                  <UserCircleIcon className="h-8 w-8 text-gray-400" />
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  href="/login"
                  className="text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200"
                >
                  Login
                </Link>
                <Link
                  href="/register"
                  className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                >
                  Register
                </Link>
              </div>
            )}

            {/* Mobile menu button */}
            <button
              onClick={toggleMobileMenu}
              className="md:hidden p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-md"
              aria-label="Toggle mobile menu"
            >
              {mobileMenuOpen ? (
                <XMarkIcon className="h-6 w-6" />
              ) : (
                <Bars3Icon className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile menu */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200 dark:border-gray-700">
              {/* Quick access links for mobile */}
              <Link
                href="/documents"
                className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                onClick={() => toggleMobileMenu()}
              >
                Documents
              </Link>
              <Link
                href="/search"
                className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                onClick={() => toggleMobileMenu()}
              >
                Search
              </Link>
              {isAuthenticated && (
                <Link
                  href="/dashboard"
                  className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                  onClick={() => toggleMobileMenu()}
                >
                  Dashboard
                </Link>
              )}

              {/* Divider */}
              <div className="border-t border-gray-200 dark:border-gray-700 my-2"></div>

              {/* More navigation options */}
              <Link
                href="/regulations"
                className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                onClick={() => toggleMobileMenu()}
              >
                Regulations
              </Link>
              <Link
                href="/agencies"
                className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                onClick={() => toggleMobileMenu()}
              >
                Agencies
              </Link>
              <Link
                href="/categories"
                className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                onClick={() => toggleMobileMenu()}
              >
                Categories
              </Link>
              <Link
                href="/calendar"
                className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                onClick={() => toggleMobileMenu()}
              >
                Calendar
              </Link>
              {isAuthenticated && (user?.role === 'admin' || user?.role === 'editor' || user?.role === 'publisher') && (
                <Link
                  href="/finance"
                  className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                  onClick={() => toggleMobileMenu()}
                >
                  Finance
                </Link>
              )}
              <Link
                href="/about"
                className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                onClick={() => toggleMobileMenu()}
              >
                About
              </Link>
              
              {isAuthenticated && (
                <>
                  {/* User Info */}
                  <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 bg-primary-600 rounded-full flex items-center justify-center text-white font-medium">
                        {user?.first_name?.[0] || user?.email?.[0]?.toUpperCase() || 'U'}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {user?.first_name ? `${user.first_name} ${user.last_name || ''}`.trim() : user?.email}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                          {user?.role || 'User'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Documents */}
                  <div className="py-2">
                    <div className="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Documents
                    </div>
                    <Link
                      href="/documents"
                      className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                      onClick={() => toggleMobileMenu()}
                    >
                      Browse Documents
                    </Link>
                    <Link
                      href="/documents/new"
                      className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                      onClick={() => toggleMobileMenu()}
                    >
                      Create Document
                    </Link>
                    <Link
                      href="/search"
                      className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                      onClick={() => toggleMobileMenu()}
                    >
                      Advanced Search
                    </Link>
                  </div>

                  {/* Management */}
                  {(user?.role === 'admin' || user?.role === 'editor' || user?.role === 'publisher' || user?.role === 'reviewer') && (
                    <div className="py-2 border-t border-gray-200 dark:border-gray-700">
                      <div className="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                        Management
                      </div>
                      {/* Editor and above can manage documents and categories */}
                      {(user?.role === 'admin' || user?.role === 'editor' || user?.role === 'publisher') && (
                        <>
                          <Link
                            href="/admin/documents"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Manage Documents
                          </Link>
                          <Link
                            href="/admin/categories"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Manage Categories
                          </Link>
                        </>
                      )}
                      {/* Admin-only features */}
                      {user?.role === 'admin' && (
                        <>
                          <div className="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Admin Only
                          </div>
                          <Link
                            href="/admin"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Admin Dashboard
                          </Link>
                          <Link
                            href="/admin/agencies"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Manage Agencies
                          </Link>
                          <Link
                            href="/admin/proceedings"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Manage Proceedings
                          </Link>
                          <Link
                            href="/admin/users"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            User Management
                          </Link>
                          <Link
                            href="/admin/roles"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Role Management
                          </Link>
                          <Link
                            href="/admin/retention"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Retention Policies
                          </Link>
                          <Link
                            href="/admin/processing"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Document Processing
                          </Link>
                          <Link
                            href="/admin/analytics"
                            className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                            onClick={() => toggleMobileMenu()}
                          >
                            Advanced Analytics
                          </Link>
                        </>
                      )}
                    </div>
                  )}

                  {/* Account */}
                  <div className="py-2 border-t border-gray-200 dark:border-gray-700">
                    <div className="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Account
                    </div>
                    <Link
                      href="/profile"
                      className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                      onClick={() => toggleMobileMenu()}
                    >
                      My Profile
                    </Link>
                    <Link
                      href="/change-password"
                      className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                      onClick={() => toggleMobileMenu()}
                    >
                      Change Password
                    </Link>
                    <Link
                      href="/dashboard"
                      className="block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                      onClick={() => toggleMobileMenu()}
                    >
                      Dashboard
                    </Link>
                    <button
                      onClick={() => {
                        handleLogout();
                        toggleMobileMenu();
                      }}
                      className="block w-full text-left px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400"
                    >
                      Sign Out
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
