'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  CpuChipIcon,
  PlayIcon,
  StopIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import { biApi } from '../../../services/enterpriseApi';

interface DataMiningJob {
  id: number;
  job_name: string;
  description: string;
  algorithm: string;
  data_source: string;
  parameters: any;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  results: any;
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
}

const DataMiningPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [jobs, setJobs] = useState<DataMiningJob[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchJobs();
  }, []);

  const fetchJobs = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch data mining models from enterprise BI API
      const response = await biApi.getDataMiningModels();

      if (response.data) {
        // Transform data mining models to data mining jobs format
        const transformedJobs: DataMiningJob[] = response.data.map((model: any) => ({
          id: model.id,
          job_name: model.model_name,
          description: model.description || 'Data mining model',
          algorithm: model.algorithm || 'Machine Learning',
          data_source: model.training_data || 'Enterprise Database',
          parameters: model.parameters ? JSON.parse(model.parameters || '{}') : {},
          status: (model.deployed_at ? 'completed' :
                 model.trained_at ? 'running' : 'pending') as 'pending' | 'running' | 'completed' | 'failed' | 'cancelled',
          progress: model.deployed_at ? 100 :
                   model.trained_at ? 80 : 0,
          results: model.accuracy ? {
            accuracy: model.accuracy,
            precision: model.precision,
            recall: model.recall,
            f1_score: model.f1_score
          } : null,
          created_at: model.created_at,
          updated_at: model.updated_at,
          completed_at: model.deployed_at,
          started_at: model.trained_at
        }));

        setJobs(transformedJobs);
      } else {
        throw new Error('Failed to fetch data mining models');
      }
    } catch (err: any) {
      console.error('Error fetching data mining jobs:', err);
      setError(err.response?.data?.message || err.message || 'Failed to fetch data mining jobs');

      // Set empty array on error
      setJobs([]);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this data mining job?')) return;

    try {
      await biApi.deleteDataMiningModel(id);
      setJobs(jobs.filter(job => job.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to delete data mining job');
    }
  };

  const handleStartJob = async (id: number) => {
    try {
      // Start training the model
      await biApi.trainDataMiningModel(id);
      setJobs(jobs.map(job =>
        job.id === id ? { ...job, status: 'running' as const, started_at: new Date().toISOString() } : job
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to start data mining job');
    }
  };

  const handleStopJob = async (id: number) => {
    try {
      // Stop the training process
      await biApi.stopDataMiningModel(id);
      setJobs(jobs.map(job =>
        job.id === id ? { ...job, status: 'cancelled' as const } : job
      ));
    } catch (err: any) {
      setError(err.message || 'Failed to stop data mining job');
    }
  };

  const filteredJobs = jobs.filter(job =>
    job.job_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    job.algorithm.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'running':
        return <CpuChipIcon className="h-4 w-4" />;
      case 'failed':
        return <StopIcon className="h-4 w-4" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  if (!user) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be logged in to view data mining jobs.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Data Mining</h1>
              <p className="text-gray-600 mt-1">Manage and monitor data mining jobs and analytics</p>
            </div>
            {(user.role === 'admin' || user.role === 'editor') && (
              <Link
                href="/enterprise/bi/data-mining/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Mining Job
              </Link>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search data mining jobs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading data mining jobs...</p>
          </div>
        ) : (
          /* Jobs Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Job Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Algorithm
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Data Source
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredJobs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                      No data mining jobs found.
                      {(user.role === 'admin' || user.role === 'editor') && (
                        <Link
                          href="/enterprise/bi/data-mining/new"
                          className="block mt-2 text-primary-600 hover:text-primary-500"
                        >
                          Create your first mining job
                        </Link>
                      )}
                    </td>
                  </tr>
                ) : (
                  filteredJobs.map((job) => (
                    <tr key={job.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{job.job_name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{job.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {job.algorithm}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {job.data_source}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                          {getStatusIcon(job.status)}
                          <span className="ml-1">{job.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${job.progress}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-900">{job.progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/enterprise/bi/data-mining/${job.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {(user.role === 'admin' || user.role === 'editor') && (
                            <>
                              {job.status === 'pending' && (
                                <button
                                  onClick={() => handleStartJob(job.id)}
                                  className="text-green-600 hover:text-green-900"
                                  title="Start Job"
                                >
                                  <PlayIcon className="h-4 w-4" />
                                </button>
                              )}
                              {job.status === 'running' && (
                                <button
                                  onClick={() => handleStopJob(job.id)}
                                  className="text-red-600 hover:text-red-900"
                                  title="Stop Job"
                                >
                                  <StopIcon className="h-4 w-4" />
                                </button>
                              )}
                              <button
                                onClick={() => router.push(`/enterprise/bi/data-mining/${job.id}/edit`)}
                                className="text-yellow-600 hover:text-yellow-900"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(job.id)}
                                className="text-red-600 hover:text-red-900"
                              >
                                <TrashIcon className="h-4 w-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default DataMiningPage;
