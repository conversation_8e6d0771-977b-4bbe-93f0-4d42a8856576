-- Sample data for Federal Register Clone
-- Test data for development and testing

-- Insert sample agencies
INSERT INTO agencies (name, short_name, slug, description, is_active, agency_type, jurisdiction) VALUES
('Department of Environmental Protection', 'DEP', 'department-environmental-protection', 'Federal agency responsible for environmental protection and regulation', true, 'federal', 'national'),
('Department of Health and Human Services', 'HHS', 'department-health-human-services', 'Federal agency overseeing health and human services', true, 'federal', 'national'),
('Department of Transportation', 'DOT', 'department-transportation', 'Federal agency managing transportation infrastructure and safety', true, 'federal', 'national'),
('Federal Communications Commission', 'FCC', 'federal-communications-commission', 'Independent agency regulating communications', true, 'independent', 'national'),
('Securities and Exchange Commission', 'SEC', 'securities-exchange-commission', 'Independent agency regulating securities markets', true, 'independent', 'national');

-- Insert sample users
INSERT INTO users (username, email, first_name, last_name, password_hash, role, is_active, agency_id) VALUES
('admin', '<EMAIL>', 'Admin', 'User', '$2a$10$example_hash_admin', 'admin', true, 1),
('editor1', '<EMAIL>', 'John', 'Editor', '$2a$10$example_hash_editor1', 'editor', true, 1),
('editor2', '<EMAIL>', 'Jane', 'Smith', '$2a$10$example_hash_editor2', 'editor', true, 2),
('reviewer1', '<EMAIL>', 'Bob', 'Reviewer', '$2a$10$example_hash_reviewer1', 'reviewer', true, 3),
('viewer1', '<EMAIL>', 'Alice', 'Viewer', '$2a$10$example_hash_viewer1', 'viewer', true, null);

-- Insert sample categories
INSERT INTO categories (name, slug, description, color, icon, is_active) VALUES
('Environmental Protection', 'environmental-protection', 'Documents related to environmental protection and conservation', '#10B981', 'leaf', true),
('Healthcare Regulations', 'healthcare-regulations', 'Healthcare policies and medical regulations', '#3B82F6', 'heart', true),
('Transportation Safety', 'transportation-safety', 'Transportation safety rules and guidelines', '#F59E0B', 'truck', true),
('Communications Policy', 'communications-policy', 'Telecommunications and media regulations', '#8B5CF6', 'phone', true),
('Financial Regulations', 'financial-regulations', 'Securities and financial market regulations', '#EF4444', 'currency-dollar', true),
('Proposed Rules', 'proposed-rules', 'Proposed federal rules awaiting public comment', '#F59E0B', 'document-text', true),
('Final Rules', 'final-rules', 'Finalized federal rules and regulations', '#10B981', 'check-circle', true),
('Notices', 'notices', 'Federal notices and announcements', '#6B7280', 'bell', true);

-- Insert sample tags
INSERT INTO tags (name, slug, description, color, is_active, created_by_id) VALUES
('Climate Change', 'climate-change', 'Documents related to climate change policies', '#10B981', true, 2),
('Public Health', 'public-health', 'Public health and safety documents', '#3B82F6', true, 3),
('Safety Standards', 'safety-standards', 'Safety standards and requirements', '#F59E0B', true, 4),
('Emergency Response', 'emergency-response', 'Emergency response procedures', '#EF4444', true, 2),
('Data Privacy', 'data-privacy', 'Data privacy and protection regulations', '#8B5CF6', true, 3),
('Economic Impact', 'economic-impact', 'Documents with significant economic impact', '#F59E0B', true, 2),
('Small Business', 'small-business', 'Regulations affecting small businesses', '#10B981', true, 4);

-- Insert sample subjects
INSERT INTO subjects (name, slug, description, cfr_title, is_active) VALUES
('Air Quality Standards', 'air-quality-standards', 'Federal air quality regulations and standards', '40', true),
('Water Protection', 'water-protection', 'Water quality and protection regulations', '40', true),
('Food Safety', 'food-safety', 'Food safety and inspection regulations', '21', true),
('Drug Approval', 'drug-approval', 'Pharmaceutical approval processes', '21', true),
('Vehicle Safety', 'vehicle-safety', 'Motor vehicle safety standards', '49', true),
('Aviation Regulations', 'aviation-regulations', 'Federal aviation regulations', '14', true),
('Telecommunications', 'telecommunications', 'Telecommunications regulations', '47', true),
('Securities Trading', 'securities-trading', 'Securities trading regulations', '17', true);

-- Insert sample documents
INSERT INTO documents (
    title, slug, abstract, content, type, status, 
    agency_id, created_by_id, fr_document_number,
    publication_date, effective_date, accepts_comments
) VALUES
(
    'New Air Quality Standards for Industrial Emissions',
    'new-air-quality-standards-industrial-emissions',
    'This rule establishes new air quality standards for industrial emissions to protect public health and the environment.',
    'The Environmental Protection Agency (EPA) is establishing new air quality standards for industrial emissions. These standards will require facilities to implement advanced pollution control technologies...',
    'rule',
    'published',
    1, 2, '2024-001',
    '2024-01-15 10:00:00+00', '2024-03-15 00:00:00+00', false
),
(
    'Proposed Healthcare Data Privacy Regulations',
    'proposed-healthcare-data-privacy-regulations',
    'Proposed regulations to enhance privacy protections for healthcare data in digital systems.',
    'The Department of Health and Human Services proposes new regulations to strengthen privacy protections for healthcare data. These regulations would require healthcare providers to implement enhanced security measures...',
    'proposed_rule',
    'open_for_comment',
    2, 3, '2024-002',
    '2024-01-20 10:00:00+00', '2024-06-20 00:00:00+00', true
),
(
    'Transportation Safety Notice: Winter Driving Guidelines',
    'transportation-safety-notice-winter-driving',
    'Safety notice providing guidelines for winter driving conditions and vehicle preparation.',
    'The Department of Transportation issues this safety notice to provide guidance on winter driving conditions. Drivers should ensure their vehicles are properly prepared for winter weather...',
    'notice',
    'published',
    3, 4, '2024-003',
    '2024-01-25 10:00:00+00', '2024-01-25 10:00:00+00', false
),
(
    'FCC Broadband Infrastructure Investment Rules',
    'fcc-broadband-infrastructure-investment-rules',
    'New rules governing federal investment in broadband infrastructure development.',
    'The Federal Communications Commission establishes new rules for federal broadband infrastructure investment. These rules will streamline the approval process for broadband projects...',
    'rule',
    'published',
    4, 2, '2024-004',
    '2024-02-01 10:00:00+00', '2024-04-01 00:00:00+00', false
),
(
    'Securities Market Transparency Enhancement Proposal',
    'securities-market-transparency-enhancement-proposal',
    'Proposed enhancements to securities market transparency and reporting requirements.',
    'The Securities and Exchange Commission proposes enhancements to market transparency requirements. These changes would require additional disclosure from market participants...',
    'proposed_rule',
    'open_for_comment',
    5, 3, '2024-005',
    '2024-02-05 10:00:00+00', '2024-08-05 00:00:00+00', true
);

-- Create document-category associations
INSERT INTO document_category_assignments (document_id, category_id) VALUES
(1, 1), (1, 7), -- Air quality: Environmental + Final Rules
(2, 2), (2, 6), -- Healthcare: Healthcare + Proposed Rules
(3, 3), (3, 8), -- Transportation: Transportation + Notices
(4, 4), (4, 7), -- FCC: Communications + Final Rules
(5, 5), (5, 6); -- SEC: Financial + Proposed Rules

-- Create document-tag associations
INSERT INTO document_tag_assignments (document_id, tag_id) VALUES
(1, 1), (1, 6), -- Air quality: Climate Change + Economic Impact
(2, 2), (2, 5), -- Healthcare: Public Health + Data Privacy
(3, 3), (3, 4), -- Transportation: Safety Standards + Emergency Response
(4, 6), (4, 7), -- FCC: Economic Impact + Small Business
(5, 6), (5, 7); -- SEC: Economic Impact + Small Business

-- Create document-subject associations
INSERT INTO document_subject_assignments (document_id, subject_id) VALUES
(1, 1), -- Air quality standards
(2, 3), -- Food safety (healthcare related)
(3, 5), -- Vehicle safety
(4, 7), -- Telecommunications
(5, 8); -- Securities trading

-- Update tag usage counts
UPDATE tags SET usage_count = (
    SELECT COUNT(*) FROM document_tag_assignments WHERE tag_id = tags.id
);

-- Insert user preferences for sample users
INSERT INTO user_preferences (user_id, documents_per_page, theme, email_notifications) VALUES
(1, 50, 'dark', true),
(2, 25, 'light', true),
(3, 25, 'light', false),
(4, 20, 'light', true),
(5, 10, 'light', false);
