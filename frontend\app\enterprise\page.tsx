'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '../stores/authStore';

interface EnterpriseStats {
  contentRepositories: number;
  activeWorkflows: number;
  complianceScore: number;
  riskAssessments: number;
  financialReports: number;
  employees: number;
  trainings: number;
  dashboards: number;
}

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  href: string;
  color: string;
}

const EnterpriseDashboard: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const [stats, setStats] = useState<EnterpriseStats>({
    contentRepositories: 0,
    activeWorkflows: 0,
    complianceScore: 0,
    riskAssessments: 0,
    financialReports: 0,
    employees: 0,
    trainings: 0,
    dashboards: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const quickActions: QuickAction[] = [
    {
      title: 'Content Management',
      description: 'Manage enterprise content repositories, workflows, and collaboration',
      icon: '📁',
      href: '/enterprise/content',
      color: 'bg-blue-500'
    },
    {
      title: 'Financial Management',
      description: 'Chart of accounts, budgets, general ledger, and financial reporting',
      icon: '💰',
      href: '/enterprise/financial',
      color: 'bg-green-500'
    },
    {
      title: 'Compliance & Risk',
      description: 'Compliance requirements, assessments, risk management, and policies',
      icon: '🛡️',
      href: '/enterprise/compliance',
      color: 'bg-red-500'
    },
    {
      title: 'Business Intelligence',
      description: 'Dashboards, reports, KPIs, and data analytics',
      icon: '📊',
      href: '/enterprise/bi',
      color: 'bg-purple-500'
    },
    {
      title: 'Human Resources',
      description: 'Employee management, performance reviews, and training programs',
      icon: '👥',
      href: '/enterprise/hr',
      color: 'bg-orange-500'
    },
    {
      title: 'Data Management',
      description: 'Data warehouses, sources, and mining models',
      icon: '🗄️',
      href: '/enterprise/data',
      color: 'bg-indigo-500'
    }
  ];

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);

        // Check if we're on the client side
        if (typeof window === 'undefined') {
          setLoading(false);
          return;
        }

        // Fetch real enterprise statistics from multiple API endpoints
        const [
          contentResponse,
          hrResponse,
          biResponse,
          financialResponse,
          complianceResponse
        ] = await Promise.allSettled([
          // Content Management stats
          fetch('/api/v1/enterprise/content/repositories', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          }).then(res => res.json()),

          // HR stats
          fetch('/api/v1/enterprise/hr/dashboard', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          }).then(res => res.json()),

          // BI stats
          fetch('/api/v1/enterprise/bi/dashboards', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          }).then(res => res.json()),

          // Financial stats
          fetch('/api/v1/analytics/dashboard', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          }).then(res => res.json()),

          // Compliance stats (using document stats as proxy)
          fetch('/api/v1/analytics/documents/stats', {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          }).then(res => res.json())
        ]);

        // Extract data from successful responses
        const contentData = contentResponse.status === 'fulfilled' ? contentResponse.value : null;
        const hrData = hrResponse.status === 'fulfilled' ? hrResponse.value : null;
        const biData = biResponse.status === 'fulfilled' ? biResponse.value : null;
        const financialData = financialResponse.status === 'fulfilled' ? financialResponse.value : null;
        const complianceData = complianceResponse.status === 'fulfilled' ? complianceResponse.value : null;

        // Aggregate statistics from real API responses
        const realStats: EnterpriseStats = {
          contentRepositories: contentData?.pagination?.total || 0,
          activeWorkflows: contentData?.repositories?.filter((r: any) => r.status === 'active')?.length || 0,
          complianceScore: Math.round((complianceData?.totalDocuments || 0) / 10), // Simple compliance score calculation
          riskAssessments: complianceData?.recentDocuments || 0,
          financialReports: Math.round((financialData?.data?.total_budget || 0) / 1000000), // Convert to millions
          employees: hrData?.dashboard?.employees?.total || 0,
          trainings: hrData?.dashboard?.training?.total_programs || 0,
          dashboards: biData?.pagination?.total || 0,
        };

        setStats(realStats);
      } catch (err) {
        setError('Failed to load enterprise statistics');
        console.error('Error fetching enterprise stats:', err);

        // Fallback to default values if API calls fail
        setStats({
          contentRepositories: 0,
          activeWorkflows: 0,
          complianceScore: 0,
          riskAssessments: 0,
          financialReports: 0,
          employees: 0,
          trainings: 0,
          dashboards: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const handleQuickAction = (href: string) => {
    router.push(href);
  };

  if (!isAuthenticated) {
    router.push('/login');
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Enterprise Management</h1>
          <p className="mt-2 text-gray-600">
            Comprehensive enterprise-level document and business management system
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">📁</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Content Repositories</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {loading ? '...' : stats.contentRepositories}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">⚡</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Active Workflows</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {loading ? '...' : stats.activeWorkflows}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">🛡️</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Compliance Score</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {loading ? '...' : `${stats.complianceScore}%`}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm">👥</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Employees</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {loading ? '...' : stats.employees}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Enterprise Modules</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <div
                key={index}
                onClick={() => handleQuickAction(action.href)}
                className="bg-white rounded-lg shadow hover:shadow-lg transition-shadow cursor-pointer p-6"
              >
                <div className="flex items-center mb-4">
                  <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                    <span className="text-white text-xl">{action.icon}</span>
                  </div>
                  <h3 className="ml-4 text-lg font-semibold text-gray-900">{action.title}</h3>
                </div>
                <p className="text-gray-600">{action.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Enterprise Activity</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">New compliance assessment completed for SOX framework</span>
                <span className="text-xs text-gray-400">2 hours ago</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Financial report generated for Q4 2024</span>
                <span className="text-xs text-gray-400">4 hours ago</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-gray-600">New employee onboarding workflow started</span>
                <span className="text-xs text-gray-400">6 hours ago</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Business intelligence dashboard updated</span>
                <span className="text-xs text-gray-400">8 hours ago</span>
              </div>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnterpriseDashboard;
