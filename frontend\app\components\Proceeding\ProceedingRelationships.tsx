import React, { useState, useEffect } from 'react';
import {
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  ScaleIcon,
  PlusIcon,
  TrashIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import AddRelationshipModal from './AddRelationshipModal';

interface ProceedingRelationshipsProps {
  proceedingId: number;
  onUpdate?: () => void;
}

interface RelatedTask {
  task_id: number;
  title: string;
  status: string;
  relationship_type: string;
  notes?: string;
  created_at: string;
}

interface RelatedDocument {
  document_id: number;
  title: string;
  status: string;
  relationship_type: string;
  notes?: string;
  created_at: string;
}

interface RelatedRegulation {
  regulation_id: number;
  title: string;
  status: string;
  relationship_type: string;
  notes?: string;
  created_at: string;
}

interface ProceedingRelationshipsSummary {
  proceeding_id: number;
  related_tasks: RelatedTask[];
  related_documents: RelatedDocument[];
  related_regulations: RelatedRegulation[];
}

const ProceedingRelationships: React.FC<ProceedingRelationshipsProps> = ({
  proceedingId,
  onUpdate
}) => {
  const { isAuthenticated, user } = useAuthStore();
  const [loading, setLoading] = useState(false);
  const [relationships, setRelationships] = useState<ProceedingRelationshipsSummary | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [addFormType, setAddFormType] = useState<'task' | 'document' | 'regulation'>('task');
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['tasks']));

  // Load relationships
  useEffect(() => {
    loadRelationships();
  }, [proceedingId]);

  const loadRelationships = async () => {
    try {
      setLoading(true);

      // Get the authentication token
      const token = localStorage.getItem('federal_register_token');
      if (!token) {
        console.error('No authentication token found');
        setRelationships({
          proceeding_id: proceedingId,
          related_tasks: [],
          related_documents: [],
          related_regulations: []
        });
        return;
      }

      const response = await fetch(`/api/v1/proceedings/${proceedingId}/relationships`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Handle the response structure properly
      if (data.data) {
        setRelationships(data.data);
      } else {
        // Set default empty structure if no data
        setRelationships({
          proceeding_id: proceedingId,
          related_tasks: [],
          related_documents: [],
          related_regulations: []
        });
      }
    } catch (error) {
      console.error('Error loading proceeding relationships:', error);
      // Set default empty structure on error
      setRelationships({
        proceeding_id: proceedingId,
        related_tasks: [],
        related_documents: [],
        related_regulations: []
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddRelationship = async (type: 'task' | 'document' | 'regulation', entityId: number, relationshipType: string, notes: string) => {
    try {
      await apiService.post(`/proceedings/${proceedingId}/link-${type}`, {
        [`${type}_id`]: entityId,
        relationship_type: relationshipType,
        notes
      });
      
      loadRelationships();
      setShowAddForm(false);
      onUpdate?.();
    } catch (error) {
      console.error(`Error adding ${type} relationship:`, error);
      throw error;
    }
  };

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const canEdit = isAuthenticated && user?.role && ['admin', 'editor'].includes(user.role);

  if (loading && !relationships) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Proceeding Relationships
          </h3>
          {canEdit && (
            <div className="flex space-x-2">
              <button
                onClick={() => { setAddFormType('task'); setShowAddForm(true); }}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Link Task
              </button>
              <button
                onClick={() => { setAddFormType('document'); setShowAddForm(true); }}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Link Document
              </button>
              <button
                onClick={() => { setAddFormType('regulation'); setShowAddForm(true); }}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500"
              >
                <PlusIcon className="h-4 w-4 mr-1" />
                Link Regulation
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="p-6">
        {!relationships ? (
          <div className="text-center py-8">
            <InformationCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No relationships found</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              This proceeding has no linked tasks, documents, or regulations yet.
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Related Tasks */}
            <div>
              <button
                onClick={() => toggleSection('tasks')}
                className="flex items-center w-full text-left"
              >
                {expandedSections.has('tasks') ? (
                  <ChevronDownIcon className="h-5 w-5 text-gray-400 mr-2" />
                ) : (
                  <ChevronRightIcon className="h-5 w-5 text-gray-400 mr-2" />
                )}
                <ClipboardDocumentListIcon className="h-5 w-5 text-blue-500 mr-2" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Related Tasks ({relationships.related_tasks?.length || 0})
                </span>
              </button>
              
              {expandedSections.has('tasks') && (
                <div className="mt-3 ml-7">
                  {!relationships.related_tasks || relationships.related_tasks.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400">No related tasks</p>
                  ) : (
                    <div className="space-y-2">
                      {relationships.related_tasks.map((task) => (
                        <div key={task.task_id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {task.title}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {task.relationship_type} • {task.status}
                            </p>
                            {task.notes && (
                              <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                                {task.notes}
                              </p>
                            )}
                          </div>
                          {canEdit && (
                            <button className="text-red-600 hover:text-red-800">
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Related Documents */}
            <div>
              <button
                onClick={() => toggleSection('documents')}
                className="flex items-center w-full text-left"
              >
                {expandedSections.has('documents') ? (
                  <ChevronDownIcon className="h-5 w-5 text-gray-400 mr-2" />
                ) : (
                  <ChevronRightIcon className="h-5 w-5 text-gray-400 mr-2" />
                )}
                <DocumentTextIcon className="h-5 w-5 text-green-500 mr-2" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Related Documents ({relationships.related_documents?.length || 0})
                </span>
              </button>
              
              {expandedSections.has('documents') && (
                <div className="mt-3 ml-7">
                  {!relationships.related_documents || relationships.related_documents.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400">No related documents</p>
                  ) : (
                    <div className="space-y-2">
                      {relationships.related_documents.map((document) => (
                        <div key={document.document_id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {document.title}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {document.relationship_type} • {document.status}
                            </p>
                            {document.notes && (
                              <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                                {document.notes}
                              </p>
                            )}
                          </div>
                          {canEdit && (
                            <button className="text-red-600 hover:text-red-800">
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Related Regulations */}
            <div>
              <button
                onClick={() => toggleSection('regulations')}
                className="flex items-center w-full text-left"
              >
                {expandedSections.has('regulations') ? (
                  <ChevronDownIcon className="h-5 w-5 text-gray-400 mr-2" />
                ) : (
                  <ChevronRightIcon className="h-5 w-5 text-gray-400 mr-2" />
                )}
                <ScaleIcon className="h-5 w-5 text-purple-500 mr-2" />
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  Related Regulations ({relationships.related_regulations?.length || 0})
                </span>
              </button>
              
              {expandedSections.has('regulations') && (
                <div className="mt-3 ml-7">
                  {!relationships.related_regulations || relationships.related_regulations.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400">No related regulations</p>
                  ) : (
                    <div className="space-y-2">
                      {relationships.related_regulations.map((regulation) => (
                        <div key={regulation.regulation_id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                          <div>
                            <p className="text-sm font-medium text-gray-900 dark:text-white">
                              {regulation.title}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {regulation.relationship_type} • {regulation.status}
                            </p>
                            {regulation.notes && (
                              <p className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                                {regulation.notes}
                              </p>
                            )}
                          </div>
                          {canEdit && (
                            <button className="text-red-600 hover:text-red-800">
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Add Relationship Form Modal */}
      {showAddForm && (
        <AddRelationshipModal
          type={addFormType}
          proceedingId={proceedingId}
          onClose={() => setShowAddForm(false)}
          onSuccess={() => {
            loadRelationships();
            setShowAddForm(false);
            onUpdate?.();
          }}
        />
      )}
    </div>
  );
};

export default ProceedingRelationships;
