'use client'

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  ExclamationTriangleIcon,
  HomeIcon,
  ArrowLeftIcon,
  MagnifyingGlassIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import Layout from './components/Layout/Layout';

const NotFoundPage: React.FC = () => {
  const router = useRouter();

  const handleGoBack = () => {
    if (window.history.length > 1) {
      router.back();
    } else {
      router.push('/');
    }
  };

  const popularPages = [
    { name: 'Documents', href: '/documents', icon: DocumentTextIcon },
    { name: 'Regulations', href: '/regulations', icon: DocumentTextIcon },
    { name: 'Agencies', href: '/agencies', icon: DocumentTextIcon },
    { name: 'Categories', href: '/categories', icon: DocumentTextIcon },
  ];

  return (
    <Layout>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 text-center">
          {/* Error Icon */}
          <div className="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-12 w-12 text-red-600" />
          </div>

          {/* Error Message */}
          <div>
            <h1 className="text-6xl font-bold text-gray-900 mb-4">404</h1>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h2>
            <p className="text-gray-600 mb-8">
              Sorry, we couldn't find the page you're looking for. The page may have been moved, 
              deleted, or the URL might be incorrect.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="space-y-4">
            <button
              onClick={handleGoBack}
              className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Go Back
            </button>

            <Link
              href="/"
              className="w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <HomeIcon className="h-4 w-4 mr-2" />
              Go to Homepage
            </Link>

            <Link
              href="/documents"
              className="w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
              Search Documents
            </Link>
          </div>

          {/* Popular Pages */}
          <div className="mt-12">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Popular Pages</h3>
            <div className="grid grid-cols-2 gap-3">
              {popularPages.map((page) => (
                <Link
                  key={page.name}
                  href={page.href}
                  className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <page.icon className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">{page.name}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* Help Text */}
          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 mb-2">Need Help?</h4>
            <p className="text-sm text-blue-700 mb-3">
              If you believe this is an error or you're having trouble finding what you need:
            </p>
            <div className="space-y-2">
              <Link
                href="/contact"
                className="block text-sm text-blue-600 hover:text-blue-800 underline"
              >
                Contact Support
              </Link>
              <Link
                href="/help"
                className="block text-sm text-blue-600 hover:text-blue-800 underline"
              >
                View Help Documentation
              </Link>
            </div>
          </div>

          {/* Error Code for Support */}
          <div className="text-xs text-gray-500">
            Error Code: 404 | Page Not Found
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default NotFoundPage;
