# PowerShell script to fix the corrupted agencies.go file
# This script removes duplicate package declarations and import blocks

$filePath = "internal\api\handlers\agencies.go"

Write-Host "Reading the corrupted agencies.go file..." -ForegroundColor Yellow

# Read all lines from the file
$lines = Get-Content $filePath

Write-Host "Original file has $($lines.Count) lines" -ForegroundColor Cyan

# Track if we're inside a duplicate package/import block
$insideDuplicateBlock = $false
$blockStartLine = -1
$cleanedLines = @()
$duplicatesFound = 0

for ($i = 0; $i -lt $lines.Count; $i++) {
    $line = $lines[$i]
    
    # Check if this is a duplicate package declaration (not the first one)
    if ($line -eq "package handlers" -and $i -gt 0) {
        # Look back to see if this is really a duplicate (not the first one)
        $foundFirstPackage = $false
        for ($j = 0; $j -lt $i; $j++) {
            if ($lines[$j] -eq "package handlers") {
                $foundFirstPackage = $true
                break
            }
        }
        
        if ($foundFirstPackage) {
            Write-Host "Found duplicate package declaration at line $($i + 1)" -ForegroundColor Red
            $insideDuplicateBlock = $true
            $blockStartLine = $i
            $duplicatesFound++
            continue
        }
    }
    
    # If we're inside a duplicate block, skip lines until we find the end
    if ($insideDuplicateBlock) {
        # Check if this line ends the import block (empty line after imports or start of actual code)
        if ($line -match "^\s*$" -and $i -gt $blockStartLine + 1) {
            # Check if the previous line was a closing parenthesis or import
            $prevLine = if ($i -gt 0) { $lines[$i - 1] } else { "" }
            if ($prevLine -match "^\s*\)\s*$" -or $prevLine -match "^\s*import" -or $prevLine -match "github\.com|federal-register-clone") {
                $insideDuplicateBlock = $false
                Write-Host "End of duplicate block found at line $($i + 1)" -ForegroundColor Green
                continue
            }
        }
        # Skip this line as it's part of a duplicate block
        continue
    }
    
    # Add the line to cleaned content
    $cleanedLines += $line
}

Write-Host "Removed $duplicatesFound duplicate package/import blocks" -ForegroundColor Green
Write-Host "Cleaned file has $($cleanedLines.Count) lines" -ForegroundColor Cyan

# Write the cleaned content back to the file
Write-Host "Writing cleaned content back to file..." -ForegroundColor Yellow
$cleanedLines | Set-Content $filePath -Encoding UTF8

Write-Host "File has been cleaned successfully!" -ForegroundColor Green
Write-Host "Removed $($lines.Count - $cleanedLines.Count) lines in total" -ForegroundColor Cyan
