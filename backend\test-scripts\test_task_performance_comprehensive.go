package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

const taskPerfBaseURL = "http://localhost:8080"

type LoginRequest struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

type LoginResponse struct {
	Token string `json:"token"`
	User  struct {
		ID    int    `json:"id"`
		Email string `json:"email"`
		Role  string `json:"role"`
	} `json:"user"`
}

type TaskRequest struct {
	Title        string  `json:"title"`
	Description  string  `json:"description"`
	Type         string  `json:"type"`
	Priority     string  `json:"priority"`
	Status       string  `json:"status"`
	DueDate      *string `json:"due_date,omitempty"`
	DocumentID   *int    `json:"document_id,omitempty"`
	RegulationID *int    `json:"regulation_id,omitempty"`
}

type TaskPerformanceRequest struct {
	PerformancePercentage  float64 `json:"performance_percentage"`
	DeadlineAdherenceScore float64 `json:"deadline_adherence_score"`
	QualityScore           float64 `json:"quality_score"`
	CompletionEfficiency   float64 `json:"completion_efficiency"`
	PriorityHandlingScore  float64 `json:"priority_handling_score"`
	PerformanceNotes       string  `json:"performance_notes"`
}

var taskPerfAuthToken string

func testTaskPerformanceMain() {
	log.Println("Starting comprehensive task performance evaluation testing...")

	// 1. Login as admin
	if err := login(); err != nil {
		log.Fatalf("Login failed: %v", err)
	}

	// 2. Test automatic performance calculation
	if err := testAutomaticPerformanceCalculation(); err != nil {
		log.Printf("Automatic performance calculation test failed: %v", err)
	}

	// 3. Test manual performance evaluation
	if err := testManualPerformanceEvaluation(); err != nil {
		log.Printf("Manual performance evaluation test failed: %v", err)
	}

	// 4. Test performance history tracking
	if err := testPerformanceHistoryTracking(); err != nil {
		log.Printf("Performance history tracking test failed: %v", err)
	}

	// 5. Test finance system sync
	if err := testFinanceSystemSync(); err != nil {
		log.Printf("Finance system sync test failed: %v", err)
	}

	// 6. Test scheduler functionality
	if err := testSchedulerFunctionality(); err != nil {
		log.Printf("Scheduler functionality test failed: %v", err)
	}

	// 7. Test edge cases
	if err := testEdgeCases(); err != nil {
		log.Printf("Edge cases test failed: %v", err)
	}

	log.Println("Task performance evaluation testing completed!")
}

func login() error {
	loginReq := LoginRequest{
		Email:    "<EMAIL>",
		Password: "admin123",
	}

	jsonData, _ := json.Marshal(loginReq)
	resp, err := http.Post(taskPerfBaseURL+"/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("login failed with status: %d", resp.StatusCode)
	}

	var loginResp LoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&loginResp); err != nil {
		return err
	}

	taskPerfAuthToken = loginResp.Token
	log.Printf("Logged in as: %s (Role: %s)", loginResp.User.Email, loginResp.User.Role)
	return nil
}

func makeAuthenticatedRequest(method, url string, body io.Reader) (*http.Response, error) {
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+taskPerfAuthToken)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	return client.Do(req)
}

func testAutomaticPerformanceCalculation() error {
	log.Println("Testing automatic performance calculation...")

	// Create a test task
	dueDate := time.Now().Add(24 * time.Hour).Format(time.RFC3339)
	taskReq := TaskRequest{
		Title:       "Test Task for Performance Calculation",
		Description: "This task is used to test automatic performance calculation",
		Type:        "general",
		Priority:    "high",
		Status:      "pending",
		DueDate:     &dueDate,
	}

	jsonData, _ := json.Marshal(taskReq)
	resp, err := makeAuthenticatedRequest("POST", taskPerfBaseURL+"/tasks", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("failed to create task: %d", resp.StatusCode)
	}

	var taskResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&taskResp); err != nil {
		return err
	}

	taskID := int(taskResp["id"].(float64))
	log.Printf("Created test task with ID: %d", taskID)

	// Complete the task
	resp, err = makeAuthenticatedRequest("POST", fmt.Sprintf("%s/tasks/%d/complete", taskPerfBaseURL, taskID), nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to complete task: %d", resp.StatusCode)
	}

	// Wait a moment for performance calculation
	time.Sleep(2 * time.Second)

	// Check if performance was calculated
	resp, err = makeAuthenticatedRequest("GET", fmt.Sprintf("%s/tasks/%d/performance", taskPerfBaseURL, taskID), nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to get task performance: %d", resp.StatusCode)
	}

	var perfResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&perfResp); err != nil {
		return err
	}

	data := perfResp["data"].(map[string]interface{})
	performance := data["performance"].(map[string]interface{})

	log.Printf("Automatic performance calculation results:")
	log.Printf("  Overall Performance: %.2f%%", performance["performance_percentage"].(float64))
	log.Printf("  Deadline Adherence: %.2f%%", performance["deadline_adherence_score"].(float64))
	log.Printf("  Quality Score: %.2f%%", performance["quality_score"].(float64))
	log.Printf("  Completion Efficiency: %.2f%%", performance["completion_efficiency"].(float64))
	log.Printf("  Priority Handling: %.2f%%", performance["priority_handling_score"].(float64))

	return nil
}

func testManualPerformanceEvaluation() error {
	log.Println("Testing manual performance evaluation...")

	// Create another test task
	taskReq := TaskRequest{
		Title:       "Test Task for Manual Performance",
		Description: "This task is used to test manual performance evaluation",
		Type:        "review",
		Priority:    "medium",
		Status:      "completed",
	}

	jsonData, _ := json.Marshal(taskReq)
	resp, err := makeAuthenticatedRequest("POST", baseURL+"/tasks", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("failed to create task: %d", resp.StatusCode)
	}

	var taskResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&taskResp); err != nil {
		return err
	}

	taskID := int(taskResp["id"].(float64))
	log.Printf("Created test task with ID: %d", taskID)

	// Manually set performance
	perfReq := TaskPerformanceRequest{
		PerformancePercentage:  85.5,
		DeadlineAdherenceScore: 90.0,
		QualityScore:           80.0,
		CompletionEfficiency:   85.0,
		PriorityHandlingScore:  88.0,
		PerformanceNotes:       "Manual evaluation test - good performance overall",
	}

	jsonData, _ = json.Marshal(perfReq)
	resp, err = makeAuthenticatedRequest("PUT", fmt.Sprintf("%s/tasks/%d/performance", baseURL, taskID), bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to update task performance: %d", resp.StatusCode)
	}

	log.Println("Manual performance evaluation completed successfully")
	return nil
}

func testPerformanceHistoryTracking() error {
	log.Println("Testing performance history tracking...")

	// Use the first task created
	taskID := 1 // Assuming first task exists

	// Get performance history
	resp, err := makeAuthenticatedRequest("GET", fmt.Sprintf("%s/tasks/%d/performance/history", baseURL, taskID), nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to get performance history: %d", resp.StatusCode)
	}

	var historyResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&historyResp); err != nil {
		return err
	}

	data := historyResp["data"].([]interface{})
	log.Printf("Performance history contains %d entries", len(data))

	return nil
}

func testFinanceSystemSync() error {
	log.Println("Testing finance system sync...")

	// Create a task linked to a document
	taskReq := TaskRequest{
		Title:       "Test Task for Finance Sync",
		Description: "This task is linked to a document to test finance sync",
		Type:        "general",
		Priority:    "high",
		Status:      "pending",
		DocumentID:  func() *int { id := 1; return &id }(), // Link to document ID 1
	}

	jsonData, _ := json.Marshal(taskReq)
	resp, err := makeAuthenticatedRequest("POST", baseURL+"/tasks", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("failed to create task: %d", resp.StatusCode)
	}

	var taskResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&taskResp); err != nil {
		return err
	}

	taskID := int(taskResp["id"].(float64))
	log.Printf("Created task with document link, ID: %d", taskID)

	// Complete the task to trigger performance calculation and finance sync
	resp, err = makeAuthenticatedRequest("POST", fmt.Sprintf("%s/tasks/%d/complete", baseURL, taskID), nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// Wait for sync to complete
	time.Sleep(3 * time.Second)

	log.Println("Finance system sync test completed")
	return nil
}

func testSchedulerFunctionality() error {
	log.Println("Testing scheduler functionality...")

	// Get scheduler status
	resp, err := makeAuthenticatedRequest("GET", baseURL+"/tasks/performance/scheduler/status", nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to get scheduler status: %d", resp.StatusCode)
	}

	var statusResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&statusResp); err != nil {
		return err
	}

	data := statusResp["data"].(map[string]interface{})
	log.Printf("Scheduler status: %v", data)

	// Trigger manual evaluation
	resp, err = makeAuthenticatedRequest("POST", baseURL+"/tasks/performance/scheduler/run", nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("failed to run manual evaluation: %d", resp.StatusCode)
	}

	log.Println("Scheduler functionality test completed")
	return nil
}

func testEdgeCases() error {
	log.Println("Testing edge cases...")

	// Test 1: Task with no due date
	taskReq := TaskRequest{
		Title:       "Task with No Due Date",
		Description: "Testing performance calculation for task without due date",
		Type:        "general",
		Priority:    "low",
		Status:      "pending",
	}

	jsonData, _ := json.Marshal(taskReq)
	resp, err := makeAuthenticatedRequest("POST", baseURL+"/tasks", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("failed to create task without due date: %d", resp.StatusCode)
	}

	// Test 2: Overdue task
	pastDate := time.Now().Add(-48 * time.Hour).Format(time.RFC3339)
	overdueTaskReq := TaskRequest{
		Title:       "Overdue Task",
		Description: "Testing performance calculation for overdue task",
		Type:        "urgent",
		Priority:    "urgent",
		Status:      "pending",
		DueDate:     &pastDate,
	}

	jsonData, _ = json.Marshal(overdueTaskReq)
	resp, err = makeAuthenticatedRequest("POST", baseURL+"/tasks", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return fmt.Errorf("failed to create overdue task: %d", resp.StatusCode)
	}

	log.Println("Edge cases test completed")
	return nil
}
