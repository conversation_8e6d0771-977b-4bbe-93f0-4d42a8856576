package main

import (
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// Minimal Task model for testing
type Task struct {
	ID           uint   `json:"id" gorm:"primaryKey"`
	Title        string `json:"title" gorm:"not null"`
	Description  string `json:"description" gorm:"type:text"`
	Status       string `json:"status" gorm:"not null;default:'pending'"`
	AssignedToID *uint  `json:"assigned_to_id"`
	CreatedByID  uint   `json:"created_by_id" gorm:"not null"`
}

func main() {
	// Load environment variables
	if err := godotenv.Load("../.env"); err != nil {
		log.Printf("Warning: .env file not found: %v", err)
	}

	// Database connection
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")

	if dbHost == "" {
		dbHost = "localhost"
	}
	if dbPort == "" {
		dbPort = "5432"
	}
	if dbUser == "" {
		dbUser = "postgres"
	}
	if dbName == "" {
		dbName = "federal_register_db"
	}

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=Asia/Shanghai",
		dbHost, dbUser, dbPassword, dbName, dbPort)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	fmt.Println("Connected to database successfully")

	// Test simple query without preloading
	var tasks []Task
	if err := db.Find(&tasks).Error; err != nil {
		fmt.Printf("Error querying tasks: %v\n", err)
		return
	}

	fmt.Printf("Found %d tasks\n", len(tasks))

	// Test query with limit
	var limitedTasks []Task
	if err := db.Limit(5).Find(&limitedTasks).Error; err != nil {
		fmt.Printf("Error querying limited tasks: %v\n", err)
		return
	}

	fmt.Printf("Found %d limited tasks\n", len(limitedTasks))
	fmt.Println("Direct database query successful!")
}
