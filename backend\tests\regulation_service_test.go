package tests

import (
	"testing"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type RegulationServiceTestSuite struct {
	suite.Suite
	db                *gorm.DB
	regulationService *services.RegulationService
	testUser          *models.User
	testAgency        *models.Agency
}

func (suite *RegulationServiceTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Set global database
	database.DB = db
	suite.db = db

	// Run migrations
	err = db.AutoMigrate(
		&models.User{},
		&models.Agency{},
		&models.LawsAndRules{},
		&models.RegulationDocumentVersion{},
		&models.Chunk{},
		&models.ChunkContentVersion{},
		&models.RegulationDocumentVersionChunkMap{},
	)
	suite.Require().NoError(err)

	// Initialize service
	suite.regulationService = services.NewRegulationService(db)

	// Create test data
	suite.createTestData()
}

func (suite *RegulationServiceTestSuite) createTestData() {
	// Create test agency
	agency := &models.Agency{
		Name:        "Test Service Agency",
		ShortName:   "TSA",
		Slug:        "test-service-agency",
		Description: "Test agency for service testing",
		IsActive:    true,
	}
	err := suite.db.Create(agency).Error
	suite.Require().NoError(err)
	suite.testAgency = agency

	// Create test user
	user := &models.User{
		Username:   "serviceuser",
		Email:      "<EMAIL>",
		FirstName:  "Service",
		LastName:   "User",
		Role:       "editor",
		IsActive:   true,
		IsVerified: true,
	}
	err = suite.db.Create(user).Error
	suite.Require().NoError(err)
	suite.testUser = user
}

func (suite *RegulationServiceTestSuite) TearDownSuite() {
	if suite.db != nil {
		sqlDB, _ := suite.db.DB()
		sqlDB.Close()
	}
}

func (suite *RegulationServiceTestSuite) TestCreateRegulation() {
	// Test data
	effectiveDate := time.Now().AddDate(0, 1, 0)
	regulation := &models.LawsAndRules{
		Title:         "Service Test Regulation",
		ShortTitle:    "STR",
		Type:          "rule",
		Status:        "draft",
		CFRTitle:      "40",
		AgencyID:      suite.testAgency.ID,
		CreatedByID:   suite.testUser.ID,
		Description:   "A regulation created via service",
		EffectiveDate: &effectiveDate,
		IsSignificant: true,
	}

	// Create regulation with empty initial chunks
	err := suite.regulationService.CreateRegulation(regulation, []services.CreateChunkRequest{})
	assert.NoError(suite.T(), err)
	assert.NotZero(suite.T(), regulation.ID)

	// Verify in database
	var dbRegulation models.LawsAndRules
	err = suite.db.First(&dbRegulation, regulation.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Service Test Regulation", dbRegulation.Title)
	assert.Equal(suite.T(), models.RegulationType("rule"), dbRegulation.Type)
}

func (suite *RegulationServiceTestSuite) TestGetRegulations() {
	// Create test regulations
	regulations := []*models.LawsAndRules{
		{
			Title:       "Rule Regulation",
			Type:        "rule",
			Status:      "effective",
			AgencyID:    suite.testAgency.ID,
			CreatedByID: suite.testUser.ID,
		},
		{
			Title:       "Law Regulation",
			Type:        "law",
			Status:      "effective",
			AgencyID:    suite.testAgency.ID,
			CreatedByID: suite.testUser.ID,
		},
		{
			Title:       "Draft Regulation",
			Type:        "rule",
			Status:      "draft",
			AgencyID:    suite.testAgency.ID,
			CreatedByID: suite.testUser.ID,
		},
	}

	for _, reg := range regulations {
		err := suite.db.Create(reg).Error
		suite.Require().NoError(err)
	}

	// Test getting all regulations
	filters := services.RegulationFilters{
		Page:    1,
		PerPage: 10,
	}
	results, total, err := suite.regulationService.GetRegulations(filters)
	assert.NoError(suite.T(), err)
	assert.GreaterOrEqual(suite.T(), len(results), 3)
	assert.GreaterOrEqual(suite.T(), total, int64(3))

	// Test filtering by type
	filters.Type = "rule"
	results, total, err = suite.regulationService.GetRegulations(filters)
	assert.NoError(suite.T(), err)
	assert.GreaterOrEqual(suite.T(), len(results), 2) // At least 2 rules
	for _, reg := range results {
		assert.Equal(suite.T(), models.RegulationType("rule"), reg.Type)
	}

	// Test filtering by status
	filters = services.RegulationFilters{
		Status:  "effective",
		Page:    1,
		PerPage: 10,
	}
	results, total, err = suite.regulationService.GetRegulations(filters)
	assert.NoError(suite.T(), err)
	assert.GreaterOrEqual(suite.T(), len(results), 2) // At least 2 effective
	for _, reg := range results {
		assert.Equal(suite.T(), "effective", reg.Status)
	}
}

func (suite *RegulationServiceTestSuite) TestGetRegulationByID() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:       "Get By ID Test",
		Type:        "rule",
		Status:      "effective",
		AgencyID:    suite.testAgency.ID,
		CreatedByID: suite.testUser.ID,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Test getting by ID
	result, err := suite.regulationService.GetRegulationByID(regulation.ID)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), regulation.ID, result.ID)
	assert.Equal(suite.T(), "Get By ID Test", result.Title)

	// Test getting non-existent regulation
	_, err = suite.regulationService.GetRegulationByID(99999)
	assert.Error(suite.T(), err)
}

func (suite *RegulationServiceTestSuite) TestUpdateRegulation() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:       "Original Update Title",
		Type:        "rule",
		Status:      "draft",
		AgencyID:    suite.testAgency.ID,
		CreatedByID: suite.testUser.ID,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Update regulation
	regulation.Title = "Updated Title"
	regulation.Type = "regulation"
	regulation.Status = "effective"

	err = suite.regulationService.UpdateRegulation(regulation)
	assert.NoError(suite.T(), err)

	// Verify update
	var updated models.LawsAndRules
	err = suite.db.First(&updated, regulation.ID).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Updated Title", updated.Title)
	assert.Equal(suite.T(), models.RegulationType("regulation"), updated.Type)
	assert.Equal(suite.T(), "effective", updated.Status)
}

func (suite *RegulationServiceTestSuite) TestDeleteRegulation() {
	// Create test regulation
	regulation := &models.LawsAndRules{
		Title:       "To Be Deleted Service",
		Type:        "rule",
		Status:      "draft",
		AgencyID:    suite.testAgency.ID,
		CreatedByID: suite.testUser.ID,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Delete regulation
	err = suite.regulationService.DeleteRegulation(regulation.ID)
	assert.NoError(suite.T(), err)

	// Verify soft delete
	var deleted models.LawsAndRules
	err = suite.db.First(&deleted, regulation.ID).Error
	assert.Error(suite.T(), err) // Should not find it in normal query

	// Verify it exists with Unscoped
	err = suite.db.Unscoped().First(&deleted, regulation.ID).Error
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), deleted.DeletedAt)
}

func (suite *RegulationServiceTestSuite) TestGetRegulationWithChunks() {
	// Create regulation with hierarchical chunks
	regulation := &models.LawsAndRules{
		Title:       "Chunks Test Regulation",
		Type:        "regulation",
		Status:      "effective",
		AgencyID:    suite.testAgency.ID,
		CreatedByID: suite.testUser.ID,
	}
	err := suite.db.Create(regulation).Error
	suite.Require().NoError(err)

	// Create chunks
	chapter := &models.Chunk{
		LawRuleID:       regulation.ID,
		ChunkType:       "chapter",
		ChunkIdentifier: "SERVICE-CHAPTER-I",
		Number:          "I",
		Title:           "Service Chapter",
		OrderInParent:   1,
	}
	err = suite.db.Create(chapter).Error
	suite.Require().NoError(err)

	section := &models.Chunk{
		LawRuleID:       regulation.ID,
		ParentChunkID:   &chapter.ID,
		ChunkType:       "section",
		ChunkIdentifier: "SERVICE-CHAPTER-I-SECTION-1001",
		Number:          "1001",
		Title:           "Service Section",
		OrderInParent:   1,
	}
	err = suite.db.Create(section).Error
	suite.Require().NoError(err)

	// Create content
	content := &models.ChunkContentVersion{
		ChunkID:           section.ID,
		Content:           "Service test content for the section.",
		VersionNumber:     1,
		IsCurrent:         true,
		IsActive:          true,
		ModifiedByID:      suite.testUser.ID,
		ChangeDescription: "Initial service content",
	}
	err = suite.db.Create(content).Error
	suite.Require().NoError(err)

	section.CurrentChunkContentVersionID = &content.ID
	err = suite.db.Save(section).Error
	suite.Require().NoError(err)

	// Create document version
	now := time.Now()
	version := &models.RegulationDocumentVersion{
		LawRuleID:       regulation.ID,
		VersionNumber:   "1.0.0",
		PublicationDate: &now,
		EffectiveDate:   &now,
		IsOfficial:      true,
		CreatedByID:     suite.testUser.ID,
	}
	err = suite.db.Create(version).Error
	suite.Require().NoError(err)

	regulation.CurrentDocumentVersionID = &version.ID
	err = suite.db.Save(regulation).Error
	suite.Require().NoError(err)

	// Test getting regulation with chunks
	result, err := suite.regulationService.GetRegulationWithChunks(regulation.ID, nil)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), regulation.ID, result.Regulation.ID)
	assert.Len(suite.T(), result.Chunks, 1) // Should have 1 root chunk (chapter)

	// Verify hierarchical structure
	assert.Equal(suite.T(), "chapter", result.Chunks[0].ChunkType)
	assert.Len(suite.T(), result.Chunks[0].Children, 1) // Chapter should have 1 child (section)
	assert.Equal(suite.T(), "section", result.Chunks[0].Children[0].ChunkType)
	assert.NotNil(suite.T(), result.Chunks[0].Children[0].CurrentContent)
}

func TestRegulationServiceTestSuite(t *testing.T) {
	suite.Run(t, new(RegulationServiceTestSuite))
}
