# Field Preloading System Documentation

## Overview

The Field Preloading System automatically populates form fields with intelligent default values based on Federal Register best practices and user workflow optimization. This system reduces manual data entry while maintaining full user control over all field values.

## Architecture

### Backend Components

1. **PreloadingService** (`backend/internal/services/preloading_service.go`)
   - Core service handling all preloading logic
   - Generates sequential numbers (FR numbers, docket numbers)
   - Calculates intelligent defaults based on context

2. **PreloadingHandler** (`backend/internal/api/handlers/preloading.go`)
   - REST API endpoints for frontend consumption
   - Handles authentication and parameter validation

3. **Router Integration** (`backend/internal/api/router.go`)
   - Protected endpoints under `/api/v1/preloading/`
   - Requires authentication for all preloading operations

### Frontend Components

1. **Form Integration**
   - Document creation form (`frontend/app/documents/new/page.tsx`)
   - Regulation creation form (`frontend/app/regulations/new/page.tsx`)
   - Agency creation form (`frontend/app/agencies/new/page.tsx`)
   - Category creation form (`frontend/app/categories/new/page.tsx`)
   - Proceeding creation form (`frontend/app/components/Proceeding/ProceedingForm.tsx`)
   - Finance creation form (`frontend/app/components/Finance/FinanceModal.tsx`)

## API Endpoints

### Document Preloading
```
GET /api/v1/preloading/documents?agency_id={id}&category_ids={id1,id2}
```
**Response:**
```json
{
  "publication_date": "2025-07-05",
  "effective_date": "2025-08-04",
  "comment_due_date": "2025-09-03",
  "fr_document_number": "2025-001-0001",
  "docket_number": "EPA-ENV-RULE-0001",
  "language": "en",
  "original_format": "pdf",
  "status": "draft",
  "type": "rule",
  "visibility_level": 1,
  "is_public": true,
  "accepts_comments": true
}
```

### Regulation Preloading
```
GET /api/v1/preloading/regulations?parent_id={id}
```
**Response:**
```json
{
  "enactment_date": "2025-07-05",
  "publication_date": "2025-07-05",
  "effective_date": "2025-08-04",
  "version_number": "1.0.0",
  "status": "draft",
  "type": "regulation",
  "hierarchy_level": "regulation",
  "is_significant": false,
  "order_in_parent": 1
}
```

### Agency Preloading
```
GET /api/v1/preloading/agencies
```
**Response:**
```json
{
  "established_at": "2025-07-05",
  "country": "US",
  "is_active": true,
  "agency_type": "federal",
  "primary_color": "#3B82F6",
  "secondary_color": "#1E40AF"
}
```

### Category Preloading
```
GET /api/v1/preloading/categories
```
**Response:**
```json
{
  "color": "#3B82F6",
  "sort_order": 5,
  "is_active": true
}
```

### Proceeding Preloading
```
GET /api/v1/preloading/proceedings?name={proceeding_name}
```
**Response:**
```json
{
  "initiation_date": "2025-07-05",
  "planned_start_date": "2025-07-05",
  "planned_end_date": "2025-10-03",
  "unique_id": "Test Proceeding 2025-07-05",
  "status": "planning",
  "priority": "medium",
  "requires_mandatory_review": true,
  "minimum_steps_required": 5,
  "sequential_execution": true,
  "is_public": false,
  "total_steps": 0,
  "completed_steps": 0,
  "failed_steps": 0,
  "progress_percent": 0.0
}
```

### Finance Preloading
```
GET /api/v1/preloading/finances
```
**Response:**
```json
{
  "year": 2025,
  "budget_type": "original",
  "performance_percentage": 100.00,
  "is_auto_calculated": false
}
```

### Utility Endpoints

#### Slug Generation
```
GET /api/v1/preloading/slug?text={text_to_convert}
```
**Response:**
```json
{
  "slug": "environmental-protection-agency"
}
```

#### FR Number Generation
```
GET /api/v1/preloading/fr-number
```
**Response:**
```json
{
  "fr_document_number": "2025-001-0042"
}
```

#### Docket Number Generation
```
GET /api/v1/preloading/docket-number?agency_id={id}&category_ids={id1,id2}
```
**Response:**
```json
{
  "docket_number": "EPA-ENV-RULE-0015"
}
```

## Field Preloading Rules

### Auto-Generated Fields (No User Input Required)
- **Timestamps**: `created_at`, `updated_at`
- **Sequential Numbers**: FR document numbers, docket numbers, proceeding unique IDs
- **Calculated Values**: View counts, download counts, progress percentages
- **Derived Fields**: Slugs from names, short names from full names

### Preloaded with User Override
- **Dates**: Creation dates (today), publication dates (today), effective dates (publication + 30 days)
- **Status Fields**: Default statuses appropriate for each entity type
- **Configuration**: Default colors, sort orders, visibility levels
- **Flags**: Default boolean values based on best practices

### Smart Date Calculations
- **Effective Date**: Publication date + 30 days (Federal Register standard)
- **Comment Due Date**: Publication date + 60 days (Federal Register standard)
- **Proceeding End Date**: Start date + 90 days (administrative proceeding standard)

## Frontend Integration

### Loading Defaults on Form Initialization
```javascript
const loadDefaultValues = async () => {
  try {
    const response = await fetch('/api/v1/preloading/documents', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });

    if (response.ok) {
      const defaults = await response.json();
      setFormData(prev => ({
        ...prev,
        ...formatDefaults(defaults)
      }));
    }
  } catch (err) {
    console.error('Error loading default values:', err);
  }
};
```

### Dynamic Regeneration
```javascript
const handleAgencyChange = (agencyId) => {
  setFormData(prev => ({ ...prev, agency_id: agencyId }));
  
  // Regenerate docket number when agency changes
  if (agencyId) {
    regenerateDocketNumber(agencyId, formData.category_ids);
  }
};
```

### Smart Date Updates
```javascript
const calculateRelatedDates = (publicationDate) => {
  const pubDate = new Date(publicationDate);
  const effectiveDate = new Date(pubDate);
  effectiveDate.setDate(effectiveDate.getDate() + 30);
  
  setFormData(prev => ({
    ...prev,
    effective_date: prev.effective_date || formatDate(effectiveDate)
  }));
};
```

## Testing

### Backend Testing
Run the comprehensive backend test:
```bash
cd backend
go run test_preloading_comprehensive.go
```

### Frontend Testing
1. Open browser console on any form page
2. Load the test script: Copy contents of `frontend/test-preloading.js`
3. Run tests:
```javascript
const tester = new PreloadingTester();
tester.runAllTests();
```

## Best Practices

### For Developers
1. **Always provide fallbacks**: If API calls fail, use sensible hardcoded defaults
2. **Preserve user input**: Don't override fields that users have already modified
3. **Validate generated values**: Ensure auto-generated numbers are unique and properly formatted
4. **Handle edge cases**: Account for missing agencies, categories, or other dependencies

### For Users
1. **Review defaults**: All preloaded values can be modified before saving
2. **Understand calculations**: Date calculations follow Federal Register standards
3. **Use regeneration**: Click regenerate buttons to get new sequential numbers
4. **Context matters**: Some defaults change based on selected agencies or categories

## Configuration

### Environment Variables
- Database connection settings affect sequential number generation
- No additional configuration required for basic preloading functionality

### Customization
- Modify `PreloadingService` methods to adjust default values
- Update date calculation logic in `calculateRelatedDates` functions
- Customize sequential number formats in generation methods

## Troubleshooting

### Common Issues
1. **Missing defaults**: Check authentication and API connectivity
2. **Incorrect dates**: Verify system clock and timezone settings
3. **Duplicate numbers**: Ensure database transactions are properly handled
4. **Performance**: Consider caching for frequently accessed defaults

### Debug Mode
Enable detailed logging by setting appropriate log levels in the backend configuration.
