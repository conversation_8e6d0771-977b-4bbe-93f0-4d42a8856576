package services

import (
	"fmt"
	"time"

	"federal-register-clone/internal/config"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// RetentionPolicyService handles document retention policy operations
type RetentionPolicyService struct {
	db                  *gorm.DB
	notificationService *NotificationService
}

// NewRetentionPolicyService creates a new retention policy service
func NewRetentionPolicyService(db *gorm.DB, cfg *config.Config) *RetentionPolicyService {
	notificationService := NewNotificationService(db, cfg)
	return &RetentionPolicyService{
		db:                  db,
		notificationService: notificationService,
	}
}

// CreateRetentionPolicy creates a new retention policy
func (s *RetentionPolicyService) CreateRetentionPolicy(req CreateRetentionPolicyRequest) (*models.RetentionPolicy, error) {
	policy := &models.RetentionPolicy{
		Name:                 req.Name,
		Description:          req.Description,
		Type:                 req.Type,
		Status:               models.RetentionStatusActive,
		RetentionPeriodDays:  req.RetentionPeriodDays,
		RetentionPeriodYears: req.RetentionPeriodYears,
		Action:               req.Action,
		AutoExecute:          req.AutoExecute,
		TriggerEvent:         req.TriggerEvent,
		TriggerDocumentType:  req.TriggerDocumentType,
		TriggerCategory:      req.TriggerCategory,
		TriggerAgency:        req.TriggerAgency,
		LegalHoldEnabled:     req.LegalHoldEnabled,
		LegalHoldReason:      req.LegalHoldReason,
		LegalHoldStartDate:   req.LegalHoldStartDate,
		LegalHoldEndDate:     req.LegalHoldEndDate,
		LegalHoldContact:     req.LegalHoldContact,
		RegulatoryFramework:  req.RegulatoryFramework,
		ComplianceNotes:      req.ComplianceNotes,
		AuditRequired:        req.AuditRequired,
		NotifyBeforeDays:     req.NotifyBeforeDays,
		NotificationEmails:   req.NotificationEmails,
		EscalationEmails:     req.EscalationEmails,
		CustomRules:          req.CustomRules,
		ExceptionRules:       req.ExceptionRules,
		CreatedByID:          req.CreatedByID,
		OwnerID:              req.OwnerID,
		RequiresApproval:     req.RequiresApproval,
	}

	// Calculate next execution date if auto-execute is enabled
	if policy.AutoExecute {
		policy.NextExecutionAt = s.calculateNextExecution(policy)
	}

	if err := s.db.Create(policy).Error; err != nil {
		return nil, fmt.Errorf("failed to create retention policy: %w", err)
	}

	return policy, nil
}

// GetRetentionPolicies retrieves retention policies with filtering
func (s *RetentionPolicyService) GetRetentionPolicies(filter RetentionPolicyFilter) ([]models.RetentionPolicy, error) {
	query := s.db.Preload("CreatedBy").Preload("Owner").Preload("ApprovedBy")

	if filter.Type != "" {
		query = query.Where("type = ?", filter.Type)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.OwnerID != 0 {
		query = query.Where("owner_id = ?", filter.OwnerID)
	}
	if filter.RegulatoryFramework != "" {
		query = query.Where("regulatory_framework = ?", filter.RegulatoryFramework)
	}

	var policies []models.RetentionPolicy
	if err := query.Order("created_at DESC").Find(&policies).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve retention policies: %w", err)
	}

	return policies, nil
}

// GetRetentionPolicy retrieves a specific retention policy
func (s *RetentionPolicyService) GetRetentionPolicy(id uint) (*models.RetentionPolicy, error) {
	var policy models.RetentionPolicy
	if err := s.db.Preload("CreatedBy").Preload("Owner").Preload("ApprovedBy").
		Preload("PolicyAssignments").Preload("ExecutionLogs").
		Where("id = ?", id).First(&policy).Error; err != nil {
		return nil, fmt.Errorf("retention policy not found: %w", err)
	}

	return &policy, nil
}

// UpdateRetentionPolicy updates a retention policy
func (s *RetentionPolicyService) UpdateRetentionPolicy(id uint, req UpdateRetentionPolicyRequest) (*models.RetentionPolicy, error) {
	var policy models.RetentionPolicy
	if err := s.db.Where("id = ?", id).First(&policy).Error; err != nil {
		return nil, fmt.Errorf("retention policy not found: %w", err)
	}

	// Update fields
	if req.Name != "" {
		policy.Name = req.Name
	}
	if req.Description != "" {
		policy.Description = req.Description
	}
	if req.Status != "" {
		policy.Status = models.RetentionStatus(req.Status)
	}
	if req.RetentionPeriodDays != nil {
		policy.RetentionPeriodDays = *req.RetentionPeriodDays
	}
	if req.RetentionPeriodYears != nil {
		policy.RetentionPeriodYears = *req.RetentionPeriodYears
	}
	if req.Action != "" {
		policy.Action = models.RetentionAction(req.Action)
	}
	if req.AutoExecute != nil {
		policy.AutoExecute = *req.AutoExecute
	}
	if req.NotifyBeforeDays != nil {
		policy.NotifyBeforeDays = *req.NotifyBeforeDays
	}

	// Recalculate next execution if auto-execute changed
	if req.AutoExecute != nil && *req.AutoExecute {
		policy.NextExecutionAt = s.calculateNextExecution(&policy)
	}

	if err := s.db.Save(&policy).Error; err != nil {
		return nil, fmt.Errorf("failed to update retention policy: %w", err)
	}

	return &policy, nil
}

// DeleteRetentionPolicy deletes a retention policy
func (s *RetentionPolicyService) DeleteRetentionPolicy(id uint) error {
	// Check if policy has active assignments
	var assignmentCount int64
	s.db.Model(&models.RetentionPolicyAssignment{}).
		Where("policy_id = ? AND is_active = ?", id, true).
		Count(&assignmentCount)

	if assignmentCount > 0 {
		return fmt.Errorf("cannot delete policy with active assignments")
	}

	if err := s.db.Delete(&models.RetentionPolicy{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete retention policy: %w", err)
	}

	return nil
}

// AssignPolicyToDocument assigns a retention policy to a document
func (s *RetentionPolicyService) AssignPolicyToDocument(req AssignPolicyRequest) error {
	// Check if assignment already exists
	var existingAssignment models.RetentionPolicyAssignment
	if err := s.db.Where("policy_id = ? AND document_id = ? AND is_active = ?",
		req.PolicyID, req.DocumentID, true).First(&existingAssignment).Error; err == nil {
		return fmt.Errorf("policy already assigned to this document")
	}

	assignment := &models.RetentionPolicyAssignment{
		PolicyID:              req.PolicyID,
		DocumentID:            req.DocumentID,
		CategoryID:            req.CategoryID,
		AgencyID:              req.AgencyID,
		EffectiveDate:         req.EffectiveDate,
		ExpirationDate:        req.ExpirationDate,
		OverrideRetentionDays: req.OverrideRetentionDays,
		OverrideAction:        req.OverrideAction,
		OverrideReason:        req.OverrideReason,
		AssignedByID:          req.AssignedByID,
		Notes:                 req.Notes,
	}

	if err := s.db.Create(assignment).Error; err != nil {
		return fmt.Errorf("failed to assign policy: %w", err)
	}

	// Create document retention status
	if req.DocumentID != nil {
		s.createDocumentRetentionStatus(*req.DocumentID, req.PolicyID, req.EffectiveDate)
	}

	return nil
}

// ExecuteRetentionPolicy executes a retention policy
func (s *RetentionPolicyService) ExecuteRetentionPolicy(policyID uint, executedByID *uint, executionType string) (*models.RetentionExecutionLog, error) {
	var policy models.RetentionPolicy
	if err := s.db.Where("id = ?", policyID).First(&policy).Error; err != nil {
		return nil, fmt.Errorf("retention policy not found: %w", err)
	}

	startTime := time.Now()
	log := &models.RetentionExecutionLog{
		PolicyID:      policyID,
		ExecutionDate: startTime,
		ExecutionType: executionType,
		Action:        policy.Action,
		Status:        "running",
		ExecutedByID:  executedByID,
		StartTime:     startTime,
	}

	// Create execution log
	if err := s.db.Create(log).Error; err != nil {
		return nil, fmt.Errorf("failed to create execution log: %w", err)
	}

	// Get documents that need action
	documents, err := s.getDocumentsForRetentionAction(policyID)
	if err != nil {
		log.Status = "failed"
		log.ErrorDetails = err.Error()
		s.db.Save(log)
		return log, err
	}

	// Execute action on documents
	results := s.executeRetentionAction(documents, policy.Action)

	// Update execution log with results
	endTime := time.Now()
	log.EndTime = &endTime
	log.DurationMs = int(endTime.Sub(startTime).Milliseconds())
	log.DocumentsProcessed = results.Processed
	log.DocumentsArchived = results.Archived
	log.DocumentsDeleted = results.Deleted
	log.DocumentsSkipped = results.Skipped
	log.ErrorCount = results.Errors
	log.Status = "success"
	if results.Errors > 0 {
		log.Status = "partial"
	}

	// Update policy statistics
	policy.LastExecutedAt = &endTime
	policy.ExecutionCount++
	policy.DocumentsAffected += results.Processed
	policy.DocumentsArchived += results.Archived
	policy.DocumentsDeleted += results.Deleted

	// Calculate next execution
	if policy.AutoExecute {
		policy.NextExecutionAt = s.calculateNextExecution(&policy)
	}

	s.db.Save(&policy)
	s.db.Save(log)

	return log, nil
}

// GetDocumentRetentionStatus gets retention status for a document
func (s *RetentionPolicyService) GetDocumentRetentionStatus(documentID uint) ([]models.DocumentRetentionStatus, error) {
	var statuses []models.DocumentRetentionStatus
	if err := s.db.Preload("Policy").Where("document_id = ?", documentID).Find(&statuses).Error; err != nil {
		return nil, fmt.Errorf("failed to get document retention status: %w", err)
	}

	return statuses, nil
}

// Helper functions

func (s *RetentionPolicyService) calculateNextExecution(policy *models.RetentionPolicy) *time.Time {
	// Simple daily execution for now - can be enhanced with more sophisticated scheduling
	next := time.Now().AddDate(0, 0, 1)
	return &next
}

func (s *RetentionPolicyService) createDocumentRetentionStatus(documentID, policyID uint, effectiveDate time.Time) error {
	var policy models.RetentionPolicy
	if err := s.db.Where("id = ?", policyID).First(&policy).Error; err != nil {
		return err
	}

	retentionEndDate := effectiveDate.AddDate(policy.RetentionPeriodYears, 0, policy.RetentionPeriodDays)
	actionDueDate := retentionEndDate.AddDate(0, 0, -policy.NotifyBeforeDays)

	status := &models.DocumentRetentionStatus{
		DocumentID:         documentID,
		PolicyID:           policyID,
		RetentionStartDate: effectiveDate,
		RetentionEndDate:   retentionEndDate,
		ActionDueDate:      actionDueDate,
		Status:             "active",
		Action:             policy.Action,
		IsLegalHold:        policy.LegalHoldEnabled,
		LegalHoldReason:    policy.LegalHoldReason,
	}

	return s.db.Create(status).Error
}

func (s *RetentionPolicyService) getDocumentsForRetentionAction(policyID uint) ([]models.Document, error) {
	var documents []models.Document

	// Get documents through retention status that are due for action
	if err := s.db.Table("documents").
		Joins("JOIN document_retention_status ON documents.id = document_retention_status.document_id").
		Where("document_retention_status.policy_id = ? AND document_retention_status.action_due_date <= ? AND document_retention_status.status = ?",
			policyID, time.Now(), "active").
		Find(&documents).Error; err != nil {
		return nil, err
	}

	return documents, nil
}

func (s *RetentionPolicyService) executeRetentionAction(documents []models.Document, action models.RetentionAction) RetentionExecutionResults {
	results := RetentionExecutionResults{}

	for _, doc := range documents {
		results.Processed++

		switch action {
		case models.ActionArchive:
			if err := s.archiveDocument(doc.ID); err != nil {
				results.Errors++
			} else {
				results.Archived++
			}
		case models.ActionDelete:
			if err := s.deleteDocument(doc.ID); err != nil {
				results.Errors++
			} else {
				results.Deleted++
			}
		case models.ActionReview:
			// Flag for review - update status
			s.db.Model(&models.DocumentRetentionStatus{}).
				Where("document_id = ?", doc.ID).
				Update("status", "review_required")
		case models.ActionNotify:
			// Send real notification using notification service
			if s.notificationService != nil {
				// Get retention status and policy details for notification
				var retentionStatus models.DocumentRetentionStatus
				if err := s.db.Preload("Policy").Where("document_id = ?", doc.ID).First(&retentionStatus).Error; err == nil {
					err := s.notificationService.SendRetentionPolicyNotification(
						doc.ID,
						retentionStatus.Policy.Name,
						string(models.ActionNotify),
						retentionStatus.ActionDueDate,
					)
					if err != nil {
						fmt.Printf("Failed to send retention notification: %v\n", err)
					}
				}
			}

			// Update last notified timestamp
			s.db.Model(&models.DocumentRetentionStatus{}).
				Where("document_id = ?", doc.ID).
				Update("last_notified_at", time.Now())
		default:
			results.Skipped++
		}
	}

	return results
}

func (s *RetentionPolicyService) archiveDocument(documentID uint) error {
	// Implement document archival logic
	// For now, just update document status
	return s.db.Model(&models.Document{}).
		Where("id = ?", documentID).
		Update("status", "archived").Error
}

func (s *RetentionPolicyService) deleteDocument(documentID uint) error {
	// Implement document deletion logic
	// For now, just soft delete
	return s.db.Delete(&models.Document{}, documentID).Error
}

// Request/Response types
type CreateRetentionPolicyRequest struct {
	Name                 string                     `json:"name"`
	Description          string                     `json:"description"`
	Type                 models.RetentionPolicyType `json:"type"`
	RetentionPeriodDays  int                        `json:"retention_period_days"`
	RetentionPeriodYears int                        `json:"retention_period_years"`
	Action               models.RetentionAction     `json:"action"`
	AutoExecute          bool                       `json:"auto_execute"`
	TriggerEvent         string                     `json:"trigger_event"`
	TriggerDocumentType  string                     `json:"trigger_document_type"`
	TriggerCategory      string                     `json:"trigger_category"`
	TriggerAgency        string                     `json:"trigger_agency"`
	LegalHoldEnabled     bool                       `json:"legal_hold_enabled"`
	LegalHoldReason      string                     `json:"legal_hold_reason"`
	LegalHoldStartDate   *time.Time                 `json:"legal_hold_start_date"`
	LegalHoldEndDate     *time.Time                 `json:"legal_hold_end_date"`
	LegalHoldContact     string                     `json:"legal_hold_contact"`
	RegulatoryFramework  string                     `json:"regulatory_framework"`
	ComplianceNotes      string                     `json:"compliance_notes"`
	AuditRequired        bool                       `json:"audit_required"`
	NotifyBeforeDays     int                        `json:"notify_before_days"`
	NotificationEmails   string                     `json:"notification_emails"`
	EscalationEmails     string                     `json:"escalation_emails"`
	CustomRules          string                     `json:"custom_rules"`
	ExceptionRules       string                     `json:"exception_rules"`
	CreatedByID          uint                       `json:"created_by_id"`
	OwnerID              uint                       `json:"owner_id"`
	RequiresApproval     bool                       `json:"requires_approval"`
}

type UpdateRetentionPolicyRequest struct {
	Name                 string `json:"name"`
	Description          string `json:"description"`
	Status               string `json:"status"`
	RetentionPeriodDays  *int   `json:"retention_period_days"`
	RetentionPeriodYears *int   `json:"retention_period_years"`
	Action               string `json:"action"`
	AutoExecute          *bool  `json:"auto_execute"`
	NotifyBeforeDays     *int   `json:"notify_before_days"`
}

type RetentionPolicyFilter struct {
	Type                string `json:"type"`
	Status              string `json:"status"`
	OwnerID             uint   `json:"owner_id"`
	RegulatoryFramework string `json:"regulatory_framework"`
}

type AssignPolicyRequest struct {
	PolicyID              uint                    `json:"policy_id"`
	DocumentID            *uint                   `json:"document_id"`
	CategoryID            *uint                   `json:"category_id"`
	AgencyID              *uint                   `json:"agency_id"`
	EffectiveDate         time.Time               `json:"effective_date"`
	ExpirationDate        *time.Time              `json:"expiration_date"`
	OverrideRetentionDays *int                    `json:"override_retention_days"`
	OverrideAction        *models.RetentionAction `json:"override_action"`
	OverrideReason        string                  `json:"override_reason"`
	AssignedByID          uint                    `json:"assigned_by_id"`
	Notes                 string                  `json:"notes"`
}

type RetentionExecutionResults struct {
	Processed int `json:"processed"`
	Archived  int `json:"archived"`
	Deleted   int `json:"deleted"`
	Skipped   int `json:"skipped"`
	Errors    int `json:"errors"`
}
