package models

import (
	"time"

	"gorm.io/gorm"
)

// RetentionPolicyType represents the type of retention policy
type RetentionPolicyType string

const (
	RetentionTypeTime     RetentionPolicyType = "time_based"     // Based on time duration
	RetentionTypeEvent    RetentionPolicyType = "event_based"    // Based on specific events
	RetentionTypeLegal    RetentionPolicyType = "legal_hold"     // Legal hold (indefinite)
	RetentionTypeCustom   RetentionPolicyType = "custom"         // Custom rules
	RetentionTypeRegulatory RetentionPolicyType = "regulatory"   // Regulatory compliance
)

// RetentionAction represents what happens when retention period expires
type RetentionAction string

const (
	ActionArchive RetentionAction = "archive"    // Move to archive storage
	ActionDelete  RetentionAction = "delete"     // Permanently delete
	ActionReview  RetentionAction = "review"     // Flag for manual review
	ActionNotify  RetentionAction = "notify"     // Send notification only
)

// RetentionStatus represents the current status of a retention policy
type RetentionStatus string

const (
	RetentionStatusActive    RetentionStatus = "active"
	RetentionStatusInactive  RetentionStatus = "inactive"
	RetentionStatusSuspended RetentionStatus = "suspended"
	RetentionStatusExpired   RetentionStatus = "expired"
)

// RetentionPolicy represents a document retention policy
type RetentionPolicy struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Policy identification
	Name        string              `json:"name" gorm:"not null"`
	Description string              `json:"description" gorm:"type:text"`
	Type        RetentionPolicyType `json:"type" gorm:"not null"`
	Status      RetentionStatus     `json:"status" gorm:"default:'active'"`

	// Retention rules
	RetentionPeriodDays int             `json:"retention_period_days"`                    // Days to retain
	RetentionPeriodYears int            `json:"retention_period_years"`                  // Years to retain
	Action              RetentionAction `json:"action" gorm:"default:'archive'"`         // Action when expired
	AutoExecute         bool            `json:"auto_execute" gorm:"default:false"`       // Auto-execute action
	
	// Trigger conditions
	TriggerEvent        string `json:"trigger_event"`                                   // Event that starts retention
	TriggerDocumentType string `json:"trigger_document_type"`                          // Document type filter
	TriggerCategory     string `json:"trigger_category"`                               // Category filter
	TriggerAgency       string `json:"trigger_agency"`                                 // Agency filter
	
	// Legal hold settings
	LegalHoldEnabled    bool       `json:"legal_hold_enabled" gorm:"default:false"`
	LegalHoldReason     string     `json:"legal_hold_reason"`
	LegalHoldStartDate  *time.Time `json:"legal_hold_start_date"`
	LegalHoldEndDate    *time.Time `json:"legal_hold_end_date"`
	LegalHoldContact    string     `json:"legal_hold_contact"`

	// Compliance settings
	RegulatoryFramework string `json:"regulatory_framework"`                           // e.g., "SOX", "GDPR", "HIPAA"
	ComplianceNotes     string `json:"compliance_notes" gorm:"type:text"`
	AuditRequired       bool   `json:"audit_required" gorm:"default:false"`

	// Notification settings
	NotifyBeforeDays    int    `json:"notify_before_days" gorm:"default:30"`           // Days before action
	NotificationEmails  string `json:"notification_emails" gorm:"type:text"`          // JSON array of emails
	EscalationEmails    string `json:"escalation_emails" gorm:"type:text"`            // JSON array for escalation

	// Advanced rules (JSON)
	CustomRules         string `json:"custom_rules" gorm:"type:text"`                  // JSON rules engine
	ExceptionRules      string `json:"exception_rules" gorm:"type:text"`              // JSON exception rules
	
	// Policy ownership
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	OwnerID     uint `json:"owner_id" gorm:"not null"`
	Owner       User `json:"owner" gorm:"foreignKey:OwnerID"`

	// Approval workflow
	RequiresApproval bool       `json:"requires_approval" gorm:"default:true"`
	ApprovedAt       *time.Time `json:"approved_at"`
	ApprovedByID     *uint      `json:"approved_by_id"`
	ApprovedBy       *User      `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`

	// Execution tracking
	LastExecutedAt   *time.Time `json:"last_executed_at"`
	NextExecutionAt  *time.Time `json:"next_execution_at"`
	ExecutionCount   int        `json:"execution_count" gorm:"default:0"`
	
	// Statistics
	DocumentsAffected int `json:"documents_affected" gorm:"default:0"`
	DocumentsArchived int `json:"documents_archived" gorm:"default:0"`
	DocumentsDeleted  int `json:"documents_deleted" gorm:"default:0"`

	// Relationships
	PolicyAssignments []RetentionPolicyAssignment `json:"policy_assignments,omitempty" gorm:"foreignKey:PolicyID"`
	ExecutionLogs     []RetentionExecutionLog     `json:"execution_logs,omitempty" gorm:"foreignKey:PolicyID"`
}

// RetentionPolicyAssignment represents assignment of policy to documents/categories
type RetentionPolicyAssignment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	PolicyID uint            `json:"policy_id" gorm:"not null"`
	Policy   RetentionPolicy `json:"policy" gorm:"foreignKey:PolicyID"`

	// Assignment target (one of these will be set)
	DocumentID *uint     `json:"document_id"`
	Document   *Document `json:"document,omitempty" gorm:"foreignKey:DocumentID"`
	CategoryID *uint     `json:"category_id"`
	Category   *Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	AgencyID   *uint     `json:"agency_id"`
	Agency     *Agency   `json:"agency,omitempty" gorm:"foreignKey:AgencyID"`

	// Assignment details
	AssignedAt      time.Time  `json:"assigned_at"`
	EffectiveDate   time.Time  `json:"effective_date"`
	ExpirationDate  *time.Time `json:"expiration_date"`
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	
	// Override settings
	OverrideRetentionDays *int             `json:"override_retention_days"`
	OverrideAction        *RetentionAction `json:"override_action"`
	OverrideReason        string           `json:"override_reason"`

	// Assignment metadata
	AssignedByID uint `json:"assigned_by_id" gorm:"not null"`
	AssignedBy   User `json:"assigned_by" gorm:"foreignKey:AssignedByID"`
	Notes        string `json:"notes" gorm:"type:text"`
}

// RetentionExecutionLog represents execution history of retention policies
type RetentionExecutionLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	PolicyID uint            `json:"policy_id" gorm:"not null"`
	Policy   RetentionPolicy `json:"policy" gorm:"foreignKey:PolicyID"`

	// Execution details
	ExecutionDate     time.Time `json:"execution_date"`
	ExecutionType     string    `json:"execution_type"`                                // "scheduled", "manual", "triggered"
	Action            RetentionAction `json:"action"`
	Status            string    `json:"status"`                                        // "success", "failed", "partial"
	
	// Results
	DocumentsProcessed int    `json:"documents_processed"`
	DocumentsArchived  int    `json:"documents_archived"`
	DocumentsDeleted   int    `json:"documents_deleted"`
	DocumentsSkipped   int    `json:"documents_skipped"`
	ErrorCount         int    `json:"error_count"`
	
	// Execution context
	ExecutedByID *uint  `json:"executed_by_id"`
	ExecutedBy   *User  `json:"executed_by,omitempty" gorm:"foreignKey:ExecutedByID"`
	TriggerEvent string `json:"trigger_event"`
	
	// Detailed results
	ProcessedDocuments string `json:"processed_documents" gorm:"type:text"`           // JSON array of document IDs
	ErrorDetails       string `json:"error_details" gorm:"type:text"`                // JSON error details
	ExecutionSummary   string `json:"execution_summary" gorm:"type:text"`            // Human readable summary
	
	// Timing
	StartTime    time.Time  `json:"start_time"`
	EndTime      *time.Time `json:"end_time"`
	DurationMs   int        `json:"duration_ms"`
}

// DocumentRetentionStatus represents the retention status of a specific document
type DocumentRetentionStatus struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`
	PolicyID   uint     `json:"policy_id" gorm:"not null"`
	Policy     RetentionPolicy `json:"policy" gorm:"foreignKey:PolicyID"`

	// Retention timeline
	RetentionStartDate time.Time  `json:"retention_start_date"`
	RetentionEndDate   time.Time  `json:"retention_end_date"`
	ActionDueDate      time.Time  `json:"action_due_date"`
	LastNotifiedAt     *time.Time `json:"last_notified_at"`
	
	// Current status
	Status              string          `json:"status"`                                 // "active", "expired", "on_hold", "archived", "deleted"
	Action              RetentionAction `json:"action"`
	IsLegalHold         bool            `json:"is_legal_hold" gorm:"default:false"`
	LegalHoldReason     string          `json:"legal_hold_reason"`
	
	// Execution tracking
	ActionExecutedAt    *time.Time `json:"action_executed_at"`
	ActionExecutedByID  *uint      `json:"action_executed_by_id"`
	ActionExecutedBy    *User      `json:"action_executed_by,omitempty" gorm:"foreignKey:ActionExecutedByID"`
	
	// Archive information
	ArchiveLocation     string `json:"archive_location"`
	ArchiveChecksum     string `json:"archive_checksum"`
	ArchiveSize         int64  `json:"archive_size"`
	
	// Audit trail
	StatusHistory       string `json:"status_history" gorm:"type:text"`               // JSON history of status changes
	Notes               string `json:"notes" gorm:"type:text"`
}

// RetentionSchedule represents scheduled retention policy executions
type RetentionSchedule struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	PolicyID uint            `json:"policy_id" gorm:"not null"`
	Policy   RetentionPolicy `json:"policy" gorm:"foreignKey:PolicyID"`

	// Schedule configuration
	ScheduleType    string    `json:"schedule_type"`                                  // "daily", "weekly", "monthly", "yearly", "cron"
	CronExpression  string    `json:"cron_expression"`                               // Cron expression for custom schedules
	NextRunAt       time.Time `json:"next_run_at"`
	LastRunAt       *time.Time `json:"last_run_at"`
	
	// Schedule status
	IsActive        bool   `json:"is_active" gorm:"default:true"`
	RunCount        int    `json:"run_count" gorm:"default:0"`
	FailureCount    int    `json:"failure_count" gorm:"default:0"`
	LastRunStatus   string `json:"last_run_status"`                                  // "success", "failed", "skipped"
	
	// Configuration
	TimeZone        string `json:"time_zone" gorm:"default:'UTC'"`
	MaxRetries      int    `json:"max_retries" gorm:"default:3"`
	RetryInterval   int    `json:"retry_interval" gorm:"default:300"`                // Seconds between retries
	
	// Notifications
	NotifyOnSuccess bool   `json:"notify_on_success" gorm:"default:false"`
	NotifyOnFailure bool   `json:"notify_on_failure" gorm:"default:true"`
	NotificationEmails string `json:"notification_emails" gorm:"type:text"`         // JSON array
}

// TableName returns the table name for RetentionPolicy model
func (RetentionPolicy) TableName() string {
	return "retention_policies"
}

// TableName returns the table name for RetentionPolicyAssignment model
func (RetentionPolicyAssignment) TableName() string {
	return "retention_policy_assignments"
}

// TableName returns the table name for RetentionExecutionLog model
func (RetentionExecutionLog) TableName() string {
	return "retention_execution_logs"
}

// TableName returns the table name for DocumentRetentionStatus model
func (DocumentRetentionStatus) TableName() string {
	return "document_retention_status"
}

// TableName returns the table name for RetentionSchedule model
func (RetentionSchedule) TableName() string {
	return "retention_schedules"
}
