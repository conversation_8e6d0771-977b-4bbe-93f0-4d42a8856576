'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../../services/enterpriseApi';
import { CostCenter, Employee } from '../../../../../types/enterprise';

const EditCostCenterPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const costCenterId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [formData, setFormData] = useState<Partial<CostCenter>>({
    cost_center_code: '',
    cost_center_name: '',
    description: '',
    department: '',
    location: '',
    manager_id: undefined,
    parent_cost_center_id: undefined,
    budget_amount: 0,
    actual_amount: 0,
    currency_code: 'USD',
    is_active: true,
    cost_allocation_method: 'direct',
    responsibility_center_type: 'cost',
    gl_account_range_start: '',
    gl_account_range_end: '',
    metadata: ''
  });

  useEffect(() => {
    if (costCenterId) {
      fetchCostCenter();
      fetchEmployees();
    }
  }, [costCenterId]);

  const fetchCostCenter = async () => {
    try {
      setFetchLoading(true);
      const response = await financialApi.getCostCenter(costCenterId);
      setFormData(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch cost center');
    } finally {
      setFetchLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      // Note: This would need to be implemented in the HR API
      // const response = await hrApi.getEmployees();
      // setEmployees(response.data);
    } catch (err: any) {
      console.log('Failed to fetch employees:', err.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await financialApi.updateCostCenter(costCenterId, formData);
      router.push(`/enterprise/financial/cost-centers/${costCenterId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update cost center');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value === '' ? undefined : value
    }));
  };

  if (fetchLoading) return <div className="p-6">Loading cost center...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Cost Center</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/financial/cost-centers/${costCenterId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Cost Center Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost Center Code *
            </label>
            <input
              type="text"
              name="cost_center_code"
              value={formData.cost_center_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., CC-001"
            />
          </div>

          {/* Cost Center Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost Center Name *
            </label>
            <input
              type="text"
              name="cost_center_name"
              value={formData.cost_center_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Finance Department"
            />
          </div>

          {/* Department */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Department
            </label>
            <input
              type="text"
              name="department"
              value={formData.department}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Finance, HR, IT"
            />
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Location
            </label>
            <input
              type="text"
              name="location"
              value={formData.location}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Building A, Floor 2"
            />
          </div>

          {/* Manager */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Manager
            </label>
            <select
              name="manager_id"
              value={formData.manager_id || ''}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Manager</option>
              {employees.map((employee) => (
                <option key={employee.id} value={employee.id}>
                  {employee.first_name} {employee.last_name}
                </option>
              ))}
            </select>
          </div>

          {/* Budget Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Budget Amount
            </label>
            <input
              type="number"
              name="budget_amount"
              value={formData.budget_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Actual Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Actual Amount
            </label>
            <input
              type="number"
              name="actual_amount"
              value={formData.actual_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency Code
            </label>
            <input
              type="text"
              name="currency_code"
              value={formData.currency_code}
              onChange={handleChange}
              maxLength={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="USD"
            />
          </div>

          {/* Cost Allocation Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost Allocation Method
            </label>
            <select
              name="cost_allocation_method"
              value={formData.cost_allocation_method}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="direct">Direct</option>
              <option value="indirect">Indirect</option>
              <option value="activity_based">Activity Based</option>
              <option value="proportional">Proportional</option>
            </select>
          </div>

          {/* Responsibility Center Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Responsibility Center Type
            </label>
            <select
              name="responsibility_center_type"
              value={formData.responsibility_center_type}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="cost">Cost Center</option>
              <option value="profit">Profit Center</option>
              <option value="investment">Investment Center</option>
              <option value="revenue">Revenue Center</option>
            </select>
          </div>

          {/* GL Account Range Start */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              GL Account Range Start
            </label>
            <input
              type="text"
              name="gl_account_range_start"
              value={formData.gl_account_range_start}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., 5000"
            />
          </div>

          {/* GL Account Range End */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              GL Account Range End
            </label>
            <input
              type="text"
              name="gl_account_range_end"
              value={formData.gl_account_range_end}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., 5999"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Cost center description..."
          />
        </div>

        {/* Active Checkbox */}
        <div className="mt-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleChange}
              className="mr-2"
            />
            Active
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/financial/cost-centers/${costCenterId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Cost Center'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditCostCenterPage;
