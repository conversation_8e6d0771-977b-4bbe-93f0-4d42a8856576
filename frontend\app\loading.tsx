'use client'

import React from 'react';
import {
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  BookOpenIcon
} from '@heroicons/react/24/outline';

const LoadingPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full space-y-8 text-center">
        {/* Main Loading Spinner */}
        <div className="relative">
          <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-primary-100">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
          
          {/* Floating Icons Animation */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="relative w-32 h-32">
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 animate-bounce">
                <DocumentTextIcon className="h-6 w-6 text-primary-400" style={{ animationDelay: '0s' }} />
              </div>
              <div className="absolute top-1/2 right-0 transform -translate-y-1/2 animate-bounce">
                <BuildingOfficeIcon className="h-6 w-6 text-primary-400" style={{ animationDelay: '0.2s' }} />
              </div>
              <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 animate-bounce">
                <BookOpenIcon className="h-6 w-6 text-primary-400" style={{ animationDelay: '0.4s' }} />
              </div>
              <div className="absolute top-1/2 left-0 transform -translate-y-1/2 animate-bounce">
                <TagIcon className="h-6 w-6 text-primary-400" style={{ animationDelay: '0.6s' }} />
              </div>
            </div>
          </div>
        </div>

        {/* Loading Text */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Loading...</h2>
          <p className="text-gray-600">
            Please wait while we prepare your document management system.
          </p>
        </div>

        {/* Progress Indicators */}
        <div className="space-y-4">
          {/* Animated Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-primary-600 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
          </div>

          {/* Loading Steps */}
          <div className="space-y-2 text-sm text-gray-500">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-primary-600 rounded-full animate-pulse"></div>
              <span>Initializing application...</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-primary-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
              <span>Loading user preferences...</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-primary-300 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
              <span>Preparing document library...</span>
            </div>
          </div>
        </div>

        {/* Skeleton Content Preview */}
        <div className="mt-12 space-y-4">
          <div className="text-left">
            <div className="h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4 mb-2"></div>
            <div className="h-4 bg-gray-200 rounded animate-pulse w-1/2"></div>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <div className="h-16 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-16 bg-gray-200 rounded animate-pulse"></div>
            <div className="h-16 bg-gray-200 rounded animate-pulse"></div>
          </div>
        </div>

        {/* Loading Tips */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Did you know?</h4>
          <p className="text-sm text-blue-700">
            You can use keyboard shortcuts to navigate quickly through documents. 
            Press '?' to view all available shortcuts once the page loads.
          </p>
        </div>

        {/* Timeout Warning (appears after delay) */}
        <div className="mt-4 opacity-0 animate-fade-in" style={{ animationDelay: '10s', animationFillMode: 'forwards' }}>
          <p className="text-xs text-gray-500">
            Taking longer than expected? 
            <button 
              onClick={() => window.location.reload()} 
              className="text-primary-600 hover:text-primary-700 underline ml-1"
            >
              Try refreshing the page
            </button>
          </p>
        </div>
      </div>

      <style jsx>{`
        @keyframes fade-in {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s ease-in-out;
        }
      `}</style>
    </div>
  );
};

export default LoadingPage;
