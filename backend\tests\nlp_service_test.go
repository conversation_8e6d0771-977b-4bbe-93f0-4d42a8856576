package tests

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

type NLPServiceTestSuite struct {
	suite.Suite
	db         *gorm.DB
	nlpService *services.NLPService
}

func TestNLPServiceSuite(t *testing.T) {
	suite.Run(t, new(NLPServiceTestSuite))
}

func (suite *NLPServiceTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Set global database
	database.DB = db
	suite.db = db

	// Run migrations
	err = db.AutoMigrate(&models.SystemEvent{})
	suite.Require().NoError(err)

	// Initialize service
	suite.nlpService = services.NewNLPService()
}

func (suite *NLPServiceTestSuite) TearDownSuite() {
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *NLPServiceTestSuite) TestExtractKeywordsAdvanced() {
	testCases := []struct {
		name     string
		content  string
		expected []string
	}{
		{
			name:     "Legal document",
			content:  "This regulation establishes compliance requirements for federal agencies regarding data protection and privacy standards.",
			expected: []string{"regulation", "compliance", "requirements", "federal", "agencies", "data", "protection", "privacy", "standards"},
		},
		{
			name:     "Technical document",
			content:  "The system architecture implements advanced algorithms for document processing and classification using machine learning techniques.",
			expected: []string{"system", "architecture", "implements", "advanced", "algorithms", "document", "processing", "classification", "machine", "learning", "techniques"},
		},
		{
			name:     "Empty content",
			content:  "",
			expected: []string{},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			keywords := suite.nlpService.ExtractKeywordsAdvanced(tc.content)
			
			// Check that we get some keywords for non-empty content
			if tc.content != "" {
				assert.NotEmpty(suite.T(), keywords, "Should extract keywords from non-empty content")
				
				// Check that keywords are relevant (at least some should match expected)
				foundRelevant := false
				for _, keyword := range keywords {
					for _, expected := range tc.expected {
						if keyword == expected {
							foundRelevant = true
							break
						}
					}
					if foundRelevant {
						break
					}
				}
				assert.True(suite.T(), foundRelevant, "Should extract at least one relevant keyword")
			} else {
				assert.Empty(suite.T(), keywords, "Should return empty keywords for empty content")
			}
		})
	}
}

func (suite *NLPServiceTestSuite) TestAnalyzeSentiment() {
	testCases := []struct {
		name              string
		content           string
		expectedSentiment string
		minConfidence     float64
	}{
		{
			name:              "Positive content",
			content:           "This is an excellent regulation that will greatly improve compliance and benefit all stakeholders.",
			expectedSentiment: "positive",
			minConfidence:     0.1,
		},
		{
			name:              "Negative content",
			content:           "This terrible policy will cause significant problems and harm the industry with its awful requirements.",
			expectedSentiment: "negative",
			minConfidence:     0.1,
		},
		{
			name:              "Neutral content",
			content:           "The regulation establishes requirements for data processing in accordance with federal standards.",
			expectedSentiment: "neutral",
			minConfidence:     0.0,
		},
		{
			name:              "Empty content",
			content:           "",
			expectedSentiment: "neutral",
			minConfidence:     0.0,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			result := suite.nlpService.AnalyzeSentiment(tc.content)
			
			// Check that result has required fields
			assert.Contains(suite.T(), result, "sentiment")
			assert.Contains(suite.T(), result, "confidence")
			assert.Contains(suite.T(), result, "scores")
			
			sentiment := result["sentiment"].(string)
			confidence := result["confidence"].(float64)
			
			assert.Equal(suite.T(), tc.expectedSentiment, sentiment)
			assert.GreaterOrEqual(suite.T(), confidence, tc.minConfidence)
			
			// Check scores structure
			scores := result["scores"].(map[string]float64)
			assert.Contains(suite.T(), scores, "positive")
			assert.Contains(suite.T(), scores, "negative")
			assert.Contains(suite.T(), scores, "neutral")
		})
	}
}

func (suite *NLPServiceTestSuite) TestAnalyzeComplexity() {
	testCases := []struct {
		name    string
		content string
	}{
		{
			name:    "Simple content",
			content: "This is a simple sentence. It has basic words.",
		},
		{
			name:    "Complex content",
			content: "The comprehensive regulatory framework establishes sophisticated methodologies for implementing advanced compliance mechanisms through systematic organizational restructuring and technological integration processes.",
		},
		{
			name:    "Empty content",
			content: "",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			result := suite.nlpService.AnalyzeComplexity(tc.content)
			
			// Check that result has required fields
			assert.Contains(suite.T(), result, "word_count")
			assert.Contains(suite.T(), result, "sentence_count")
			assert.Contains(suite.T(), result, "lexical_diversity")
			assert.Contains(suite.T(), result, "readability_score")
			
			wordCount := result["word_count"].(int)
			sentenceCount := result["sentence_count"].(int)
			
			if tc.content == "" {
				assert.Equal(suite.T(), 0, wordCount)
			} else {
				assert.Greater(suite.T(), wordCount, 0)
				assert.Greater(suite.T(), sentenceCount, 0)
			}
		})
	}
}

func (suite *NLPServiceTestSuite) TestExtractEntities() {
	testCases := []struct {
		name     string
		content  string
		expected map[string][]string
	}{
		{
			name:    "Content with entities",
			content: "The Environmental Protection Agency (EPA) issued new regulations on January 15, 2024, affecting companies in California and New York.",
			expected: map[string][]string{
				"organizations": {"Environmental Protection Agency", "EPA"},
				"dates":         {"January 15, 2024"},
				"locations":     {"California", "New York"},
			},
		},
		{
			name:     "Empty content",
			content:  "",
			expected: map[string][]string{},
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			entities := suite.nlpService.ExtractEntities(tc.content)
			
			if tc.content == "" {
				// For empty content, should return empty or minimal entities
				assert.True(suite.T(), len(entities) == 0 || 
					(len(entities) > 0 && len(entities["organizations"]) == 0))
			} else {
				// Should extract some entities for non-empty content
				assert.NotEmpty(suite.T(), entities, "Should extract entities from non-empty content")
				
				// Check for expected entity types
				for entityType := range tc.expected {
					if entities[entityType] != nil {
						assert.NotEmpty(suite.T(), entities[entityType], 
							"Should find entities of type %s", entityType)
					}
				}
			}
		})
	}
}

func (suite *NLPServiceTestSuite) TestClassifyContent() {
	testCases := []struct {
		name     string
		content  string
		expected string
	}{
		{
			name:     "Legal content",
			content:  "This statute establishes legal requirements for contract enforcement and litigation procedures in federal courts.",
			expected: "legal",
		},
		{
			name:     "Financial content",
			content:  "The budget allocation includes revenue projections, expense forecasting, and investment portfolio management strategies.",
			expected: "financial",
		},
		{
			name:     "Technical content",
			content:  "The software architecture implements microservices using containerization and orchestration technologies.",
			expected: "technical",
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			classification := suite.nlpService.ClassifyContent(tc.content)
			
			// Should return a valid classification
			assert.NotEmpty(suite.T(), classification, "Should return a classification")
			
			// For specific test cases, check if we get expected classification
			// Note: This is a basic check since classification can be subjective
			validClassifications := []string{"legal", "financial", "technical", "regulatory", "policy", "general"}
			assert.Contains(suite.T(), validClassifications, classification, 
				"Should return a valid classification type")
		})
	}
}

func (suite *NLPServiceTestSuite) TestGenerateNLPSummary() {
	testCases := []struct {
		name    string
		content string
		maxLen  int
	}{
		{
			name:    "Long content",
			content: "This is a comprehensive document about federal regulations. It covers multiple aspects of compliance requirements, implementation procedures, and enforcement mechanisms. The document provides detailed guidance for organizations to ensure proper adherence to regulatory standards and best practices in their operations.",
			maxLen:  100,
		},
		{
			name:    "Short content",
			content: "Brief regulation summary.",
			maxLen:  50,
		},
	}

	for _, tc := range testCases {
		suite.Run(tc.name, func() {
			summary := suite.nlpService.GenerateNLPSummary(tc.content, tc.maxLen)
			
			// Should return a summary
			assert.NotEmpty(suite.T(), summary, "Should generate a summary")
			
			// Summary should be shorter than or equal to max length
			assert.LessOrEqual(suite.T(), len(summary), tc.maxLen, 
				"Summary should not exceed max length")
			
			// For short content, summary might be the same as original
			if len(tc.content) <= tc.maxLen {
				assert.Equal(suite.T(), tc.content, summary, 
					"Short content should remain unchanged")
			}
		})
	}
}
