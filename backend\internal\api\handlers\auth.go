﻿package handlers

import (
	"fmt"
	"net/http"

	"federal-register-clone/internal/api/middleware"
	"federal-register-clone/internal/auth"
	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"

	"github.com/gin-gonic/gin"
)

var emailService *services.EmailService

// InitializeEmailService initializes the email service
func InitializeEmailService(cfg *config.Config) {
	emailService = services.NewEmailService(cfg)
}

// LoginRequest represents a login request
type LoginRequest struct {
	Identifier string `json:"identifier" binding:"required"` // username or email
	Password   string `json:"password" binding:"required"`
}

// RegisterRequest represents a registration request
type RegisterRequest struct {
	Username     string `json:"username" binding:"required"`
	Email        string `json:"email" binding:"required,email"`
	Password     string `json:"password" binding:"required,min=8"`
	FirstName    string `json:"first_name"`
	LastName     string `json:"last_name"`
	Title        string `json:"title"`
	Organization string `json:"organization"`
}

// LoginResponse represents a login response
type LoginResponse struct {
	Token        string       `json:"token"`
	RefreshToken string       `json:"refresh_token"`
	User         *models.User `json:"user"`
	ExpiresIn    int          `json:"expires_in"`
}

// RefreshTokenRequest represents a refresh token request
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// Login authenticates a user and returns JWT tokens
// @Summary User login
// @Description Authenticate user with username/email and password
// @Tags auth
// @Accept json
// @Produce json
// @Param request body LoginRequest true "Login credentials"
// @Success 200 {object} LoginResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/login [post]
func Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	// Authenticate user
	user, err := auth.AuthenticateUser(req.Identifier, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Authentication failed",
			Message: err.Error(),
		})
		return
	}

	// Generate access token (24 hours)
	accessToken, err := auth.GenerateToken(user, 24)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Token generation failed",
			Message: err.Error(),
		})
		return
	}

	// Generate refresh token (7 days)
	refreshToken, err := auth.GenerateToken(user, 168)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Refresh token generation failed",
			Message: err.Error(),
		})
		return
	}

	// Create session
	// Note: In a production environment, you might want to store refresh tokens separately

	// Remove sensitive information
	user.PasswordHash = ""

	c.JSON(http.StatusOK, LoginResponse{
		Token:        accessToken,
		RefreshToken: refreshToken,
		User:         user,
		ExpiresIn:    24 * 3600, // 24 hours in seconds
	})
}

// Register creates a new user account
// @Summary User registration
// @Description Create a new user account
// @Tags auth
// @Accept json
// @Produce json
// @Param request body RegisterRequest true "Registration details"
// @Success 201 {object} LoginResponse
// @Failure 400 {object} ErrorResponse
// @Failure 409 {object} ErrorResponse
// @Router /auth/register [post]
func Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	// Create user model
	user := &models.User{
		Username:     req.Username,
		Email:        req.Email,
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		Title:        req.Title,
		Organization: req.Organization,
		Role:         models.RoleViewer, // Default role
		IsActive:     true,
		IsVerified:   false, // Email verification required
	}

	// Create user
	err := auth.CreateUser(user, req.Password)
	if err != nil {
		// Check for duplicate username/email
		if err.Error() == "UNIQUE constraint failed" {
			c.JSON(http.StatusConflict, ErrorResponse{
				Error:   "User already exists",
				Message: "Username or email already taken",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "User creation failed",
			Message: err.Error(),
		})
		return
	}

	// Generate tokens
	accessToken, err := auth.GenerateToken(user, 24)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Token generation failed",
			Message: err.Error(),
		})
		return
	}

	refreshToken, err := auth.GenerateToken(user, 168)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Refresh token generation failed",
			Message: err.Error(),
		})
		return
	}

	// Remove sensitive information
	user.PasswordHash = ""

	c.JSON(http.StatusCreated, LoginResponse{
		Token:        accessToken,
		RefreshToken: refreshToken,
		User:         user,
		ExpiresIn:    24 * 3600,
	})
}

// RefreshToken refreshes an access token using a refresh token
// @Summary Refresh access token
// @Description Get a new access token using a refresh token
// @Tags auth
// @Accept json
// @Produce json
// @Param request body RefreshTokenRequest true "Refresh token"
// @Success 200 {object} LoginResponse
// @Failure 400 {object} ErrorResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/refresh [post]
func RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request",
			Message: err.Error(),
		})
		return
	}

	// Validate refresh token
	claims, err := auth.ValidateToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Invalid refresh token",
			Message: err.Error(),
		})
		return
	}

	// Get user
	user, err := auth.GetUserByID(claims.UserID)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User not found",
			Message: err.Error(),
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User account is inactive",
			Message: "Account has been deactivated",
		})
		return
	}

	// Generate new access token
	accessToken, err := auth.GenerateToken(user, 24)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Token generation failed",
			Message: err.Error(),
		})
		return
	}

	// Remove sensitive information
	user.PasswordHash = ""

	c.JSON(http.StatusOK, LoginResponse{
		Token:        accessToken,
		RefreshToken: req.RefreshToken, // Return the same refresh token
		User:         user,
		ExpiresIn:    24 * 3600,
	})
}

// UpdateProfileRequest represents a profile update request
type UpdateProfileRequest struct {
	FirstName    string `json:"first_name"`
	LastName     string `json:"last_name"`
	Email        string `json:"email" binding:"email"`
	Phone        string `json:"phone"`
	Title        string `json:"title"`
	Department   string `json:"department"`
	Organization string `json:"organization"`
	Bio          string `json:"bio"`
	AgencyID     *uint  `json:"agency_id"`
}

// UpdateProfile updates the current user's profile
func UpdateProfile(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User not authenticated",
			Message: "No user found in context",
		})
		return
	}

	userModel, ok := user.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid user data",
			Message: "User data type assertion failed",
		})
		return
	}

	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request data",
			Message: err.Error(),
		})
		return
	}

	// Update user fields
	userModel.FirstName = req.FirstName
	userModel.LastName = req.LastName
	userModel.Email = req.Email
	userModel.Phone = req.Phone
	userModel.Title = req.Title
	userModel.Department = req.Department
	userModel.Organization = req.Organization
	userModel.Bio = req.Bio

	// Only admin users can update their agency_id
	if req.AgencyID != nil && userModel.Role == models.RoleAdmin {
		// Validate agency if provided
		if *req.AgencyID != 0 {
			db := database.GetDB()
			if db != nil {
				var agency models.Agency
				if err := db.First(&agency, *req.AgencyID).Error; err != nil {
					c.JSON(http.StatusBadRequest, ErrorResponse{
						Error:   "Invalid agency ID",
						Message: "The specified agency does not exist",
					})
					return
				}
			}
		}
		userModel.AgencyID = req.AgencyID
	}

	// Save to database
	err := auth.UpdateUser(userModel)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Profile update failed",
			Message: err.Error(),
		})
		return
	}

	// Remove sensitive information
	userModel.PasswordHash = ""

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Profile updated successfully",
		Data:    userModel,
	})
}

// GetUserPermissions returns the current user's permissions
func GetUserPermissions(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User not authenticated",
			Message: "No user found in context",
		})
		return
	}

	userModel, ok := user.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid user data",
			Message: "User data type assertion failed",
		})
		return
	}

	// Add debug logging
	fmt.Printf("DEBUG: User ID: %d, Role: %s\n", userModel.ID, userModel.Role)

	// Define permissions based on user role
	permissions := map[string]bool{
		"canCreate":  false,
		"canEdit":    false,
		"canDelete":  false,
		"canView":    true, // All authenticated users can view
		"canPublish": false,
		"canReview":  false,
	}

	switch userModel.Role {
	case models.RoleAdmin:
		fmt.Printf("DEBUG: Setting admin permissions\n")
		permissions["canCreate"] = true
		permissions["canEdit"] = true
		permissions["canDelete"] = true
		permissions["canPublish"] = true
		permissions["canReview"] = true
	case models.RoleEditor:
		fmt.Printf("DEBUG: Setting editor permissions\n")
		permissions["canCreate"] = true
		permissions["canEdit"] = true
	case models.RoleReviewer:
		fmt.Printf("DEBUG: Setting reviewer permissions\n")
		permissions["canReview"] = true
	case models.RolePublisher:
		fmt.Printf("DEBUG: Setting publisher permissions\n")
		permissions["canPublish"] = true
	case models.RoleViewer, models.RoleViewer1, models.RoleViewer2, models.RoleViewer3:
		fmt.Printf("DEBUG: Setting viewer permissions\n")
		// Viewers only have view permission (already set to true above)
		// All other permissions remain false
	default:
		fmt.Printf("DEBUG: Unknown role, using default permissions\n")
	}

	fmt.Printf("DEBUG: Final permissions: %+v\n", permissions)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Permissions retrieved successfully",
		Data:    permissions,
	})
}

// GetUserStats returns the current user's activity statistics
func GetUserStats(c *gin.Context) {
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User not authenticated",
			Message: "No user found in context",
		})
		return
	}

	userModel, ok := user.(*models.User)
	if !ok {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid user data",
			Message: "User data type assertion failed",
		})
		return
	}

	// Calculate actual statistics from database
	stats := calculateUserStats(userModel.ID)
	stats["lastLoginAt"] = userModel.LastLoginAt
	stats["accountCreatedAt"] = userModel.CreatedAt

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User statistics retrieved successfully",
		Data:    stats,
	})
}

// ChangePasswordRequest represents a password change request
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" binding:"required"`
}

// ChangePassword changes the current user's password
func ChangePassword(c *gin.Context) {
	_, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User not authenticated",
			Message: "No user found in context",
		})
		return
	}

	// userModel, ok := user.(*models.User)
	// if !ok {
	//	c.JSON(http.StatusInternalServerError, ErrorResponse{
	//		Error:   "Invalid user data",
	//		Message: "User data type assertion failed",
	//	})
	//	return
	// }

	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request data",
			Message: err.Error(),
		})
		return
	}

	// Validate that new passwords match
	if req.NewPassword != req.ConfirmPassword {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Password confirmation failed",
			Message: "New passwords do not match",
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User not authenticated",
			Message: "Unable to determine user identity",
		})
		return
	}

	// Get current user from database to verify current password
	var currentUser models.User
	if err := db.First(&currentUser, userID).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "User not found",
			Message: "Unable to find user account",
		})
		return
	}

	// Verify current password
	if !auth.CheckPassword(req.CurrentPassword, currentUser.PasswordHash) {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid current password",
			Message: "The current password you entered is incorrect",
		})
		return
	}

	// Hash new password
	hashedPassword, err := auth.HashPassword(req.NewPassword)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Password hashing failed",
			Message: err.Error(),
		})
		return
	}

	// Update password in database
	if err := db.Model(&currentUser).Update("password_hash", hashedPassword).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Password update failed",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Password changed successfully",
		Data:    nil,
	})
}

// ForgotPasswordRequest represents a forgot password request
type ForgotPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

// ForgotPassword initiates the password reset process
func ForgotPassword(c *gin.Context) {
	var req ForgotPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request data",
			Message: err.Error(),
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find user by email
	var user models.User
	err := db.Where("email = ?", req.Email).First(&user).Error
	if err != nil {
		// Don't reveal if email exists or not for security
		c.JSON(http.StatusOK, SuccessResponse{
			Message: "If an account with that email exists, a password reset link has been sent",
			Data:    nil,
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusOK, SuccessResponse{
			Message: "If an account with that email exists, a password reset link has been sent",
			Data:    nil,
		})
		return
	}

	// Generate reset token (using JWT with short expiry)
	resetToken, err := auth.GenerateToken(&user, 1) // 1 hour expiry
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Reset token generation failed",
			Message: err.Error(),
		})
		return
	}

	// Send password reset email
	if emailService != nil {
		err = emailService.SendPasswordResetEmail(user.Email, user.Username, resetToken)
		if err != nil {
			// Log error but don't reveal to user for security
			fmt.Printf("Failed to send password reset email to %s: %v\n", user.Email, err)
		}
	} else {
		// Fallback: log the reset token for development
		fmt.Printf("Password reset token for %s: %s\n", user.Email, resetToken)
		fmt.Printf("Reset URL: http://localhost:3000/reset-password?token=%s\n", resetToken)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "If an account with that email exists, a password reset link has been sent",
		Data:    nil, // Don't expose reset token in production
	})
}

// ResetPasswordRequest represents a password reset request
type ResetPasswordRequest struct {
	Token           string `json:"token" binding:"required"`
	Password        string `json:"password" binding:"required,min=8"`
	ConfirmPassword string `json:"confirm_password" binding:"required"`
}

// ResetPassword resets a user's password using a reset token
func ResetPassword(c *gin.Context) {
	var req ResetPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request data",
			Message: err.Error(),
		})
		return
	}

	// Validate that passwords match
	if req.Password != req.ConfirmPassword {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Password confirmation failed",
			Message: "Passwords do not match",
		})
		return
	}

	// Validate reset token
	claims, err := auth.ValidateToken(req.Token)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid or expired reset token",
			Message: err.Error(),
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get user from token claims
	var user models.User
	if err := db.First(&user, claims.UserID).Error; err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid reset token",
			Message: "User not found",
		})
		return
	}

	// Check if user is active
	if !user.IsActive {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Account inactive",
			Message: "User account is not active",
		})
		return
	}

	// Hash new password
	hashedPassword, err := auth.HashPassword(req.Password)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Password hashing failed",
			Message: err.Error(),
		})
		return
	}

	// Update password in database
	if err := db.Model(&user).Update("password_hash", hashedPassword).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Password reset failed",
			Message: err.Error(),
		})
		return
	}

	// Invalidate all user sessions to force re-login
	auth.InvalidateAllUserSessions(user.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Password reset successfully",
		Data:    nil,
	})
}

// Logout invalidates the current session
// @Summary User logout
// @Description Invalidate the current user session
// @Tags auth
// @Security BearerAuth
// @Success 200 {object} SuccessResponse
// @Failure 401 {object} ErrorResponse
// @Router /auth/logout [post]
func Logout(c *gin.Context) {
	// Get token from header
	token := c.GetHeader("Authorization")
	if token != "" && len(token) > 7 {
		token = token[7:] // Remove "Bearer " prefix
		auth.InvalidateUserSession(token)
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Logged out successfully",
	})
}

// GetCurrentUser returns the current authenticated user
// @Summary Get current user
// @Description Get the current authenticated user's information
// @Tags auth
// @Security BearerAuth
// @Produce json
// @Success 200 {object} models.User
// @Failure 401 {object} ErrorResponse
// @Router /auth/me [get]
func GetCurrentUser(c *gin.Context) {
	user, exists := middleware.GetCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "User not authenticated",
			Message: "No valid authentication found",
		})
		return
	}

	// Remove sensitive information
	user.PasswordHash = ""

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "User retrieved successfully",
		Data:    user,
	})
}

// calculateUserStats calculates user activity statistics
func calculateUserStats(userID uint) map[string]interface{} {
	db := database.GetDB()
	if db == nil {
		return map[string]interface{}{
			"documentsCreated":   0,
			"documentsEdited":    0,
			"documentsPublished": 0,
			"documentsReviewed":  0,
		}
	}

	stats := make(map[string]interface{})

	// Count documents created by user
	var documentsCreated int64
	db.Model(&models.Document{}).Where("created_by_id = ?", userID).Count(&documentsCreated)
	stats["documentsCreated"] = documentsCreated

	// Count documents edited by user (based on content versions)
	var documentsEdited int64
	db.Table("content_versions").
		Select("COUNT(DISTINCT document_id)").
		Where("author_id = ?", userID).
		Scan(&documentsEdited)
	stats["documentsEdited"] = documentsEdited

	// Count published documents by user
	var documentsPublished int64
	db.Model(&models.Document{}).Where("created_by_id = ? AND status = ?", userID, "published").Count(&documentsPublished)
	stats["documentsPublished"] = documentsPublished

	// Count documents reviewed by user (based on document reviews)
	var documentsReviewed int64
	db.Model(&models.DocumentReview{}).
		Where("reviewer_id = ? AND reviewed_at IS NOT NULL", userID).
		Count(&documentsReviewed)
	stats["documentsReviewed"] = documentsReviewed

	// Additional statistics for better user insights

	// Count documents updated by user (different from created)
	var documentsUpdated int64
	db.Model(&models.Document{}).Where("updated_by_id = ?", userID).Count(&documentsUpdated)
	stats["documentsUpdated"] = documentsUpdated

	// Count pending reviews assigned to user
	var pendingReviews int64
	db.Model(&models.DocumentReview{}).
		Where("reviewer_id = ? AND status = ? AND reviewed_at IS NULL", userID, "pending").
		Count(&pendingReviews)
	stats["pendingReviews"] = pendingReviews

	// Count total content versions created by user
	var contentVersionsCreated int64
	db.Table("content_versions").Where("author_id = ?", userID).Count(&contentVersionsCreated)
	stats["contentVersionsCreated"] = contentVersionsCreated

	// Calculate average review rating given by user
	var avgReviewRating float64
	db.Model(&models.DocumentReview{}).
		Select("COALESCE(AVG(rating), 0)").
		Where("reviewer_id = ? AND rating > 0", userID).
		Scan(&avgReviewRating)
	stats["avgReviewRating"] = avgReviewRating

	return stats
}
