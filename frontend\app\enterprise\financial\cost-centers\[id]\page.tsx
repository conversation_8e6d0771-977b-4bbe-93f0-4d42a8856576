'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../services/enterpriseApi';
import { CostCenter } from '../../../../types/enterprise';

const CostCenterViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const costCenterId = parseInt(params.id as string);
  
  const [costCenter, setCostCenter] = useState<CostCenter | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (costCenterId) {
      fetchCostCenter();
    }
  }, [costCenterId]);

  const fetchCostCenter = async () => {
    try {
      setLoading(true);
      const response = await financialApi.getCostCenter(costCenterId);
      setCostCenter(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch cost center');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this cost center?')) return;
    
    try {
      await financialApi.deleteCostCenter(costCenterId);
      router.push('/enterprise/financial/cost-centers');
    } catch (err: any) {
      setError(err.message || 'Failed to delete cost center');
    }
  };

  if (loading) return <div className="p-6">Loading cost center...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!costCenter) return <div className="p-6">Cost center not found</div>;

  const utilizationPercentage = costCenter.budget_amount > 0 ? (costCenter.actual_amount / costCenter.budget_amount) * 100 : 0;
  const variance = costCenter.budget_amount - costCenter.actual_amount;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Cost Center Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/financial/cost-centers')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Cost Centers
          </button>
          <button
            onClick={() => router.push(`/enterprise/financial/cost-centers/${costCenterId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Cost Center
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Cost Center
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{costCenter.cost_center_name}</h2>
              <p className="text-sm text-gray-600">Code: {costCenter.cost_center_code}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                costCenter.is_active 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {costCenter.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>
        </div>

        {/* Budget Summary */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Budget Summary</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {costCenter.currency_code} {costCenter.budget_amount.toLocaleString()}
              </div>
              <div className="text-sm text-blue-600">Budget Amount</div>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {costCenter.currency_code} {costCenter.actual_amount.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">Actual Amount</div>
            </div>
            <div className={`p-4 rounded-lg ${variance >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
              <div className={`text-2xl font-bold ${variance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {costCenter.currency_code} {Math.abs(variance).toLocaleString()}
              </div>
              <div className={`text-sm ${variance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {variance >= 0 ? 'Under Budget' : 'Over Budget'}
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">
                {utilizationPercentage.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Utilization</div>
            </div>
          </div>

          {/* Utilization Bar */}
          <div className="mt-4">
            <div className="flex justify-between text-sm text-gray-600 mb-1">
              <span>Budget Utilization</span>
              <span>{utilizationPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  utilizationPercentage > 100 ? 'bg-red-500' : 
                  utilizationPercentage > 90 ? 'bg-yellow-500' : 'bg-green-500'
                }`}
                style={{ width: `${Math.min(utilizationPercentage, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Cost Center Information */}
        <div className="px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Cost Center Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Department</label>
              <p className="mt-1 text-sm text-gray-900">{costCenter.department || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Location</label>
              <p className="mt-1 text-sm text-gray-900">{costCenter.location || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Manager</label>
              <p className="mt-1 text-sm text-gray-900">
                {costCenter.manager ? 
                  `${costCenter.manager.first_name} ${costCenter.manager.last_name}` : 
                  'N/A'
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Cost Allocation Method</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{costCenter.cost_allocation_method}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Responsibility Center Type</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{costCenter.responsibility_center_type}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Currency</label>
              <p className="mt-1 text-sm text-gray-900">{costCenter.currency_code}</p>
            </div>

            {costCenter.gl_account_range_start && (
              <div>
                <label className="block text-sm font-medium text-gray-700">GL Account Range</label>
                <p className="mt-1 text-sm text-gray-900">
                  {costCenter.gl_account_range_start} - {costCenter.gl_account_range_end}
                </p>
              </div>
            )}

            {costCenter.parent_cost_center && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Parent Cost Center</label>
                <p className="mt-1 text-sm text-gray-900">
                  {costCenter.parent_cost_center.cost_center_code} - {costCenter.parent_cost_center.cost_center_name}
                </p>
              </div>
            )}
          </div>

          {costCenter.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <p className="mt-1 text-sm text-gray-900">{costCenter.description}</p>
            </div>
          )}
        </div>

        {/* Performance Metrics */}
        <div className="px-6 py-4 border-t border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {variance >= 0 ? '+' : ''}{costCenter.currency_code} {variance.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Budget Variance</div>
              <div className={`text-xs mt-1 ${variance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {variance >= 0 ? 'Favorable' : 'Unfavorable'}
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {utilizationPercentage.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Budget Utilization</div>
              <div className={`text-xs mt-1 ${
                utilizationPercentage <= 100 ? 'text-green-600' : 'text-red-600'
              }`}>
                {utilizationPercentage <= 100 ? 'Within Budget' : 'Over Budget'}
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-lg font-semibold text-gray-900">
                {costCenter.budget_amount > 0 ? 
                  ((costCenter.actual_amount / costCenter.budget_amount) * 100).toFixed(1) : 
                  '0.0'
                }%
              </div>
              <div className="text-sm text-gray-600">Efficiency Ratio</div>
              <div className="text-xs mt-1 text-blue-600">Actual vs Budget</div>
            </div>
          </div>
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(costCenter.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(costCenter.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostCenterViewPage;
