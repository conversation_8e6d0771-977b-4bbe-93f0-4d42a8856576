import React, { useState } from 'react';
import {
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  // XMarkIcon,
  // CalendarIcon,
  // DocumentTextIcon,
  // BuildingOfficeIcon,
  // TagIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';
import { Agency, Category } from '../../types';

interface AdvancedSearchFormProps {
  onSearch: (params: Record<string, unknown>) => void;
  agencies: Agency[];
  categories: Category[];
  loading?: boolean;
}

interface AdvancedFilters {
  query: string;
  exactPhrase: string;
  anyWords: string;
  excludeWords: string;
  documentType: string;
  status: string;
  agencyId: string;
  categoryIds: string[];
  documentNumber: string;
  docketNumber: string;
  cfr: string;
  publicationDateRange: { from: string; to: string };
  effectiveDateRange: { from: string; to: string };
  commentDateRange: { from: string; to: string };
  pageCount: { min: string; max: string };
  hasComments: string;
  hasAttachments: string;
  sortBy: string;
  sortOrder: string;
}

const AdvancedSearchForm: React.FC<AdvancedSearchFormProps> = ({
  onSearch,
  agencies,
  categories,
  loading = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [filters, setFilters] = useState<AdvancedFilters>({
    query: '',
    exactPhrase: '',
    anyWords: '',
    excludeWords: '',
    documentType: '',
    status: '',
    agencyId: '',
    categoryIds: [],
    documentNumber: '',
    docketNumber: '',
    cfr: '',
    publicationDateRange: { from: '', to: '' },
    effectiveDateRange: { from: '', to: '' },
    commentDateRange: { from: '', to: '' },
    pageCount: { min: '', max: '' },
    hasComments: '',
    hasAttachments: '',
    sortBy: 'relevance',
    sortOrder: 'desc'
  });

  const handleInputChange = (field: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateRangeChange = (rangeField: string, dateField: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [rangeField]: {
        ...(prev[rangeField as keyof AdvancedFilters] as Record<string, string>),
        [dateField]: value
      }
    }));
  };

  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      categoryIds: checked
        ? [...prev.categoryIds, categoryId]
        : prev.categoryIds.filter(id => id !== categoryId)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Build search parameters
    const searchParams: Record<string, unknown> = {};
    
    // Basic search
    if (filters.query) searchParams.q = filters.query;
    
    // Advanced text search
    if (filters.exactPhrase) searchParams.exact_phrase = filters.exactPhrase;
    if (filters.anyWords) searchParams.any_words = filters.anyWords;
    if (filters.excludeWords) searchParams.exclude_words = filters.excludeWords;
    
    // Document properties
    if (filters.documentType) searchParams.type = filters.documentType;
    if (filters.status) searchParams.status = filters.status;
    if (filters.agencyId) searchParams.agency_id = filters.agencyId;
    if (filters.categoryIds.length > 0) searchParams.category_ids = filters.categoryIds.join(',');
    
    // Document numbers
    if (filters.documentNumber) searchParams.document_number = filters.documentNumber;
    if (filters.docketNumber) searchParams.docket_number = filters.docketNumber;
    if (filters.cfr) searchParams.cfr = filters.cfr;
    
    // Date ranges
    if (filters.publicationDateRange.from) searchParams.publication_date_from = filters.publicationDateRange.from;
    if (filters.publicationDateRange.to) searchParams.publication_date_to = filters.publicationDateRange.to;
    if (filters.effectiveDateRange.from) searchParams.effective_date_from = filters.effectiveDateRange.from;
    if (filters.effectiveDateRange.to) searchParams.effective_date_to = filters.effectiveDateRange.to;
    if (filters.commentDateRange.from) searchParams.comment_date_from = filters.commentDateRange.from;
    if (filters.commentDateRange.to) searchParams.comment_date_to = filters.commentDateRange.to;
    
    // Page count
    if (filters.pageCount.min) searchParams.page_count_min = filters.pageCount.min;
    if (filters.pageCount.max) searchParams.page_count_max = filters.pageCount.max;
    
    // Boolean filters
    if (filters.hasComments) searchParams.has_comments = filters.hasComments === 'true';
    if (filters.hasAttachments) searchParams.has_attachments = filters.hasAttachments === 'true';
    
    // Sorting
    searchParams.sort_by = filters.sortBy;
    searchParams.sort_order = filters.sortOrder;
    
    onSearch(searchParams);
  };

  const clearFilters = () => {
    setFilters({
      query: '',
      exactPhrase: '',
      anyWords: '',
      excludeWords: '',
      documentType: '',
      status: '',
      agencyId: '',
      categoryIds: [],
      documentNumber: '',
      docketNumber: '',
      cfr: '',
      publicationDateRange: { from: '', to: '' },
      effectiveDateRange: { from: '', to: '' },
      commentDateRange: { from: '', to: '' },
      pageCount: { min: '', max: '' },
      hasComments: '',
      hasAttachments: '',
      sortBy: 'relevance',
      sortOrder: 'desc'
    });
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
      <form onSubmit={handleSubmit}>
        {/* Basic Search */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={filters.query}
                onChange={(e) => handleInputChange('query', e.target.value)}
                className="w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white text-lg"
                placeholder="Search documents, agencies, regulations..."
              />
            </div>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
            >
              {loading ? 'Searching...' : 'Search'}
            </button>
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <AdjustmentsHorizontalIcon className="h-5 w-5 mr-2" />
              Advanced
              {showAdvanced ? (
                <ChevronUpIcon className="h-4 w-4 ml-2" />
              ) : (
                <ChevronDownIcon className="h-4 w-4 ml-2" />
              )}
            </button>
          </div>
        </div>

        {/* Advanced Filters */}
        {showAdvanced && (
          <div className="p-6 space-y-6">
            {/* Text Search Options */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Text Search Options
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Exact Phrase
                  </label>
                  <input
                    type="text"
                    value={filters.exactPhrase}
                    onChange={(e) => handleInputChange('exactPhrase', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="&quot;exact phrase&quot;"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Any of These Words
                  </label>
                  <input
                    type="text"
                    value={filters.anyWords}
                    onChange={(e) => handleInputChange('anyWords', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="word1 word2 word3"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Exclude Words
                  </label>
                  <input
                    type="text"
                    value={filters.excludeWords}
                    onChange={(e) => handleInputChange('excludeWords', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="-exclude -these"
                  />
                </div>
              </div>
            </div>

            {/* Document Properties */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Document Properties
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Document Type
                  </label>
                  <select
                    value={filters.documentType}
                    onChange={(e) => handleInputChange('documentType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">All Types</option>
                    <option value="rule">Rule</option>
                    <option value="proposed_rule">Proposed Rule</option>
                    <option value="notice">Notice</option>
                    <option value="presidential">Presidential Document</option>
                    <option value="correction">Correction</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Status
                  </label>
                  <select
                    value={filters.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">All Statuses</option>
                    <option value="published">Published</option>
                    <option value="public_inspection">Public Inspection</option>
                    <option value="withdrawn">Withdrawn</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Agency
                  </label>
                  <select
                    value={filters.agencyId}
                    onChange={(e) => handleInputChange('agencyId', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">All Agencies</option>
                    {agencies.map((agency) => (
                      <option key={agency.id} value={agency.id}>
                        {agency.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Sort By
                  </label>
                  <select
                    value={filters.sortBy}
                    onChange={(e) => handleInputChange('sortBy', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="relevance">Relevance</option>
                    <option value="publication_date">Publication Date</option>
                    <option value="effective_date">Effective Date</option>
                    <option value="title">Title</option>
                    <option value="agency">Agency</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Categories */}
            {categories.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                  Categories
                </h3>
                <div className="max-h-40 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-3">
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {categories.map((category) => (
                      <label key={category.id} className="flex items-center">
                        <input
                          type="checkbox"
                          checked={filters.categoryIds.includes(category.id.toString())}
                          onChange={(e) => handleCategoryChange(category.id.toString(), e.target.checked)}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                          {category.name}
                        </span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Document Numbers */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Document Numbers
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    FR Document Number
                  </label>
                  <input
                    type="text"
                    value={filters.documentNumber}
                    onChange={(e) => handleInputChange('documentNumber', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="2025-07479"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Docket Number
                  </label>
                  <input
                    type="text"
                    value={filters.docketNumber}
                    onChange={(e) => handleInputChange('docketNumber', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="BOEM-2025-0015"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    CFR Citation
                  </label>
                  <input
                    type="text"
                    value={filters.cfr}
                    onChange={(e) => handleInputChange('cfr', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="40 CFR 123"
                  />
                </div>
              </div>
            </div>

            {/* Date Ranges */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Date Ranges
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Publication Date
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="date"
                      value={filters.publicationDateRange.from}
                      onChange={(e) => handleDateRangeChange('publicationDateRange', 'from', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                    <input
                      type="date"
                      value={filters.publicationDateRange.to}
                      onChange={(e) => handleDateRangeChange('publicationDateRange', 'to', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Effective Date
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="date"
                      value={filters.effectiveDateRange.from}
                      onChange={(e) => handleDateRangeChange('effectiveDateRange', 'from', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                    <input
                      type="date"
                      value={filters.effectiveDateRange.to}
                      onChange={(e) => handleDateRangeChange('effectiveDateRange', 'to', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Comment Due Date
                  </label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="date"
                      value={filters.commentDateRange.from}
                      onChange={(e) => handleDateRangeChange('commentDateRange', 'from', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                    <input
                      type="date"
                      value={filters.commentDateRange.to}
                      onChange={(e) => handleDateRangeChange('commentDateRange', 'to', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Filters */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                Additional Filters
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Has Comments
                  </label>
                  <select
                    value={filters.hasComments}
                    onChange={(e) => handleInputChange('hasComments', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Any</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Has Attachments
                  </label>
                  <select
                    value={filters.hasAttachments}
                    onChange={(e) => handleInputChange('hasAttachments', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Any</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Min Pages
                  </label>
                  <input
                    type="number"
                    value={filters.pageCount.min}
                    onChange={(e) => handleDateRangeChange('pageCount', 'min', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Max Pages
                  </label>
                  <input
                    type="number"
                    value={filters.pageCount.max}
                    onChange={(e) => handleDateRangeChange('pageCount', 'max', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                    placeholder="999"
                  />
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                type="button"
                onClick={clearFilters}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
              >
                Clear All Filters
              </button>
              
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowAdvanced(false)}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                >
                  Hide Advanced
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                >
                  {loading ? 'Searching...' : 'Search'}
                </button>
              </div>
            </div>
          </div>
        )}
      </form>
    </div>
  );
};

export default AdvancedSearchForm;
