# Federal Register Clone API Documentation

## Overview

The Federal Register Clone API provides comprehensive access to government documents, agencies, categories, and user management functionality. The API follows RESTful principles and returns JSON responses.

**Base URL**: `http://127.0.0.1:8080/api/v1`

## Authentication

The API uses <PERSON>W<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Endpoints

#### POST /auth/login
Authenticate a user and receive access tokens.

**Request Body:**
```json
{
  "identifier": "username or email",
  "password": "user_password"
}
```

**Response:**
```json
{
  "token": "jwt_access_token",
  "refresh_token": "jwt_refresh_token",
  "user": {
    "id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "role": "editor",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>"
  },
  "expires_in": 86400
}
```

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "username": "john_doe",
  "email": "<EMAIL>",
  "password": "secure_password",
  "first_name": "John",
  "last_name": "Doe",
  "title": "Document Specialist",
  "organization": "Government Agency"
}
```

#### POST /auth/refresh
Refresh an access token using a refresh token.

**Request Body:**
```json
{
  "refresh_token": "jwt_refresh_token"
}
```

#### GET /auth/me
Get current authenticated user information.

**Headers:** `Authorization: Bearer <token>`

**Response:**
```json
{
  "id": 1,
  "username": "john_doe",
  "email": "<EMAIL>",
  "role": "editor",
  "first_name": "John",
  "last_name": "Doe",
  "agency": {
    "id": 1,
    "name": "Department of Example",
    "short_name": "DOE"
  }
}
```

#### POST /auth/logout
Logout and invalidate the current session.

**Headers:** `Authorization: Bearer <token>`

## Public Endpoints

### Documents

#### GET /public/documents
Get a list of published documents with pagination and filtering.

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `per_page` (int): Items per page (default: 25, max: 100)
- `q` (string): Search query
- `type` (string): Document type (rule, proposed_rule, notice, presidential, correction, other)
- `agency` (string): Agency ID
- `category` (string): Category ID
- `date_from` (string): Start date (YYYY-MM-DD)
- `date_to` (string): End date (YYYY-MM-DD)
- `sort` (string): Sort field (created_at, title, publication_date)
- `order` (string): Sort order (asc, desc)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "title": "Environmental Protection Regulation",
      "slug": "environmental-protection-regulation",
      "abstract": "New regulations for environmental protection...",
      "type": "rule",
      "status": "published",
      "publication_date": "2024-01-15T00:00:00Z",
      "effective_date": "2024-02-15T00:00:00Z",
      "agency": {
        "id": 1,
        "name": "Environmental Protection Agency",
        "short_name": "EPA"
      },
      "categories": [
        {
          "id": 1,
          "name": "Environmental Protection",
          "slug": "environmental-protection"
        }
      ],
      "view_count": 150,
      "created_at": "2024-01-10T10:00:00Z"
    }
  ],
  "total": 1250,
  "page": 1,
  "per_page": 25,
  "total_pages": 50,
  "has_next": true,
  "has_prev": false
}
```

#### GET /public/documents/{id}
Get a specific document by ID.

**Response:**
```json
{
  "id": 1,
  "title": "Environmental Protection Regulation",
  "content": "Full document content...",
  "type": "rule",
  "status": "published",
  "fr_document_number": "2024-12345",
  "fr_citation": "89 FR 12345",
  "cfr_citations": "[\"40 CFR 123\"]",
  "publication_date": "2024-01-15T00:00:00Z",
  "effective_date": "2024-02-15T00:00:00Z",
  "comment_due_date": "2024-03-15T00:00:00Z",
  "agency": {
    "id": 1,
    "name": "Environmental Protection Agency",
    "short_name": "EPA"
  },
  "files": [
    {
      "id": 1,
      "file_name": "regulation.pdf",
      "original_name": "EPA_Regulation_2024.pdf",
      "file_type": "pdf",
      "file_size": 1024000,
      "is_public": true
    }
  ],
  "accepts_comments": true,
  "comment_count": 45
}
```

#### GET /public/documents/search
Search documents with full-text search capabilities.

**Query Parameters:** Same as `/public/documents` plus:
- `q` (string, required): Search query

### Agencies

#### GET /public/agencies
Get a list of government agencies.

**Query Parameters:**
- `page` (int): Page number
- `per_page` (int): Items per page

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "Environmental Protection Agency",
      "short_name": "EPA",
      "slug": "environmental-protection-agency",
      "description": "Federal agency responsible for environmental protection",
      "website": "https://www.epa.gov",
      "document_count": 150
    }
  ],
  "total": 45,
  "page": 1,
  "per_page": 25,
  "total_pages": 2,
  "has_next": true,
  "has_prev": false
}
```

#### GET /public/agencies/{id}
Get a specific agency by ID.

#### GET /public/agencies/{id}/documents
Get documents published by a specific agency.

### Categories

#### GET /public/categories
Get a list of document categories.

**Response:**
```json
{
  "message": "Categories retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Environmental Protection",
      "slug": "environmental-protection",
      "description": "Documents related to environmental protection",
      "document_count": 150
    }
  ]
}
```

#### GET /public/categories/{id}/documents
Get documents in a specific category.

### Comments

#### POST /public/documents/{id}/comments
Submit a public comment on a document.

**Request Body:**
```json
{
  "commenter_name": "John Citizen",
  "commenter_email": "<EMAIL>",
  "organization": "Citizens for Environment",
  "subject": "Support for new regulation",
  "content": "I strongly support this new environmental regulation..."
}
```

#### GET /public/documents/{id}/comments
Get public comments for a document.

## Protected Endpoints

These endpoints require authentication and appropriate permissions.

### Document Management

#### GET /documents
Get documents with user permissions applied.

**Headers:** `Authorization: Bearer <token>`

#### POST /documents
Create a new document (requires editor role or higher).

**Request Body:**
```json
{
  "title": "New Environmental Regulation",
  "abstract": "Summary of the regulation...",
  "content": "Full content of the regulation...",
  "type": "rule",
  "agency_id": 1,
  "category_ids": [1, 2],
  "effective_date": "2024-03-01",
  "accepts_comments": true,
  "comment_due_date": "2024-02-15"
}
```

#### PUT /documents/{id}
Update an existing document (requires appropriate permissions).

#### DELETE /documents/{id}
Delete a document (requires admin role).

### Document Workflow

#### POST /documents/{id}/submit
Submit a document for review (requires editor role).

#### POST /documents/{id}/approve
Approve a document (requires reviewer role or higher).

#### POST /documents/{id}/publish
Publish a document (requires publisher role or higher).

#### POST /documents/{id}/withdraw
Withdraw a published document (requires admin role).

### File Management

#### POST /documents/{id}/files
Upload a file for a document.

**Content-Type:** `multipart/form-data`

#### GET /files/{id}/download
Download a document file.

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error type",
  "message": "Detailed error message"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Unprocessable Entity
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

The API implements rate limiting:
- **Default**: 100 requests per minute per IP
- **Burst**: 10 requests
- **Headers**: Rate limit information is included in response headers

## Pagination

Paginated endpoints return the following structure:

```json
{
  "data": [...],
  "total": 1250,
  "page": 1,
  "per_page": 25,
  "total_pages": 50,
  "has_next": true,
  "has_prev": false
}
```

## Search

The API supports full-text search using PostgreSQL's built-in search capabilities:

- **Simple search**: Basic keyword matching
- **Phrase search**: Use quotes for exact phrases
- **Boolean operators**: AND, OR, NOT
- **Filters**: Combine search with filters for refined results

## Webhooks

*Coming soon* - Webhook support for real-time notifications of document changes.

## SDKs and Libraries

*Coming soon* - Official SDKs for popular programming languages.

## Support

For API support, please contact: <EMAIL>
