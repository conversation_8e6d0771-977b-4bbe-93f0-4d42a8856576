'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ClockIcon,
  DocumentIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface RetentionPolicy {
  id: number;
  name: string;
  description: string;
  entity_type: 'document' | 'regulation' | 'finance' | 'user' | 'log' | 'all';
  retention_period_days: number;
  action_type: 'delete' | 'archive' | 'anonymize' | 'notify';
  is_active: boolean;
  auto_apply: boolean;
  conditions: any;
  last_applied: string;
  next_run: string;
  affected_count: number;
  created_by: number;
  created_at: string;
  updated_at: string;
}

const RetentionPoliciesPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [policies, setPolicies] = useState<RetentionPolicy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchPolicies();
  }, []);

  const fetchPolicies = async () => {
    try {
      setLoading(true);
      // Fetch real retention policies from API
      const response = await fetch('/api/retention/policies', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch retention policies');
      }

      const data = await response.json();
      const policies: RetentionPolicy[] = data.data || [];

      setPolicies(policies);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch retention policies');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this retention policy?')) return;
    
    try {
      // await apiService.deleteRetentionPolicy(id);
      setPolicies(policies.filter(policy => policy.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete retention policy');
    }
  };

  const handleToggleActive = async (id: number) => {
    try {
      // await apiService.toggleRetentionPolicy(id);
      setPolicies(policies.map(policy => 
        policy.id === id ? { ...policy, is_active: !policy.is_active } : policy
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to toggle policy status');
    }
  };

  const handleRunPolicy = async (id: number) => {
    try {
      // await apiService.runRetentionPolicy(id);
      setPolicies(policies.map(policy => 
        policy.id === id ? { ...policy, last_applied: new Date().toISOString() } : policy
      ));
      alert('Retention policy executed successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to run retention policy');
    }
  };

  const filteredPolicies = policies.filter(policy =>
    policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.entity_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getEntityTypeColor = (type: string) => {
    switch (type) {
      case 'document':
        return 'bg-blue-100 text-blue-800';
      case 'regulation':
        return 'bg-purple-100 text-purple-800';
      case 'finance':
        return 'bg-green-100 text-green-800';
      case 'user':
        return 'bg-orange-100 text-orange-800';
      case 'log':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-red-100 text-red-800';
    }
  };

  const getActionTypeColor = (action: string) => {
    switch (action) {
      case 'delete':
        return 'bg-red-100 text-red-800';
      case 'archive':
        return 'bg-blue-100 text-blue-800';
      case 'anonymize':
        return 'bg-yellow-100 text-yellow-800';
      case 'notify':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDays = (days: number) => {
    if (days >= 365) {
      const years = Math.floor(days / 365);
      const remainingDays = days % 365;
      return remainingDays > 0 ? `${years}y ${remainingDays}d` : `${years} year${years > 1 ? 's' : ''}`;
    }
    return `${days} day${days > 1 ? 's' : ''}`;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view retention policies.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Retention Policies</h1>
              <p className="text-gray-600 mt-1">Manage data retention policies and automated cleanup rules</p>
            </div>
            <Link
              href="/admin/retention/policies/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Policy
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search retention policies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading retention policies...</p>
          </div>
        ) : (
          /* Policies Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Policy Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entity Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Retention Period
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Action
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Affected Items
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Applied
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredPolicies.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No retention policies found.
                      <Link
                        href="/admin/retention/policies/new"
                        className="block mt-2 text-primary-600 hover:text-primary-500"
                      >
                        Create your first retention policy
                      </Link>
                    </td>
                  </tr>
                ) : (
                  filteredPolicies.map((policy) => (
                    <tr key={policy.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{policy.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{policy.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEntityTypeColor(policy.entity_type)}`}>
                          {policy.entity_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <ClockIcon className="h-4 w-4 mr-1 text-gray-400" />
                          {formatDays(policy.retention_period_days)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActionTypeColor(policy.action_type)}`}>
                          {policy.action_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {policy.is_active ? (
                            <CheckCircleIcon className="h-4 w-4 text-green-600 mr-1" />
                          ) : (
                            <ExclamationTriangleIcon className="h-4 w-4 text-yellow-600 mr-1" />
                          )}
                          <span className={`text-sm ${policy.is_active ? 'text-green-600' : 'text-yellow-600'}`}>
                            {policy.is_active ? 'Active' : 'Inactive'}
                          </span>
                          {policy.auto_apply && (
                            <span className="ml-2 text-xs text-blue-600">(Auto)</span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {policy.affected_count.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(policy.last_applied)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/retention/policies/${policy.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                            title="View Policy"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleRunPolicy(policy.id)}
                            className="text-green-600 hover:text-green-900"
                            title="Run Policy Now"
                          >
                            <ShieldCheckIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleToggleActive(policy.id)}
                            className={`${
                              policy.is_active ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'
                            }`}
                            title={policy.is_active ? 'Deactivate' : 'Activate'}
                          >
                            {policy.is_active ? <ExclamationTriangleIcon className="h-4 w-4" /> : <CheckCircleIcon className="h-4 w-4" />}
                          </button>
                          <button
                            onClick={() => router.push(`/admin/retention/policies/${policy.id}/edit`)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Edit Policy"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(policy.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Policy"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default RetentionPoliciesPage;
