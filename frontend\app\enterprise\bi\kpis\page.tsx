'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon as TrendingUpIcon,
  ArrowTrendingDownIcon as TrendingDownIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import { biApi } from '../../../services/enterpriseApi';
import { KPI } from '../../../types/enterprise';

const KPIsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [kpis, setKpis] = useState<KPI[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchKPIs();
  }, []);

  const fetchKPIs = async () => {
    try {
      setLoading(true);
      const response = await biApi.getKPIs();
      setKpis(response.data || []);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch KPIs');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this KPI?')) return;
    
    try {
      await biApi.deleteKPI(id);
      await fetchKPIs(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to delete KPI');
    }
  };

  const filteredKPIs = kpis.filter(kpi =>
    kpi.kpi_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kpi.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    kpi.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend?.toLowerCase()) {
      case 'up':
      case 'increasing':
        return <TrendingUpIcon className="h-4 w-4 text-green-600" />;
      case 'down':
      case 'decreasing':
        return <TrendingDownIcon className="h-4 w-4 text-red-600" />;
      default:
        return <ArrowRightIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatValue = (value: number, unit: string) => {
    if (unit === '%') {
      return `${value}%`;
    } else if (unit === '$') {
      return `$${value.toLocaleString()}`;
    } else {
      return `${value.toLocaleString()} ${unit}`;
    }
  };

  if (!user) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be logged in to view KPIs.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Key Performance Indicators</h1>
              <p className="text-gray-600 mt-1">Monitor and track organizational performance metrics</p>
            </div>
            {(user.role === 'admin' || user.role === 'editor') && (
              <Link
                href="/enterprise/bi/kpis/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create KPI
              </Link>
            )}
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search KPIs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading KPIs...</p>
          </div>
        ) : (
          /* KPIs Grid */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredKPIs.length === 0 ? (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No KPIs found.</p>
                {(user.role === 'admin' || user.role === 'editor') && (
                  <Link
                    href="/enterprise/bi/kpis/new"
                    className="inline-flex items-center mt-4 text-primary-600 hover:text-primary-500"
                  >
                    <PlusIcon className="h-4 w-4 mr-2" />
                    Create your first KPI
                  </Link>
                )}
              </div>
            ) : (
              filteredKPIs.map((kpi) => (
                <div key={kpi.id} className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 truncate">
                        {kpi.kpi_name}
                      </h3>
                      <p className="text-sm text-gray-500">{kpi.category}</p>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(kpi.is_active ? 'active' : 'inactive')}`}>
                      {kpi.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-2xl font-bold text-gray-900">
                        {formatValue(kpi.current_value, kpi.unit)}
                      </span>
                      {getTrendIcon(kpi.trend)}
                    </div>
                    <div className="text-sm text-gray-600">
                      Target: {formatValue(kpi.target_value, kpi.unit)}
                    </div>
                    {kpi.target_value > 0 && (
                      <div className="mt-2">
                        <div className="bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full ${
                              (kpi.current_value / kpi.target_value) >= 1 ? 'bg-green-600' :
                              (kpi.current_value / kpi.target_value) >= 0.8 ? 'bg-yellow-600' :
                              'bg-red-600'
                            }`}
                            style={{ width: `${Math.min((kpi.current_value / kpi.target_value) * 100, 100)}%` }}
                          ></div>
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {((kpi.current_value / kpi.target_value) * 100).toFixed(1)}% of target
                        </div>
                      </div>
                    )}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {kpi.description}
                  </p>
                  
                  <div className="text-xs text-gray-500 mb-4">
                    <p>Frequency: {kpi.update_frequency}</p>
                    <p>Updated: {new Date(kpi.updated_at).toLocaleDateString()}</p>
                  </div>
                  
                  <div className="flex space-x-2">
                    <button
                      onClick={() => router.push(`/enterprise/bi/kpis/${kpi.id}`)}
                      className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
                    >
                      <EyeIcon className="h-4 w-4 inline mr-1" />
                      View
                    </button>
                    {(user.role === 'admin' || user.role === 'editor') && (
                      <>
                        <button
                          onClick={() => router.push(`/enterprise/bi/kpis/${kpi.id}/edit`)}
                          className="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700"
                        >
                          <PencilIcon className="h-4 w-4 inline mr-1" />
                          Edit
                        </button>
                        <button
                          onClick={() => handleDelete(kpi.id)}
                          className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default KPIsPage;
