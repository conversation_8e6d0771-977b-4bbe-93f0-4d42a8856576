-- Migration 022: Fix missing columns causing 500 errors
-- This migration adds missing columns that are referenced in models but don't exist in database

-- 1. Add missing columns to digital_certificates table
ALTER TABLE digital_certificates
ADD COLUMN IF NOT EXISTS key_algorithm VARCHAR(50) DEFAULT 'RSA',
ADD COLUMN IF NOT EXISTS key_length INTEGER DEFAULT 2048,
ADD COLUMN IF NOT EXISTS signature_algorithm VARCHAR(50) DEFAULT 'SHA256withRSA',
ADD COLUMN IF NOT EXISTS public_key_algorithm VARCHAR(50) DEFAULT 'RSA';

-- 2. Add missing columns to kpis table if they don't exist
ALTER TABLE kpis
ADD COLUMN IF NOT EXISTS formula TEXT,
ADD COLUMN IF NOT EXISTS data_source TEXT,
ADD COLUMN IF NOT EXISTS min_threshold DECIMAL(15,4),
ADD COLUMN IF NOT EXISTS max_threshold DECIMAL(15,4),
ADD COLUMN IF NOT EXISTS critical_min DECIMAL(15,4),
ADD COLUMN IF NOT EXISTS critical_max DECIMAL(15,4),
ADD COLUMN IF NOT EXISTS previous_value DECIMAL(15,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS trend VARCHAR(50) DEFAULT 'stable',
ADD COLUMN IF NOT EXISTS last_calculated TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS next_update TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS alert_enabled BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS alert_threshold DECIMAL(15,4),
ADD COLUMN IF NOT EXISTS last_alert TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- 3. Add missing columns to regulation_chunks table
ALTER TABLE regulation_chunks
ADD COLUMN IF NOT EXISTS chunk_order INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS content TEXT,
ADD COLUMN IF NOT EXISTS chunk_type VARCHAR(50) DEFAULT 'section',
ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT true;

-- 4. Add missing columns to digital_signatures table
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS mfa_required BOOLEAN DEFAULT false;

-- 5. Add missing columns to digital_certificates table (additional)
ALTER TABLE digital_certificates
ADD COLUMN IF NOT EXISTS curve_type VARCHAR(50) DEFAULT 'P-256',
ADD COLUMN IF NOT EXISTS subject_alt_names TEXT;

-- 6. Add missing columns to digital_signatures table (additional)
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS mfa_methods TEXT;

-- 7. Add missing columns to regulation_chunks table (additional)
ALTER TABLE regulation_chunks
ADD COLUMN IF NOT EXISTS version VARCHAR(50) DEFAULT '1.0',
ADD COLUMN IF NOT EXISTS effective_date TIMESTAMPTZ;

-- 8. Add missing columns to digital_signatures table (additional)
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS sms_verification BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS totp_verification VARCHAR(255);

-- 9. Add missing columns to digital_certificates table (additional)
ALTER TABLE digital_certificates
ADD COLUMN IF NOT EXISTS authority_key_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS subject_key_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS basic_constraints VARCHAR(255);

-- 10. Fix proceeding_regulations table column naming for GORM compatibility
-- Drop the existing table and recreate with correct column names
DROP TABLE IF EXISTS proceeding_regulations CASCADE;
CREATE TABLE proceeding_regulations (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    laws_and_rules_id INTEGER NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related',
    notes TEXT,
    PRIMARY KEY (proceeding_id, laws_and_rules_id)
);

-- Create indexes for the new table
CREATE INDEX IF NOT EXISTS idx_proceeding_regulations_proceeding_id ON proceeding_regulations(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_regulations_laws_and_rules_id ON proceeding_regulations(laws_and_rules_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_regulations_relationship_type ON proceeding_regulations(relationship_type);

-- 10. Add missing columns to digital_signatures table (additional)
ALTER TABLE digital_signatures
ADD COLUMN IF NOT EXISTS email_verification BOOLEAN DEFAULT false;

-- 11. Add missing columns to regulation_chunks table (additional)
ALTER TABLE regulation_chunks
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- 4. Update existing records to have default values
UPDATE digital_certificates SET key_algorithm = 'RSA' WHERE key_algorithm IS NULL;
UPDATE digital_certificates SET key_length = 2048 WHERE key_length IS NULL;
UPDATE digital_certificates SET signature_algorithm = 'SHA256withRSA' WHERE signature_algorithm IS NULL;
UPDATE kpis SET trend = 'stable' WHERE trend IS NULL;
UPDATE kpis SET previous_value = 0 WHERE previous_value IS NULL;
UPDATE kpis SET alert_enabled = false WHERE alert_enabled IS NULL;

-- 4. Add comments for documentation
COMMENT ON COLUMN digital_certificates.key_algorithm IS 'Algorithm used for key generation (RSA, ECDSA, etc.)';
COMMENT ON COLUMN digital_certificates.key_length IS 'Key length in bits (e.g., 2048, 4096)';
COMMENT ON COLUMN digital_certificates.signature_algorithm IS 'Algorithm used for digital signatures';
COMMENT ON COLUMN kpis.formula IS 'Mathematical formula for calculating the KPI';
COMMENT ON COLUMN kpis.data_source IS 'Source of data for KPI calculation';
COMMENT ON COLUMN kpis.min_threshold IS 'Minimum acceptable threshold value';
COMMENT ON COLUMN kpis.max_threshold IS 'Maximum acceptable threshold value';
COMMENT ON COLUMN kpis.critical_min IS 'Critical minimum threshold value';
COMMENT ON COLUMN kpis.critical_max IS 'Critical maximum threshold value';
COMMENT ON COLUMN kpis.alert_threshold IS 'Threshold value that triggers alerts';
COMMENT ON COLUMN kpis.last_alert IS 'Timestamp of the last alert sent for this KPI';
