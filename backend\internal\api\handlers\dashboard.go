package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// DashboardStats represents dashboard statistics
type DashboardStats struct {
	Documents      int64   `json:"documents"`
	Regulations    int64   `json:"regulations"`
	Tasks          int64   `json:"tasks"`
	Finances       int64   `json:"finances"`
	Summaries      int64   `json:"summaries"`
	Agencies       int64   `json:"agencies"`
	Categories     int64   `json:"categories"`
	OverdueTasks   int64   `json:"overdue_tasks"`
	TotalBudget    float64 `json:"total_budget"`
	PerformanceAvg float64 `json:"performance_avg"`
}

// GetDashboardStats returns dashboard statistics
// @Summary Get dashboard statistics
// @Description Returns comprehensive dashboard statistics
// @Tags dashboard
// @Accept json
// @Produce json
// @Success 200 {object} SuccessResponse
// @Failure 500 {object} ErrorResponse
// @Router /analytics/dashboard [get]
func GetDashboardStats(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var stats DashboardStats

	// Count documents
	db.Model(&models.Document{}).Count(&stats.Documents)

	// Count regulations
	db.Model(&models.LawsAndRules{}).Count(&stats.Regulations)

	// Count tasks
	db.Model(&models.Task{}).Count(&stats.Tasks)

	// Count finances
	db.Model(&models.Finance{}).Count(&stats.Finances)

	// Count summaries
	db.Model(&models.Summary{}).Count(&stats.Summaries)

	// Count agencies
	db.Model(&models.Agency{}).Count(&stats.Agencies)

	// Count categories
	db.Model(&models.Category{}).Count(&stats.Categories)

	// Count overdue tasks
	now := time.Now()
	db.Model(&models.Task{}).Where("due_date < ? AND status != ? AND status != ?",
		now, models.TaskStatusCompleted, models.TaskStatusCancelled).Count(&stats.OverdueTasks)

	// Calculate total budget
	db.Model(&models.Finance{}).Where("budget_type = ?", "original").
		Select("COALESCE(SUM(amount), 0)").Scan(&stats.TotalBudget)

	// Calculate average performance
	db.Model(&models.FinancePerformance{}).
		Select("COALESCE(AVG(performance_percentage), 0)").Scan(&stats.PerformanceAvg)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Dashboard statistics retrieved successfully",
		Data:    stats,
	})
}

// GetDocumentStats returns document statistics
func GetDocumentStats(c *gin.Context) {
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var totalDocuments int64
	db.Model(&models.Document{}).Where("is_public = ? AND visibility_level = ?", true, 1).Count(&totalDocuments)

	var totalAgencies int64
	db.Model(&models.Agency{}).Where("is_active = ?", true).Count(&totalAgencies)

	var recentDocuments int64
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	db.Model(&models.Document{}).Where("is_public = ? AND visibility_level = ? AND created_at >= ?", true, 1, thirtyDaysAgo).Count(&recentDocuments)

	stats := gin.H{
		"totalDocuments":  totalDocuments,
		"totalAgencies":   totalAgencies,
		"recentDocuments": recentDocuments,
	}

	c.JSON(http.StatusOK, stats)
}

// GetAgencyStats returns agency statistics
func GetAgencyStats(c *gin.Context) {
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var activeAgencies int64
	db.Model(&models.Agency{}).Where("is_active = ?", true).Count(&activeAgencies)

	var inactiveAgencies int64
	db.Model(&models.Agency{}).Where("is_active = ?", false).Count(&inactiveAgencies)

	stats := gin.H{
		"active_agencies":   activeAgencies,
		"inactive_agencies": inactiveAgencies,
	}

	c.JSON(http.StatusOK, stats)
}

// GetSystemUserStats returns system-wide user statistics
func GetSystemUserStats(c *gin.Context) {
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var totalUsers int64
	db.Model(&models.User{}).Count(&totalUsers)

	var activeUsers int64
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	db.Model(&models.User{}).Where("last_login_at > ?", thirtyDaysAgo).Count(&activeUsers)

	var adminCount, editorCount, viewerCount int64
	db.Model(&models.User{}).Where("role = ?", models.RoleAdmin).Count(&adminCount)
	db.Model(&models.User{}).Where("role = ?", models.RoleEditor).Count(&editorCount)
	db.Model(&models.User{}).Where("role = ?", models.RoleViewer).Count(&viewerCount)

	stats := gin.H{
		"total_users":  totalUsers,
		"active_users": activeUsers,
		"role_breakdown": gin.H{
			"admins":  adminCount,
			"editors": editorCount,
			"viewers": viewerCount,
		},
	}

	c.JSON(http.StatusOK, stats)
}

// GetRelationshipStats returns relationship statistics
func GetRelationshipStats(c *gin.Context) {
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var documentRelationships int64
	db.Model(&models.RegulationDocumentRelationship{}).Count(&documentRelationships)

	var regulationRelationships int64
	db.Model(&models.Relationship{}).Count(&regulationRelationships)

	var categoryRelationships int64
	db.Model(&models.RegulationCategoryRelationship{}).Count(&categoryRelationships)

	stats := gin.H{
		"document_relationships":   documentRelationships,
		"regulation_relationships": regulationRelationships,
		"category_relationships":   categoryRelationships,
	}

	c.JSON(http.StatusOK, stats)
}
