'use client'

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../../components/Layout/Layout';
import { useAuthStore } from '../../../../stores/authStore';
import apiService from '../../../../services/api';
import { Document, Agency, Category, Tag } from '../../../../types';

interface DocumentFormData {
  title: string;
  content: string;
  abstract: string; // Backend uses 'abstract' not 'summary'
  type: string; // Backend uses 'type' not 'document_type'
  status: string;
  agency_id: number | null;
  category_ids: number[]; // Backend uses 'category_ids' array not single 'category_id'
  fr_document_number: string; // Backend uses 'fr_document_number'
  docket_number: string;
  publication_date: string;
  effective_date: string;
  termination_date: string;
  comment_due_date: string;
  accepts_comments: boolean;
  is_public: boolean;
  visibility_level: number;
  tag_ids: number[]; // Backend uses tag_ids array
}

const AdminEditDocumentPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const { user, isAuthenticated } = useAuthStore();
  const [document, setDocument] = useState<Document | null>(null);
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [availableTags, setAvailableTags] = useState<Tag[]>([]);
  const [tagInput, setTagInput] = useState('');
  const [formData, setFormData] = useState<DocumentFormData>({
    title: '',
    content: '',
    abstract: '',
    type: 'rule',
    status: 'draft',
    agency_id: null,
    category_ids: [],
    fr_document_number: '',
    docket_number: '',
    publication_date: '',
    effective_date: '',
    termination_date: '',
    comment_due_date: '',
    accepts_comments: false,
    is_public: false,
    visibility_level: 1,
    tag_ids: []
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');

  const documentId = params?.id as string;

  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'admin') {
      router.push('/dashboard');
      return;
    }

    if (documentId) {
      fetchDocument();
      fetchAgencies();
      fetchCategories();
      fetchTags();
    }
  }, [documentId, isAuthenticated, user, router]);

  const fetchDocument = async () => {
    try {
      setLoading(true);
      const docData = await apiService.getDocument(parseInt(documentId));
      setDocument(docData);
      setFormData({
        title: docData.title || '',
        content: docData.content || '',
        abstract: docData.abstract || '',
        type: docData.type || 'rule',
        status: docData.status || 'draft',
        agency_id: docData.agency_id || null,
        category_ids: docData.categories ? docData.categories.map((cat: any) => cat.id) : [],
        fr_document_number: docData.fr_document_number || '',
        docket_number: docData.docket_number || '',
        publication_date: docData.publication_date ? new Date(docData.publication_date).toISOString().split('T')[0] : '',
        effective_date: docData.effective_date ? new Date(docData.effective_date).toISOString().split('T')[0] : '',
        termination_date: docData.termination_date ? new Date(docData.termination_date).toISOString().split('T')[0] : '',
        comment_due_date: docData.comment_due_date ? new Date(docData.comment_due_date).toISOString().split('T')[0] : '',
        accepts_comments: docData.accepts_comments ?? false,
        is_public: docData.is_public ?? false,
        visibility_level: docData.visibility_level || 1,
        tag_ids: docData.tags ? docData.tags.map((tag: any) => tag.id) : []
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch document');
    } finally {
      setLoading(false);
    }
  };

  const fetchAgencies = async () => {
    try {
      const response = await apiService.getAgencies({ per_page: 100 });
      setAgencies(response.data || []);
    } catch (err: any) {
      console.error('Failed to fetch agencies:', err);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await apiService.getCategories({ per_page: 100 });
      setCategories(response.data || []);
    } catch (err: any) {
      console.error('Failed to fetch categories:', err);
    }
  };

  const fetchTags = async () => {
    try {
      const response = await apiService.getTags();
      setAvailableTags(response.data || []);
    } catch (err: any) {
      console.error('Failed to fetch tags:', err);
    }
  };

  const handleAddTag = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      const tagName = tagInput.trim();

      // Check if tag already exists
      let existingTag = availableTags.find(tag =>
        tag.name.toLowerCase() === tagName.toLowerCase()
      );

      // If tag doesn't exist, create it
      if (!existingTag) {
        try {
          const response = await apiService.createTag({
            name: tagName,
            description: `Auto-created tag: ${tagName}`
          });
          existingTag = response.data;
          setAvailableTags(prev => [...prev, existingTag!]);
        } catch (err: any) {
          console.error('Failed to create tag:', err);
          setError('Failed to create tag');
          return;
        }
      }

      // Add tag to form data if not already added
      if (existingTag && !formData.tag_ids.includes(existingTag.id)) {
        setFormData(prev => ({
          ...prev,
          tag_ids: [...prev.tag_ids, existingTag!.id]
        }));
      }

      setTagInput('');
    }
  };

  const handleRemoveTag = (tagId: number) => {
    setFormData(prev => ({
      ...prev,
      tag_ids: prev.tag_ids.filter(id => id !== tagId)
    }));
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked :
              name === 'agency_id' ? (value ? parseInt(value) : null) :
              name === 'category_ids' ? (value ? [parseInt(value)] : []) :
              name === 'visibility_level' ? parseInt(value) || 1 :
              value
    }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title.trim()) {
      setError('Document title is required');
      return;
    }

    try {
      setSaving(true);
      setError('');

      // Prepare the data in the format expected by the backend
      const updateData = {
        ...formData,
        // Ensure agency_id is properly set
        agency_id: formData.agency_id || undefined,
        // Ensure category_ids is properly set
        category_ids: formData.category_ids.length > 0 ? formData.category_ids : undefined,
        // Ensure tag_ids is properly set
        tag_ids: formData.tag_ids.length > 0 ? formData.tag_ids : undefined
      };

      await apiService.updateDocument(parseInt(documentId), updateData);
      router.push('/admin/documents');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update document');
    } finally {
      setSaving(false);
    }
  };

  if (!isAuthenticated || user?.role !== 'admin') {
    return null;
  }

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading document...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!document) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Document Not Found</h3>
            <p className="text-gray-600 mb-4">The document you're looking for doesn't exist.</p>
            <Link
              href="/admin/documents"
              className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Documents
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/admin/documents"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Admin Documents
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Document</h1>
              <p className="text-gray-600 mt-1">Update document information</p>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Form */}
        <div className="bg-white rounded-lg shadow-md">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Title */}
              <div className="md:col-span-2">
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              {/* Document Type */}
              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                  Document Type
                </label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="rule">Rule</option>
                  <option value="proposed_rule">Proposed Rule</option>
                  <option value="notice">Notice</option>
                  <option value="presidential">Presidential Document</option>
                </select>
              </div>

              {/* Status */}
              <div>
                <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="draft">Draft</option>
                  <option value="under_review">Under Review</option>
                  <option value="published">Published</option>
                  <option value="withdrawn">Withdrawn</option>
                </select>
              </div>

              {/* Agency */}
              <div>
                <label htmlFor="agency_id" className="block text-sm font-medium text-gray-700 mb-2">
                  Agency
                </label>
                <select
                  id="agency_id"
                  name="agency_id"
                  value={formData.agency_id || ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select Agency</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id}>
                      {agency.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category */}
              <div>
                <label htmlFor="category_ids" className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <select
                  id="category_ids"
                  name="category_ids"
                  value={formData.category_ids.length > 0 ? formData.category_ids[0] : ''}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">Select Category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* FR Number */}
              <div>
                <label htmlFor="fr_document_number" className="block text-sm font-medium text-gray-700 mb-2">
                  FR Number
                </label>
                <input
                  type="text"
                  id="fr_document_number"
                  name="fr_document_number"
                  value={formData.fr_document_number}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* Docket Number */}
              <div>
                <label htmlFor="docket_number" className="block text-sm font-medium text-gray-700 mb-2">
                  Docket Number
                </label>
                <input
                  type="text"
                  id="docket_number"
                  name="docket_number"
                  value={formData.docket_number}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* Publication Date */}
              <div>
                <label htmlFor="publication_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Publication Date
                </label>
                <input
                  type="date"
                  id="publication_date"
                  name="publication_date"
                  value={formData.publication_date}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* Effective Date */}
              <div>
                <label htmlFor="effective_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Effective Date
                </label>
                <input
                  type="date"
                  id="effective_date"
                  name="effective_date"
                  value={formData.effective_date}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* Termination Date */}
              <div>
                <label htmlFor="termination_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Termination Date
                </label>
                <input
                  type="date"
                  id="termination_date"
                  name="termination_date"
                  value={formData.termination_date}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* Comment Due Date */}
              <div>
                <label htmlFor="comment_due_date" className="block text-sm font-medium text-gray-700 mb-2">
                  Comment Due Date
                </label>
                <input
                  type="date"
                  id="comment_due_date"
                  name="comment_due_date"
                  value={formData.comment_due_date}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* Visibility Level */}
              <div>
                <label htmlFor="visibility_level" className="block text-sm font-medium text-gray-700 mb-2">
                  Visibility Level
                </label>
                <select
                  id="visibility_level"
                  name="visibility_level"
                  value={formData.visibility_level}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value={1}>Public</option>
                  <option value={2}>Internal</option>
                  <option value={3}>Restricted</option>
                </select>
              </div>

              {/* Checkboxes */}
              <div className="md:col-span-2 flex items-center space-x-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="accepts_comments"
                    checked={formData.accepts_comments}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Accepts Comments</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="is_public"
                    checked={formData.is_public}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Public</span>
                </label>
              </div>
            </div>

            {/* Tags */}
            <div>
              <label htmlFor="tag_ids" className="block text-sm font-medium text-gray-700 mb-2">
                Tags
              </label>
              <div className="space-y-2">
                <input
                  type="text"
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleAddTag}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Type a tag and press Enter"
                />
                {formData.tag_ids && formData.tag_ids.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tag_ids.map((tagId, index) => {
                      const tag = availableTags.find(t => t.id === tagId);
                      return tag ? (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                        >
                          {tag.name}
                          <button
                            type="button"
                            onClick={() => handleRemoveTag(tagId)}
                            className="ml-1 text-primary-600 hover:text-primary-800"
                          >
                            ×
                          </button>
                        </span>
                      ) : null;
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Abstract */}
            <div>
              <label htmlFor="abstract" className="block text-sm font-medium text-gray-700 mb-2">
                Abstract
              </label>
              <textarea
                id="abstract"
                name="abstract"
                value={formData.abstract}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* Content */}
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-2">
                Content
              </label>
              <textarea
                id="content"
                name="content"
                value={formData.content}
                onChange={handleInputChange}
                rows={10}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link
                href="/admin/documents"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <XMarkIcon className="h-4 w-4 mr-2" />
                Cancel
              </Link>
              <button
                type="submit"
                disabled={saving}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 disabled:opacity-50"
              >
                <CheckIcon className="h-4 w-4 mr-2" />
                {saving ? 'Updating...' : 'Update Document'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default AdminEditDocumentPage;
