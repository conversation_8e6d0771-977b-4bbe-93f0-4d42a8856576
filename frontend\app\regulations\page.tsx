'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  BookOpenIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  TagIcon,
  ClockIcon,
  ChevronRightIcon,
  FolderIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';
import { Regulation } from '../types';
import DataTable, { Column, ActionButton } from '../components/UI/DataTable';
import StatusBadge from '../components/UI/StatusBadge';
import SearchFilter, { FilterOption } from '../components/UI/SearchFilter';

const RegulationsPage: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const [regulations, setRegulations] = useState<Regulation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    chapter: '',
    title: '',
    status: '',
    sort: 'chapter_number',
    order: 'asc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    total_pages: 0
  });

  const fetchRegulations = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        per_page: pagination.per_page,
        search: searchTerm,
        ...filters
      };

      const response = isAuthenticated
        ? await apiService.getAuthenticatedRegulations(params)
        : await apiService.getPublicRegulations(params);

      // Handle different response structures
      if (isAuthenticated) {
        // PaginationResponse structure
        setRegulations(response.data || []);
        setPagination({
          page: response.page || 1,
          per_page: response.per_page || 20,
          total: response.total || 0,
          total_pages: response.total_pages || 1
        });
      } else {
        // SuccessResponse structure - data is in response.data
        setRegulations(response.data || []);
        setPagination({
          page: 1,
          per_page: 20,
          total: Array.isArray(response.data) ? response.data.length : 0,
          total_pages: 1
        });
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch regulations');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRegulations();
  }, [pagination.page, searchTerm, filters, isAuthenticated]);

  const canEdit = () => {
    if (!user) return false;
    return user.role === 'admin' || user.role === 'editor';
  };



  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this regulation?')) return;

    try {
      await apiService.deleteRegulation(id);
      fetchRegulations();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete regulation');
    }
  };

  // Define table columns
  const columns: Column<Regulation>[] = [
    {
      key: 'chapter_number',
      label: 'Chapter',
      sortable: true,
      render: (value, item) => (
        <div className="font-mono text-sm">
          {value && `Ch. ${value}`}
        </div>
      )
    },
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      render: (value, item) => (
        <div>
          <Link href={`/regulations/${item.id}`} className="text-blue-600 hover:text-blue-800 font-medium">
            {value}
          </Link>
          {item.description && (
            <p className="text-sm text-gray-500 mt-1 line-clamp-2">{item.description}</p>
          )}
        </div>
      )
    },
    {
      key: 'usc_title',
      label: 'USC Title',
      render: (value) => value ? `USC ${value}` : '-'
    },
    {
      key: 'cfr_title',
      label: 'CFR Title',
      render: (value) => value ? `CFR ${value}` : '-'
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => <StatusBadge status={value} size="sm" />
    },
    {
      key: 'effective_date',
      label: 'Effective Date',
      sortable: true,
      render: (value) => value ? new Date(value).toLocaleDateString() : '-'
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  // Define table actions
  const actions: ActionButton<Regulation>[] = [
    {
      label: 'View',
      icon: EyeIcon,
      href: (item) => `/regulations/${parseInt(item.id.toString())}`,
      className: 'text-blue-600 hover:text-blue-800'
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      href: (item) => `/regulations/${parseInt(item.id.toString())}/edit`,
      className: 'text-green-600 hover:text-green-800',
      show: () => isAuthenticated && (user?.role === 'admin' || user?.role === 'editor')
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (item) => handleDelete(item.id),
      className: 'text-red-600 hover:text-red-800',
      show: () => isAuthenticated && user?.role === 'admin'
    }
  ];

  // Define filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'effective', label: 'Effective' },
        { value: 'published', label: 'Published' },
        { value: 'draft', label: 'Draft' },
        { value: 'under_review', label: 'Under Review' },
        { value: 'approved', label: 'Approved' },
        { value: 'archived', label: 'Archived' },
        { value: 'superseded', label: 'Superseded' },
        { value: 'terminated', label: 'Terminated' }
      ]
    },
    {
      key: 'usc_title',
      label: 'USC Title',
      type: 'number',
      placeholder: 'Enter USC Title number'
    },
    {
      key: 'cfr_title',
      label: 'CFR Title',
      type: 'number',
      placeholder: 'Enter CFR Title number'
    },
    {
      key: 'effective_date',
      label: 'Effective Date',
      type: 'daterange'
    }
  ];

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Regulations</h1>
            <p className="text-gray-600">
              Browse the Code of Federal Regulations (CFR) and regulatory structure
            </p>
          </div>
          <div className="flex space-x-3">
            <Link
              href="/regulations/full"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <BookOpenIcon className="h-4 w-4 mr-2" />
              Book View
            </Link>
            {isAuthenticated && canEdit() && (
              <Link
                href="/regulations/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Regulation
              </Link>
            )}
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <SearchFilter
            searchValue={searchTerm}
            onSearchChange={setSearchTerm}
            filters={filterOptions}
            filterValues={filters}
            onFilterChange={(newFilters) => {
              setFilters(prev => ({ ...prev, ...newFilters }));
              setPagination(prev => ({ ...prev, page: 1 }));
            }}
            placeholder="Search regulations..."
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Regulations Table */}
        <DataTable
          data={regulations}
          columns={columns}
          actions={actions}
          loading={loading}
          error={error}
          pagination={pagination}
          onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
          onSort={(column, direction) => {
            setFilters(prev => ({ ...prev, sort: column, order: direction }));
            setPagination(prev => ({ ...prev, page: 1 }));
          }}
          sortColumn={filters.sort}
          sortDirection={filters.order as 'asc' | 'desc'}
          emptyMessage={
            searchTerm
              ? 'No regulations found matching your search criteria. Try adjusting your search terms or filters.'
              : 'No regulations found. Get started by adding your first regulation.'
          }
        />
      </div>
    </Layout>
  );
};

export default RegulationsPage;
