<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Federal Register Clone - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, #2563eb, #1d4ed8);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            border-radius: 10px;
        }
        
        h1 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .demo-section {
            background: white;
            padding: 2rem;
            margin-bottom: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            color: #2563eb;
            margin-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 0.5rem;
        }
        
        .api-test {
            margin-bottom: 1.5rem;
        }
        
        .api-test h3 {
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .api-url {
            background: #f3f4f6;
            padding: 0.5rem;
            border-radius: 5px;
            font-family: monospace;
            margin-bottom: 0.5rem;
            word-break: break-all;
        }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        button:hover {
            background: #1d4ed8;
        }
        
        .result {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 0.5rem;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result pre {
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 0.9rem;
        }
        
        .search-form {
            margin-bottom: 1rem;
        }
        
        .search-form input {
            padding: 0.5rem;
            border: 1px solid #d1d5db;
            border-radius: 5px;
            margin-right: 0.5rem;
            width: 300px;
        }
        
        .status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status.loading {
            background: #dbeafe;
            color: #1e40af;
        }
        
        .document-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            background: white;
        }
        
        .document-title {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 0.5rem;
        }
        
        .document-meta {
            font-size: 0.9rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }
        
        .document-content {
            color: #374151;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>🏛️ Federal Register Clone</h1>
            <p class="subtitle">Document Management System - API Demo</p>
        </header>

        <div class="demo-section">
            <h2>🚀 API Status</h2>
            <div class="api-test">
                <h3>Health Check</h3>
                <div class="api-url">GET http://localhost:8080/health</div>
                <button onclick="testHealthCheck()">Test Health Check</button>
                <div id="health-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📚 Documents API</h2>
            
            <div class="api-test">
                <h3>Get All Documents</h3>
                <div class="api-url">GET http://localhost:8080/api/v1/public/documents</div>
                <button onclick="getAllDocuments()">Get Documents</button>
                <button onclick="getAllDocuments(2)">Get Page 2</button>
                <div id="documents-result" class="result" style="display: none;"></div>
            </div>

            <div class="api-test">
                <h3>Get Single Document</h3>
                <div class="api-url">GET http://localhost:8080/api/v1/public/documents/{id}</div>
                <button onclick="getDocument(1)">Get Document #1</button>
                <button onclick="getDocument(2)">Get Document #2</button>
                <button onclick="getDocument(3)">Get Document #3</button>
                <div id="single-document-result" class="result" style="display: none;"></div>
            </div>

            <div class="api-test">
                <h3>Search Documents</h3>
                <div class="api-url">GET http://localhost:8080/api/v1/public/documents/search?q={query}</div>
                <div class="search-form">
                    <input type="text" id="search-query" placeholder="Enter search term..." value="environment">
                    <button onclick="searchDocuments()">Search</button>
                </div>
                <button onclick="searchDocuments('healthcare')">Search "healthcare"</button>
                <button onclick="searchDocuments('transportation')">Search "transportation"</button>
                <div id="search-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🏢 Agencies API</h2>
            <div class="api-test">
                <h3>Get All Agencies</h3>
                <div class="api-url">GET http://127.0.0.1:8080/api/v1/public/agencies</div>
                <button onclick="getAgencies()">Get Agencies</button>
                <div id="agencies-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🏷️ Categories API</h2>
            <div class="api-test">
                <h3>Get All Categories</h3>
                <div class="api-url">GET http://127.0.0.1:8080/api/v1/public/categories</div>
                <button onclick="getCategories()">Get Categories</button>
                <div id="categories-result" class="result" style="display: none;"></div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📊 Live Document Display</h2>
            <div id="documents-display"></div>
            <button onclick="displayDocuments()">Load & Display Documents</button>
        </div>
    </div>

    <script>
        const API_BASE = 'http://127.0.0.1:8080';

        async function makeRequest(url, elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = '<span class="status loading">Loading...</span>';

            try {
                const response = await fetch(url);
                const data = await response.json();
                
                if (response.ok) {
                    element.innerHTML = `
                        <span class="status success">✅ Success (${response.status})</span>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    element.innerHTML = `
                        <span class="status error">❌ Error (${response.status})</span>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                element.innerHTML = `
                    <span class="status error">❌ Network Error</span>
                    <pre>Error: ${error.message}</pre>
                `;
            }
        }

        function testHealthCheck() {
            makeRequest(`${API_BASE}/health`, 'health-result');
        }

        function getAllDocuments(page = 1) {
            makeRequest(`${API_BASE}/api/v1/public/documents?page=${page}`, 'documents-result');
        }

        function getDocument(id) {
            makeRequest(`${API_BASE}/api/v1/public/documents/${id}`, 'single-document-result');
        }

        function searchDocuments(query = null) {
            const searchQuery = query || document.getElementById('search-query').value;
            if (!searchQuery) {
                alert('Please enter a search term');
                return;
            }
            makeRequest(`${API_BASE}/api/v1/public/documents/search?q=${encodeURIComponent(searchQuery)}`, 'search-result');
        }

        function getAgencies() {
            makeRequest(`${API_BASE}/api/v1/public/agencies`, 'agencies-result');
        }

        function getCategories() {
            makeRequest(`${API_BASE}/api/v1/public/categories`, 'categories-result');
        }

        async function displayDocuments() {
            const displayElement = document.getElementById('documents-display');
            displayElement.innerHTML = '<span class="status loading">Loading documents...</span>';

            try {
                const response = await fetch(`${API_BASE}/api/v1/public/documents`);
                const data = await response.json();

                if (response.ok && data.data) {
                    let html = `<h3>📄 Documents (${data.total} total)</h3>`;
                    
                    data.data.forEach(doc => {
                        html += `
                            <div class="document-card">
                                <div class="document-title">${doc.title}</div>
                                <div class="document-meta">
                                    <strong>Type:</strong> ${doc.type} | 
                                    <strong>Status:</strong> ${doc.status} | 
                                    <strong>Agency:</strong> ${doc.agency} | 
                                    <strong>Views:</strong> ${doc.view_count}
                                </div>
                                <div class="document-content">${doc.content.substring(0, 200)}...</div>
                            </div>
                        `;
                    });

                    displayElement.innerHTML = html;
                } else {
                    displayElement.innerHTML = '<span class="status error">❌ Failed to load documents</span>';
                }
            } catch (error) {
                displayElement.innerHTML = `<span class="status error">❌ Error: ${error.message}</span>`;
            }
        }

        // Auto-test health check on page load
        window.onload = function() {
            testHealthCheck();
        };
    </script>
</body>
</html>
