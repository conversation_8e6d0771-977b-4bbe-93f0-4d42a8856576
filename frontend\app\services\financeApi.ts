import apiService from './api';
import {
  Finance,
  FinancePerformance,
  FinanceCategory,
  CreateFinanceRequest,
  UpdateFinanceRequest,
  CreatePerformanceRequest,
  CreateFinanceCategoryRequest,
  FinanceFilters,
  BudgetSummary,
  FinanceTableData,
  PaginatedResponse
} from '../types';

class FinanceApiService {
  private baseUrl = '/finance';

  // Finance CRUD operations
  async getFinances(params: FinanceFilters & { page?: number; per_page?: number } = {}): Promise<PaginatedResponse<Finance>> {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page.toString());
    if (params.per_page) queryParams.append('per_page', params.per_page.toString());
    if (params.year) queryParams.append('year', params.year.toString());
    if (params.document_id) queryParams.append('document_id', params.document_id.toString());
    if (params.regulation_id) queryParams.append('regulation_id', params.regulation_id.toString());
    if (params.budget_type) queryParams.append('budget_type', params.budget_type);
    if (params.category_id) queryParams.append('category_id', params.category_id.toString());

    const response = await apiService.get(`${this.baseUrl}?${queryParams.toString()}`);
    return response as PaginatedResponse<Finance>;
  }

  async getFinance(id: number): Promise<Finance> {
    const response = await apiService.get(`${this.baseUrl}/${id}`);
    return response as Finance;
  }

  async createFinance(data: CreateFinanceRequest): Promise<Finance> {
    const response = await apiService.post(this.baseUrl, data);
    return response as Finance;
  }

  async updateFinance(id: number, data: UpdateFinanceRequest): Promise<void> {
    await apiService.put(`${this.baseUrl}/${id}`, data);
  }

  async deleteFinance(id: number): Promise<void> {
    await apiService.delete(`${this.baseUrl}/${id}`);
  }

  // Performance management
  async createPerformance(data: CreatePerformanceRequest): Promise<FinancePerformance> {
    const response = await apiService.post(`${this.baseUrl}/performance`, data);
    return response as FinancePerformance;
  }

  async getPerformances(params: {
    year?: number;
    document_id?: number;
    regulation_id?: number;
  } = {}): Promise<FinancePerformance[]> {
    const queryParams = new URLSearchParams();

    if (params.year) queryParams.append('year', params.year.toString());
    if (params.document_id) queryParams.append('document_id', params.document_id.toString());
    if (params.regulation_id) queryParams.append('regulation_id', params.regulation_id.toString());

    const response = await apiService.get(`${this.baseUrl}/performance?${queryParams.toString()}`);
    return (response as any)?.data || [];
  }

  // Finance categories
  async getFinanceCategories(): Promise<FinanceCategory[]> {
    const response = await apiService.get(`${this.baseUrl}/categories`);
    return (response as any)?.data || [];
  }

  async createFinanceCategory(data: CreateFinanceCategoryRequest): Promise<FinanceCategory> {
    const response = await apiService.post(`${this.baseUrl}/categories`, data);
    return response as FinanceCategory;
  }

  // Budget summary and aggregation
  async getBudgetSummary(year: number): Promise<BudgetSummary> {
    const response = await apiService.get(`${this.baseUrl}/year/${year}/summary`);
    return response as BudgetSummary;
  }

  async getYearlyAggregate(year: number): Promise<{ year: number; total: number }> {
    const response = await apiService.get(`${this.baseUrl}/year/${year}/aggregate`);
    return response as { year: number; total: number };
  }

  // Helper methods for finance table data
  async getFinanceTableData(year: number): Promise<FinanceTableData[]> {
    const [originalFinances, actualFinances] = await Promise.all([
      this.getFinances({ year, budget_type: 'original', per_page: 1000 }),
      this.getFinances({ year, budget_type: 'actual', per_page: 1000 })
    ]);

    // Group by entity (document or regulation)
    const entityMap = new Map<string, FinanceTableData>();

    // Process original budgets
    originalFinances.data.forEach(finance => {
      const key = finance.document_id 
        ? `document-${finance.document_id}` 
        : `regulation-${finance.regulation_id}`;
      
      const title = finance.document?.title || finance.regulation?.title || 'Unknown';
      const type = finance.document_id ? 'document' : 'regulation';
      const entity_id = finance.document_id || finance.regulation_id || 0;

      if (!entityMap.has(key)) {
        entityMap.set(key, {
          id: finance.id,
          title,
          type: type as 'document' | 'regulation',
          entity_id,
          original_budget: 0,
          actual_budget: 0,
          performance_percentage: 100,
          category: finance.category?.name || 'Uncategorized',
          category_color: finance.category?.color || '#6B7280',
          year
        });
      }

      const entry = entityMap.get(key)!;
      entry.original_budget += finance.amount;
    });

    // Process actual budgets
    actualFinances.data.forEach(finance => {
      const key = finance.document_id 
        ? `document-${finance.document_id}` 
        : `regulation-${finance.regulation_id}`;

      if (entityMap.has(key)) {
        const entry = entityMap.get(key)!;
        entry.actual_budget += finance.amount;
        entry.performance_percentage = finance.performance_percentage;
      }
    });

    return Array.from(entityMap.values());
  }

  // Intelligent finance parsing using backend NLP service
  async parseFinanceFromText(text: string, sourceType: 'document' | 'regulation', sourceId: number): Promise<{
    suggested_finances: CreateFinanceRequest[];
    confidence: number;
  }> {
    try {
      // Use the backend's intelligent text parsing endpoint
      const response = await apiService.post<{
        data: {
          parsed_items: any[];
          costs: Array<{
            amount: number;
            description: string;
            context: string;
            currency: string;
          }>;
          confidence: number;
        };
      }>('/api/v1/tasks/parse-text', {
        text,
        source_type: sourceType,
        source_id: sourceId
      });

      const { costs, confidence } = response.data;

      // Convert extracted costs to finance suggestions
      const suggested_finances: CreateFinanceRequest[] = costs.map(cost => ({
        name: cost.description || 'Extracted Financial Item',
        description: `${cost.context} (Amount: ${cost.currency} ${cost.amount})`,
        amount: cost.amount,
        year: new Date().getFullYear(),
        currency: cost.currency,
        type: 'expense' as const,
        category: 'operational',
        fiscal_year: new Date().getFullYear(),
        quarter: Math.ceil((new Date().getMonth() + 1) / 3) as 1 | 2 | 3 | 4,
        source_type: sourceType,
        source_id: sourceId,
        status: 'draft' as const
      }));

      return {
        suggested_finances,
        confidence
      };
    } catch (error) {
      console.error('Error parsing finance from text:', error);
      return {
        suggested_finances: [],
        confidence: 0
      };
    }
  }
}

export default new FinanceApiService();
