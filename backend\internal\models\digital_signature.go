package models

import (
	"time"

	"gorm.io/gorm"
)

// SignatureType represents the type of digital signature
type SignatureType string

const (
	SignatureTypeSimple      SignatureType = "simple"       // Basic electronic signature (SES)
	SignatureTypeAdvanced    SignatureType = "advanced"     // Advanced electronic signature with certificate (AES)
	SignatureTypeQualified   SignatureType = "qualified"    // Qualified electronic signature (QES) - highest level
	SignatureTypeBiometric   SignatureType = "biometric"    // Biometric signature with fingerprint/face recognition
	SignatureTypeMultiFactor SignatureType = "multi_factor" // Multi-factor authentication signature
	SignatureTypeBlockchain  SignatureType = "blockchain"   // Blockchain-based signature
	SignatureTypeQuantumSafe SignatureType = "quantum_safe" // Post-quantum cryptography signature
)

// SignatureStatus represents the status of a signature
type SignatureStatus string

const (
	SignatureStatusPending   SignatureStatus = "pending"   // Waiting for signature
	SignatureStatusSigned    SignatureStatus = "signed"    // Successfully signed
	SignatureStatusRejected  SignatureStatus = "rejected"  // Signature rejected
	SignatureStatusExpired   SignatureStatus = "expired"   // Signature request expired
	SignatureStatusCancelled SignatureStatus = "cancelled" // Signature request cancelled
	SignatureStatusInvalid   SignatureStatus = "invalid"   // Signature validation failed
	SignatureStatusSuspended SignatureStatus = "suspended" // Signature suspended pending investigation
	SignatureStatusRevoked   SignatureStatus = "revoked"   // Signature revoked
	SignatureStatusArchived  SignatureStatus = "archived"  // Signature archived for long-term storage
)

// CertificateStatus represents the status of a digital certificate
type CertificateStatus string

const (
	CertificateStatusActive    CertificateStatus = "active"
	CertificateStatusExpired   CertificateStatus = "expired"
	CertificateStatusRevoked   CertificateStatus = "revoked"
	CertificateStatusSuspended CertificateStatus = "suspended"
	CertificateStatusPending   CertificateStatus = "pending"  // Certificate issuance pending
	CertificateStatusRenewing  CertificateStatus = "renewing" // Certificate renewal in progress
)

// BiometricType represents the type of biometric authentication
type BiometricType string

const (
	BiometricTypeFingerprint BiometricType = "fingerprint"
	BiometricTypeFaceID      BiometricType = "face_id"
	BiometricTypeVoice       BiometricType = "voice"
	BiometricTypeRetina      BiometricType = "retina"
	BiometricTypeHandwriting BiometricType = "handwriting"
	BiometricTypeBehavioral  BiometricType = "behavioral"
)

// HashAlgorithm represents cryptographic hash algorithms
type HashAlgorithm string

const (
	HashAlgorithmSHA256    HashAlgorithm = "sha256"
	HashAlgorithmSHA384    HashAlgorithm = "sha384"
	HashAlgorithmSHA512    HashAlgorithm = "sha512"
	HashAlgorithmSHA3_256  HashAlgorithm = "sha3_256"
	HashAlgorithmSHA3_512  HashAlgorithm = "sha3_512"
	HashAlgorithmBLAKE2b   HashAlgorithm = "blake2b"
	HashAlgorithmKeccak256 HashAlgorithm = "keccak256"
)

// EncryptionAlgorithm represents encryption algorithms
type EncryptionAlgorithm string

const (
	EncryptionAlgorithmRSA2048   EncryptionAlgorithm = "rsa_2048"
	EncryptionAlgorithmRSA4096   EncryptionAlgorithm = "rsa_4096"
	EncryptionAlgorithmECDSA256  EncryptionAlgorithm = "ecdsa_p256"
	EncryptionAlgorithmECDSA384  EncryptionAlgorithm = "ecdsa_p384"
	EncryptionAlgorithmECDSA521  EncryptionAlgorithm = "ecdsa_p521"
	EncryptionAlgorithmEd25519   EncryptionAlgorithm = "ed25519"
	EncryptionAlgorithmDilithium EncryptionAlgorithm = "dilithium" // Post-quantum
	EncryptionAlgorithmFalcon    EncryptionAlgorithm = "falcon"    // Post-quantum
	EncryptionAlgorithmSphincs   EncryptionAlgorithm = "sphincs"   // Post-quantum
)

// ComplianceStandard represents regulatory compliance standards
type ComplianceStandard string

const (
	ComplianceStandardEIDAS       ComplianceStandard = "eidas"        // EU eIDAS regulation
	ComplianceStandardPAdES       ComplianceStandard = "pades"        // PDF Advanced Electronic Signatures
	ComplianceStandardXAdES       ComplianceStandard = "xades"        // XML Advanced Electronic Signatures
	ComplianceStandardCAdES       ComplianceStandard = "cades"        // CMS Advanced Electronic Signatures
	ComplianceStandardFIPS140     ComplianceStandard = "fips_140"     // FIPS 140-2 compliance
	ComplianceStandardCommonCC    ComplianceStandard = "common_cc"    // Common Criteria
	ComplianceStandardSOX         ComplianceStandard = "sox"          // Sarbanes-Oxley Act
	ComplianceStandardHIPAA       ComplianceStandard = "hipaa"        // Health Insurance Portability and Accountability Act
	ComplianceStandardGDPR        ComplianceStandard = "gdpr"         // General Data Protection Regulation
	ComplianceStandardFISMA       ComplianceStandard = "fisma"        // Federal Information Security Management Act
	ComplianceStandardNIST        ComplianceStandard = "nist"         // NIST Cybersecurity Framework
	ComplianceStandardISO27001    ComplianceStandard = "iso_27001"    // ISO 27001 Information Security
	ComplianceStandardPCI_DSS     ComplianceStandard = "pci_dss"      // Payment Card Industry Data Security Standard
	ComplianceStandardFERPA       ComplianceStandard = "ferpa"        // Family Educational Rights and Privacy Act
	ComplianceStandardGLBA        ComplianceStandard = "glba"         // Gramm-Leach-Bliley Act
	ComplianceStandardCCPA        ComplianceStandard = "ccpa"         // California Consumer Privacy Act
	ComplianceStandardPIPEDA      ComplianceStandard = "pipeda"       // Personal Information Protection and Electronic Documents Act
	ComplianceStandardLGPD        ComplianceStandard = "lgpd"         // Lei Geral de Proteção de Dados (Brazil)
	ComplianceStandardAPPI        ComplianceStandard = "appi"         // Act on Protection of Personal Information (Japan)
	ComplianceStandardPDPA        ComplianceStandard = "pdpa"         // Personal Data Protection Act (Singapore/Thailand)
	ComplianceStandardCybersecAct ComplianceStandard = "cybersec_act" // Cybersecurity Act
	ComplianceStandardNIS2        ComplianceStandard = "nis2"         // Network and Information Security Directive 2
	ComplianceStandardDORA        ComplianceStandard = "dora"         // Digital Operational Resilience Act
	ComplianceStandardMiFID2      ComplianceStandard = "mifid2"       // Markets in Financial Instruments Directive II
	ComplianceStandardBaselIII    ComplianceStandard = "basel_iii"    // Basel III banking regulations
)

// TrustLevel represents the trust level of a signature or certificate
type TrustLevel string

const (
	TrustLevelNone     TrustLevel = "none"     // No trust established
	TrustLevelLow      TrustLevel = "low"      // Basic trust
	TrustLevelMedium   TrustLevel = "medium"   // Standard trust
	TrustLevelHigh     TrustLevel = "high"     // High trust
	TrustLevelCritical TrustLevel = "critical" // Critical/maximum trust
	TrustLevelAbsolute TrustLevel = "absolute" // Absolute trust (government/military)
)

// SecurityLevel represents the security classification level
type SecurityLevel string

const (
	SecurityLevelPublic       SecurityLevel = "public"        // Public information
	SecurityLevelInternal     SecurityLevel = "internal"      // Internal use only
	SecurityLevelConfidential SecurityLevel = "confidential"  // Confidential
	SecurityLevelRestricted   SecurityLevel = "restricted"    // Restricted access
	SecurityLevelSecret       SecurityLevel = "secret"        // Secret classification
	SecurityLevelTopSecret    SecurityLevel = "top_secret"    // Top secret classification
	SecurityLevelCosmicSecret SecurityLevel = "cosmic_secret" // NATO Cosmic Top Secret
)

// CertificateType represents different types of certificates
type CertificateType string

const (
	CertificateTypeSSL         CertificateType = "ssl"          // SSL/TLS certificates
	CertificateTypeCodeSigning CertificateType = "code_signing" // Code signing certificates
	CertificateTypeEmail       CertificateType = "email"        // S/MIME email certificates
	CertificateTypeClient      CertificateType = "client"       // Client authentication
	CertificateTypeServer      CertificateType = "server"       // Server authentication
	CertificateTypeCA          CertificateType = "ca"           // Certificate Authority
	CertificateTypeOCSP        CertificateType = "ocsp"         // OCSP responder
	CertificateTypeTimestamp   CertificateType = "timestamp"    // Timestamp authority
	CertificateTypeVPN         CertificateType = "vpn"          // VPN certificates
	CertificateTypeWiFi        CertificateType = "wifi"         // WiFi certificates
	CertificateTypeIoT         CertificateType = "iot"          // IoT device certificates
	CertificateTypeMobile      CertificateType = "mobile"       // Mobile device certificates
	CertificateTypeSmartCard   CertificateType = "smart_card"   // Smart card certificates
	CertificateTypeDocument    CertificateType = "document"     // Document signing
	CertificateTypeIdentity    CertificateType = "identity"     // Identity certificates
	CertificateTypeAttribute   CertificateType = "attribute"    // Attribute certificates
)

// ValidationLevel represents the level of validation performed
type ValidationLevel string

const (
	ValidationLevelNone          ValidationLevel = "none"          // No validation
	ValidationLevelBasic         ValidationLevel = "basic"         // Basic validation
	ValidationLevelStandard      ValidationLevel = "standard"      // Standard validation
	ValidationLevelExtended      ValidationLevel = "extended"      // Extended validation
	ValidationLevelComprehensive ValidationLevel = "comprehensive" // Comprehensive validation
	ValidationLevelForensic      ValidationLevel = "forensic"      // Forensic-level validation
)

// CryptographicStrength represents the cryptographic strength level
type CryptographicStrength string

const (
	CryptoStrengthWeak        CryptographicStrength = "weak"         // < 80 bits
	CryptoStrengthLow         CryptographicStrength = "low"          // 80-111 bits
	CryptoStrengthMedium      CryptographicStrength = "medium"       // 112-127 bits
	CryptoStrengthHigh        CryptographicStrength = "high"         // 128-191 bits
	CryptoStrengthVeryHigh    CryptographicStrength = "very_high"    // 192-255 bits
	CryptoStrengthExtreme     CryptographicStrength = "extreme"      // 256+ bits
	CryptoStrengthQuantumSafe CryptographicStrength = "quantum_safe" // Post-quantum safe
)

// DigitalSignature represents a digital signature on a document
type DigitalSignature struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Signature identification
	SignatureID string          `json:"signature_id" gorm:"uniqueIndex;not null"` // Unique signature identifier
	Type        SignatureType   `json:"type" gorm:"not null"`
	Status      SignatureStatus `json:"status" gorm:"default:'pending'"`

	// Document relationship
	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	// Signer information
	SignerID    uint   `json:"signer_id" gorm:"not null"`
	Signer      User   `json:"signer" gorm:"foreignKey:SignerID"`
	SignerName  string `json:"signer_name" gorm:"not null"`
	SignerEmail string `json:"signer_email" gorm:"not null"`
	SignerTitle string `json:"signer_title"`

	// Signature data
	SignatureData     string `json:"signature_data" gorm:"type:text"` // Base64 encoded signature
	SignatureHash     string `json:"signature_hash" gorm:"not null"`  // Hash of the signature
	DocumentHash      string `json:"document_hash" gorm:"not null"`   // Hash of document at time of signing
	SignatureMethod   string `json:"signature_method"`                // Method used for signing
	SignatureLocation string `json:"signature_location"`              // Geographic location of signing

	// Advanced cryptographic information
	HashAlgorithm       HashAlgorithm       `json:"hash_algorithm" gorm:"default:'sha256'"`
	EncryptionAlgorithm EncryptionAlgorithm `json:"encryption_algorithm" gorm:"default:'rsa_2048'"`
	KeySize             int                 `json:"key_size" gorm:"default:2048"`
	SignatureAlgorithm  string              `json:"signature_algorithm"` // e.g., "RSA-PSS", "ECDSA"

	// Biometric authentication data
	BiometricType      *BiometricType `json:"biometric_type,omitempty"`
	BiometricData      string         `json:"biometric_data,omitempty" gorm:"type:text"` // Encrypted biometric template
	BiometricScore     *float64       `json:"biometric_score,omitempty"`                 // Confidence score (0-1)
	BiometricThreshold *float64       `json:"biometric_threshold,omitempty"`             // Required threshold for acceptance
	LivenessDetection  bool           `json:"liveness_detection" gorm:"default:false"`   // Anti-spoofing detection

	// Multi-factor authentication
	MFARequired         bool   `json:"mfa_required" gorm:"default:false"`
	MFAMethods          string `json:"mfa_methods" gorm:"type:text"` // JSON array of MFA methods used
	SMSVerification     string `json:"sms_verification"`             // SMS verification code (hashed)
	EmailVerification   string `json:"email_verification"`           // Email verification token (hashed)
	TOTPVerification    string `json:"totp_verification"`            // TOTP verification code (hashed)
	HardwareTokenSerial string `json:"hardware_token_serial"`        // Hardware token serial number

	// Certificate information
	CertificateID     *uint               `json:"certificate_id"`
	Certificate       *DigitalCertificate `json:"certificate,omitempty" gorm:"foreignKey:CertificateID"`
	CertificateSerial string              `json:"certificate_serial"`
	CertificateIssuer string              `json:"certificate_issuer"`
	CertificateChain  string              `json:"certificate_chain" gorm:"type:text"` // Full certificate chain
	CertificatePolicy string              `json:"certificate_policy"`                 // Certificate policy OID

	// Timestamping and OCSP
	TimestampToken string     `json:"timestamp_token" gorm:"type:text"` // RFC 3161 timestamp token
	TimestampURL   string     `json:"timestamp_url"`                    // Timestamp authority URL
	TimestampHash  string     `json:"timestamp_hash"`                   // Hash of timestamp
	OCSPResponse   string     `json:"ocsp_response" gorm:"type:text"`   // OCSP response
	OCSPUrl        string     `json:"ocsp_url"`                         // OCSP responder URL
	CRLUrl         string     `json:"crl_url"`                          // Certificate Revocation List URL
	CRLNextUpdate  *time.Time `json:"crl_next_update"`                  // Next CRL update time

	// Timing information
	RequestedAt time.Time  `json:"requested_at"`
	SignedAt    *time.Time `json:"signed_at"`
	ExpiresAt   *time.Time `json:"expires_at"`
	ValidFrom   *time.Time `json:"valid_from"`
	ValidUntil  *time.Time `json:"valid_until"`

	// Workflow information
	WorkflowStepID  *uint `json:"workflow_step_id"` // Associated workflow step
	SigningOrder    int   `json:"signing_order"`    // Order in multi-signature workflow
	IsRequired      bool  `json:"is_required" gorm:"default:true"`
	RequiresWitness bool  `json:"requires_witness" gorm:"default:false"`

	// Validation and verification
	IsValid          bool       `json:"is_valid" gorm:"default:false"`
	ValidationStatus string     `json:"validation_status"`
	ValidatedAt      *time.Time `json:"validated_at"`
	ValidatedByID    *uint      `json:"validated_by_id"`
	ValidatedBy      *User      `json:"validated_by,omitempty" gorm:"foreignKey:ValidatedByID"`

	// Audit trail
	IPAddress     string `json:"ip_address"`
	UserAgent     string `json:"user_agent"`
	DeviceInfo    string `json:"device_info"`
	GeoLocation   string `json:"geo_location"`
	RequestedByID uint   `json:"requested_by_id" gorm:"not null"`
	RequestedBy   User   `json:"requested_by" gorm:"foreignKey:RequestedByID"`

	// Compliance and regulatory
	ComplianceStandards string `json:"compliance_standards" gorm:"type:text"`  // JSON array of compliance standards
	RegulatoryLevel     string `json:"regulatory_level"`                       // "low", "medium", "high", "critical"
	LegalFramework      string `json:"legal_framework"`                        // Legal framework (e.g., "EU", "US", "UK")
	RetentionPeriod     int    `json:"retention_period" gorm:"default:7"`      // Years to retain signature
	ArchivalRequired    bool   `json:"archival_required" gorm:"default:false"` // Long-term archival required

	// Blockchain and distributed ledger
	BlockchainTxHash  string  `json:"blockchain_tx_hash"`     // Blockchain transaction hash
	BlockchainNetwork string  `json:"blockchain_network"`     // Blockchain network (e.g., "ethereum", "hyperledger")
	SmartContractAddr string  `json:"smart_contract_address"` // Smart contract address
	BlockNumber       *uint64 `json:"block_number"`           // Block number where signature is recorded
	MerkleRoot        string  `json:"merkle_root"`            // Merkle tree root hash

	// Advanced security features
	AntiTamperSeal    string `json:"anti_tamper_seal"`                    // Anti-tamper seal hash
	IntegrityChecksum string `json:"integrity_checksum"`                  // Document integrity checksum
	WatermarkData     string `json:"watermark_data" gorm:"type:text"`     // Digital watermark data
	SteganographyData string `json:"steganography_data" gorm:"type:text"` // Hidden steganographic data

	// Witness and notarization
	WitnessRequired  bool       `json:"witness_required" gorm:"default:false"` // Requires witness
	WitnessID        *uint      `json:"witness_id"`                            // Witness user ID
	Witness          *User      `json:"witness,omitempty" gorm:"foreignKey:WitnessID"`
	NotaryRequired   bool       `json:"notary_required" gorm:"default:false"` // Requires notarization
	NotaryID         *uint      `json:"notary_id"`                            // Notary user ID
	Notary           *User      `json:"notary,omitempty" gorm:"foreignKey:NotaryID"`
	NotaryCommission string     `json:"notary_commission"` // Notary commission number
	NotaryExpiration *time.Time `json:"notary_expiration"` // Notary commission expiration

	// Risk assessment and fraud detection
	RiskScore          *float64 `json:"risk_score"`                           // Risk assessment score (0-1)
	FraudIndicators    string   `json:"fraud_indicators" gorm:"type:text"`    // JSON array of fraud indicators
	BehavioralAnalysis string   `json:"behavioral_analysis" gorm:"type:text"` // Behavioral biometric analysis
	DeviceFingerprint  string   `json:"device_fingerprint"`                   // Unique device fingerprint
	NetworkFingerprint string   `json:"network_fingerprint"`                  // Network connection fingerprint

	// Additional metadata
	Reason     string   `json:"reason"`                         // Reason for signature
	Notes      string   `json:"notes" gorm:"type:text"`         // Additional notes
	Metadata   string   `json:"metadata" gorm:"type:text"`      // JSON metadata
	IsVisible  bool     `json:"is_visible" gorm:"default:true"` // Whether signature is visible on document
	PageNumber *int     `json:"page_number"`                    // Page where signature appears
	XPosition  *float64 `json:"x_position"`                     // X coordinate on page
	YPosition  *float64 `json:"y_position"`                     // Y coordinate on page
}

// DigitalCertificate represents a digital certificate for signing
type DigitalCertificate struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Certificate identification
	SerialNumber string            `json:"serial_number" gorm:"uniqueIndex;not null"`
	Thumbprint   string            `json:"thumbprint" gorm:"uniqueIndex;not null"`
	Status       CertificateStatus `json:"status" gorm:"default:'active'"`

	// Certificate owner
	OwnerID uint `json:"owner_id" gorm:"not null"`
	Owner   User `json:"owner" gorm:"foreignKey:OwnerID"`

	// Certificate details
	Subject            string `json:"subject" gorm:"not null"`
	Issuer             string `json:"issuer" gorm:"not null"`
	CommonName         string `json:"common_name" gorm:"not null"`
	Organization       string `json:"organization"`
	OrganizationalUnit string `json:"organizational_unit"`
	Country            string `json:"country"`
	State              string `json:"state"`
	Locality           string `json:"locality"`
	EmailAddress       string `json:"email_address"`

	// Certificate validity
	NotBefore        time.Time  `json:"not_before"`
	NotAfter         time.Time  `json:"not_after"`
	IssuedAt         time.Time  `json:"issued_at"`
	RevokedAt        *time.Time `json:"revoked_at"`
	RevokedByID      *uint      `json:"revoked_by_id"`
	RevokedBy        *User      `json:"revoked_by,omitempty" gorm:"foreignKey:RevokedByID"`
	RevocationReason string     `json:"revocation_reason"`

	// Certificate data
	PublicKey        string `json:"public_key" gorm:"type:text;not null"`
	PrivateKey       string `json:"private_key" gorm:"type:text"`      // Encrypted private key
	CertificateData  string `json:"certificate_data" gorm:"type:text"` // PEM encoded certificate
	KeyUsage         string `json:"key_usage"`                         // Certificate key usage
	ExtendedKeyUsage string `json:"extended_key_usage"`                // Extended key usage

	// Certificate authority information
	CAIssued         bool                `json:"ca_issued" gorm:"default:false"`
	CACertificateID  *uint               `json:"ca_certificate_id"`
	CACertificate    *DigitalCertificate `json:"ca_certificate,omitempty" gorm:"foreignKey:CACertificateID"`
	CertificateChain string              `json:"certificate_chain" gorm:"type:text"`

	// Advanced certificate features
	KeyAlgorithm       EncryptionAlgorithm `json:"key_algorithm" gorm:"default:'rsa_2048'"`
	KeyLength          int                 `json:"key_length" gorm:"default:2048"`
	SignatureAlgorithm string              `json:"signature_algorithm"`  // Certificate signature algorithm
	PublicKeyAlgorithm string              `json:"public_key_algorithm"` // Public key algorithm
	CurveType          string              `json:"curve_type"`           // Elliptic curve type (for ECDSA)

	// Certificate extensions
	SubjectAltNames     string `json:"subject_alt_names" gorm:"type:text"`    // Subject Alternative Names
	AuthorityKeyID      string `json:"authority_key_id"`                      // Authority Key Identifier
	SubjectKeyID        string `json:"subject_key_id"`                        // Subject Key Identifier
	BasicConstraints    string `json:"basic_constraints"`                     // Basic constraints extension
	CertificatePolicies string `json:"certificate_policies" gorm:"type:text"` // Certificate policies

	// OCSP and CRL information
	OCSPUrls            string `json:"ocsp_urls" gorm:"type:text"`             // OCSP responder URLs (JSON array)
	CRLDistributionUrls string `json:"crl_distribution_urls" gorm:"type:text"` // CRL distribution points
	OCSPMustStaple      bool   `json:"ocsp_must_staple" gorm:"default:false"`  // OCSP Must-Staple extension

	// Hardware Security Module (HSM) support
	HSMBacked        bool   `json:"hsm_backed" gorm:"default:false"` // Certificate backed by HSM
	HSMSlotID        string `json:"hsm_slot_id"`                     // HSM slot identifier
	HSMKeyLabel      string `json:"hsm_key_label"`                   // HSM key label
	HSMProvider      string `json:"hsm_provider"`                    // HSM provider name
	PKCS11TokenLabel string `json:"pkcs11_token_label"`              // PKCS#11 token label

	// Certificate transparency and monitoring
	CTLogEntries      string `json:"ct_log_entries" gorm:"type:text"`         // Certificate Transparency log entries
	CTPreCertificate  string `json:"ct_pre_certificate" gorm:"type:text"`     // CT pre-certificate
	MonitoringEnabled bool   `json:"monitoring_enabled" gorm:"default:false"` // Certificate monitoring enabled
	ExpirationAlerts  bool   `json:"expiration_alerts" gorm:"default:true"`   // Send expiration alerts

	// Post-quantum cryptography
	QuantumSafe      bool   `json:"quantum_safe" gorm:"default:false"` // Post-quantum cryptography
	HybridAlgorithm  string `json:"hybrid_algorithm"`                  // Hybrid classical/quantum algorithm
	QuantumAlgorithm string `json:"quantum_algorithm"`                 // Post-quantum algorithm used

	// Usage tracking
	SignatureCount int        `json:"signature_count" gorm:"default:0"`
	LastUsedAt     *time.Time `json:"last_used_at"`

	// Compliance and audit
	ComplianceLevel   ComplianceStandard `json:"compliance_level"`                        // Compliance standard
	AuditTrailEnabled bool               `json:"audit_trail_enabled" gorm:"default:true"` // Enable audit trail
	FIPSCompliant     bool               `json:"fips_compliant" gorm:"default:false"`     // FIPS 140-2 compliant
	CommonCriteriaEAL int                `json:"common_criteria_eal" gorm:"default:0"`    // Common Criteria EAL level

	// Additional metadata
	Purpose   string `json:"purpose"` // Purpose of certificate
	Notes     string `json:"notes" gorm:"type:text"`
	IsDefault bool   `json:"is_default" gorm:"default:false"` // Default certificate for user
	IsActive  bool   `json:"is_active" gorm:"default:true"`
}

// SignatureWorkflow represents a multi-step signature workflow
type SignatureWorkflow struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Workflow identification
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`

	// Document relationship
	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	// Workflow status
	Status         string     `json:"status" gorm:"default:'pending'"`
	CurrentStep    int        `json:"current_step" gorm:"default:1"`
	TotalSteps     int        `json:"total_steps" gorm:"not null"`
	CompletedSteps int        `json:"completed_steps" gorm:"default:0"`
	StartedAt      *time.Time `json:"started_at"`
	CompletedAt    *time.Time `json:"completed_at"`
	ExpiresAt      *time.Time `json:"expires_at"`

	// Workflow configuration
	RequireSequential bool `json:"require_sequential" gorm:"default:true"`  // Must sign in order
	AllowParallel     bool `json:"allow_parallel" gorm:"default:false"`     // Allow parallel signing
	RequireAllSigners bool `json:"require_all_signers" gorm:"default:true"` // All signers must sign

	// Creator information
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Relationships
	Signatures []DigitalSignature `json:"signatures,omitempty" gorm:"foreignKey:WorkflowStepID"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// SignatureAuditLog represents audit trail for signature operations
type SignatureAuditLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Related entities
	SignatureID *uint             `json:"signature_id"`
	Signature   *DigitalSignature `json:"signature,omitempty" gorm:"foreignKey:SignatureID"`
	DocumentID  uint              `json:"document_id" gorm:"not null"`
	Document    Document          `json:"document" gorm:"foreignKey:DocumentID"`

	// Audit information
	Action      string `json:"action" gorm:"not null"` // "created", "signed", "rejected", "validated", etc.
	Description string `json:"description" gorm:"type:text"`
	UserID      uint   `json:"user_id" gorm:"not null"`
	User        User   `json:"user" gorm:"foreignKey:UserID"`

	// Technical details
	IPAddress   string `json:"ip_address"`
	UserAgent   string `json:"user_agent"`
	DeviceInfo  string `json:"device_info"`
	GeoLocation string `json:"geo_location"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TimestampAuthority represents a trusted timestamp authority
type TimestampAuthority struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Authority identification
	Name        string `json:"name" gorm:"not null"`
	URL         string `json:"url" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`

	// Authority configuration
	Protocol      string        `json:"protocol" gorm:"default:'rfc3161'"` // RFC 3161, etc.
	HashAlgorithm HashAlgorithm `json:"hash_algorithm" gorm:"default:'sha256'"`
	RequiresAuth  bool          `json:"requires_auth" gorm:"default:false"`
	Username      string        `json:"username"`
	Password      string        `json:"password"` // Encrypted

	// Certificate information
	CertificateID *uint               `json:"certificate_id"`
	Certificate   *DigitalCertificate `json:"certificate,omitempty" gorm:"foreignKey:CertificateID"`
	TrustedRoot   string              `json:"trusted_root" gorm:"type:text"` // Trusted root certificate

	// Status and monitoring
	IsActive     bool       `json:"is_active" gorm:"default:true"`
	LastChecked  *time.Time `json:"last_checked"`
	ResponseTime *int       `json:"response_time"` // Average response time in ms
	SuccessRate  *float64   `json:"success_rate"`  // Success rate (0-1)

	// Usage statistics
	RequestCount int        `json:"request_count" gorm:"default:0"`
	LastUsed     *time.Time `json:"last_used"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// OCSPResponder represents an OCSP responder configuration
type OCSPResponder struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Responder identification
	Name        string `json:"name" gorm:"not null"`
	URL         string `json:"url" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`

	// Responder configuration
	Protocol     string `json:"protocol" gorm:"default:'ocsp'"`
	RequiresAuth bool   `json:"requires_auth" gorm:"default:false"`
	Username     string `json:"username"`
	Password     string `json:"password"` // Encrypted

	// Certificate authority
	CAIssuerID    uint               `json:"ca_issuer_id" gorm:"not null"`
	CAIssuer      DigitalCertificate `json:"ca_issuer" gorm:"foreignKey:CAIssuerID"`
	ResponderCert string             `json:"responder_cert" gorm:"type:text"` // OCSP responder certificate

	// Status and monitoring
	IsActive     bool       `json:"is_active" gorm:"default:true"`
	LastChecked  *time.Time `json:"last_checked"`
	ResponseTime *int       `json:"response_time"` // Average response time in ms
	SuccessRate  *float64   `json:"success_rate"`  // Success rate (0-1)

	// Usage statistics
	RequestCount int        `json:"request_count" gorm:"default:0"`
	LastUsed     *time.Time `json:"last_used"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// BiometricTemplate represents stored biometric templates
type BiometricTemplate struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Template identification
	TemplateID    string        `json:"template_id" gorm:"uniqueIndex;not null"`
	UserID        uint          `json:"user_id" gorm:"not null"`
	User          User          `json:"user" gorm:"foreignKey:UserID"`
	BiometricType BiometricType `json:"biometric_type" gorm:"not null"`

	// Template data
	TemplateData string  `json:"template_data" gorm:"type:text;not null"` // Encrypted biometric template
	TemplateHash string  `json:"template_hash" gorm:"not null"`           // Hash of template
	Quality      float64 `json:"quality"`                                 // Template quality score (0-1)

	// Enrollment information
	EnrollmentDate time.Time `json:"enrollment_date"`
	DeviceInfo     string    `json:"device_info"` // Device used for enrollment
	SensorInfo     string    `json:"sensor_info"` // Biometric sensor information

	// Security and privacy
	EncryptionKey string     `json:"encryption_key"`                // Key used for template encryption
	AccessCount   int        `json:"access_count" gorm:"default:0"` // Number of times accessed
	LastAccessed  *time.Time `json:"last_accessed"`

	// Template lifecycle
	ExpiresAt *time.Time `json:"expires_at"`
	IsActive  bool       `json:"is_active" gorm:"default:true"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// SignaturePolicy represents signature policies and rules
type SignaturePolicy struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Policy identification
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`
	Version     string `json:"version" gorm:"default:'1.0'"`

	// Policy rules
	RequiredSignatureType SignatureType `json:"required_signature_type" gorm:"default:'advanced'"`
	MinimumKeySize        int           `json:"minimum_key_size" gorm:"default:2048"`
	AllowedHashAlgorithms string        `json:"allowed_hash_algorithms" gorm:"type:text"` // JSON array
	AllowedEncryptionAlgs string        `json:"allowed_encryption_algs" gorm:"type:text"` // JSON array
	RequiredCompliance    string        `json:"required_compliance" gorm:"type:text"`     // JSON array

	// Biometric requirements
	RequireBiometric      bool    `json:"require_biometric" gorm:"default:false"`
	AllowedBiometricTypes string  `json:"allowed_biometric_types" gorm:"type:text"` // JSON array
	MinBiometricScore     float64 `json:"min_biometric_score" gorm:"default:0.8"`
	RequireLivenessCheck  bool    `json:"require_liveness_check" gorm:"default:false"`

	// Multi-factor authentication
	RequireMFA         bool   `json:"require_mfa" gorm:"default:false"`
	RequiredMFAMethods string `json:"required_mfa_methods" gorm:"type:text"` // JSON array

	// Certificate requirements
	RequireCertificate bool   `json:"require_certificate" gorm:"default:true"`
	AllowSelfSigned    bool   `json:"allow_self_signed" gorm:"default:false"`
	RequireCAIssued    bool   `json:"require_ca_issued" gorm:"default:true"`
	TrustedCAs         string `json:"trusted_cas" gorm:"type:text"` // JSON array of CA IDs

	// Timestamping requirements
	RequireTimestamp bool   `json:"require_timestamp" gorm:"default:false"`
	TrustedTSAs      string `json:"trusted_tsas" gorm:"type:text"` // JSON array of TSA IDs

	// Validation requirements
	RequireOCSP bool `json:"require_ocsp" gorm:"default:false"`
	RequireCRL  bool `json:"require_crl" gorm:"default:false"`
	MaxCertAge  int  `json:"max_cert_age" gorm:"default:365"` // Days

	// Geographic restrictions
	AllowedCountries string `json:"allowed_countries" gorm:"type:text"` // JSON array
	BlockedCountries string `json:"blocked_countries" gorm:"type:text"` // JSON array

	// Workflow requirements
	RequireWitness        bool `json:"require_witness" gorm:"default:false"`
	RequireNotary         bool `json:"require_notary" gorm:"default:false"`
	RequireSequentialSign bool `json:"require_sequential_sign" gorm:"default:false"`

	// Retention and archival
	RetentionPeriod int  `json:"retention_period" gorm:"default:7"` // Years
	RequireArchival bool `json:"require_archival" gorm:"default:false"`

	// Status and lifecycle
	IsActive       bool       `json:"is_active" gorm:"default:true"`
	EffectiveFrom  time.Time  `json:"effective_from"`
	EffectiveUntil *time.Time `json:"effective_until"`

	// Creator information
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// CertificateAuthority represents a certificate authority configuration
type CertificateAuthority struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// CA identification
	Name         string `json:"name" gorm:"not null"`
	CommonName   string `json:"common_name" gorm:"not null"`
	Organization string `json:"organization"`
	Country      string `json:"country"`
	Description  string `json:"description" gorm:"type:text"`

	// CA hierarchy
	IsRootCA   bool                  `json:"is_root_ca" gorm:"default:false"`
	ParentCAID *uint                 `json:"parent_ca_id"`
	ParentCA   *CertificateAuthority `json:"parent_ca,omitempty" gorm:"foreignKey:ParentCAID"`
	Level      int                   `json:"level" gorm:"default:0"` // 0=root, 1=intermediate, etc.

	// CA certificate
	CertificateID   uint               `json:"certificate_id" gorm:"not null"`
	Certificate     DigitalCertificate `json:"certificate" gorm:"foreignKey:CertificateID"`
	CertificateData string             `json:"certificate_data" gorm:"type:text"` // PEM encoded CA certificate
	PrivateKeyData  string             `json:"private_key_data" gorm:"type:text"` // Encrypted private key

	// CA configuration
	KeyAlgorithm   EncryptionAlgorithm `json:"key_algorithm" gorm:"default:'rsa_2048'"`
	KeySize        int                 `json:"key_size" gorm:"default:2048"`
	HashAlgorithm  HashAlgorithm       `json:"hash_algorithm" gorm:"default:'sha256'"`
	ValidityPeriod int                 `json:"validity_period" gorm:"default:3650"` // Days

	// CA services
	OCSPEnabled     bool   `json:"ocsp_enabled" gorm:"default:true"`
	OCSPUrl         string `json:"ocsp_url"`
	CRLEnabled      bool   `json:"crl_enabled" gorm:"default:true"`
	CRLUrl          string `json:"crl_url"`
	CRLUpdatePeriod int    `json:"crl_update_period" gorm:"default:24"` // Hours

	// CA policies
	CertificatePolicy string `json:"certificate_policy" gorm:"type:text"` // JSON policy document
	IssuancePolicy    string `json:"issuance_policy" gorm:"type:text"`    // JSON issuance rules

	// Status and monitoring
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	IsTrusted       bool       `json:"is_trusted" gorm:"default:false"`
	LastHealthCheck *time.Time `json:"last_health_check"`
	HealthStatus    string     `json:"health_status" gorm:"default:'unknown'"`

	// Usage statistics
	IssuedCertCount  int        `json:"issued_cert_count" gorm:"default:0"`
	RevokedCertCount int        `json:"revoked_cert_count" gorm:"default:0"`
	LastIssuance     *time.Time `json:"last_issuance"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for DigitalSignature model
func (DigitalSignature) TableName() string {
	return "digital_signatures"
}

// TableName returns the table name for DigitalCertificate model
func (DigitalCertificate) TableName() string {
	return "digital_certificates"
}

// TableName returns the table name for SignatureWorkflow model
func (SignatureWorkflow) TableName() string {
	return "signature_workflows"
}

// TableName returns the table name for SignatureAuditLog model
func (SignatureAuditLog) TableName() string {
	return "signature_audit_logs"
}

// TableName returns the table name for TimestampAuthority model
func (TimestampAuthority) TableName() string {
	return "timestamp_authorities"
}

// TableName returns the table name for OCSPResponder model
func (OCSPResponder) TableName() string {
	return "ocsp_responders"
}

// TableName returns the table name for BiometricTemplate model
func (BiometricTemplate) TableName() string {
	return "biometric_templates"
}

// TableName returns the table name for SignaturePolicy model
func (SignaturePolicy) TableName() string {
	return "signature_policies"
}

// HSMConfiguration represents Hardware Security Module configuration
type HSMConfiguration struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// HSM identification
	Name         string `json:"name" gorm:"not null"`
	Vendor       string `json:"vendor"` // e.g., "SafeNet", "Thales", "Utimaco"
	Model        string `json:"model"`  // HSM model
	SerialNumber string `json:"serial_number" gorm:"uniqueIndex"`
	Description  string `json:"description" gorm:"type:text"`

	// HSM connection
	ConnectionType string `json:"connection_type" gorm:"default:'network'"` // "network", "usb", "pcie"
	IPAddress      string `json:"ip_address"`
	Port           int    `json:"port" gorm:"default:1792"`

	// PKCS#11 configuration
	PKCS11Library string `json:"pkcs11_library"` // Path to PKCS#11 library
	SlotID        int    `json:"slot_id"`
	TokenLabel    string `json:"token_label"`
	PIN           string `json:"pin"` // Encrypted PIN

	// HSM capabilities
	SupportedAlgorithms string `json:"supported_algorithms" gorm:"type:text"` // JSON array
	MaxKeySize          int    `json:"max_key_size" gorm:"default:4096"`
	FIPS140Level        int    `json:"fips_140_level" gorm:"default:2"`      // FIPS 140-2 level
	CommonCriteriaEAL   int    `json:"common_criteria_eal" gorm:"default:4"` // Common Criteria EAL

	// Status and monitoring
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	LastHealthCheck *time.Time `json:"last_health_check"`
	HealthStatus    string     `json:"health_status" gorm:"default:'unknown'"`

	// Usage statistics
	KeyCount       int        `json:"key_count" gorm:"default:0"`
	OperationCount int        `json:"operation_count" gorm:"default:0"`
	LastUsed       *time.Time `json:"last_used"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// SignatureValidation represents signature validation results
type SignatureValidation struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Related signature
	SignatureID uint             `json:"signature_id" gorm:"not null"`
	Signature   DigitalSignature `json:"signature" gorm:"foreignKey:SignatureID"`

	// Validation details
	ValidationTime   time.Time `json:"validation_time"`
	ValidatorID      uint      `json:"validator_id" gorm:"not null"`
	Validator        User      `json:"validator" gorm:"foreignKey:ValidatorID"`
	ValidationMethod string    `json:"validation_method"` // "automatic", "manual", "hybrid"

	// Validation results
	IsValid          bool    `json:"is_valid"`
	ValidationScore  float64 `json:"validation_score"`  // Confidence score (0-1)
	ValidationStatus string  `json:"validation_status"` // "valid", "invalid", "warning", "unknown"

	// Certificate validation
	CertificateValid  bool   `json:"certificate_valid"`
	CertificateStatus string `json:"certificate_status"`
	OCSPStatus        string `json:"ocsp_status"` // "good", "revoked", "unknown"
	CRLStatus         string `json:"crl_status"`  // "good", "revoked", "unknown"

	// Timestamp validation
	TimestampValid  bool   `json:"timestamp_valid"`
	TimestampStatus string `json:"timestamp_status"`
	ClockSkew       *int   `json:"clock_skew"` // Seconds

	// Cryptographic validation
	SignatureIntact bool `json:"signature_intact"`
	DocumentIntact  bool `json:"document_intact"`
	HashMatch       bool `json:"hash_match"`

	// Biometric validation (if applicable)
	BiometricValid   *bool    `json:"biometric_valid,omitempty"`
	BiometricScore   *float64 `json:"biometric_score,omitempty"`
	LivenessDetected *bool    `json:"liveness_detected,omitempty"`

	// Policy compliance
	PolicyCompliant  bool   `json:"policy_compliant"`
	PolicyViolations string `json:"policy_violations" gorm:"type:text"` // JSON array

	// Validation errors and warnings
	Errors   string `json:"errors" gorm:"type:text"`   // JSON array
	Warnings string `json:"warnings" gorm:"type:text"` // JSON array

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// SignatureArchive represents long-term signature archival
type SignatureArchive struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Related signature
	SignatureID uint             `json:"signature_id" gorm:"not null"`
	Signature   DigitalSignature `json:"signature" gorm:"foreignKey:SignatureID"`

	// Archive details
	ArchiveDate     time.Time  `json:"archive_date"`
	ArchiveReason   string     `json:"archive_reason"`   // "retention_policy", "legal_hold", "manual"
	RetentionPeriod int        `json:"retention_period"` // Years
	ExpirationDate  *time.Time `json:"expiration_date"`

	// Archive format and location
	ArchiveFormat   string `json:"archive_format" gorm:"default:'asice'"` // "asice", "pdf", "xml", "json"
	StorageLocation string `json:"storage_location"`                      // Storage path or URL
	StorageProvider string `json:"storage_provider"`                      // "local", "s3", "azure", "gcp"
	EncryptionKey   string `json:"encryption_key"`                        // Archive encryption key

	// Archive integrity
	ArchiveHash        string     `json:"archive_hash"`                      // Hash of archived data
	IntegrityChecks    int        `json:"integrity_checks" gorm:"default:0"` // Number of integrity checks performed
	LastIntegrityCheck *time.Time `json:"last_integrity_check"`
	IntegrityStatus    string     `json:"integrity_status" gorm:"default:'unknown'"`

	// Legal and compliance
	LegalHold          bool   `json:"legal_hold" gorm:"default:false"`
	ComplianceStandard string `json:"compliance_standard"`
	AuditTrail         string `json:"audit_trail" gorm:"type:text"` // JSON audit trail

	// Access control
	AccessCount  int        `json:"access_count" gorm:"default:0"`
	LastAccessed *time.Time `json:"last_accessed"`
	AccessLog    string     `json:"access_log" gorm:"type:text"` // JSON access log

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// CryptographicOracle represents an advanced cryptographic oracle system
type CryptographicOracle struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Oracle identification
	Name        string `json:"name" gorm:"not null"`
	OracleType  string `json:"oracle_type"` // "entropy", "random", "quantum", "hardware"
	Provider    string `json:"provider"`    // Oracle provider
	Version     string `json:"version"`
	Description string `json:"description" gorm:"type:text"`

	// Oracle configuration
	EndpointURL string `json:"endpoint_url"`
	APIKey      string `json:"api_key"` // Encrypted API key
	Protocol    string `json:"protocol" gorm:"default:'https'"`
	Timeout     int    `json:"timeout" gorm:"default:30"` // Seconds

	// Cryptographic capabilities
	EntropySource   string `json:"entropy_source"`   // "quantum", "thermal", "atmospheric"
	RandomnessLevel string `json:"randomness_level"` // "true_random", "pseudo_random", "hybrid"
	BiasCorrection  bool   `json:"bias_correction" gorm:"default:true"`

	// Quality metrics
	EntropyRate  float64 `json:"entropy_rate"`                        // Bits per second
	QualityScore float64 `json:"quality_score"`                       // 0-1 quality rating
	FIPS140Tests bool    `json:"fips_140_tests" gorm:"default:false"` // Passes FIPS 140 tests
	NISTTests    bool    `json:"nist_tests" gorm:"default:false"`     // Passes NIST randomness tests

	// Status and monitoring
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	LastHealthCheck *time.Time `json:"last_health_check"`
	HealthStatus    string     `json:"health_status" gorm:"default:'unknown'"`

	// Usage statistics
	RequestCount   int        `json:"request_count" gorm:"default:0"`
	BytesGenerated uint64     `json:"bytes_generated" gorm:"default:0"`
	LastUsed       *time.Time `json:"last_used"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// QuantumKeyDistribution represents quantum key distribution systems
type QuantumKeyDistribution struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// QKD identification
	Name         string `json:"name" gorm:"not null"`
	Protocol     string `json:"protocol"` // "BB84", "E91", "SARG04", "COW"
	Provider     string `json:"provider"` // QKD system provider
	Model        string `json:"model"`
	SerialNumber string `json:"serial_number" gorm:"uniqueIndex"`

	// QKD configuration
	TransmitterID string  `json:"transmitter_id"`
	ReceiverID    string  `json:"receiver_id"`
	ChannelType   string  `json:"channel_type"` // "fiber", "free_space", "satellite"
	Distance      float64 `json:"distance"`     // Kilometers
	Wavelength    float64 `json:"wavelength"`   // Nanometers

	// Security parameters
	KeyRate              float64 `json:"key_rate"`       // Keys per second
	QBER                 float64 `json:"qber"`           // Quantum Bit Error Rate
	SecurityLevel        int     `json:"security_level"` // Security parameter
	PrivacyAmplification bool    `json:"privacy_amplification" gorm:"default:true"`

	// Key management
	KeyLength      int `json:"key_length" gorm:"default:256"`      // Bits
	KeyLifetime    int `json:"key_lifetime" gorm:"default:3600"`   // Seconds
	KeyRefreshRate int `json:"key_refresh_rate" gorm:"default:60"` // Seconds

	// Status and monitoring
	IsActive   bool       `json:"is_active" gorm:"default:true"`
	LastSync   *time.Time `json:"last_sync"`
	SyncStatus string     `json:"sync_status" gorm:"default:'unknown'"`

	// Usage statistics
	KeysGenerated uint64     `json:"keys_generated" gorm:"default:0"`
	KeysConsumed  uint64     `json:"keys_consumed" gorm:"default:0"`
	LastKeyGen    *time.Time `json:"last_key_gen"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// HomomorphicEncryption represents homomorphic encryption configurations
type HomomorphicEncryption struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// HE identification
	Name    string `json:"name" gorm:"not null"`
	Scheme  string `json:"scheme"`  // "BFV", "BGV", "CKKS", "TFHE", "FHEW"
	Library string `json:"library"` // "SEAL", "HElib", "PALISADE", "Lattigo"
	Version string `json:"version"`

	// Cryptographic parameters
	SecurityLevel int    `json:"security_level" gorm:"default:128"`   // Bits
	PolyModDegree int    `json:"poly_mod_degree" gorm:"default:8192"` // Polynomial modulus degree
	CoeffModulus  string `json:"coeff_modulus" gorm:"type:text"`      // Coefficient modulus chain
	PlainModulus  uint64 `json:"plain_modulus"`                       // Plaintext modulus

	// Performance parameters
	NoiseLevel float64 `json:"noise_level"`                 // Current noise level
	MaxDepth   int     `json:"max_depth"`                   // Maximum circuit depth
	Precision  int     `json:"precision" gorm:"default:40"` // Decimal precision

	// Key management
	PublicKey  string `json:"public_key" gorm:"type:text"`  // Serialized public key
	SecretKey  string `json:"secret_key" gorm:"type:text"`  // Encrypted secret key
	RelinKeys  string `json:"relin_keys" gorm:"type:text"`  // Relinearization keys
	GaloisKeys string `json:"galois_keys" gorm:"type:text"` // Galois keys

	// Usage tracking
	OperationCount int        `json:"operation_count" gorm:"default:0"`
	LastOperation  *time.Time `json:"last_operation"`

	// Status
	IsActive bool `json:"is_active" gorm:"default:true"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ZeroKnowledgeProof represents zero-knowledge proof systems
type ZeroKnowledgeProof struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// ZKP identification
	Name        string `json:"name" gorm:"not null"`
	ProofSystem string `json:"proof_system"` // "zk-SNARKs", "zk-STARKs", "Bulletproofs", "Plonk"
	Curve       string `json:"curve"`        // "BN254", "BLS12-381", "secp256k1"
	Library     string `json:"library"`      // "libsnark", "bellman", "arkworks"

	// Circuit parameters
	CircuitSize   int    `json:"circuit_size"`   // Number of constraints
	PublicInputs  int    `json:"public_inputs"`  // Number of public inputs
	PrivateInputs int    `json:"private_inputs"` // Number of private inputs
	CircuitHash   string `json:"circuit_hash"`   // Hash of circuit definition

	// Cryptographic parameters
	SecurityLevel  int     `json:"security_level" gorm:"default:128"`  // Bits
	SoundnessError float64 `json:"soundness_error"`                    // Soundness error probability
	ZeroKnowledge  bool    `json:"zero_knowledge" gorm:"default:true"` // Perfect zero-knowledge

	// Performance metrics
	ProofSize  int `json:"proof_size"`  // Bytes
	ProofTime  int `json:"proof_time"`  // Milliseconds
	VerifyTime int `json:"verify_time"` // Milliseconds
	SetupTime  int `json:"setup_time"`  // Milliseconds

	// Keys and parameters
	ProvingKey      string `json:"proving_key" gorm:"type:text"`       // Serialized proving key
	VerifyingKey    string `json:"verifying_key" gorm:"type:text"`     // Serialized verifying key
	CommonRefString string `json:"common_ref_string" gorm:"type:text"` // Common reference string

	// Usage tracking
	ProofCount  int        `json:"proof_count" gorm:"default:0"`
	VerifyCount int        `json:"verify_count" gorm:"default:0"`
	LastProof   *time.Time `json:"last_proof"`

	// Status
	IsActive bool `json:"is_active" gorm:"default:true"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for CertificateAuthority model
func (CertificateAuthority) TableName() string {
	return "certificate_authorities"
}

// TableName returns the table name for HSMConfiguration model
func (HSMConfiguration) TableName() string {
	return "hsm_configurations"
}

// TableName returns the table name for SignatureValidation model
func (SignatureValidation) TableName() string {
	return "signature_validations"
}

// SecureMultiPartyComputation represents secure multi-party computation configurations
type SecureMultiPartyComputation struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// SMPC identification
	Name       string `json:"name" gorm:"not null"`
	Protocol   string `json:"protocol"`                     // "BGW", "GMW", "SPDZ", "Shamir"
	Library    string `json:"library"`                      // "MP-SPDZ", "SCALE-MAMBA", "ABY"
	PartyCount int    `json:"party_count" gorm:"default:3"` // Number of parties
	Threshold  int    `json:"threshold" gorm:"default:2"`   // Threshold for secret sharing

	// Security parameters
	SecurityLevel  int `json:"security_level" gorm:"default:128"` // Bits
	FieldSize      int `json:"field_size" gorm:"default:256"`     // Field size in bits
	StatisticalSec int `json:"statistical_sec" gorm:"default:40"` // Statistical security parameter

	// Network configuration
	PartyEndpoints  string `json:"party_endpoints" gorm:"type:text"`       // JSON array of party endpoints
	NetworkTopology string `json:"network_topology" gorm:"default:'star'"` // "star", "mesh", "ring"

	// Performance metrics
	RoundComplexity int    `json:"round_complexity"` // Communication rounds
	Bandwidth       uint64 `json:"bandwidth"`        // Bytes per computation
	ComputeTime     int    `json:"compute_time"`     // Milliseconds

	// Circuit description
	CircuitFile string `json:"circuit_file"` // Path to circuit file
	CircuitHash string `json:"circuit_hash"` // Hash of circuit
	GateCount   int    `json:"gate_count"`   // Number of gates

	// Status and monitoring
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	LastComputation *time.Time `json:"last_computation"`

	// Usage statistics
	ComputationCount int    `json:"computation_count" gorm:"default:0"`
	TotalRuntime     uint64 `json:"total_runtime" gorm:"default:0"` // Total milliseconds

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TrustedExecutionEnvironment represents TEE configurations
type TrustedExecutionEnvironment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// TEE identification
	Name    string `json:"name" gorm:"not null"`
	TEEType string `json:"tee_type"` // "SGX", "TrustZone", "SEV", "Keystone"
	Vendor  string `json:"vendor"`   // "Intel", "ARM", "AMD", "RISC-V"
	Version string `json:"version"`

	// Hardware configuration
	CPUModel     string `json:"cpu_model"`
	EnclaveSize  uint64 `json:"enclave_size"`  // Bytes
	SecureMemory uint64 `json:"secure_memory"` // Bytes

	// Security features
	RemoteAttestation bool `json:"remote_attestation" gorm:"default:true"`
	SealedStorage     bool `json:"sealed_storage" gorm:"default:true"`
	SecureBoot        bool `json:"secure_boot" gorm:"default:true"`
	MemoryEncryption  bool `json:"memory_encryption" gorm:"default:true"`

	// Attestation configuration
	AttestationURL   string `json:"attestation_url"`
	QuotingEnclave   string `json:"quoting_enclave"`
	ProvisioningCert string `json:"provisioning_cert" gorm:"type:text"`

	// Performance metrics
	EnclaveStartTime int     `json:"enclave_start_time"` // Milliseconds
	AttestationTime  int     `json:"attestation_time"`   // Milliseconds
	ThroughputMBps   float64 `json:"throughput_mbps"`    // MB/s

	// Status and monitoring
	IsActive          bool       `json:"is_active" gorm:"default:true"`
	LastAttestation   *time.Time `json:"last_attestation"`
	AttestationStatus string     `json:"attestation_status" gorm:"default:'unknown'"`

	// Usage statistics
	EnclaveCount   int        `json:"enclave_count" gorm:"default:0"`
	OperationCount int        `json:"operation_count" gorm:"default:0"`
	LastOperation  *time.Time `json:"last_operation"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// DistributedLedgerTechnology represents DLT configurations beyond blockchain
type DistributedLedgerTechnology struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// DLT identification
	Name       string `json:"name" gorm:"not null"`
	LedgerType string `json:"ledger_type"` // "DAG", "Hashgraph", "Tangle", "Blockchain"
	Protocol   string `json:"protocol"`    // "IOTA", "Hedera", "Nano", "Avalanche"
	Network    string `json:"network"`     // "mainnet", "testnet", "private"

	// Network configuration
	NodeEndpoints string `json:"node_endpoints" gorm:"type:text"` // JSON array of node endpoints
	ConsensusAlgo string `json:"consensus_algo"`                  // "PoW", "PoS", "DPoS", "pBFT", "Gossip"
	NetworkID     string `json:"network_id"`

	// Performance characteristics
	TPS             float64 `json:"tps"`      // Transactions per second
	Finality        int     `json:"finality"` // Seconds to finality
	EnergyEfficient bool    `json:"energy_efficient" gorm:"default:false"`

	// Security parameters
	SecurityModel  string `json:"security_model"`  // "Probabilistic", "Deterministic"
	FaultTolerance string `json:"fault_tolerance"` // "1/3", "1/2", "2/3"

	// Integration configuration
	APIEndpoint   string `json:"api_endpoint"`
	APIKey        string `json:"api_key"` // Encrypted
	WalletAddress string `json:"wallet_address"`
	PrivateKey    string `json:"private_key"` // Encrypted

	// Status and monitoring
	IsActive   bool       `json:"is_active" gorm:"default:true"`
	LastSync   *time.Time `json:"last_sync"`
	SyncStatus string     `json:"sync_status" gorm:"default:'unknown'"`

	// Usage statistics
	TransactionCount uint64     `json:"transaction_count" gorm:"default:0"`
	LastTransaction  *time.Time `json:"last_transaction"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// AdvancedThreatProtection represents ATP configurations for signature security
type AdvancedThreatProtection struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// ATP identification
	Name       string `json:"name" gorm:"not null"`
	EngineType string `json:"engine_type"` // "ML", "AI", "Rule-based", "Hybrid"
	Provider   string `json:"provider"`    // "CrowdStrike", "SentinelOne", "Custom"
	Version    string `json:"version"`

	// Detection capabilities
	MalwareDetection   bool `json:"malware_detection" gorm:"default:true"`
	PhishingDetection  bool `json:"phishing_detection" gorm:"default:true"`
	AnomalyDetection   bool `json:"anomaly_detection" gorm:"default:true"`
	BehavioralAnalysis bool `json:"behavioral_analysis" gorm:"default:true"`

	// Machine learning configuration
	ModelPath           string  `json:"model_path"`
	ModelVersion        string  `json:"model_version"`
	ConfidenceThreshold float64 `json:"confidence_threshold" gorm:"default:0.8"`
	TrainingDataSize    uint64  `json:"training_data_size"`

	// Real-time protection
	RealTimeScanning  bool `json:"real_time_scanning" gorm:"default:true"`
	QuarantineEnabled bool `json:"quarantine_enabled" gorm:"default:true"`
	AutoRemediation   bool `json:"auto_remediation" gorm:"default:false"`

	// Threat intelligence
	ThreatFeedURL    string     `json:"threat_feed_url"`
	ThreatFeedAPIKey string     `json:"threat_feed_api_key"` // Encrypted
	LastThreatUpdate *time.Time `json:"last_threat_update"`
	ThreatSignatures uint64     `json:"threat_signatures" gorm:"default:0"`

	// Performance metrics
	ScanTime          int     `json:"scan_time"` // Milliseconds
	FalsePositiveRate float64 `json:"false_positive_rate"`
	DetectionRate     float64 `json:"detection_rate"`

	// Status and monitoring
	IsActive bool       `json:"is_active" gorm:"default:true"`
	LastScan *time.Time `json:"last_scan"`

	// Usage statistics
	ThreatsDetected uint64 `json:"threats_detected" gorm:"default:0"`
	ThreatsBlocked  uint64 `json:"threats_blocked" gorm:"default:0"`
	ScansPerformed  uint64 `json:"scans_performed" gorm:"default:0"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// QuantumResistantCryptography represents post-quantum cryptographic configurations
type QuantumResistantCryptography struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// QRC identification
	Name            string `json:"name" gorm:"not null"`
	AlgorithmFamily string `json:"algorithm_family"`            // "Lattice", "Code", "Multivariate", "Hash", "Isogeny"
	Algorithm       string `json:"algorithm"`                   // "Kyber", "Dilithium", "SPHINCS+", "BIKE", "Rainbow"
	NISTLevel       int    `json:"nist_level" gorm:"default:1"` // NIST security level 1-5

	// Cryptographic parameters
	SecurityLevel   int `json:"security_level" gorm:"default:128"`   // Classical security level
	QuantumSecurity int `json:"quantum_security" gorm:"default:128"` // Quantum security level
	KeySize         int `json:"key_size"`                            // Key size in bytes
	SignatureSize   int `json:"signature_size"`                      // Signature size in bytes

	// Performance characteristics
	KeyGenTime int `json:"key_gen_time"` // Microseconds
	SignTime   int `json:"sign_time"`    // Microseconds
	VerifyTime int `json:"verify_time"`  // Microseconds

	// Implementation details
	Library        string `json:"library"`                        // "liboqs", "PQClean", "SUPERCOP"
	Implementation string `json:"implementation"`                 // "ref", "avx2", "aarch64"
	Optimizations  string `json:"optimizations" gorm:"type:text"` // JSON array of optimizations

	// Standardization status
	NISTStatus   string     `json:"nist_status"` // "candidate", "finalist", "standardized"
	RFC          string     `json:"rfc"`         // RFC number if standardized
	StandardDate *time.Time `json:"standard_date"`

	// Hybrid configuration
	HybridMode    bool   `json:"hybrid_mode" gorm:"default:false"` // Hybrid with classical crypto
	ClassicalAlgo string `json:"classical_algo"`                   // Classical algorithm for hybrid

	// Status and monitoring
	IsActive      bool       `json:"is_active" gorm:"default:true"`
	LastBenchmark *time.Time `json:"last_benchmark"`

	// Usage statistics
	OperationCount uint64     `json:"operation_count" gorm:"default:0"`
	LastOperation  *time.Time `json:"last_operation"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// FederatedIdentityManagement represents federated identity configurations
type FederatedIdentityManagement struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// FIM identification
	Name     string `json:"name" gorm:"not null"`
	Protocol string `json:"protocol"` // "SAML", "OAuth2", "OpenID", "WS-Fed"
	Provider string `json:"provider"` // "Azure AD", "Okta", "Auth0", "Keycloak"
	Version  string `json:"version"`

	// Identity provider configuration
	IdPEntityID    string `json:"idp_entity_id"`
	IdPSSOURL      string `json:"idp_sso_url"`
	IdPSLOURL      string `json:"idp_slo_url"`
	IdPCertificate string `json:"idp_certificate" gorm:"type:text"`

	// Service provider configuration
	SPEntityID     string `json:"sp_entity_id"`
	SPAssertionURL string `json:"sp_assertion_url"`
	SPCertificate  string `json:"sp_certificate" gorm:"type:text"`
	SPPrivateKey   string `json:"sp_private_key" gorm:"type:text"` // Encrypted

	// Attribute mapping
	AttributeMap string `json:"attribute_map" gorm:"type:text"` // JSON attribute mapping
	NameIDFormat string `json:"name_id_format"`

	// Security configuration
	SignRequests            bool `json:"sign_requests" gorm:"default:true"`
	EncryptAssertions       bool `json:"encrypt_assertions" gorm:"default:false"`
	RequireSignedAssertions bool `json:"require_signed_assertions" gorm:"default:true"`

	// Session management
	SessionTimeout int  `json:"session_timeout" gorm:"default:3600"` // Seconds
	SlidingExpiry  bool `json:"sliding_expiry" gorm:"default:true"`

	// Status and monitoring
	IsActive bool       `json:"is_active" gorm:"default:true"`
	LastSync *time.Time `json:"last_sync"`

	// Usage statistics
	LoginCount uint64     `json:"login_count" gorm:"default:0"`
	LastLogin  *time.Time `json:"last_login"`

	// Additional metadata
	Notes    string `json:"notes" gorm:"type:text"`
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for SignatureArchive model
func (SignatureArchive) TableName() string {
	return "signature_archives"
}

// TableName returns the table name for CryptographicOracle model
func (CryptographicOracle) TableName() string {
	return "cryptographic_oracles"
}

// TableName returns the table name for QuantumKeyDistribution model
func (QuantumKeyDistribution) TableName() string {
	return "quantum_key_distributions"
}

// TableName returns the table name for HomomorphicEncryption model
func (HomomorphicEncryption) TableName() string {
	return "homomorphic_encryptions"
}

// TableName returns the table name for ZeroKnowledgeProof model
func (ZeroKnowledgeProof) TableName() string {
	return "zero_knowledge_proofs"
}

// TableName returns the table name for SecureMultiPartyComputation model
func (SecureMultiPartyComputation) TableName() string {
	return "secure_multiparty_computations"
}

// TableName returns the table name for TrustedExecutionEnvironment model
func (TrustedExecutionEnvironment) TableName() string {
	return "trusted_execution_environments"
}

// TableName returns the table name for DistributedLedgerTechnology model
func (DistributedLedgerTechnology) TableName() string {
	return "distributed_ledger_technologies"
}

// TableName returns the table name for AdvancedThreatProtection model
func (AdvancedThreatProtection) TableName() string {
	return "advanced_threat_protections"
}

// TableName returns the table name for QuantumResistantCryptography model
func (QuantumResistantCryptography) TableName() string {
	return "quantum_resistant_cryptographies"
}

// TableName returns the table name for FederatedIdentityManagement model
func (FederatedIdentityManagement) TableName() string {
	return "federated_identity_managements"
}
