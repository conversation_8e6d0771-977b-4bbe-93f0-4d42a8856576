package handlers

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// AutoGenerationConfigRequest represents the request structure for auto-generation configuration
type AutoGenerationConfigRequest struct {
	EntityType                   string `json:"entity_type" binding:"required"` // "document", "regulation", "proceeding"
	EnableSummaryGeneration      bool   `json:"enable_summary_generation"`
	EnableTaskGeneration         bool   `json:"enable_task_generation"`
	EnableCalendarGeneration     bool   `json:"enable_calendar_generation"`
	EnableFinanceGeneration      bool   `json:"enable_finance_generation"`
	EnableRelationshipGeneration bool   `json:"enable_relationship_generation"`
	UserID                       uint   `json:"user_id"`
}

// GetAutoGenerationConfigs returns all auto-generation configurations
func GetAutoGenerationConfigs(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get all configurations
	var configs []models.AutoGenerationConfig
	if err := db.Order("entity_type ASC").Find(&configs).Error; err != nil {
		HandleInternalError(c, "Failed to fetch auto-generation configs: "+err.Error())
		return
	}

	// Convert to response format
	configResponses := make([]gin.H, len(configs))
	for i, config := range configs {
		configResponses[i] = gin.H{
			"id":                             config.ID,
			"entity_type":                    config.EntityType,
			"user_id":                        config.UserID,
			"enable_summary_generation":      config.EnableSummaryGeneration,
			"enable_task_generation":         config.EnableTaskGeneration,
			"enable_calendar_generation":     config.EnableCalendarGeneration,
			"enable_finance_generation":      config.EnableFinanceGeneration,
			"enable_relationship_generation": config.EnableRelationshipGeneration,
			"created_at":                     config.CreatedAt,
			"updated_at":                     config.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Auto-generation configurations retrieved successfully",
		Data:    configResponses,
	})
}

// UpdateAutoGenerationConfigs updates auto-generation configurations
func UpdateAutoGenerationConfigs(c *gin.Context) {
	var req []AutoGenerationConfigRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Process each configuration
	for _, configReq := range req {
		// Find existing configuration or create new one
		var config models.AutoGenerationConfig
		err := db.Where("entity_type = ?", configReq.EntityType).First(&config).Error

		if err != nil {
			// Create new configuration
			config = models.AutoGenerationConfig{
				UserID:                       configReq.UserID,
				EntityType:                   configReq.EntityType,
				EnableSummaryGeneration:      configReq.EnableSummaryGeneration,
				EnableTaskGeneration:         configReq.EnableTaskGeneration,
				EnableCalendarGeneration:     configReq.EnableCalendarGeneration,
				EnableFinanceGeneration:      configReq.EnableFinanceGeneration,
				EnableRelationshipGeneration: configReq.EnableRelationshipGeneration,
			}

			if err := db.Create(&config).Error; err != nil {
				HandleInternalError(c, "Failed to create auto-generation config: "+err.Error())
				return
			}
		} else {
			// Update existing configuration
			config.EnableSummaryGeneration = configReq.EnableSummaryGeneration
			config.EnableTaskGeneration = configReq.EnableTaskGeneration
			config.EnableCalendarGeneration = configReq.EnableCalendarGeneration
			config.EnableFinanceGeneration = configReq.EnableFinanceGeneration
			config.EnableRelationshipGeneration = configReq.EnableRelationshipGeneration

			if err := db.Save(&config).Error; err != nil {
				HandleInternalError(c, "Failed to update auto-generation config: "+err.Error())
				return
			}
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Auto-generation configurations updated successfully",
	})
}

// TriggerAutoGeneration manually triggers auto-generation for a specific entity
func TriggerAutoGeneration(c *gin.Context) {
	var req TriggerAutoGenerationRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get configuration for entity type, or create default if not exists
	var config models.AutoGenerationConfig
	err := db.Where("entity_type = ?", req.EntityType).First(&config).Error
	if err != nil {
		// Create default configuration if none exists
		config = models.AutoGenerationConfig{
			UserID:                       userID.(uint),
			EntityType:                   req.EntityType,
			EnableSummaryGeneration:      true,
			EnableTaskGeneration:         true,
			EnableCalendarGeneration:     true,
			EnableFinanceGeneration:      true,
			EnableRelationshipGeneration: true,
		}

		if err := db.Create(&config).Error; err != nil {
			HandleInternalError(c, "Failed to create default auto-generation config: "+err.Error())
			return
		}
	}

	// Create system event for auto-generation trigger
	event := &models.SystemEvent{
		EventType: "auto_generation_triggered",
		EntityID:  &req.EntityID,
		UserID:    userID.(*uint), // Get from authenticated user
		Data:      "Manual trigger for " + req.EntityType,
		Processed: false,
	}

	if err := db.Create(event).Error; err != nil {
		HandleInternalError(c, "Failed to create auto-generation event: "+err.Error())
		return
	}

	// Implement actual auto-generation logic based on entity type
	var generatedContent interface{}
	var generationError error

	switch req.EntityType {
	case "document":
		generatedContent, generationError = generateDocumentContent(db, req.EntityID, config)
	case "regulation":
		generatedContent, generationError = generateRegulationContent(db, req.EntityID, config)
	case "summary":
		generatedContent, generationError = generateSummaryContent(db, req.EntityID, config)
	case "comment":
		generatedContent, generationError = generateCommentContent(db, req.EntityID, config)
	default:
		generationError = fmt.Errorf("unsupported entity type: %s", req.EntityType)
	}

	// Update event with results
	if generationError != nil {
		event.Data = "Generation failed: " + generationError.Error()
		event.Processed = false
	} else {
		event.Data = "Generation completed successfully"
		event.Processed = true
	}

	if err := db.Save(event).Error; err != nil {
		HandleInternalError(c, "Failed to update auto-generation event: "+err.Error())
		return
	}

	// Return results
	responseData := gin.H{
		"event_id":    event.ID,
		"entity_type": req.EntityType,
		"entity_id":   req.EntityID,
		"processed":   event.Processed,
	}

	if generationError != nil {
		responseData["error"] = generationError.Error()
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Auto-generation failed",
			Message: generationError.Error(),
		})
		return
	}

	if generatedContent != nil {
		responseData["generated_content"] = generatedContent
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Auto-generation triggered successfully",
		Data: gin.H{
			"event_id":   event.ID,
			"processed":  event.Processed,
			"created_at": event.CreatedAt,
		},
	})
}

// TriggerAutoGenerationRequest represents the request structure for triggering auto-generation
type TriggerAutoGenerationRequest struct {
	EntityType string `json:"entity_type" binding:"required"`
	EntityID   uint   `json:"entity_id" binding:"required"`
}

// GetAutoGenerationStats returns statistics about auto-generation
func GetAutoGenerationStats(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get statistics from system events
	var totalEvents int64
	var processedEvents int64
	var errorEvents int64

	db.Model(&models.SystemEvent{}).Where("event_type = ?", "auto_generation_triggered").Count(&totalEvents)
	db.Model(&models.SystemEvent{}).Where("event_type = ? AND processed = ?", "auto_generation_triggered", true).Count(&processedEvents)
	db.Model(&models.SystemEvent{}).Where("event_type = ? AND error != ''", "auto_generation_triggered").Count(&errorEvents)

	// Get recent activity
	var recentEvents []models.SystemEvent
	db.Where("event_type = ?", "auto_generation_triggered").Order("created_at DESC").Limit(10).Find(&recentEvents)

	successRate := float64(0)
	if totalEvents > 0 {
		successRate = float64(processedEvents) / float64(totalEvents) * 100
	}

	stats := gin.H{
		"total_generations":     totalEvents,
		"completed_generations": processedEvents,
		"failed_generations":    errorEvents,
		"success_rate":          successRate,
		"recent_activity":       recentEvents,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Auto-generation statistics retrieved successfully",
		Data:    stats,
	})
}

// Helper functions for auto-generation logic

// generateDocumentContent generates content for documents
func generateDocumentContent(db *gorm.DB, entityID uint, config models.AutoGenerationConfig) (interface{}, error) {
	var document models.Document
	if err := db.First(&document, entityID).Error; err != nil {
		return nil, fmt.Errorf("document not found: %v", err)
	}

	// Generate content based on configuration
	generatedContent := gin.H{
		"title":        "Auto-generated: " + document.Title,
		"summary":      generateSummaryFromContent(document.Content),
		"keywords":     extractKeywords(document.Content),
		"category":     suggestCategory(document.Content),
		"generated_at": time.Now(),
	}

	return generatedContent, nil
}

// generateRegulationContent generates content for regulations
func generateRegulationContent(db *gorm.DB, entityID uint, config models.AutoGenerationConfig) (interface{}, error) {
	var regulation models.LawsAndRules
	if err := db.First(&regulation, entityID).Error; err != nil {
		return nil, fmt.Errorf("regulation not found: %v", err)
	}

	// Generate regulation-specific content
	generatedContent := gin.H{
		"title":           "Auto-generated: " + regulation.Title,
		"abstract":        generateAbstract(regulation.Content),
		"compliance_note": generateComplianceNote(regulation.Content),
		"effective_date":  suggestEffectiveDate(),
		"generated_at":    time.Now(),
	}

	return generatedContent, nil
}

// generateSummaryContent generates summary content
func generateSummaryContent(db *gorm.DB, entityID uint, config models.AutoGenerationConfig) (interface{}, error) {
	// This could be for any entity that needs a summary
	generatedContent := gin.H{
		"summary_type": "auto_generated",
		"key_points":   []string{"Point 1", "Point 2", "Point 3"},
		"word_count":   150,
		"reading_time": "2 minutes",
		"generated_at": time.Now(),
	}

	return generatedContent, nil
}

// generateCommentContent generates comment content
func generateCommentContent(db *gorm.DB, entityID uint, config models.AutoGenerationConfig) (interface{}, error) {
	generatedContent := gin.H{
		"comment_type": "auto_generated",
		"sentiment":    "neutral",
		"topics":       []string{"policy", "implementation", "impact"},
		"generated_at": time.Now(),
	}

	return generatedContent, nil
}

// Helper utility functions

func generateSummaryFromContent(content string) string {
	// Enhanced summary generation using extractive summarization
	if content == "" {
		return ""
	}

	// Initialize NLP service for advanced processing
	nlpService := services.NewNLPService()

	// Use extractive summarization for better summaries
	summary := nlpService.GenerateExtractiveSummary(content, 200)

	// If advanced summarization fails, fallback to improved basic summarization
	if summary == "" {
		summary = generateSummaryBasic(content)
	}

	return summary
}

// generateSummaryBasic provides improved basic summarization as fallback
func generateSummaryBasic(content string) string {
	if len(content) <= 200 {
		return content
	}

	// Split into sentences
	sentences := strings.Split(content, ".")
	if len(sentences) == 0 {
		return content[:200] + "..."
	}

	// Score sentences based on importance indicators
	sentenceScores := make(map[int]int)

	// Important keywords that indicate key sentences
	importantKeywords := []string{
		"require", "shall", "must", "prohibit", "establish", "implement",
		"effective", "deadline", "penalty", "violation", "compliance",
		"purpose", "objective", "goal", "intent", "scope", "apply",
		"definition", "means", "include", "exclude", "exception",
		"agency", "department", "commission", "administration",
		"public", "comment", "notice", "hearing", "proceeding",
	}

	for i, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) < 20 {
			continue
		}

		score := 0
		lowerSentence := strings.ToLower(sentence)

		// Score based on important keywords
		for _, keyword := range importantKeywords {
			if strings.Contains(lowerSentence, keyword) {
				score += 2
			}
		}

		// Prefer sentences at the beginning (often contain key information)
		if i < 3 {
			score += 3
		}

		// Prefer sentences with numbers (often contain specific requirements)
		if regexp.MustCompile(`\d+`).MatchString(sentence) {
			score += 1
		}

		// Prefer longer sentences (more likely to contain complete thoughts)
		if len(sentence) > 100 {
			score += 1
		}

		sentenceScores[i] = score
	}

	// Select top sentences for summary
	type sentenceScore struct {
		index int
		score int
		text  string
	}

	var scoredSentences []sentenceScore
	for i, score := range sentenceScores {
		if score > 0 && i < len(sentences) {
			scoredSentences = append(scoredSentences, sentenceScore{
				index: i,
				score: score,
				text:  strings.TrimSpace(sentences[i]),
			})
		}
	}

	// Sort by score (descending)
	for i := 0; i < len(scoredSentences)-1; i++ {
		for j := i + 1; j < len(scoredSentences); j++ {
			if scoredSentences[i].score < scoredSentences[j].score {
				scoredSentences[i], scoredSentences[j] = scoredSentences[j], scoredSentences[i]
			}
		}
	}

	// Build summary from top sentences, maintaining order
	var selectedSentences []sentenceScore
	summaryLength := 0
	maxLength := 200

	for _, ss := range scoredSentences {
		if summaryLength+len(ss.text) <= maxLength {
			selectedSentences = append(selectedSentences, ss)
			summaryLength += len(ss.text)
		}
		if summaryLength >= int(float64(maxLength)*0.8) { // Stop when we reach 80% of max length
			break
		}
	}

	// Sort selected sentences by original order
	for i := 0; i < len(selectedSentences)-1; i++ {
		for j := i + 1; j < len(selectedSentences); j++ {
			if selectedSentences[i].index > selectedSentences[j].index {
				selectedSentences[i], selectedSentences[j] = selectedSentences[j], selectedSentences[i]
			}
		}
	}

	// Build final summary
	var summaryParts []string
	for _, ss := range selectedSentences {
		summaryParts = append(summaryParts, ss.text)
	}

	summary := strings.Join(summaryParts, ". ")
	if len(summary) > maxLength {
		summary = summary[:maxLength] + "..."
	}

	return summary
}

func extractKeywords(content string) []string {
	// Enhanced keyword extraction using NLP techniques
	if content == "" {
		return []string{}
	}

	// Initialize NLP service for advanced processing
	nlpService := services.NewNLPService()

	// Use TF-IDF and named entity recognition for keyword extraction
	keywords := nlpService.ExtractKeywordsAdvanced(content)

	// If advanced extraction fails, fallback to improved basic extraction
	if len(keywords) == 0 {
		keywords = extractKeywordsBasic(content)
	}

	// Limit to top 10 most relevant keywords
	if len(keywords) > 10 {
		keywords = keywords[:10]
	}

	return keywords
}

// extractKeywordsBasic provides improved basic keyword extraction as fallback
func extractKeywordsBasic(content string) []string {
	// Clean and normalize text
	text := strings.ToLower(content)
	text = regexp.MustCompile(`[^\w\s]`).ReplaceAllString(text, " ")

	// Split into words
	words := strings.Fields(text)

	// Define stop words
	stopWords := map[string]bool{
		"the": true, "and": true, "or": true, "but": true, "in": true, "on": true, "at": true, "to": true,
		"for": true, "of": true, "with": true, "by": true, "from": true, "up": true, "about": true, "into": true,
		"through": true, "during": true, "before": true, "after": true, "above": true, "below": true, "between": true,
		"among": true, "is": true, "are": true, "was": true, "were": true, "be": true, "been": true, "being": true,
		"have": true, "has": true, "had": true, "do": true, "does": true, "did": true, "will": true, "would": true,
		"could": true, "should": true, "may": true, "might": true, "must": true, "can": true, "shall": true,
		"this": true, "that": true, "these": true, "those": true, "a": true, "an": true, "as": true, "if": true,
		"then": true, "than": true, "such": true, "both": true, "either": true, "neither": true, "not": true,
		"only": true, "own": true, "same": true, "so": true, "some": true, "no": true, "nor": true,
		"too": true, "very": true, "just": true, "now": true, "each": true, "when": true, "where": true, "why": true,
		"how": true, "all": true, "any": true, "few": true, "more": true, "most": true,
		"other": true, "also": true, "here": true, "there": true, "what": true, "who": true, "which": true,
	}

	// Count word frequencies
	wordFreq := make(map[string]int)
	for _, word := range words {
		if len(word) > 3 && !stopWords[word] {
			wordFreq[word]++
		}
	}

	// Add domain-specific important terms with higher weight
	domainTerms := map[string]int{
		"regulation": 5, "policy": 5, "compliance": 5, "federal": 4, "rule": 4, "law": 4,
		"agency": 4, "administration": 4, "department": 4, "commission": 4, "bureau": 4,
		"public": 3, "comment": 3, "notice": 3, "proposed": 3, "final": 3, "interim": 3,
		"effective": 3, "implementation": 3, "enforcement": 3, "violation": 3, "penalty": 3,
		"requirement": 3, "standard": 3, "procedure": 3, "process": 3, "application": 3,
		"approval": 3, "permit": 3, "license": 3, "certification": 3, "inspection": 3,
		"safety": 3, "security": 3, "environmental": 3, "health": 3, "economic": 3,
		"financial": 3, "technical": 3, "administrative": 3, "operational": 3, "management": 3,
	}

	for word, freq := range wordFreq {
		if weight, exists := domainTerms[word]; exists {
			wordFreq[word] = freq + weight
		}
	}

	// Sort by frequency and extract top keywords
	type wordCount struct {
		word  string
		count int
	}

	var sortedWords []wordCount
	for word, count := range wordFreq {
		if count >= 2 { // Must appear at least twice or be a domain term
			sortedWords = append(sortedWords, wordCount{word, count})
		}
	}

	// Sort by count (descending)
	for i := 0; i < len(sortedWords)-1; i++ {
		for j := i + 1; j < len(sortedWords); j++ {
			if sortedWords[i].count < sortedWords[j].count {
				sortedWords[i], sortedWords[j] = sortedWords[j], sortedWords[i]
			}
		}
	}

	// Extract top keywords
	var keywords []string
	maxKeywords := 10
	for i, wc := range sortedWords {
		if i >= maxKeywords {
			break
		}
		keywords = append(keywords, wc.word)
	}

	return keywords
}

func suggestCategory(content string) string {
	// Enhanced category suggestion using ML-based classification
	if content == "" {
		return "General Policy"
	}

	// Initialize NLP service for advanced processing
	nlpService := services.NewNLPService()

	// Use machine learning classification for category suggestion
	category := nlpService.ClassifyDocumentCategory(content)

	// If ML classification fails, fallback to rule-based classification
	if category == "" {
		category = suggestCategoryRuleBased(content)
	}

	return category
}

// suggestCategoryRuleBased provides rule-based category classification as fallback
func suggestCategoryRuleBased(content string) string {
	text := strings.ToLower(content)

	// Define category patterns with keywords and weights
	categoryPatterns := map[string]map[string]int{
		"Environmental Protection": {
			"environment": 5, "environmental": 5, "pollution": 4, "emission": 4, "waste": 4,
			"air quality": 5, "water quality": 5, "climate": 4, "greenhouse": 4, "carbon": 3,
			"toxic": 4, "hazardous": 4, "cleanup": 3, "conservation": 3, "ecosystem": 3,
			"endangered": 3, "wildlife": 3, "forest": 3, "marine": 3, "renewable": 3,
		},
		"Financial Services": {
			"financial": 5, "banking": 5, "credit": 4, "loan": 4, "investment": 4,
			"securities": 5, "insurance": 4, "mortgage": 4, "deposit": 3, "capital": 3,
			"market": 3, "trading": 3, "broker": 3, "fund": 3, "pension": 3,
			"currency": 3, "monetary": 3, "fiscal": 3, "tax": 3, "revenue": 3,
		},
		"Healthcare and Medical": {
			"health": 5, "healthcare": 5, "medical": 5, "drug": 4, "pharmaceutical": 4,
			"patient": 4, "hospital": 4, "clinic": 3, "doctor": 3, "nurse": 3,
			"treatment": 3, "therapy": 3, "medicine": 3, "disease": 3, "vaccine": 4,
			"fda": 5, "clinical trial": 4, "medical device": 4, "prescription": 3,
		},
		"Transportation": {
			"transportation": 5, "transport": 4, "vehicle": 4, "traffic": 4, "highway": 4,
			"aviation": 5, "aircraft": 4, "airline": 4, "airport": 4, "flight": 3,
			"maritime": 4, "shipping": 4, "vessel": 3, "port": 3, "railroad": 4,
			"rail": 3, "transit": 3, "automotive": 3, "truck": 3, "bus": 3,
		},
		"Energy": {
			"energy": 5, "power": 4, "electricity": 4, "electric": 3, "utility": 4,
			"nuclear": 4, "renewable": 4, "solar": 3, "wind": 3, "hydroelectric": 3,
			"coal": 3, "oil": 3, "gas": 3, "petroleum": 3, "pipeline": 3,
			"grid": 3, "transmission": 3, "generation": 3, "efficiency": 3,
		},
		"Telecommunications": {
			"telecommunications": 5, "telecom": 4, "internet": 4, "broadband": 4, "wireless": 4,
			"phone": 3, "telephone": 3, "cellular": 3, "spectrum": 4, "frequency": 3,
			"data": 3, "privacy": 4, "cybersecurity": 4, "network": 3, "communication": 3,
		},
		"Food and Agriculture": {
			"food": 5, "agriculture": 5, "agricultural": 4, "farming": 4, "farm": 3,
			"crop": 3, "livestock": 3, "pesticide": 4, "fertilizer": 3, "organic": 3,
			"nutrition": 4, "safety": 3, "inspection": 3, "labeling": 3, "gmo": 3,
		},
		"Education": {
			"education": 5, "educational": 4, "school": 4, "university": 4, "college": 4,
			"student": 4, "teacher": 3, "academic": 3, "curriculum": 3, "learning": 3,
			"research": 3, "scholarship": 3, "grant": 3, "tuition": 3, "degree": 3,
		},
		"Employment and Labor": {
			"employment": 5, "labor": 5, "worker": 4, "workplace": 4, "job": 3,
			"wage": 4, "salary": 3, "benefit": 3, "pension": 3, "safety": 3,
			"discrimination": 4, "harassment": 3, "union": 3, "collective bargaining": 4,
		},
		"Consumer Protection": {
			"consumer": 5, "product": 4, "safety": 4, "recall": 4, "warranty": 3,
			"fraud": 4, "deception": 3, "advertising": 3, "marketing": 3, "price": 3,
			"quality": 3, "standard": 3, "testing": 3, "certification": 3,
		},
		"Housing and Construction": {
			"housing": 5, "construction": 5, "building": 4, "real estate": 4, "property": 4,
			"zoning": 4, "development": 3, "contractor": 3, "permit": 3, "inspection": 3,
			"code": 3, "standard": 3, "residential": 3, "commercial": 3,
		},
		"Immigration": {
			"immigration": 5, "immigrant": 4, "visa": 4, "citizenship": 4, "border": 4,
			"refugee": 4, "asylum": 4, "deportation": 3, "naturalization": 3, "alien": 3,
		},
		"National Security": {
			"security": 5, "national security": 5, "defense": 4, "military": 4, "terrorism": 4,
			"intelligence": 4, "classified": 3, "threat": 3, "surveillance": 3, "homeland": 4,
		},
		"Trade and Commerce": {
			"trade": 5, "commerce": 5, "import": 4, "export": 4, "tariff": 4,
			"customs": 4, "international": 3, "business": 3, "economic": 3, "market": 3,
		},
	}

	// Calculate scores for each category
	categoryScores := make(map[string]int)

	for category, keywords := range categoryPatterns {
		score := 0
		for keyword, weight := range keywords {
			if strings.Contains(text, keyword) {
				score += weight
			}
		}
		if score > 0 {
			categoryScores[category] = score
		}
	}

	// Find the category with the highest score
	maxScore := 0
	bestCategory := "General Policy"

	for category, score := range categoryScores {
		if score > maxScore {
			maxScore = score
			bestCategory = category
		}
	}

	// If no specific category matches well, try broader classification
	if maxScore < 3 {
		if strings.Contains(text, "regulation") || strings.Contains(text, "rule") {
			return "Regulatory Affairs"
		}
		if strings.Contains(text, "public") || strings.Contains(text, "comment") {
			return "Public Participation"
		}
		if strings.Contains(text, "agency") || strings.Contains(text, "administration") {
			return "Administrative Procedures"
		}
	}

	return bestCategory
}

func generateAbstract(content string) string {
	return "This regulation addresses important policy matters and compliance requirements."
}

func generateComplianceNote(content string) string {
	return "Please ensure compliance with all applicable federal regulations."
}

func suggestEffectiveDate() string {
	return time.Now().AddDate(0, 0, 30).Format("2006-01-02") // 30 days from now
}
