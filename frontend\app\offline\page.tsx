'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  WifiIcon,
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CloudIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';

const OfflinePage: React.FC = () => {
  const [isOnline, setIsOnline] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const [lastAttempt, setLastAttempt] = useState<Date | null>(null);

  useEffect(() => {
    // Check initial online status
    setIsOnline(navigator.onLine);

    // Listen for online/offline events
    const handleOnline = () => {
      setIsOnline(true);
      // Redirect to home page when back online
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    };

    const handleOffline = () => {
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleRetryConnection = async () => {
    setRetryCount(prev => prev + 1);
    setLastAttempt(new Date());

    try {
      // Try to fetch a small resource to test connectivity
      const response = await fetch('/api/health', {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      if (response.ok) {
        setIsOnline(true);
        window.location.href = '/';
      }
    } catch (error) {
      // Still offline
      console.log('Still offline');
    }
  };

  const offlineFeatures = [
    {
      icon: DocumentTextIcon,
      title: 'Cached Documents',
      description: 'View recently accessed documents that are stored locally'
    },
    {
      icon: ClockIcon,
      title: 'Draft Saving',
      description: 'Your work is automatically saved locally and will sync when online'
    },
    {
      icon: CloudIcon,
      title: 'Offline Search',
      description: 'Search through your cached documents and recent activity'
    }
  ];

  const troubleshootingSteps = [
    'Check your internet connection',
    'Verify your WiFi or ethernet connection',
    'Try refreshing the page',
    'Check if other websites are working',
    'Contact your network administrator if the problem persists'
  ];

  if (isOnline) {
    return (
      <Layout>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8 text-center">
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-green-100">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Connection Restored!</h2>
              <p className="text-gray-600 mb-6">
                Your internet connection has been restored. Redirecting you back to the application...
              </p>
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="mx-auto h-16 w-16 flex items-center justify-center rounded-full bg-orange-100 mb-6">
              <WifiIcon className="h-8 w-8 text-orange-600" />
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">You're Offline</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              It looks like you've lost your internet connection. Don't worry - you can still access 
              some features while offline.
            </p>
          </div>

          {/* Connection Status */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ExclamationTriangleIcon className="h-6 w-6 text-orange-500 mr-3" />
                <div>
                  <h2 className="text-lg font-semibold text-gray-900">Connection Status</h2>
                  <p className="text-gray-600">No internet connection detected</p>
                </div>
              </div>
              <button
                onClick={handleRetryConnection}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                Retry Connection
              </button>
            </div>
            
            {retryCount > 0 && (
              <div className="mt-4 p-3 bg-gray-50 rounded-md">
                <p className="text-sm text-gray-600">
                  Retry attempts: {retryCount}
                  {lastAttempt && (
                    <span className="ml-2">
                      (Last attempt: {lastAttempt.toLocaleTimeString()})
                    </span>
                  )}
                </p>
              </div>
            )}
          </div>

          {/* Available Offline Features */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Available Offline Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {offlineFeatures.map((feature, index) => (
                <div key={index} className="text-center">
                  <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100 mb-4">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Offline Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button
                  onClick={() => {
                    // Access cached documents
                    const cachedDocs = localStorage.getItem('cachedDocuments');
                    if (cachedDocs) {
                      alert('Accessing cached documents...');
                    } else {
                      alert('No cached documents available');
                    }
                  }}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-sm font-medium text-gray-900">View Cached Documents</span>
                  </div>
                </button>
                
                <button
                  onClick={() => {
                    // Show offline search
                    alert('Offline search functionality would be implemented here');
                  }}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <CloudIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-sm font-medium text-gray-900">Search Offline Content</span>
                  </div>
                </button>
                
                <button
                  onClick={() => {
                    // Show draft documents
                    const drafts = localStorage.getItem('draftDocuments');
                    if (drafts) {
                      alert('Accessing draft documents...');
                    } else {
                      alert('No draft documents found');
                    }
                  }}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center">
                    <ClockIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-sm font-medium text-gray-900">View Draft Documents</span>
                  </div>
                </button>
              </div>
            </div>

            {/* Troubleshooting */}
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Troubleshooting</h3>
              <div className="space-y-3">
                {troubleshootingSteps.map((step, index) => (
                  <div key={index} className="flex items-start">
                    <span className="flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-sm font-medium mr-3">
                      {index + 1}
                    </span>
                    <span className="text-sm text-gray-700">{step}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sync Status */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Sync Status</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center">
                  <ClockIcon className="h-5 w-5 text-yellow-500 mr-3" />
                  <span className="text-sm font-medium text-yellow-800">Pending Changes</span>
                </div>
                <span className="text-sm text-yellow-600">
                  {Math.floor(Math.random() * 5) + 1} items waiting to sync
                </span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center">
                  <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm font-medium text-green-800">Last Sync</span>
                </div>
                <span className="text-sm text-green-600">
                  {new Date(Date.now() - Math.random() * 3600000).toLocaleString()}
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-4">
              Your changes will automatically sync when your connection is restored.
            </p>
          </div>

          {/* Help Information */}
          <div className="bg-blue-50 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-4">Need Help?</h3>
            <div className="space-y-2">
              <p className="text-sm text-blue-700">
                • This page will automatically refresh when your connection is restored
              </p>
              <p className="text-sm text-blue-700">
                • Your work is saved locally and will sync when you're back online
              </p>
              <p className="text-sm text-blue-700">
                • You can continue working with cached documents and drafts
              </p>
              <p className="text-sm text-blue-700">
                • Contact IT support if connection issues persist: 1-800-NOTECONTROL
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default OfflinePage;
