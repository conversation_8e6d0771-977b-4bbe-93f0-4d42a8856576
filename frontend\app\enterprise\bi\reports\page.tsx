'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { biApi } from '../../../services/enterpriseApi';

interface Report {
  id: number;
  created_at: string;
  updated_at: string;
  report_code: string;
  report_name: string;
  description?: string;
  category: string;
  report_type: string;
  data_source_id?: number;
  query_definition?: string;
  parameters?: string;
  schedule?: string;
  output_format: string;
  is_public: boolean;
  is_active: boolean;
  owner_id: number;
  last_executed?: string;
  execution_count: number;
  avg_execution_time: number;
  file_size?: number;
  retention_days: number;
  metadata?: string;
}

const ReportsListPage: React.FC = () => {
  const router = useRouter();
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('');

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const response = await biApi.getReports({
        search: searchTerm,
        category: filterCategory || undefined,
      });
      setReports(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch reports');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this report?')) return;
    
    try {
      await biApi.deleteReport(id);
      await fetchReports(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to delete report');
    }
  };

  const handleExecute = async (id: number) => {
    try {
      await biApi.executeReport(id);
      await fetchReports(); // Refresh to update execution stats
    } catch (err: any) {
      setError(err.message || 'Failed to execute report');
    }
  };

  const filteredReports = reports.filter(report =>
    report.report_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.report_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) return <div className="p-6">Loading reports...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">BI Reports</h1>
        <button
          onClick={() => router.push('/enterprise/bi/reports/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Create New Report
        </button>
      </div>

      {/* Search and Filter */}
      <div className="mb-6 flex gap-4">
        <input
          type="text"
          placeholder="Search reports..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border rounded px-3 py-2 flex-1"
        />
        <select
          value={filterCategory}
          onChange={(e) => setFilterCategory(e.target.value)}
          className="border rounded px-3 py-2"
        >
          <option value="">All Categories</option>
          <option value="financial">Financial</option>
          <option value="operational">Operational</option>
          <option value="sales">Sales</option>
          <option value="hr">HR</option>
          <option value="compliance">Compliance</option>
        </select>
        <button
          onClick={fetchReports}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Search
        </button>
      </div>

      {/* Reports Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Code
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Executed
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredReports.map((report) => (
              <tr key={report.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {report.report_code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {report.report_name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {report.category}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {report.report_type}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {report.last_executed ? new Date(report.last_executed).toLocaleDateString() : 'Never'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    report.is_active 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {report.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => router.push(`/enterprise/bi/reports/${report.id}`)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </button>
                    <button
                      onClick={() => handleExecute(report.id)}
                      className="text-green-600 hover:text-green-900"
                    >
                      Execute
                    </button>
                    <button
                      onClick={() => router.push(`/enterprise/bi/reports/${report.id}/edit`)}
                      className="text-yellow-600 hover:text-yellow-900"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(report.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {filteredReports.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No reports found
          </div>
        )}
      </div>
    </div>
  );
};

export default ReportsListPage;
