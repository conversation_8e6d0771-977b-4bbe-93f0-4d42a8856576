package models

import (
	"time"

	"gorm.io/gorm"
)

// RegulationType represents the type of regulation
type RegulationType string

const (
	RegulationTypeLaw        RegulationType = "law"
	RegulationTypeRule       RegulationType = "rule"
	RegulationTypeRegulation RegulationType = "regulation"
	RegulationTypeCode       RegulationType = "code"
)

// RegulationStatus represents the status of a regulation
type RegulationStatus string

const (
	RegulationStatusDraft      RegulationStatus = "draft"
	RegulationStatusReview     RegulationStatus = "under_review"
	RegulationStatusApproved   RegulationStatus = "approved"
	RegulationStatusPublished  RegulationStatus = "published"
	RegulationStatusEffective  RegulationStatus = "effective"
	RegulationStatusTerminated RegulationStatus = "terminated"
	RegulationStatusArchived   RegulationStatus = "archived"
)

// ChunkType represents the hierarchical level of a chunk
type ChunkType string

const (
	ChunkTypeTitle      ChunkType = "title"
	ChunkTypeDivision   ChunkType = "division"
	ChunkTypeChapter    ChunkType = "chapter"
	ChunkTypeSubtitle   ChunkType = "subtitle"
	ChunkTypeSection    ChunkType = "section"
	ChunkTypeSubsection ChunkType = "subsection"
	ChunkTypeParagraph  ChunkType = "paragraph"
	ChunkTypeClause     ChunkType = "clause"
	ChunkTypeSubclause  ChunkType = "subclause"
)

// HierarchyLevel represents the level in the regulatory hierarchy
type HierarchyLevel string

const (
	HierarchyLevelCFRTitle   HierarchyLevel = "cfr_title"
	HierarchyLevelChapter    HierarchyLevel = "chapter"
	HierarchyLevelSubchapter HierarchyLevel = "subchapter"
	HierarchyLevelPart       HierarchyLevel = "part"
	HierarchyLevelSection    HierarchyLevel = "section"
	HierarchyLevelRegulation HierarchyLevel = "regulation"
)

// RelationshipType represents how regulations relate to each other
type RelationshipType string

const (
	RelationshipAmends     RelationshipType = "amends"
	RelationshipRepeals    RelationshipType = "repeals"
	RelationshipRefersTo   RelationshipType = "refers_to"
	RelationshipSupersedes RelationshipType = "supersedes"
	RelationshipImplements RelationshipType = "implements"
)

// DocumentRelationshipType represents how regulations relate to documents
type DocumentRelationshipType string

const (
	DocumentRelationshipImplements DocumentRelationshipType = "implements"
	DocumentRelationshipBasedOn    DocumentRelationshipType = "based_on"
	DocumentRelationshipAmends     DocumentRelationshipType = "amends"
	DocumentRelationshipRepeals    DocumentRelationshipType = "repeals"
	DocumentRelationshipReferences DocumentRelationshipType = "references"
	DocumentRelationshipSupersedes DocumentRelationshipType = "supersedes"
)

// AgencyRelationshipType represents how regulations relate to agencies
type AgencyRelationshipType string

const (
	AgencyRelationshipEstablishedBy AgencyRelationshipType = "established_by"
	AgencyRelationshipAuthorizedBy  AgencyRelationshipType = "authorized_by"
	AgencyRelationshipModifiedBy    AgencyRelationshipType = "modified_by"
	AgencyRelationshipAbolishedBy   AgencyRelationshipType = "abolished_by"
)

// CategoryRelationshipType represents how regulations relate to categories
type CategoryRelationshipType string

const (
	CategoryRelationshipCreatedBy   CategoryRelationshipType = "created_by"
	CategoryRelationshipModifiedBy  CategoryRelationshipType = "modified_by"
	CategoryRelationshipAbolishedBy CategoryRelationshipType = "abolished_by"
	CategoryRelationshipGovernedBy  CategoryRelationshipType = "governed_by"
)

// LawsAndRules represents the top-level regulation document
type LawsAndRules struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Basic information
	Title                    string                     `json:"title" gorm:"not null"`
	ShortTitle               string                     `json:"short_title"`
	Type                     RegulationType             `json:"type" gorm:"not null"`
	Status                   RegulationStatus           `json:"status" gorm:"default:'draft'"`
	CurrentDocumentVersionID *uint                      `json:"current_document_version_id"`
	CurrentDocumentVersion   *RegulationDocumentVersion `json:"current_document_version,omitempty" gorm:"foreignKey:CurrentDocumentVersionID"`

	// Legal identifiers
	PublicLawNumber      string `json:"public_law_number"`
	RegulatoryIdentifier string `json:"regulatory_identifier"` // RIN
	CFRTitle             string `json:"cfr_title"`
	USCTitle             string `json:"usc_title"`
	DocketNumber         string `json:"docket_number"`

	// Dates
	EnactmentDate   *time.Time `json:"enactment_date"`
	EffectiveDate   *time.Time `json:"effective_date"`
	TerminationDate *time.Time `json:"termination_date"`
	PublicationDate *time.Time `json:"publication_date"`

	// Relationships
	AgencyID    uint   `json:"agency_id" gorm:"not null"`
	Agency      Agency `json:"agency" gorm:"foreignKey:AgencyID"`
	CreatedByID uint   `json:"created_by_id" gorm:"not null"`
	CreatedBy   User   `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Metadata
	Description   string `json:"description"`
	Content       string `json:"content" gorm:"type:text"` // Main regulation content
	Notes         string `json:"notes"`
	IsSignificant bool   `json:"is_significant" gorm:"default:false"`

	// Hierarchical structure
	HierarchyLevel string         `json:"hierarchy_level" gorm:"default:'regulation'"` // 'cfr_title', 'chapter', 'subchapter', 'part', 'section', 'regulation'
	ChapterNumber  string         `json:"chapter_number"`                              // e.g., "I", "II", "III"
	Subchapter     string         `json:"subchapter"`                                  // e.g., "A", "B", "C"
	PartNumber     string         `json:"part_number"`                                 // e.g., "100", "200"
	SectionNumber  string         `json:"section_number"`                              // e.g., "1.1", "2.5"
	Subsection     string         `json:"subsection"`                                  // e.g., "(a)", "(b)"
	ParentID       *uint          `json:"parent_id"`
	Parent         *LawsAndRules  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children       []LawsAndRules `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	OrderInParent  int            `json:"order_in_parent" gorm:"default:0"`

	// Related data
	DocumentVersions []RegulationDocumentVersion `json:"document_versions,omitempty" gorm:"foreignKey:LawRuleID"`
	Chunks           []Chunk                     `json:"chunks,omitempty" gorm:"foreignKey:LawRuleID"`
	Relationships    []Relationship              `json:"relationships,omitempty" gorm:"foreignKey:SourceLawRuleID"`

	// New relationship types
	DocumentRelationships []RegulationDocumentRelationship `json:"document_relationships,omitempty" gorm:"foreignKey:RegulationID"`
	AgencyRelationships   []RegulationAgencyRelationship   `json:"agency_relationships,omitempty" gorm:"foreignKey:RegulationID"`
	CategoryRelationships []RegulationCategoryRelationship `json:"category_relationships,omitempty" gorm:"foreignKey:RegulationID"`
}

// RegulationDocumentVersion represents a specific version of a regulation
type RegulationDocumentVersion struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Version information
	LawRuleID     uint          `json:"law_rule_id" gorm:"not null"`
	LawRule       *LawsAndRules `json:"law_rule,omitempty"`
	VersionNumber string        `json:"version_number" gorm:"not null"` // e.g., "1.0.0", "1.1.0"

	// Dates
	PublicationDate *time.Time `json:"publication_date"`
	EffectiveDate   *time.Time `json:"effective_date"`

	// Status and metadata
	IsOfficial       bool   `json:"is_official" gorm:"default:false"`
	CreatedByID      uint   `json:"created_by_id" gorm:"not null"`
	CreatedBy        User   `json:"created_by" gorm:"foreignKey:CreatedByID"`
	Notes            string `json:"notes"`
	SummaryOfChanges string `json:"summary_of_changes"`

	// Related data
	ChunkMappings []RegulationDocumentVersionChunkMap `json:"chunk_mappings,omitempty" gorm:"foreignKey:DocumentVersionID"`
}

// Chunk represents a hierarchical piece of content
type Chunk struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Hierarchy
	LawRuleID     uint          `json:"law_rule_id" gorm:"not null"`
	LawRule       *LawsAndRules `json:"law_rule,omitempty" gorm:"foreignKey:LawRuleID"`
	ParentChunkID *uint         `json:"parent_chunk_id"`
	ParentChunk   *Chunk        `json:"parent_chunk,omitempty" gorm:"foreignKey:ParentChunkID"`
	ChildChunks   []Chunk       `json:"child_chunks,omitempty" gorm:"foreignKey:ParentChunkID"`
	OrderInParent int           `json:"order_in_parent" gorm:"default:0"`
	ChunkType     ChunkType     `json:"chunk_type" gorm:"not null"`

	// Identification
	ChunkIdentifier string `json:"chunk_identifier" gorm:"uniqueIndex;not null"` // e.g., "USC10-201-2001"
	Number          string `json:"number"`                                       // e.g., "101", "2001"
	Title           string `json:"title"`                                        // e.g., "Authorization of appropriations"

	// Current content version
	CurrentChunkContentVersionID *uint                `json:"current_chunk_content_version_id"`
	CurrentChunkContentVersion   *ChunkContentVersion `json:"current_chunk_content_version,omitempty" gorm:"foreignKey:CurrentChunkContentVersionID"`

	// Related data
	ContentVersions []ChunkContentVersion               `json:"content_versions,omitempty" gorm:"foreignKey:ChunkID"`
	ChunkMappings   []RegulationDocumentVersionChunkMap `json:"chunk_mappings,omitempty" gorm:"foreignKey:ChunkID"`
}

// ChunkContentVersion represents a specific version of chunk content
type ChunkContentVersion struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Content
	ChunkID       uint   `json:"chunk_id" gorm:"not null"`
	Chunk         *Chunk `json:"chunk,omitempty" gorm:"foreignKey:ChunkID"`
	Content       string `json:"content" gorm:"type:text"`
	VersionNumber int    `json:"version_number" gorm:"not null"`
	IsCurrent     bool   `json:"is_current" gorm:"default:false"`
	IsActive      bool   `json:"is_active" gorm:"default:true"` // false for deleted/archived chunks

	// Metadata
	ModifiedByID      uint   `json:"modified_by_id" gorm:"not null"`
	ModifiedBy        User   `json:"modified_by" gorm:"foreignKey:ModifiedByID"`
	ChangeDescription string `json:"change_description"`

	// Related data
	ChunkMappings []RegulationDocumentVersionChunkMap `json:"chunk_mappings,omitempty" gorm:"foreignKey:ChunkContentVersionID"`
}

// RegulationDocumentVersionChunkMap maps document versions to specific chunk content versions
type RegulationDocumentVersionChunkMap struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentVersionID     uint                       `json:"document_version_id" gorm:"not null"`
	DocumentVersion       *RegulationDocumentVersion `json:"document_version,omitempty" gorm:"foreignKey:DocumentVersionID"`
	ChunkID               uint                       `json:"chunk_id" gorm:"not null"`
	Chunk                 *Chunk                     `json:"chunk,omitempty" gorm:"foreignKey:ChunkID"`
	ChunkContentVersionID uint                       `json:"chunk_content_version_id" gorm:"not null"`
	ChunkContentVersion   *ChunkContentVersion       `json:"chunk_content_version,omitempty" gorm:"foreignKey:ChunkContentVersionID"`
}

// Relationship represents relationships between regulations
type Relationship struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Source (the regulation that creates the relationship)
	SourceLawRuleID uint          `json:"source_law_rule_id" gorm:"not null"`
	SourceLawRule   *LawsAndRules `json:"source_law_rule,omitempty" gorm:"foreignKey:SourceLawRuleID"`
	SourceChunkID   *uint         `json:"source_chunk_id"`
	SourceChunk     *Chunk        `json:"source_chunk,omitempty" gorm:"foreignKey:SourceChunkID"`

	// Target (the regulation being referenced)
	TargetLawRuleID uint          `json:"target_law_rule_id" gorm:"not null"`
	TargetLawRule   *LawsAndRules `json:"target_law_rule,omitempty" gorm:"foreignKey:TargetLawRuleID"`
	TargetChunkID   *uint         `json:"target_chunk_id"`
	TargetChunk     *Chunk        `json:"target_chunk,omitempty" gorm:"foreignKey:TargetChunkID"`

	// Relationship details
	RelationshipType            RelationshipType `json:"relationship_type" gorm:"not null"`
	EffectiveDateOfRelationship *time.Time       `json:"effective_date_of_relationship"`
	Description                 string           `json:"description"`

	// Metadata
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
}

// RegulationDocumentRelationship represents relationships between regulations and documents
type RegulationDocumentRelationship struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Source regulation
	RegulationID      uint          `json:"regulation_id" gorm:"not null"`
	Regulation        *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`
	RegulationChunkID *uint         `json:"regulation_chunk_id"`
	RegulationChunk   *Chunk        `json:"regulation_chunk,omitempty" gorm:"foreignKey:RegulationChunkID"`

	// Target document
	DocumentID uint      `json:"document_id" gorm:"not null"`
	Document   *Document `json:"document,omitempty" gorm:"foreignKey:DocumentID"`

	// Relationship details
	RelationshipType DocumentRelationshipType `json:"relationship_type" gorm:"not null"`
	EffectiveDate    *time.Time               `json:"effective_date"`
	TerminationDate  *time.Time               `json:"termination_date"`
	Description      string                   `json:"description"`
	CitationText     string                   `json:"citation_text"`

	// Metadata
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	IsActive    bool `json:"is_active" gorm:"default:true"`
}

// RegulationAgencyRelationship represents relationships between regulations and agencies
type RegulationAgencyRelationship struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Source regulation
	RegulationID      uint          `json:"regulation_id" gorm:"not null"`
	Regulation        *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`
	RegulationChunkID *uint         `json:"regulation_chunk_id"`
	RegulationChunk   *Chunk        `json:"regulation_chunk,omitempty" gorm:"foreignKey:RegulationChunkID"`

	// Target agency
	AgencyID uint    `json:"agency_id" gorm:"not null"`
	Agency   *Agency `json:"agency,omitempty" gorm:"foreignKey:AgencyID"`

	// Relationship details
	RelationshipType AgencyRelationshipType `json:"relationship_type" gorm:"not null"`
	EffectiveDate    *time.Time             `json:"effective_date"`
	TerminationDate  *time.Time             `json:"termination_date"`
	Description      string                 `json:"description"`
	AuthorityScope   string                 `json:"authority_scope"`

	// Metadata
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	IsActive    bool `json:"is_active" gorm:"default:true"`
}

// RegulationCategoryRelationship represents relationships between regulations and categories
type RegulationCategoryRelationship struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Source regulation
	RegulationID      uint          `json:"regulation_id" gorm:"not null"`
	Regulation        *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`
	RegulationChunkID *uint         `json:"regulation_chunk_id"`
	RegulationChunk   *Chunk        `json:"regulation_chunk,omitempty" gorm:"foreignKey:RegulationChunkID"`

	// Target category
	CategoryID uint      `json:"category_id" gorm:"not null"`
	Category   *Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`

	// Relationship details
	RelationshipType CategoryRelationshipType `json:"relationship_type" gorm:"not null"`
	EffectiveDate    *time.Time               `json:"effective_date"`
	TerminationDate  *time.Time               `json:"termination_date"`
	Description      string                   `json:"description"`
	ScopeDefinition  string                   `json:"scope_definition"`

	// Metadata
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	IsActive    bool `json:"is_active" gorm:"default:true"`
}

// TableName methods for custom table names
func (LawsAndRules) TableName() string {
	return "laws_and_rules"
}

func (RegulationDocumentVersion) TableName() string {
	return "regulation_document_versions"
}

func (Chunk) TableName() string {
	return "regulation_chunks"
}

func (ChunkContentVersion) TableName() string {
	return "regulation_chunk_content_versions"
}

func (RegulationDocumentVersionChunkMap) TableName() string {
	return "regulation_document_version_chunk_maps"
}

func (Relationship) TableName() string {
	return "regulation_relationships"
}

func (RegulationDocumentRelationship) TableName() string {
	return "regulation_document_relationships"
}

func (RegulationAgencyRelationship) TableName() string {
	return "regulation_agency_relationships"
}

func (RegulationCategoryRelationship) TableName() string {
	return "regulation_category_relationships"
}

// RegulationChunk represents a chunk/section of a regulation
type RegulationChunk struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Regulation reference
	LawRuleID  uint          `json:"law_rule_id" gorm:"column:law_rule_id;not null"`
	Regulation *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:LawRuleID"`

	// Chunk details
	ChunkOrder int    `json:"chunk_order" gorm:"not null"`
	Title      string `json:"title" gorm:"not null"`
	Content    string `json:"content" gorm:"type:text;not null"`
	ChunkType  string `json:"chunk_type" gorm:"default:'section'"` // "section", "subsection", "paragraph"

	// Versioning
	Version       string     `json:"version" gorm:"default:'1.0'"`
	IsActive      bool       `json:"is_active" gorm:"default:true"`
	EffectiveDate *time.Time `json:"effective_date"`

	// Metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// RegulationCategoryAssignment represents assignment of regulations to categories
type RegulationCategoryAssignment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Regulation reference
	RegulationID uint          `json:"regulation_id" gorm:"not null"`
	Regulation   *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`

	// Category reference
	CategoryID uint      `json:"category_id" gorm:"not null"`
	Category   *Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`

	// Assignment details
	AssignmentType string `json:"assignment_type" gorm:"default:'primary'"` // "primary", "secondary"
	IsActive       bool   `json:"is_active" gorm:"default:true"`

	// Metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// RegulationRelationship represents relationships between regulations
type RegulationRelationship struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Source regulation
	SourceRegulationID uint          `json:"source_regulation_id" gorm:"not null"`
	SourceRegulation   *LawsAndRules `json:"source_regulation,omitempty" gorm:"foreignKey:SourceRegulationID"`

	// Target regulation
	TargetRegulationID uint          `json:"target_regulation_id" gorm:"not null"`
	TargetRegulation   *LawsAndRules `json:"target_regulation,omitempty" gorm:"foreignKey:TargetRegulationID"`

	// Relationship details
	RelationshipType string `json:"relationship_type" gorm:"not null"` // "amends", "supersedes", "references", "implements"
	Description      string `json:"description" gorm:"type:text"`
	IsActive         bool   `json:"is_active" gorm:"default:true"`

	// Metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for RegulationChunk model
func (RegulationChunk) TableName() string {
	return "regulation_chunks"
}

// TableName returns the table name for RegulationCategoryAssignment model
func (RegulationCategoryAssignment) TableName() string {
	return "regulation_category_assignments"
}

// RegulationStructuredContent represents structured content within regulations
type RegulationStructuredContent struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Regulation reference
	RegulationID uint          `json:"regulation_id" gorm:"not null"`
	Regulation   *LawsAndRules `json:"regulation,omitempty" gorm:"foreignKey:RegulationID"`

	// Content structure
	SectionNumber string `json:"section_number" gorm:"not null"`
	Title         string `json:"title" gorm:"not null"`
	Content       string `json:"content" gorm:"type:text;not null"`
	ContentType   string `json:"content_type" gorm:"default:'text'"` // "text", "table", "list", "formula"

	// Hierarchy
	ParentSectionID *uint                        `json:"parent_section_id"`
	ParentSection   *RegulationStructuredContent `json:"parent_section,omitempty" gorm:"foreignKey:ParentSectionID"`
	Level           int                          `json:"level" gorm:"default:1"` // 1=chapter, 2=section, 3=subsection, etc.

	// Ordering and status
	SortOrder int  `json:"sort_order" gorm:"default:0"`
	IsActive  bool `json:"is_active" gorm:"default:true"`

	// Metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for RegulationRelationship model
func (RegulationRelationship) TableName() string {
	return "regulation_relationships"
}

// TableName returns the table name for RegulationStructuredContent model
func (RegulationStructuredContent) TableName() string {
	return "regulation_structured_contents"
}
