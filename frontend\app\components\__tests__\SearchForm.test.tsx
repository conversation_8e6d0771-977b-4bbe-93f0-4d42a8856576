import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { jest } from '@jest/globals';
import SearchForm from '../Search/SearchForm';

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: mockPush,
    query: {},
    isReady: true,
  }),
}));

// Mock API service
const mockGetPublicAgencies = jest.fn() as jest.MockedFunction<any>;
mockGetPublicAgencies.mockResolvedValue({
  data: [
    { id: 1, name: 'Department of Test', short_name: 'DOT' },
    { id: 2, name: 'Environmental Protection Agency', short_name: 'EPA' },
  ],
});

const mockGetPublicCategories = jest.fn() as jest.MockedFunction<any>;
mockGetPublicCategories.mockResolvedValue({
  data: [
    { id: 1, name: 'Environmental Protection' },
    { id: 2, name: 'Healthcare' },
  ],
});

const mockApiService = {
  getPublicAgencies: mockGetPublicAgencies,
  getPublicCategories: mockGetPublicCategories,
};

jest.mock('../../services/api', () => ({
  __esModule: true,
  default: mockApiService,
}));

// Mock UI store
const mockSetSearchQuery = jest.fn();
const mockSetSearchFilters = jest.fn();
const mockClearSearchFilters = jest.fn();

jest.mock('../../stores/uiStore', () => ({
  useUIStore: () => ({
    searchQuery: '',
    setSearchQuery: mockSetSearchQuery,
    searchFilters: {},
    setSearchFilters: mockSetSearchFilters,
    clearSearchFilters: mockClearSearchFilters,
  }),
}));

describe('SearchForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders basic search form', () => {
    render(<SearchForm />);
    
    expect(screen.getByPlaceholderText(/search documents/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /search/i })).toBeInTheDocument();
  });

  it('handles search input changes', async () => {
    const user = userEvent.setup();
    render(<SearchForm />);
    
    const searchInput = screen.getByPlaceholderText(/search documents/i);
    await user.type(searchInput, 'environmental regulations');
    
    expect(searchInput).toHaveValue('environmental regulations');
  });

  it('submits search form', async () => {
    const user = userEvent.setup();
    const mockOnSearch = jest.fn();
    
    render(<SearchForm onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText(/search documents/i);
    const searchButton = screen.getByRole('button', { name: /search/i });
    
    await user.type(searchInput, 'test query');
    await user.click(searchButton);
    
    expect(mockOnSearch).toHaveBeenCalledWith({
      query: 'test query',
    });
  });

  it('navigates to search page when no onSearch callback provided', async () => {
    const user = userEvent.setup();
    render(<SearchForm />);
    
    const searchInput = screen.getByPlaceholderText(/search documents/i);
    const searchButton = screen.getByRole('button', { name: /search/i });
    
    await user.type(searchInput, 'test query');
    await user.click(searchButton);
    
    expect(mockPush).toHaveBeenCalledWith('/search?q=test%20query');
  });

  it('shows advanced filters when showAdvanced is true', () => {
    render(<SearchForm showAdvanced={true} />);
    
    expect(screen.getByText(/agency/i)).toBeInTheDocument();
    expect(screen.getByText(/category/i)).toBeInTheDocument();
    expect(screen.getByText(/document type/i)).toBeInTheDocument();
  });

  it('toggles filter visibility', async () => {
    const user = userEvent.setup();
    render(<SearchForm />);
    
    const filterButton = screen.getByRole('button', { name: '' }); // Filter icon button
    await user.click(filterButton);
    
    await waitFor(() => {
      expect(screen.getByText(/agency/i)).toBeInTheDocument();
    });
  });

  it('handles filter changes', async () => {
    const user = userEvent.setup();
    render(<SearchForm showAdvanced={true} />);
    
    await waitFor(() => {
      expect(screen.getByText(/agency/i)).toBeInTheDocument();
    });
    
    const agencySelect = screen.getByDisplayValue(/all agencies/i);
    await user.selectOptions(agencySelect, '1');
    
    expect(mockSetSearchFilters).toHaveBeenCalledWith({ agency: '1' });
  });

  it('clears filters when clear button is clicked', async () => {
    const user = userEvent.setup();
    
    // Mock that there are active filters
    const { useUIStore } = await import('../../stores/uiStore');
    jest.mocked(useUIStore).mockReturnValue({
      searchQuery: 'test',
      setSearchQuery: mockSetSearchQuery,
      searchFilters: { agency: '1' },
      setSearchFilters: mockSetSearchFilters,
      clearSearchFilters: mockClearSearchFilters,
    });
    
    render(<SearchForm showAdvanced={true} />);
    
    await waitFor(() => {
      expect(screen.getByText(/clear all filters/i)).toBeInTheDocument();
    });
    
    const clearButton = screen.getByText(/clear all filters/i);
    await user.click(clearButton);
    
    expect(mockClearSearchFilters).toHaveBeenCalled();
  });

  it('handles form submission with Enter key', async () => {
    const user = userEvent.setup();
    const mockOnSearch = jest.fn();
    
    render(<SearchForm onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText(/search documents/i);
    await user.type(searchInput, 'test query{enter}');
    
    expect(mockOnSearch).toHaveBeenCalledWith({
      query: 'test query',
    });
  });

  it('trims whitespace from search query', async () => {
    const user = userEvent.setup();
    const mockOnSearch = jest.fn();
    
    render(<SearchForm onSearch={mockOnSearch} />);
    
    const searchInput = screen.getByPlaceholderText(/search documents/i);
    const searchButton = screen.getByRole('button', { name: /search/i });
    
    await user.type(searchInput, '  test query  ');
    await user.click(searchButton);
    
    expect(mockOnSearch).toHaveBeenCalledWith({
      query: 'test query',
    });
  });

  it('does not submit empty search query', async () => {
    const user = userEvent.setup();
    const mockOnSearch = jest.fn();
    
    render(<SearchForm onSearch={mockOnSearch} />);
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    await user.click(searchButton);
    
    expect(mockOnSearch).toHaveBeenCalledWith({
      query: '',
    });
  });

  it('loads filter options when advanced filters are shown', async () => {
    render(<SearchForm showAdvanced={true} />);

    await waitFor(() => {
      expect(mockGetPublicAgencies).toHaveBeenCalled();
      expect(mockGetPublicCategories).toHaveBeenCalled();
    });
  });

  it('handles date filter changes', async () => {
    const user = userEvent.setup();
    render(<SearchForm showAdvanced={true} />);
    
    await waitFor(() => {
      expect(screen.getByText(/from date/i)).toBeInTheDocument();
    });
    
    const fromDateInput = screen.getByLabelText(/from date/i);
    await user.type(fromDateInput, '2024-01-01');
    
    expect(mockSetSearchFilters).toHaveBeenCalledWith({ dateFrom: '2024-01-01' });
  });

  it('handles document type filter changes', async () => {
    const user = userEvent.setup();
    render(<SearchForm showAdvanced={true} />);
    
    await waitFor(() => {
      expect(screen.getByText(/document type/i)).toBeInTheDocument();
    });
    
    const typeSelect = screen.getByDisplayValue(/all types/i);
    await user.selectOptions(typeSelect, 'rule');
    
    expect(mockSetSearchFilters).toHaveBeenCalledWith({ type: 'rule' });
  });
});
