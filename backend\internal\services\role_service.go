package services

import (
	"fmt"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// RoleService handles role management operations
type RoleService struct {
	db *gorm.DB
}

// NewRoleService creates a new role service
func NewRoleService(db *gorm.DB) *RoleService {
	return &RoleService{db: db}
}

// GetRoles returns all roles with optional filtering
func (s *RoleService) GetRoles(includeSystemRoles bool) ([]models.RoleWithPermissions, error) {
	var roles []models.Role
	query := s.db.Preload("Permissions")
	
	if !includeSystemRoles {
		query = query.Where("is_system_role = ?", false)
	}
	
	if err := query.Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles: %w", err)
	}

	var result []models.RoleWithPermissions
	for _, role := range roles {
		// Count users with this role
		var userCount int64
		s.db.Model(&models.UserRole{}).Where("role_id = ? AND is_active = ?", role.ID, true).Count(&userCount)
		
		roleWithPerms := models.RoleWithPermissions{
			Role:            role,
			PermissionCount: len(role.Permissions),
			UserCount:       int(userCount),
		}
		result = append(result, roleWithPerms)
	}

	return result, nil
}

// GetRoleByID returns a role by its ID
func (s *RoleService) GetRoleByID(id uint) (*models.Role, error) {
	var role models.Role
	if err := s.db.Preload("Permissions").First(&role, id).Error; err != nil {
		return nil, fmt.Errorf("failed to get role: %w", err)
	}
	return &role, nil
}

// CreateRole creates a new role
func (s *RoleService) CreateRole(role *models.Role) error {
	if err := s.db.Create(role).Error; err != nil {
		return fmt.Errorf("failed to create role: %w", err)
	}
	return nil
}

// UpdateRole updates an existing role
func (s *RoleService) UpdateRole(role *models.Role) error {
	if err := s.db.Save(role).Error; err != nil {
		return fmt.Errorf("failed to update role: %w", err)
	}
	return nil
}

// DeleteRole soft deletes a role
func (s *RoleService) DeleteRole(id uint) error {
	// Check if role is a system role
	var role models.Role
	if err := s.db.First(&role, id).Error; err != nil {
		return fmt.Errorf("role not found: %w", err)
	}
	
	if role.IsSystemRole {
		return fmt.Errorf("cannot delete system role")
	}
	
	// Check if role is assigned to any users
	var userCount int64
	s.db.Model(&models.UserRole{}).Where("role_id = ? AND is_active = ?", id, true).Count(&userCount)
	if userCount > 0 {
		return fmt.Errorf("cannot delete role that is assigned to users")
	}
	
	if err := s.db.Delete(&role).Error; err != nil {
		return fmt.Errorf("failed to delete role: %w", err)
	}
	return nil
}

// GetPermissions returns all permissions
func (s *RoleService) GetPermissions() ([]models.Permission, error) {
	var permissions []models.Permission
	if err := s.db.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions: %w", err)
	}
	return permissions, nil
}

// AssignRolePermissions assigns permissions to a role
func (s *RoleService) AssignRolePermissions(roleID uint, permissionIDs []uint) error {
	// Remove existing permissions
	if err := s.db.Where("role_id = ?", roleID).Delete(&models.RolePermission{}).Error; err != nil {
		return fmt.Errorf("failed to remove existing permissions: %w", err)
	}
	
	// Add new permissions
	for _, permissionID := range permissionIDs {
		rolePermission := models.RolePermission{
			RoleID:       roleID,
			PermissionID: permissionID,
		}
		if err := s.db.Create(&rolePermission).Error; err != nil {
			return fmt.Errorf("failed to assign permission: %w", err)
		}
	}
	
	return nil
}

// GetUserRoles returns all users with their roles
func (s *RoleService) GetUserRoles(page, perPage int) ([]models.UserWithRoles, int64, error) {
	var users []models.User
	var total int64
	
	// Get total count
	s.db.Model(&models.User{}).Count(&total)
	
	// Get users with pagination
	offset := (page - 1) * perPage
	if err := s.db.Offset(offset).Limit(perPage).Find(&users).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}
	
	var result []models.UserWithRoles
	for _, user := range users {
		// Get user's roles
		var userRoles []models.UserRole
		s.db.Preload("Role").Where("user_id = ? AND is_active = ?", user.ID, true).Find(&userRoles)
		
		var roles []models.RoleInfo
		for _, ur := range userRoles {
			if !ur.IsExpired() {
				roles = append(roles, models.RoleInfo{
					ID:          ur.Role.ID,
					Name:        ur.Role.Name,
					DisplayName: ur.Role.DisplayName,
					AssignedAt:  ur.AssignedAt,
					ExpiresAt:   ur.ExpiresAt,
					IsActive:    ur.IsActive,
				})
			}
		}
		
		userWithRoles := models.UserWithRoles{
			User:  user,
			Roles: roles,
		}
		result = append(result, userWithRoles)
	}
	
	return result, total, nil
}

// AssignUserRoles assigns roles to a user
func (s *RoleService) AssignUserRoles(userID uint, roleIDs []uint, assignedBy uint, expiresAt *time.Time) error {
	// Start transaction
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()
	
	// Deactivate existing roles
	if err := tx.Model(&models.UserRole{}).Where("user_id = ?", userID).Update("is_active", false).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to deactivate existing roles: %w", err)
	}
	
	// Assign new roles
	for _, roleID := range roleIDs {
		userRole := models.UserRole{
			UserID:     userID,
			RoleID:     roleID,
			AssignedBy: &assignedBy,
			ExpiresAt:  expiresAt,
			IsActive:   true,
		}
		
		// Try to update existing record first
		var existingUserRole models.UserRole
		if err := tx.Where("user_id = ? AND role_id = ?", userID, roleID).First(&existingUserRole).Error; err != nil {
			// Record doesn't exist, create new one
			if err := tx.Create(&userRole).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to assign role: %w", err)
			}
		} else {
			// Record exists, update it
			existingUserRole.IsActive = true
			existingUserRole.AssignedBy = &assignedBy
			existingUserRole.ExpiresAt = expiresAt
			existingUserRole.AssignedAt = time.Now()
			if err := tx.Save(&existingUserRole).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("failed to update role assignment: %w", err)
			}
		}
	}
	
	return tx.Commit().Error
}

// RemoveUserRole removes a specific role from a user
func (s *RoleService) RemoveUserRole(userID, roleID uint) error {
	if err := s.db.Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ?", userID, roleID).
		Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to remove user role: %w", err)
	}
	return nil
}

// GetUserPermissions returns all permissions for a user
func (s *RoleService) GetUserPermissions(userID uint) ([]models.PermissionInfo, error) {
	permissions, err := models.GetUserPermissions(userID, s.db)
	if err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}
	
	var result []models.PermissionInfo
	for _, perm := range permissions {
		result = append(result, models.PermissionInfo{
			ID:          perm.ID,
			Name:        perm.Name,
			DisplayName: perm.DisplayName,
			Resource:    perm.Resource,
			Action:      perm.Action,
		})
	}
	
	return result, nil
}

// CheckUserPermission checks if a user has a specific permission
func (s *RoleService) CheckUserPermission(userID uint, permissionName string) (bool, error) {
	var count int64
	err := s.db.Table("permissions").
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Joins("JOIN user_roles ON role_permissions.role_id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.is_active = ? AND permissions.name = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > ?)", 
			userID, true, permissionName, time.Now()).
		Count(&count).Error
	
	if err != nil {
		return false, fmt.Errorf("failed to check user permission: %w", err)
	}
	
	return count > 0, nil
}

// GetRolesByNames returns roles by their names
func (s *RoleService) GetRolesByNames(names []string) ([]models.Role, error) {
	var roles []models.Role
	if err := s.db.Where("name IN ?", names).Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles by names: %w", err)
	}
	return roles, nil
}
