[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:fix http://localhost:3000/dashboard TypeError: Cannot read properties of null (reading 'length')     at DashboardPage (http://localhost:3000/_next/static/chunks/app/dashboard/page.js:3083:34) DESCRIPTION:
-[x] NAME:fix no preloading on document edit page and  Request URL http://127.0.0.1:8080/api/v1/documents/150 Request Method PUT json: cannot unmarshal string into Go struct field .cfr_citations of type []string DESCRIPTION:
-[x] NAME:fix http://localhost:3000/search TypeError: results.map is not a function DESCRIPTION:
-[x] NAME:fix http://localhost:3000/agencies/98 edit button jumps to http://localhost:3000/agencies/undefined/edit DESCRIPTION:
-[/] NAME:please loop over all pages to check if there are any /undefined jumps like previous fixes DESCRIPTION:like http://localhost:3000/documents/undefined/edit in view page of document, please check all view page for each section
-[ ] NAME:fix http://127.0.0.1:8080/api/v1/documents/149 api calls content are not filled in update page fields DESCRIPTION:
-[ ] NAME:fix http://localhost:3000/finance Key: 'FinanceRequest.Title' Error:Field validation for 'Title' failed on the 'required' tag Key: 'FinanceRequest.Type' Error:Field validation for 'Type' failed on the 'required' tag when regulation and other fields are not mark as compulsory DESCRIPTION:
-[ ] NAME:fix http://localhost:3000/proceedings/55 TypeError: Cannot read properties of undefined (reading 'toUpperCase')     at ProceedingStepCard (http://localhost:3000/_next/static/chunks/app/proceedings/%5Bid%5D/page.js:4130:22) DESCRIPTION: