-- Migration 010: Proceeding System
-- Creates tables for the formal proceeding management system
-- Implements the 7 key requirements for proceedings

-- Create proceedings table (main proceeding entity)
CREATE TABLE IF NOT EXISTS proceedings (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,

    -- Unique identification (requirement 4)
    name VARCHAR(255) NOT NULL,
    initiation_date TIMESTAMP WITH TIME ZONE NOT NULL,
    unique_id VARCHAR(500) NOT NULL UNIQUE, -- Generated: "Name YYYY-MM-DD"

    -- Basic proceeding information
    description TEXT,
    objective TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'suspended', 'completed', 'cancelled', 'under_review')),
    priority VARCHAR(50) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent', 'critical')),

    -- PRP Alignment (requirement 6)
    prp_sections TEXT, -- Comma-separated PRP section references
    prp_alignment TEXT NOT NULL, -- Description of PRP correlation
    new_prp_elements TEXT, -- New PRP elements to be developed

    -- Timing information
    planned_start_date TIMESTAMP WITH TIME ZONE,
    actual_start_date TIMESTAMP WITH TIME ZONE,
    planned_end_date TIMESTAMP WITH TIME ZONE,
    actual_end_date TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER, -- Duration in days
    actual_duration INTEGER, -- Duration in days

    -- Review requirements (requirement 5)
    review_required BOOLEAN DEFAULT TRUE,
    review_scheduled BOOLEAN DEFAULT FALSE,
    review_date TIMESTAMP WITH TIME ZONE,
    review_completed BOOLEAN DEFAULT FALSE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    review_report_id INTEGER, -- Link to review report
    critical_milestones TEXT, -- JSON array of milestone dates

    -- Existing directives consideration (requirement 7)
    existing_directives_reviewed BOOLEAN DEFAULT FALSE,
    referenced_rules TEXT, -- JSON array of rule IDs
    referenced_orders TEXT, -- JSON array of order IDs
    referenced_directives TEXT, -- JSON array of directive IDs
    conflict_analysis TEXT, -- Analysis of potential conflicts
    integration_plan TEXT, -- Plan for integrating existing frameworks

    -- IFR Integration (requirement 6)
    requires_ifr BOOLEAN DEFAULT FALSE,
    ifr_triggered BOOLEAN DEFAULT FALSE,
    ifr_description TEXT,
    ifr_document_id INTEGER, -- Link to IFR document if created

    -- Relationships
    initiated_by_id INTEGER NOT NULL REFERENCES users(id),
    owner_id INTEGER NOT NULL REFERENCES users(id),

    -- Related entities
    agency_id INTEGER REFERENCES agencies(id),
    category_id INTEGER REFERENCES categories(id),

    -- Progress tracking
    total_steps INTEGER DEFAULT 0,
    completed_steps INTEGER DEFAULT 0,
    progress_percent DECIMAL(5,2) DEFAULT 0,
    current_step_order INTEGER,

    -- Additional metadata
    tags TEXT, -- Comma-separated tags
    is_public BOOLEAN DEFAULT FALSE,
    notes TEXT,
    attachments TEXT, -- JSON array of file paths
    external_refs TEXT -- JSON array of external references
);

-- Create primary_steps table (sequential steps within proceedings)
CREATE TABLE IF NOT EXISTS primary_steps (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,

    -- Step identification and ordering (requirement 2: sequential execution)
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    step_order INTEGER NOT NULL, -- Sequential order (1, 2, 3, etc.)
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed', 'blocked', 'skipped', 'cancelled')),

    -- Sequential execution requirements (requirement 2)
    can_start_concurrently BOOLEAN DEFAULT FALSE, -- Allow concurrent start with previous step
    previous_step_required BOOLEAN DEFAULT TRUE, -- Require previous step completion
    blocks_next_step BOOLEAN DEFAULT TRUE, -- This step blocks next step until complete

    -- Step timing
    planned_start_date TIMESTAMP WITH TIME ZONE,
    actual_start_date TIMESTAMP WITH TIME ZONE,
    planned_end_date TIMESTAMP WITH TIME ZONE,
    actual_end_date TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER, -- Duration in days
    actual_duration INTEGER, -- Duration in days

    -- Step completion requirements
    completion_criteria TEXT, -- What defines completion
    completion_evidence TEXT, -- Evidence of completion
    completed_at TIMESTAMP WITH TIME ZONE,
    completed_by_id INTEGER REFERENCES users(id),

    -- Step assignment and responsibility
    assigned_to_id INTEGER REFERENCES users(id),
    owner_id INTEGER NOT NULL REFERENCES users(id),

    -- Administrative Procedure component integration (requirement 1)
    invokes_info_collection BOOLEAN DEFAULT FALSE,
    invokes_review_report BOOLEAN DEFAULT FALSE,
    invokes_orders BOOLEAN DEFAULT FALSE,
    invokes_ifr BOOLEAN DEFAULT FALSE,
    invokes_task_table BOOLEAN DEFAULT FALSE,
    component_details TEXT, -- Details of component integration

    -- Sub-task management
    allows_sub_tasks BOOLEAN DEFAULT TRUE,
    sub_tasks_required BOOLEAN DEFAULT FALSE,
    sub_tasks_count INTEGER DEFAULT 0,
    completed_sub_tasks INTEGER DEFAULT 0,

    -- Dependencies and relationships
    depends_on_step_ids TEXT, -- JSON array of step IDs this depends on
    blocks_step_ids TEXT, -- JSON array of step IDs this blocks
    related_task_ids TEXT, -- JSON array of related task IDs
    related_document_ids TEXT, -- JSON array of related document IDs
    related_regulation_ids TEXT, -- JSON array of related regulation IDs

    -- Additional metadata
    priority VARCHAR(50) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent', 'critical')),
    is_optional BOOLEAN DEFAULT FALSE,
    is_critical BOOLEAN DEFAULT FALSE,
    notes TEXT,
    attachments TEXT, -- JSON array of file paths
    external_refs TEXT, -- JSON array of external references

    -- Progress tracking
    progress_percent DECIMAL(5,2) DEFAULT 0,
    last_update_by INTEGER REFERENCES users(id),

    -- Ensure unique step order within proceeding
    UNIQUE(proceeding_id, step_order)
);

-- Create proceeding_logs table (activity and decision tracking)
CREATE TABLE IF NOT EXISTS proceeding_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,

    -- Log identification
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    log_type VARCHAR(50) NOT NULL CHECK (log_type IN ('general', 'step_update', 'status_change', 'review', 'decision', 'issue', 'resolution', 'milestone')),
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,

    -- Step-specific logging
    primary_step_id INTEGER REFERENCES primary_steps(id),
    step_order INTEGER, -- For quick reference without join

    -- Status and decision tracking
    previous_status VARCHAR(100),
    new_status VARCHAR(100),
    decision TEXT,
    rationale TEXT,
    impact TEXT,

    -- User tracking
    author_id INTEGER NOT NULL REFERENCES users(id),

    -- Related entities
    related_task_id INTEGER,
    related_document_id INTEGER,
    related_regulation_id INTEGER,
    related_agency_id INTEGER,

    -- Timing and scheduling
    event_date TIMESTAMP WITH TIME ZONE, -- When the logged event occurred
    scheduled_date TIMESTAMP WITH TIME ZONE, -- When a future event is scheduled
    due_date TIMESTAMP WITH TIME ZONE, -- When something is due

    -- Review and approval tracking
    requires_review BOOLEAN DEFAULT FALSE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by_id INTEGER REFERENCES users(id),
    approval_required BOOLEAN DEFAULT FALSE,
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by_id INTEGER REFERENCES users(id),

    -- Additional metadata
    priority VARCHAR(50) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent', 'critical')),
    is_public BOOLEAN DEFAULT FALSE,
    tags TEXT, -- Comma-separated tags
    attachments TEXT, -- JSON array of file paths
    external_refs TEXT, -- JSON array of external references
    metadata TEXT -- JSON object for additional data
);

-- Create proceeding_attachments table (file attachments for proceedings)
CREATE TABLE IF NOT EXISTS proceeding_attachments (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,

    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    description TEXT,
    uploaded_by INTEGER NOT NULL REFERENCES users(id),

    -- Attachment categorization
    attachment_type VARCHAR(50), -- "evidence", "reference", "template", "report", etc.
    is_public BOOLEAN DEFAULT FALSE,
    step_id INTEGER REFERENCES primary_steps(id) -- Associated with specific step
);

-- Create proceeding_participants table (users involved in proceedings with roles)
CREATE TABLE IF NOT EXISTS proceeding_participants (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,

    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id),

    -- Participant role and permissions
    role VARCHAR(50) NOT NULL, -- "owner", "participant", "observer", "reviewer", "approver"
    can_edit BOOLEAN DEFAULT FALSE,
    can_approve BOOLEAN DEFAULT FALSE,
    can_view BOOLEAN DEFAULT TRUE,
    can_comment BOOLEAN DEFAULT TRUE,

    -- Participation tracking
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    added_by_id INTEGER NOT NULL REFERENCES users(id),

    -- Notification preferences
    notify_on_updates BOOLEAN DEFAULT TRUE,
    notify_on_steps BOOLEAN DEFAULT TRUE,
    notify_on_review BOOLEAN DEFAULT TRUE,

    -- Ensure unique user per proceeding
    UNIQUE(proceeding_id, user_id)
);

-- Create junction tables for many-to-many relationships

-- Junction table for proceeding-task relationships
CREATE TABLE IF NOT EXISTS proceeding_tasks (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    task_id INTEGER NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "generated_by", "triggers", "blocks"
    notes TEXT,
    PRIMARY KEY (proceeding_id, task_id)
);

-- Junction table for proceeding-document relationships
CREATE TABLE IF NOT EXISTS proceeding_documents (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "source", "output", "reference"
    notes TEXT,
    PRIMARY KEY (proceeding_id, document_id)
);

-- Junction table for proceeding-regulation relationships
CREATE TABLE IF NOT EXISTS proceeding_regulations (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    regulation_id INTEGER NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "modifies", "creates", "references"
    notes TEXT,
    PRIMARY KEY (proceeding_id, regulation_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_proceedings_unique_id ON proceedings(unique_id);
CREATE INDEX IF NOT EXISTS idx_proceedings_status ON proceedings(status);
CREATE INDEX IF NOT EXISTS idx_proceedings_priority ON proceedings(priority);
CREATE INDEX IF NOT EXISTS idx_proceedings_owner_id ON proceedings(owner_id);
CREATE INDEX IF NOT EXISTS idx_proceedings_initiated_by_id ON proceedings(initiated_by_id);
CREATE INDEX IF NOT EXISTS idx_proceedings_agency_id ON proceedings(agency_id);
CREATE INDEX IF NOT EXISTS idx_proceedings_category_id ON proceedings(category_id);
CREATE INDEX IF NOT EXISTS idx_proceedings_initiation_date ON proceedings(initiation_date);
CREATE INDEX IF NOT EXISTS idx_proceedings_review_date ON proceedings(review_date);
CREATE INDEX IF NOT EXISTS idx_proceedings_deleted_at ON proceedings(deleted_at);

CREATE INDEX IF NOT EXISTS idx_primary_steps_proceeding_id ON primary_steps(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_primary_steps_step_order ON primary_steps(step_order);
CREATE INDEX IF NOT EXISTS idx_primary_steps_status ON primary_steps(status);
CREATE INDEX IF NOT EXISTS idx_primary_steps_assigned_to_id ON primary_steps(assigned_to_id);
CREATE INDEX IF NOT EXISTS idx_primary_steps_owner_id ON primary_steps(owner_id);
CREATE INDEX IF NOT EXISTS idx_primary_steps_completed_by_id ON primary_steps(completed_by_id);
CREATE INDEX IF NOT EXISTS idx_primary_steps_deleted_at ON primary_steps(deleted_at);

CREATE INDEX IF NOT EXISTS idx_proceeding_logs_proceeding_id ON proceeding_logs(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_logs_log_type ON proceeding_logs(log_type);
CREATE INDEX IF NOT EXISTS idx_proceeding_logs_author_id ON proceeding_logs(author_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_logs_primary_step_id ON proceeding_logs(primary_step_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_logs_event_date ON proceeding_logs(event_date);
CREATE INDEX IF NOT EXISTS idx_proceeding_logs_deleted_at ON proceeding_logs(deleted_at);

CREATE INDEX IF NOT EXISTS idx_proceeding_attachments_proceeding_id ON proceeding_attachments(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_attachments_uploaded_by ON proceeding_attachments(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_proceeding_attachments_step_id ON proceeding_attachments(step_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_attachments_deleted_at ON proceeding_attachments(deleted_at);

CREATE INDEX IF NOT EXISTS idx_proceeding_participants_proceeding_id ON proceeding_participants(proceeding_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_participants_user_id ON proceeding_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_proceeding_participants_role ON proceeding_participants(role);
CREATE INDEX IF NOT EXISTS idx_proceeding_participants_is_active ON proceeding_participants(is_active);
CREATE INDEX IF NOT EXISTS idx_proceeding_participants_deleted_at ON proceeding_participants(deleted_at);

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_proceedings_updated_at BEFORE UPDATE ON proceedings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_primary_steps_updated_at BEFORE UPDATE ON primary_steps
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proceeding_logs_updated_at BEFORE UPDATE ON proceeding_logs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proceeding_attachments_updated_at BEFORE UPDATE ON proceeding_attachments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proceeding_participants_updated_at BEFORE UPDATE ON proceeding_participants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically generate unique_id for proceedings
CREATE OR REPLACE FUNCTION generate_proceeding_unique_id()
RETURNS TRIGGER AS $$
BEGIN
    NEW.unique_id = NEW.name || ' ' || TO_CHAR(NEW.initiation_date, 'YYYY-MM-DD');
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER generate_proceeding_unique_id_trigger BEFORE INSERT ON proceedings
    FOR EACH ROW EXECUTE FUNCTION generate_proceeding_unique_id();

-- Create function to validate minimum 5 steps requirement (requirement 3)
CREATE OR REPLACE FUNCTION validate_proceeding_minimum_steps()
RETURNS TRIGGER AS $$
DECLARE
    step_count INTEGER;
BEGIN
    -- Only validate when proceeding status changes to 'active'
    IF NEW.status = 'active' AND (OLD.status IS NULL OR OLD.status != 'active') THEN
        SELECT COUNT(*) INTO step_count
        FROM primary_steps
        WHERE proceeding_id = NEW.id AND deleted_at IS NULL;

        IF step_count < 5 THEN
            RAISE EXCEPTION 'Proceeding must have at least 5 primary steps before activation (requirement 3). Current count: %', step_count;
        END IF;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER validate_proceeding_minimum_steps_trigger BEFORE UPDATE ON proceedings
    FOR EACH ROW EXECUTE FUNCTION validate_proceeding_minimum_steps();

-- Create function to update proceeding progress when steps change
CREATE OR REPLACE FUNCTION update_proceeding_progress()
RETURNS TRIGGER AS $$
DECLARE
    total_count INTEGER;
    completed_count INTEGER;
    progress_pct DECIMAL(5,2);
    current_step INTEGER;
BEGIN
    -- Get proceeding ID from the step
    IF TG_OP = 'DELETE' THEN
        -- Count steps for the deleted step's proceeding
        SELECT COUNT(*), COUNT(*) FILTER (WHERE status = 'completed')
        INTO total_count, completed_count
        FROM primary_steps
        WHERE proceeding_id = OLD.proceeding_id AND deleted_at IS NULL AND id != OLD.id;
    ELSE
        -- Count steps for the current step's proceeding
        SELECT COUNT(*), COUNT(*) FILTER (WHERE status = 'completed')
        INTO total_count, completed_count
        FROM primary_steps
        WHERE proceeding_id = NEW.proceeding_id AND deleted_at IS NULL;
    END IF;

    -- Calculate progress percentage
    IF total_count > 0 THEN
        progress_pct = (completed_count::DECIMAL / total_count::DECIMAL) * 100;
    ELSE
        progress_pct = 0;
    END IF;

    -- Find current step (first non-completed step)
    SELECT step_order INTO current_step
    FROM primary_steps
    WHERE proceeding_id = COALESCE(NEW.proceeding_id, OLD.proceeding_id)
      AND status NOT IN ('completed', 'skipped', 'cancelled')
      AND deleted_at IS NULL
    ORDER BY step_order ASC
    LIMIT 1;

    -- Update proceeding progress
    IF TG_OP = 'DELETE' THEN
        UPDATE proceedings
        SET total_steps = total_count,
            completed_steps = completed_count,
            progress_percent = progress_pct,
            current_step_order = current_step,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = OLD.proceeding_id;
    ELSE
        UPDATE proceedings
        SET total_steps = total_count,
            completed_steps = completed_count,
            progress_percent = progress_pct,
            current_step_order = current_step,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = NEW.proceeding_id;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER update_proceeding_progress_trigger
    AFTER INSERT OR UPDATE OR DELETE ON primary_steps
    FOR EACH ROW EXECUTE FUNCTION update_proceeding_progress();
