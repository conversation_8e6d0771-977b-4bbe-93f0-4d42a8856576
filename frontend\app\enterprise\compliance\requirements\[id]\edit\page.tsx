'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { complianceApi } from '../../../../../services/enterpriseApi';
import { ComplianceRequirement } from '../../../../../types/enterprise';

const EditComplianceRequirementPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const requirementId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<ComplianceRequirement>>({
    requirement_code: '',
    requirement_name: '',
    title: '',
    description: '',
    category: '',
    subcategory: '',
    regulatory_framework: '',
    authority: '',
    priority: 'medium',
    status: 'active',
    effective_date: '',
    due_date: '',
    review_frequency: 'annual',
    next_review_date: '',
    compliance_criteria: '',
    evidence_requirements: '',
    responsible_party: '',
    approval_required: false,
    automated_monitoring: false,
    risk_level: 'medium',
    business_impact: 'medium',
    implementation_cost: 0,
    penalty_amount: 0,
    currency_code: 'USD',
    tags: '',
    metadata: ''
  });

  useEffect(() => {
    if (requirementId) {
      fetchRequirement();
    }
  }, [requirementId]);

  const fetchRequirement = async () => {
    try {
      setFetchLoading(true);
      const response = await complianceApi.getRequirement(requirementId);
      const requirement = response.data;
      setFormData({
        ...requirement,
        effective_date: requirement.effective_date?.split('T')[0] || '',
        due_date: requirement.due_date?.split('T')[0] || '',
        next_review_date: requirement.next_review_date?.split('T')[0] || '',
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch compliance requirement');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await complianceApi.updateRequirement(requirementId, formData);
      router.push(`/enterprise/compliance/requirements/${requirementId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update compliance requirement');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  if (fetchLoading) return <div className="p-6">Loading compliance requirement...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Compliance Requirement</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/compliance/requirements/${requirementId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Requirement Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Requirement Code *
            </label>
            <input
              type="text"
              name="requirement_code"
              value={formData.requirement_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., REQ-2025-001"
            />
          </div>

          {/* Requirement Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Requirement Name *
            </label>
            <input
              type="text"
              name="requirement_name"
              value={formData.requirement_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Data Privacy Compliance"
            />
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., GDPR Article 32 - Security of Processing"
            />
          </div>

          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category *
            </label>
            <select
              name="category"
              value={formData.category}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select Category</option>
              <option value="financial">Financial</option>
              <option value="data_privacy">Data Privacy</option>
              <option value="security">Security</option>
              <option value="environmental">Environmental</option>
              <option value="health_safety">Health & Safety</option>
              <option value="employment">Employment</option>
              <option value="tax">Tax</option>
              <option value="industry_specific">Industry Specific</option>
            </select>
          </div>

          {/* Subcategory */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Subcategory
            </label>
            <input
              type="text"
              name="subcategory"
              value={formData.subcategory}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., GDPR, SOX, HIPAA"
            />
          </div>

          {/* Framework */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Framework *
            </label>
            <input
              type="text"
              name="framework"
              value={formData.framework}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., GDPR, SOX, HIPAA"
            />
          </div>

          {/* Regulatory Framework */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Regulatory Framework
            </label>
            <input
              type="text"
              name="regulatory_framework"
              value={formData.regulatory_framework}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., GDPR, SOX, HIPAA"
            />
          </div>

          {/* Authority */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Authority
            </label>
            <input
              type="text"
              name="authority"
              value={formData.authority}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., SEC, FDA, EPA"
            />
          </div>

          {/* Priority */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Priority *
            </label>
            <select
              name="priority"
              value={formData.priority}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Risk Level */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Risk Level
            </label>
            <select
              name="risk_level"
              value={formData.risk_level}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Business Impact */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Business Impact
            </label>
            <select
              name="business_impact"
              value={formData.business_impact}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
              <option value="pending">Pending</option>
              <option value="expired">Expired</option>
            </select>
          </div>

          {/* Effective Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Effective Date
            </label>
            <input
              type="date"
              name="effective_date"
              value={formData.effective_date}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Due Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Due Date
            </label>
            <input
              type="date"
              name="due_date"
              value={formData.due_date}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Review Frequency */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Review Frequency
            </label>
            <select
              name="review_frequency"
              value={formData.review_frequency}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="semi_annual">Semi-Annual</option>
              <option value="annual">Annual</option>
              <option value="biennial">Biennial</option>
            </select>
          </div>

          {/* Next Review Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Next Review Date
            </label>
            <input
              type="date"
              name="next_review_date"
              value={formData.next_review_date}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Implementation Cost */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Implementation Cost
            </label>
            <input
              type="number"
              name="implementation_cost"
              value={formData.implementation_cost}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Penalty Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Penalty Amount
            </label>
            <input
              type="number"
              name="penalty_amount"
              value={formData.penalty_amount}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Currency Code
            </label>
            <input
              type="text"
              name="currency_code"
              value={formData.currency_code}
              onChange={handleChange}
              maxLength={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="USD"
            />
          </div>

          {/* Responsible Party */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Responsible Party
            </label>
            <input
              type="text"
              name="responsible_party"
              value={formData.responsible_party}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Legal Department, IT Security"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Requirement description..."
          />
        </div>

        {/* Compliance Criteria */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Compliance Criteria
          </label>
          <textarea
            name="compliance_criteria"
            value={formData.compliance_criteria}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Specific criteria that must be met..."
          />
        </div>

        {/* Evidence Requirements */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Evidence Requirements
          </label>
          <textarea
            name="evidence_requirements"
            value={formData.evidence_requirements}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Documentation and evidence required..."
          />
        </div>

        {/* Checkboxes */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="approval_required"
              checked={formData.approval_required}
              onChange={handleChange}
              className="mr-2"
            />
            Approval Required
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="automated_monitoring"
              checked={formData.automated_monitoring}
              onChange={handleChange}
              className="mr-2"
            />
            Automated Monitoring
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/compliance/requirements/${requirementId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Requirement'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditComplianceRequirementPage;
