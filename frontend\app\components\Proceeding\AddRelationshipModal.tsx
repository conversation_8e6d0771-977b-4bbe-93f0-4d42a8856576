import React, { useState, useEffect } from 'react';
import { XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import apiService from '../../services/api';

interface AddRelationshipModalProps {
  type: 'task' | 'document' | 'regulation';
  proceedingId: number;
  onClose: () => void;
  onSuccess: () => void;
}

interface EntityOption {
  id: number;
  title: string;
  status?: string;
  type?: string;
  agency?: string;
}

const AddRelationshipModal: React.FC<AddRelationshipModalProps> = ({
  type,
  proceedingId,
  onClose,
  onSuccess
}) => {
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [entities, setEntities] = useState<EntityOption[]>([]);
  const [selectedEntityId, setSelectedEntityId] = useState<number | null>(null);
  const [relationshipType, setRelationshipType] = useState('related');
  const [notes, setNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);

  // Load entities based on type
  useEffect(() => {
    loadEntities();
  }, [type, searchTerm]);

  const loadEntities = async () => {
    try {
      setLoading(true);
      let response;
      
      const params = {
        page: 1,
        per_page: 50,
        search: searchTerm
      };

      switch (type) {
        case 'task':
          response = await apiService.getTasks(params);
          setEntities(response.data.map((task: any) => ({
            id: task.id,
            title: task.title,
            status: task.status,
            type: task.type
          })));
          break;
        case 'document':
          response = await apiService.getDocuments(params);
          setEntities(response.data.map((doc: any) => ({
            id: doc.id,
            title: doc.title,
            status: doc.status,
            type: doc.type,
            agency: doc.agency?.name
          })));
          break;
        case 'regulation':
          response = await apiService.getRegulations(params);
          setEntities(response.data.map((reg: any) => ({
            id: reg.id,
            title: reg.title,
            status: reg.status,
            type: reg.type
          })));
          break;
      }
    } catch (error) {
      console.error(`Error loading ${type}s:`, error);
      setEntities([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedEntityId) return;

    try {
      setSubmitting(true);
      
      const token = localStorage.getItem('federal_register_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`/api/v1/proceedings/${proceedingId}/link-${type}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          [`${type}_id`]: selectedEntityId,
          relationship_type: relationshipType,
          notes
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      onSuccess();
    } catch (error) {
      console.error(`Error adding ${type} relationship:`, error);
      alert(`Failed to add ${type} relationship. Please try again.`);
    } finally {
      setSubmitting(false);
    }
  };

  const relationshipTypes = [
    { value: 'related', label: 'Related' },
    { value: 'reference', label: 'Reference' },
    { value: 'supports', label: 'Supports' },
    { value: 'modifies', label: 'Modifies' },
    { value: 'implements', label: 'Implements' },
    { value: 'supersedes', label: 'Supersedes' }
  ];

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Link {type.charAt(0).toUpperCase() + type.slice(1)} to Proceeding
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search {type}s
            </label>
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder={`Search for ${type}s...`}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          {/* Entity Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select {type}
            </label>
            <div className="max-h-64 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
              {loading ? (
                <div className="p-4 text-center text-gray-500">Loading...</div>
              ) : entities.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  No {type}s found. Try adjusting your search.
                </div>
              ) : (
                entities.map((entity) => (
                  <div
                    key={entity.id}
                    className={`p-3 border-b border-gray-200 dark:border-gray-600 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 ${
                      selectedEntityId === entity.id ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                    }`}
                    onClick={() => setSelectedEntityId(entity.id)}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        checked={selectedEntityId === entity.id}
                        onChange={() => setSelectedEntityId(entity.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <div className="ml-3 flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {entity.title}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {entity.status && `Status: ${entity.status}`}
                          {entity.type && ` • Type: ${entity.type}`}
                          {entity.agency && ` • Agency: ${entity.agency}`}
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Relationship Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Relationship Type
            </label>
            <select
              value={relationshipType}
              onChange={(e) => setRelationshipType(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            >
              {relationshipTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notes (optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              placeholder="Add any additional notes about this relationship..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!selectedEntityId || submitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? 'Adding...' : 'Add Relationship'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddRelationshipModal;
