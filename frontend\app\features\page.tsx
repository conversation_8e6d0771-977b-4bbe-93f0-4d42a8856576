'use client'

import React from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  ClockIcon,
  GlobeAltIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  CalendarIcon,
  BellIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  BookOpenIcon,
  CogIcon,
  CloudIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';

const FeaturesPage: React.FC = () => {
  const coreFeatures = [
    {
      icon: DocumentTextIcon,
      title: 'Document Management',
      description: 'Complete document lifecycle management with version control, collaborative editing, and automated workflows.',
      features: [
        'Markdown-based document editing',
        'Version control and history tracking',
        'Collaborative editing and comments',
        'Automated approval workflows',
        'Document templates and standards',
        'Bulk operations and batch processing'
      ]
    },
    {
      icon: BuildingOfficeIcon,
      title: 'Agency Management',
      description: 'Comprehensive agency profiles with contact management, document associations, and organizational hierarchy.',
      features: [
        'Detailed agency profiles and information',
        'Contact management and directories',
        'Organizational hierarchy mapping',
        'Agency-specific document collections',
        'Logo and branding management',
        'Integration with federal databases'
      ]
    },
    {
      icon: TagIcon,
      title: 'Category System',
      description: 'Hierarchical categorization system with CFR integration and flexible taxonomy management.',
      features: [
        'Hierarchical category structure',
        'CFR title integration',
        'Custom taxonomy creation',
        'Automated categorization suggestions',
        'Cross-reference capabilities',
        'Category-based permissions'
      ]
    },
    {
      icon: UserGroupIcon,
      title: 'Role-Based Access Control',
      description: 'Granular permission system with customizable roles and secure access management.',
      features: [
        'Multiple user roles (Admin, Editor, Viewer)',
        'Granular permission settings',
        'Department-specific access controls',
        'Audit trails for all actions',
        'Single sign-on (SSO) integration',
        'Multi-factor authentication'
      ]
    }
  ];

  const advancedFeatures = [
    {
      icon: CalendarIcon,
      title: 'Calendar & Scheduling',
      description: 'Integrated calendar system for tracking deadlines, hearings, and important dates.',
      features: [
        'Document effective date tracking',
        'Public hearing scheduling',
        'Comment period management',
        'Deadline notifications',
        'Agency availability tracking',
        'Automated reminders'
      ]
    },
    {
      icon: BookOpenIcon,
      title: 'Regulations Management',
      description: 'Federal Register-style regulation management with hierarchical structure and versioning.',
      features: [
        'CFR chapter/title organization',
        'Regulation versioning and history',
        'Cross-reference linking',
        'Hierarchical navigation',
        'Amendment tracking',
        'Superseded regulation handling'
      ]
    },
    {
      icon: MagnifyingGlassIcon,
      title: 'Advanced Search',
      description: 'Powerful search capabilities with full-text indexing and intelligent filtering.',
      features: [
        'Full-text search across all content',
        'Advanced filtering options',
        'Faceted search interface',
        'Search result highlighting',
        'Saved searches and alerts',
        'API-based search integration'
      ]
    },
    {
      icon: BellIcon,
      title: 'Notifications & Alerts',
      description: 'Comprehensive notification system for keeping stakeholders informed.',
      features: [
        'Email and in-app notifications',
        'Customizable alert preferences',
        'Document change notifications',
        'Deadline and reminder alerts',
        'Subscription management',
        'Bulk notification capabilities'
      ]
    }
  ];

  const technicalFeatures = [
    {
      icon: CloudIcon,
      title: 'Cloud-Native Architecture',
      description: 'Modern, scalable architecture built for reliability and performance.'
    },
    {
      icon: LockClosedIcon,
      title: 'Enterprise Security',
      description: 'Bank-level security with encryption, audit trails, and compliance features.'
    },
    {
      icon: CogIcon,
      title: 'API Integration',
      description: 'RESTful APIs for seamless integration with existing government systems.'
    },
    {
      icon: ChartBarIcon,
      title: 'Analytics & Reporting',
      description: 'Comprehensive analytics dashboard with customizable reports and insights.'
    }
  ];

  return (
    <Layout>
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
        <div className="container-custom py-16 lg:py-24">
          <div className="text-center">
            <h1 className="text-4xl lg:text-6xl font-bold mb-6">
              Powerful Features
            </h1>
            <p className="text-xl lg:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
              Everything you need to manage federal documents, regulations, and compliance 
              in one comprehensive platform.
            </p>
            <Link
              href="/register"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors duration-200"
            >
              Get Started Today
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </Link>
          </div>
        </div>
      </div>

      {/* Core Features */}
      <div className="py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Core Features</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Essential tools for federal document management and regulatory compliance.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {coreFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-8 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center mb-6">
                  <div className="flex-shrink-0">
                    <feature.icon className="h-8 w-8 text-primary-600" />
                  </div>
                  <h3 className="ml-3 text-xl font-semibold text-gray-900">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-600 mb-6">
                  {feature.description}
                </p>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center text-sm text-gray-700">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Advanced Features */}
      <div className="bg-gray-50 py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Advanced Capabilities</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Sophisticated features for complex regulatory environments and large-scale operations.
            </p>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {advancedFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-8 hover:shadow-lg transition-shadow duration-200">
                <div className="flex items-center mb-6">
                  <div className="flex-shrink-0">
                    <feature.icon className="h-8 w-8 text-primary-600" />
                  </div>
                  <h3 className="ml-3 text-xl font-semibold text-gray-900">
                    {feature.title}
                  </h3>
                </div>
                <p className="text-gray-600 mb-6">
                  {feature.description}
                </p>
                <ul className="space-y-2">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-center text-sm text-gray-700">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                      {item}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Technical Features */}
      <div className="py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Technical Excellence</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Built on modern technology stack with enterprise-grade security and scalability.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {technicalFeatures.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="h-8 w-8 text-primary-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Integration Section */}
      <div className="bg-gray-50 py-16">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Seamless Integration</h2>
            <p className="text-lg text-gray-600 mb-8">
              Our platform integrates with existing government systems and third-party tools 
              to provide a unified document management experience.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white rounded-lg shadow-md p-6">
                <GlobeAltIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Federal Systems</h3>
                <p className="text-sm text-gray-600">Integration with existing federal databases and systems</p>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <CogIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Third-Party Tools</h3>
                <p className="text-sm text-gray-600">Connect with popular productivity and collaboration tools</p>
              </div>
              
              <div className="bg-white rounded-lg shadow-md p-6">
                <ShieldCheckIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">Compliance Systems</h3>
                <p className="text-sm text-gray-600">Automated compliance checking and regulatory updates</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="bg-primary-600 py-16">
        <div className="container-custom text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Experience These Features?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Join federal agencies already using our platform to streamline their 
            document management and regulatory processes.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/register"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-gray-50 transition-colors duration-200"
            >
              Start Free Trial
              <ArrowRightIcon className="h-5 w-5 ml-2" />
            </Link>
            <Link
              href="/about"
              className="inline-flex items-center px-6 py-3 border border-white text-base font-medium rounded-md text-white bg-transparent hover:bg-white hover:text-primary-600 transition-colors duration-200"
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FeaturesPage;
