package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

var notificationService *services.NotificationService

// InitializeHRNotificationService initializes the HR notification service
func InitializeHRNotificationService(cfg *config.Config) {
	db := database.GetDB()
	if db != nil {
		notificationService = services.NewNotificationService(db, cfg)
	}
}

// DeleteEmployee deletes an employee
func DeleteEmployee(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid employee ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var employee models.Employee
	if err := db.First(&employee, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Employee not found"})
		return
	}

	if err := db.Delete(&employee).Error; err != nil {
		HandleInternalError(c, "Failed to delete employee: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Employee deleted successfully"})
}

// Department Handlers

// GetDepartments returns all departments with pagination
func GetDepartments(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total departments
	var total int64
	db.Model(&models.Department{}).Count(&total)

	// Get departments with pagination
	var departments []models.Department
	offset := (page - 1) * perPage
	if err := db.Preload("Manager").Preload("CostCenter").
		Offset(offset).
		Limit(perPage).
		Order("department_name ASC").
		Find(&departments).Error; err != nil {
		HandleInternalError(c, "Failed to fetch departments: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"departments": departments,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateDepartment creates a new department
func CreateDepartment(c *gin.Context) {
	var req DepartmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create department
	department := models.Department{
		DepartmentCode: req.DepartmentCode,
		Name:           req.DepartmentName,
		Description:    req.Description,
		ManagerID:      req.ManagerID,
		BudgetAmount:   req.BudgetAmount,
		ActualSpend:    req.ActualSpend,
		CostCenterID:   req.CostCenterID,
		Location:       req.Location,
		OfficeSpace:    req.OfficeSpace,
		IsActive:       req.IsActive,
		EmployeeCount:  req.EmployeeCount,
		Metadata:       req.Metadata,
	}

	if err := db.Create(&department).Error; err != nil {
		HandleInternalError(c, "Failed to create department: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Manager").Preload("CostCenter").First(&department, department.ID)

	c.JSON(http.StatusCreated, gin.H{"department": department})
}

// Position Handlers

// GetPositions returns all positions with pagination
func GetPositions(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total positions
	var total int64
	db.Model(&models.Position{}).Count(&total)

	// Get positions with pagination
	var positions []models.Position
	offset := (page - 1) * perPage
	if err := db.Preload("Department").Preload("ReportsTo").
		Offset(offset).
		Limit(perPage).
		Order("position_title ASC").
		Find(&positions).Error; err != nil {
		HandleInternalError(c, "Failed to fetch positions: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"positions": positions,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreatePosition creates a new position
func CreatePosition(c *gin.Context) {
	var req PositionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create position
	position := models.Position{
		PositionCode:   req.PositionCode,
		Title:          req.PositionTitle,
		Description:    req.Description,
		Level:          req.Level,
		Grade:          req.Grade,
		JobFamily:      req.JobFamily,
		DepartmentID:   req.DepartmentID,
		ReportsToID:    req.ReportsToID,
		MinSalary:      req.MinSalary,
		MaxSalary:      req.MaxSalary,
		Skills:         req.RequiredSkills,
		IsActive:       req.IsActive,
		IsApproved:     req.IsApproved,
		HeadcountLimit: req.HeadcountLimit,
		CurrentCount:   req.CurrentCount,
		Metadata:       req.Metadata,
	}

	if err := db.Create(&position).Error; err != nil {
		HandleInternalError(c, "Failed to create position: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Department").Preload("ReportsTo").First(&position, position.ID)

	c.JSON(http.StatusCreated, gin.H{"position": position})
}

// Performance Review Handlers

// GetPerformanceReviews returns all performance reviews with pagination
func GetPerformanceReviews(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total reviews
	var total int64
	db.Model(&models.PerformanceReview{}).Count(&total)

	// Get reviews with pagination
	var reviews []models.PerformanceReview
	offset := (page - 1) * perPage
	if err := db.Preload("Employee").Preload("Reviewer").
		Offset(offset).
		Limit(perPage).
		Order("created_at DESC").
		Find(&reviews).Error; err != nil {
		HandleInternalError(c, "Failed to fetch performance reviews: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"reviews": reviews,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreatePerformanceReview creates a new performance review
func CreatePerformanceReview(c *gin.Context) {
	var req PerformanceReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create performance review
	review := models.PerformanceReview{
		EmployeeID:          req.EmployeeID,
		ReviewerID:          req.ReviewerID,
		ReviewPeriod:        req.ReviewPeriod,
		ReviewType:          req.ReviewType,
		Goals:               req.Goals,
		Achievements:        req.Achievements,
		AreasForImprovement: req.AreasImprovement,
		OverallRating:       req.OverallRating,
		ManagerComments:     req.Comments,
		Metadata:            req.Metadata,
	}

	if err := db.Create(&review).Error; err != nil {
		HandleInternalError(c, "Failed to create performance review: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Employee").Preload("Reviewer").First(&review, review.ID)

	c.JSON(http.StatusCreated, gin.H{"review": review})
}

// Training Handlers

// GetTrainingPrograms returns all training programs with pagination
func GetTrainingPrograms(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total training programs
	var total int64
	db.Model(&models.Training{}).Count(&total)

	// Get training programs with pagination
	var programs []models.Training
	offset := (page - 1) * perPage
	if err := db.Offset(offset).
		Limit(perPage).
		Order("start_date DESC").
		Find(&programs).Error; err != nil {
		HandleInternalError(c, "Failed to fetch training programs: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"programs": programs,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateTrainingProgram creates a new training program
func CreateTrainingProgram(c *gin.Context) {
	var req TrainingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create training program
	program := models.Training{
		Title:           req.TrainingName,
		Description:     req.Description,
		TrainingType:    req.TrainingType,
		Category:        req.Category,
		Duration:        req.Duration,
		StartDate:       &req.StartDate,
		EndDate:         &req.EndDate,
		Provider:        req.InstructorName,
		MaxParticipants: req.MaxParticipants,
		Prerequisites:   req.Prerequisites,
		Materials:       req.Materials,
		Metadata:        req.Metadata,
	}

	if err := db.Create(&program).Error; err != nil {
		HandleInternalError(c, "Failed to create training program: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, gin.H{"program": program})
}

// TerminateEmployee terminates an employee
func TerminateEmployee(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid employee ID"})
		return
	}

	var req struct {
		TerminationDate   time.Time `json:"termination_date" binding:"required"`
		TerminationReason string    `json:"termination_reason" binding:"required"`
		Notes             string    `json:"notes"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var employee models.Employee
	if err := db.First(&employee, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Employee not found"})
		return
	}

	// Update employee status and termination details
	employee.EmploymentStatus = "terminated"
	employee.TerminationDate = &req.TerminationDate
	employee.TerminationReason = req.TerminationReason

	if err := db.Save(&employee).Error; err != nil {
		HandleInternalError(c, "Failed to terminate employee: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Department").Preload("Manager").First(&employee, employee.ID)

	c.JSON(http.StatusOK, gin.H{"employee": employee, "message": "Employee terminated successfully"})
}

// ApprovePosition approves a position
func ApprovePosition(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid position ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var position models.Position
	if err := db.First(&position, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Position not found"})
		return
	}

	// Mark as approved
	position.IsApproved = true

	if err := db.Save(&position).Error; err != nil {
		HandleInternalError(c, "Failed to approve position: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Department").Preload("ReportsTo").First(&position, position.ID)

	c.JSON(http.StatusOK, gin.H{"position": position, "message": "Position approved successfully"})
}

// GetTrainings returns all training programs (alias for GetTrainingPrograms)
func GetTrainings(c *gin.Context) {
	GetTrainingPrograms(c)
}

// CreateTraining creates a new training program (alias for CreateTrainingProgram)
func CreateTraining(c *gin.Context) {
	CreateTrainingProgram(c)
}

// EnrollInTraining enrolls an employee in a training program
func EnrollInTraining(c *gin.Context) {
	trainingID, err := strconv.ParseUint(c.Param("trainingId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid training ID"})
		return
	}

	var req struct {
		EmployeeID uint `json:"employee_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if training exists
	var training models.Training
	if err := db.First(&training, uint(trainingID)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Training program not found"})
		return
	}

	// Check if employee exists
	var employee models.Employee
	if err := db.First(&employee, req.EmployeeID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Employee not found"})
		return
	}

	// Create training enrollment
	enrollment := models.TrainingEnrollment{
		EmployeeID:   req.EmployeeID,
		TrainingID:   uint(trainingID),
		EnrolledAt:   time.Now(),
		EnrolledByID: req.EmployeeID, // Self-enrollment or by HR admin
		Status:       "enrolled",
	}

	if err := db.Create(&enrollment).Error; err != nil {
		HandleInternalError(c, "Failed to enroll in training: "+err.Error())
		return
	}

	// Update training enrollment count
	training.EnrollmentCount++
	db.Save(&training)

	// Load relationships
	db.Preload("Employee").Preload("Training").First(&enrollment, enrollment.ID)

	// Send enrollment notification
	if notificationService != nil {
		err := notificationService.SendTrainingEnrollmentNotification(
			req.EmployeeID,
			training.Title,
			"enrolled",
		)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Failed to send enrollment notification: %v\n", err)
		}
	}

	c.JSON(http.StatusCreated, gin.H{"enrollment": enrollment})
}

// GetTrainingEnrollments returns all training enrollments
func GetTrainingEnrollments(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total enrollments
	var total int64
	db.Model(&models.TrainingEnrollment{}).Count(&total)

	// Get enrollments with pagination
	var enrollments []models.TrainingEnrollment
	offset := (page - 1) * perPage
	if err := db.Preload("Employee").Preload("Training").
		Offset(offset).
		Limit(perPage).
		Order("enrollment_date DESC").
		Find(&enrollments).Error; err != nil {
		HandleInternalError(c, "Failed to fetch training enrollments: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"enrollments": enrollments,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CompleteTraining marks a training enrollment as completed
func CompleteTraining(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid enrollment ID"})
		return
	}

	var req struct {
		Score     float64 `json:"score"`
		Feedback  string  `json:"feedback"`
		Rating    float64 `json:"rating"`
		IssueCert bool    `json:"issue_certificate"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var enrollment models.TrainingEnrollment
	if err := db.First(&enrollment, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Training enrollment not found"})
		return
	}

	// Mark as completed
	now := time.Now()
	enrollment.Status = "completed"
	enrollment.CompletedAt = &now
	enrollment.Score = req.Score
	enrollment.Feedback = req.Feedback
	enrollment.Rating = req.Rating
	enrollment.ProgressPercent = 100

	if req.IssueCert && req.Score >= enrollment.PassingScore {
		enrollment.CertificateIssued = true
		enrollment.CertificateNumber = "CERT-" + time.Now().Format("20060102150405")
	}

	if err := db.Save(&enrollment).Error; err != nil {
		HandleInternalError(c, "Failed to complete training: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Employee").Preload("Training").First(&enrollment, enrollment.ID)

	// Send completion notification
	if notificationService != nil {
		err := notificationService.SendTrainingEnrollmentNotification(
			enrollment.EmployeeID,
			enrollment.Training.Title,
			"completed",
		)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Failed to send completion notification: %v\n", err)
		}
	}

	c.JSON(http.StatusOK, gin.H{"enrollment": enrollment, "message": "Training completed successfully"})
}

// GetHRDashboard returns HR dashboard statistics
func GetHRDashboard(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get employee statistics
	var totalEmployees int64
	var activeEmployees int64
	var terminatedEmployees int64

	db.Model(&models.Employee{}).Count(&totalEmployees)
	db.Model(&models.Employee{}).Where("employment_status = ?", "active").Count(&activeEmployees)
	db.Model(&models.Employee{}).Where("employment_status = ?", "terminated").Count(&terminatedEmployees)

	// Get department statistics
	var totalDepartments int64
	db.Model(&models.Department{}).Count(&totalDepartments)

	// Get training statistics
	var totalTrainings int64
	var activeTrainings int64
	var totalEnrollments int64
	var completedEnrollments int64

	db.Model(&models.Training{}).Count(&totalTrainings)
	db.Model(&models.Training{}).Where("status = ?", "open").Count(&activeTrainings)
	db.Model(&models.TrainingEnrollment{}).Count(&totalEnrollments)
	db.Model(&models.TrainingEnrollment{}).Where("status = ?", "completed").Count(&completedEnrollments)

	// Get performance review statistics
	var totalReviews int64
	var pendingReviews int64

	db.Model(&models.PerformanceReview{}).Count(&totalReviews)
	db.Model(&models.PerformanceReview{}).Where("status = ?", "pending").Count(&pendingReviews)

	// Calculate completion rate
	var completionRate float64
	if totalEnrollments > 0 {
		completionRate = float64(completedEnrollments) / float64(totalEnrollments) * 100
	}

	dashboard := gin.H{
		"employees": gin.H{
			"total":      totalEmployees,
			"active":     activeEmployees,
			"terminated": terminatedEmployees,
		},
		"departments": gin.H{
			"total": totalDepartments,
		},
		"training": gin.H{
			"total_programs":    totalTrainings,
			"active_programs":   activeTrainings,
			"total_enrollments": totalEnrollments,
			"completed":         completedEnrollments,
			"completion_rate":   completionRate,
		},
		"performance_reviews": gin.H{
			"total":   totalReviews,
			"pending": pendingReviews,
		},
	}

	c.JSON(http.StatusOK, gin.H{"dashboard": dashboard})
}
