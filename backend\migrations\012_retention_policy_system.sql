-- Document Retention Policy System Migration
-- This migration creates tables for automated document retention policies

-- Retention Policies Table
CREATE TABLE IF NOT EXISTS retention_policies (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Policy identification
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'active',
    
    -- Retention rules
    retention_period_days INTEGER DEFAULT 0,
    retention_period_years INTEGER DEFAULT 0,
    action VARCHAR(50) DEFAULT 'archive',
    auto_execute BOOLEAN DEFAULT FALSE,
    
    -- Trigger conditions
    trigger_event VARCHAR(255),
    trigger_document_type VARCHAR(255),
    trigger_category VARCHAR(255),
    trigger_agency VARCHAR(255),
    
    -- Legal hold settings
    legal_hold_enabled BOOLEAN DEFAULT FALSE,
    legal_hold_reason TEXT,
    legal_hold_start_date TIMESTAMP WITH TIME ZONE,
    legal_hold_end_date TIMESTAMP WITH TIME ZONE,
    legal_hold_contact VARCHAR(255),
    
    -- Compliance settings
    regulatory_framework VARCHAR(255),
    compliance_notes TEXT,
    audit_required BOOLEAN DEFAULT FALSE,
    
    -- Notification settings
    notify_before_days INTEGER DEFAULT 30,
    notification_emails TEXT,
    escalation_emails TEXT,
    
    -- Advanced rules
    custom_rules TEXT,
    exception_rules TEXT,
    
    -- Policy ownership
    created_by_id INTEGER NOT NULL REFERENCES users(id),
    owner_id INTEGER NOT NULL REFERENCES users(id),
    
    -- Approval workflow
    requires_approval BOOLEAN DEFAULT TRUE,
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by_id INTEGER REFERENCES users(id),
    
    -- Execution tracking
    last_executed_at TIMESTAMP WITH TIME ZONE,
    next_execution_at TIMESTAMP WITH TIME ZONE,
    execution_count INTEGER DEFAULT 0,
    
    -- Statistics
    documents_affected INTEGER DEFAULT 0,
    documents_archived INTEGER DEFAULT 0,
    documents_deleted INTEGER DEFAULT 0
);

-- Retention Policy Assignments Table
CREATE TABLE IF NOT EXISTS retention_policy_assignments (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    policy_id INTEGER NOT NULL REFERENCES retention_policies(id) ON DELETE CASCADE,
    
    -- Assignment target (one of these will be set)
    document_id INTEGER REFERENCES documents(id),
    category_id INTEGER REFERENCES categories(id),
    agency_id INTEGER REFERENCES agencies(id),
    
    -- Assignment details
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    effective_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expiration_date TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Override settings
    override_retention_days INTEGER,
    override_action VARCHAR(50),
    override_reason TEXT,
    
    -- Assignment metadata
    assigned_by_id INTEGER NOT NULL REFERENCES users(id),
    notes TEXT
);

-- Retention Execution Logs Table
CREATE TABLE IF NOT EXISTS retention_execution_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    policy_id INTEGER NOT NULL REFERENCES retention_policies(id) ON DELETE CASCADE,
    
    -- Execution details
    execution_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    execution_type VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    status VARCHAR(50) NOT NULL,
    
    -- Results
    documents_processed INTEGER DEFAULT 0,
    documents_archived INTEGER DEFAULT 0,
    documents_deleted INTEGER DEFAULT 0,
    documents_skipped INTEGER DEFAULT 0,
    error_count INTEGER DEFAULT 0,
    
    -- Execution context
    executed_by_id INTEGER REFERENCES users(id),
    trigger_event VARCHAR(255),
    
    -- Detailed results
    processed_documents TEXT,
    error_details TEXT,
    execution_summary TEXT,
    
    -- Timing
    start_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    duration_ms INTEGER DEFAULT 0
);

-- Document Retention Status Table
CREATE TABLE IF NOT EXISTS document_retention_status (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    policy_id INTEGER NOT NULL REFERENCES retention_policies(id) ON DELETE CASCADE,
    
    -- Retention timeline
    retention_start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    retention_end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    action_due_date TIMESTAMP WITH TIME ZONE NOT NULL,
    last_notified_at TIMESTAMP WITH TIME ZONE,
    
    -- Current status
    status VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    is_legal_hold BOOLEAN DEFAULT FALSE,
    legal_hold_reason TEXT,
    
    -- Execution tracking
    action_executed_at TIMESTAMP WITH TIME ZONE,
    action_executed_by_id INTEGER REFERENCES users(id),
    
    -- Archive information
    archive_location VARCHAR(500),
    archive_checksum VARCHAR(255),
    archive_size BIGINT,
    
    -- Audit trail
    status_history TEXT,
    notes TEXT,
    
    -- Unique constraint to prevent duplicate assignments
    UNIQUE(document_id, policy_id)
);

-- Retention Schedules Table
CREATE TABLE IF NOT EXISTS retention_schedules (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    policy_id INTEGER NOT NULL REFERENCES retention_policies(id) ON DELETE CASCADE,
    
    -- Schedule configuration
    schedule_type VARCHAR(50) NOT NULL,
    cron_expression VARCHAR(255),
    next_run_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_run_at TIMESTAMP WITH TIME ZONE,
    
    -- Schedule status
    is_active BOOLEAN DEFAULT TRUE,
    run_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    last_run_status VARCHAR(50),
    
    -- Configuration
    time_zone VARCHAR(50) DEFAULT 'UTC',
    max_retries INTEGER DEFAULT 3,
    retry_interval INTEGER DEFAULT 300,
    
    -- Notifications
    notify_on_success BOOLEAN DEFAULT FALSE,
    notify_on_failure BOOLEAN DEFAULT TRUE,
    notification_emails TEXT
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_retention_policies_type ON retention_policies(type);
CREATE INDEX IF NOT EXISTS idx_retention_policies_status ON retention_policies(status);
CREATE INDEX IF NOT EXISTS idx_retention_policies_created_by_id ON retention_policies(created_by_id);
CREATE INDEX IF NOT EXISTS idx_retention_policies_owner_id ON retention_policies(owner_id);
CREATE INDEX IF NOT EXISTS idx_retention_policies_next_execution_at ON retention_policies(next_execution_at);
CREATE INDEX IF NOT EXISTS idx_retention_policies_deleted_at ON retention_policies(deleted_at);

CREATE INDEX IF NOT EXISTS idx_retention_policy_assignments_policy_id ON retention_policy_assignments(policy_id);
CREATE INDEX IF NOT EXISTS idx_retention_policy_assignments_document_id ON retention_policy_assignments(document_id);
CREATE INDEX IF NOT EXISTS idx_retention_policy_assignments_category_id ON retention_policy_assignments(category_id);
CREATE INDEX IF NOT EXISTS idx_retention_policy_assignments_agency_id ON retention_policy_assignments(agency_id);
CREATE INDEX IF NOT EXISTS idx_retention_policy_assignments_is_active ON retention_policy_assignments(is_active);
CREATE INDEX IF NOT EXISTS idx_retention_policy_assignments_effective_date ON retention_policy_assignments(effective_date);
CREATE INDEX IF NOT EXISTS idx_retention_policy_assignments_deleted_at ON retention_policy_assignments(deleted_at);

CREATE INDEX IF NOT EXISTS idx_retention_execution_logs_policy_id ON retention_execution_logs(policy_id);
CREATE INDEX IF NOT EXISTS idx_retention_execution_logs_execution_date ON retention_execution_logs(execution_date);
CREATE INDEX IF NOT EXISTS idx_retention_execution_logs_status ON retention_execution_logs(status);
CREATE INDEX IF NOT EXISTS idx_retention_execution_logs_executed_by_id ON retention_execution_logs(executed_by_id);
CREATE INDEX IF NOT EXISTS idx_retention_execution_logs_deleted_at ON retention_execution_logs(deleted_at);

CREATE INDEX IF NOT EXISTS idx_document_retention_status_document_id ON document_retention_status(document_id);
CREATE INDEX IF NOT EXISTS idx_document_retention_status_policy_id ON document_retention_status(policy_id);
CREATE INDEX IF NOT EXISTS idx_document_retention_status_status ON document_retention_status(status);
CREATE INDEX IF NOT EXISTS idx_document_retention_status_action_due_date ON document_retention_status(action_due_date);
CREATE INDEX IF NOT EXISTS idx_document_retention_status_is_legal_hold ON document_retention_status(is_legal_hold);
CREATE INDEX IF NOT EXISTS idx_document_retention_status_deleted_at ON document_retention_status(deleted_at);

CREATE INDEX IF NOT EXISTS idx_retention_schedules_policy_id ON retention_schedules(policy_id);
CREATE INDEX IF NOT EXISTS idx_retention_schedules_next_run_at ON retention_schedules(next_run_at);
CREATE INDEX IF NOT EXISTS idx_retention_schedules_is_active ON retention_schedules(is_active);
CREATE INDEX IF NOT EXISTS idx_retention_schedules_deleted_at ON retention_schedules(deleted_at);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_retention_policies_updated_at BEFORE UPDATE ON retention_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_retention_policy_assignments_updated_at BEFORE UPDATE ON retention_policy_assignments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_retention_execution_logs_updated_at BEFORE UPDATE ON retention_execution_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_document_retention_status_updated_at BEFORE UPDATE ON document_retention_status FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_retention_schedules_updated_at BEFORE UPDATE ON retention_schedules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample retention policies
INSERT INTO retention_policies (
    name,
    description,
    type,
    retention_period_years,
    action,
    auto_execute,
    regulatory_framework,
    created_by_id,
    owner_id,
    approved_at,
    approved_by_id
) VALUES 
(
    'Standard Document Retention',
    'Standard 7-year retention policy for business documents',
    'time_based',
    7,
    'archive',
    TRUE,
    'SOX',
    1,
    1,
    NOW(),
    1
),
(
    'Legal Hold Policy',
    'Indefinite retention for documents under legal hold',
    'legal_hold',
    0,
    'review',
    FALSE,
    'Legal',
    1,
    1,
    NOW(),
    1
),
(
    'GDPR Compliance',
    'GDPR-compliant retention for personal data documents',
    'regulatory',
    2,
    'delete',
    TRUE,
    'GDPR',
    1,
    1,
    NOW(),
    1
) ON CONFLICT DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE retention_policies IS 'Stores document retention policies with rules and schedules';
COMMENT ON TABLE retention_policy_assignments IS 'Assigns retention policies to documents, categories, or agencies';
COMMENT ON TABLE retention_execution_logs IS 'Logs execution history of retention policy actions';
COMMENT ON TABLE document_retention_status IS 'Tracks retention status for individual documents';
COMMENT ON TABLE retention_schedules IS 'Manages scheduled execution of retention policies';
