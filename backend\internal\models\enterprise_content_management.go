package models

import (
	"time"

	"gorm.io/gorm"
)

// ContentLifecycleStage represents the stage in content lifecycle
type ContentLifecycleStage string

const (
	ContentStageCreation     ContentLifecycleStage = "creation"
	ContentStageReview       ContentLifecycleStage = "review"
	ContentStageApproval     ContentLifecycleStage = "approval"
	ContentStagePublication  ContentLifecycleStage = "publication"
	ContentStageDistribution ContentLifecycleStage = "distribution"
	ContentStageMaintenance  ContentLifecycleStage = "maintenance"
	ContentStageArchival     ContentLifecycleStage = "archival"
	ContentStageDisposal     ContentLifecycleStage = "disposal"
)

// ContentClassification represents security and access classification
type ContentClassification string

const (
	ClassificationPublic       ContentClassification = "public"
	ClassificationInternal     ContentClassification = "internal"
	ClassificationConfidential ContentClassification = "confidential"
	ClassificationRestricted   ContentClassification = "restricted"
	ClassificationSecret       ContentClassification = "secret"
	ClassificationTopSecret    ContentClassification = "top_secret"
)

// ContentRepository represents a content repository with advanced ECM features
type ContentRepository struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Repository identification
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`
	Type        string `json:"type" gorm:"default:'document'"` // "document", "media", "records", "web"

	// Storage configuration
	StorageType   string `json:"storage_type" gorm:"default:'filesystem'"` // "filesystem", "s3", "azure", "gcp"
	StoragePath   string `json:"storage_path"`
	StorageConfig string `json:"storage_config" gorm:"type:text"` // JSON configuration

	// Capacity and limits
	MaxSize      uint64 `json:"max_size" gorm:"default:0"` // Bytes, 0 = unlimited
	CurrentSize  uint64 `json:"current_size" gorm:"default:0"`
	MaxFiles     uint64 `json:"max_files" gorm:"default:0"` // 0 = unlimited
	CurrentFiles uint64 `json:"current_files" gorm:"default:0"`

	// Security and access
	Classification ContentClassification `json:"classification" gorm:"default:'internal'"`
	EncryptionKey  string                `json:"encryption_key"`                 // Encrypted storage key
	AccessPolicy   string                `json:"access_policy" gorm:"type:text"` // JSON access policy

	// Versioning and backup
	VersioningEnabled bool   `json:"versioning_enabled" gorm:"default:true"`
	MaxVersions       int    `json:"max_versions" gorm:"default:10"`
	BackupEnabled     bool   `json:"backup_enabled" gorm:"default:true"`
	BackupSchedule    string `json:"backup_schedule" gorm:"default:'daily'"`

	// Compliance and retention
	RetentionPolicyID *uint            `json:"retention_policy_id"`
	RetentionPolicy   *RetentionPolicy `json:"retention_policy,omitempty" gorm:"foreignKey:RetentionPolicyID"`
	ComplianceLevel   string           `json:"compliance_level" gorm:"default:'standard'"`

	// Monitoring and analytics
	IsActive     bool       `json:"is_active" gorm:"default:true"`
	LastAccessed *time.Time `json:"last_accessed"`
	AccessCount  uint64     `json:"access_count" gorm:"default:0"`

	// Owner and creator relationships
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ContentVersion represents version control for content
type ContentVersion struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Version identification
	DocumentID    uint     `json:"document_id" gorm:"not null"`
	Document      Document `json:"document" gorm:"foreignKey:DocumentID"`
	VersionNumber string   `json:"version_number" gorm:"not null"` // e.g., "1.0", "1.1", "2.0"

	// Version details
	Title       string `json:"title"`
	Content     string `json:"content" gorm:"type:text"`
	ContentHash string `json:"content_hash"` // SHA-256 hash
	FileSize    uint64 `json:"file_size"`
	MimeType    string `json:"mime_type"`

	// Version metadata
	ChangeType string `json:"change_type"` // "major", "minor", "patch", "hotfix"
	ChangeLog  string `json:"change_log" gorm:"type:text"`
	AuthorID   uint   `json:"author_id" gorm:"not null"`
	Author     User   `json:"author" gorm:"foreignKey:AuthorID"`

	// Approval workflow
	ApprovalStatus  string     `json:"approval_status" gorm:"default:'pending'"` // "pending", "approved", "rejected"
	ApprovedByID    *uint      `json:"approved_by_id"`
	ApprovedBy      *User      `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`
	ApprovedAt      *time.Time `json:"approved_at"`
	RejectionReason string     `json:"rejection_reason" gorm:"type:text"`

	// Lifecycle management
	IsActive     bool       `json:"is_active" gorm:"default:true"`
	PublishedAt  *time.Time `json:"published_at"`
	DeprecatedAt *time.Time `json:"deprecated_at"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ContentWorkflow represents workflow management for content
type ContentWorkflow struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Workflow identification
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`
	Type        string `json:"type" gorm:"default:'approval'"` // "approval", "review", "publication"

	// Workflow definition
	Steps      string `json:"steps" gorm:"type:text"`      // JSON workflow steps
	Rules      string `json:"rules" gorm:"type:text"`      // JSON business rules
	Conditions string `json:"conditions" gorm:"type:text"` // JSON conditions

	// Workflow configuration
	IsParallel      bool `json:"is_parallel" gorm:"default:false"`   // Parallel or sequential
	RequireAll      bool `json:"require_all" gorm:"default:true"`    // Require all approvals
	TimeoutHours    int  `json:"timeout_hours" gorm:"default:72"`    // Workflow timeout
	EscalationHours int  `json:"escalation_hours" gorm:"default:24"` // Escalation timeout

	// Workflow triggers
	TriggerEvents string `json:"trigger_events" gorm:"type:text"` // JSON trigger events
	AutoStart     bool   `json:"auto_start" gorm:"default:false"`

	// Status and monitoring
	IsActive       bool       `json:"is_active" gorm:"default:true"`
	LastExecuted   *time.Time `json:"last_executed"`
	ExecutionCount uint64     `json:"execution_count" gorm:"default:0"`
	SuccessRate    float64    `json:"success_rate" gorm:"default:0"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ContentWorkflowInstance represents a running workflow instance
type ContentWorkflowInstance struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Instance identification
	WorkflowID uint            `json:"workflow_id" gorm:"not null"`
	Workflow   ContentWorkflow `json:"workflow" gorm:"foreignKey:WorkflowID"`
	DocumentID uint            `json:"document_id" gorm:"not null"`
	Document   Document        `json:"document" gorm:"foreignKey:DocumentID"`

	// Instance details
	InstanceID  string `json:"instance_id" gorm:"uniqueIndex;not null"`
	Status      string `json:"status" gorm:"default:'running'"` // "running", "completed", "failed", "cancelled"
	CurrentStep int    `json:"current_step" gorm:"default:0"`
	TotalSteps  int    `json:"total_steps"`

	// Execution details
	StartedAt   time.Time  `json:"started_at"`
	CompletedAt *time.Time `json:"completed_at"`
	StartedByID uint       `json:"started_by_id" gorm:"not null"`
	StartedBy   User       `json:"started_by" gorm:"foreignKey:StartedByID"`

	// Progress tracking
	StepResults string `json:"step_results" gorm:"type:text"` // JSON step results
	ErrorLog    string `json:"error_log" gorm:"type:text"`

	// Timeout and escalation
	TimeoutAt    *time.Time `json:"timeout_at"`
	EscalationAt *time.Time `json:"escalation_at"`
	IsEscalated  bool       `json:"is_escalated" gorm:"default:false"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ContentCollaboration represents collaboration features
type ContentCollaboration struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Collaboration identification
	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`
	UserID     uint     `json:"user_id" gorm:"not null"`
	User       User     `json:"user" gorm:"foreignKey:UserID"`

	// Collaboration type
	Type       string `json:"type" gorm:"not null"`             // "edit", "review", "comment", "approve"
	Permission string `json:"permission" gorm:"default:'read'"` // "read", "write", "admin"

	// Collaboration details
	StartedAt time.Time  `json:"started_at"`
	EndedAt   *time.Time `json:"ended_at"`
	IsActive  bool       `json:"is_active" gorm:"default:true"`

	// Session tracking
	SessionID   string     `json:"session_id"`
	LastAction  *time.Time `json:"last_action"`
	ActionCount uint64     `json:"action_count" gorm:"default:0"`

	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// ContentTemplate represents content templates for standardized document creation
type ContentTemplate struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Template identification
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description" gorm:"type:text"`
	Category    string `json:"category"`                       // "document", "form", "report", "email"
	Type        string `json:"type" gorm:"default:'standard'"` // "standard", "custom", "system"

	// Template content
	Content   string `json:"content" gorm:"type:text"`     // Template content (HTML, Markdown, etc.)
	Format    string `json:"format" gorm:"default:'html'"` // "html", "markdown", "docx", "pdf"
	Schema    string `json:"schema" gorm:"type:text"`      // JSON schema for template variables
	Variables string `json:"variables" gorm:"type:text"`   // JSON array of template variables

	// Template configuration
	IsActive bool   `json:"is_active" gorm:"default:true"`
	IsPublic bool   `json:"is_public" gorm:"default:false"`
	Version  string `json:"version" gorm:"default:'1.0'"`

	// Owner and permissions
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Usage tracking
	UsageCount uint64     `json:"usage_count" gorm:"default:0"`
	LastUsed   *time.Time `json:"last_used"`

	// Additional metadata
	Tags     string `json:"tags" gorm:"type:text"`     // JSON array of tags
	Metadata string `json:"metadata" gorm:"type:text"` // JSON metadata
}

// TableName returns the table name for ContentRepository model
func (ContentRepository) TableName() string {
	return "content_repositories"
}

// TableName returns the table name for ContentTemplate model
func (ContentTemplate) TableName() string {
	return "content_templates"
}

// TableName returns the table name for ContentVersion model
func (ContentVersion) TableName() string {
	return "content_versions"
}

// TableName returns the table name for ContentWorkflow model
func (ContentWorkflow) TableName() string {
	return "content_workflows"
}

// TableName returns the table name for ContentWorkflowInstance model
func (ContentWorkflowInstance) TableName() string {
	return "content_workflow_instances"
}

// TableName returns the table name for ContentCollaboration model
func (ContentCollaboration) TableName() string {
	return "content_collaborations"
}
