package handlers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// setupTestDB creates an in-memory SQLite database for testing
func setupTestDB() *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		panic("failed to connect to test database")
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&models.User{},
		&models.Agency{},
		&models.Document{},
		&models.DocumentComment{},
		&models.DigitalCertificate{},
		&models.DigitalSignature{},
	)
	if err != nil {
		panic("failed to migrate test database")
	}

	return db
}

// setupTestData creates test data in the database
func setupTestData(db *gorm.DB) {
	// Create test user
	user := models.User{
		Username:  "testuser",
		Email:     "<EMAIL>",
		FirstName: "Test",
		LastName:  "User",
		Role:      models.RoleUser,
		IsActive:  true,
	}
	db.Create(&user)

	// Create test agency
	agency := models.Agency{
		Name:      "Test Agency",
		ShortName: "TA",
		Slug:      "test-agency",
		IsActive:  true,
	}
	db.Create(&agency)

	// Create test document
	document := models.Document{
		Title:           "Test Document",
		Abstract:        "This is a test document",
		Type:            "rule",
		Status:          "published",
		AgencyID:        agency.ID,
		AcceptsComments: true,
		IsPublic:        true,
		VisibilityLevel: 1,
		PublicationDate: time.Now(),
	}
	db.Create(&document)

	// Create test certificate
	cert := models.DigitalCertificate{
		SerialNumber: "TEST123456",
		Subject:      "CN=Test User,O=Test Org",
		Issuer:       "CN=Test CA,O=Test CA Org",
		CommonName:   "Test User",
		NotBefore:    time.Now().AddDate(0, 0, -30),
		NotAfter:     time.Now().AddDate(1, 0, 0),
		Status:       models.CertificateStatusActive,
		OwnerID:      user.ID,
		IsActive:     true,
		IsDefault:    true,
		Purpose:      "signing",
	}
	db.Create(&cert)
}

func TestGetDocumentComments(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	db := setupTestDB()
	setupTestData(db)
	database.SetDB(db) // Set the test database

	// Create test comment
	comment := models.DocumentComment{
		DocumentID:     1,
		CommenterName:  "Test Commenter",
		CommenterEmail: "<EMAIL>",
		Subject:        "Test Comment",
		Content:        "This is a test comment",
		IsPublic:       true,
		IsVerified:     false,
		IsModerated:    false,
	}
	db.Create(&comment)

	// Setup router
	router := gin.New()
	router.GET("/documents/:id/comments", GetDocumentComments)

	// Test request
	req, _ := http.NewRequest("GET", "/documents/1/comments", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response PaginationResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, int64(1), response.Total)
	assert.Len(t, response.Data, 1)
}

func TestCreateComment(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	db := setupTestDB()
	setupTestData(db)
	database.SetDB(db)

	// Setup router
	router := gin.New()
	router.POST("/documents/:id/comments", CreateComment)

	// Test data
	commentData := CommentRequest{
		CommenterName:  "New Commenter",
		CommenterEmail: "<EMAIL>",
		Subject:        "New Comment",
		Content:        "This is a new test comment",
	}

	jsonData, _ := json.Marshal(commentData)

	// Test request
	req, _ := http.NewRequest("POST", "/documents/1/comments", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusCreated, w.Code)

	var response SuccessResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Comment submitted successfully", response.Message)

	// Verify comment was created in database
	var comment models.DocumentComment
	err = db.Where("commenter_name = ?", "New Commenter").First(&comment).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Comment", comment.Subject)
}

func TestCreateSignedComment(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	db := setupTestDB()
	setupTestData(db)
	database.SetDB(db)

	// Setup router
	router := gin.New()
	router.POST("/documents/:id/comments/signed", CreateSignedComment)

	// Test data
	signedCommentData := SignedCommentRequest{
		CommentRequest: CommentRequest{
			CommenterName:  "Signed Commenter",
			CommenterEmail: "<EMAIL>",
			Subject:        "Signed Comment",
			Content:        "This is a digitally signed test comment",
		},
	}
	signedCommentData.DigitalSignature.SignatureValue = "test_signature_value"
	signedCommentData.DigitalSignature.CertificateID = 1
	signedCommentData.DigitalSignature.Timestamp = time.Now().Format(time.RFC3339)
	signedCommentData.DigitalSignature.HashAlgorithm = "sha256"
	signedCommentData.DigitalSignature.SigningMethod = "rsa_pss"

	jsonData, _ := json.Marshal(signedCommentData)

	// Test request
	req, _ := http.NewRequest("POST", "/documents/1/comments/signed", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusCreated, w.Code)

	var response SuccessResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Signed comment submitted successfully", response.Message)

	// Verify signed comment was created
	var comment models.DocumentComment
	err = db.Where("commenter_name = ?", "Signed Commenter").First(&comment).Error
	assert.NoError(t, err)
	assert.True(t, comment.IsVerified)
	assert.Contains(t, comment.Subject, "[DIGITALLY SIGNED]")
}

func TestGetComment(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	db := setupTestDB()
	setupTestData(db)
	database.SetDB(db)

	// Create test comment
	comment := models.DocumentComment{
		DocumentID:     1,
		CommenterName:  "Test Commenter",
		CommenterEmail: "<EMAIL>",
		Subject:        "Test Comment",
		Content:        "This is a test comment",
		IsPublic:       true,
		IsVerified:     false,
		IsModerated:    false,
	}
	db.Create(&comment)

	// Setup router
	router := gin.New()
	router.GET("/comments/:id", GetComment)

	// Test request
	req, _ := http.NewRequest("GET", "/comments/1", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response SuccessResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Comment retrieved successfully", response.Message)
}

func TestVerifyCommentSignature(t *testing.T) {
	// Setup
	gin.SetMode(gin.TestMode)
	db := setupTestDB()
	setupTestData(db)
	database.SetDB(db)

	// Create test comment and signature
	comment := models.DocumentComment{
		DocumentID:     1,
		CommenterName:  "Signed Commenter",
		CommenterEmail: "<EMAIL>",
		Subject:        "[DIGITALLY SIGNED] Test Comment",
		Content:        "This is a signed test comment",
		IsPublic:       true,
		IsVerified:     true,
		IsModerated:    false,
	}
	db.Create(&comment)

	signedAt := time.Now()
	signature := models.DigitalSignature{
		SignatureID:     "test_signature_123",
		Type:            models.SignatureTypeAdvanced,
		Status:          models.SignatureStatusSigned,
		DocumentID:      1,
		SignerID:        1,
		SignerName:      "Signed Commenter",
		SignerEmail:     "<EMAIL>",
		DocumentHash:    "test_hash",
		SignatureData:   "test_signature_value",
		SignedAt:        &signedAt,
		CertificateID:   func() *uint { id := uint(1); return &id }(),
		HashAlgorithm:   models.HashAlgorithmSHA256,
		SignatureMethod: "rsa_pss",
		IsValid:         true,
		RequestedAt:     time.Now(),
		RequestedByID:   1,
	}
	db.Create(&signature)

	// Setup router
	router := gin.New()
	router.GET("/comments/:id/verify", VerifyCommentSignature)

	// Test request
	req, _ := http.NewRequest("GET", "/comments/1/verify", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response SuccessResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Signature verification completed", response.Message)
}
