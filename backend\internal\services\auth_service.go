package services

import (
	"errors"
	"fmt"
	"time"

	"federal-register-clone/internal/auth"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// AuthService handles authentication and authorization
type AuthService struct {
	db *gorm.DB
}

// NewAuthService creates a new auth service
func NewAuthService() *AuthService {
	return &AuthService{
		db: database.GetDB(),
	}
}

// RegisterUser registers a new user
func (s *AuthService) RegisterUser(userData *models.User, password string) (*models.User, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	// Check if username or email already exists
	var existingUser models.User
	if err := s.db.Where("username = ? OR email = ?", userData.Username, userData.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("username or email already exists")
	}

	// Hash password
	hashedPassword, err := auth.HashPassword(password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	userData.PasswordHash = hashedPassword
	userData.IsActive = true
	userData.IsVerified = false       // Require email verification
	userData.Role = models.RoleViewer // Default role

	// Create user
	if err := s.db.Create(userData).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Create default user preferences
	preferences := &models.UserPreferences{
		UserID:               userData.ID,
		EmailNotifications:   true,
		DocumentAlerts:       true,
		WeeklyDigest:         false,
		CommentNotifications: true,
		DocumentsPerPage:     25,
		DefaultView:          "list",
		Theme:                "light",
		Language:             "en",
		Timezone:             "UTC",
		DefaultSearchSort:    "relevance",
		SaveSearchHistory:    true,
		AutoCompleteEnabled:  true,
	}

	if err := s.db.Create(preferences).Error; err != nil {
		// Log error but don't fail user creation
		fmt.Printf("Failed to create user preferences: %v\n", err)
	}

	// Remove password hash from response
	userData.PasswordHash = ""
	return userData, nil
}

// AuthenticateUser authenticates a user with credentials
func (s *AuthService) AuthenticateUser(identifier, password string) (*models.User, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var user models.User

	// Find user by username or email
	if err := s.db.Preload("Agency").Where("username = ? OR email = ?", identifier, identifier).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid credentials")
		}
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	// Check if user is active
	if !user.IsActive {
		return nil, errors.New("user account is inactive")
	}

	// Verify password
	if !auth.CheckPassword(password, user.PasswordHash) {
		return nil, errors.New("invalid credentials")
	}

	// Update last login time
	now := time.Now()
	user.LastLoginAt = &now
	s.db.Save(&user)

	// Remove password hash from response
	user.PasswordHash = ""
	return &user, nil
}

// ChangePassword changes a user's password
func (s *AuthService) ChangePassword(userID uint, currentPassword, newPassword string) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	var user models.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return errors.New("user not found")
	}

	// Verify current password
	if !auth.CheckPassword(currentPassword, user.PasswordHash) {
		return errors.New("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := auth.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// Update password
	if err := s.db.Model(&user).Update("password_hash", hashedPassword).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	// Invalidate all user sessions to force re-login
	auth.InvalidateAllUserSessions(userID)

	return nil
}

// UpdateUserRole updates a user's role (admin only)
func (s *AuthService) UpdateUserRole(userID uint, newRole models.UserRole, updatedByID uint) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Verify the user making the change is an admin
	var updatedBy models.User
	if err := s.db.First(&updatedBy, updatedByID).Error; err != nil {
		return errors.New("unauthorized")
	}

	if updatedBy.Role != models.RoleAdmin {
		return errors.New("insufficient permissions")
	}

	// Update user role
	result := s.db.Model(&models.User{}).Where("id = ?", userID).Update("role", newRole)
	if result.Error != nil {
		return fmt.Errorf("failed to update user role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("user not found")
	}

	return nil
}

// DeactivateUser deactivates a user account
func (s *AuthService) DeactivateUser(userID uint, deactivatedByID uint) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Verify the user making the change has permission
	var deactivatedBy models.User
	if err := s.db.First(&deactivatedBy, deactivatedByID).Error; err != nil {
		return errors.New("unauthorized")
	}

	if deactivatedBy.Role != models.RoleAdmin {
		return errors.New("insufficient permissions")
	}

	// Deactivate user
	result := s.db.Model(&models.User{}).Where("id = ?", userID).Update("is_active", false)
	if result.Error != nil {
		return fmt.Errorf("failed to deactivate user: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return errors.New("user not found")
	}

	// Invalidate all user sessions
	auth.InvalidateAllUserSessions(userID)

	return nil
}

// CanUserAccessDocument checks if a user can access a specific document
func (s *AuthService) CanUserAccessDocument(userID uint, documentID uint, action string) (bool, error) {
	if s.db == nil {
		return false, errors.New("database not initialized")
	}

	var user models.User
	if err := s.db.Preload("Agency").First(&user, userID).Error; err != nil {
		return false, errors.New("user not found")
	}

	var document models.Document
	if err := s.db.First(&document, documentID).Error; err != nil {
		return false, errors.New("document not found")
	}

	return s.checkDocumentPermission(&user, &document, action), nil
}

// checkDocumentPermission checks document permissions based on user role and document status
func (s *AuthService) checkDocumentPermission(user *models.User, document *models.Document, action string) bool {
	switch action {
	case "read":
		// Published documents can be read by anyone
		if document.Status == models.StatusPublished {
			return true
		}
		// Draft/review documents can be read by creators, same agency, or higher roles
		if user.Role == models.RoleAdmin {
			return true
		}
		if document.CreatedByID == user.ID {
			return true
		}
		if user.AgencyID != nil && document.AgencyID == *user.AgencyID {
			return user.Role == models.RoleEditor || user.Role == models.RoleReviewer || user.Role == models.RolePublisher
		}
		return false

	case "edit":
		// Only admins, document creators, or same agency editors can edit
		if user.Role == models.RoleAdmin {
			return true
		}
		if document.CreatedByID == user.ID && user.Role == models.RoleEditor {
			return true
		}
		if user.AgencyID != nil && document.AgencyID == *user.AgencyID && user.Role == models.RoleEditor {
			return true
		}
		return false

	case "delete":
		// Only admins can delete documents
		return user.Role == models.RoleAdmin

	case "approve":
		// Only reviewers and above can approve
		if user.Role == models.RoleAdmin || user.Role == models.RoleReviewer {
			return true
		}
		// Same agency reviewers can approve
		if user.AgencyID != nil && document.AgencyID == *user.AgencyID && user.Role == models.RoleReviewer {
			return true
		}
		return false

	case "publish":
		// Only publishers and admins can publish
		if user.Role == models.RoleAdmin || user.Role == models.RolePublisher {
			return true
		}
		// Same agency publishers can publish
		if user.AgencyID != nil && document.AgencyID == *user.AgencyID && user.Role == models.RolePublisher {
			return true
		}
		return false

	default:
		return false
	}
}

// GetUserPermissions returns a user's permissions summary
func (s *AuthService) GetUserPermissions(userID uint) (*UserPermissions, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var user models.User
	if err := s.db.Preload("Agency").First(&user, userID).Error; err != nil {
		return nil, errors.New("user not found")
	}

	permissions := &UserPermissions{
		UserID:   user.ID,
		Role:     user.Role,
		AgencyID: user.AgencyID,
	}

	// Set permissions based on role
	switch user.Role {
	case models.RoleAdmin:
		permissions.CanCreateDocuments = true
		permissions.CanEditAnyDocument = true
		permissions.CanDeleteDocuments = true
		permissions.CanApproveDocuments = true
		permissions.CanPublishDocuments = true
		permissions.CanManageUsers = true
		permissions.CanManageAgencies = true
		permissions.CanViewAnalytics = true

	case models.RolePublisher:
		permissions.CanCreateDocuments = true
		permissions.CanEditOwnDocuments = true
		permissions.CanApproveDocuments = true
		permissions.CanPublishDocuments = true

	case models.RoleReviewer:
		permissions.CanCreateDocuments = true
		permissions.CanEditOwnDocuments = true
		permissions.CanApproveDocuments = true

	case models.RoleEditor:
		permissions.CanCreateDocuments = true
		permissions.CanEditOwnDocuments = true

	case models.RoleViewer:
		// Viewers can only read published documents
	}

	return permissions, nil
}

// UserPermissions represents a user's permissions
type UserPermissions struct {
	UserID              uint                  `json:"user_id"`
	Role                models.LegacyUserRole `json:"role"`
	AgencyID            *uint                 `json:"agency_id"`
	CanCreateDocuments  bool                  `json:"can_create_documents"`
	CanEditOwnDocuments bool                  `json:"can_edit_own_documents"`
	CanEditAnyDocument  bool                  `json:"can_edit_any_document"`
	CanDeleteDocuments  bool                  `json:"can_delete_documents"`
	CanApproveDocuments bool                  `json:"can_approve_documents"`
	CanPublishDocuments bool                  `json:"can_publish_documents"`
	CanManageUsers      bool                  `json:"can_manage_users"`
	CanManageAgencies   bool                  `json:"can_manage_agencies"`
	CanViewAnalytics    bool                  `json:"can_view_analytics"`
}
