import React from 'react';
import { ExclamationCircleIcon } from '@heroicons/react/24/outline';

export interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'password' | 'number' | 'date' | 'datetime-local' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'file';
  value?: any;
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  error?: string;
  help?: string;
  options?: Array<{ value: string | number; label: string; disabled?: boolean }>;
  multiple?: boolean;
  rows?: number;
  min?: string | number;
  max?: string | number;
  step?: string | number;
  accept?: string;
  className?: string;
  inputClassName?: string;
  labelClassName?: string;
  children?: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  error,
  help,
  options = [],
  multiple = false,
  rows = 3,
  min,
  max,
  step,
  accept,
  className = '',
  inputClassName = '',
  labelClassName = '',
  children
}) => {
  const baseInputClasses = `
    block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
    placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500
    disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed
    ${error ? 'border-red-300 focus:ring-red-500 focus:border-red-500' : ''}
    ${inputClassName}
  `.trim().replace(/\s+/g, ' ');

  const labelClasses = `
    block text-sm font-medium text-gray-700 mb-1
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
    ${labelClassName}
  `.trim().replace(/\s+/g, ' ');

  const renderInput = () => {
    const commonProps = {
      id: name,
      name,
      value: value || '',
      onChange,
      onBlur,
      placeholder,
      required,
      disabled,
      className: baseInputClasses,
      min,
      max,
      step
    };

    switch (type) {
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            rows={rows}
          />
        );

      case 'select':
        return (
          <select
            {...commonProps}
            multiple={multiple}
          >
            {placeholder && !multiple && (
              <option value="" disabled>
                {placeholder}
              </option>
            )}
            {options.map((option) => (
              <option
                key={option.value}
                value={option.value}
                disabled={option.disabled}
              >
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'checkbox':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              id={name}
              name={name}
              checked={!!value}
              onChange={onChange}
              onBlur={onBlur}
              disabled={disabled}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:cursor-not-allowed"
            />
            <label htmlFor={name} className="ml-2 block text-sm text-gray-900">
              {label}
            </label>
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {options.map((option) => (
              <div key={option.value} className="flex items-center">
                <input
                  type="radio"
                  id={`${name}-${option.value}`}
                  name={name}
                  value={option.value}
                  checked={value === option.value}
                  onChange={onChange}
                  onBlur={onBlur}
                  disabled={disabled || option.disabled}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 disabled:cursor-not-allowed"
                />
                <label
                  htmlFor={`${name}-${option.value}`}
                  className="ml-2 block text-sm text-gray-900"
                >
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );

      case 'file':
        return (
          <input
            type="file"
            id={name}
            name={name}
            onChange={onChange}
            onBlur={onBlur}
            disabled={disabled}
            accept={accept}
            multiple={multiple}
            className={`
              block w-full text-sm text-gray-500
              file:mr-4 file:py-2 file:px-4
              file:rounded-md file:border-0
              file:text-sm file:font-medium
              file:bg-blue-50 file:text-blue-700
              hover:file:bg-blue-100
              disabled:file:bg-gray-50 disabled:file:text-gray-500
              ${inputClassName}
            `.trim().replace(/\s+/g, ' ')}
          />
        );

      default:
        return (
          <input
            type={type}
            {...commonProps}
            accept={accept}
          />
        );
    }
  };

  if (type === 'checkbox') {
    return (
      <div className={`${className}`}>
        {renderInput()}
        {error && (
          <div className="mt-1 flex items-center text-sm text-red-600">
            <ExclamationCircleIcon className="h-4 w-4 mr-1" />
            {error}
          </div>
        )}
        {help && !error && (
          <p className="mt-1 text-sm text-gray-500">{help}</p>
        )}
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <label htmlFor={name} className={labelClasses}>
        {label}
      </label>
      {renderInput()}
      {children}
      {error && (
        <div className="mt-1 flex items-center text-sm text-red-600">
          <ExclamationCircleIcon className="h-4 w-4 mr-1" />
          {error}
        </div>
      )}
      {help && !error && (
        <p className="mt-1 text-sm text-gray-500">{help}</p>
      )}
    </div>
  );
};

export default FormField;

// Additional form components for complex scenarios

export interface FormSectionProps {
  title?: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className = ''
}) => (
  <div className={`space-y-4 ${className}`}>
    {(title || description) && (
      <div className="border-b border-gray-200 pb-4">
        {title && (
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        )}
        {description && (
          <p className="mt-1 text-sm text-gray-600">{description}</p>
        )}
      </div>
    )}
    <div className="space-y-4">
      {children}
    </div>
  </div>
);

export interface FormActionsProps {
  children: React.ReactNode;
  className?: string;
  align?: 'left' | 'center' | 'right';
}

export const FormActions: React.FC<FormActionsProps> = ({
  children,
  className = '',
  align = 'right'
}) => {
  const alignClasses = {
    left: 'justify-start',
    center: 'justify-center',
    right: 'justify-end'
  };

  return (
    <div className={`flex ${alignClasses[align]} space-x-3 pt-6 border-t border-gray-200 ${className}`}>
      {children}
    </div>
  );
};

export interface FormGridProps {
  children: React.ReactNode;
  columns?: 1 | 2 | 3 | 4;
  className?: string;
}

export const FormGrid: React.FC<FormGridProps> = ({
  children,
  columns = 2,
  className = ''
}) => {
  const gridClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <div className={`grid ${gridClasses[columns]} gap-4 ${className}`}>
      {children}
    </div>
  );
};
