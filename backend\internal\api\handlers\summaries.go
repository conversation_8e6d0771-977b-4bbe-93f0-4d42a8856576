package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GetPublicSummaries returns all public summaries
func GetPublicSummaries(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c<PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Count total summaries
	var total int64
	db.Model(&models.Summary{}).Where("is_public = ?", true).Count(&total)

	// Get summaries with pagination
	var summaries []models.Summary
	offset := (page - 1) * perPage
	if err := db.Where("is_public = ?", true).
		Order("created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&summaries).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch summaries",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	summaryResponses := make([]gin.H, len(summaries))
	for i, summary := range summaries {
		summaryResponses[i] = gin.H{
			"id":          summary.ID,
			"title":       summary.Title,
			"content":     summary.Content,
			"entity_type": summary.EntityType,
			"entity_id":   summary.EntityID,
			"action_type": summary.ActionType,
			"created_at":  summary.CreatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       summaryResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}
