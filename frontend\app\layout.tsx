'use client'

import { useEffect } from 'react'
import './styles/globals.css'
import { useUIStore } from './stores/uiStore'

// Note: Metadata export needs to be in a separate file for client components
// This is a temporary solution - in production, consider using a server component wrapper

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { theme } = useUIStore()

  useEffect(() => {
    // Apply theme to document root
    const root = document.documentElement
    root.classList.remove('light', 'dark')
    root.classList.add(theme)

    // One-time cleanup of invalid tokens from localStorage
    const cleanupInvalidTokens = () => {
      const tokenKey = 'federal_register_token';
      const refreshKey = 'federal_register_refresh_token';

      const token = localStorage.getItem(tokenKey);
      const refreshToken = localStorage.getItem(refreshKey);

      if (token === 'null' || token === 'undefined') {
        console.warn('Cleaning up invalid token string from localStorage');
        localStorage.removeItem(tokenKey);
        document.cookie = `${tokenKey}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
      }

      if (refreshToken === 'null' || refreshToken === 'undefined') {
        console.warn('Cleaning up invalid refresh token string from localStorage');
        localStorage.removeItem(refreshKey);
        document.cookie = `${refreshKey}=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT;`;
      }
    };

    cleanupInvalidTokens();
  }, [theme])

  return (
    <html lang="en" className={theme}>
      <head>
        <title>Federal Register Clone</title>
        <meta name="description" content="Document Management System" />
      </head>
      <body className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {children}
      </body>
    </html>
  )
}
