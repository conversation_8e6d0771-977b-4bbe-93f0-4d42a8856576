# Proceeding System Documentation

## Overview

The Proceeding System is a formal framework for managing complex, overarching processes within the SGU (Student Government Unit) Administrative Procedure. It implements a structured approach to significant undertakings that extend beyond routine task execution, ensuring methodical progress through sequential primary steps.

## 7 Key Requirements Implementation

### 1. Purpose
**Requirement:** A "Proceeding" is a formal, overarching process undertaken by the SGU to manage and resolve a specific, complex objective or a set of interrelated issues, typically focused on one or multiple topics within the Personal Regulation Plan (PRP).

**Implementation:**
- Proceedings serve as frameworks to orchestrate various Administrative Procedure components
- Integration with Information Collection, Review Reports, Orders, IFR development, and Task Table management
- Clear objective definition and PRP alignment requirements

### 2. Sequential Execution
**Requirement:** Each primary step within a proceeding must be substantially completed before the initiation of the subsequent primary step, ensuring methodical progress.

**Implementation:**
- Backend validation in `proceeding_service.go` - `ValidateSequentialExecution()`
- Frontend validation in `proceedingValidation.ts` - `validateSequentialExecution()`
- Database triggers to enforce step ordering
- Step properties: `previous_step_required`, `blocks_next_step`, `can_start_concurrently`

### 3. Minimum Complexity and Structure
**Requirement:** To qualify as a formal proceeding, an initiative must comprise a minimum of five (5) distinct, sequential primary steps.

**Implementation:**
- Database constraint validation in migration
- Backend validation in handlers and services
- Frontend form validation requiring minimum 5 steps
- Database trigger: `validate_proceeding_minimum_steps_trigger`

### 4. Unique Identification
**Requirement:** Each proceeding shall be assigned a unique identifier consisting of a descriptive name and the initiation date.

**Implementation:**
- Database field: `unique_id` (generated automatically)
- Format: "Name YYYY-MM-DD"
- Database trigger: `generate_proceeding_unique_id_trigger`
- Unique constraint on `unique_id` field

### 5. Mandatory Review
**Requirement:** Every proceeding, upon its completion or at defined critical milestones, must undergo a formal review as per Part 2 Review Report (RR).

**Implementation:**
- Database fields: `review_required`, `review_scheduled`, `review_date`, `review_completed`
- Automatic review scheduling upon proceeding completion
- Integration with review report system via `TriggerReviewReport` handler
- Review tracking in proceeding logs

### 6. PRP Alignment and Scope
**Requirement:** A proceeding must have a clear and direct correlation to existing sections within the Personal Regulation Plan (PRP) or aim to develop new PRP elements.

**Implementation:**
- Required field: `prp_alignment` (cannot be empty)
- Optional fields: `prp_sections`, `new_prp_elements`
- Validation in backend services and frontend forms
- IFR integration: `requires_ifr`, `ifr_triggered`, `ifr_description`

### 7. Consideration of Existing Directives and Rules
**Requirement:** All relevant existing rules, orders, and operational directives must be thoroughly reviewed and integrated during the planning and execution phases.

**Implementation:**
- Database field: `existing_directives_reviewed` (required for activation)
- Reference fields: `referenced_rules`, `referenced_orders`, `referenced_directives`
- Analysis fields: `conflict_analysis`, `integration_plan`
- Validation prevents activation without directive review

## Database Schema

### Core Tables

#### `proceedings`
- Primary proceeding entity with all 7 requirements implemented
- Unique identification, PRP alignment, review tracking
- Progress tracking and metadata

#### `primary_steps`
- Sequential steps with ordering and dependency management
- Administrative component integration flags
- Completion criteria and evidence tracking

#### `proceeding_logs`
- Activity and decision tracking
- Review and approval workflow
- Audit trail for all proceeding activities

#### `proceeding_participants`
- Role-based access control
- Notification preferences
- Participation tracking

#### Junction Tables
- `proceeding_tasks` - Links to task management system
- `proceeding_documents` - Links to document management system
- `proceeding_regulations` - Links to regulation system

### Database Triggers and Functions

1. **Automatic Timestamp Updates**
   - `update_updated_at_column()` - Updates timestamps on record changes

2. **Unique ID Generation**
   - `generate_proceeding_unique_id()` - Creates unique identifiers (Requirement 4)

3. **Minimum Steps Validation**
   - `validate_proceeding_minimum_steps()` - Enforces 5-step minimum (Requirement 3)

4. **Progress Tracking**
   - `update_proceeding_progress()` - Automatically calculates progress percentages

## API Endpoints

### Core CRUD Operations
```
GET    /api/proceedings              - List proceedings with filtering
POST   /api/proceedings              - Create new proceeding
GET    /api/proceedings/{id}         - Get proceeding details
PUT    /api/proceedings/{id}         - Update proceeding
DELETE /api/proceedings/{id}         - Delete proceeding
```

### Step Management
```
GET    /api/proceedings/{id}/steps                    - Get proceeding steps
PUT    /api/proceedings/{id}/steps/{step_id}/status   - Update step status
```

### Activity Logging
```
POST   /api/proceedings/{id}/logs    - Add log entry
```

### System Integration
```
GET    /api/proceedings/{id}/relationships     - Get all relationships
POST   /api/proceedings/{id}/link-task         - Link to task
POST   /api/proceedings/{id}/link-document     - Link to document
POST   /api/proceedings/{id}/link-regulation   - Link to regulation
POST   /api/proceedings/{id}/trigger-review    - Trigger review report
```

## Frontend Components

### Core Components

#### `ProceedingCard`
- Display proceeding summary with progress
- Status badges and priority indicators
- Quick action buttons

#### `ProceedingForm`
- Create/edit proceeding with validation
- Minimum 5 steps requirement enforcement
- PRP alignment validation

#### `ProceedingStepManager`
- Sequential step management interface
- Status transition controls
- Progress visualization

#### `ProceedingDetails`
- Comprehensive proceeding view
- Tabbed interface (Overview, Steps, Logs)
- Integration relationship display

### Pages

#### `/proceedings`
- List all proceedings with filtering
- Search, sort, and pagination
- Quick actions and bulk operations

#### `/proceedings/new`
- Create new proceeding form
- Requirements validation
- Step-by-step wizard

#### `/proceedings/[id]`
- Detailed proceeding view
- Step management interface
- Activity log and relationships

### Custom Hooks

#### `useProceedings`
- Proceeding list management
- CRUD operations
- Filtering and pagination

#### `useProceeding`
- Single proceeding management
- Step status updates
- Log entry creation

## Validation and Business Logic

### Sequential Execution Validation
```typescript
validateSequentialExecution(steps, targetStepOrder, newStatus)
```
- Enforces requirement 2 (sequential execution)
- Checks previous step completion
- Validates dependency chains

### Proceeding Activation Validation
```typescript
validateProceedingActivation(proceeding)
```
- Validates all 7 requirements before activation
- Checks minimum steps, PRP alignment, directive review
- Returns comprehensive validation results

### Progress Calculation
```typescript
calculateProgress(steps)
```
- Calculates completion percentage
- Identifies current step
- Tracks critical step completion

## Integration Points

### Task Management System
- Link proceedings to existing tasks
- Create tasks from proceeding steps
- Track task completion within proceedings

### Document Management System
- Associate documents with proceedings
- Generate documents from proceeding outputs
- Reference existing documents in steps

### Regulation System
- Link proceedings to regulations
- Track regulation modifications
- Create new regulations from proceedings

### Review Report System
- Automatic review scheduling (Requirement 5)
- Review trigger integration
- Review completion tracking

## Security and Access Control

### Role-Based Permissions
- **Viewer:** Read-only access to public proceedings
- **Editor:** Create, edit proceedings and steps
- **Admin:** Full access including deletion
- **Agency Manager:** Manage agency-specific proceedings

### Authentication Requirements
- All proceeding operations require authentication
- JWT token validation
- User context tracking in logs

## Deployment and Migration

### Database Migration
- Migration file: `010_proceeding_system.sql`
- Automatic detection and execution
- Rollback procedures documented

### Environment Configuration
- Database connection settings
- Authentication configuration
- File upload paths for attachments

## Monitoring and Logging

### Activity Logging
- All proceeding actions logged
- User attribution and timestamps
- Decision rationale tracking

### Progress Monitoring
- Real-time progress calculation
- Step completion tracking
- Milestone achievement alerts

### Error Handling
- Comprehensive error messages
- Validation feedback
- Recovery procedures

## Best Practices

### Creating Proceedings
1. Define clear objectives aligned with PRP
2. Review existing directives thoroughly
3. Plan minimum 5 meaningful steps
4. Assign appropriate ownership and participants
5. Set realistic timelines and milestones

### Managing Steps
1. Complete steps sequentially unless explicitly allowed
2. Document completion evidence
3. Update progress regularly
4. Communicate blockers promptly
5. Review dependencies before starting

### System Integration
1. Link related tasks, documents, and regulations
2. Trigger reviews at appropriate milestones
3. Maintain audit trails
4. Follow approval workflows
5. Document decisions and rationale

## Troubleshooting

### Common Issues

#### "Minimum 5 steps required"
- Ensure proceeding has at least 5 primary steps
- Check step ordering (1, 2, 3, 4, 5...)
- Verify steps are properly saved

#### "Sequential execution violation"
- Complete previous steps before starting next
- Check step dependencies
- Verify `previous_step_required` settings

#### "PRP alignment required"
- Provide meaningful PRP alignment description
- Reference specific PRP sections if applicable
- Explain correlation to existing regulations

#### "Existing directives not reviewed"
- Set `existing_directives_reviewed` to true
- Complete conflict analysis
- Develop integration plan

### Performance Optimization
- Use pagination for large proceeding lists
- Implement caching for frequently accessed data
- Optimize database queries with proper indexing
- Use lazy loading for related entities

## Future Enhancements

### Planned Features
1. Automated workflow triggers
2. Advanced reporting and analytics
3. Integration with external systems
4. Mobile application support
5. Advanced notification system

### Scalability Considerations
1. Database partitioning for large datasets
2. Microservice architecture migration
3. Event-driven architecture implementation
4. Real-time collaboration features
5. Advanced search and filtering capabilities
