'use client'

import React, { useState } from 'react';
import {
  Check<PERSON><PERSON>cleIcon,
  ExclamationC<PERSON>cleIcon,
  ClockIcon,
  PlayIcon,
  PauseIcon,
  DocumentTextIcon,
  MicrophoneIcon,
  VideoCameraIcon,
  EyeIcon,
  PencilIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';
import StepStatusUpdateModal from './StepStatusUpdateModal';

interface ProceedingStep {
  id: number;
  name: string;
  description: string;
  step_order: number;
  step_type: string;
  status: string;
  requires_previous_completion: boolean;
  is_mandatory: boolean;
  allows_parallel_execution: boolean;
  content_type: string;
  text_content?: string;
  voice_recording?: string;
  document_content?: string;
  video_content?: string;
  planned_start_date?: string;
  planned_end_date?: string;
  actual_start_date?: string;
  actual_end_date?: string;
  estimated_duration?: number;
  actual_duration?: number;
  completion_criteria: string;
  completion_evidence?: string;
  requires_review: boolean;
  review_completed: boolean;
  reviewed_at?: string;
  reviewed_by?: any;
  review_comments?: string;
  assigned_to?: any;
  owner?: any;
  priority: string;
  is_optional: boolean;
  is_critical: boolean;
  notes?: string;
  progress_percent: number;
  created_at: string;
  updated_at: string;
}

interface ProceedingStepCardProps {
  step: ProceedingStep;
  canEdit: boolean;
  canStart: boolean;
  onStatusChange: (stepId: number, newStatus: string) => void;
  onUpdateStatus?: (stepId: number, status: string, data: any) => Promise<void>;
  onEdit: (step: ProceedingStep) => void;
  onView: (step: ProceedingStep) => void;
}

const ProceedingStepCard: React.FC<ProceedingStepCardProps> = ({
  step,
  canEdit,
  canStart,
  onStatusChange,
  onUpdateStatus,
  onEdit,
  onView
}) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'under_review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'on_hold':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'skipped':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
      case 'urgent':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-600';
    }
  };

  const getStepTypeIcon = (stepType: string) => {
    switch (stepType) {
      case 'notice_of_intent':
      case 'advance_notice':
        return <DocumentTextIcon className="h-5 w-5" />;
      case 'notice_proposed_rule':
      case 'final_rule':
        return <DocumentTextIcon className="h-5 w-5" />;
      case 'public_comment':
        return <MicrophoneIcon className="h-5 w-5" />;
      case 'regulation_publication':
        return <CheckCircleIcon className="h-5 w-5" />;
      default:
        return <ClockIcon className="h-5 w-5" />;
    }
  };

  const getContentTypeIcon = (contentType: string) => {
    switch (contentType) {
      case 'voice':
        return <MicrophoneIcon className="h-4 w-4" />;
      case 'video':
        return <VideoCameraIcon className="h-4 w-4" />;
      case 'document':
        return <DocumentTextIcon className="h-4 w-4" />;
      default:
        return <DocumentTextIcon className="h-4 w-4" />;
    }
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!canEdit) return;

    setIsUpdating(true);
    try {
      await onStatusChange(step.id, newStatus);
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleStatusUpdate = async (stepId: number, status: string, data: any) => {
    if (onUpdateStatus) {
      await onUpdateStatus(stepId, status, data);
    } else {
      await onStatusChange(stepId, status);
    }
  };

  const getStepTypeName = (stepType: string) => {
    switch (stepType) {
      case 'notice_of_intent':
        return 'Notice of Intent';
      case 'advance_notice':
        return 'Advance Notice';
      case 'notice_proposed_rule':
        return 'Notice of Proposed Rulemaking';
      case 'public_comment':
        return 'Public Comment Period';
      case 'supplemental_notice':
        return 'Supplemental Notice';
      case 'final_rule':
        return 'Final Rule';
      case 'interim_final_rule':
        return 'Interim Final Rule';
      case 'regulation_publication':
        return 'Regulation Publication';
      case 'correcting_amendment':
        return 'Correcting Amendment';
      default:
        return 'Custom Step';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
              <span className="text-sm font-semibold text-blue-600 dark:text-blue-300">
                {step.step_order}
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {step.name}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getStepTypeName(step.step_type)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border ${getStatusColor(step.status)}`}>
            {step.status?.replace('_', ' ').toUpperCase() || 'UNKNOWN'}
          </span>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(step.priority)}`}>
            {step.priority?.toUpperCase() || 'UNKNOWN'}
          </span>
        </div>
      </div>

      {/* Description */}
      <p className="text-gray-700 dark:text-gray-300 mb-4">
        {step.description}
      </p>

      {/* Content Type and Requirements */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="flex items-center space-x-2">
          {getContentTypeIcon(step.content_type)}
          <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
            {step.content_type}
          </span>
        </div>
        
        {step.is_mandatory && (
          <div className="flex items-center space-x-2">
            <ExclamationCircleIcon className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-600 dark:text-red-400">
              Mandatory
            </span>
          </div>
        )}
        
        {step.requires_review && (
          <div className="flex items-center space-x-2">
            <EyeIcon className="h-4 w-4 text-blue-500" />
            <span className="text-sm text-blue-600 dark:text-blue-400">
              Requires Review
            </span>
          </div>
        )}
        
        {step.requires_previous_completion && (
          <div className="flex items-center space-x-2">
            <ClockIcon className="h-4 w-4 text-orange-500" />
            <span className="text-sm text-orange-600 dark:text-orange-400">
              Sequential
            </span>
          </div>
        )}
      </div>

      {/* Dates and Progress */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            <strong>Planned:</strong> {formatDate(step.planned_start_date)} - {formatDate(step.planned_end_date)}
          </p>
          {step.actual_start_date && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Actual:</strong> {formatDate(step.actual_start_date)} - {formatDate(step.actual_end_date)}
            </p>
          )}
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-sm text-gray-600 dark:text-gray-400">Progress</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {step.progress_percent}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${step.progress_percent}%` }}
            />
          </div>
        </div>
      </div>

      {/* Completion Criteria */}
      {step.completion_criteria && (
        <div className="mb-4">
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Completion Criteria:
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {step.completion_criteria}
          </p>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onView(step)}
            className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <EyeIcon className="h-4 w-4 mr-1" />
            View
          </button>
          
          {canEdit && (
            <>
              <button
                onClick={() => onEdit(step)}
                className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PencilIcon className="h-4 w-4 mr-1" />
                Edit
              </button>
              <button
                onClick={() => setShowStatusModal(true)}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowRightIcon className="h-4 w-4 mr-1" />
                Update Status
              </button>
            </>
          )}
        </div>

        {/* Status Change Buttons */}
        {canEdit && (
          <div className="flex items-center space-x-2">
            {step.status === 'not_started' && canStart && (
              <button
                onClick={() => handleStatusChange('in_progress')}
                disabled={isUpdating}
                className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                <PlayIcon className="h-4 w-4 mr-1" />
                Start
              </button>
            )}
            
            {step.status === 'in_progress' && (
              <>
                <button
                  onClick={() => handleStatusChange('under_review')}
                  disabled={isUpdating}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
                >
                  Submit for Review
                </button>
                <button
                  onClick={() => handleStatusChange('on_hold')}
                  disabled={isUpdating}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  <PauseIcon className="h-4 w-4 mr-1" />
                  Hold
                </button>
              </>
            )}
            
            {step.status === 'under_review' && (
              <>
                <button
                  onClick={() => handleStatusChange('completed')}
                  disabled={isUpdating}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  <CheckCircleIcon className="h-4 w-4 mr-1" />
                  Complete
                </button>
                <button
                  onClick={() => handleStatusChange('failed')}
                  disabled={isUpdating}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                >
                  <ExclamationCircleIcon className="h-4 w-4 mr-1" />
                  Fail
                </button>
              </>
            )}
          </div>
        )}
      </div>

      {/* Status Update Modal */}
      <StepStatusUpdateModal
        step={step}
        isOpen={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        onUpdate={handleStatusUpdate}
      />
    </div>
  );
};

export default ProceedingStepCard;
