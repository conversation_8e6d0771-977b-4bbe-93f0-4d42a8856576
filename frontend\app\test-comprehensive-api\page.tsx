'use client';

import React, { useState } from 'react';
import apiService from '../services/api';
import { contentApi, financialApi, complianceApi, biApi, hrApi } from '../services/enterpriseApi';
import Layout from '../components/Layout/Layout';

interface TestResult {
  endpoint: string;
  method: string;
  status: 'pending' | 'success' | 'error';
  response?: any;
  error?: string;
  duration?: number;
}

const ComprehensiveAPITestPage: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const updateTestResult = (endpoint: string, method: string, result: Partial<TestResult>) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.endpoint === endpoint && r.method === method);
      if (existing) {
        return prev.map(r => 
          r.endpoint === endpoint && r.method === method 
            ? { ...r, ...result }
            : r
        );
      } else {
        return [...prev, { endpoint, method, status: 'pending', ...result }];
      }
    });
  };

  const runTest = async (
    testName: string,
    endpoint: string,
    method: string,
    testFunction: () => Promise<any>
  ) => {
    const startTime = Date.now();
    updateTestResult(endpoint, method, { status: 'pending' });

    try {
      const response = await testFunction();
      const duration = Date.now() - startTime;
      updateTestResult(endpoint, method, {
        status: 'success',
        response,
        duration
      });
      console.log(`✅ ${testName} - Success`, response);
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateTestResult(endpoint, method, {
        status: 'error',
        error: error.message || 'Unknown error',
        duration
      });
      console.error(`❌ ${testName} - Error:`, error);
    }
  };

  const testPreloadingAPIs = async () => {
    console.log('🧪 Testing Preloading APIs...');
    
    await runTest('Document Defaults', '/preloading/documents', 'GET', 
      () => apiService.getDocumentDefaults());
    
    await runTest('Regulation Defaults', '/preloading/regulations', 'GET', 
      () => apiService.getRegulationDefaults());
    
    await runTest('Agency Defaults', '/preloading/agencies', 'GET', 
      () => apiService.getAgencyDefaults());
    
    await runTest('Category Defaults', '/preloading/categories', 'GET', 
      () => apiService.getCategoryDefaults());
    
    await runTest('Proceeding Defaults', '/preloading/proceedings', 'GET', 
      () => apiService.getProceedingDefaults());
    
    await runTest('Finance Defaults', '/preloading/finances', 'GET', 
      () => apiService.getFinanceDefaults());
    
    await runTest('Generate Slug', '/preloading/slug', 'GET', 
      () => apiService.generateSlug('Test Document Title'));
    
    await runTest('Generate FR Number', '/preloading/fr-number', 'GET', 
      () => apiService.generateFRNumber());
    
    await runTest('Generate Docket Number', '/preloading/docket-number', 'GET', 
      () => apiService.generateDocketNumber(1));
    
    await runTest('Get Public Law Number', '/preloading/public-law-number', 'GET', 
      () => apiService.getPublicLawNumber());
    
    await runTest('Get Regulatory Identifier', '/preloading/regulatory-identifier', 'GET', 
      () => apiService.getRegulatoryIdentifier(1));
    
    await runTest('Get Regulation Docket Number', '/preloading/regulation-docket-number', 'GET', 
      () => apiService.getRegulationDocketNumber(1));
  };

  const testDigitalSignatureAPIs = async () => {
    console.log('🧪 Testing Digital Signature APIs...');
    
    await runTest('Get User Signatures', '/signatures/my', 'GET', 
      () => apiService.getUserSignatures());
    
    await runTest('Get Certificates', '/certificates', 'GET', 
      () => apiService.getCertificates());
    
    await runTest('Get User Certificates', '/user/certificates', 'GET', 
      () => apiService.getUserCertificates());
  };

  const testDocumentProcessingAPIs = async () => {
    console.log('🧪 Testing Document Processing APIs...');
    
    await runTest('Get Processing Jobs', '/processing/jobs', 'GET', 
      () => apiService.getProcessingJobs());
  };

  const testRoleManagementAPIs = async () => {
    console.log('🧪 Testing Role Management APIs...');
    
    await runTest('Get Roles', '/roles', 'GET', 
      () => apiService.getRoles());
    
    await runTest('Get Permissions', '/permissions', 'GET', 
      () => apiService.getPermissions());
  };

  const testRetentionPolicyAPIs = async () => {
    console.log('🧪 Testing Retention Policy APIs...');
    
    await runTest('Get Retention Policies', '/retention-policies', 'GET', 
      () => apiService.getRetentionPolicies());
  };

  const testAdvancedAnalyticsAPIs = async () => {
    console.log('🧪 Testing Advanced Analytics APIs...');
    
    await runTest('Get Advanced Analytics', '/analytics/advanced', 'GET', 
      () => apiService.getAdvancedAnalytics());
    
    await runTest('Get Document Analytics', '/analytics/documents', 'GET', 
      () => apiService.getDocumentAnalytics());
    
    await runTest('Get User Analytics', '/analytics/users', 'GET', 
      () => apiService.getUserAnalytics());
    
    await runTest('Get System Analytics', '/analytics/system', 'GET', 
      () => apiService.getSystemAnalytics());
    
    await runTest('Get Performance Metrics', '/analytics/performance', 'GET', 
      () => apiService.getPerformanceMetrics());
    
    await runTest('Get Usage Statistics', '/analytics/usage', 'GET', 
      () => apiService.getUsageStatistics());
  };

  const testEnterpriseContentAPIs = async () => {
    console.log('🧪 Testing Enterprise Content APIs...');
    
    await runTest('Get Content Repositories', '/enterprise/content/repositories', 'GET', 
      () => contentApi.getRepositories());
    
    await runTest('Get Content Workflows', '/enterprise/content/workflows', 'GET', 
      () => contentApi.getWorkflows());
    
    await runTest('Get Workflow Instances', '/enterprise/content/workflow-instances', 'GET', 
      () => contentApi.getWorkflowInstances());
  };

  const testEnterpriseFinancialAPIs = async () => {
    console.log('🧪 Testing Enterprise Financial APIs...');
    
    await runTest('Get Chart of Accounts', '/enterprise/financial/accounts', 'GET', 
      () => financialApi.getAccounts());
    
    await runTest('Get GL Entries', '/enterprise/financial/gl-entries', 'GET', 
      () => financialApi.getGLEntries());
    
    await runTest('Get Budgets', '/enterprise/financial/budgets', 'GET', 
      () => financialApi.getBudgets());
    
    await runTest('Get Cost Centers', '/enterprise/financial/cost-centers', 'GET', 
      () => financialApi.getCostCenters());
    
    await runTest('Get Financial Reports', '/enterprise/financial/reports', 'GET', 
      () => financialApi.getFinancialReports());
  };

  const testEnterpriseComplianceAPIs = async () => {
    console.log('🧪 Testing Enterprise Compliance APIs...');
    
    await runTest('Get Compliance Requirements', '/enterprise/compliance/requirements', 'GET', 
      () => complianceApi.getRequirements());
    
    await runTest('Get Compliance Assessments', '/enterprise/compliance/assessments', 'GET', 
      () => complianceApi.getAssessments());
    
    await runTest('Get Compliance Findings', '/enterprise/compliance/findings', 'GET', 
      () => complianceApi.getFindings());
    
    await runTest('Get Risk Assessments', '/enterprise/compliance/risks', 'GET', 
      () => complianceApi.getRisks());
  };

  const testEnterpriseBIAPIs = async () => {
    console.log('🧪 Testing Enterprise BI APIs...');
    
    await runTest('Get Data Warehouses', '/enterprise/bi/warehouses', 'GET', 
      () => biApi.getDataWarehouses());
    
    await runTest('Get Data Sources', '/enterprise/bi/data-sources', 'GET', 
      () => biApi.getDataSources());
    
    await runTest('Get Dashboards', '/enterprise/bi/dashboards', 'GET', 
      () => biApi.getDashboards());
    
    await runTest('Get Reports', '/enterprise/bi/reports', 'GET', 
      () => biApi.getReports());
    
    await runTest('Get KPIs', '/enterprise/bi/kpis', 'GET', 
      () => biApi.getKPIs());
  };

  const testEnterpriseHRAPIs = async () => {
    console.log('🧪 Testing Enterprise HR APIs...');
    
    await runTest('Get Employees', '/enterprise/hr/employees', 'GET', 
      () => hrApi.getEmployees());
    
    await runTest('Get Departments', '/enterprise/hr/departments', 'GET', 
      () => hrApi.getDepartments());
    
    await runTest('Get Trainings', '/enterprise/hr/trainings', 'GET', 
      () => hrApi.getTrainings());
    
    await runTest('Get Training Enrollments', '/enterprise/hr/training-enrollments', 'GET', 
      () => hrApi.getTrainingEnrollments());
    
    await runTest('Get HR Dashboard', '/enterprise/hr/dashboard', 'GET', 
      () => hrApi.getHRDashboard());
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    try {
      if (selectedCategory === 'all' || selectedCategory === 'preloading') {
        await testPreloadingAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'signatures') {
        await testDigitalSignatureAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'processing') {
        await testDocumentProcessingAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'roles') {
        await testRoleManagementAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'retention') {
        await testRetentionPolicyAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'analytics') {
        await testAdvancedAnalyticsAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'enterprise-content') {
        await testEnterpriseContentAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'enterprise-financial') {
        await testEnterpriseFinancialAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'enterprise-compliance') {
        await testEnterpriseComplianceAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'enterprise-bi') {
        await testEnterpriseBIAPIs();
      }
      if (selectedCategory === 'all' || selectedCategory === 'enterprise-hr') {
        await testEnterpriseHRAPIs();
      }
      
      console.log('🎉 All tests completed!');
    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'pending': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  const successCount = testResults.filter(r => r.status === 'success').length;
  const errorCount = testResults.filter(r => r.status === 'error').length;
  const totalCount = testResults.length;

  return (
    <Layout title="Comprehensive API Testing" requireAuth={true}>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Comprehensive API Testing
          </h1>

          {/* Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
              <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  disabled={isRunning}
                >
                  <option value="all">All Categories</option>
                  <option value="preloading">Preloading APIs</option>
                  <option value="signatures">Digital Signatures</option>
                  <option value="processing">Document Processing</option>
                  <option value="roles">Role Management</option>
                  <option value="retention">Retention Policies</option>
                  <option value="analytics">Advanced Analytics</option>
                  <option value="enterprise-content">Enterprise Content</option>
                  <option value="enterprise-financial">Enterprise Financial</option>
                  <option value="enterprise-compliance">Enterprise Compliance</option>
                  <option value="enterprise-bi">Enterprise BI</option>
                  <option value="enterprise-hr">Enterprise HR</option>
                </select>

                <button
                  onClick={runAllTests}
                  disabled={isRunning}
                  className={`px-6 py-2 rounded-md font-medium ${
                    isRunning
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 hover:bg-blue-700'
                  } text-white transition-colors`}
                >
                  {isRunning ? 'Running Tests...' : 'Run Tests'}
                </button>
              </div>

              {/* Test Summary */}
              {totalCount > 0 && (
                <div className="flex gap-4 text-sm">
                  <span className="text-green-600">✅ {successCount}</span>
                  <span className="text-red-600">❌ {errorCount}</span>
                  <span className="text-gray-600">Total: {totalCount}</span>
                </div>
              )}
            </div>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-6 py-4 bg-gray-50 border-b">
                <h2 className="text-lg font-semibold text-gray-900">Test Results</h2>
              </div>
              <div className="max-h-96 overflow-y-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50 sticky top-0">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Method
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Endpoint
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Duration
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Result
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {testResults.map((result, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`flex items-center ${getStatusColor(result.status)}`}>
                            {getStatusIcon(result.status)}
                            <span className="ml-2 text-sm font-medium">
                              {result.status.toUpperCase()}
                            </span>
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                          {result.method}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                          {result.endpoint}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {result.duration ? `${result.duration}ms` : '-'}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                          {result.error ? (
                            <span className="text-red-600">{result.error}</span>
                          ) : result.response ? (
                            <span className="text-green-600">Success</span>
                          ) : (
                            '-'
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">
              Testing Instructions
            </h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• Make sure the backend server is running on port 8080</li>
              <li>• Ensure you are logged in with appropriate permissions</li>
              <li>• Select a specific category to test or run all tests</li>
              <li>• Check the browser console for detailed logs</li>
              <li>• Green checkmarks indicate successful API calls</li>
              <li>• Red X marks indicate failed API calls (check error details)</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default ComprehensiveAPITestPage;
