'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  BuildingOfficeIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  GlobeAltIcon,
  EnvelopeIcon,
  PhoneIcon,
  MapPinIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';
import { Agency } from '../types';
import DataTable, { Column, ActionButton } from '../components/UI/DataTable';
import StatusBadge from '../components/UI/StatusBadge';
import SearchFilter, { FilterOption } from '../components/UI/SearchFilter';

const AgenciesPage: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    agency_type: '',
    is_active: '',
    sort: 'name',
    order: 'asc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    total_pages: 0
  });

  const fetchAgencies = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        per_page: pagination.per_page,
        search: searchTerm,
        ...filters
      };

      const response = isAuthenticated 
        ? await apiService.getAgencies(params)
        : await apiService.getPublicAgencies(params);
      
      setAgencies(response.data);
      setPagination({
        page: response.page,
        per_page: response.per_page,
        total: response.total,
        total_pages: response.total_pages
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch agencies');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAgencies();
  }, [pagination.page, searchTerm, filters, isAuthenticated]);

  const canEdit = () => {
    if (!user) return false;
    return user.role === 'admin' || user.role === 'editor';
  };



  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this agency?')) return;

    try {
      await apiService.deleteAgency(id);
      fetchAgencies();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete agency');
    }
  };

  // Define table columns
  const columns: Column<Agency>[] = [
    {
      key: 'name',
      label: 'Agency Name',
      sortable: true,
      render: (value, item) => (
        <div>
          <Link href={`/agencies/${item.id}`} className="text-blue-600 hover:text-blue-800 font-medium">
            {value}
          </Link>
          {item.short_name && (
            <p className="text-sm text-gray-500 mt-1">({item.short_name})</p>
          )}
        </div>
      )
    },
    {
      key: 'agency_type',
      label: 'Type',
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {value?.replace('_', ' ').toUpperCase() || 'FEDERAL'}
        </span>
      )
    },
    {
      key: 'description',
      label: 'Description',
      render: (value) => (
        <div className="max-w-xs">
          <p className="text-sm text-gray-600 line-clamp-2">{value || 'No description available'}</p>
        </div>
      )
    },
    {
      key: 'contact_email',
      label: 'Contact',
      render: (value, item) => (
        <div className="text-sm">
          {value && (
            <div className="flex items-center text-gray-600">
              <EnvelopeIcon className="h-4 w-4 mr-1" />
              <a href={`mailto:${value}`} className="hover:text-blue-600">{value}</a>
            </div>
          )}
          {item.contact_phone && (
            <div className="flex items-center text-gray-600 mt-1">
              <PhoneIcon className="h-4 w-4 mr-1" />
              <span>{item.contact_phone}</span>
            </div>
          )}
        </div>
      )
    },
    {
      key: 'website_url',
      label: 'Website',
      render: (value) => value ? (
        <a
          href={value}
          target="_blank"
          rel="noopener noreferrer"
          className="inline-flex items-center text-blue-600 hover:text-blue-800"
        >
          <GlobeAltIcon className="h-4 w-4 mr-1" />
          Visit
        </a>
      ) : '-'
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => <StatusBadge status={value || 'active'} size="sm" />
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    }
  ];

  // Define table actions
  const actions: ActionButton<Agency>[] = [
    {
      label: 'View',
      icon: EyeIcon,
      href: (item) => `/agencies/${parseInt(item.id.toString())}`,
      className: 'text-blue-600 hover:text-blue-800'
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      href: (item) => `/agencies/${parseInt(item.id.toString())}/edit`,
      className: 'text-green-600 hover:text-green-800',
      show: () => isAuthenticated && (user?.role === 'admin' || user?.role === 'editor')
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (item) => handleDelete(item.id),
      className: 'text-red-600 hover:text-red-800',
      show: () => isAuthenticated && user?.role === 'admin'
    }
  ];

  // Define filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'agency_type',
      label: 'Agency Type',
      type: 'select',
      options: [
        { value: 'federal', label: 'Federal' },
        { value: 'independent', label: 'Independent' },
        { value: 'cabinet', label: 'Cabinet Level' },
        { value: 'regulatory', label: 'Regulatory' },
        { value: 'executive', label: 'Executive' }
      ]
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'active', label: 'Active' },
        { value: 'inactive', label: 'Inactive' },
        { value: 'archived', label: 'Archived' }
      ]
    }
  ];

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Government Agencies</h1>
            <p className="text-gray-600">
              Browse federal agencies and their information
            </p>
          </div>
          {isAuthenticated && canEdit() && (
            <Link
              href="/agencies/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Agency
            </Link>
          )}
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <SearchFilter
            searchValue={searchTerm}
            onSearchChange={setSearchTerm}
            filters={filterOptions}
            filterValues={filters}
            onFilterChange={(newFilters) => {
              setFilters(prev => ({ ...prev, ...newFilters }));
              setPagination(prev => ({ ...prev, page: 1 }));
            }}
            placeholder="Search agencies..."
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Agencies Table */}
        <DataTable
          data={agencies}
          columns={columns}
          actions={actions}
          loading={loading}
          error={error}
          pagination={pagination}
          onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
          onSort={(column, direction) => {
            setFilters(prev => ({ ...prev, sort: column, order: direction }));
            setPagination(prev => ({ ...prev, page: 1 }));
          }}
          sortColumn={filters.sort}
          sortDirection={filters.order as 'asc' | 'desc'}
          emptyMessage={
            searchTerm
              ? 'No agencies found matching your search criteria. Try adjusting your search terms or filters.'
              : 'No agencies found. Get started by adding your first agency.'
          }
        />
      </div>
    </Layout>
  );
};

export default AgenciesPage;
