package main

import (
	"bufio"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"

	_ "github.com/lib/pq"
)

// TableDefinition represents a table schema
type TableDefinition struct {
	Name   string
	Schema string
}

// getAllRequiredTables returns all tables that backend handlers expect to exist
func getAllRequiredTables() []TableDefinition {
	return []TableDefinition{
		// Core System Tables
		{
			Name: "users",
			Schema: `CREATE TABLE IF NOT EXISTS users (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				username TEXT NOT NULL UNIQUE,
				email TEXT NOT NULL UNIQUE,
				password_hash TEXT NOT NULL,
				first_name TEXT,
				last_name TEXT,
				title TEXT,
				department TEXT,
				organization TEXT,
				phone TEXT,
				bio TEXT,
				role TEXT DEFAULT 'viewer',
				is_active BOOLEAN DEFAULT true,
				is_verified BOOLEAN DEFAULT false,
				last_login_at TIMESTAMPTZ,
				agency_id BIGINT
			);`,
		},
		{
			Name: "user_sessions",
			Schema: `CREATE TABLE IF NOT EXISTS user_sessions (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				user_id BIGINT NOT NULL,
				session_token TEXT NOT NULL UNIQUE,
				expires_at TIMESTAMPTZ NOT NULL,
				ip_address INET,
				user_agent TEXT,
				is_active BOOLEAN DEFAULT true
			);`,
		},
		{
			Name: "user_preferences",
			Schema: `CREATE TABLE IF NOT EXISTS user_preferences (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				user_id BIGINT NOT NULL UNIQUE,
				documents_per_page INTEGER DEFAULT 25,
				default_view TEXT DEFAULT 'list',
				theme TEXT DEFAULT 'light',
				language TEXT DEFAULT 'en',
				timezone TEXT DEFAULT 'UTC',
				default_search_sort TEXT DEFAULT 'relevance',
				save_search_history BOOLEAN DEFAULT true,
				autocomplete_enabled BOOLEAN DEFAULT true
			);`,
		},
		{
			Name: "agencies",
			Schema: `CREATE TABLE IF NOT EXISTS agencies (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL,
				short_name TEXT UNIQUE,
				slug TEXT NOT NULL UNIQUE,
				description TEXT,
				website TEXT,
				email TEXT,
				phone TEXT,
				address TEXT,
				city TEXT,
				state TEXT,
				zip_code TEXT,
				country TEXT DEFAULT 'US',
				parent_agency_id BIGINT,
				is_active BOOLEAN DEFAULT true,
				agency_type TEXT,
				jurisdiction TEXT,
				established_at TIMESTAMPTZ,
				logo_url TEXT,
				primary_color TEXT,
				secondary_color TEXT
			);`,
		},
		{
			Name: "agency_contacts",
			Schema: `CREATE TABLE IF NOT EXISTS agency_contacts (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				agency_id BIGINT NOT NULL,
				contact_type TEXT NOT NULL,
				name TEXT NOT NULL,
				title TEXT,
				email TEXT,
				phone TEXT,
				address TEXT,
				is_primary BOOLEAN DEFAULT false,
				is_public BOOLEAN DEFAULT true
			);`,
		},
		{
			Name: "categories",
			Schema: `CREATE TABLE IF NOT EXISTS categories (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL UNIQUE,
				slug TEXT NOT NULL UNIQUE,
				description TEXT,
				color TEXT DEFAULT '#3B82F6',
				icon TEXT,
				parent_category_id BIGINT,
				sort_order INTEGER DEFAULT 0,
				is_active BOOLEAN DEFAULT true,
				document_count INTEGER DEFAULT 0,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "tags",
			Schema: `CREATE TABLE IF NOT EXISTS tags (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL UNIQUE,
				slug TEXT NOT NULL UNIQUE,
				description TEXT,
				color TEXT DEFAULT '#6B7280',
				usage_count INTEGER DEFAULT 0,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "subjects",
			Schema: `CREATE TABLE IF NOT EXISTS subjects (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL UNIQUE,
				slug TEXT NOT NULL UNIQUE,
				description TEXT,
				cfr_title TEXT,
				cfr_part TEXT,
				usage_count INTEGER DEFAULT 0,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "documents",
			Schema: `CREATE TABLE IF NOT EXISTS documents (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				title TEXT NOT NULL,
				slug TEXT NOT NULL UNIQUE,
				abstract TEXT,
				content TEXT,
				type TEXT NOT NULL,
				status TEXT DEFAULT 'draft',
				fr_document_number TEXT UNIQUE,
				fr_citation TEXT,
				cfr_citations TEXT,
				publication_date TIMESTAMPTZ,
				effective_date TIMESTAMPTZ,
				termination_date TIMESTAMPTZ,
				comment_due_date TIMESTAMPTZ,
				page_count BIGINT,
				word_count BIGINT,
				language TEXT DEFAULT 'en',
				original_format TEXT,
				file_size BIGINT,
				checksum TEXT,
				agency_id BIGINT NOT NULL,
				created_by_id BIGINT NOT NULL,
				updated_by_id BIGINT,
				visibility_level INTEGER DEFAULT 1,
				is_public BOOLEAN DEFAULT true,
				regulation_count INTEGER DEFAULT 0,
				significant_rule BOOLEAN DEFAULT false,
				economic_impact BOOLEAN DEFAULT false,
				small_entity_impact BOOLEAN DEFAULT false,
				unfunded_mandate BOOLEAN DEFAULT false,
				federalism_impact BOOLEAN DEFAULT false,
				international_impact BOOLEAN DEFAULT false,
				environmental_impact BOOLEAN DEFAULT false,
				energy_impact BOOLEAN DEFAULT false,
				paperwork_reduction_act BOOLEAN DEFAULT false
			);`,
		},
		{
			Name: "document_files",
			Schema: `CREATE TABLE IF NOT EXISTS document_files (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				filename TEXT NOT NULL,
				original_filename TEXT NOT NULL,
				file_path TEXT NOT NULL,
				file_size BIGINT NOT NULL,
				mime_type TEXT NOT NULL,
				file_hash TEXT,
				is_primary BOOLEAN DEFAULT false,
				upload_status TEXT DEFAULT 'pending',
				processing_status TEXT DEFAULT 'pending',
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "document_versions",
			Schema: `CREATE TABLE IF NOT EXISTS document_versions (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				version_number INTEGER NOT NULL,
				title TEXT NOT NULL,
				content TEXT,
				change_summary TEXT,
				is_current BOOLEAN DEFAULT false,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "document_reviews",
			Schema: `CREATE TABLE IF NOT EXISTS document_reviews (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				reviewer_id BIGINT NOT NULL,
				status TEXT NOT NULL,
				review_type TEXT NOT NULL,
				comments TEXT,
				rating INTEGER,
				reviewed_at TIMESTAMPTZ,
				due_date TIMESTAMPTZ
			);`,
		},
		{
			Name: "document_comments",
			Schema: `CREATE TABLE IF NOT EXISTS document_comments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				parent_comment_id BIGINT,
				author_id BIGINT NOT NULL,
				content TEXT NOT NULL,
				comment_type TEXT DEFAULT 'public',
				status TEXT DEFAULT 'active',
				is_anonymous BOOLEAN DEFAULT false,
				attachments TEXT,
				likes_count INTEGER DEFAULT 0,
				replies_count INTEGER DEFAULT 0
			);`,
		},
		{
			Name: "document_category_assignments",
			Schema: `CREATE TABLE IF NOT EXISTS document_category_assignments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				category_id BIGINT NOT NULL,
				assigned_by_id BIGINT NOT NULL,
				UNIQUE(document_id, category_id)
			);`,
		},
		{
			Name: "document_tag_assignments",
			Schema: `CREATE TABLE IF NOT EXISTS document_tag_assignments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				tag_id BIGINT NOT NULL,
				assigned_by_id BIGINT NOT NULL,
				UNIQUE(document_id, tag_id)
			);`,
		},
		{
			Name: "document_subject_assignments",
			Schema: `CREATE TABLE IF NOT EXISTS document_subject_assignments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				subject_id BIGINT NOT NULL,
				assigned_by_id BIGINT NOT NULL,
				UNIQUE(document_id, subject_id)
			);`,
		},
		{
			Name: "laws_and_rules",
			Schema: `CREATE TABLE IF NOT EXISTS laws_and_rules (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				title TEXT NOT NULL,
				slug TEXT NOT NULL UNIQUE,
				description TEXT,
				content TEXT,
				cfr_title TEXT,
				cfr_part TEXT,
				cfr_section TEXT,
				chapter_number TEXT,
				subchapter TEXT,
				part_number TEXT,
				section_number TEXT,
				subsection TEXT,
				effective_date TIMESTAMPTZ,
				expiration_date TIMESTAMPTZ,
				status TEXT DEFAULT 'active',
				version TEXT DEFAULT '1.0',
				authority TEXT,
				source TEXT,
				notes TEXT,
				is_current BOOLEAN DEFAULT true,
				parent_regulation_id BIGINT,
				agency_id BIGINT,
				created_by_id BIGINT NOT NULL,
				updated_by_id BIGINT
			);`,
		},
		{
			Name: "summaries",
			Schema: `CREATE TABLE IF NOT EXISTS summaries (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				title TEXT NOT NULL,
				content TEXT NOT NULL,
				summary_type TEXT NOT NULL,
				entity_type TEXT NOT NULL,
				entity_id BIGINT NOT NULL,
				action_type TEXT NOT NULL,
				publication_date TIMESTAMPTZ,
				is_featured BOOLEAN DEFAULT false,
				is_public BOOLEAN DEFAULT true,
				view_count INTEGER DEFAULT 0,
				agency_id BIGINT,
				category_id BIGINT
			);`,
		},
		{
			Name: "finances",
			Schema: `CREATE TABLE IF NOT EXISTS finances (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL,
				description TEXT,
				amount DECIMAL(15,2) NOT NULL,
				budget_amount DECIMAL(15,2),
				actual_amount DECIMAL(15,2),
				variance_amount DECIMAL(15,2),
				variance_percentage DECIMAL(5,2),
				fiscal_year INTEGER,
				quarter INTEGER,
				month INTEGER,
				category TEXT,
				subcategory TEXT,
				account_code TEXT,
				cost_center TEXT,
				department TEXT,
				project_code TEXT,
				status TEXT DEFAULT 'active',
				notes TEXT,
				created_by_id BIGINT NOT NULL,
				approved_by_id BIGINT,
				approved_at TIMESTAMPTZ
			);`,
		},
		{
			Name: "finance_categories",
			Schema: `CREATE TABLE IF NOT EXISTS finance_categories (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL,
				description TEXT,
				color TEXT DEFAULT '#3B82F6',
				is_active BOOLEAN DEFAULT true
			);`,
		},
		{
			Name: "finance_performance",
			Schema: `CREATE TABLE IF NOT EXISTS finance_performance (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				finance_id BIGINT NOT NULL,
				performance_score DECIMAL(5,2) DEFAULT 0,
				efficiency_rating DECIMAL(5,2) DEFAULT 0,
				cost_effectiveness DECIMAL(5,2) DEFAULT 0,
				budget_adherence DECIMAL(5,2) DEFAULT 0,
				roi_percentage DECIMAL(5,2) DEFAULT 0,
				evaluation_period TEXT,
				evaluation_notes TEXT,
				evaluated_by_id BIGINT,
				evaluated_at TIMESTAMPTZ
			);`,
		},
		{
			Name: "system_events",
			Schema: `CREATE TABLE IF NOT EXISTS system_events (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				event_type TEXT NOT NULL,
				entity_id BIGINT,
				user_id BIGINT,
				data JSONB,
				processed BOOLEAN DEFAULT false,
				error TEXT
			);
			CREATE INDEX IF NOT EXISTS idx_system_events_event_type ON system_events(event_type);
			CREATE INDEX IF NOT EXISTS idx_system_events_user_id ON system_events(user_id);`,
		},
		{
			Name: "auto_generation_configs",
			Schema: `CREATE TABLE IF NOT EXISTS auto_generation_configs (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				entity_type TEXT NOT NULL,
				trigger_event TEXT NOT NULL,
				generation_type TEXT NOT NULL,
				template_content TEXT,
				configuration JSONB,
				is_active BOOLEAN DEFAULT true,
				description TEXT,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "entity_relationships",
			Schema: `CREATE TABLE IF NOT EXISTS entity_relationships (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				source_entity_type TEXT NOT NULL,
				source_entity_id BIGINT NOT NULL,
				target_entity_type TEXT NOT NULL,
				target_entity_id BIGINT NOT NULL,
				relationship_type TEXT NOT NULL,
				relationship_data JSONB,
				strength DECIMAL(3,2) DEFAULT 1.0,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "tasks",
			Schema: `CREATE TABLE IF NOT EXISTS tasks (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				title TEXT NOT NULL,
				description TEXT,
				status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled', 'on_hold')),
				priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
				category TEXT,
				assigned_to_id BIGINT,
				created_by_id BIGINT NOT NULL,
				due_date TIMESTAMPTZ,
				start_date TIMESTAMPTZ,
				end_date TIMESTAMPTZ,
				duration INTEGER,
				is_all_day BOOLEAN DEFAULT false,
				time_zone TEXT DEFAULT 'UTC',
				is_recurring BOOLEAN DEFAULT false,
				recurrence_rule TEXT,
				recurrence_end TIMESTAMPTZ,
				parent_task_id BIGINT,
				progress_percentage DECIMAL(5,2) DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
				estimated_hours DECIMAL(8,2),
				actual_hours DECIMAL(8,2),
				tags TEXT,
				attachments TEXT,
				notes TEXT,
				performance_percentage DECIMAL(5,2) DEFAULT 0 CHECK (performance_percentage >= 0 AND performance_percentage <= 100),
				deadline_adherence_score DECIMAL(5,2) DEFAULT 0 CHECK (deadline_adherence_score >= 0 AND deadline_adherence_score <= 100),
				quality_score DECIMAL(5,2) DEFAULT 0 CHECK (quality_score >= 0 AND quality_score <= 100),
				completion_efficiency DECIMAL(5,2) DEFAULT 0 CHECK (completion_efficiency >= 0 AND completion_efficiency <= 100),
				priority_handling_score DECIMAL(5,2) DEFAULT 0 CHECK (priority_handling_score >= 0 AND priority_handling_score <= 100),
				performance_notes TEXT,
				evaluation_date TIMESTAMPTZ
			);`,
		},
		// Business Intelligence Tables
		{
			Name: "data_sources",
			Schema: `CREATE TABLE IF NOT EXISTS data_sources (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				source_name TEXT NOT NULL UNIQUE,
				source_type TEXT NOT NULL,
				connection_string TEXT,
				configuration JSONB,
				is_active BOOLEAN DEFAULT true,
				last_sync TIMESTAMPTZ,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "data_warehouses",
			Schema: `CREATE TABLE IF NOT EXISTS data_warehouses (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				warehouse_name TEXT NOT NULL UNIQUE,
				warehouse_type TEXT NOT NULL,
				connection_details JSONB,
				storage_capacity BIGINT,
				current_usage BIGINT DEFAULT 0,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "dashboards",
			Schema: `CREATE TABLE IF NOT EXISTS dashboards (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				dashboard_name TEXT NOT NULL,
				description TEXT,
				layout_config JSONB,
				widgets JSONB,
				is_public BOOLEAN DEFAULT false,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL,
				last_accessed TIMESTAMPTZ,
				access_count INTEGER DEFAULT 0
			);`,
		},
		{
			Name: "reports",
			Schema: `CREATE TABLE IF NOT EXISTS reports (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				report_name TEXT NOT NULL,
				description TEXT,
				report_type TEXT NOT NULL,
				query_definition JSONB,
				parameters JSONB,
				schedule_config JSONB,
				output_format TEXT DEFAULT 'pdf',
				is_scheduled BOOLEAN DEFAULT false,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL,
				last_generated TIMESTAMPTZ
			);`,
		},
		{
			Name: "kpis",
			Schema: `CREATE TABLE IF NOT EXISTS kpis (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				kpi_name TEXT NOT NULL,
				description TEXT,
				category TEXT,
				unit TEXT,
				target_value DECIMAL(15,2),
				current_value DECIMAL(15,2),
				update_frequency TEXT,
				calculation_method TEXT,
				data_source_id BIGINT,
				owner_id BIGINT,
				is_active BOOLEAN DEFAULT true,
				last_updated TIMESTAMPTZ
			);`,
		},
		{
			Name: "data_mining",
			Schema: `CREATE TABLE IF NOT EXISTS data_mining (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				project_name TEXT NOT NULL,
				description TEXT,
				algorithm_type TEXT NOT NULL,
				data_source_id BIGINT,
				configuration JSONB,
				status TEXT DEFAULT 'pending',
				results JSONB,
				accuracy_score DECIMAL(5,4),
				created_by_id BIGINT NOT NULL
			);`,
		},
		// HR Tables
		{
			Name: "employees",
			Schema: `CREATE TABLE IF NOT EXISTS employees (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				employee_id TEXT NOT NULL UNIQUE,
				user_id BIGINT,
				first_name TEXT NOT NULL,
				last_name TEXT NOT NULL,
				email TEXT NOT NULL UNIQUE,
				phone TEXT,
				department_id BIGINT,
				position_id BIGINT,
				hire_date DATE,
				termination_date DATE,
				employment_status TEXT DEFAULT 'active',
				job_title TEXT,
				manager_id BIGINT,
				base_salary DECIMAL(12,2) DEFAULT 0,
				currency TEXT DEFAULT 'USD',
				pay_frequency TEXT DEFAULT 'monthly'
			);`,
		},
		{
			Name: "departments",
			Schema: `CREATE TABLE IF NOT EXISTS departments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				department_code TEXT UNIQUE NOT NULL,
				name TEXT NOT NULL,
				description TEXT,
				parent_department_id BIGINT,
				level INTEGER DEFAULT 0,
				manager_id BIGINT,
				budget_amount DECIMAL(15,2) DEFAULT 0,
				actual_spend DECIMAL(15,2) DEFAULT 0,
				location TEXT,
				is_active BOOLEAN DEFAULT true,
				employee_count INTEGER DEFAULT 0
			);`,
		},
		{
			Name: "positions",
			Schema: `CREATE TABLE IF NOT EXISTS positions (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				position_code TEXT UNIQUE NOT NULL,
				title TEXT NOT NULL,
				description TEXT,
				level TEXT,
				grade TEXT,
				job_family TEXT,
				department_id BIGINT NOT NULL,
				reports_to_id BIGINT,
				requirements TEXT,
				qualifications TEXT,
				skills TEXT,
				min_salary DECIMAL(12,2) DEFAULT 0,
				max_salary DECIMAL(12,2) DEFAULT 0,
				currency TEXT DEFAULT 'USD',
				is_active BOOLEAN DEFAULT true,
				is_approved BOOLEAN DEFAULT false,
				headcount_limit INTEGER DEFAULT 1,
				current_count INTEGER DEFAULT 0
			);`,
		},
		{
			Name: "performance_reviews",
			Schema: `CREATE TABLE IF NOT EXISTS performance_reviews (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				employee_id BIGINT NOT NULL,
				reviewer_id BIGINT NOT NULL,
				review_period_start DATE NOT NULL,
				review_period_end DATE NOT NULL,
				overall_rating DECIMAL(3,2),
				goals_achievement DECIMAL(3,2),
				competency_rating DECIMAL(3,2),
				development_areas TEXT,
				achievements TEXT,
				goals_next_period TEXT,
				status TEXT DEFAULT 'draft',
				submitted_at TIMESTAMPTZ,
				approved_at TIMESTAMPTZ
			);`,
		},
		{
			Name: "trainings",
			Schema: `CREATE TABLE IF NOT EXISTS trainings (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				training_name TEXT NOT NULL,
				description TEXT,
				training_type TEXT,
				provider TEXT,
				duration_hours DECIMAL(6,2),
				cost DECIMAL(10,2),
				max_participants INTEGER,
				prerequisites TEXT,
				learning_objectives TEXT,
				materials TEXT,
				is_mandatory BOOLEAN DEFAULT false,
				is_active BOOLEAN DEFAULT true
			);`,
		},
		{
			Name: "training_enrollments",
			Schema: `CREATE TABLE IF NOT EXISTS training_enrollments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				employee_id BIGINT NOT NULL,
				training_id BIGINT NOT NULL,
				enrollment_date DATE NOT NULL,
				completion_date DATE,
				status TEXT DEFAULT 'enrolled',
				score DECIMAL(5,2),
				feedback TEXT,
				certificate_issued BOOLEAN DEFAULT false,
				certificate_url TEXT
			);`,
		},
		// Compliance and Audit Tables
		{
			Name: "compliance_requirements",
			Schema: `CREATE TABLE IF NOT EXISTS compliance_requirements (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				requirement_name TEXT NOT NULL,
				requirement_code TEXT UNIQUE,
				description TEXT,
				category TEXT,
				severity TEXT DEFAULT 'medium',
				compliance_framework TEXT,
				implementation_status TEXT DEFAULT 'pending',
				due_date DATE,
				responsible_party_id BIGINT,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "compliance_findings",
			Schema: `CREATE TABLE IF NOT EXISTS compliance_findings (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				finding_title TEXT NOT NULL,
				description TEXT,
				severity TEXT DEFAULT 'medium',
				status TEXT DEFAULT 'open',
				requirement_id BIGINT,
				identified_by_id BIGINT,
				assigned_to_id BIGINT,
				due_date DATE,
				resolution_notes TEXT,
				resolved_at TIMESTAMPTZ
			);`,
		},
		{
			Name: "risk_assessments",
			Schema: `CREATE TABLE IF NOT EXISTS risk_assessments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				assessment_name TEXT NOT NULL,
				description TEXT,
				risk_category TEXT,
				probability_score INTEGER,
				impact_score INTEGER,
				risk_level TEXT,
				mitigation_strategy TEXT,
				owner_id BIGINT,
				status TEXT DEFAULT 'active',
				review_date DATE,
				next_review_date DATE
			);`,
		},
		{
			Name: "policy_management",
			Schema: `CREATE TABLE IF NOT EXISTS policy_management (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				policy_name TEXT NOT NULL,
				policy_number TEXT UNIQUE,
				description TEXT,
				policy_type TEXT,
				effective_date DATE,
				expiration_date DATE,
				status TEXT DEFAULT 'draft',
				content TEXT,
				approval_required BOOLEAN DEFAULT true,
				approved_by_id BIGINT,
				approved_at TIMESTAMPTZ,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "audit_logs",
			Schema: `CREATE TABLE IF NOT EXISTS audit_logs (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				action TEXT NOT NULL,
				entity_type TEXT NOT NULL,
				entity_id BIGINT,
				user_id BIGINT,
				changes JSONB,
				ip_address INET,
				user_agent TEXT,
				session_id TEXT,
				request_id TEXT
			);`,
		},
		{
			Name: "compliance_rules",
			Schema: `CREATE TABLE IF NOT EXISTS compliance_rules (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				rule_name TEXT NOT NULL,
				description TEXT,
				rule_type TEXT,
				conditions JSONB,
				actions JSONB,
				is_active BOOLEAN DEFAULT true,
				severity TEXT DEFAULT 'medium',
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "compliance_violations",
			Schema: `CREATE TABLE IF NOT EXISTS compliance_violations (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				rule_id BIGINT NOT NULL,
				entity_type TEXT NOT NULL,
				entity_id BIGINT NOT NULL,
				violation_details JSONB,
				severity TEXT,
				status TEXT DEFAULT 'open',
				resolved_at TIMESTAMPTZ,
				resolution_notes TEXT
			);`,
		},
		{
			Name: "compliance_assessments",
			Schema: `CREATE TABLE IF NOT EXISTS compliance_assessments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				assessment_name TEXT NOT NULL,
				description TEXT,
				framework TEXT,
				scope TEXT,
				status TEXT DEFAULT 'planning',
				start_date DATE,
				end_date DATE,
				assessor_id BIGINT,
				overall_score DECIMAL(5,2),
				findings_count INTEGER DEFAULT 0,
				recommendations TEXT
			);`,
		},
		{
			Name: "compliance_reports",
			Schema: `CREATE TABLE IF NOT EXISTS compliance_reports (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				report_name TEXT NOT NULL,
				report_type TEXT,
				period_start DATE,
				period_end DATE,
				content TEXT,
				summary TEXT,
				recommendations TEXT,
				status TEXT DEFAULT 'draft',
				generated_by_id BIGINT,
				approved_by_id BIGINT,
				approved_at TIMESTAMPTZ
			);`,
		},
		// Role Management Tables
		{
			Name: "user_roles",
			Schema: `CREATE TABLE IF NOT EXISTS user_roles (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL UNIQUE,
				description TEXT,
				is_system_role BOOLEAN DEFAULT false,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT
			);`,
		},
		{
			Name: "role_permissions",
			Schema: `CREATE TABLE IF NOT EXISTS role_permissions (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				role_id BIGINT NOT NULL,
				permission TEXT NOT NULL,
				resource TEXT,
				action TEXT,
				conditions JSONB,
				is_granted BOOLEAN DEFAULT true
			);`,
		},
		// Notification Tables
		{
			Name: "notifications",
			Schema: `CREATE TABLE IF NOT EXISTS notifications (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				user_id BIGINT NOT NULL,
				title TEXT NOT NULL,
				message TEXT,
				type TEXT DEFAULT 'info',
				is_read BOOLEAN DEFAULT false,
				action_url TEXT,
				expires_at TIMESTAMPTZ,
				priority TEXT DEFAULT 'normal',
				category TEXT,
				metadata JSONB
			);`,
		},
		// Interconnect Tables
		{
			Name: "interconnects",
			Schema: `CREATE TABLE IF NOT EXISTS interconnects (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL,
				description TEXT,
				source_entity_type TEXT NOT NULL,
				source_entity_id BIGINT NOT NULL,
				target_entity_type TEXT NOT NULL,
				target_entity_id BIGINT NOT NULL,
				connection_type TEXT NOT NULL,
				connection_data JSONB,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL
			);`,
		},
		{
			Name: "proceeding_integrations",
			Schema: `CREATE TABLE IF NOT EXISTS proceeding_integrations (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				proceeding_id BIGINT NOT NULL,
				integration_type TEXT NOT NULL,
				external_system TEXT,
				external_id TEXT,
				configuration JSONB,
				status TEXT DEFAULT 'active',
				last_sync TIMESTAMPTZ,
				sync_status TEXT,
				error_message TEXT
			);`,
		},
		// Additional Core Tables
		{
			Name: "agency_categories",
			Schema: `CREATE TABLE IF NOT EXISTS agency_categories (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL UNIQUE,
				description TEXT,
				color TEXT DEFAULT '#3B82F6',
				is_active BOOLEAN DEFAULT true
			);`,
		},
		{
			Name: "agency_category_assignments",
			Schema: `CREATE TABLE IF NOT EXISTS agency_category_assignments (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				agency_id BIGINT NOT NULL,
				category_id BIGINT NOT NULL,
				assigned_by_id BIGINT NOT NULL,
				UNIQUE(agency_id, category_id)
			);`,
		},
		// Enterprise Financial Management Tables
		{
			Name: "chart_of_accounts",
			Schema: `CREATE TABLE IF NOT EXISTS chart_of_accounts (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				account_code TEXT NOT NULL UNIQUE,
				account_name TEXT NOT NULL,
				account_type TEXT NOT NULL,
				description TEXT,
				parent_account_id BIGINT,
				level INTEGER DEFAULT 0,
				is_active BOOLEAN DEFAULT true,
				is_control_account BOOLEAN DEFAULT false,
				allow_posting BOOLEAN DEFAULT true,
				normal_balance TEXT NOT NULL,
				current_balance DECIMAL(15,2) DEFAULT 0,
				opening_balance DECIMAL(15,2) DEFAULT 0,
				currency_code TEXT DEFAULT 'USD',
				tax_code TEXT,
				reporting_code TEXT,
				gl_class TEXT,
				metadata TEXT
			);`,
		},
		{
			Name: "general_ledger",
			Schema: `CREATE TABLE IF NOT EXISTS general_ledger (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				transaction_id TEXT NOT NULL UNIQUE,
				transaction_date DATE NOT NULL,
				account_id BIGINT NOT NULL,
				transaction_type TEXT NOT NULL,
				amount DECIMAL(15,2) NOT NULL,
				description TEXT,
				reference_number TEXT,
				source_document TEXT,
				journal_entry_id BIGINT,
				created_by_id BIGINT,
				approved_by_id BIGINT,
				approved_at TIMESTAMPTZ,
				currency_code TEXT DEFAULT 'USD',
				exchange_rate DECIMAL(10,6) DEFAULT 1.0,
				base_currency_amount DECIMAL(15,2),
				metadata TEXT
			);`,
		},
		{
			Name: "budget_plans",
			Schema: `CREATE TABLE IF NOT EXISTS budget_plans (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				budget_code TEXT NOT NULL UNIQUE,
				budget_name TEXT NOT NULL,
				description TEXT,
				fiscal_year INTEGER NOT NULL,
				budget_period TEXT NOT NULL,
				start_date DATE NOT NULL,
				end_date DATE NOT NULL,
				budget_type TEXT DEFAULT 'operational',
				budget_category TEXT,
				planned_amount DECIMAL(15,2) NOT NULL,
				revised_amount DECIMAL(15,2),
				actual_amount DECIMAL(15,2) DEFAULT 0,
				committed_amount DECIMAL(15,2) DEFAULT 0,
				variance_amount DECIMAL(15,2) DEFAULT 0,
				variance_percent DECIMAL(5,2) DEFAULT 0,
				department_id BIGINT,
				cost_center_id BIGINT,
				created_by_id BIGINT,
				approved_by_id BIGINT,
				approved_at TIMESTAMPTZ,
				status TEXT DEFAULT 'draft',
				currency_code TEXT DEFAULT 'USD',
				metadata TEXT
			);`,
		},
		{
			Name: "cost_centers",
			Schema: `CREATE TABLE IF NOT EXISTS cost_centers (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				cost_center_code TEXT NOT NULL UNIQUE,
				cost_center_name TEXT NOT NULL,
				description TEXT,
				cost_center_type TEXT NOT NULL,
				parent_cost_center_id BIGINT,
				level INTEGER DEFAULT 0,
				is_active BOOLEAN DEFAULT true,
				manager_id BIGINT,
				budget_amount DECIMAL(15,2) DEFAULT 0,
				actual_amount DECIMAL(15,2) DEFAULT 0,
				committed_amount DECIMAL(15,2) DEFAULT 0,
				allocation_method TEXT DEFAULT 'direct',
				allocation_base TEXT,
				allocation_rate DECIMAL(10,4) DEFAULT 0,
				currency_code TEXT DEFAULT 'USD',
				metadata TEXT
			);`,
		},
		{
			Name: "financial_reports",
			Schema: `CREATE TABLE IF NOT EXISTS financial_reports (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				report_code TEXT NOT NULL UNIQUE,
				report_name TEXT NOT NULL,
				report_type TEXT NOT NULL,
				description TEXT,
				period_type TEXT NOT NULL,
				start_date DATE NOT NULL,
				end_date DATE NOT NULL,
				generated_at TIMESTAMPTZ,
				generated_by_id BIGINT,
				report_data TEXT,
				report_format TEXT DEFAULT 'json',
				file_path TEXT,
				status TEXT DEFAULT 'draft',
				reviewed_by_id BIGINT,
				reviewed_at TIMESTAMPTZ,
				distribution_list TEXT,
				published_at TIMESTAMPTZ,
				metadata TEXT
			);`,
		},
		// Content Management Tables
		{
			Name: "content_repositories",
			Schema: `CREATE TABLE IF NOT EXISTS content_repositories (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL,
				description TEXT,
				type TEXT DEFAULT 'document',
				storage_type TEXT,
				storage_path TEXT,
				storage_config TEXT,
				max_size BIGINT DEFAULT 1073741824,
				current_size BIGINT DEFAULT 0,
				total_size BIGINT DEFAULT 0,
				max_files INTEGER DEFAULT 10000,
				current_files INTEGER DEFAULT 0,
				file_count INTEGER DEFAULT 0,
				classification TEXT DEFAULT 'internal',
				encryption_key TEXT,
				access_policy TEXT,
				versioning_enabled BOOLEAN DEFAULT true,
				max_versions INTEGER DEFAULT 10,
				backup_enabled BOOLEAN DEFAULT true,
				backup_schedule TEXT DEFAULT 'daily',
				retention_policy_id BIGINT REFERENCES retention_policies(id),
				compliance_level TEXT DEFAULT 'standard',
				is_active BOOLEAN DEFAULT true,
				last_accessed TIMESTAMPTZ,
				access_count BIGINT DEFAULT 0,
				metadata TEXT
			);`,
		},
		{
			Name: "content_templates",
			Schema: `CREATE TABLE IF NOT EXISTS content_templates (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				template_name TEXT NOT NULL,
				description TEXT,
				template_type TEXT NOT NULL,
				content_type TEXT NOT NULL,
				template_content TEXT NOT NULL,
				variables JSONB,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL,
				usage_count INTEGER DEFAULT 0,
				last_used TIMESTAMPTZ,
				tags TEXT,
				metadata TEXT
			);`,
		},
		{
			Name: "content_versions",
			Schema: `CREATE TABLE IF NOT EXISTS content_versions (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				content_id BIGINT NOT NULL,
				version_number TEXT NOT NULL,
				title TEXT NOT NULL,
				content TEXT NOT NULL,
				change_summary TEXT,
				is_current BOOLEAN DEFAULT false,
				created_by_id BIGINT NOT NULL,
				file_size BIGINT,
				checksum TEXT,
				metadata TEXT
			);`,
		},
		{
			Name: "content_workflows",
			Schema: `CREATE TABLE IF NOT EXISTS content_workflows (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				workflow_name TEXT NOT NULL,
				description TEXT,
				workflow_type TEXT NOT NULL,
				steps JSONB NOT NULL,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL,
				metadata TEXT
			);`,
		},
		{
			Name: "content_workflow_instances",
			Schema: `CREATE TABLE IF NOT EXISTS content_workflow_instances (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				workflow_id BIGINT NOT NULL,
				content_id BIGINT NOT NULL,
				current_step INTEGER DEFAULT 1,
				status TEXT DEFAULT 'pending',
				started_by_id BIGINT NOT NULL,
				completed_at TIMESTAMPTZ,
				metadata TEXT
			);`,
		},
		{
			Name: "content_collaborations",
			Schema: `CREATE TABLE IF NOT EXISTS content_collaborations (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				content_id BIGINT NOT NULL,
				user_id BIGINT NOT NULL,
				permission_level TEXT NOT NULL,
				invited_by_id BIGINT NOT NULL,
				accepted_at TIMESTAMPTZ,
				last_accessed TIMESTAMPTZ,
				is_active BOOLEAN DEFAULT true
			);`,
		},
		// Document Processing Tables
		{
			Name: "document_processing_jobs",
			Schema: `CREATE TABLE IF NOT EXISTS document_processing_jobs (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				job_type TEXT NOT NULL,
				status TEXT DEFAULT 'pending',
				priority INTEGER DEFAULT 5,
				started_at TIMESTAMPTZ,
				completed_at TIMESTAMPTZ,
				error_message TEXT,
				result_data JSONB,
				processing_time INTEGER,
				created_by_id BIGINT NOT NULL,
				metadata TEXT
			);`,
		},
		{
			Name: "extracted_metadata",
			Schema: `CREATE TABLE IF NOT EXISTS extracted_metadata (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				metadata_type TEXT NOT NULL,
				metadata_key TEXT NOT NULL,
				metadata_value TEXT,
				confidence_score DECIMAL(5,4),
				extraction_method TEXT,
				verified BOOLEAN DEFAULT false,
				verified_by_id BIGINT,
				verified_at TIMESTAMPTZ
			);`,
		},
		{
			Name: "document_classifications",
			Schema: `CREATE TABLE IF NOT EXISTS document_classifications (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				classification_type TEXT NOT NULL,
				classification_value TEXT NOT NULL,
				confidence_score DECIMAL(5,4),
				classification_method TEXT,
				is_verified BOOLEAN DEFAULT false,
				verified_by_id BIGINT,
				verified_at TIMESTAMPTZ,
				metadata TEXT
			);`,
		},
		{
			Name: "processing_templates",
			Schema: `CREATE TABLE IF NOT EXISTS processing_templates (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				template_name TEXT NOT NULL,
				description TEXT,
				document_type TEXT NOT NULL,
				processing_steps JSONB NOT NULL,
				is_active BOOLEAN DEFAULT true,
				created_by_id BIGINT NOT NULL,
				usage_count INTEGER DEFAULT 0,
				success_rate DECIMAL(5,2) DEFAULT 0,
				avg_processing_time INTEGER DEFAULT 0,
				metadata TEXT
			);`,
		},
		{
			Name: "extracted_entities",
			Schema: `CREATE TABLE IF NOT EXISTS extracted_entities (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				document_id BIGINT NOT NULL,
				entity_type TEXT NOT NULL,
				entity_value TEXT NOT NULL,
				start_position INTEGER,
				end_position INTEGER,
				confidence_score DECIMAL(5,4),
				extraction_method TEXT,
				is_verified BOOLEAN DEFAULT false,
				verified_by_id BIGINT,
				verified_at TIMESTAMPTZ
			);`,
		},
		{
			Name: "processing_logs",
			Schema: `CREATE TABLE IF NOT EXISTS processing_logs (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				job_id BIGINT NOT NULL,
				log_level TEXT NOT NULL,
				message TEXT NOT NULL,
				step_name TEXT,
				execution_time INTEGER,
				memory_usage BIGINT,
				additional_data JSONB
			);`,
		},
		// Workflow Engine Tables
		{
			Name: "workflow_templates",
			Schema: `CREATE TABLE IF NOT EXISTS workflow_templates (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				name TEXT NOT NULL,
				description TEXT,
				category TEXT,
				type TEXT DEFAULT 'sequential',
				default_sla_hours INTEGER DEFAULT 48,
				allow_parallel_steps BOOLEAN DEFAULT false,
				require_all_approvals BOOLEAN DEFAULT true,
				enable_escalation BOOLEAN DEFAULT true,
				escalation_hours INTEGER DEFAULT 72,
				created_by_id BIGINT NOT NULL,
				step_definitions JSONB,
				escalation_rules JSONB,
				is_active BOOLEAN DEFAULT true,
				usage_count INTEGER DEFAULT 0,
				success_rate DECIMAL(5,2) DEFAULT 0,
				metadata TEXT
			);`,
		},
		{
			Name: "workflow_instances",
			Schema: `CREATE TABLE IF NOT EXISTS workflow_instances (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				template_id BIGINT NOT NULL,
				entity_type TEXT NOT NULL,
				entity_id BIGINT NOT NULL,
				status TEXT DEFAULT 'pending',
				current_step INTEGER DEFAULT 1,
				total_steps INTEGER NOT NULL,
				started_by_id BIGINT NOT NULL,
				started_at TIMESTAMPTZ DEFAULT NOW(),
				completed_at TIMESTAMPTZ,
				due_date TIMESTAMPTZ,
				priority TEXT DEFAULT 'medium',
				context_data JSONB,
				metadata TEXT
			);`,
		},
		{
			Name: "workflow_steps",
			Schema: `CREATE TABLE IF NOT EXISTS workflow_steps (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				instance_id BIGINT NOT NULL,
				step_number INTEGER NOT NULL,
				step_name TEXT NOT NULL,
				step_type TEXT NOT NULL,
				status TEXT DEFAULT 'pending',
				assigned_to_id BIGINT,
				started_at TIMESTAMPTZ,
				completed_at TIMESTAMPTZ,
				due_date TIMESTAMPTZ,
				comments TEXT,
				decision TEXT,
				step_data JSONB,
				metadata TEXT
			);`,
		},
		{
			Name: "workflow_approvals",
			Schema: `CREATE TABLE IF NOT EXISTS workflow_approvals (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				step_id BIGINT NOT NULL,
				approver_id BIGINT NOT NULL,
				status TEXT DEFAULT 'pending',
				decision TEXT,
				comments TEXT,
				approved_at TIMESTAMPTZ,
				is_required BOOLEAN DEFAULT true,
				approval_order INTEGER DEFAULT 1
			);`,
		},
		{
			Name: "workflow_escalations",
			Schema: `CREATE TABLE IF NOT EXISTS workflow_escalations (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				instance_id BIGINT NOT NULL,
				step_id BIGINT,
				escalation_type TEXT NOT NULL,
				escalated_to_id BIGINT NOT NULL,
				escalated_by_id BIGINT,
				escalated_at TIMESTAMPTZ DEFAULT NOW(),
				reason TEXT,
				resolved_at TIMESTAMPTZ,
				resolution TEXT
			);`,
		},
		{
			Name: "workflow_notifications",
			Schema: `CREATE TABLE IF NOT EXISTS workflow_notifications (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				instance_id BIGINT NOT NULL,
				step_id BIGINT,
				recipient_id BIGINT NOT NULL,
				notification_type TEXT NOT NULL,
				title TEXT NOT NULL,
				message TEXT NOT NULL,
				sent_at TIMESTAMPTZ,
				read_at TIMESTAMPTZ,
				action_url TEXT,
				is_sent BOOLEAN DEFAULT false
			);`,
		},
		{
			Name: "workflow_audit_logs",
			Schema: `CREATE TABLE IF NOT EXISTS workflow_audit_logs (
				id BIGSERIAL PRIMARY KEY,
				created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
				deleted_at TIMESTAMPTZ,
				instance_id BIGINT NOT NULL,
				step_id BIGINT,
				user_id BIGINT NOT NULL,
				action TEXT NOT NULL,
				old_values JSONB,
				new_values JSONB,
				ip_address INET,
				user_agent TEXT,
				session_id TEXT
			);`,
		},
	}
}

func main() {
	// Load environment variables from .env file
	loadEnv("../.env")

	// Database connection
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "federal_register_db")

	connStr := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("🔍 Checking database tables...")

	requiredTables := getAllRequiredTables()
	missingTables := []TableDefinition{}
	existingTables := []string{}

	// Check which tables exist
	for _, table := range requiredTables {
		exists, err := tableExists(db, table.Name)
		if err != nil {
			log.Printf("Error checking table %s: %v", table.Name, err)
			continue
		}

		if exists {
			existingTables = append(existingTables, table.Name)
		} else {
			missingTables = append(missingTables, table)
		}
	}

	fmt.Printf("✅ Found %d existing tables\n", len(existingTables))
	fmt.Printf("❌ Found %d missing tables\n", len(missingTables))

	if len(missingTables) == 0 {
		fmt.Println("🎉 All required tables exist!")
		return
	}

	fmt.Println("\n📋 Missing tables:")
	for _, table := range missingTables {
		fmt.Printf("  - %s\n", table.Name)
	}

	fmt.Println("\n🔧 Creating missing tables...")

	// Create missing tables
	for _, table := range missingTables {
		fmt.Printf("Creating table: %s... ", table.Name)

		if err := createTable(db, table); err != nil {
			fmt.Printf("❌ Failed: %v\n", err)
		} else {
			fmt.Printf("✅ Success\n")
		}
	}

	fmt.Println("\n🎉 Database verification and migration complete!")
}

func tableExists(db *sql.DB, tableName string) (bool, error) {
	query := `
		SELECT EXISTS (
			SELECT FROM information_schema.tables 
			WHERE table_schema = 'public' 
			AND table_name = $1
		);`

	var exists bool
	err := db.QueryRow(query, tableName).Scan(&exists)
	return exists, err
}

func createTable(db *sql.DB, table TableDefinition) error {
	// Execute the schema (may contain multiple statements)
	statements := strings.Split(table.Schema, ";")

	for _, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" {
			continue
		}

		if _, err := db.Exec(stmt); err != nil {
			return fmt.Errorf("failed to execute statement: %v", err)
		}
	}

	return nil
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// loadEnv loads environment variables from a .env file
func loadEnv(filename string) {
	file, err := os.Open(filename)
	if err != nil {
		log.Printf("Warning: Could not open .env file: %v", err)
		return
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		parts := strings.SplitN(line, "=", 2)
		if len(parts) == 2 {
			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			os.Setenv(key, value)
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Warning: Error reading .env file: %v", err)
	}
}
