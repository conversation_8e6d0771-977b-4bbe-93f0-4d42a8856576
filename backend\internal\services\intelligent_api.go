package services

import (
	"fmt"
	"strings"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// IntelligentAPIService handles automatic API calls based on parsed text
type IntelligentAPIService struct {
	db          *gorm.DB
	textParser  *TextParserService
	taskService *TaskService
}

// APIResult represents the result of an automatic API call
type APIResult struct {
	Type       string      `json:"type"`   // "task", "agency", "category"
	Action     string      `json:"action"` // "created", "updated", "failed"
	EntityID   *uint       `json:"entity_id,omitempty"`
	EntityName string      `json:"entity_name"`
	Error      string      `json:"error,omitempty"`
	SourceText string      `json:"source_text"`
	Confidence float64     `json:"confidence"`
	Metadata   interface{} `json:"metadata,omitempty"`
}

// ProcessingResult contains all results from processing text
type ProcessingResult struct {
	ParsedItems []ParsedItem `json:"parsed_items"`
	APIResults  []APIResult  `json:"api_results"`
	Summary     string       `json:"summary"`
	Success     bool         `json:"success"`
	Errors      []string     `json:"errors,omitempty"`
}

// NewIntelligentAPIService creates a new intelligent API service
func NewIntelligentAPIService() *IntelligentAPIService {
	return &IntelligentAPIService{
		db:          database.GetDB(),
		textParser:  NewTextParserService(),
		taskService: NewTaskService(),
	}
}

// ProcessDocumentText processes document content and executes API calls
func (s *IntelligentAPIService) ProcessDocumentText(
	content string,
	documentID uint,
	userID uint,
	autoExecute bool,
) (*ProcessingResult, error) {

	// Parse the text
	parsedItems, err := s.textParser.ParseText(content, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to parse text: %w", err)
	}

	result := &ProcessingResult{
		ParsedItems: parsedItems,
		APIResults:  []APIResult{},
		Success:     true,
		Errors:      []string{},
	}

	// If autoExecute is false, just return the parsed items for user confirmation
	if !autoExecute {
		result.Summary = fmt.Sprintf("Found %d actionable items in text", len(parsedItems))
		return result, nil
	}

	// Execute API calls for each parsed item
	for _, item := range parsedItems {
		apiResult := s.executeAPICall(item, documentID, userID)
		result.APIResults = append(result.APIResults, apiResult)

		if apiResult.Error != "" {
			result.Errors = append(result.Errors, apiResult.Error)
		}
	}

	// Generate summary
	result.Summary = s.generateProcessingSummary(result)
	result.Success = len(result.Errors) == 0

	return result, nil
}

// ProcessRegulationText processes regulation content and executes API calls
func (s *IntelligentAPIService) ProcessRegulationText(
	content string,
	regulationID uint,
	userID uint,
	autoExecute bool,
) (*ProcessingResult, error) {

	// Parse the text
	parsedItems, err := s.textParser.ParseText(content, time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to parse text: %w", err)
	}

	result := &ProcessingResult{
		ParsedItems: parsedItems,
		APIResults:  []APIResult{},
		Success:     true,
		Errors:      []string{},
	}

	// If autoExecute is false, just return the parsed items for user confirmation
	if !autoExecute {
		result.Summary = fmt.Sprintf("Found %d actionable items in text", len(parsedItems))
		return result, nil
	}

	// Execute API calls for each parsed item
	for _, item := range parsedItems {
		apiResult := s.executeAPICallForRegulation(item, regulationID, userID)
		result.APIResults = append(result.APIResults, apiResult)

		if apiResult.Error != "" {
			result.Errors = append(result.Errors, apiResult.Error)
		}
	}

	// Generate summary
	result.Summary = s.generateProcessingSummary(result)
	result.Success = len(result.Errors) == 0

	return result, nil
}

// executeAPICall executes the appropriate API call for a parsed item
func (s *IntelligentAPIService) executeAPICall(item ParsedItem, documentID uint, userID uint) APIResult {
	switch item.Type {
	case "task":
		return s.createTask(item, &documentID, nil, userID)
	case "agency":
		return s.createAgency(item, userID)
	case "category":
		return s.createCategory(item, userID)
	default:
		return APIResult{
			Type:       item.Type,
			Action:     "failed",
			Error:      fmt.Sprintf("Unknown item type: %s", item.Type),
			SourceText: item.SourceText,
			Confidence: item.Confidence,
		}
	}
}

// executeAPICallForRegulation executes API calls for regulation-related items
func (s *IntelligentAPIService) executeAPICallForRegulation(item ParsedItem, regulationID uint, userID uint) APIResult {
	switch item.Type {
	case "task":
		return s.createTask(item, nil, &regulationID, userID)
	case "agency":
		return s.createAgency(item, userID)
	case "category":
		return s.createCategory(item, userID)
	default:
		return APIResult{
			Type:       item.Type,
			Action:     "failed",
			Error:      fmt.Sprintf("Unknown item type: %s", item.Type),
			SourceText: item.SourceText,
			Confidence: item.Confidence,
		}
	}
}

// createTask creates a new task from parsed item
func (s *IntelligentAPIService) createTask(item ParsedItem, documentID *uint, regulationID *uint, userID uint) APIResult {
	task := &models.Task{
		Title:          item.Title,
		Description:    item.Description,
		Type:           item.TaskType,
		Priority:       item.Priority,
		Status:         models.TaskStatusPending,
		DueDate:        item.DueDate,
		StartDate:      item.StartDate,
		EndDate:        item.EndDate,
		Duration:       item.Duration,
		SourceType:     "parsed_text",
		SourceText:     item.SourceText,
		ParsedFromText: true,
		CreatedByID:    userID,
		DocumentID:     documentID,
		RegulationID:   regulationID,
		IsPublic:       false,
	}

	if err := s.taskService.CreateTask(task); err != nil {
		return APIResult{
			Type:       "task",
			Action:     "failed",
			Error:      fmt.Sprintf("Failed to create task: %v", err),
			SourceText: item.SourceText,
			Confidence: item.Confidence,
		}
	}

	return APIResult{
		Type:       "task",
		Action:     "created",
		EntityID:   &task.ID,
		EntityName: task.Title,
		SourceText: item.SourceText,
		Confidence: item.Confidence,
		Metadata:   task,
	}
}

// createAgency creates a new agency from parsed item
func (s *IntelligentAPIService) createAgency(item ParsedItem, userID uint) APIResult {
	// Check if agency already exists
	var existingAgency models.Agency
	if err := s.db.Where("name ILIKE ?", item.Title).First(&existingAgency).Error; err == nil {
		return APIResult{
			Type:       "agency",
			Action:     "exists",
			EntityID:   &existingAgency.ID,
			EntityName: existingAgency.Name,
			SourceText: item.SourceText,
			Confidence: item.Confidence,
			Metadata:   existingAgency,
		}
	}

	// Create new agency
	agency := &models.Agency{
		Name:        item.Title,
		Slug:        s.generateSlug(item.Title),
		Description: fmt.Sprintf("Agency created from text: %s", item.SourceText),
		IsActive:    true,
		Country:     "US",
	}

	if err := s.db.Create(agency).Error; err != nil {
		return APIResult{
			Type:       "agency",
			Action:     "failed",
			Error:      fmt.Sprintf("Failed to create agency: %v", err),
			SourceText: item.SourceText,
			Confidence: item.Confidence,
		}
	}

	return APIResult{
		Type:       "agency",
		Action:     "created",
		EntityID:   &agency.ID,
		EntityName: agency.Name,
		SourceText: item.SourceText,
		Confidence: item.Confidence,
		Metadata:   agency,
	}
}

// createCategory creates a new category from parsed item
func (s *IntelligentAPIService) createCategory(item ParsedItem, userID uint) APIResult {
	// Check if category already exists
	var existingCategory models.Category
	if err := s.db.Where("name ILIKE ?", item.Title).First(&existingCategory).Error; err == nil {
		return APIResult{
			Type:       "category",
			Action:     "exists",
			EntityID:   &existingCategory.ID,
			EntityName: existingCategory.Name,
			SourceText: item.SourceText,
			Confidence: item.Confidence,
			Metadata:   existingCategory,
		}
	}

	// Create new category
	category := &models.Category{
		Name:        item.Title,
		Slug:        s.generateSlug(item.Title),
		Description: fmt.Sprintf("Category created from text: %s", item.SourceText),
		IsActive:    true,
		Color:       "#007bff", // Default blue color
		SortOrder:   0,
	}

	if err := s.db.Create(category).Error; err != nil {
		return APIResult{
			Type:       "category",
			Action:     "failed",
			Error:      fmt.Sprintf("Failed to create category: %v", err),
			SourceText: item.SourceText,
			Confidence: item.Confidence,
		}
	}

	return APIResult{
		Type:       "category",
		Action:     "created",
		EntityID:   &category.ID,
		EntityName: category.Name,
		SourceText: item.SourceText,
		Confidence: item.Confidence,
		Metadata:   category,
	}
}

// generateProcessingSummary creates a summary of processing results
func (s *IntelligentAPIService) generateProcessingSummary(result *ProcessingResult) string {
	if len(result.ParsedItems) == 0 {
		return "No actionable items found in text"
	}

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("Processed %d items: ", len(result.ParsedItems)))

	taskCount := 0
	agencyCount := 0
	categoryCount := 0
	errorCount := len(result.Errors)

	for _, apiResult := range result.APIResults {
		switch apiResult.Type {
		case "task":
			if apiResult.Action == "created" {
				taskCount++
			}
		case "agency":
			if apiResult.Action == "created" {
				agencyCount++
			}
		case "category":
			if apiResult.Action == "created" {
				categoryCount++
			}
		}
	}

	parts := []string{}
	if taskCount > 0 {
		parts = append(parts, fmt.Sprintf("%d tasks created", taskCount))
	}
	if agencyCount > 0 {
		parts = append(parts, fmt.Sprintf("%d agencies created", agencyCount))
	}
	if categoryCount > 0 {
		parts = append(parts, fmt.Sprintf("%d categories created", categoryCount))
	}
	if errorCount > 0 {
		parts = append(parts, fmt.Sprintf("%d errors", errorCount))
	}

	if len(parts) > 0 {
		summary.WriteString(strings.Join(parts, ", "))
	} else {
		summary.WriteString("no new items created")
	}

	return summary.String()
}

// generateSlug creates a URL-friendly slug from a name
func (s *IntelligentAPIService) generateSlug(name string) string {
	slug := strings.ToLower(name)
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")

	// Remove special characters
	var result strings.Builder
	for _, r := range slug {
		if (r >= 'a' && r <= 'z') || (r >= '0' && r <= '9') || r == '-' {
			result.WriteRune(r)
		}
	}

	slug = result.String()

	// Remove multiple consecutive hyphens
	for strings.Contains(slug, "--") {
		slug = strings.ReplaceAll(slug, "--", "-")
	}

	// Trim hyphens from start and end
	slug = strings.Trim(slug, "-")

	// Limit length
	if len(slug) > 100 {
		slug = slug[:100]
		slug = strings.Trim(slug, "-")
	}

	return slug
}
