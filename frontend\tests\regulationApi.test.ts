import regulationApi from '../app/services/regulationApi';
import apiService from '../app/services/api';
import { LawsAndRules, RegulationFilters, CreateRegulationRequest } from '../app/types';

// Mock the API service
jest.mock('../app/services/api');
const mockedApiService = apiService as jest.Mocked<typeof apiService>;

describe('RegulationApiService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getPublicRegulations', () => {
    it('should fetch public regulations with default filters', async () => {
      const mockResponse = {
        data: [
          {
            id: 1,
            title: 'Test Regulation',
            type: 'rule',
            status: 'effective',
            agency: { id: 1, name: 'Test Agency' }
          }
        ],
        total: 1,
        page: 1,
        per_page: 25,
        total_pages: 1,
        has_next: false,
        has_prev: false
      };

      mockedApiService.get.mockResolvedValue(mockResponse);

      const result = await regulationApi.getPublicRegulations();

      expect(mockedApiService.get).toHaveBeenCalledWith('/public/regulations');
      expect(result).toEqual(mockResponse);
    });

    it('should fetch public regulations with filters', async () => {
      const filters: RegulationFilters = {
        type: 'rule',
        agency_id: 1,
        search: 'environmental',
        page: 2,
        per_page: 10,
        sort_by: 'title',
        sort_order: 'asc'
      };

      const mockResponse = {
        data: [],
        total: 0,
        page: 2,
        per_page: 10,
        total_pages: 0,
        has_next: false,
        has_prev: true
      };

      mockedApiService.get.mockResolvedValue(mockResponse);

      const result = await regulationApi.getPublicRegulations(filters);

      expect(mockedApiService.get).toHaveBeenCalledWith(
        '/public/regulations?type=rule&agency_id=1&search=environmental&page=2&per_page=10&sort_by=title&sort_order=asc'
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getPublicRegulation', () => {
    it('should fetch a specific regulation by ID', async () => {
      const mockResponse = {
        regulation: {
          id: 1,
          title: 'Test Regulation',
          type: 'rule',
          status: 'effective'
        },
        chunks: []
      };

      mockedApiService.get.mockResolvedValue(mockResponse);

      const result = await regulationApi.getPublicRegulation(1);

      expect(mockedApiService.get).toHaveBeenCalledWith('/public/regulations/1');
      expect(result).toEqual(mockResponse);
    });

    it('should fetch a specific regulation version', async () => {
      const mockResponse = {
        regulation: {
          id: 1,
          title: 'Test Regulation',
          type: 'rule',
          status: 'effective'
        },
        chunks: []
      };

      mockedApiService.get.mockResolvedValue(mockResponse);

      const result = await regulationApi.getPublicRegulation(1, 2);

      expect(mockedApiService.get).toHaveBeenCalledWith('/public/regulations/1?version=2');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('createRegulation', () => {
    it('should create a new regulation', async () => {
      const regulationData: CreateRegulationRequest = {
        title: 'New Test Regulation',
        short_title: 'NTR',
        type: 'rule',
        cfr_title: '40',
        agency_id: 1,
        description: 'A new test regulation',
        effective_date: '2024-01-01',
        is_significant: true,
        initial_chunks: []
      };

      const mockResponse = {
        data: { id: 123 }
      };

      mockedApiService.post.mockResolvedValue(mockResponse);

      const result = await regulationApi.createRegulation(regulationData);

      expect(mockedApiService.post).toHaveBeenCalledWith('/regulations', regulationData);
      expect(result).toEqual({ id: 123 });
    });
  });

  describe('updateRegulation', () => {
    it('should update an existing regulation', async () => {
      const regulationData: CreateRegulationRequest = {
        title: 'Updated Test Regulation',
        short_title: 'UTR',
        type: 'regulation',
        cfr_title: '47',
        agency_id: 1,
        description: 'An updated test regulation',
        effective_date: '2024-02-01',
        is_significant: false,
        initial_chunks: []
      };

      mockedApiService.put.mockResolvedValue(undefined);

      await regulationApi.updateRegulation(1, regulationData);

      expect(mockedApiService.put).toHaveBeenCalledWith('/regulations/1', regulationData);
    });
  });

  describe('deleteRegulation', () => {
    it('should delete a regulation', async () => {
      mockedApiService.delete.mockResolvedValue(undefined);

      await regulationApi.deleteRegulation(1);

      expect(mockedApiService.delete).toHaveBeenCalledWith('/regulations/1');
    });
  });

  describe('utility methods', () => {
    describe('getRegulationTypes', () => {
      it('should return available regulation types', () => {
        const types = regulationApi.getRegulationTypes();
        
        expect(types).toEqual([
          { value: 'law', label: 'Law' },
          { value: 'rule', label: 'Rule' },
          { value: 'regulation', label: 'Regulation' },
          { value: 'code', label: 'Code' }
        ]);
      });
    });

    describe('getEffectivenessStatus', () => {
      it('should return "not_yet_effective" for future effective date', () => {
        const futureDate = new Date();
        futureDate.setMonth(futureDate.getMonth() + 1);
        
        const regulation = {
          effective_date: futureDate.toISOString()
        } as LawsAndRules;

        const status = regulationApi.getEffectivenessStatus(regulation);
        expect(status).toBe('not_yet_effective');
      });

      it('should return "effective" for past effective date without termination', () => {
        const pastDate = new Date();
        pastDate.setMonth(pastDate.getMonth() - 1);
        
        const regulation = {
          effective_date: pastDate.toISOString(),
          termination_date: null
        } as LawsAndRules;

        const status = regulationApi.getEffectivenessStatus(regulation);
        expect(status).toBe('effective');
      });

      it('should return "terminated" for past termination date', () => {
        const pastEffectiveDate = new Date();
        pastEffectiveDate.setFullYear(pastEffectiveDate.getFullYear() - 1);
        
        const pastTerminationDate = new Date();
        pastTerminationDate.setMonth(pastTerminationDate.getMonth() - 1);
        
        const regulation = {
          effective_date: pastEffectiveDate.toISOString(),
          termination_date: pastTerminationDate.toISOString()
        } as LawsAndRules;

        const status = regulationApi.getEffectivenessStatus(regulation);
        expect(status).toBe('terminated');
      });

      it('should return "not_yet_effective" for missing effective date', () => {
        const regulation = {
          effective_date: null
        } as LawsAndRules;

        const status = regulationApi.getEffectivenessStatus(regulation);
        expect(status).toBe('not_yet_effective');
      });
    });

    describe('formatRegulationTitle', () => {
      it('should format regulation title with short title', () => {
        const regulation = {
          title: 'Environmental Protection Standards',
          short_title: 'EPA Standards'
        } as LawsAndRules;

        const formatted = regulationApi.formatRegulationTitle(regulation);
        expect(formatted).toBe('EPA Standards (Environmental Protection Standards)');
      });

      it('should format regulation title without short title', () => {
        const regulation = {
          title: 'Environmental Protection Standards',
          short_title: ''
        } as LawsAndRules;

        const formatted = regulationApi.formatRegulationTitle(regulation);
        expect(formatted).toBe('Environmental Protection Standards');
      });
    });

    describe('formatDate', () => {
      it('should format date string', () => {
        const dateString = '2024-01-15';
        const formatted = regulationApi.formatDate(dateString);
        expect(formatted).toBe('January 15, 2024');
      });

      it('should handle null date', () => {
        const formatted = regulationApi.formatDate(null);
        expect(formatted).toBe('Not set');
      });

      it('should handle undefined date', () => {
        const formatted = regulationApi.formatDate(undefined);
        expect(formatted).toBe('Not set');
      });
    });
  });
});
