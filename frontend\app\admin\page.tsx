'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ChartBarIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  UserGroupIcon,
  TagIcon,
  CalendarIcon,
  ClockIcon,
  EyeIcon,
  PlusIcon,
  CogIcon,
  ShieldCheckIcon,
  BookOpenIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';

interface DashboardStats {
  // Backend returns flat structure
  total_documents: number;
  published_documents: number;
  draft_documents: number;
  under_review: number;
  total_agencies: number;
  active_users: number;
  total_users: number;
  total_categories: number;
  my_documents: number;
  documents_this_month: number;
  recent_views: number;
  recent_activity: any[];
}

const AdminDashboard: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    if (!isAuthenticated || user?.role !== 'admin') {
      router.push('/dashboard');
      return;
    }

    const fetchDashboardStats = async () => {
      try {
        setLoading(true);
        const response = await apiService.getDashboardStats();
        setStats(response.data);
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardStats();
  }, [isAuthenticated, user, router]);

  if (loading) {
    return (
      <Layout
        title="Admin Dashboard - Federal Register Clone"
        requireAuth={true}
        allowedRoles={['admin']}
      >
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout
        title="Admin Dashboard - Federal Register Clone"
        requireAuth={true}
        allowedRoles={['admin']}
      >
        <div className="container-custom py-8">
          <div className="text-center">
            <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Dashboard Error</h3>
            <p className="text-gray-600 mb-4">{error}</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout
      title="Admin Dashboard - Federal Register Clone"
      requireAuth={true}
      allowedRoles={['admin']}
    >
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600 mt-1">System overview and management</p>
            </div>
            <div className="flex items-center space-x-3">
              <Link
                href="/admin/settings"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <CogIcon className="h-4 w-4 mr-2" />
                Settings
              </Link>
              <Link
                href="/admin/users"
                className="inline-flex items-center px-4 py-2 bg-primary-600 text-white rounded-md text-sm font-medium hover:bg-primary-700"
              >
                <UserGroupIcon className="h-4 w-4 mr-2" />
                Manage Users
              </Link>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Documents Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <DocumentTextIcon className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Documents</p>
                <p className="text-2xl font-semibold text-gray-900">{stats?.total_documents || 0}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Published: {stats?.published_documents || 0}</span>
                <span>Draft: {stats?.draft_documents || 0}</span>
              </div>
            </div>
          </div>

          {/* Users Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <UserGroupIcon className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Users</p>
                <p className="text-2xl font-semibold text-gray-900">{stats?.total_users || 0}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Active: {stats?.active_users || 0}</span>
                <span>Total: {stats?.total_users || 0}</span>
              </div>
            </div>
          </div>

          {/* Agencies Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BuildingOfficeIcon className="h-8 w-8 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Agencies</p>
                <p className="text-2xl font-semibold text-gray-900">{stats?.total_agencies || 0}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Active: {stats?.total_agencies || 0}</span>
              </div>
            </div>
          </div>

          {/* My Documents Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BookOpenIcon className="h-8 w-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">My Documents</p>
                <p className="text-2xl font-semibold text-gray-900">{stats?.my_documents || 0}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>This Month: {stats?.documents_this_month || 0}</span>
              </div>
            </div>
          </div>

          {/* Categories Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TagIcon className="h-8 w-8 text-indigo-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Categories</p>
                <p className="text-2xl font-semibold text-gray-900">{stats?.total_categories || 0}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Total: {stats?.total_categories || 0}</span>
              </div>
            </div>
          </div>

          {/* Document Views Stats */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Document Views</p>
                <p className="text-2xl font-semibold text-gray-900">{stats?.recent_views || 0}</p>
              </div>
            </div>
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Total Views</span>
              </div>
            </div>
          </div>


        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div className="grid grid-cols-2 gap-4">
              <Link
                href="/documents/new"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <PlusIcon className="h-6 w-6 text-blue-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">New Document</span>
              </Link>
              <Link
                href="/agencies/new"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <PlusIcon className="h-6 w-6 text-purple-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">New Agency</span>
              </Link>
              <Link
                href="/categories/new"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <PlusIcon className="h-6 w-6 text-indigo-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">New Category</span>
              </Link>
              <Link
                href="/regulations/new"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <PlusIcon className="h-6 w-6 text-orange-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">New Regulation</span>
              </Link>
              <Link
                href="/proceedings/new"
                className="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <PlusIcon className="h-6 w-6 text-teal-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">New Proceeding</span>
              </Link>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Management</h2>
            <div className="space-y-3">
              <Link
                href="/admin/users"
                className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <UserGroupIcon className="h-5 w-5 text-gray-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">User Management</span>
              </Link>
              <Link
                href="/admin/documents"
                className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <DocumentTextIcon className="h-5 w-5 text-gray-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">Document Management</span>
              </Link>
              <Link
                href="/admin/regulations"
                className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <BookOpenIcon className="h-5 w-5 text-gray-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">Regulation Management</span>
              </Link>
              <Link
                href="/admin/proceedings"
                className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ClipboardDocumentListIcon className="h-5 w-5 text-gray-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">Proceeding Management</span>
              </Link>
              <Link
                href="/admin/analytics"
                className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <ChartBarIcon className="h-5 w-5 text-gray-600 mr-3" />
                <span className="text-sm font-medium text-gray-900">Analytics Dashboard</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        {stats?.recent_activity && stats.recent_activity.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Activity</h2>
            <div className="space-y-4">
              {stats.recent_activity.slice(0, 10).map((activity, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg">
                  <div className="flex-shrink-0">
                    <ClockIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{activity.description}</p>
                    <p className="text-xs text-gray-500">{new Date(activity.created_at).toLocaleString()}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default AdminDashboard;
