-- Migration 021: Fix database schema issues causing 500 errors
-- This migration fixes missing columns and tables that are causing runtime errors

-- 1. Add regulation_id column to documents table
ALTER TABLE documents 
ADD COLUMN IF NOT EXISTS regulation_id BIGINT REFERENCES laws_and_rules(id);

-- 2. Create regulation_agencies table (alias for regulation_agency_relationships)
CREATE OR REPLACE VIEW regulation_agencies AS 
SELECT 
    id,
    created_at,
    updated_at,
    deleted_at,
    regulation_id,
    agency_id,
    relationship_type,
    effective_date,
    termination_date,
    description,
    authority_scope,
    created_by_id,
    is_active
FROM regulation_agency_relationships
WHERE deleted_at IS NULL;

-- 3. Create regulation_categories table (alias for regulation_category_relationships)
CREATE OR REPLACE VIEW regulation_categories AS 
SELECT 
    id,
    created_at,
    updated_at,
    deleted_at,
    regulation_id,
    category_id,
    relationship_type,
    effective_date,
    termination_date,
    description,
    scope_definition,
    created_by_id,
    is_active
FROM regulation_category_relationships
WHERE deleted_at IS NULL;

-- 4. Fix interconnects table column names to match the model
-- First, check if the table has the wrong column names and fix them
DO $$
BEGIN
    -- Check if old column names exist and rename them
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'interconnects' AND column_name = 'source_entity_type') THEN
        ALTER TABLE interconnects RENAME COLUMN source_entity_type TO source_type;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'interconnects' AND column_name = 'source_entity_id') THEN
        ALTER TABLE interconnects RENAME COLUMN source_entity_id TO source_id;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'interconnects' AND column_name = 'target_entity_type') THEN
        ALTER TABLE interconnects RENAME COLUMN target_entity_type TO target_type;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'interconnects' AND column_name = 'target_entity_id') THEN
        ALTER TABLE interconnects RENAME COLUMN target_entity_id TO target_id;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'interconnects' AND column_name = 'connection_type') THEN
        ALTER TABLE interconnects RENAME COLUMN connection_type TO relationship;
    END IF;
END $$;

-- 5. Add missing columns to interconnects table if they don't exist
ALTER TABLE interconnects 
ADD COLUMN IF NOT EXISTS source_type TEXT,
ADD COLUMN IF NOT EXISTS source_id BIGINT,
ADD COLUMN IF NOT EXISTS target_type TEXT,
ADD COLUMN IF NOT EXISTS target_id BIGINT,
ADD COLUMN IF NOT EXISTS relationship TEXT,
ADD COLUMN IF NOT EXISTS metadata TEXT;

-- 6. Update interconnects table to have proper constraints
ALTER TABLE interconnects 
ALTER COLUMN source_type SET NOT NULL,
ALTER COLUMN source_id SET NOT NULL,
ALTER COLUMN target_type SET NOT NULL,
ALTER COLUMN target_id SET NOT NULL,
ALTER COLUMN relationship SET NOT NULL;

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_regulation_id ON documents(regulation_id);
CREATE INDEX IF NOT EXISTS idx_interconnects_source ON interconnects(source_type, source_id);
CREATE INDEX IF NOT EXISTS idx_interconnects_target ON interconnects(target_type, target_id);
CREATE INDEX IF NOT EXISTS idx_interconnects_relationship ON interconnects(relationship);

-- 8. Add comments for documentation
COMMENT ON COLUMN documents.regulation_id IS 'Reference to the regulation this document implements or relates to';
COMMENT ON VIEW regulation_agencies IS 'View providing backward compatibility for regulation-agency relationships';
COMMENT ON VIEW regulation_categories IS 'View providing backward compatibility for regulation-category relationships';
