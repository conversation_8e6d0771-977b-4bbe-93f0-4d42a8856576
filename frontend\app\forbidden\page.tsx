'use client';

import React from 'react';
import Link from 'next/link';
import { ShieldExclamationIcon, HomeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { useAuthStore } from '../stores/authStore';

export default function ForbiddenPage() {
  const { user, isAuthenticated } = useAuthStore();

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <ShieldExclamationIcon className="h-16 w-16 text-orange-500" />
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          403 - Forbidden
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          You don't have the required permissions
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Insufficient Role Permissions
              </h3>
              <p className="text-sm text-gray-600">
                Your current role doesn't have the necessary permissions to perform this action.
              </p>
            </div>

            {isAuthenticated && user && (
              <div className="mb-6 p-4 bg-gray-50 rounded-md">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Current Permissions</h4>
                <div className="text-sm text-gray-600">
                  <p><strong>Role:</strong> {user.role}</p>
                  <div className="mt-2">
                    <p className="font-medium">Available Actions:</p>
                    <ul className="mt-1 text-xs space-y-1">
                      {user.role === 'admin' && (
                        <>
                          <li>• Full system access</li>
                          <li>• User management</li>
                          <li>• System configuration</li>
                        </>
                      )}
                      {user.role === 'editor' && (
                        <>
                          <li>• Create and edit documents</li>
                          <li>• Manage categories</li>
                          <li>• Review content</li>
                        </>
                      )}
                      {user.role === 'viewer' && (
                        <>
                          <li>• View public documents</li>
                          <li>• Search content</li>
                          <li>• View profile</li>
                        </>
                      )}
                    </ul>
                  </div>
                </div>
              </div>
            )}

            <div className="space-y-4">
              <div className="space-y-3">
                <button
                  onClick={() => window.history.back()}
                  className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Go Back
                </button>
                
                <Link
                  href="/dashboard"
                  className="w-full flex justify-center items-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <HomeIcon className="h-4 w-4 mr-2" />
                  Go to Dashboard
                </Link>
              </div>

              <Link
                href="/"
                className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <HomeIcon className="h-4 w-4 mr-2" />
                Go to Home
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <div className="text-center">
            <p className="text-xs text-gray-500">
              Need additional permissions?{' '}
              <Link href="/contact" className="text-primary-600 hover:text-primary-500">
                Contact your administrator
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
