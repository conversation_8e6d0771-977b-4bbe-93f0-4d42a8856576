package middleware

import (
	"federal-register-clone/internal/config"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// CORS middleware handles Cross-Origin Resource Sharing
func CORS(corsConfig config.CORSConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// Check if origin is allowed
		if isOriginAllowed(origin, corsConfig.AllowedOrigins) {
			c.<PERSON>("Access-Control-Allow-Origin", origin)
		}

		// Set other CORS headers
		c.<PERSON><PERSON>("Access-Control-Allow-Methods", strings.Join(corsConfig.AllowedMethods, ", "))
		c.<PERSON>("Access-Control-Allow-Headers", strings.Join(corsConfig.AllowedHeaders, ", "))
		c.<PERSON><PERSON>("Access-Control-Allow-Credentials", "true")
		c.<PERSON>("Access-Control-Max-Age", "86400") // 24 hours

		// Handle preflight requests
		if c.Request.Method == "OPTIONS" {
			c.<PERSON>bort<PERSON>(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// isOriginAllowed checks if the origin is in the allowed origins list
func isOriginAllowed(origin string, allowedOrigins []string) bool {
	if origin == "" {
		return false
	}

	for _, allowed := range allowedOrigins {
		if allowed == "*" || allowed == origin {
			return true
		}
		
		// Support wildcard subdomains (e.g., *.example.com)
		if strings.HasPrefix(allowed, "*.") {
			domain := strings.TrimPrefix(allowed, "*.")
			if strings.HasSuffix(origin, domain) {
				return true
			}
		}
	}

	return false
}
