'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlayIcon,
  StopIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  CogIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface ProcessingWorkflow {
  id: number;
  name: string;
  description: string;
  workflow_type: 'document' | 'regulation' | 'finance' | 'user' | 'system' | 'custom';
  status: 'active' | 'inactive' | 'running' | 'paused' | 'error';
  trigger_type: 'manual' | 'scheduled' | 'event' | 'api';
  trigger_config: any;
  steps_count: number;
  last_run: string;
  next_run: string;
  success_rate: number;
  created_by: number;
  created_at: string;
  updated_at: string;
}

const ProcessingWorkflowsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [workflows, setWorkflows] = useState<ProcessingWorkflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchWorkflows();
  }, []);

  const fetchWorkflows = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch processing workflows from content workflows API
      const response = await apiService.get<{
        success: boolean;
        message: string;
        data: ProcessingWorkflow[];
      }>('/enterprise/content/workflows');

      if (response.success && response.data) {
        // Transform content workflows to processing workflows format
        const transformedWorkflows: ProcessingWorkflow[] = response.data.map((workflow: any) => ({
          id: workflow.id,
          name: workflow.name,
          description: workflow.description || '',
          workflow_type: workflow.type || 'document',
          status: workflow.is_active ? 'active' : 'inactive',
          trigger_type: workflow.auto_start ? 'event' : 'manual',
          trigger_config: {
            event: workflow.trigger_events || 'manual_trigger',
            conditions: []
          },
          steps_count: workflow.steps ? JSON.parse(workflow.steps || '[]').length : 0,
          last_run: workflow.last_executed || '',
          next_run: '',
          success_rate: workflow.success_rate || 0,
          created_by: workflow.created_by_id || 1,
          created_at: workflow.created_at,
          updated_at: workflow.updated_at
        }));

        setWorkflows(transformedWorkflows);
      } else {
        throw new Error(response.message || 'Failed to fetch workflows');
      }
    } catch (err: any) {
      console.error('Error fetching processing workflows:', err);
      setError(err.response?.data?.message || err.message || 'Failed to fetch processing workflows');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this workflow?')) return;

    try {
      await apiService.delete(`/enterprise/content/workflows/${id}`);
      setWorkflows(workflows.filter(workflow => workflow.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete workflow');
    }
  };

  const handleToggleStatus = async (id: number, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      const isActive = newStatus === 'active';

      await apiService.put(`/enterprise/content/workflows/${id}`, {
        is_active: isActive
      });

      setWorkflows(workflows.map(workflow =>
        workflow.id === id ? { ...workflow, status: newStatus as any } : workflow
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to update workflow status');
    }
  };

  const handleRunWorkflow = async (id: number) => {
    try {
      await apiService.post(`/enterprise/content/workflows/${id}/start`, {
        content_type: 'document',
        content_id: 1 // Default content ID for manual trigger
      });

      setWorkflows(workflows.map(workflow =>
        workflow.id === id ? { ...workflow, status: 'running' as any, last_run: new Date().toISOString() } : workflow
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to run workflow');
    }
  };

  const filteredWorkflows = workflows.filter(workflow =>
    workflow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.workflow_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'document':
        return 'bg-blue-100 text-blue-800';
      case 'regulation':
        return 'bg-purple-100 text-purple-800';
      case 'finance':
        return 'bg-green-100 text-green-800';
      case 'user':
        return 'bg-orange-100 text-orange-800';
      case 'system':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'running':
        return <CogIcon className="h-4 w-4 animate-spin" />;
      case 'paused':
        return <ClockIcon className="h-4 w-4" />;
      case 'error':
        return <XCircleIcon className="h-4 w-4" />;
      case 'inactive':
        return <StopIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view processing workflows.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Processing Workflows</h1>
              <p className="text-gray-600 mt-1">Manage automated processing workflows and business logic</p>
            </div>
            <Link
              href="/admin/processing/workflows/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Workflow
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search workflows..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading processing workflows...</p>
          </div>
        ) : (
          /* Workflows Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Workflow Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trigger
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Steps
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Success Rate
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Run
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredWorkflows.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No processing workflows found.
                      <Link
                        href="/admin/processing/workflows/new"
                        className="block mt-2 text-primary-600 hover:text-primary-500"
                      >
                        Create your first workflow
                      </Link>
                    </td>
                  </tr>
                ) : (
                  filteredWorkflows.map((workflow) => (
                    <tr key={workflow.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{workflow.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{workflow.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(workflow.workflow_type)}`}>
                          {workflow.workflow_type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(workflow.status)}`}>
                          {getStatusIcon(workflow.status)}
                          <span className="ml-1">{workflow.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {workflow.trigger_type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {workflow.steps_count}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ width: `${workflow.success_rate}%` }}
                            ></div>
                          </div>
                          <span>{workflow.success_rate.toFixed(1)}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(workflow.last_run)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/processing/workflows/${workflow.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                            title="View Workflow"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {workflow.trigger_type === 'manual' && workflow.status !== 'running' && (
                            <button
                              onClick={() => handleRunWorkflow(workflow.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Run Workflow"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleToggleStatus(workflow.id, workflow.status)}
                            className={`${
                              workflow.status === 'active' ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'
                            }`}
                            title={workflow.status === 'active' ? 'Deactivate' : 'Activate'}
                          >
                            {workflow.status === 'active' ? <StopIcon className="h-4 w-4" /> : <PlayIcon className="h-4 w-4" />}
                          </button>
                          <button
                            onClick={() => router.push(`/admin/processing/workflows/${workflow.id}/edit`)}
                            className="text-purple-600 hover:text-purple-900"
                            title="Edit Workflow"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(workflow.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Workflow"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ProcessingWorkflowsPage;
