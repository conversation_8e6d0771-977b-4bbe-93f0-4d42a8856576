import React from 'react';
import Link from 'next/link';
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  ChatBubbleLeftRightIcon,
  // BookmarkIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import Tooltip from './Tooltip';

type ActionButton = {
  key: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  className: string;
  tooltip?: string;
  requiresConfirm?: boolean;
} & (
  | { href: string; onClick?: undefined }
  | { onClick: () => void; href?: undefined }
);

interface ActionButtonsProps {
  documentId: number;
  canEdit?: boolean;
  canDelete?: boolean;
  canView?: boolean;
  canComment?: boolean;
  canDownload?: boolean;
  canShare?: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
  onStatusChange?: (status: string) => void;
  availableStatusChanges?: string[];
  layout?: 'horizontal' | 'vertical' | 'dropdown';
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
  className?: string;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
  documentId,
  canEdit = false,
  canDelete = false,
  canView = true,
  canComment = false,
  canDownload = true,
  canShare = true,
  onEdit,
  onDelete,
  onStatusChange,
  availableStatusChanges = [],
  layout = 'horizontal',
  size = 'md',
  showLabels = true,
  className = ''
}) => {
  const getSizeClasses = () => {
    const sizes = {
      sm: showLabels ? 'px-2 py-1 text-xs' : 'p-1',
      md: showLabels ? 'px-3 py-1 text-xs' : 'p-2',
      lg: showLabels ? 'px-4 py-2 text-sm' : 'p-2'
    };
    return sizes[size];
  };

  const getIconSize = () => {
    const sizes = {
      sm: 'h-3 w-3',
      md: 'h-4 w-4',
      lg: 'h-5 w-5'
    };
    return sizes[size];
  };

  const getLayoutClasses = () => {
    const layouts = {
      horizontal: 'flex items-center space-x-2',
      vertical: 'flex flex-col space-y-2',
      dropdown: 'space-y-1'
    };
    return layouts[layout];
  };

  const baseButtonClass = `inline-flex items-center border rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${getSizeClasses()}`;

  const buttons: ActionButton[] = [
    // View Button
    canView && documentId && {
      key: 'view',
      href: `/documents/${documentId}`,
      icon: EyeIcon,
      label: 'View',
      className: 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:ring-gray-500',
      tooltip: 'View document details'
    },

    // Edit Button
    canEdit && documentId && {
      key: 'edit',
      onClick: onEdit || (() => window.location.href = `/documents/${documentId}/edit`),
      icon: PencilIcon,
      label: 'Edit',
      className: 'border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 focus:ring-blue-500',
      tooltip: 'Edit this document'
    },

    // Delete Button
    canDelete && {
      key: 'delete',
      onClick: onDelete,
      icon: TrashIcon,
      label: 'Delete',
      className: 'border-red-300 dark:border-red-600 text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/40 focus:ring-red-500',
      tooltip: 'Delete this document',
      requiresConfirm: true
    },

    // Comment Button
    canComment && documentId && {
      key: 'comment',
      href: `/documents/${documentId}/comment`,
      icon: ChatBubbleLeftRightIcon,
      label: 'Comment',
      className: 'border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 focus:ring-green-500',
      tooltip: 'Submit a public comment'
    },

    // Download Button
    canDownload && documentId && {
      key: 'download',
      onClick: () => window.open(`/api/documents/${documentId}/download`, '_blank'),
      icon: ArrowDownTrayIcon,
      label: 'Download',
      className: 'border-purple-300 dark:border-purple-600 text-purple-700 dark:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/40 focus:ring-purple-500',
      tooltip: 'Download document as PDF'
    },

    // Share Button
    canShare && documentId && {
      key: 'share',
      onClick: () => {
        if (navigator.share) {
          navigator.share({
            title: 'Federal Register Document',
            url: window.location.origin + `/documents/${documentId}`
          });
        } else {
          navigator.clipboard.writeText(window.location.origin + `/documents/${documentId}`);
          // You could show a toast notification here
        }
      },
      icon: ShareIcon,
      label: 'Share',
      className: 'border-indigo-300 dark:border-indigo-600 text-indigo-700 dark:text-indigo-300 bg-indigo-50 dark:bg-indigo-900/20 hover:bg-indigo-100 dark:hover:bg-indigo-900/40 focus:ring-indigo-500',
      tooltip: 'Share this document'
    }
  ].filter(Boolean) as ActionButton[];

  // Status change buttons
  const statusButtons: ActionButton[] = availableStatusChanges.map(status => {
    const statusConfig: { [key: string]: { icon: React.ComponentType<{ className?: string }>, label: string, color: string } } = {
      'under_review': { icon: ClockIcon, label: 'Submit for Review', color: 'yellow' },
      'approved': { icon: CheckCircleIcon, label: 'Approve', color: 'green' },
      'rejected': { icon: XCircleIcon, label: 'Reject', color: 'red' },
      'published': { icon: CheckCircleIcon, label: 'Publish', color: 'blue' },
      'withdrawn': { icon: XCircleIcon, label: 'Withdraw', color: 'red' },
      'draft': { icon: PencilIcon, label: 'Return to Draft', color: 'gray' }
    };

    const config = statusConfig[status];
    if (!config) return null;

    const Icon = config.icon;
    const colorClasses = {
      yellow: 'border-yellow-300 dark:border-yellow-600 text-yellow-700 dark:text-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 hover:bg-yellow-100 dark:hover:bg-yellow-900/40 focus:ring-yellow-500',
      green: 'border-green-300 dark:border-green-600 text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/40 focus:ring-green-500',
      red: 'border-red-300 dark:border-red-600 text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/40 focus:ring-red-500',
      blue: 'border-blue-300 dark:border-blue-600 text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/40 focus:ring-blue-500',
      gray: 'border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-900/20 hover:bg-gray-100 dark:hover:bg-gray-900/40 focus:ring-gray-500'
    };

    return {
      key: `status-${status}`,
      onClick: () => onStatusChange?.(status),
      icon: Icon,
      label: config.label,
      className: colorClasses[config.color as keyof typeof colorClasses],
      tooltip: `Change status to ${config.label.toLowerCase()}`
    };
  }).filter(Boolean) as ActionButton[];

  const allButtons = [...buttons, ...statusButtons];

  const renderButton = (button: ActionButton) => {
    const Icon = button.icon;
    const content = (
      <>
        <Icon className={`${getIconSize()} ${showLabels ? 'mr-1' : ''}`} />
        {showLabels && button.label}
      </>
    );

    const buttonElement = button.href ? (
      <Link
        key={button.key}
        href={button.href}
        className={`${baseButtonClass} ${button.className}`}
      >
        {content}
      </Link>
    ) : (
      <button
        key={button.key}
        onClick={button.onClick}
        className={`${baseButtonClass} ${button.className}`}
      >
        {content}
      </button>
    );

    return button.tooltip ? (
      <Tooltip key={button.key} content={button.tooltip}>
        {buttonElement}
      </Tooltip>
    ) : buttonElement;
  };

  if (allButtons.length === 0) {
    return null;
  }

  return (
    <div className={`${getLayoutClasses()} ${className}`}>
      {allButtons.map(renderButton)}
    </div>
  );
};

export default ActionButtons;
