'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { complianceApi } from '../../../services/enterpriseApi';
import { ComplianceAssessment } from '../../../types/enterprise';

const ComplianceAssessmentsListPage: React.FC = () => {
  const router = useRouter();
  const [assessments, setAssessments] = useState<ComplianceAssessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  useEffect(() => {
    fetchAssessments();
  }, []);

  const fetchAssessments = async () => {
    try {
      setLoading(true);
      const response = await complianceApi.getAssessments({
        search: searchTerm,
        status: filterStatus || undefined,
      });
      setAssessments(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch compliance assessments');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this compliance assessment?')) return;
    
    try {
      await complianceApi.deleteAssessment(id);
      await fetchAssessments(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to delete compliance assessment');
    }
  };

  const handleStartAssessment = async (id: number) => {
    try {
      await complianceApi.startAssessment(id);
      await fetchAssessments(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to start assessment');
    }
  };

  const filteredAssessments = assessments.filter(assessment =>
    assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assessment.assessment_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) return <div className="p-6">Loading compliance assessments...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Compliance Assessments</h1>
        <button
          onClick={() => router.push('/enterprise/compliance/assessments/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Create New Assessment
        </button>
      </div>

      {/* Search and Filter */}
      <div className="mb-6 flex gap-4">
        <input
          type="text"
          placeholder="Search assessments..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border rounded px-3 py-2 flex-1"
        />
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="border rounded px-3 py-2"
        >
          <option value="">All Status</option>
          <option value="planning">Planning</option>
          <option value="in_progress">In Progress</option>
          <option value="completed">Completed</option>
          <option value="cancelled">Cancelled</option>
        </select>
        <button
          onClick={fetchAssessments}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Search
        </button>
      </div>

      {/* Assessments Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Code
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Framework
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Score
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredAssessments.map((assessment) => (
              <tr key={assessment.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {assessment.assessment_code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {assessment.title}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {assessment.framework}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className="capitalize">{assessment.assessment_type}</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {assessment.overall_score !== undefined ? (
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      assessment.overall_score >= 90 
                        ? 'bg-green-100 text-green-800'
                        : assessment.overall_score >= 70
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {assessment.overall_score}%
                    </span>
                  ) : (
                    <span className="text-gray-400">N/A</span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    assessment.status === 'completed' 
                      ? 'bg-green-100 text-green-800'
                      : assessment.status === 'in_progress'
                      ? 'bg-blue-100 text-blue-800'
                      : assessment.status === 'planning'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {assessment.status.replace('_', ' ')}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => router.push(`/enterprise/compliance/assessments/${assessment.id}`)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </button>
                    <button
                      onClick={() => router.push(`/enterprise/compliance/assessments/${assessment.id}/edit`)}
                      className="text-green-600 hover:text-green-900"
                    >
                      Edit
                    </button>
                    {assessment.status === 'planning' && (
                      <button
                        onClick={() => handleStartAssessment(assessment.id)}
                        className="text-purple-600 hover:text-purple-900"
                      >
                        Start
                      </button>
                    )}
                    <button
                      onClick={() => handleDelete(assessment.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {filteredAssessments.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No compliance assessments found
          </div>
        )}
      </div>
    </div>
  );
};

export default ComplianceAssessmentsListPage;
