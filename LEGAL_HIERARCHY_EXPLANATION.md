# Legal Document Hierarchy Structure

## Research Summary

Based on research of federal legal document structure, the current regulation hierarchy has been corrected to follow proper legal document organization.

## Previous Problematic Structure

The old structure incorrectly treated different classification systems as nested hierarchical levels:
```
USC Title → CFR Title → Public Law → RIN → Docket
```

**Problems:**
1. USC and CFR are parallel classification systems, not hierarchical
2. Public Law, RIN, and Docket are administrative identifiers, not hierarchical levels
3. This created confusing groupings like "USC Title Unspecified" with nested CFR titles

## Correct Legal Document Hierarchy

### Primary Hierarchy: Code of Federal Regulations (CFR)
The CFR follows a proper hierarchical structure:

```
CFR Title → Chapter → Part → Section
```

**Definitions:**
- **CFR Title**: Broad subject areas (e.g., Title 40 = Environmental Protection)
- **Chapter**: Agency subdivisions within a title (e.g., Chapter I = EPA)
- **Part**: Specific regulatory topics (e.g., Part 50 = National Primary and Secondary Ambient Air Quality Standards)
- **Section**: Individual regulations (e.g., Section 50.4 = National primary ambient air quality standards for lead)

### Administrative Metadata (Not Hierarchical)
These are separate classification/tracking systems:

- **USC Title**: United States Code title reference (e.g., Title 26 = Internal Revenue Code)
- **Public Law Number**: Specific legislation (e.g., Public Law 117-58)
- **Regulatory Identifier Number (RIN)**: Unique regulatory action ID (e.g., 2060-AU14)
- **Docket Number**: Administrative record ID (e.g., EPA-HQ-OAR-2021-0208)

## Implementation Changes

### Frontend Changes
1. **Tree Structure**: Now uses CFR Title → Chapter → Part → Section hierarchy
2. **Metadata Display**: USC Title, Public Law, RIN, and Docket shown as metadata, not hierarchy levels
3. **Icons Updated**: 
   - CFR Title: BookOpenIcon
   - Chapter: FolderIcon
   - Part: DocumentDuplicateIcon
   - Section: ArchiveBoxIcon

### Backend Support
The backend already supports this structure with:
- `cfr_title`: CFR Title number
- `chapter_number`: Chapter identifier
- `part_number`: Part number
- `section_number`: Section number
- `usc_title`, `public_law_number`, `regulatory_identifier`, `docket_number`: Metadata fields

## Benefits of Correct Structure

1. **Legal Accuracy**: Follows actual federal regulation organization
2. **User Understanding**: Clear hierarchy that matches legal professionals' expectations
3. **Better Navigation**: Logical drill-down from broad topics to specific regulations
4. **Metadata Clarity**: Administrative identifiers shown as reference information, not navigation structure

## Example Structure

```
CFR Title 40 (Environmental Protection)
├── Chapter I (Environmental Protection Agency)
│   ├── Part 50 (National Primary and Secondary Ambient Air Quality Standards)
│   │   ├── Section 50.1 (Definitions)
│   │   ├── Section 50.4 (National primary ambient air quality standards for lead)
│   │   └── Section 50.6 (National primary and secondary ambient air quality standards for PM2.5)
│   └── Part 51 (Requirements for Preparation, Adoption, and Submittal of Implementation Plans)
└── Chapter II (Other Agency)
```

Each regulation also displays metadata:
- USC Title: 42 (The Public Health and Welfare)
- Public Law: 91-604 (Clean Air Act)
- RIN: 2060-AU14
- Docket: EPA-HQ-OAR-2021-0208
