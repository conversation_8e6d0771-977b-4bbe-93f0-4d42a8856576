'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { contentApi } from '../../../../services/enterpriseApi';
import { ContentRepository } from '../../../../types/enterprise';

const NewContentRepositoryPage: React.FC = () => {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<ContentRepository>>({
    repository_code: '',
    repository_name: '',
    name: '',
    description: '',
    type: 'document',
    repository_type: 'document',
    storage_type: 'local',
    storage_path: '',
    storage_config: '',
    max_size: 1073741824, // 1GB
    current_size: 0,
    total_size: 0,
    max_files: 10000,
    current_files: 0,
    file_count: 0,
    classification: 'internal',
    encryption_key: '',
    access_policy: '',
    is_public: false,
    versioning_enabled: true,
    max_versions: 10,
    backup_enabled: true,
    backup_schedule: 'daily',
    retention_policy_id: undefined,
    compliance_level: 'standard',
    is_active: true,
    last_accessed: '',
    access_count: 0,
    metadata: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Ensure aliases are set
      const submitData = {
        ...formData,
        name: formData.repository_name,
        repository_type: formData.type,
        total_size: formData.current_size,
        file_count: formData.current_files
      };
      await contentApi.createRepository(submitData);
      router.push('/enterprise/content/repositories');
    } catch (err: any) {
      setError(err.message || 'Failed to create content repository');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Create New Content Repository</h1>
        <button
          onClick={() => router.push('/enterprise/content/repositories')}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Back to Repositories
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Repository Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Repository Code *
            </label>
            <input
              type="text"
              name="repository_code"
              value={formData.repository_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., REPO-2025-001"
            />
          </div>

          {/* Repository Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Repository Name *
            </label>
            <input
              type="text"
              name="repository_name"
              value={formData.repository_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., Document Repository"
            />
          </div>

          {/* Repository Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Repository Type *
            </label>
            <select
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="document">Document</option>
              <option value="media">Media</option>
              <option value="template">Template</option>
              <option value="archive">Archive</option>
              <option value="backup">Backup</option>
            </select>
          </div>

          {/* Storage Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Storage Type *
            </label>
            <select
              name="storage_type"
              value={formData.storage_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="local">Local Storage</option>
              <option value="s3">Amazon S3</option>
              <option value="azure">Azure Blob</option>
              <option value="gcp">Google Cloud Storage</option>
              <option value="ftp">FTP Server</option>
              <option value="network">Network Drive</option>
            </select>
          </div>

          {/* Storage Path */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Storage Path
            </label>
            <input
              type="text"
              name="storage_path"
              value={formData.storage_path}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., /var/content/documents"
            />
          </div>

          {/* Classification */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Classification *
            </label>
            <select
              name="classification"
              value={formData.classification}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="public">Public</option>
              <option value="internal">Internal</option>
              <option value="confidential">Confidential</option>
              <option value="restricted">Restricted</option>
              <option value="top_secret">Top Secret</option>
            </select>
          </div>

          {/* Max Size (MB) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Size (MB)
            </label>
            <input
              type="number"
              name="max_size"
              value={Math.round(formData.max_size / 1024 / 1024)}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                max_size: parseFloat(e.target.value) * 1024 * 1024 || 0
              }))}
              min="1"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Max Files */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Files
            </label>
            <input
              type="number"
              name="max_files"
              value={formData.max_files}
              onChange={handleChange}
              min="1"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Max Versions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Versions
            </label>
            <input
              type="number"
              name="max_versions"
              value={formData.max_versions}
              onChange={handleChange}
              min="1"
              max="100"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Compliance Level */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Compliance Level
            </label>
            <select
              name="compliance_level"
              value={formData.compliance_level}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="basic">Basic</option>
              <option value="standard">Standard</option>
              <option value="enhanced">Enhanced</option>
              <option value="strict">Strict</option>
            </select>
          </div>

          {/* Backup Schedule */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Backup Schedule
            </label>
            <select
              name="backup_schedule"
              value={formData.backup_schedule}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="hourly">Hourly</option>
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="never">Never</option>
            </select>
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Repository description and purpose..."
          />
        </div>

        {/* Storage Configuration */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Storage Configuration (JSON)
          </label>
          <textarea
            name="storage_config"
            value={formData.storage_config}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder='{"bucket": "my-bucket", "region": "us-east-1"}'
          />
        </div>

        {/* Access Policy */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Access Policy (JSON)
          </label>
          <textarea
            name="access_policy"
            value={formData.access_policy}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder='{"read": ["user", "admin"], "write": ["admin"]}'
          />
        </div>

        {/* Checkboxes */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_public"
              checked={formData.is_public}
              onChange={handleChange}
              className="mr-2"
            />
            Public Repository
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="versioning_enabled"
              checked={formData.versioning_enabled}
              onChange={handleChange}
              className="mr-2"
            />
            Enable Versioning
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="backup_enabled"
              checked={formData.backup_enabled}
              onChange={handleChange}
              className="mr-2"
            />
            Enable Backup
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleChange}
              className="mr-2"
            />
            Active Repository
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push('/enterprise/content/repositories')}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Repository'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default NewContentRepositoryPage;
