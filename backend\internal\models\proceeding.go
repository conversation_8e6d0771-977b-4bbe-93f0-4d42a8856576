package models

import (
	"time"

	"gorm.io/gorm"
)

// ProceedingStatus represents the status of a proceeding
type ProceedingStatus string

const (
	ProceedingStatusPlanning    ProceedingStatus = "planning"
	ProceedingStatusActive      ProceedingStatus = "active"
	ProceedingStatusSuspended   ProceedingStatus = "suspended"
	ProceedingStatusCompleted   ProceedingStatus = "completed"
	ProceedingStatusCancelled   ProceedingStatus = "cancelled"
	ProceedingStatusUnderReview ProceedingStatus = "under_review"
)

// ProceedingPriority represents the priority level of a proceeding
type ProceedingPriority string

const (
	ProceedingPriorityLow      ProceedingPriority = "low"
	ProceedingPriorityMedium   ProceedingPriority = "medium"
	ProceedingPriorityHigh     ProceedingPriority = "high"
	ProceedingPriorityUrgent   ProceedingPriority = "urgent"
	ProceedingPriorityCritical ProceedingPriority = "critical"
)

// ProceedingStepType represents the type of administrative proceeding step
type ProceedingStepType string

const (
	StepTypeNoticeOfIntent        ProceedingStepType = "notice_of_intent"
	StepTypeAdvanceNotice         ProceedingStepType = "advance_notice"
	StepTypeNoticeProposedRule    ProceedingStepType = "notice_proposed_rule"
	StepTypePublicComment         ProceedingStepType = "public_comment"
	StepTypeSupplementalNotice    ProceedingStepType = "supplemental_notice"
	StepTypeFinalRule             ProceedingStepType = "final_rule"
	StepTypeInterimFinalRule      ProceedingStepType = "interim_final_rule"
	StepTypeRegulationPublication ProceedingStepType = "regulation_publication"
	StepTypeCorrectingAmendment   ProceedingStepType = "correcting_amendment"
	StepTypeCustom                ProceedingStepType = "custom"
)

// ProceedingStepStatus represents the status of a proceeding step
type ProceedingStepStatus string

const (
	ProceedingStepNotStarted  ProceedingStepStatus = "not_started"
	ProceedingStepInProgress  ProceedingStepStatus = "in_progress"
	ProceedingStepUnderReview ProceedingStepStatus = "under_review"
	ProceedingStepCompleted   ProceedingStepStatus = "completed"
	ProceedingStepFailed      ProceedingStepStatus = "failed"
	ProceedingStepSkipped     ProceedingStepStatus = "skipped"
	ProceedingStepOnHold      ProceedingStepStatus = "on_hold"
)

// ContentType represents the type of content in a proceeding step
type ContentType string

const (
	ContentTypeText     ContentType = "text"
	ContentTypeVoice    ContentType = "voice"
	ContentTypeDocument ContentType = "document"
	ContentTypeVideo    ContentType = "video"
	ContentTypeMixed    ContentType = "mixed"
)

// ProceedingLogType represents the type of log entry
type ProceedingLogType string

const (
	LogTypeGeneral      ProceedingLogType = "general"
	LogTypeStepUpdate   ProceedingLogType = "step_update"
	LogTypeStatusChange ProceedingLogType = "status_change"
	LogTypeReview       ProceedingLogType = "review"
	LogTypeDecision     ProceedingLogType = "decision"
	LogTypeIssue        ProceedingLogType = "issue"
	LogTypeResolution   ProceedingLogType = "resolution"
	LogTypeMilestone    ProceedingLogType = "milestone"
)

// Proceeding represents a formal, overarching process undertaken by the SGU
type Proceeding struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Unique identification (requirement 4)
	Name           string    `json:"name" gorm:"not null"`                  // Descriptive name
	InitiationDate time.Time `json:"initiation_date" gorm:"not null"`       // Date component of unique ID
	UniqueID       *string   `json:"unique_id" gorm:"uniqueIndex;not null"` // Generated: "Name YYYY-MM-DD"

	// Basic proceeding information
	Description string             `json:"description" gorm:"type:text"`
	Objective   string             `json:"objective" gorm:"type:text;not null"`
	Status      ProceedingStatus   `json:"status" gorm:"default:'planning'"`
	Priority    ProceedingPriority `json:"priority" gorm:"default:'medium'"`

	// Administrative proceeding requirements
	RequiresMandatoryReview bool `json:"requires_mandatory_review" gorm:"default:true"`
	MinimumStepsRequired    int  `json:"minimum_steps_required" gorm:"default:5"`
	SequentialExecution     bool `json:"sequential_execution" gorm:"default:true"`

	// Legal framework integration
	LegalAuthority      string `json:"legal_authority" gorm:"type:text"`      // Legal basis for proceeding
	RegulatoryImpact    string `json:"regulatory_impact" gorm:"type:text"`    // Expected regulatory impact
	PublicInterest      string `json:"public_interest" gorm:"type:text"`      // Public interest justification
	StakeholderAnalysis string `json:"stakeholder_analysis" gorm:"type:text"` // Analysis of affected parties

	// Timing information
	PlannedStartDate  *time.Time `json:"planned_start_date"`
	ActualStartDate   *time.Time `json:"actual_start_date"`
	PlannedEndDate    *time.Time `json:"planned_end_date"`
	ActualEndDate     *time.Time `json:"actual_end_date"`
	EstimatedDuration *int       `json:"estimated_duration"` // Duration in days
	ActualDuration    *int       `json:"actual_duration"`    // Duration in days

	// Review requirements (requirement 5)
	ReviewRequired     bool       `json:"review_required" gorm:"default:true"`
	ReviewScheduled    bool       `json:"review_scheduled" gorm:"default:false"`
	ReviewDate         *time.Time `json:"review_date"`
	ReviewCompleted    bool       `json:"review_completed" gorm:"default:false"`
	ReviewCompletedAt  *time.Time `json:"review_completed_at"`
	ReviewReportID     *uint      `json:"review_report_id"`                     // Link to review report
	CriticalMilestones string     `json:"critical_milestones" gorm:"type:text"` // JSON array of milestone dates

	// Existing directives consideration (requirement 7)
	ExistingDirectivesReviewed bool   `json:"existing_directives_reviewed" gorm:"default:false"`
	ReferencedRules            string `json:"referenced_rules" gorm:"type:text"`      // JSON array of rule IDs
	ReferencedOrders           string `json:"referenced_orders" gorm:"type:text"`     // JSON array of order IDs
	ReferencedDirectives       string `json:"referenced_directives" gorm:"type:text"` // JSON array of directive IDs
	ConflictAnalysis           string `json:"conflict_analysis" gorm:"type:text"`     // Analysis of potential conflicts
	IntegrationPlan            string `json:"integration_plan" gorm:"type:text"`      // Plan for integrating existing frameworks

	// IFR Integration (requirement 6)
	RequiresIFR    bool   `json:"requires_ifr" gorm:"default:false"`
	IFRTriggered   bool   `json:"ifr_triggered" gorm:"default:false"`
	IFRDescription string `json:"ifr_description" gorm:"type:text"`
	IFRDocumentID  *uint  `json:"ifr_document_id"` // Link to IFR document if created

	// Relationships
	InitiatedByID uint `json:"initiated_by_id" gorm:"not null"`
	InitiatedBy   User `json:"initiated_by" gorm:"foreignKey:InitiatedByID"`
	OwnerID       uint `json:"owner_id" gorm:"not null"`
	Owner         User `json:"owner" gorm:"foreignKey:OwnerID"`

	// Related entities
	AgencyID   *uint     `json:"agency_id"`
	Agency     *Agency   `json:"agency,omitempty" gorm:"foreignKey:AgencyID"`
	CategoryID *uint     `json:"category_id"`
	Category   *Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`

	// Progress tracking
	TotalSteps       int     `json:"total_steps" gorm:"default:0"`
	CompletedSteps   int     `json:"completed_steps" gorm:"default:0"`
	FailedSteps      int     `json:"failed_steps" gorm:"default:0"`
	ProgressPercent  float64 `json:"progress_percent" gorm:"default:0"`
	CurrentStepOrder *int    `json:"current_step_order"`

	// Additional metadata
	Tags         string `json:"tags"` // Comma-separated tags
	IsPublic     bool   `json:"is_public" gorm:"default:false"`
	Notes        string `json:"notes" gorm:"type:text"`
	Attachments  string `json:"attachments" gorm:"type:text"`   // JSON array of file paths
	ExternalRefs string `json:"external_refs" gorm:"type:text"` // JSON array of external references

	// Relationships to other entities
	ProceedingSteps []ProceedingStep `json:"proceeding_steps,omitempty" gorm:"foreignKey:ProceedingID;constraint:OnDelete:CASCADE"`
	ProceedingLogs  []ProceedingLog  `json:"proceeding_logs,omitempty" gorm:"foreignKey:ProceedingID;constraint:OnDelete:CASCADE"`
	RelatedTasks    []Task           `json:"related_tasks,omitempty" gorm:"many2many:proceeding_tasks"`
	RelatedDocs     []Document       `json:"related_documents,omitempty" gorm:"many2many:proceeding_documents"`
	RelatedRegs     []LawsAndRules   `json:"related_regulations,omitempty" gorm:"many2many:proceeding_regulations"`
}

// ProceedingStep represents a sequential step in an administrative proceeding
type ProceedingStep struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Step identification and ordering
	ProceedingID uint                 `json:"proceeding_id" gorm:"not null"`
	Proceeding   Proceeding           `json:"proceeding" gorm:"foreignKey:ProceedingID"`
	StepOrder    int                  `json:"step_order" gorm:"not null"` // Sequential order (1, 2, 3, etc.)
	StepType     ProceedingStepType   `json:"step_type" gorm:"not null"`  // Type of administrative step
	Name         string               `json:"name" gorm:"not null"`
	Description  string               `json:"description" gorm:"type:text;not null"`
	Status       ProceedingStepStatus `json:"status" gorm:"default:'not_started'"`

	// Sequential execution requirements
	RequiresPreviousCompletion bool `json:"requires_previous_completion" gorm:"default:true"` // Must wait for previous step
	IsMandatory                bool `json:"is_mandatory" gorm:"default:true"`                 // Required for proceeding completion
	AllowsParallelExecution    bool `json:"allows_parallel_execution" gorm:"default:false"`   // Can run parallel with other steps

	// Content management (voice/text/document support)
	ContentType     ContentType `json:"content_type" gorm:"default:'text'"`
	TextContent     string      `json:"text_content" gorm:"type:text"`     // Text-based content
	VoiceRecording  string      `json:"voice_recording"`                   // Path to voice recording file
	DocumentContent string      `json:"document_content" gorm:"type:text"` // Document-based content
	VideoContent    string      `json:"video_content"`                     // Path to video file
	Attachments     string      `json:"attachments" gorm:"type:text"`      // JSON array of file paths

	// Document and regulation references
	ReferencedDocumentIDs   string `json:"referenced_document_ids" gorm:"type:text"`   // JSON array of document IDs
	ReferencedRegulationIDs string `json:"referenced_regulation_ids" gorm:"type:text"` // JSON array of regulation IDs
	GeneratedDocumentID     *uint  `json:"generated_document_id"`                      // Document created by this step
	GeneratedRegulationID   *uint  `json:"generated_regulation_id"`                    // Regulation created by this step

	// Step timing
	PlannedStartDate  *time.Time `json:"planned_start_date"`
	ActualStartDate   *time.Time `json:"actual_start_date"`
	PlannedEndDate    *time.Time `json:"planned_end_date"`
	ActualEndDate     *time.Time `json:"actual_end_date"`
	EstimatedDuration *int       `json:"estimated_duration"` // Duration in days
	ActualDuration    *int       `json:"actual_duration"`    // Duration in days

	// Step completion requirements (Complete/Fail marking)
	CompletionCriteria string     `json:"completion_criteria" gorm:"type:text"` // What defines completion
	CompletionEvidence string     `json:"completion_evidence" gorm:"type:text"` // Evidence of completion
	CompletedAt        *time.Time `json:"completed_at"`
	CompletedByID      *uint      `json:"completed_by_id"`
	CompletedBy        *User      `json:"completed_by,omitempty" gorm:"foreignKey:CompletedByID"`
	FailureReason      string     `json:"failure_reason" gorm:"type:text"` // Reason for failure if status is failed

	// Mandatory review requirements
	RequiresReview  bool       `json:"requires_review" gorm:"default:true"`
	ReviewCompleted bool       `json:"review_completed" gorm:"default:false"`
	ReviewedAt      *time.Time `json:"reviewed_at"`
	ReviewedByID    *uint      `json:"reviewed_by_id"`
	ReviewedBy      *User      `json:"reviewed_by,omitempty" gorm:"foreignKey:ReviewedByID"`
	ReviewComments  string     `json:"review_comments" gorm:"type:text"`

	// Step assignment and responsibility
	AssignedToID *uint `json:"assigned_to_id"`
	AssignedTo   *User `json:"assigned_to,omitempty" gorm:"foreignKey:AssignedToID"`
	OwnerID      uint  `json:"owner_id" gorm:"not null"`
	Owner        User  `json:"owner" gorm:"foreignKey:OwnerID"`

	// Additional metadata
	Priority     ProceedingPriority `json:"priority" gorm:"default:'medium'"`
	IsOptional   bool               `json:"is_optional" gorm:"default:false"`
	IsCritical   bool               `json:"is_critical" gorm:"default:false"`
	Notes        string             `json:"notes" gorm:"type:text"`
	ExternalRefs string             `json:"external_refs" gorm:"type:text"` // JSON array of external references

	// Progress tracking
	ProgressPercent float64 `json:"progress_percent" gorm:"default:0"`
	LastUpdateBy    *uint   `json:"last_update_by"`
	LastUpdatedBy   *User   `json:"last_updated_by,omitempty" gorm:"foreignKey:LastUpdateBy"`
}

// ProceedingLog represents a log entry for tracking proceeding activities and decisions
type ProceedingLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Log identification
	ProceedingID uint              `json:"proceeding_id" gorm:"not null"`
	Proceeding   Proceeding        `json:"proceeding" gorm:"foreignKey:ProceedingID"`
	LogType      ProceedingLogType `json:"log_type" gorm:"not null"`
	Title        string            `json:"title" gorm:"not null"`
	Content      string            `json:"content" gorm:"type:text;not null"`

	// Step-specific logging
	ProceedingStepID *uint           `json:"proceeding_step_id"`
	ProceedingStep   *ProceedingStep `json:"proceeding_step,omitempty" gorm:"foreignKey:ProceedingStepID"`
	StepOrder        *int            `json:"step_order"` // For quick reference without join

	// Status and decision tracking
	PreviousStatus string `json:"previous_status"`
	NewStatus      string `json:"new_status"`
	Decision       string `json:"decision" gorm:"type:text"`
	Rationale      string `json:"rationale" gorm:"type:text"`
	Impact         string `json:"impact" gorm:"type:text"`

	// User tracking
	AuthorID uint `json:"author_id" gorm:"not null"`
	Author   User `json:"author" gorm:"foreignKey:AuthorID"`

	// Related entities
	RelatedTaskID       *uint `json:"related_task_id"`
	RelatedDocumentID   *uint `json:"related_document_id"`
	RelatedRegulationID *uint `json:"related_regulation_id"`
	RelatedAgencyID     *uint `json:"related_agency_id"`

	// Timing and scheduling
	EventDate     *time.Time `json:"event_date"`     // When the logged event occurred
	ScheduledDate *time.Time `json:"scheduled_date"` // When a future event is scheduled
	DueDate       *time.Time `json:"due_date"`       // When something is due

	// Review and approval tracking
	RequiresReview   bool       `json:"requires_review" gorm:"default:false"`
	ReviewedAt       *time.Time `json:"reviewed_at"`
	ReviewedByID     *uint      `json:"reviewed_by_id"`
	ReviewedBy       *User      `json:"reviewed_by,omitempty" gorm:"foreignKey:ReviewedByID"`
	ApprovalRequired bool       `json:"approval_required" gorm:"default:false"`
	ApprovedAt       *time.Time `json:"approved_at"`
	ApprovedByID     *uint      `json:"approved_by_id"`
	ApprovedBy       *User      `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`

	// Additional metadata
	Priority     ProceedingPriority `json:"priority" gorm:"default:'medium'"`
	IsPublic     bool               `json:"is_public" gorm:"default:false"`
	Tags         string             `json:"tags"`                           // Comma-separated tags
	Attachments  string             `json:"attachments" gorm:"type:text"`   // JSON array of file paths
	ExternalRefs string             `json:"external_refs" gorm:"type:text"` // JSON array of external references
	Metadata     string             `json:"metadata" gorm:"type:text"`      // JSON object for additional data
}

// ProceedingAttachment represents file attachments for proceedings
type ProceedingAttachment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	ProceedingID uint       `json:"proceeding_id" gorm:"not null"`
	Proceeding   Proceeding `json:"proceeding" gorm:"foreignKey:ProceedingID"`
	FileName     string     `json:"file_name" gorm:"not null"`
	FilePath     string     `json:"file_path" gorm:"not null"`
	FileSize     int64      `json:"file_size"`
	MimeType     string     `json:"mime_type"`
	Description  string     `json:"description" gorm:"type:text"`
	UploadedBy   uint       `json:"uploaded_by" gorm:"not null"`
	Uploader     User       `json:"uploader" gorm:"foreignKey:UploadedBy"`

	// Attachment categorization
	AttachmentType string `json:"attachment_type"` // "evidence", "reference", "template", "report", etc.
	IsPublic       bool   `json:"is_public" gorm:"default:false"`
	StepID         *uint  `json:"step_id"` // Associated with specific step
}

// ProceedingParticipant represents users involved in a proceeding with specific roles
type ProceedingParticipant struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	ProceedingID uint       `json:"proceeding_id" gorm:"not null"`
	Proceeding   Proceeding `json:"proceeding" gorm:"foreignKey:ProceedingID"`
	UserID       uint       `json:"user_id" gorm:"not null"`
	User         User       `json:"user" gorm:"foreignKey:UserID"`

	// Participant role and permissions
	Role       string `json:"role" gorm:"not null"` // "owner", "participant", "observer", "reviewer", "approver"
	CanEdit    bool   `json:"can_edit" gorm:"default:false"`
	CanApprove bool   `json:"can_approve" gorm:"default:false"`
	CanView    bool   `json:"can_view" gorm:"default:true"`
	CanComment bool   `json:"can_comment" gorm:"default:true"`

	// Participation tracking
	JoinedAt  time.Time  `json:"joined_at"`
	LeftAt    *time.Time `json:"left_at"`
	IsActive  bool       `json:"is_active" gorm:"default:true"`
	AddedByID uint       `json:"added_by_id" gorm:"not null"`
	AddedBy   User       `json:"added_by" gorm:"foreignKey:AddedByID"`

	// Notification preferences
	NotifyOnUpdates bool `json:"notify_on_updates" gorm:"default:true"`
	NotifyOnSteps   bool `json:"notify_on_steps" gorm:"default:true"`
	NotifyOnReview  bool `json:"notify_on_review" gorm:"default:true"`
}
