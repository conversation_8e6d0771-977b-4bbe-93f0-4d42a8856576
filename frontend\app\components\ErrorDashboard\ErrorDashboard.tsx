import React, { useState, useEffect } from 'react';
import { errorLogger, ErrorLog } from '../../utils/errorLogger';

const ErrorDashboard: React.FC = () => {
  const [errors, setErrors] = useState<ErrorLog[]>([]);
  const [filter, setFilter] = useState<{
    type?: string;
    severity?: string;
    search?: string;
  }>({});
  const [isVisible, setIsVisible] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    const updateErrors = () => {
      setErrors(errorLogger.getErrors());
    };

    updateErrors();
    const interval = setInterval(updateErrors, 1000);
    return () => clearInterval(interval);
  }, []);

  // Don't render on server side
  if (!isMounted) {
    return null;
  }

  const filteredErrors = errors.filter(error => {
    if (filter.type && error.type !== filter.type) return false;
    if (filter.severity && error.severity !== filter.severity) return false;
    if (filter.search && !error.message.toLowerCase().includes(filter.search.toLowerCase())) return false;
    return true;
  });

  const summary = isMounted ? errorLogger.getErrorSummary() : {
    total: 0,
    byType: {},
    bySeverity: {},
    recent: [],
  };

  const getSeverityColor = (severity: string) => {
    const colors = {
      low: 'text-blue-600 bg-blue-100',
      medium: 'text-yellow-600 bg-yellow-100',
      high: 'text-red-600 bg-red-100',
      critical: 'text-red-800 bg-red-200',
    };
    return colors[severity as keyof typeof colors] || colors.medium;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      react: 'text-purple-600 bg-purple-100',
      axios: 'text-green-600 bg-green-100',
      runtime: 'text-orange-600 bg-orange-100',
      unhandled: 'text-red-600 bg-red-100',
      promise: 'text-pink-600 bg-pink-100',
    };
    return colors[type as keyof typeof colors] || colors.runtime;
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsVisible(true)}
          className="bg-red-600 text-white px-4 py-2 rounded-full shadow-lg hover:bg-red-700 flex items-center space-x-2"
        >
          <span>🚨</span>
          <span>Errors ({summary.total})</span>
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white">
            Error Dashboard
          </h2>
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        {/* Summary */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="bg-gray-100 dark:bg-gray-700 p-3 rounded">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{summary.total}</div>
              <div className="text-sm text-gray-600 dark:text-gray-300">Total Errors</div>
            </div>
            <div className="bg-red-100 dark:bg-red-900 p-3 rounded">
              <div className="text-2xl font-bold text-red-800 dark:text-red-200">
                {summary.bySeverity.critical || 0}
              </div>
              <div className="text-sm text-red-600 dark:text-red-300">Critical</div>
            </div>
            <div className="bg-yellow-100 dark:bg-yellow-900 p-3 rounded">
              <div className="text-2xl font-bold text-yellow-800 dark:text-yellow-200">
                {summary.bySeverity.high || 0}
              </div>
              <div className="text-sm text-yellow-600 dark:text-yellow-300">High</div>
            </div>
            <div className="bg-green-100 dark:bg-green-900 p-3 rounded">
              <div className="text-2xl font-bold text-green-800 dark:text-green-200">
                {summary.byType.axios || 0}
              </div>
              <div className="text-sm text-green-600 dark:text-green-300">API Errors</div>
            </div>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-2">
            <select
              value={filter.type || ''}
              onChange={(e) => setFilter({ ...filter, type: e.target.value || undefined })}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm"
            >
              <option value="">All Types</option>
              <option value="react">React</option>
              <option value="axios">Axios</option>
              <option value="runtime">Runtime</option>
              <option value="promise">Promise</option>
            </select>
            <select
              value={filter.severity || ''}
              onChange={(e) => setFilter({ ...filter, severity: e.target.value || undefined })}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm"
            >
              <option value="">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <input
              type="text"
              placeholder="Search errors..."
              value={filter.search || ''}
              onChange={(e) => setFilter({ ...filter, search: e.target.value || undefined })}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm flex-1 min-w-0"
            />
            <button
              onClick={() => errorLogger.clearErrors()}
              className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
            >
              Clear All
            </button>
            <button
              onClick={() => {
                const data = errorLogger.exportErrors();
                const blob = new Blob([data], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `errors_${new Date().toISOString()}.json`;
                a.click();
              }}
              className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
            >
              Export
            </button>
          </div>
        </div>

        {/* Error List */}
        <div className="flex-1 overflow-auto p-4">
          {filteredErrors.length === 0 ? (
            <div className="text-center text-gray-500 dark:text-gray-400 py-8">
              No errors found
            </div>
          ) : (
            <div className="space-y-3">
              {filteredErrors.map((error) => (
                <div
                  key={error.id}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-900"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getTypeColor(error.type)}`}>
                        {error.type}
                      </span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getSeverityColor(error.severity)}`}>
                        {error.severity}
                      </span>
                      {error.component && (
                        <span className="px-2 py-1 rounded text-xs font-medium text-gray-600 bg-gray-200">
                          {error.component}
                        </span>
                      )}
                    </div>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {new Date(error.timestamp).toLocaleString()}
                    </span>
                  </div>
                  
                  <div className="mb-2">
                    <div className="font-medium text-gray-900 dark:text-white mb-1">
                      {error.message}
                    </div>
                    {error.url && (
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        URL: {error.url}
                      </div>
                    )}
                  </div>

                  {(error.request || error.response) && (
                    <div className="mb-2 text-sm">
                      {error.request && (
                        <div className="mb-1">
                          <strong>Request:</strong> {error.request.method} {error.request.url}
                        </div>
                      )}
                      {error.response && (
                        <div>
                          <strong>Response:</strong> {error.response.status} {error.response.statusText}
                        </div>
                      )}
                    </div>
                  )}

                  {error.stack && (
                    <details className="mt-2">
                      <summary className="cursor-pointer text-sm text-gray-600 dark:text-gray-400">
                        Stack Trace
                      </summary>
                      <pre className="mt-1 text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
                        {error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ErrorDashboard;
