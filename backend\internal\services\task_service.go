package services

import (
	"fmt"
	"log"
	"time"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// TaskService handles task-related operations
type TaskService struct {
	db *gorm.DB
}

// NewTaskService creates a new task service
func NewTaskService() *TaskService {
	return &TaskService{
		db: database.GetDB(),
	}
}

// CreateTask creates a new task
func (s *TaskService) CreateTask(task *models.Task) error {
	// Set default values
	if task.Status == "" {
		task.Status = models.TaskStatusPending
	}
	if task.Priority == "" {
		task.Priority = models.TaskPriorityMedium
	}
	if task.Type == "" {
		task.Type = models.TaskTypeGeneral
	}
	if task.TimeZone == "" {
		task.TimeZone = "UTC"
	}

	// Validate required fields
	if task.Title == "" {
		return fmt.Errorf("task title is required")
	}
	if task.CreatedByID == 0 {
		return fmt.Errorf("created_by_id is required")
	}

	// Set completion status if due date has passed
	if task.DueDate != nil && task.DueDate.Before(time.Now()) && task.Status == models.TaskStatusPending {
		task.Status = models.TaskStatusOnHold
	}

	return s.db.Create(task).Error
}

// GetTask retrieves a task by ID with ALL related data
func (s *TaskService) GetTask(id uint) (*models.Task, error) {
	var task models.Task
	err := s.db.Preload("AssignedTo").
		Preload("CreatedBy").
		Preload("Document", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Agency").Preload("CreatedBy")
		}).
		Preload("Regulation", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Agency").Preload("CreatedBy")
		}).
		Preload("Agency").
		Preload("Category").
		Preload("ParentTask").
		First(&task, id).Error

	if err != nil {
		return nil, err
	}

	return &task, nil
}

// UpdateTask updates an existing task
func (s *TaskService) UpdateTask(id uint, updates *models.Task) error {
	// Don't allow updating certain fields
	updates.ID = 0
	updates.CreatedAt = time.Time{}
	updates.CreatedByID = 0

	return s.db.Model(&models.Task{}).Where("id = ?", id).Updates(updates).Error
}

// DeleteTask soft deletes a task
func (s *TaskService) DeleteTask(id uint) error {
	return s.db.Delete(&models.Task{}, id).Error
}

// ListTasks retrieves tasks with filtering and pagination
func (s *TaskService) ListTasks(filters TaskFilters) ([]models.Task, int64, error) {
	var tasks []models.Task
	var total int64

	query := s.db.Model(&models.Task{})

	// Apply filters
	if filters.AssignedToID != nil {
		query = query.Where("assigned_to_id = ?", *filters.AssignedToID)
	}
	if filters.CreatedByID != nil {
		query = query.Where("created_by_id = ?", *filters.CreatedByID)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Priority != "" {
		query = query.Where("priority = ?", filters.Priority)
	}
	if filters.DocumentID != nil {
		query = query.Where("document_id = ?", *filters.DocumentID)
	}
	if filters.RegulationID != nil {
		query = query.Where("regulation_id = ?", *filters.RegulationID)
	}
	if filters.AgencyID != nil {
		query = query.Where("agency_id = ?", *filters.AgencyID)
	}
	if filters.CategoryID != nil {
		query = query.Where("category_id = ?", *filters.CategoryID)
	}
	if filters.ParsedFromText != nil {
		query = query.Where("parsed_from_text = ?", *filters.ParsedFromText)
	}
	if filters.IsPublic != nil {
		query = query.Where("is_public = ?", *filters.IsPublic)
	}
	if !filters.DueDateFrom.IsZero() {
		query = query.Where("due_date >= ?", filters.DueDateFrom)
	}
	if !filters.DueDateTo.IsZero() {
		query = query.Where("due_date <= ?", filters.DueDateTo)
	}
	if filters.Search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	query.Count(&total)

	// Apply pagination and ordering
	if filters.Limit > 0 {
		query = query.Limit(filters.Limit)
	}
	if filters.Offset > 0 {
		query = query.Offset(filters.Offset)
	}

	orderBy := "created_at DESC"
	if filters.OrderBy != "" {
		orderBy = filters.OrderBy
	}
	query = query.Order(orderBy)

	// Load relationships and execute query
	err := query.Preload("AssignedTo").
		Preload("CreatedBy").
		Preload("Document").
		Preload("Regulation").
		Preload("Agency").
		Preload("Category").
		Find(&tasks).Error

	return tasks, total, err
}

// CompleteTask marks a task as completed and triggers performance evaluation
func (s *TaskService) CompleteTask(id uint, completedByID uint) error {
	now := time.Now()

	// Update task status
	err := s.db.Model(&models.Task{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":       models.TaskStatusCompleted,
			"completed_at": &now,
			"completed_by": completedByID,
		}).Error

	if err != nil {
		return err
	}

	// Trigger automatic performance evaluation
	var task models.Task
	if err := s.db.First(&task, id).Error; err != nil {
		return err
	}

	// Trigger automatic performance evaluation using database trigger
	// The database trigger will automatically calculate performance when task is updated
	// This avoids circular import issues while ensuring performance is calculated

	return nil
}

// GetTasksByDueDate retrieves tasks due within a specific date range
func (s *TaskService) GetTasksByDueDate(from, to time.Time) ([]models.Task, error) {
	var tasks []models.Task
	err := s.db.Where("due_date BETWEEN ? AND ?", from, to).
		Preload("AssignedTo").
		Preload("CreatedBy").
		Preload("Document").
		Preload("Regulation").
		Order("due_date ASC").
		Find(&tasks).Error

	return tasks, err
}

// GetOverdueTasks retrieves all overdue tasks
func (s *TaskService) GetOverdueTasks() ([]models.Task, error) {
	var tasks []models.Task
	now := time.Now()
	err := s.db.Where("due_date < ? AND status IN (?)", now,
		[]models.TaskStatus{models.TaskStatusPending, models.TaskStatusInProgress}).
		Preload("AssignedTo").
		Preload("CreatedBy").
		Preload("Document").
		Preload("Regulation").
		Order("due_date ASC").
		Find(&tasks).Error

	return tasks, err
}

// TaskFilters represents filters for task queries
type TaskFilters struct {
	AssignedToID   *uint               `json:"assigned_to_id"`
	CreatedByID    *uint               `json:"created_by_id"`
	Status         models.TaskStatus   `json:"status"`
	Type           models.TaskType     `json:"type"`
	Priority       models.TaskPriority `json:"priority"`
	DocumentID     *uint               `json:"document_id"`
	RegulationID   *uint               `json:"regulation_id"`
	AgencyID       *uint               `json:"agency_id"`
	CategoryID     *uint               `json:"category_id"`
	ParsedFromText *bool               `json:"parsed_from_text"`
	IsPublic       *bool               `json:"is_public"`
	DueDateFrom    time.Time           `json:"due_date_from"`
	DueDateTo      time.Time           `json:"due_date_to"`
	Search         string              `json:"search"`
	Limit          int                 `json:"limit"`
	Offset         int                 `json:"offset"`
	OrderBy        string              `json:"order_by"`
}

// Auto-generation methods for unified system

// AutoGenerateTasksFromDocument creates tasks based on document content and dates
func (s *TaskService) AutoGenerateTasksFromDocument(document *models.Document, userID uint) error {
	// Parse content for dates and deadlines
	nlpService := NewNLPService()
	deadlines := nlpService.ExtractDeadlines(document.Content)

	for _, deadline := range deadlines {
		task := &models.Task{
			Title:          fmt.Sprintf("Document Deadline: %s", deadline.Description),
			Description:    fmt.Sprintf("Deadline extracted from document '%s': %s", document.Title, deadline.Context),
			Type:           models.TaskTypeDeadline,
			Status:         models.TaskStatusPending,
			Priority:       s.calculateTaskPriority(deadline.Urgency),
			DueDate:        &deadline.Date,
			SourceType:     "document",
			SourceID:       &document.ID,
			SourceText:     deadline.Context,
			ParsedFromText: true,
			CreatedByID:    userID,
			AgencyID:       &document.AgencyID,
			DocumentID:     &document.ID,
		}

		if err := s.db.Create(task).Error; err != nil {
			log.Printf("Failed to create task from document: %v", err)
			continue
		}
	}

	// Create review tasks for document lifecycle
	if document.EffectiveDate != nil {
		reviewDate := document.EffectiveDate.AddDate(0, 0, -30) // 30 days before effective
		if reviewDate.After(time.Now()) {
			task := &models.Task{
				Title:       fmt.Sprintf("Review Document Before Effective Date: %s", document.Title),
				Description: fmt.Sprintf("Review document '%s' before it becomes effective on %s", document.Title, document.EffectiveDate.Format("2006-01-02")),
				Type:        models.TaskTypeReview,
				Status:      models.TaskStatusPending,
				Priority:    models.TaskPriorityMedium,
				DueDate:     &reviewDate,
				SourceType:  "document",
				SourceID:    &document.ID,
				CreatedByID: userID,
				AgencyID:    &document.AgencyID,
				DocumentID:  &document.ID,
			}

			if err := s.db.Create(task).Error; err != nil {
				log.Printf("Failed to create review task: %v", err)
			}
		}
	}

	return nil
}

// AutoGenerateTasksFromRegulation creates implementation and compliance tasks for regulations
func (s *TaskService) AutoGenerateTasksFromRegulation(regulation *models.LawsAndRules, userID uint) error {
	// Implementation deadline task
	if regulation.EffectiveDate != nil {
		implementationDate := regulation.EffectiveDate.AddDate(0, 0, -60) // 60 days before effective
		if implementationDate.After(time.Now()) {
			task := &models.Task{
				Title:        fmt.Sprintf("Implement Regulation: %s", regulation.Title),
				Description:  fmt.Sprintf("Prepare for implementation of regulation '%s' effective %s", regulation.Title, regulation.EffectiveDate.Format("2006-01-02")),
				Type:         models.TaskTypeDeadline,
				Status:       models.TaskStatusPending,
				Priority:     models.TaskPriorityHigh,
				DueDate:      &implementationDate,
				SourceType:   "regulation",
				SourceID:     &regulation.ID,
				CreatedByID:  userID,
				AgencyID:     &regulation.AgencyID,
				RegulationID: &regulation.ID,
			}

			if err := s.db.Create(task).Error; err != nil {
				log.Printf("Failed to create implementation task: %v", err)
			}
		}
	}

	return nil
}

// calculateTaskPriority determines task priority based on urgency
func (s *TaskService) calculateTaskPriority(urgency string) models.TaskPriority {
	switch urgency {
	case "urgent", "immediate", "critical":
		return models.TaskPriorityHigh
	case "soon", "important":
		return models.TaskPriorityMedium
	default:
		return models.TaskPriorityLow
	}
}

// MarkTaskComplete marks a task as completed and triggers performance updates
func (s *TaskService) MarkTaskComplete(taskID uint, userID uint) error {
	var task models.Task
	if err := s.db.First(&task, taskID).Error; err != nil {
		return err
	}

	now := time.Now()
	updates := map[string]interface{}{
		"status":       models.TaskStatusCompleted,
		"completed_at": &now,
		"completed_by": userID,
		"updated_at":   now,
	}

	if err := s.db.Model(&task).Updates(updates).Error; err != nil {
		return err
	}

	// Update the task object with new values
	task.Status = models.TaskStatusCompleted
	task.CompletedAt = &now

	// Trigger finance performance update if task is related to document/regulation
	if task.DocumentID != nil || task.RegulationID != nil {
		financeService := NewFinanceService()
		financeService.UpdatePerformanceFromTask(&task)
	}

	return nil
}
