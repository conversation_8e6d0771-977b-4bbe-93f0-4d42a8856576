'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { contentApi } from '../../../../services/enterpriseApi';
import { ContentRepository } from '../../../../types/enterprise';

const ContentRepositoryViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const repositoryId = parseInt(params.id as string);
  
  const [repository, setRepository] = useState<ContentRepository | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (repositoryId) {
      fetchRepository();
    }
  }, [repositoryId]);

  const fetchRepository = async () => {
    try {
      setLoading(true);
      const response = await contentApi.getRepository(repositoryId);
      setRepository(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch content repository');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this content repository?')) return;
    
    try {
      await contentApi.deleteRepository(repositoryId);
      router.push('/enterprise/content/repositories');
    } catch (err: any) {
      setError(err.message || 'Failed to delete content repository');
    }
  };

  if (loading) return <div className="p-6">Loading content repository...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!repository) return <div className="p-6">Content repository not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Content Repository Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/content/repositories')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Repositories
          </button>
          <button
            onClick={() => router.push(`/enterprise/content/repositories/${repositoryId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Repository
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Repository
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{repository.repository_name}</h2>
              <p className="text-sm text-gray-600">Code: {repository.repository_code}</p>
              <p className="text-sm text-gray-600">Type: {repository.type}</p>
            </div>
            <div className="text-right">
              <div className="flex flex-col space-y-2">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  repository.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {repository.is_active ? 'Active' : 'Inactive'}
                </span>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  repository.is_public 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {repository.is_public ? 'Public' : 'Private'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Repository Overview */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Repository Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {repository.current_files || 0}
              </div>
              <div className="text-sm text-blue-600">Total Files</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {repository.current_size ? `${(repository.current_size / 1024 / 1024).toFixed(1)} MB` : '0 MB'}
              </div>
              <div className="text-sm text-green-600">Current Size</div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {repository.max_size ? `${(repository.max_size / 1024 / 1024).toFixed(1)} MB` : 'Unlimited'}
              </div>
              <div className="text-sm text-orange-600">Max Size</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {repository.access_count || 0}
              </div>
              <div className="text-sm text-purple-600">Access Count</div>
            </div>
          </div>
        </div>

        {/* Repository Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Repository Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Storage Type</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{repository.storage_type}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Storage Path</label>
              <p className="mt-1 text-sm text-gray-900 font-mono text-xs">
                {repository.storage_path || 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Classification</label>
              <p className="mt-1 text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  repository.classification === 'public' 
                    ? 'bg-green-100 text-green-800'
                    : repository.classification === 'internal'
                    ? 'bg-blue-100 text-blue-800'
                    : repository.classification === 'confidential'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {repository.classification}
                </span>
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Compliance Level</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{repository.compliance_level}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Backup Schedule</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{repository.backup_schedule}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Last Accessed</label>
              <p className="mt-1 text-sm text-gray-900">
                {repository.last_accessed ? 
                  new Date(repository.last_accessed).toLocaleString() : 
                  'Never'
                }
              </p>
            </div>
          </div>

          {repository.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {repository.description}
              </div>
            </div>
          )}
        </div>

        {/* Storage Configuration */}
        {repository.storage_config && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Storage Configuration</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">
                {JSON.stringify(JSON.parse(repository.storage_config || '{}'), null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Access Policy */}
        {repository.access_policy && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Access Policy</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">
                {repository.access_policy}
              </pre>
            </div>
          </div>
        )}

        {/* Capacity and Limits */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Capacity and Limits</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Storage Utilization</label>
              <div className="mt-2">
                <div className="bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ 
                      width: `${repository.max_size > 0 ? 
                        Math.min((repository.current_size / repository.max_size) * 100, 100) : 
                        0}%` 
                    }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {repository.max_size > 0 ? 
                    `${((repository.current_size / repository.max_size) * 100).toFixed(1)}%` : 
                    '0%'
                  } utilized
                </p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">File Count Utilization</label>
              <div className="mt-2">
                <div className="bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ 
                      width: `${repository.max_files > 0 ? 
                        Math.min((repository.current_files / repository.max_files) * 100, 100) : 
                        0}%` 
                    }}
                  ></div>
                </div>
                <p className="text-sm text-gray-600 mt-1">
                  {repository.max_files > 0 ? 
                    `${((repository.current_files / repository.max_files) * 100).toFixed(1)}%` : 
                    '0%'
                  } utilized
                </p>
              </div>
            </div>
          </div>

          <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Available Space</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {repository.max_size > 0 ? 
                  `${((repository.max_size - repository.current_size) / 1024 / 1024).toFixed(1)} MB` : 
                  'Unlimited'
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Available File Slots</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">
                {repository.max_files > 0 ? 
                  (repository.max_files - repository.current_files).toLocaleString() : 
                  'Unlimited'
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Max Versions</label>
              <p className="mt-1 text-lg font-semibold text-gray-900">{repository.max_versions}</p>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2 ${
                repository.versioning_enabled 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {repository.versioning_enabled ? 'Enabled' : 'Disabled'}
              </span>
              <span className="text-sm text-gray-700">Versioning</span>
            </div>
            
            <div className="flex items-center">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2 ${
                repository.backup_enabled 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {repository.backup_enabled ? 'Enabled' : 'Disabled'}
              </span>
              <span className="text-sm text-gray-700">Backup</span>
            </div>
            
            <div className="flex items-center">
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mr-2 ${
                repository.encryption_key 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {repository.encryption_key ? 'Enabled' : 'Disabled'}
              </span>
              <span className="text-sm text-gray-700">Encryption</span>
            </div>
          </div>
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(repository.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(repository.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentRepositoryViewPage;
