package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// EmployeeRequest represents the request structure for employees
type EmployeeRequest struct {
	EmployeeID        string     `json:"employee_id" binding:"required"`
	FirstName         string     `json:"first_name" binding:"required"`
	LastName          string     `json:"last_name" binding:"required"`
	MiddleName        string     `json:"middle_name"`
	DateOfBirth       *time.Time `json:"date_of_birth"`
	Gender            string     `json:"gender"`
	MaritalStatus     string     `json:"marital_status"`
	Nationality       string     `json:"nationality"`
	PersonalEmail     string     `json:"personal_email"`
	PersonalPhone     string     `json:"personal_phone"`
	EmergencyContact  string     `json:"emergency_contact"`
	Address           string     `json:"address"`
	WorkEmail         string     `json:"work_email"`
	WorkPhone         string     `json:"work_phone"`
	JobTitle          string     `json:"job_title" binding:"required"`
	JobDescription    string     `json:"job_description"`
	DepartmentID      *uint      `json:"department_id"`
	ManagerID         *uint      `json:"manager_id"`
	BaseSalary        float64    `json:"base_salary"`
	Currency          string     `json:"currency"`
	PayFrequency      string     `json:"pay_frequency"`
	WorkSchedule      string     `json:"work_schedule"`
	TimeZone          string     `json:"time_zone"`
	WorkLocation      string     `json:"work_location"`
	PerformanceRating float64    `json:"performance_rating"`
	VacationDays      int        `json:"vacation_days"`
	SickDays          int        `json:"sick_days"`
	PersonalDays      int        `json:"personal_days"`
	HireDate          time.Time  `json:"hire_date" binding:"required"`
	Status            string     `json:"status"`
	Metadata          string     `json:"metadata"`
}

// DepartmentRequest represents the request structure for departments
type DepartmentRequest struct {
	DepartmentCode string  `json:"department_code" binding:"required"`
	DepartmentName string  `json:"department_name" binding:"required"`
	Description    string  `json:"description"`
	ManagerID      *uint   `json:"manager_id"`
	BudgetAmount   float64 `json:"budget_amount"`
	ActualSpend    float64 `json:"actual_spend"`
	CostCenterID   *uint   `json:"cost_center_id"`
	Location       string  `json:"location"`
	OfficeSpace    string  `json:"office_space"`
	IsActive       bool    `json:"is_active"`
	EmployeeCount  int     `json:"employee_count"`
	Metadata       string  `json:"metadata"`
}

// PositionRequest represents the request structure for positions
type PositionRequest struct {
	PositionCode    string  `json:"position_code" binding:"required"`
	PositionTitle   string  `json:"position_title" binding:"required"`
	Description     string  `json:"description"`
	Level           string  `json:"level"`
	Grade           string  `json:"grade"`
	JobFamily       string  `json:"job_family"`
	DepartmentID    uint    `json:"department_id" binding:"required"`
	ReportsToID     *uint   `json:"reports_to_id"`
	MinSalary       float64 `json:"min_salary"`
	MaxSalary       float64 `json:"max_salary"`
	RequiredSkills  string  `json:"required_skills"`
	PreferredSkills string  `json:"preferred_skills"`
	IsActive        bool    `json:"is_active"`
	IsApproved      bool    `json:"is_approved"`
	HeadcountLimit  int     `json:"headcount_limit"`
	CurrentCount    int     `json:"current_count"`
	Metadata        string  `json:"metadata"`
}

// PerformanceReviewRequest represents the request structure for performance reviews
type PerformanceReviewRequest struct {
	EmployeeID       uint       `json:"employee_id" binding:"required"`
	ReviewerID       uint       `json:"reviewer_id" binding:"required"`
	ReviewPeriod     string     `json:"review_period" binding:"required"`
	ReviewType       string     `json:"review_type"`
	Goals            string     `json:"goals"`
	Achievements     string     `json:"achievements"`
	AreasImprovement string     `json:"areas_improvement"`
	OverallRating    float64    `json:"overall_rating"`
	Comments         string     `json:"comments"`
	Status           string     `json:"status"`
	DueDate          *time.Time `json:"due_date"`
	Metadata         string     `json:"metadata"`
}

// TrainingRequest represents the request structure for training programs
type TrainingRequest struct {
	TrainingName       string    `json:"training_name" binding:"required"`
	Description        string    `json:"description"`
	TrainingType       string    `json:"training_type"`
	Category           string    `json:"category"`
	Duration           int       `json:"duration"`
	DurationUnit       string    `json:"duration_unit"`
	StartDate          time.Time `json:"start_date" binding:"required"`
	EndDate            time.Time `json:"end_date" binding:"required"`
	InstructorName     string    `json:"instructor_name"`
	InstructorEmail    string    `json:"instructor_email"`
	Location           string    `json:"location"`
	MaxParticipants    int       `json:"max_participants"`
	Cost               float64   `json:"cost"`
	Currency           string    `json:"currency"`
	Prerequisites      string    `json:"prerequisites"`
	LearningObjectives string    `json:"learning_objectives"`
	Materials          string    `json:"materials"`
	Status             string    `json:"status"`
	EnrollmentCount    int       `json:"enrollment_count"`
	CompletionRate     float64   `json:"completion_rate"`
	Metadata           string    `json:"metadata"`
}

// Employee Handlers

// GetEmployees returns all employees with pagination
func GetEmployees(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total employees
	var total int64
	db.Model(&models.Employee{}).Count(&total)

	// Get employees with pagination
	var employees []models.Employee
	offset := (page - 1) * perPage
	if err := db.Preload("Department").Preload("Manager").
		Offset(offset).
		Limit(perPage).
		Order("last_name ASC, first_name ASC").
		Find(&employees).Error; err != nil {
		HandleInternalError(c, "Failed to fetch employees: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"employees": employees,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateEmployee creates a new employee
func CreateEmployee(c *gin.Context) {
	var req EmployeeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create employee
	employee := models.Employee{
		EmployeeID:       req.EmployeeID,
		FirstName:        req.FirstName,
		LastName:         req.LastName,
		MiddleName:       req.MiddleName,
		DateOfBirth:      req.DateOfBirth,
		Gender:           req.Gender,
		MaritalStatus:    req.MaritalStatus,
		Nationality:      req.Nationality,
		PersonalEmail:    req.PersonalEmail,
		PersonalPhone:    req.PersonalPhone,
		EmergencyContact: req.EmergencyContact,
		Address:          req.Address,

		JobTitle:          req.JobTitle,
		JobDescription:    req.JobDescription,
		DepartmentID:      req.DepartmentID,
		ManagerID:         req.ManagerID,
		BaseSalary:        req.BaseSalary,
		Currency:          req.Currency,
		PayFrequency:      req.PayFrequency,
		WorkSchedule:      req.WorkSchedule,
		TimeZone:          req.TimeZone,
		WorkLocation:      req.WorkLocation,
		PerformanceRating: req.PerformanceRating,
		VacationDays:      req.VacationDays,
		SickDays:          req.SickDays,
		PersonalDays:      req.PersonalDays,
		HireDate:          req.HireDate,
		EmploymentStatus:  models.EmploymentStatus(req.Status),
		Metadata:          req.Metadata,
	}

	if err := db.Create(&employee).Error; err != nil {
		HandleInternalError(c, "Failed to create employee: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Department").Preload("Manager").First(&employee, employee.ID)

	c.JSON(http.StatusCreated, gin.H{"employee": employee})
}

// GetEmployee returns a specific employee
func GetEmployee(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid employee ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var employee models.Employee
	if err := db.Preload("Department").Preload("Manager").First(&employee, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Employee not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"employee": employee})
}

// UpdateEmployee updates an employee
func UpdateEmployee(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid employee ID"})
		return
	}

	var req EmployeeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var employee models.Employee
	if err := db.First(&employee, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Employee not found"})
		return
	}

	// Update employee fields
	employee.EmployeeID = req.EmployeeID
	employee.FirstName = req.FirstName
	employee.LastName = req.LastName
	employee.MiddleName = req.MiddleName
	employee.DateOfBirth = req.DateOfBirth
	employee.Gender = req.Gender
	employee.MaritalStatus = req.MaritalStatus
	employee.Nationality = req.Nationality
	employee.PersonalEmail = req.PersonalEmail
	employee.PersonalPhone = req.PersonalPhone
	employee.EmergencyContact = req.EmergencyContact
	employee.Address = req.Address

	employee.JobTitle = req.JobTitle
	employee.JobDescription = req.JobDescription
	employee.DepartmentID = req.DepartmentID
	employee.ManagerID = req.ManagerID
	employee.BaseSalary = req.BaseSalary
	employee.Currency = req.Currency
	employee.PayFrequency = req.PayFrequency
	employee.WorkSchedule = req.WorkSchedule
	employee.TimeZone = req.TimeZone
	employee.WorkLocation = req.WorkLocation
	employee.PerformanceRating = req.PerformanceRating
	employee.VacationDays = req.VacationDays
	employee.SickDays = req.SickDays
	employee.PersonalDays = req.PersonalDays
	employee.HireDate = req.HireDate
	employee.EmploymentStatus = models.EmploymentStatus(req.Status)
	employee.Metadata = req.Metadata

	if err := db.Save(&employee).Error; err != nil {
		HandleInternalError(c, "Failed to update employee: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Department").Preload("Manager").First(&employee, employee.ID)

	c.JSON(http.StatusOK, gin.H{"employee": employee})
}
