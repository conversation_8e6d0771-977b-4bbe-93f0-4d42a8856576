package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// RetentionPolicyRequest represents the request structure for retention policies
type RetentionPolicyRequest struct {
	Name          string `json:"name" binding:"required"`
	Description   string `json:"description"`
	EntityType    string `json:"entity_type" binding:"required"`
	RetentionDays int    `json:"retention_days" binding:"required"`
	IsActive      bool   `json:"is_active"`
	AutoDelete    bool   `json:"auto_delete"`
}

// GetRetentionPolicies returns all retention policies with pagination
func GetRetentionPolicies(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>fault<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.Default<PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total retention policies
	var total int64
	db.Model(&models.RetentionPolicy{}).Count(&total)

	// Get retention policies with pagination
	var policies []models.RetentionPolicy
	offset := (page - 1) * perPage
	if err := db.Order("name ASC").
		Limit(perPage).
		Offset(offset).
		Find(&policies).Error; err != nil {
		HandleInternalError(c, "Failed to fetch retention policies: "+err.Error())
		return
	}

	// Convert to response format
	policyResponses := make([]gin.H, len(policies))
	for i, policy := range policies {
		policyResponses[i] = gin.H{
			"id":                     policy.ID,
			"name":                   policy.Name,
			"description":            policy.Description,
			"type":                   policy.Type,
			"status":                 policy.Status,
			"retention_period_days":  policy.RetentionPeriodDays,
			"retention_period_years": policy.RetentionPeriodYears,
			"action":                 policy.Action,
			"auto_execute":           policy.AutoExecute,
			"created_at":             policy.CreatedAt,
			"updated_at":             policy.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       policyResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetRetentionPolicy returns a single retention policy by ID
func GetRetentionPolicy(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get retention policy
	var policy models.RetentionPolicy
	if err := db.First(&policy, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Retention policy")
			return
		}
		HandleInternalError(c, "Failed to fetch retention policy: "+err.Error())
		return
	}

	response := gin.H{
		"id":                     policy.ID,
		"name":                   policy.Name,
		"description":            policy.Description,
		"type":                   policy.Type,
		"status":                 policy.Status,
		"retention_period_days":  policy.RetentionPeriodDays,
		"retention_period_years": policy.RetentionPeriodYears,
		"action":                 policy.Action,
		"auto_execute":           policy.AutoExecute,
		"created_at":             policy.CreatedAt,
		"updated_at":             policy.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Retention policy retrieved successfully",
		Data:    response,
	})
}

// CreateRetentionPolicy creates a new retention policy
func CreateRetentionPolicy(c *gin.Context) {
	var req RetentionPolicyRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create retention policy
	policy := &models.RetentionPolicy{
		Name:                req.Name,
		Description:         req.Description,
		Type:                models.RetentionPolicyType(req.EntityType),
		RetentionPeriodDays: req.RetentionDays,
		Status:              models.RetentionStatusActive,
		AutoExecute:         req.AutoDelete,
		CreatedByID:         1, // Default user ID
		OwnerID:             1, // Default owner ID
	}

	if err := db.Create(policy).Error; err != nil {
		HandleInternalError(c, "Failed to create retention policy: "+err.Error())
		return
	}

	response := gin.H{
		"id":                     policy.ID,
		"name":                   policy.Name,
		"description":            policy.Description,
		"type":                   policy.Type,
		"status":                 policy.Status,
		"retention_period_days":  policy.RetentionPeriodDays,
		"retention_period_years": policy.RetentionPeriodYears,
		"action":                 policy.Action,
		"auto_execute":           policy.AutoExecute,
		"created_at":             policy.CreatedAt,
		"updated_at":             policy.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Retention policy created successfully",
		Data:    response,
	})
}

// UpdateRetentionPolicy updates an existing retention policy
func UpdateRetentionPolicy(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RetentionPolicyRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing retention policy
	var policy models.RetentionPolicy
	if err := db.First(&policy, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Retention policy")
			return
		}
		HandleInternalError(c, "Failed to fetch retention policy: "+err.Error())
		return
	}

	// Update retention policy fields
	policy.Name = req.Name
	policy.Description = req.Description
	policy.Type = models.RetentionPolicyType(req.EntityType)
	policy.RetentionPeriodDays = req.RetentionDays
	policy.Status = models.RetentionStatusActive
	policy.AutoExecute = req.AutoDelete

	if err := db.Save(&policy).Error; err != nil {
		HandleInternalError(c, "Failed to update retention policy: "+err.Error())
		return
	}

	response := gin.H{
		"id":                     policy.ID,
		"name":                   policy.Name,
		"description":            policy.Description,
		"type":                   policy.Type,
		"status":                 policy.Status,
		"retention_period_days":  policy.RetentionPeriodDays,
		"retention_period_years": policy.RetentionPeriodYears,
		"action":                 policy.Action,
		"auto_execute":           policy.AutoExecute,
		"created_at":             policy.CreatedAt,
		"updated_at":             policy.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Retention policy updated successfully",
		Data:    response,
	})
}

// DeleteRetentionPolicy deletes a retention policy
func DeleteRetentionPolicy(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if retention policy exists
	var policy models.RetentionPolicy
	if err := db.First(&policy, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Retention policy")
			return
		}
		HandleInternalError(c, "Failed to fetch retention policy: "+err.Error())
		return
	}

	// Delete retention policy
	if err := db.Delete(&policy).Error; err != nil {
		HandleInternalError(c, "Failed to delete retention policy: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Retention policy deleted successfully",
	})
}

// ApplyRetentionPolicies applies retention policies to clean up old data
func ApplyRetentionPolicies(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get all active retention policies
	var policies []models.RetentionPolicy
	if err := db.Where("is_active = ?", true).Find(&policies).Error; err != nil {
		HandleInternalError(c, "Failed to fetch retention policies: "+err.Error())
		return
	}

	appliedPolicies := make([]gin.H, 0)
	totalProcessed := 0
	totalArchived := 0
	totalDeleted := 0
	totalErrors := 0

	for _, policy := range policies {
		// Calculate cutoff date based on retention period
		cutoffDate := time.Now().AddDate(0, 0, -policy.RetentionPeriodDays)

		var processed, archived, deleted, errors int
		var status string

		// Apply retention logic based on entity type
		switch policy.Type {
		case "documents":
			processed, archived, deleted, errors = applyDocumentRetention(db, policy, cutoffDate)
		case "logs":
			processed, archived, deleted, errors = applyLogRetention(db, policy, cutoffDate)
		case "audit_trails":
			processed, archived, deleted, errors = applyAuditRetention(db, policy, cutoffDate)
		case "user_sessions":
			processed, archived, deleted, errors = applySessionRetention(db, policy, cutoffDate)
		case "temporary_files":
			processed, archived, deleted, errors = applyTempFileRetention(db, policy, cutoffDate)
		default:
			// For unknown types, just mark as skipped
			status = "skipped"
		}

		if status == "" {
			if errors > 0 {
				if processed == errors {
					status = "failed"
				} else {
					status = "partial"
				}
			} else {
				status = "success"
			}
		}

		appliedPolicies = append(appliedPolicies, gin.H{
			"policy_id":             policy.ID,
			"policy_name":           policy.Name,
			"type":                  policy.Type,
			"retention_period_days": policy.RetentionPeriodDays,
			"cutoff_date":           cutoffDate,
			"processed":             processed,
			"archived":              archived,
			"deleted":               deleted,
			"errors":                errors,
			"status":                status,
		})

		totalProcessed += processed
		totalArchived += archived
		totalDeleted += deleted
		totalErrors += errors
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Retention policies applied successfully",
		Data: gin.H{
			"applied_policies": appliedPolicies,
			"total_policies":   len(appliedPolicies),
			"total_processed":  totalProcessed,
			"total_archived":   totalArchived,
			"total_deleted":    totalDeleted,
			"total_errors":     totalErrors,
			"execution_time":   time.Now(),
		},
	})
}

// Helper functions for applying retention policies to different entity types

// applyDocumentRetention applies retention policy to documents
func applyDocumentRetention(db *gorm.DB, policy models.RetentionPolicy, cutoffDate time.Time) (processed, archived, deleted, errors int) {
	var documents []models.Document
	if err := db.Where("created_at < ?", cutoffDate).Find(&documents).Error; err != nil {
		errors++
		return
	}

	for _, doc := range documents {
		processed++
		switch policy.Action {
		case models.ActionArchive:
			if err := db.Model(&doc).Update("status", "archived").Error; err != nil {
				errors++
			} else {
				archived++
			}
		case models.ActionDelete:
			if err := db.Delete(&doc).Error; err != nil {
				errors++
			} else {
				deleted++
			}
		}
	}
	return
}

// applyLogRetention applies retention policy to log entries
func applyLogRetention(db *gorm.DB, policy models.RetentionPolicy, cutoffDate time.Time) (processed, archived, deleted, errors int) {
	// Assuming there's a logs table - adjust based on actual log model
	var count int64
	if err := db.Table("logs").Where("created_at < ?", cutoffDate).Count(&count).Error; err != nil {
		errors++
		return
	}

	processed = int(count)
	switch policy.Action {
	case models.ActionDelete:
		if err := db.Exec("DELETE FROM logs WHERE created_at < ?", cutoffDate).Error; err != nil {
			errors++
		} else {
			deleted = processed
		}
	case models.ActionArchive:
		// In a real implementation, this would move logs to archive storage
		archived = processed
	}
	return
}

// applyAuditRetention applies retention policy to audit trails
func applyAuditRetention(db *gorm.DB, policy models.RetentionPolicy, cutoffDate time.Time) (processed, archived, deleted, errors int) {
	var count int64
	if err := db.Table("audit_logs").Where("created_at < ?", cutoffDate).Count(&count).Error; err != nil {
		errors++
		return
	}

	processed = int(count)
	switch policy.Action {
	case models.ActionDelete:
		if err := db.Exec("DELETE FROM audit_logs WHERE created_at < ?", cutoffDate).Error; err != nil {
			errors++
		} else {
			deleted = processed
		}
	case models.ActionArchive:
		archived = processed
	}
	return
}

// applySessionRetention applies retention policy to user sessions
func applySessionRetention(db *gorm.DB, policy models.RetentionPolicy, cutoffDate time.Time) (processed, archived, deleted, errors int) {
	var count int64
	if err := db.Table("user_sessions").Where("created_at < ?", cutoffDate).Count(&count).Error; err != nil {
		errors++
		return
	}

	processed = int(count)
	if policy.Action == models.ActionDelete {
		if err := db.Exec("DELETE FROM user_sessions WHERE created_at < ?", cutoffDate).Error; err != nil {
			errors++
		} else {
			deleted = processed
		}
	}
	return
}

// applyTempFileRetention applies retention policy to temporary files
func applyTempFileRetention(db *gorm.DB, policy models.RetentionPolicy, cutoffDate time.Time) (processed, archived, deleted, errors int) {
	var count int64
	if err := db.Table("temporary_files").Where("created_at < ?", cutoffDate).Count(&count).Error; err != nil {
		errors++
		return
	}

	processed = int(count)
	if policy.Action == models.ActionDelete {
		if err := db.Exec("DELETE FROM temporary_files WHERE created_at < ?", cutoffDate).Error; err != nil {
			errors++
		} else {
			deleted = processed
		}
	}
	return
}
