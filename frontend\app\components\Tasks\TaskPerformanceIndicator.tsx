'use client'

import React from 'react';
import { Task, type TaskPerformanceHistory } from '../../types';

// Performance Indicator Component (matching finance performance styling)
interface TaskPerformanceIndicatorProps {
  task: Task;
  showDetails?: boolean;
  className?: string;
}

export const TaskPerformanceIndicator: React.FC<TaskPerformanceIndicatorProps> = ({
  task,
  showDetails = false,
  className = ''
}) => {
  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-100 text-green-800';
    if (percentage >= 80) return 'bg-blue-100 text-blue-800';
    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800';
    if (percentage >= 60) return 'bg-orange-100 text-orange-800';
    return 'bg-red-100 text-red-800';
  };

  const getPerformanceLabel = (percentage: number) => {
    if (percentage >= 90) return 'Excellent';
    if (percentage >= 80) return 'Good';
    if (percentage >= 70) return 'Satisfactory';
    if (percentage >= 60) return 'Needs Improvement';
    return 'Poor';
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  if (!showDetails) {
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPerformanceColor(task.performance_percentage)} ${className}`}>
        {formatPercentage(task.performance_percentage)}
      </span>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Performance Evaluation</h3>
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getPerformanceColor(task.performance_percentage)}`}>
          {formatPercentage(task.performance_percentage)} - {getPerformanceLabel(task.performance_percentage)}
        </span>
      </div>

      {/* Performance Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Deadline Adherence</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${getPerformanceColor(task.deadline_adherence_score)}`}>
              {formatPercentage(task.deadline_adherence_score)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.deadline_adherence_score}%` }}
            ></div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Quality Score</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${getPerformanceColor(task.quality_score)}`}>
              {formatPercentage(task.quality_score)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.quality_score}%` }}
            ></div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Completion Efficiency</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${getPerformanceColor(task.completion_efficiency)}`}>
              {formatPercentage(task.completion_efficiency)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-purple-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.completion_efficiency}%` }}
            ></div>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Priority Handling</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${getPerformanceColor(task.priority_handling_score)}`}>
              {formatPercentage(task.priority_handling_score)}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-orange-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${task.priority_handling_score}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Performance Notes */}
      {task.performance_notes && (
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Performance Notes</h4>
          <div className="text-sm text-gray-700 whitespace-pre-line bg-gray-50 p-3 rounded-md">
            {task.performance_notes}
          </div>
        </div>
      )}

      {/* Evaluation Details */}
      <div className="border-t pt-4 mt-4">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            {task.is_auto_calculated ? 'Automatically calculated' : 'Manually evaluated'}
            {task.evaluated_by && ` by ${task.evaluated_by.first_name} ${task.evaluated_by.last_name}`}
          </span>
          {task.evaluation_date && (
            <span>
              {new Date(task.evaluation_date).toLocaleDateString()}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

// Performance Gauge Component (Circular) - matching finance page
interface TaskPerformanceGaugeProps {
  percentage: number;
  size?: number;
  strokeWidth?: number;
  showLabel?: boolean;
  className?: string;
}

export const TaskPerformanceGauge: React.FC<TaskPerformanceGaugeProps> = ({
  percentage,
  size = 120,
  strokeWidth = 8,
  showLabel = true,
  className = ''
}) => {
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (percentage / 100) * circumference;

  const getStrokeColor = (perf: number) => {
    if (perf >= 80) return '#10B981'; // green-500
    if (perf >= 60) return '#3B82F6'; // blue-500
    if (perf >= 40) return '#F59E0B'; // yellow-500
    return '#EF4444'; // red-500
  };

  const strokeColor = getStrokeColor(percentage);

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="relative">
        <svg
          width={size}
          height={size}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke="#E5E7EB"
            strokeWidth={strokeWidth}
            fill="transparent"
          />
          {/* Progress circle */}
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={strokeColor}
            strokeWidth={strokeWidth}
            fill="transparent"
            strokeDasharray={circumference}
            strokeDashoffset={offset}
            strokeLinecap="round"
            className="transition-all duration-500 ease-in-out"
          />
        </svg>
        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-lg font-bold text-gray-900">
            {percentage.toFixed(0)}%
          </span>
        </div>
      </div>
      {showLabel && (
        <span className="mt-2 text-sm font-medium text-gray-700">
          Performance
        </span>
      )}
    </div>
  );
};

// Performance History Component
interface TaskPerformanceHistoryComponentProps {
  history: TaskPerformanceHistory[];
  className?: string;
}

export const TaskPerformanceHistoryComponent: React.FC<TaskPerformanceHistoryComponentProps> = ({
  history,
  className = ''
}) => {
  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    if (percentage >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  if (history.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance History</h3>
        <p className="text-gray-500 text-center py-8">No performance history available.</p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-md p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance History</h3>
      <div className="space-y-4">
        {history.map((entry) => (
          <div key={entry.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className={`text-lg font-semibold ${getPerformanceColor(entry.performance_percentage)}`}>
                {entry.performance_percentage.toFixed(1)}%
              </span>
              <span className="text-sm text-gray-500">
                {new Date(entry.evaluation_date).toLocaleDateString()}
              </span>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600 mb-2">
              <span>Deadline: {entry.deadline_adherence_score.toFixed(1)}%</span>
              <span>Quality: {entry.quality_score.toFixed(1)}%</span>
              <span>Efficiency: {entry.completion_efficiency.toFixed(1)}%</span>
              <span>Priority: {entry.priority_handling_score.toFixed(1)}%</span>
            </div>
            {entry.change_reason && (
              <p className="text-sm text-gray-700 mt-2">{entry.change_reason}</p>
            )}
            <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
              <span>
                {entry.is_auto_calculated ? 'Auto-calculated' : 'Manual evaluation'}
                {entry.evaluated_by && ` by ${entry.evaluated_by.first_name} ${entry.evaluated_by.last_name}`}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
