'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../services/enterpriseApi';
import { FinancialReport } from '../../../../types/enterprise';

const FinancialReportViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const reportId = parseInt(params.id as string);
  
  const [report, setReport] = useState<FinancialReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (reportId) {
      fetchReport();
    }
  }, [reportId]);

  const fetchReport = async () => {
    try {
      setLoading(true);
      const response = await financialApi.getFinancialReport(reportId);
      setReport(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch financial report');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this financial report?')) return;
    
    try {
      await financialApi.deleteFinancialReport(reportId);
      router.push('/enterprise/financial/reports');
    } catch (err: any) {
      setError(err.message || 'Failed to delete financial report');
    }
  };

  const handleDownload = () => {
    if (report?.file_path) {
      window.open(report.file_path, '_blank');
    }
  };

  if (loading) return <div className="p-6">Loading financial report...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!report) return <div className="p-6">Financial report not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Financial Report Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/financial/reports')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Reports
          </button>
          {report.status === 'draft' && (
            <button
              onClick={() => router.push(`/enterprise/financial/reports/${reportId}/edit`)}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Edit Report
            </button>
          )}
          {report.file_path && (
            <button
              onClick={handleDownload}
              className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
            >
              Download Report
            </button>
          )}
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Report
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{report.report_name}</h2>
              <p className="text-sm text-gray-600">Report Code: {report.report_code}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                report.status === 'completed' 
                  ? 'bg-green-100 text-green-800'
                  : report.status === 'processing'
                  ? 'bg-yellow-100 text-yellow-800'
                  : report.status === 'failed'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {report.status}
              </span>
            </div>
          </div>
        </div>

        {/* Report Information */}
        <div className="px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Report Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Report Type</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{report.report_type.replace('_', ' ')}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Format</label>
              <p className="mt-1 text-sm text-gray-900 uppercase">{report.format}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Currency</label>
              <p className="mt-1 text-sm text-gray-900">{report.currency_code}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Period Start</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(report.period_start).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Period End</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(report.period_end).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Fiscal Year</label>
              <p className="mt-1 text-sm text-gray-900">{report.fiscal_year}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Fiscal Period</label>
              <p className="mt-1 text-sm text-gray-900">{report.fiscal_period}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">Generated At</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(report.generated_at).toLocaleString()}
              </p>
            </div>

            {report.file_size && (
              <div>
                <label className="block text-sm font-medium text-gray-700">File Size</label>
                <p className="mt-1 text-sm text-gray-900">
                  {(report.file_size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            )}
          </div>

          {report.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <p className="mt-1 text-sm text-gray-900">{report.description}</p>
            </div>
          )}
        </div>

        {/* Report Configuration */}
        <div className="px-6 py-4 border-t border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Report Configuration</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {report.sort_order && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Sort Order</label>
                <p className="mt-1 text-sm text-gray-900">{report.sort_order}</p>
              </div>
            )}
            
            {report.grouping && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Grouping</label>
                <p className="mt-1 text-sm text-gray-900">{report.grouping}</p>
              </div>
            )}

            {report.template_id && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Template ID</label>
                <p className="mt-1 text-sm text-gray-900">{report.template_id}</p>
              </div>
            )}
          </div>

          {report.parameters && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Parameters</label>
              <pre className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded overflow-x-auto">
                {JSON.stringify(JSON.parse(report.parameters), null, 2)}
              </pre>
            </div>
          )}

          {report.filters && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Filters</label>
              <pre className="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded overflow-x-auto">
                {JSON.stringify(JSON.parse(report.filters), null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Generation Information */}
        {(report.generated_by || report.reviewed_by) && (
          <div className="px-6 py-4 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Generation Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {report.generated_by && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Generated By</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {report.generated_by.first_name} {report.generated_by.last_name}
                  </p>
                </div>
              )}
              
              {report.reviewed_by && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reviewed By</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {report.reviewed_by.first_name} {report.reviewed_by.last_name}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* File Information */}
        {report.file_path && (
          <div className="px-6 py-4 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">File Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">File Path</label>
                <p className="mt-1 text-sm text-gray-900 break-all">{report.file_path}</p>
              </div>
              
              {report.file_hash && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">File Hash</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono">{report.file_hash}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(report.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(report.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FinancialReportViewPage;
