version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: federal_register_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: federal_register_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - federal_register_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (optional, for caching)
  redis:
    image: redis:7-alpine
    container_name: federal_register_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - federal_register_network
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Go Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: federal_register_backend
    restart: unless-stopped
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=federal_register_db
      - DB_SSLMODE=disable
      - SERVER_PORT=8080
      - SERVER_HOST=0.0.0.0
      - GIN_MODE=release
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - JWT_EXPIRY_HOURS=24
      - JWT_REFRESH_EXPIRY_HOURS=168
      - UPLOAD_PATH=/app/uploads
      - MAX_FILE_SIZE=50MB
      - ALLOWED_FILE_TYPES=pdf,doc,docx,txt
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
      - LOG_LEVEL=info
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - ENVIRONMENT=production
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - federal_register_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=http://127.0.0.1:8080
        - NEXT_PUBLIC_APP_NAME=Federal Register Clone
        - NEXT_PUBLIC_ENVIRONMENT=production
    container_name: federal_register_frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://127.0.0.1:8080
      - NEXT_PUBLIC_APP_NAME=Federal Register Clone
      - NEXT_PUBLIC_ENVIRONMENT=production
    ports:
      - "3000:3000"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - federal_register_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: federal_register_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - ./docker/ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    networks:
      - federal_register_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local

networks:
  federal_register_network:
    driver: bridge
