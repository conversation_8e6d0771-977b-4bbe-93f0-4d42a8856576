import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import SearchFilter, { FilterOption } from '../SearchFilter';

const mockFilterOptions: FilterOption[] = [
  {
    key: 'status',
    label: 'Status',
    type: 'select',
    options: [
      { value: 'active', label: 'Active' },
      { value: 'inactive', label: 'Inactive' },
      { value: 'pending', label: 'Pending' }
    ]
  },
  {
    key: 'category',
    label: 'Category',
    type: 'select',
    options: [
      { value: 'type1', label: 'Type 1' },
      { value: 'type2', label: 'Type 2' }
    ]
  },
  {
    key: 'priority',
    label: 'Priority',
    type: 'number',
    placeholder: 'Enter priority level'
  },
  {
    key: 'created_date',
    label: 'Created Date',
    type: 'daterange'
  },
  {
    key: 'tags',
    label: 'Tags',
    type: 'text',
    placeholder: 'Enter tags'
  }
];

describe('SearchFilter Component', () => {
  const defaultProps = {
    searchValue: '',
    onSearchChange: jest.fn(),
    filters: mockFilterOptions,
    filterValues: {},
    onFilterChange: jest.fn(),
    placeholder: 'Search...'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders search input correctly', () => {
    render(<SearchFilter {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    expect(searchInput).toBeInTheDocument();
    expect(searchInput).toHaveValue('');
  });

  it('handles search input changes', () => {
    render(<SearchFilter {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(defaultProps.onSearchChange).toHaveBeenCalledWith('test search');
  });

  it('renders select filters correctly', () => {
    render(<SearchFilter {...defaultProps} />);

    // Check if status select is rendered
    expect(screen.getByLabelText('Status')).toBeInTheDocument();
    
    // Check if category select is rendered
    expect(screen.getByLabelText('Category')).toBeInTheDocument();
  });

  it('handles select filter changes', () => {
    render(<SearchFilter {...defaultProps} />);

    const statusSelect = screen.getByLabelText('Status');
    fireEvent.change(statusSelect, { target: { value: 'active' } });

    expect(defaultProps.onFilterChange).toHaveBeenCalledWith({ status: 'active' });
  });

  it('renders number input filters correctly', () => {
    render(<SearchFilter {...defaultProps} />);

    const priorityInput = screen.getByPlaceholderText('Enter priority level');
    expect(priorityInput).toBeInTheDocument();
    expect(priorityInput).toHaveAttribute('type', 'number');
  });

  it('handles number input changes', () => {
    render(<SearchFilter {...defaultProps} />);

    const priorityInput = screen.getByPlaceholderText('Enter priority level');
    fireEvent.change(priorityInput, { target: { value: '5' } });

    expect(defaultProps.onFilterChange).toHaveBeenCalledWith({ priority: '5' });
  });

  it('renders text input filters correctly', () => {
    render(<SearchFilter {...defaultProps} />);

    const tagsInput = screen.getByPlaceholderText('Enter tags');
    expect(tagsInput).toBeInTheDocument();
    expect(tagsInput).toHaveAttribute('type', 'text');
  });

  it('handles text input changes', () => {
    render(<SearchFilter {...defaultProps} />);

    const tagsInput = screen.getByPlaceholderText('Enter tags');
    fireEvent.change(tagsInput, { target: { value: 'tag1, tag2' } });

    expect(defaultProps.onFilterChange).toHaveBeenCalledWith({ tags: 'tag1, tag2' });
  });

  it('renders date range filters correctly', () => {
    render(<SearchFilter {...defaultProps} />);

    // Check for date range inputs
    const dateInputs = screen.getAllByDisplayValue('');
    const dateRangeInputs = dateInputs.filter(input => 
      input.getAttribute('type') === 'date'
    );
    
    expect(dateRangeInputs.length).toBeGreaterThanOrEqual(2);
  });

  it('handles date range changes', () => {
    render(<SearchFilter {...defaultProps} />);

    // Find date inputs by their labels or container
    const createdDateContainer = screen.getByText('Created Date').closest('div');
    const dateInputs = createdDateContainer?.querySelectorAll('input[type="date"]');
    
    if (dateInputs && dateInputs.length >= 2) {
      fireEvent.change(dateInputs[0], { target: { value: '2024-01-01' } });
      
      expect(defaultProps.onFilterChange).toHaveBeenCalledWith({ 
        created_date_start: '2024-01-01' 
      });
    }
  });

  it('displays current filter values correctly', () => {
    const propsWithValues = {
      ...defaultProps,
      filterValues: {
        status: 'active',
        priority: '3',
        tags: 'important'
      }
    };

    render(<SearchFilter {...propsWithValues} />);

    const statusSelect = screen.getByLabelText('Status') as HTMLSelectElement;
    const priorityInput = screen.getByPlaceholderText('Enter priority level') as HTMLInputElement;
    const tagsInput = screen.getByPlaceholderText('Enter tags') as HTMLInputElement;

    expect(statusSelect.value).toBe('active');
    expect(priorityInput.value).toBe('3');
    expect(tagsInput.value).toBe('important');
  });

  it('handles clear filters functionality', () => {
    const propsWithValues = {
      ...defaultProps,
      filterValues: {
        status: 'active',
        priority: '3'
      }
    };

    render(<SearchFilter {...propsWithValues} />);

    // Look for clear button (if implemented)
    const clearButton = screen.queryByText('Clear Filters');
    if (clearButton) {
      fireEvent.click(clearButton);
      expect(defaultProps.onFilterChange).toHaveBeenCalledWith({});
    }
  });

  it('handles search value prop correctly', () => {
    const propsWithSearch = {
      ...defaultProps,
      searchValue: 'existing search'
    };

    render(<SearchFilter {...propsWithSearch} />);

    const searchInput = screen.getByPlaceholderText('Search...') as HTMLInputElement;
    expect(searchInput.value).toBe('existing search');
  });

  it('renders filter labels correctly', () => {
    render(<SearchFilter {...defaultProps} />);

    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Category')).toBeInTheDocument();
    expect(screen.getByText('Priority')).toBeInTheDocument();
    expect(screen.getByText('Created Date')).toBeInTheDocument();
    expect(screen.getByText('Tags')).toBeInTheDocument();
  });

  it('handles empty filter options gracefully', () => {
    const propsWithEmptyFilters = {
      ...defaultProps,
      filters: []
    };

    render(<SearchFilter {...propsWithEmptyFilters} />);

    // Should still render search input
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    
    // Should not render any filter controls
    expect(screen.queryByText('Status')).not.toBeInTheDocument();
  });

  it('applies correct CSS classes', () => {
    render(<SearchFilter {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    expect(searchInput).toHaveClass('w-full');
    
    // Check for proper styling classes
    const container = searchInput.closest('div');
    expect(container).toHaveClass('relative');
  });

  it('handles multiple filter changes in sequence', () => {
    render(<SearchFilter {...defaultProps} />);

    const statusSelect = screen.getByLabelText('Status');
    const priorityInput = screen.getByPlaceholderText('Enter priority level');

    fireEvent.change(statusSelect, { target: { value: 'active' } });
    fireEvent.change(priorityInput, { target: { value: '2' } });

    expect(defaultProps.onFilterChange).toHaveBeenCalledTimes(2);
    expect(defaultProps.onFilterChange).toHaveBeenNthCalledWith(1, { status: 'active' });
    expect(defaultProps.onFilterChange).toHaveBeenNthCalledWith(2, { priority: '2' });
  });

  it('handles debounced search input', async () => {
    render(<SearchFilter {...defaultProps} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    
    // Type multiple characters quickly
    fireEvent.change(searchInput, { target: { value: 't' } });
    fireEvent.change(searchInput, { target: { value: 'te' } });
    fireEvent.change(searchInput, { target: { value: 'test' } });

    // Should call onSearchChange for each change (or debounced if implemented)
    expect(defaultProps.onSearchChange).toHaveBeenCalled();
  });
});
