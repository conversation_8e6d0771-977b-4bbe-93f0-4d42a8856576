package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

var authToken string

func main() {
	fmt.Println("🚀 Testing 404 Endpoints")
	fmt.Println("========================")

	// Step 1: Authenticate
	if !authenticate() {
		fmt.Println("❌ Authentication failed. Exiting.")
		return
	}

	// Test category and agency document endpoints (should exist)
	endpoints := []string{
		"/categories/1/documents",
		"/agencies/1/documents",
	}

	fmt.Printf("\n📋 Testing %d endpoints...\n", len(endpoints))

	working := 0
	notFound := 0
	errors := 0

	for _, endpoint := range endpoints {
		status := testEndpoint(endpoint)
		switch {
		case status >= 200 && status < 300:
			fmt.Printf("  ✅ %s: %d\n", endpoint, status)
			working++
		case status == 404:
			fmt.Printf("  ❌ %s: 404 (Not Found)\n", endpoint)
			notFound++
		default:
			fmt.Printf("  ⚠️  %s: %d\n", endpoint, status)
			errors++
		}
	}

	fmt.Printf("\n📊 Summary:\n")
	fmt.Printf("  ✅ Working: %d\n", working)
	fmt.Printf("  ❌ 404 Not Found: %d\n", notFound)
	fmt.Printf("  ⚠️  Other Errors: %d\n", errors)
}

func authenticate() bool {
	loginData := map[string]string{
		"identifier": "<EMAIL>",
		"password":   "password",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := http.Post("http://127.0.0.1:8080/api/v1/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Login error: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		fmt.Printf("Login failed: %d - %s\n", resp.StatusCode, string(body))
		return false
	}

	var loginResponse map[string]interface{}
	json.Unmarshal(body, &loginResponse)

	if token, ok := loginResponse["token"].(string); ok {
		authToken = token
		fmt.Println("✅ Authentication successful")
		return true
	}

	fmt.Println("❌ No token in login response")
	return false
}

func testEndpoint(endpoint string) int {
	req, _ := http.NewRequest("GET", "http://127.0.0.1:8080/api/v1"+endpoint, nil)
	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return 0 // Connection error
	}
	defer resp.Body.Close()

	return resp.StatusCode
}
