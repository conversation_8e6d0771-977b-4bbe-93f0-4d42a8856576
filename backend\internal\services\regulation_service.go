package services

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

type RegulationService struct {
	db *gorm.DB
}

func NewRegulationService(db *gorm.DB) *RegulationService {
	return &RegulationService{db: db}
}

// CreateRegulation creates a new regulation with initial chunks
func (s *RegulationService) CreateRegulation(regulation *models.LawsAndRules, initialChunks []CreateChunkRequest) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Create the regulation
		if err := tx.Create(regulation).Error; err != nil {
			return fmt.Errorf("failed to create regulation: %w", err)
		}

		// Create initial document version
		docVersion := &models.RegulationDocumentVersion{
			LawRuleID:     regulation.ID,
			VersionNumber: "1.0.0",
			IsOfficial:    false,
			CreatedByID:   regulation.CreatedByID,
			Notes:         "Initial version",
		}
		if err := tx.Create(docVersion).Error; err != nil {
			return fmt.Errorf("failed to create initial document version: %w", err)
		}

		// Create initial chunks if provided
		if len(initialChunks) > 0 {
			if err := s.createChunksWithContent(tx, regulation.ID, initialChunks, regulation.CreatedByID); err != nil {
				return fmt.Errorf("failed to create initial chunks: %w", err)
			}
		}

		// Update regulation with current document version
		regulation.CurrentDocumentVersionID = &docVersion.ID
		if err := tx.Save(regulation).Error; err != nil {
			return fmt.Errorf("failed to update regulation with current version: %w", err)
		}

		return nil
	})
}

// GetRegulation retrieves a regulation with its current chunks
func (s *RegulationService) GetRegulation(id uint) (*models.LawsAndRules, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var regulation models.LawsAndRules
	err := s.db.Preload("Agency").
		Preload("CreatedBy").
		Preload("CurrentDocumentVersion").
		Preload("CurrentDocumentVersion.CreatedBy").
		First(&regulation, id).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("regulation not found")
		}
		return nil, fmt.Errorf("failed to get regulation: %w", err)
	}

	return &regulation, nil
}

// GetRegulationWithChunks retrieves a regulation with its hierarchical chunk structure
func (s *RegulationService) GetRegulationWithChunks(id uint, versionID *uint) (*RegulationWithChunks, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	// Get the regulation
	regulation, err := s.GetRegulation(id)
	if err != nil {
		return nil, err
	}

	// Determine which version to use
	var targetVersionID uint
	if versionID != nil {
		targetVersionID = *versionID
	} else if regulation.CurrentDocumentVersionID != nil {
		targetVersionID = *regulation.CurrentDocumentVersionID
	} else {
		return &RegulationWithChunks{
			Regulation: *regulation,
			Chunks:     []ChunkWithContent{},
		}, nil
	}

	// Get chunks for the specific version
	chunks, err := s.getChunksForVersion(targetVersionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get chunks for version: %w", err)
	}

	return &RegulationWithChunks{
		Regulation: *regulation,
		Chunks:     chunks,
		VersionID:  targetVersionID,
	}, nil
}

// AmendChunk creates a new version of a chunk (amendment process)
func (s *RegulationService) AmendChunk(chunkID uint, newContent string, userID uint, changeDescription string) (*models.ChunkContentVersion, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var result *models.ChunkContentVersion
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Get the chunk
		var chunk models.Chunk
		if err := tx.First(&chunk, chunkID).Error; err != nil {
			return fmt.Errorf("chunk not found: %w", err)
		}

		// Get the latest version number for this chunk
		var latestVersion models.ChunkContentVersion
		err := tx.Where("chunk_id = ?", chunkID).
			Order("version_number DESC").
			First(&latestVersion).Error

		newVersionNumber := 1
		if err == nil {
			newVersionNumber = latestVersion.VersionNumber + 1
		}

		// Mark current version as not current
		if err := tx.Model(&models.ChunkContentVersion{}).
			Where("chunk_id = ? AND is_current = true", chunkID).
			Update("is_current", false).Error; err != nil {
			return fmt.Errorf("failed to update current version flag: %w", err)
		}

		// Create new content version
		newContentVersion := &models.ChunkContentVersion{
			ChunkID:           chunkID,
			Content:           newContent,
			VersionNumber:     newVersionNumber,
			IsCurrent:         true,
			IsActive:          true,
			ModifiedByID:      userID,
			ChangeDescription: changeDescription,
		}

		if err := tx.Create(newContentVersion).Error; err != nil {
			return fmt.Errorf("failed to create new content version: %w", err)
		}

		// Update chunk's current content version
		if err := tx.Model(&chunk).Update("current_chunk_content_version_id", newContentVersion.ID).Error; err != nil {
			return fmt.Errorf("failed to update chunk current version: %w", err)
		}

		result = newContentVersion
		return nil
	})

	if err != nil {
		return nil, err
	}
	return result, nil
}

// UpdateChunkContent updates the current content of a chunk (simple update)
func (s *RegulationService) UpdateChunkContent(chunkID uint, newContent string, userID uint) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		// Get the chunk
		var chunk models.Chunk
		if err := tx.First(&chunk, chunkID).Error; err != nil {
			return fmt.Errorf("chunk not found: %w", err)
		}

		// Get the current content version
		var currentVersion models.ChunkContentVersion
		if err := tx.Where("chunk_id = ? AND is_current = true", chunkID).
			First(&currentVersion).Error; err != nil {
			return fmt.Errorf("current content version not found: %w", err)
		}

		// Update the current content version
		currentVersion.Content = newContent
		currentVersion.ModifiedByID = userID
		currentVersion.ChangeDescription = "Content updated"

		if err := tx.Save(&currentVersion).Error; err != nil {
			return fmt.Errorf("failed to update content version: %w", err)
		}

		return nil
	})
}

// CreateNewDocumentVersion creates a new official version of the regulation
func (s *RegulationService) CreateNewDocumentVersion(lawRuleID uint, userID uint, notes string, isOfficial bool) (*models.RegulationDocumentVersion, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var result *models.RegulationDocumentVersion
	err := s.db.Transaction(func(tx *gorm.DB) error {
		// Get current version to determine new version number
		var currentVersion models.RegulationDocumentVersion
		err := tx.Where("law_rule_id = ?", lawRuleID).
			Order("created_at DESC").
			First(&currentVersion).Error

		newVersionNumber := "1.0.0"
		if err == nil {
			newVersionNumber = s.incrementVersion(currentVersion.VersionNumber, isOfficial)
		}

		// Create new document version
		newDocVersion := &models.RegulationDocumentVersion{
			LawRuleID:     lawRuleID,
			VersionNumber: newVersionNumber,
			IsOfficial:    isOfficial,
			CreatedByID:   userID,
			Notes:         notes,
		}

		if isOfficial {
			now := time.Now()
			newDocVersion.PublicationDate = &now
			newDocVersion.EffectiveDate = &now
		}

		if err := tx.Create(newDocVersion).Error; err != nil {
			return fmt.Errorf("failed to create document version: %w", err)
		}

		// Create chunk mappings for all current chunks
		if err := s.createChunkMappingsForVersion(tx, newDocVersion.ID, lawRuleID); err != nil {
			return fmt.Errorf("failed to create chunk mappings: %w", err)
		}

		// Update regulation's current version if this is official
		if isOfficial {
			if err := tx.Model(&models.LawsAndRules{}).
				Where("id = ?", lawRuleID).
				Update("current_document_version_id", newDocVersion.ID).Error; err != nil {
				return fmt.Errorf("failed to update regulation current version: %w", err)
			}
		}

		result = newDocVersion
		return nil
	})

	if err != nil {
		return nil, err
	}
	return result, nil
}

// GetRegulationVersions returns all versions of a regulation
func (s *RegulationService) GetRegulationVersions(lawRuleID uint) ([]models.RegulationDocumentVersion, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var versions []models.RegulationDocumentVersion
	err := s.db.Where("law_rule_id = ?", lawRuleID).
		Preload("CreatedBy").
		Order("created_at DESC").
		Find(&versions).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get regulation versions: %w", err)
	}

	return versions, nil
}

// GetPublishedRegulations returns all published regulations (for /regulation page)
func (s *RegulationService) GetPublishedRegulations(filters RegulationFilters) (*RegulationSearchResult, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	query := s.db.Model(&models.LawsAndRules{}).
		Preload("Agency").
		Preload("CurrentDocumentVersion").
		Where("status IN ?", []models.RegulationStatus{
			models.RegulationStatusPublished,
			models.RegulationStatusEffective,
		})

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.AgencyID != 0 {
		query = query.Where("agency_id = ?", filters.AgencyID)
	}
	if filters.Search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}
	if filters.EffectiveAfter != nil {
		query = query.Where("effective_date >= ?", filters.EffectiveAfter)
	}
	if filters.EffectiveBefore != nil {
		query = query.Where("effective_date <= ?", filters.EffectiveBefore)
	}

	// Count total
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count regulations: %w", err)
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.PerPage
	query = query.Offset(offset).Limit(filters.PerPage)

	// Apply sorting
	switch filters.SortBy {
	case "title":
		query = query.Order("title " + filters.SortOrder)
	case "effective_date":
		query = query.Order("effective_date " + filters.SortOrder)
	case "agency":
		query = query.Joins("JOIN agencies ON agencies.id = laws_and_rules.agency_id").
			Order("agencies.name " + filters.SortOrder)
	default:
		query = query.Order("effective_date DESC")
	}

	var regulations []models.LawsAndRules
	if err := query.Find(&regulations).Error; err != nil {
		return nil, fmt.Errorf("failed to get regulations: %w", err)
	}

	return &RegulationSearchResult{
		Regulations: regulations,
		Total:       int(total),
		Page:        filters.Page,
		PerPage:     filters.PerPage,
		TotalPages:  int((total + int64(filters.PerPage) - 1) / int64(filters.PerPage)),
	}, nil
}

// Helper types and functions

type CreateChunkRequest struct {
	ChunkType       models.ChunkType `json:"chunk_type"`
	ChunkIdentifier string           `json:"chunk_identifier"`
	Number          string           `json:"number"`
	Title           string           `json:"title"`
	Content         string           `json:"content"`
	ParentChunkID   *uint            `json:"parent_chunk_id"`
	OrderInParent   int              `json:"order_in_parent"`
}

type ChunkWithContent struct {
	models.Chunk
	CurrentContent *models.ChunkContentVersion `json:"current_content"`
	Children       []ChunkWithContent          `json:"children"`
}

// Internal structure for building hierarchy with pointers
type chunkNode struct {
	chunk    ChunkWithContent
	children []*chunkNode
}

// convertChunkToValue recursively converts pointer-based chunks to value-based chunks
func convertNodeToChunk(node *chunkNode) ChunkWithContent {
	result := node.chunk
	result.Children = make([]ChunkWithContent, len(node.children))

	for i, child := range node.children {
		result.Children[i] = convertNodeToChunk(child)
	}

	return result
}

type RegulationWithChunks struct {
	Regulation models.LawsAndRules `json:"regulation"`
	Chunks     []ChunkWithContent  `json:"chunks"`
	VersionID  uint                `json:"version_id"`
}

type RegulationFilters struct {
	Type            string     `json:"type"`
	Status          string     `json:"status"`
	AgencyID        uint       `json:"agency_id"`
	Search          string     `json:"search"`
	EffectiveAfter  *time.Time `json:"effective_after"`
	EffectiveBefore *time.Time `json:"effective_before"`
	Page            int        `json:"page"`
	PerPage         int        `json:"per_page"`
	SortBy          string     `json:"sort_by"`
	SortOrder       string     `json:"sort_order"`
}

type RegulationSearchResult struct {
	Regulations []models.LawsAndRules `json:"regulations"`
	Total       int                   `json:"total"`
	Page        int                   `json:"page"`
	PerPage     int                   `json:"per_page"`
	TotalPages  int                   `json:"total_pages"`
}

// Helper functions

func (s *RegulationService) incrementVersion(currentVersion string, isMajor bool) string {
	parts := strings.Split(currentVersion, ".")
	if len(parts) != 3 {
		return "1.0.0"
	}

	major, _ := strconv.Atoi(parts[0])
	minor, _ := strconv.Atoi(parts[1])
	patch, _ := strconv.Atoi(parts[2])

	if isMajor {
		major++
		minor = 0
		patch = 0
	} else {
		minor++
		patch = 0
	}

	return fmt.Sprintf("%d.%d.%d", major, minor, patch)
}

func (s *RegulationService) createChunksWithContent(tx *gorm.DB, lawRuleID uint, chunks []CreateChunkRequest, userID uint) error {
	for _, chunkReq := range chunks {
		// Create chunk
		chunk := &models.Chunk{
			LawRuleID:       lawRuleID,
			ParentChunkID:   chunkReq.ParentChunkID,
			OrderInParent:   chunkReq.OrderInParent,
			ChunkType:       chunkReq.ChunkType,
			ChunkIdentifier: chunkReq.ChunkIdentifier,
			Number:          chunkReq.Number,
			Title:           chunkReq.Title,
		}

		if err := tx.Create(chunk).Error; err != nil {
			return fmt.Errorf("failed to create chunk: %w", err)
		}

		// Create initial content version
		contentVersion := &models.ChunkContentVersion{
			ChunkID:           chunk.ID,
			Content:           chunkReq.Content,
			VersionNumber:     1,
			IsCurrent:         true,
			IsActive:          true,
			ModifiedByID:      userID,
			ChangeDescription: "Initial version",
		}

		if err := tx.Create(contentVersion).Error; err != nil {
			return fmt.Errorf("failed to create chunk content version: %w", err)
		}

		// Update chunk with current content version
		chunk.CurrentChunkContentVersionID = &contentVersion.ID
		if err := tx.Save(chunk).Error; err != nil {
			return fmt.Errorf("failed to update chunk with current content version: %w", err)
		}
	}

	return nil
}

func (s *RegulationService) createChunkMappingsForVersion(tx *gorm.DB, versionID uint, lawRuleID uint) error {
	// Get all chunks for this regulation with their current content versions
	var chunks []models.Chunk
	err := tx.Where("law_rule_id = ?", lawRuleID).
		Preload("CurrentChunkContentVersion").
		Find(&chunks).Error
	if err != nil {
		return fmt.Errorf("failed to get chunks: %w", err)
	}

	// Create mappings
	for _, chunk := range chunks {
		if chunk.CurrentChunkContentVersionID != nil {
			mapping := &models.RegulationDocumentVersionChunkMap{
				DocumentVersionID:     versionID,
				ChunkID:               chunk.ID,
				ChunkContentVersionID: *chunk.CurrentChunkContentVersionID,
			}
			if err := tx.Create(mapping).Error; err != nil {
				return fmt.Errorf("failed to create chunk mapping: %w", err)
			}
		}
	}

	return nil
}

func (s *RegulationService) getChunksForVersion(versionID uint) ([]ChunkWithContent, error) {
	// First get the regulation ID from the version
	var docVersion models.RegulationDocumentVersion
	err := s.db.First(&docVersion, versionID).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get document version: %w", err)
	}

	// Get all chunks for this regulation
	var allChunks []models.Chunk
	err = s.db.Where("law_rule_id = ?", docVersion.LawRuleID).
		Preload("CurrentChunkContentVersion").
		Order("parent_chunk_id ASC, order_in_parent ASC").
		Find(&allChunks).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get chunks: %w", err)
	}

	// Get chunk mappings for this version to know which content versions to use
	var mappings []models.RegulationDocumentVersionChunkMap
	err = s.db.Where("document_version_id = ?", versionID).
		Preload("ChunkContentVersion").
		Find(&mappings).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get chunk mappings: %w", err)
	}

	// Create a map of chunk ID to content version for this document version
	contentVersionMap := make(map[uint]*models.ChunkContentVersion)
	for _, mapping := range mappings {
		contentVersionMap[mapping.ChunkID] = mapping.ChunkContentVersion
	}

	// Build hierarchical structure using internal node structure
	nodeMap := make(map[uint]*chunkNode)
	var rootNodes []*chunkNode

	// First pass: create all nodes
	for _, chunk := range allChunks {
		// Use the content version from the mapping if available, otherwise use current content
		var contentVersion *models.ChunkContentVersion
		if mappingContent, exists := contentVersionMap[chunk.ID]; exists {
			contentVersion = mappingContent
		} else {
			contentVersion = chunk.CurrentChunkContentVersion
		}

		node := &chunkNode{
			chunk: ChunkWithContent{
				Chunk:          chunk,
				CurrentContent: contentVersion,
				Children:       []ChunkWithContent{}, // Will be populated in conversion
			},
			children: []*chunkNode{},
		}
		nodeMap[chunk.ID] = node
	}

	// Second pass: build hierarchy
	for _, chunk := range allChunks {
		node := nodeMap[chunk.ID]
		if chunk.ParentChunkID != nil {
			if parent, exists := nodeMap[*chunk.ParentChunkID]; exists {
				parent.children = append(parent.children, node)
			}
		} else {
			rootNodes = append(rootNodes, node)
		}
	}

	// Convert to value slices for return
	var result []ChunkWithContent
	for _, rootNode := range rootNodes {
		result = append(result, convertNodeToChunk(rootNode))
	}

	return result, nil
}

// GetPublicRegulations returns published/effective regulations for public access
func (s *RegulationService) GetPublicRegulations(filters RegulationFilters) ([]models.LawsAndRules, int64, error) {
	var regulations []models.LawsAndRules
	var total int64

	// Build base query with public status filter
	query := s.db.Model(&models.LawsAndRules{}).
		Preload("Agency").
		Preload("CreatedBy").
		Preload("CurrentDocumentVersion").
		Preload("CurrentDocumentVersion.CreatedBy").
		Where("status IN ?", []models.RegulationStatus{
			models.RegulationStatusPublished,
			models.RegulationStatusEffective,
		})

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.AgencyID > 0 {
		query = query.Where("agency_id = ?", filters.AgencyID)
	}
	if filters.Search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}
	if filters.EffectiveAfter != nil {
		query = query.Where("effective_date >= ?", filters.EffectiveAfter)
	}
	if filters.EffectiveBefore != nil {
		query = query.Where("effective_date <= ?", filters.EffectiveBefore)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count public regulations: %w", err)
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.PerPage
	query = query.Offset(offset).Limit(filters.PerPage)

	// Apply sorting
	sortBy := filters.SortBy
	sortOrder := filters.SortOrder
	if sortBy == "" {
		sortBy = "effective_date"
	}
	if sortOrder == "" {
		sortOrder = "desc"
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Execute query
	err := query.Find(&regulations).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to fetch public regulations: %w", err)
	}

	return regulations, total, nil
}

// GetRegulations returns a list of regulations based on filters (for authenticated users)
func (s *RegulationService) GetRegulations(filters RegulationFilters) ([]models.LawsAndRules, int64, error) {
	var regulations []models.LawsAndRules
	var total int64

	// Build base query
	query := s.db.Model(&models.LawsAndRules{}).
		Preload("Agency").
		Preload("CreatedBy").
		Preload("CurrentDocumentVersion")

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.AgencyID > 0 {
		query = query.Where("agency_id = ?", filters.AgencyID)
	}
	if filters.Search != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}
	if filters.EffectiveAfter != nil {
		query = query.Where("effective_date >= ?", filters.EffectiveAfter)
	}
	if filters.EffectiveBefore != nil {
		query = query.Where("effective_date <= ?", filters.EffectiveBefore)
	}

	// Get total count
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count regulations: %w", err)
	}

	// Apply sorting
	sortBy := "effective_date"
	if filters.SortBy != "" {
		sortBy = filters.SortBy
	}
	sortOrder := "DESC"
	if filters.SortOrder == "asc" {
		sortOrder = "ASC"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	offset := (filters.Page - 1) * filters.PerPage
	query = query.Offset(offset).Limit(filters.PerPage)

	// Execute query
	err = query.Find(&regulations).Error
	if err != nil {
		return nil, 0, fmt.Errorf("failed to fetch regulations: %w", err)
	}

	return regulations, total, nil
}

// GetRegulationsWithHierarchy returns all regulations with their hierarchical chunk structure
func (s *RegulationService) GetRegulationsWithHierarchy(filters RegulationFilters) ([]RegulationWithChunks, error) {
	var regulations []models.LawsAndRules

	// Build base query
	query := s.db.Model(&models.LawsAndRules{}).
		Preload("Agency").
		Preload("CreatedBy").
		Preload("CurrentDocumentVersion").
		Preload("CurrentDocumentVersion.CreatedBy")

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.AgencyID > 0 {
		query = query.Where("agency_id = ?", filters.AgencyID)
	}
	if filters.Search != "" {
		searchTerm := "%" + filters.Search + "%"
		query = query.Where("title ILIKE ? OR description ILIKE ? OR content ILIKE ?",
			searchTerm, searchTerm, searchTerm)
	}

	// Apply sorting
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := filters.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Execute query
	if err := query.Find(&regulations).Error; err != nil {
		return nil, fmt.Errorf("failed to fetch regulations: %w", err)
	}

	// Convert each regulation to RegulationWithChunks
	var result []RegulationWithChunks
	for _, regulation := range regulations {
		regulationWithChunks, err := s.GetRegulationWithChunks(regulation.ID, nil)
		if err != nil {
			// Log error but continue with other regulations
			continue
		}
		result = append(result, *regulationWithChunks)
	}

	return result, nil
}

// GetRegulationByID returns a regulation by its ID
func (s *RegulationService) GetRegulationByID(id uint) (*models.LawsAndRules, error) {
	var regulation models.LawsAndRules
	err := s.db.Preload("Agency").
		Preload("CreatedBy").
		Preload("CurrentDocumentVersion").
		Preload("CurrentDocumentVersion.CreatedBy").
		First(&regulation, id).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get regulation: %w", err)
	}
	return &regulation, nil
}

// UpdateRegulation updates an existing regulation
func (s *RegulationService) UpdateRegulation(regulation *models.LawsAndRules) error {
	err := s.db.Save(regulation).Error
	if err != nil {
		return fmt.Errorf("failed to update regulation: %w", err)
	}
	return nil
}

// DeleteRegulation soft deletes a regulation
func (s *RegulationService) DeleteRegulation(id uint) error {
	err := s.db.Delete(&models.LawsAndRules{}, id).Error
	if err != nil {
		return fmt.Errorf("failed to delete regulation: %w", err)
	}
	return nil
}

// Relationship management methods

// CreateDocumentRelationship creates a relationship between a regulation and a document
func (s *RegulationService) CreateDocumentRelationship(relationship *models.RegulationDocumentRelationship) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Validate that regulation exists
	var regulation models.LawsAndRules
	if err := s.db.First(&regulation, relationship.RegulationID).Error; err != nil {
		return fmt.Errorf("regulation not found: %w", err)
	}

	// Validate that document exists
	var document models.Document
	if err := s.db.First(&document, relationship.DocumentID).Error; err != nil {
		return fmt.Errorf("document not found: %w", err)
	}

	// Check for duplicate active relationship
	var existing models.RegulationDocumentRelationship
	err := s.db.Where("regulation_id = ? AND document_id = ? AND relationship_type = ? AND is_active = ?",
		relationship.RegulationID, relationship.DocumentID, relationship.RelationshipType, true).
		First(&existing).Error
	if err == nil {
		return errors.New("active relationship of this type already exists")
	}

	// Create the relationship
	if err := s.db.Create(relationship).Error; err != nil {
		return fmt.Errorf("failed to create document relationship: %w", err)
	}

	return nil
}

// CreateAgencyRelationship creates a relationship between a regulation and an agency
func (s *RegulationService) CreateAgencyRelationship(relationship *models.RegulationAgencyRelationship) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Validate that regulation exists
	var regulation models.LawsAndRules
	if err := s.db.First(&regulation, relationship.RegulationID).Error; err != nil {
		return fmt.Errorf("regulation not found: %w", err)
	}

	// Validate that agency exists
	var agency models.Agency
	if err := s.db.First(&agency, relationship.AgencyID).Error; err != nil {
		return fmt.Errorf("agency not found: %w", err)
	}

	// Check for duplicate active relationship
	var existing models.RegulationAgencyRelationship
	err := s.db.Where("regulation_id = ? AND agency_id = ? AND relationship_type = ? AND is_active = ?",
		relationship.RegulationID, relationship.AgencyID, relationship.RelationshipType, true).
		First(&existing).Error
	if err == nil {
		return errors.New("active relationship of this type already exists")
	}

	// Create the relationship
	if err := s.db.Create(relationship).Error; err != nil {
		return fmt.Errorf("failed to create agency relationship: %w", err)
	}

	return nil
}

// CreateCategoryRelationship creates a relationship between a regulation and a category
func (s *RegulationService) CreateCategoryRelationship(relationship *models.RegulationCategoryRelationship) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	// Validate that regulation exists
	var regulation models.LawsAndRules
	if err := s.db.First(&regulation, relationship.RegulationID).Error; err != nil {
		return fmt.Errorf("regulation not found: %w", err)
	}

	// Validate that category exists
	var category models.Category
	if err := s.db.First(&category, relationship.CategoryID).Error; err != nil {
		return fmt.Errorf("category not found: %w", err)
	}

	// Check for duplicate active relationship
	var existing models.RegulationCategoryRelationship
	err := s.db.Where("regulation_id = ? AND category_id = ? AND relationship_type = ? AND is_active = ?",
		relationship.RegulationID, relationship.CategoryID, relationship.RelationshipType, true).
		First(&existing).Error
	if err == nil {
		return errors.New("active relationship of this type already exists")
	}

	// Create the relationship
	if err := s.db.Create(relationship).Error; err != nil {
		return fmt.Errorf("failed to create category relationship: %w", err)
	}

	return nil
}

// GetRegulationWithRelationships retrieves a regulation with all its relationships
func (s *RegulationService) GetRegulationWithRelationships(id uint) (*models.LawsAndRules, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	var regulation models.LawsAndRules
	err := s.db.Preload("Agency").
		Preload("CreatedBy").
		Preload("CurrentDocumentVersion").
		Preload("CurrentDocumentVersion.CreatedBy").
		Preload("DocumentRelationships").
		Preload("DocumentRelationships.Document").
		Preload("DocumentRelationships.Document.Agency").
		Preload("AgencyRelationships").
		Preload("AgencyRelationships.Agency").
		Preload("CategoryRelationships").
		Preload("CategoryRelationships.Category").
		First(&regulation, id).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("regulation not found")
		}
		return nil, fmt.Errorf("failed to get regulation with relationships: %w", err)
	}

	return &regulation, nil
}

// UpdateRelationshipStatus updates the active status of relationships
func (s *RegulationService) UpdateRelationshipStatus(relationshipType string, relationshipID uint, isActive bool) error {
	if s.db == nil {
		return errors.New("database not initialized")
	}

	switch relationshipType {
	case "document":
		return s.db.Model(&models.RegulationDocumentRelationship{}).
			Where("id = ?", relationshipID).
			Update("is_active", isActive).Error
	case "agency":
		return s.db.Model(&models.RegulationAgencyRelationship{}).
			Where("id = ?", relationshipID).
			Update("is_active", isActive).Error
	case "category":
		return s.db.Model(&models.RegulationCategoryRelationship{}).
			Where("id = ?", relationshipID).
			Update("is_active", isActive).Error
	default:
		return errors.New("invalid relationship type")
	}
}

// ValidateRelationshipDates ensures effective date is before termination date
func (s *RegulationService) ValidateRelationshipDates(effectiveDate, terminationDate *time.Time) error {
	if effectiveDate != nil && terminationDate != nil {
		if effectiveDate.After(*terminationDate) {
			return errors.New("effective date cannot be after termination date")
		}
	}
	return nil
}

// GetRelationshipStats returns statistics about regulation relationships
func (s *RegulationService) GetRelationshipStats() (map[string]interface{}, error) {
	if s.db == nil {
		return nil, errors.New("database not initialized")
	}

	stats := make(map[string]interface{})

	// Count document relationships by type
	var documentStats []struct {
		RelationshipType string `json:"relationship_type"`
		Count            int64  `json:"count"`
	}
	s.db.Model(&models.RegulationDocumentRelationship{}).
		Select("relationship_type, count(*) as count").
		Where("is_active = ?", true).
		Group("relationship_type").
		Find(&documentStats)

	// Count agency relationships by type
	var agencyStats []struct {
		RelationshipType string `json:"relationship_type"`
		Count            int64  `json:"count"`
	}
	s.db.Model(&models.RegulationAgencyRelationship{}).
		Select("relationship_type, count(*) as count").
		Where("is_active = ?", true).
		Group("relationship_type").
		Find(&agencyStats)

	// Count category relationships by type
	var categoryStats []struct {
		RelationshipType string `json:"relationship_type"`
		Count            int64  `json:"count"`
	}
	s.db.Model(&models.RegulationCategoryRelationship{}).
		Select("relationship_type, count(*) as count").
		Where("is_active = ?", true).
		Group("relationship_type").
		Find(&categoryStats)

	// Total counts
	var totalDocumentRelationships int64
	var totalAgencyRelationships int64
	var totalCategoryRelationships int64

	s.db.Model(&models.RegulationDocumentRelationship{}).Where("is_active = ?", true).Count(&totalDocumentRelationships)
	s.db.Model(&models.RegulationAgencyRelationship{}).Where("is_active = ?", true).Count(&totalAgencyRelationships)
	s.db.Model(&models.RegulationCategoryRelationship{}).Where("is_active = ?", true).Count(&totalCategoryRelationships)

	stats["document_relationships"] = map[string]interface{}{
		"by_type": documentStats,
		"total":   totalDocumentRelationships,
	}
	stats["agency_relationships"] = map[string]interface{}{
		"by_type": agencyStats,
		"total":   totalAgencyRelationships,
	}
	stats["category_relationships"] = map[string]interface{}{
		"by_type": categoryStats,
		"total":   totalCategoryRelationships,
	}
	stats["overall_total"] = totalDocumentRelationships + totalAgencyRelationships + totalCategoryRelationships

	return stats, nil
}
