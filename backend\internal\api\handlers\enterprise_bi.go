package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// DataWarehouseRequest represents the request structure for data warehouses
type DataWarehouseRequest struct {
	WarehouseName    string  `json:"warehouse_name" binding:"required"`
	Description      string  `json:"description"`
	DatabaseType     string  `json:"database_type" binding:"required"`
	ConnectionString string  `json:"connection_string" binding:"required"`
	StorageCapacity  uint64  `json:"storage_capacity"`
	UsedStorage      uint64  `json:"used_storage"`
	IsActive         bool    `json:"is_active"`
	QueryCount       uint64  `json:"query_count"`
	AvgQueryTime     float64 `json:"avg_query_time"`
	Metadata         string  `json:"metadata"`
}

// DataSourceRequest represents the request structure for data sources
type DataSourceRequest struct {
	SourceName       string `json:"source_name" binding:"required"`
	Description      string `json:"description"`
	SourceType       string `json:"source_type" binding:"required"`
	ConnectionString string `json:"connection_string" binding:"required"`
	AuthMethod       string `json:"auth_method"`
	Credentials      string `json:"credentials"`
	Schema           string `json:"schema"`
	TableMapping     string `json:"table_mapping"`
	FieldMapping     string `json:"field_mapping"`
	IsActive         bool   `json:"is_active"`
	SyncFrequency    string `json:"sync_frequency"`
	Metadata         string `json:"metadata"`
}

// DashboardRequest represents the request structure for dashboards
type DashboardRequest struct {
	DashboardName string `json:"dashboard_name" binding:"required"`
	Description   string `json:"description"`
	Category      string `json:"category"`
	Layout        string `json:"layout"`
	Widgets       string `json:"widgets"`
	Filters       string `json:"filters"`
	IsPublic      bool   `json:"is_public"`
	ShareToken    string `json:"share_token"`
	AccessList    string `json:"access_list"`
	IsActive      bool   `json:"is_active"`
	Metadata      string `json:"metadata"`
}

// ReportRequest represents the request structure for BI reports
type ReportRequest struct {
	ReportName     string `json:"report_name" binding:"required"`
	Description    string `json:"description"`
	ReportType     string `json:"report_type" binding:"required"`
	Query          string `json:"query"`
	DataSources    string `json:"data_sources"`
	Parameters     string `json:"parameters"`
	OutputFormat   string `json:"output_format"`
	Template       string `json:"template"`
	Visualization  string `json:"visualization"`
	IsScheduled    bool   `json:"is_scheduled"`
	ScheduleConfig string `json:"schedule_config"`
	IsActive       bool   `json:"is_active"`
	Metadata       string `json:"metadata"`
}

// KPIRequest represents the request structure for KPIs
type KPIRequest struct {
	KPIName         string  `json:"kpi_name" binding:"required"`
	Description     string  `json:"description"`
	Category        string  `json:"category"`
	Formula         string  `json:"formula"`
	DataSource      string  `json:"data_source"`
	Unit            string  `json:"unit"`
	TargetValue     float64 `json:"target_value"`
	MinThreshold    float64 `json:"min_threshold"`
	MaxThreshold    float64 `json:"max_threshold"`
	CriticalMin     float64 `json:"critical_min"`
	CriticalMax     float64 `json:"critical_max"`
	CurrentValue    float64 `json:"current_value"`
	UpdateFrequency string  `json:"update_frequency"`
	OwnerID         uint    `json:"owner_id" binding:"required"`
	IsActive        bool    `json:"is_active"`
	Metadata        string  `json:"metadata"`
}

// DataMiningModelRequest represents the request structure for data mining models
type DataMiningModelRequest struct {
	ModelName      string  `json:"model_name" binding:"required"`
	Description    string  `json:"description"`
	ModelType      string  `json:"model_type" binding:"required"`
	Algorithm      string  `json:"algorithm"`
	AnalyticsType  string  `json:"analytics_type"`
	Parameters     string  `json:"parameters"`
	Features       string  `json:"features"`
	TrainingData   string  `json:"training_data"`
	Accuracy       float64 `json:"accuracy"`
	Precision      float64 `json:"precision"`
	Recall         float64 `json:"recall"`
	F1Score        float64 `json:"f1_score"`
	ModelPath      string  `json:"model_path"`
	ModelVersion   string  `json:"model_version"`
	RetrainingFreq string  `json:"retraining_frequency"`
	CreatedByID    uint    `json:"created_by_id" binding:"required"`
	Status         string  `json:"status"`
	Metadata       string  `json:"metadata"`
}

// Data Warehouse Handlers

// GetDataWarehouses returns all data warehouses with pagination
func GetDataWarehouses(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total warehouses
	var total int64
	db.Model(&models.DataWarehouse{}).Count(&total)

	// Get warehouses with pagination
	var warehouses []models.DataWarehouse
	offset := (page - 1) * perPage
	if err := db.Offset(offset).
		Limit(perPage).
		Order("warehouse_name ASC").
		Find(&warehouses).Error; err != nil {
		HandleInternalError(c, "Failed to fetch data warehouses: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"warehouses": warehouses,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateDataWarehouse creates a new data warehouse
func CreateDataWarehouse(c *gin.Context) {
	var req DataWarehouseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create data warehouse
	warehouse := models.DataWarehouse{
		WarehouseName:    req.WarehouseName,
		Description:      req.Description,
		DatabaseType:     req.DatabaseType,
		ConnectionString: req.ConnectionString,
		StorageSize:      req.StorageCapacity,
		IsActive:         req.IsActive,
		QueryCount:       req.QueryCount,
		AvgQueryTime:     req.AvgQueryTime,
		Metadata:         req.Metadata,
	}

	if err := db.Create(&warehouse).Error; err != nil {
		HandleInternalError(c, "Failed to create data warehouse: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, gin.H{"warehouse": warehouse})
}

// Data Source Handlers

// GetDataSources returns all data sources with pagination
func GetDataSources(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total data sources
	var total int64
	db.Model(&models.DataSource{}).Count(&total)

	// Get data sources with pagination
	var dataSources []models.DataSource
	offset := (page - 1) * perPage
	if err := db.Offset(offset).
		Limit(perPage).
		Order("source_name ASC").
		Find(&dataSources).Error; err != nil {
		HandleInternalError(c, "Failed to fetch data sources: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"data_sources": dataSources,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateDataSource creates a new data source
func CreateDataSource(c *gin.Context) {
	var req DataSourceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create data source
	dataSource := models.DataSource{
		SourceName:       req.SourceName,
		Description:      req.Description,
		SourceType:       models.DataSourceType(req.SourceType),
		ConnectionString: req.ConnectionString,
		AuthMethod:       req.AuthMethod,
		Credentials:      req.Credentials,
		Schema:           req.Schema,
		TableMapping:     req.TableMapping,
		FieldMapping:     req.FieldMapping,
		IsActive:         req.IsActive,
		SyncFrequency:    req.SyncFrequency,
		Metadata:         req.Metadata,
	}

	if err := db.Create(&dataSource).Error; err != nil {
		HandleInternalError(c, "Failed to create data source: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, gin.H{"data_source": dataSource})
}

// SyncDataSource synchronizes a data source
func SyncDataSource(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid data source ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var dataSource models.DataSource
	if err := db.First(&dataSource, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Data source not found"})
		return
	}

	// Update last sync time
	now := time.Now()
	dataSource.LastSyncAt = &now

	if err := db.Save(&dataSource).Error; err != nil {
		HandleInternalError(c, "Failed to sync data source: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"data_source": dataSource, "message": "Data source synchronized successfully"})
}

// Dashboard Handlers

// GetDashboards returns all dashboards with pagination
func GetDashboards(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total dashboards
	var total int64
	db.Model(&models.Dashboard{}).Count(&total)

	// Get dashboards with pagination
	var dashboards []models.Dashboard
	offset := (page - 1) * perPage
	if err := db.Preload("Owner").
		Offset(offset).
		Limit(perPage).
		Order("dashboard_name ASC").
		Find(&dashboards).Error; err != nil {
		HandleInternalError(c, "Failed to fetch dashboards: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"dashboards": dashboards,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateDashboard creates a new dashboard
func CreateDashboard(c *gin.Context) {
	var req DashboardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Create dashboard
	dashboard := models.Dashboard{
		DashboardName: req.DashboardName,
		Description:   req.Description,
		Category:      req.Category,
		Layout:        req.Layout,
		Widgets:       req.Widgets,
		Filters:       req.Filters,
		IsPublic:      req.IsPublic,
		ShareToken:    req.ShareToken,
		AccessList:    req.AccessList,
		IsActive:      req.IsActive,
		OwnerID:       userID.(uint),
		Metadata:      req.Metadata,
	}

	if err := db.Create(&dashboard).Error; err != nil {
		HandleInternalError(c, "Failed to create dashboard: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Owner").First(&dashboard, dashboard.ID)

	c.JSON(http.StatusCreated, gin.H{"dashboard": dashboard})
}

// GetDashboard returns a specific dashboard
func GetDashboard(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid dashboard ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var dashboard models.Dashboard
	if err := db.Preload("Owner").First(&dashboard, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Dashboard not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"dashboard": dashboard})
}

// RefreshDashboard refreshes a dashboard's data
func RefreshDashboard(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid dashboard ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var dashboard models.Dashboard
	if err := db.First(&dashboard, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Dashboard not found"})
		return
	}

	// Update last refresh time
	now := time.Now()
	dashboard.LastRefreshed = &now

	if err := db.Save(&dashboard).Error; err != nil {
		HandleInternalError(c, "Failed to refresh dashboard: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Owner").First(&dashboard, dashboard.ID)

	c.JSON(http.StatusOK, gin.H{"dashboard": dashboard, "message": "Dashboard refreshed successfully"})
}

// Report Handlers

// GetReports returns all BI reports with pagination
func GetReports(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total reports
	var total int64
	db.Model(&models.Report{}).Count(&total)

	// Get reports with pagination
	var reports []models.Report
	offset := (page - 1) * perPage
	if err := db.Preload("CreatedBy").
		Offset(offset).
		Limit(perPage).
		Order("report_name ASC").
		Find(&reports).Error; err != nil {
		HandleInternalError(c, "Failed to fetch BI reports: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"reports": reports,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}
