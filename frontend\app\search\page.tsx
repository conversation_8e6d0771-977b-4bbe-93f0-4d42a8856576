'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  CalendarIcon,
  ClockIcon,
  EyeIcon,
  AdjustmentsHorizontalIcon,
  ClipboardDocumentListIcon,
  DocumentDuplicateIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import apiService from '../services/api';

interface SearchResult {
  id: number;
  type: 'document' | 'agency' | 'category' | 'regulation' | 'task' | 'calendar_event' | 'user' | 'proceeding';
  title: string;
  description?: string;
  summary?: string;
  content?: string;
  agency?: { name: string; id: number };
  category?: { name: string; id: number };
  publication_date?: string;
  created_at: string;
  updated_at: string;
  relevance_score?: number;
  highlights?: string[];
  entity_type?: string;
  status?: string;
  priority?: string;
  event_type?: string;
  role?: string;
  unique_id?: string;
}

interface SearchFilters {
  type: string;
  agency_id: string;
  category_id: string;
  date_from: string;
  date_to: string;
  sort: string;
  order: string;
}

const SearchPage: React.FC = () => {
  const searchParams = useSearchParams();
  const [query, setQuery] = useState(searchParams?.get('q') || '');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [agencies, setAgencies] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<SearchFilters>({
    type: '',
    agency_id: '',
    category_id: '',
    date_from: '',
    date_to: '',
    sort: 'relevance',
    order: 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    total_pages: 0
  });
  const [searchTime, setSearchTime] = useState(0);

  const performSearch = async () => {
    if (!query.trim()) return;

    try {
      setLoading(true);
      setError('');
      const startTime = Date.now();

      const params = {
        q: query,
        page: pagination.page,
        per_page: pagination.per_page,
        ...filters
      };

      const response = await apiService.search(params);
      setResults(Array.isArray(response.data?.results) ? response.data.results : []);
      setPagination({
        page: response.page || pagination.page,
        per_page: response.per_page || pagination.per_page,
        total: response.data?.total || 0,
        total_pages: response.total_pages || Math.ceil((response.data?.total || 0) / pagination.per_page)
      });
      
      const endTime = Date.now();
      setSearchTime((endTime - startTime) / 1000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Search failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const fetchAgencies = async () => {
    try {
      const response = await apiService.getPublicAgencies({ per_page: 100 });
      setAgencies(response.data);
    } catch (err: any) {
      console.error('Failed to fetch agencies:', err);
    }
  };

  useEffect(() => {
    if (query) {
      performSearch();
    }
  }, [pagination.page, filters]);

  useEffect(() => {
    const initialQuery = searchParams?.get('q');
    if (initialQuery) {
      setQuery(initialQuery);
      performSearch();
    }
    fetchAgencies();
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    performSearch();
  };

  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <DocumentTextIcon className="h-5 w-5 text-blue-600" />;
      case 'agency':
        return <BuildingOfficeIcon className="h-5 w-5 text-purple-600" />;
      case 'category':
        return <TagIcon className="h-5 w-5 text-green-600" />;
      case 'regulation':
        return <DocumentTextIcon className="h-5 w-5 text-orange-600" />;
      case 'task':
        return <ClipboardDocumentListIcon className="h-5 w-5 text-orange-600" />;
      case 'calendar_event':
        return <CalendarIcon className="h-5 w-5 text-indigo-600" />;
      case 'proceeding':
        return <DocumentDuplicateIcon className="h-5 w-5 text-pink-600" />;
      case 'user':
        return <UserIcon className="h-5 w-5 text-gray-600" />;
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getResultLink = (result: SearchResult) => {
    switch (result.type) {
      case 'document':
        return `/documents/${parseInt(result.id.toString())}`;
      case 'agency':
        return `/agencies/${parseInt(result.id.toString())}`;
      case 'category':
        return `/categories/${parseInt(result.id.toString())}`;
      case 'regulation':
        return `/regulations/${parseInt(result.id.toString())}`;
      case 'task':
        return `/tasks/${parseInt(result.id.toString())}`;
      case 'calendar_event':
        return `/calendar`;
      case 'proceeding':
        return `/proceedings/${parseInt(result.id.toString())}`;
      case 'user':
        return `/admin/users/${parseInt(result.id.toString())}`;
      default:
        return '#';
    }
  };

  const highlightText = (text: string, highlights?: string[]) => {
    if (!highlights || highlights.length === 0) return text;
    
    let highlightedText = text;
    highlights.forEach(highlight => {
      const regex = new RegExp(`(${highlight})`, 'gi');
      highlightedText = highlightedText.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
    });
    
    return <span dangerouslySetInnerHTML={{ __html: highlightedText }} />;
  };

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Search</h1>
          <p className="text-gray-600">
            Search across documents, agencies, categories, and regulations
          </p>
        </div>

        {/* Search Form */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <form onSubmit={handleSearch} className="mb-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search for documents, agencies, regulations, tasks, calendar events, proceedings..."
                    value={query}
                    onChange={(e) => setQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-lg"
                  />
                </div>
              </div>
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-3 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {loading ? 'Searching...' : 'Search'}
              </button>
              <button
                type="button"
                onClick={() => setShowFilters(!showFilters)}
                className="px-4 py-3 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <AdjustmentsHorizontalIcon className="h-5 w-5" />
              </button>
            </div>
          </form>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="border-t border-gray-200 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <select
                  value={filters.type}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">All Types</option>
                  <option value="documents">Documents</option>
                  <option value="agencies">Agencies</option>
                  <option value="categories">Categories</option>
                  <option value="regulations">Regulations</option>
                  <option value="tasks">Tasks</option>
                  <option value="calendar">Calendar Events</option>
                  <option value="proceedings">Proceedings</option>
                  <option value="users">Users</option>
                </select>

                <select
                  value={filters.agency_id}
                  onChange={(e) => handleFilterChange('agency_id', e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">All Agencies</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id.toString()}>
                      {agency.short_name || agency.name}
                    </option>
                  ))}
                </select>

                <input
                  type="date"
                  value={filters.date_from}
                  onChange={(e) => handleFilterChange('date_from', e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="From Date"
                />

                <input
                  type="date"
                  value={filters.date_to}
                  onChange={(e) => handleFilterChange('date_to', e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="To Date"
                />

                <select
                  value={filters.sort}
                  onChange={(e) => handleFilterChange('sort', e.target.value)}
                  className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="relevance">Relevance</option>
                  <option value="date">Date</option>
                  <option value="title">Title</option>
                  <option value="agency">Agency</option>
                </select>
              </div>
            </div>
          )}
        </div>

        {/* Search Results */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {query && !loading && (
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <p className="text-gray-600">
                {pagination.total > 0 ? (
                  <>
                    About {pagination.total.toLocaleString()} results for "{query}" 
                    ({searchTime.toFixed(2)} seconds)
                  </>
                ) : (
                  `No results found for "${query}"`
                )}
              </p>
            </div>
          </div>
        )}

        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4 w-3/4"></div>
                <div className="flex space-x-4">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (results || []).length === 0 && query ? (
          <div className="text-center py-12">
            <MagnifyingGlassIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No results found</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your search terms or filters.
            </p>
            <div className="text-sm text-gray-500">
              <p>Search tips:</p>
              <ul className="mt-2 space-y-1">
                <li>• Try different keywords</li>
                <li>• Check your spelling</li>
                <li>• Use broader search terms</li>
                <li>• Remove filters to see more results</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {(results || []).map((result) => (
              <div key={`${result.type}-${result.id}`} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      {getResultIcon(result.type)}
                      <span className="text-sm font-medium text-gray-500 capitalize">
                        {result.type}
                      </span>
                      {result.relevance_score && (
                        <span className="text-xs text-gray-400">
                          ({Math.round(result.relevance_score * 100)}% match)
                        </span>
                      )}
                    </div>
                    <Link
                      href={getResultLink(result)}
                      className="p-2 text-gray-400 hover:text-primary-600 transition-colors"
                    >
                      <EyeIcon className="h-4 w-4" />
                    </Link>
                  </div>
                  
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    <Link 
                      href={getResultLink(result)}
                      className="hover:text-primary-600 transition-colors"
                    >
                      {highlightText(result.title, result.highlights)}
                    </Link>
                  </h3>
                  
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {highlightText(
                      result.description || result.summary || result.content || 'No description available',
                      result.highlights
                    )}
                  </p>
                  
                  <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                    {result.agency && (
                      <span className="flex items-center">
                        <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                        <Link 
                          href={`/agencies/${result.agency.id}`}
                          className="hover:text-primary-600"
                        >
                          {result.agency.name}
                        </Link>
                      </span>
                    )}
                    {result.category && (
                      <span className="flex items-center">
                        <TagIcon className="h-4 w-4 mr-1" />
                        <Link 
                          href={`/categories/${result.category.id}`}
                          className="hover:text-primary-600"
                        >
                          {result.category.name}
                        </Link>
                      </span>
                    )}
                    {result.publication_date && (
                      <span className="flex items-center">
                        <CalendarIcon className="h-4 w-4 mr-1" />
                        Published: {new Date(result.publication_date).toLocaleDateString()}
                      </span>
                    )}
                    <span className="flex items-center">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      Updated: {new Date(result.updated_at).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.total_pages > 1 && (
          <div className="flex items-center justify-between mt-8">
            <div className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.per_page) + 1} to{' '}
              {Math.min(pagination.page * pagination.per_page, pagination.total)} of{' '}
              {pagination.total} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-2 text-sm text-gray-700">
                Page {pagination.page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === pagination.total_pages}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SearchPage;
