'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  Check<PERSON>ircleIcon,
  ClockIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  UserIcon,
  FlagIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';
import { Task } from '../types';

const TasksPage: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    type: '',
    assigned_to_id: '',
    sort: 'created_at',
    order: 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    total_pages: 0
  });

  const fetchTasks = async () => {
    try {
      setLoading(true);
      const params: any = {
        page: pagination.page,
        per_page: pagination.per_page,
        search: searchTerm,
      };

      // Convert filter values to appropriate types
      if (filters.status) params.status = filters.status;
      if (filters.priority) params.priority = filters.priority;
      if (filters.type) params.type = filters.type;
      if (filters.assigned_to_id) params.assigned_to_id = parseInt(filters.assigned_to_id);
      if (filters.sort) params.sort = filters.sort;
      if (filters.order) params.order = filters.order;

      const response = await apiService.getTasks(params);
      setTasks(response.data);
      setPagination({
        page: response.page,
        per_page: response.per_page,
        total: response.total,
        total_pages: response.total_pages
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch tasks');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) {
      window.location.href = '/login';
      return;
    }
    fetchTasks();
  }, [pagination.page, searchTerm, filters, isAuthenticated]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    fetchTasks();
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this task?')) return;
    
    try {
      await apiService.deleteTask(id);
      fetchTasks();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete task');
    }
  };

  const handleCompleteTask = async (id: number) => {
    try {
      await apiService.completeTask(id);
      fetchTasks();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to complete task');
    }
  };

  const canEdit = (task: Task) => {
    if (!user) return false;
    return user.role === 'admin' || task.created_by_id === user.id || task.assigned_to_id === user.id;
  };

  const canDelete = (task: Task) => {
    if (!user) return false;
    return user.role === 'admin' || task.created_by_id === user.id;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Tasks</h1>
            <p className="text-gray-600">
              Manage your tasks and track progress
            </p>
          </div>
          <Link
            href="/tasks/new"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Task
          </Link>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <form onSubmit={handleSearch} className="mb-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search tasks..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  />
                </div>
              </div>
              <button
                type="submit"
                className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                Search
              </button>
            </div>
          </form>

          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Status</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>

            <select
              value={filters.priority}
              onChange={(e) => handleFilterChange('priority', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Priority</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Types</option>
              <option value="document_review">Document Review</option>
              <option value="meeting">Meeting</option>
              <option value="deadline">Deadline</option>
              <option value="reminder">Reminder</option>
              <option value="other">Other</option>
            </select>

            <select
              value={filters.sort}
              onChange={(e) => handleFilterChange('sort', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="created_at">Created Date</option>
              <option value="due_date">Due Date</option>
              <option value="priority">Priority</option>
              <option value="title">Title</option>
            </select>

            <select
              value={filters.order}
              onChange={(e) => handleFilterChange('order', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="desc">Newest First</option>
              <option value="asc">Oldest First</option>
            </select>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Tasks List */}
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4 w-3/4"></div>
                <div className="flex space-x-4">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                  <div className="h-4 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            ))}
          </div>
        ) : tasks.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircleIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm ? 'Try adjusting your search terms or filters.' : 'Get started by creating your first task.'}
            </p>
            <Link
              href="/tasks/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Task
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {tasks.map((task) => (
              <div key={task.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2 flex-wrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                          {task.status?.replace('_', ' ').toUpperCase()}
                        </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                          <FlagIcon className="h-3 w-3 mr-1" />
                          {task.priority?.toUpperCase()}
                        </span>
                        {task.type && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            {task.type.replace('_', ' ').toUpperCase()}
                          </span>
                        )}
                        {task.due_date && isOverdue(task.due_date) && task.status !== 'completed' && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                            OVERDUE
                          </span>
                        )}
                        {task.is_all_day && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            ALL DAY
                          </span>
                        )}
                        {task.is_recurring && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            RECURRING
                          </span>
                        )}
                        {task.parsed_from_text && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            AUTO-GENERATED
                          </span>
                        )}
                        {!task.is_public && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            PRIVATE
                          </span>
                        )}
                      </div>
                      
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        <Link 
                          href={`/tasks/${task.id}`}
                          className="hover:text-primary-600 transition-colors"
                        >
                          {task.title}
                        </Link>
                      </h3>
                      
                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {task.description || 'No description available'}
                      </p>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                        <div className="space-y-1">
                          {task.due_date && (
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              <span>Due: {new Date(task.due_date).toLocaleDateString()}</span>
                              {task.due_time && <span className="ml-1">at {task.due_time}</span>}
                            </div>
                          )}
                          {task.start_date && (
                            <div className="flex items-center">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              <span>Start: {new Date(task.start_date).toLocaleDateString()}</span>
                              {task.start_time && <span className="ml-1">at {task.start_time}</span>}
                            </div>
                          )}
                          {task.assigned_to && (
                            <div className="flex items-center">
                              <UserIcon className="h-4 w-4 mr-1" />
                              <span>{task.assigned_to.first_name} {task.assigned_to.last_name}</span>
                            </div>
                          )}
                          {task.created_by && (
                            <div className="flex items-center">
                              <UserIcon className="h-4 w-4 mr-1" />
                              <span>Created by: {task.created_by.first_name} {task.created_by.last_name}</span>
                            </div>
                          )}
                        </div>
                        <div className="space-y-1">
                          {task.document_id && (
                            <div className="text-xs">
                              <span className="font-medium">Document:</span>
                              <Link href={`/documents/${parseInt(task.document_id.toString())}`} className="text-primary-600 hover:text-primary-700 ml-1">
                                View Document
                              </Link>
                            </div>
                          )}
                          {task.agency_id && (
                            <div className="text-xs">
                              <span className="font-medium">Agency:</span>
                              <Link href={`/agencies/${task.agency_id}`} className="text-primary-600 hover:text-primary-700 ml-1">
                                View Agency
                              </Link>
                            </div>
                          )}
                          {task.category_id && (
                            <div className="text-xs">
                              <span className="font-medium">Category:</span>
                              <Link href={`/categories/${task.category_id}`} className="text-primary-600 hover:text-primary-700 ml-1">
                                View Category
                              </Link>
                            </div>
                          )}
                          {task.location && (
                            <div className="text-xs">
                              <span className="font-medium">Location:</span> {task.location}
                            </div>
                          )}
                          <div className="flex items-center text-xs">
                            <ClockIcon className="h-3 w-3 mr-1" />
                            <span>Created: {new Date(task.created_at).toLocaleDateString()}</span>
                          </div>
                          {task.completed_at && (
                            <div className="flex items-center text-xs text-green-600">
                              <CheckCircleIcon className="h-3 w-3 mr-1" />
                              <span>Completed: {new Date(task.completed_at).toLocaleDateString()}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Additional metadata */}
                      {(task.tags || task.reminder_minutes || task.recurrence_pattern || task.parsed_from_text) && (
                        <div className="mt-3 pt-3 border-t border-gray-100">
                          <div className="flex flex-wrap items-center gap-2 text-xs text-gray-400">
                            {task.reminder_minutes && (
                              <span>Reminder: {task.reminder_minutes} min before</span>
                            )}
                            {task.recurrence_pattern && (
                              <span>Recurs: {task.recurrence_pattern}</span>
                            )}
                            {task.parsed_from_text && (
                              <span>Source: "{typeof task.parsed_from_text === 'string' ? task.parsed_from_text.substring(0, 50) : 'Parsed from text'}..."</span>
                            )}
                          </div>
                          {task.tags && (
                            <div className="mt-2">
                              <span className="text-xs text-gray-400 mr-2">Tags:</span>
                              {(Array.isArray(task.tags) ? task.tags : task.tags.split(',')).map((tag, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 mr-1">
                                  {tag.trim()}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <Link
                        href={`/tasks/${task.id}`}
                        className="p-2 text-gray-400 hover:text-primary-600 transition-colors"
                        title="View Task"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </Link>
                      {canEdit(task) && (
                        <Link
                          href={`/tasks/${task.id}/edit`}
                          className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit Task"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </Link>
                      )}
                      {task.status !== 'completed' && canEdit(task) && (
                        <button
                          onClick={() => handleCompleteTask(task.id)}
                          className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                          title="Mark as Complete"
                        >
                          <CheckCircleIcon className="h-5 w-5" />
                        </button>
                      )}
                      {canDelete(task) && (
                        <button
                          onClick={() => handleDelete(task.id)}
                          className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete Task"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.total_pages > 1 && (
          <div className="flex items-center justify-between mt-8">
            <div className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.per_page) + 1} to{' '}
              {Math.min(pagination.page * pagination.per_page, pagination.total)} of{' '}
              {pagination.total} results
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-2 text-sm text-gray-700">
                Page {pagination.page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === pagination.total_pages}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default TasksPage;
