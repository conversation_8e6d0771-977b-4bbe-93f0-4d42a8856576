# Federal Register Clone - Document Management Service

A comprehensive document management service inspired by the Federal Register, built with Go backend, TypeScript frontend, and PostgreSQL database.

## Features

### Core Features (Similar to Federal Register)
- **Document Management**: Upload, store, and manage government documents
- **Document Types**: Rules, Proposed Rules, Notices, Presidential Documents
- **Agency Management**: Organize documents by government agencies
- **Advanced Search**: Full-text search with filters by date, agency, document type
- **Document Categories**: Categorize documents by subject matter
- **Public Inspection**: Preview documents before official publication
- **Document Metadata**: Rich metadata including CFR citations, effective dates
- **Document History**: Track document revisions and amendments

### Technical Features
- **RESTful API**: Comprehensive REST API for all operations
- **Authentication**: JWT-based authentication with role-based access
- **Full-text Search**: PostgreSQL full-text search with indexing
- **File Processing**: Support for PDF, DOC, TXT document formats
- **Responsive UI**: Modern React-based frontend
- **Real-time Updates**: WebSocket support for live document updates

## Architecture

```
federal-register-clone/
├── backend/                 # Go backend service
│   ├── cmd/                # Application entry points
│   ├── internal/           # Private application code
│   │   ├── api/           # HTTP handlers and routes
│   │   ├── auth/          # Authentication logic
│   │   ├── config/        # Configuration management
│   │   ├── database/      # Database connection and migrations
│   │   ├── models/        # Data models
│   │   ├── services/      # Business logic
│   │   └── utils/         # Utility functions
│   ├── migrations/        # Database migrations
│   ├── docs/             # API documentation
│   └── tests/            # Backend tests
├── frontend/              # TypeScript React frontend
│   ├── src/
│   │   ├── components/   # React components
│   │   ├── pages/        # Page components
│   │   ├── services/     # API service layer
│   │   ├── types/        # TypeScript type definitions
│   │   ├── utils/        # Utility functions
│   │   └── hooks/        # Custom React hooks
│   ├── public/           # Static assets
│   └── tests/            # Frontend tests
├── database/             # Database setup and seeds
├── docker/              # Docker configuration
└── docs/                # Project documentation
```

## Technology Stack

### Backend
- **Language**: Go 1.21+
- **Framework**: Gin (HTTP router)
- **Database**: PostgreSQL 15+
- **ORM**: GORM
- **Authentication**: JWT tokens
- **Documentation**: Swagger/OpenAPI
- **Testing**: Go testing package + Testify

### Frontend
- **Language**: TypeScript
- **Framework**: React 18 with Next.js 14
- **Styling**: Tailwind CSS
- **State Management**: Zustand
- **HTTP Client**: Axios
- **Testing**: Jest + React Testing Library
- **Build Tool**: Vite/Next.js

### Database
- **Primary**: PostgreSQL 15+
- **Search**: PostgreSQL Full-Text Search
- **Migrations**: GORM Auto-migrate + custom migrations

### DevOps
- **Containerization**: Docker & Docker Compose
- **Environment**: Development, Staging, Production configs
- **CI/CD**: GitHub Actions (ready)

## Quick Start

### Prerequisites
- Go 1.21+
- Node.js 18+
- PostgreSQL 15+
- Docker (optional)

### Development Setup

1. **Clone and setup**:
```bash
git clone <repository>
cd federal-register-clone
```

2. **Backend setup**:
```bash
cd backend
go mod init federal-register-clone
go mod tidy
cp .env.example .env
# Edit .env with your database credentials
go run cmd/server/main.go
```

3. **Frontend setup**:
```bash
cd frontend
npm install
cp .env.example .env.local
# Edit .env.local with API endpoints
npm run dev
```

4. **Database setup**:
```bash
# Create database
createdb federal_register_db
# Run migrations (automatic on first start)
```

### Docker Setup (Alternative)
```bash
docker-compose up -d
```

## API Endpoints

### Documents
- `GET /api/v1/documents` - List documents with pagination and filters
- `GET /api/v1/documents/:id` - Get document details
- `POST /api/v1/documents` - Create new document
- `PUT /api/v1/documents/:id` - Update document
- `DELETE /api/v1/documents/:id` - Delete document
- `GET /api/v1/documents/search` - Advanced search

### Agencies
- `GET /api/v1/agencies` - List all agencies
- `GET /api/v1/agencies/:id/documents` - Get documents by agency

### Categories
- `GET /api/v1/categories` - List all categories
- `GET /api/v1/categories/:id/documents` - Get documents by category

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh token

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
