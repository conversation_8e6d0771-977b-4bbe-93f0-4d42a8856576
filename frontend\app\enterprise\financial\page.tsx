'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { financialApi } from '../../services/enterpriseApi';

interface ChartOfAccount {
  id: number;
  account_code: string;
  account_name: string;
  account_type: string;
  current_balance: number;
  currency_code: string;
  is_active: boolean;
  level: number;
}

interface BudgetPlan {
  id: number;
  budget_code: string;
  budget_name: string;
  fiscal_year: number;
  planned_amount: number;
  actual_amount: number;
  variance_amount: number;
  variance_percent: number;
  status: string;
}

const FinancialManagementPage: React.FC = () => {
  const router = useRouter();
  const [accounts, setAccounts] = useState<ChartOfAccount[]>([]);
  const [budgets, setBudgets] = useState<BudgetPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('accounts');

  const tabs = [
    { id: 'accounts', name: 'Chart of Accounts', icon: '📊' },
    { id: 'ledger', name: 'General Ledger', icon: '📋' },
    { id: 'budgets', name: 'Budget Management', icon: '💰' },
    { id: 'reports', name: 'Financial Reports', icon: '📈' },
    { id: 'costcenters', name: 'Cost Centers', icon: '🏢' }
  ];

  useEffect(() => {
    fetchFinancialData();
  }, []);

  const fetchFinancialData = async () => {
    try {
      setLoading(true);
      
      // Fetch actual data from API
      const [accountsResponse, budgetsResponse] = await Promise.all([
        financialApi.getAccounts(),
        financialApi.getBudgets()
      ]);

      setAccounts(accountsResponse.data || []);
      setBudgets(budgetsResponse.data || []);
    } catch (err) {
      setError('Failed to load financial data');
      console.error('Error fetching financial data:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD'): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const getAccountTypeColor = (type: string): string => {
    const colors: { [key: string]: string } = {
      'asset': 'bg-green-100 text-green-800',
      'liability': 'bg-red-100 text-red-800',
      'equity': 'bg-blue-100 text-blue-800',
      'revenue': 'bg-purple-100 text-purple-800',
      'expense': 'bg-orange-100 text-orange-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  const getVarianceColor = (variance: number): string => {
    if (variance > 0) return 'text-green-600';
    if (variance < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const renderAccounts = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Chart of Accounts</h2>
        <button
          onClick={() => router.push('/enterprise/financial/accounts/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Add Account
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Account Code
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Account Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Current Balance
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {accounts.map((account) => (
              <tr key={account.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {account.account_code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {account.account_name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getAccountTypeColor(account.account_type)}`}>
                    {account.account_type.toUpperCase()}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatCurrency(account.current_balance, account.currency_code)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    account.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {account.is_active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button
                    onClick={() => router.push(`/enterprise/financial/accounts/${account.id}`)}
                    className="text-blue-600 hover:text-blue-900 mr-3"
                  >
                    View
                  </button>
                  <button
                    onClick={() => router.push(`/enterprise/financial/accounts/${account.id}/edit`)}
                    className="text-indigo-600 hover:text-indigo-900"
                  >
                    Edit
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderBudgets = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Budget Management</h2>
        <button
          onClick={() => router.push('/enterprise/financial/budgets/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Budget
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {budgets.map((budget) => (
          <div key={budget.id} className="bg-white rounded-lg shadow border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">{budget.budget_name}</h3>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                budget.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {budget.status.toUpperCase()}
              </span>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-500">Budget Code:</span>
                <span className="text-gray-900 font-medium">{budget.budget_code}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Fiscal Year:</span>
                <span className="text-gray-900">{budget.fiscal_year}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Planned:</span>
                <span className="text-gray-900">{formatCurrency(budget.planned_amount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Actual:</span>
                <span className="text-gray-900">{formatCurrency(budget.actual_amount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Variance:</span>
                <span className={`font-medium ${getVarianceColor(budget.variance_amount)}`}>
                  {formatCurrency(budget.variance_amount)} ({budget.variance_percent.toFixed(1)}%)
                </span>
              </div>
            </div>

            <div className="mt-4 bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${
                  budget.variance_percent >= 0 ? 'bg-green-600' : 'bg-red-600'
                }`}
                style={{ width: `${Math.min(Math.abs(budget.actual_amount / budget.planned_amount) * 100, 100)}%` }}
              ></div>
            </div>

            <div className="mt-4 flex space-x-2">
              <button
                onClick={() => router.push(`/enterprise/financial/budgets/${budget.id}`)}
                className="flex-1 bg-blue-50 text-blue-700 px-3 py-2 rounded-md text-sm hover:bg-blue-100 transition-colors"
              >
                View Details
              </button>
              <button
                onClick={() => router.push(`/enterprise/financial/budgets/${budget.id}/edit`)}
                className="flex-1 bg-gray-50 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-100 transition-colors"
              >
                Edit
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderLedger = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">General Ledger</h2>
        <button
          onClick={() => router.push('/enterprise/financial/ledger/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          New Entry
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">General ledger interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderReports = () => (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Financial Reports</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Balance Sheet</h3>
          <p className="text-gray-600 text-sm">Assets, liabilities, and equity statement</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Income Statement</h3>
          <p className="text-gray-600 text-sm">Revenue and expense summary</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6 cursor-pointer hover:shadow-lg transition-shadow">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Cash Flow</h3>
          <p className="text-gray-600 text-sm">Cash inflows and outflows</p>
        </div>
      </div>
    </div>
  );

  const renderCostCenters = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Cost Centers</h2>
        <button
          onClick={() => router.push('/enterprise/financial/costcenters/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          Create Cost Center
        </button>
      </div>
      <div className="bg-white rounded-lg shadow p-6">
        <p className="text-gray-600">Cost center management interface will be implemented here.</p>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'accounts':
        return renderAccounts();
      case 'ledger':
        return renderLedger();
      case 'budgets':
        return renderBudgets();
      case 'reports':
        return renderReports();
      case 'costcenters':
        return renderCostCenters();
      default:
        return renderAccounts();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Financial Management</h1>
          <p className="mt-2 text-gray-600">
            Manage chart of accounts, budgets, general ledger, and financial reporting
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {renderTabContent()}

        {/* Error Display */}
        {error && (
          <div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <span className="text-red-400">⚠️</span>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FinancialManagementPage;
