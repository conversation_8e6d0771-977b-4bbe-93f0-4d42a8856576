'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { complianceApi } from '../../../services/enterpriseApi';

interface ComplianceFinding {
  id: number;
  created_at: string;
  updated_at: string;
  finding_code: string;
  title: string;
  description?: string;
  assessment_id?: number;
  requirement_id?: number;
  severity: string;
  category: string;
  status: string;
  identified_date: string;
  due_date?: string;
  resolved_date?: string;
  assigned_to_id?: number;
  evidence?: string;
  impact?: string;
  recommendation?: string;
  remediation_plan?: string;
  cost_to_fix?: number;
  currency_code?: string;
  risk_rating?: string;
  compliance_gap?: string;
  root_cause?: string;
  recurrence_risk?: string;
  validation_method?: string;
  is_systemic?: boolean;
  metadata?: string;
}

const ComplianceFindingsListPage: React.FC = () => {
  const router = useRouter();
  const [findings, setFindings] = useState<ComplianceFinding[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('');

  useEffect(() => {
    fetchFindings();
  }, []);

  const fetchFindings = async () => {
    try {
      setLoading(true);
      const response = await complianceApi.getFindings({
        search: searchTerm,
        status: filterStatus || undefined,
      });
      setFindings(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch compliance findings');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this compliance finding?')) return;
    
    try {
      await complianceApi.deleteFinding(id);
      await fetchFindings(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to delete compliance finding');
    }
  };

  const filteredFindings = findings.filter(finding =>
    finding.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    finding.finding_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) return <div className="p-6">Loading compliance findings...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Compliance Findings</h1>
        <button
          onClick={() => router.push('/enterprise/compliance/findings/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Add New Finding
        </button>
      </div>

      {/* Search and Filter */}
      <div className="mb-6 flex gap-4">
        <input
          type="text"
          placeholder="Search findings..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border rounded px-3 py-2 flex-1"
        />
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="border rounded px-3 py-2"
        >
          <option value="">All Status</option>
          <option value="open">Open</option>
          <option value="in_progress">In Progress</option>
          <option value="resolved">Resolved</option>
          <option value="closed">Closed</option>
        </select>
        <button
          onClick={fetchFindings}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Search
        </button>
      </div>

      {/* Findings Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Code
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Title
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Severity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Due Date
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredFindings.map((finding) => (
              <tr key={finding.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {finding.finding_code}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {finding.title}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    finding.severity === 'critical' 
                      ? 'bg-red-100 text-red-800'
                      : finding.severity === 'high'
                      ? 'bg-orange-100 text-orange-800'
                      : finding.severity === 'medium'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-green-100 text-green-800'
                  }`}>
                    {finding.severity}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {finding.category}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {finding.due_date ? new Date(finding.due_date).toLocaleDateString() : 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    finding.status === 'resolved' 
                      ? 'bg-green-100 text-green-800'
                      : finding.status === 'in_progress'
                      ? 'bg-blue-100 text-blue-800'
                      : finding.status === 'open'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {finding.status.replace('_', ' ')}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => router.push(`/enterprise/compliance/findings/${finding.id}`)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </button>
                    <button
                      onClick={() => router.push(`/enterprise/compliance/findings/${finding.id}/edit`)}
                      className="text-green-600 hover:text-green-900"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(finding.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
        {filteredFindings.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No compliance findings found
          </div>
        )}
      </div>
    </div>
  );
};

export default ComplianceFindingsListPage;
