package handlers

import (
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// ProceedingRequest represents the request structure for proceedings
type ProceedingRequest struct {
	Title       string `json:"title" binding:"required"`
	Description string `json:"description"`
	Type        string `json:"type"` // Optional field, not used in current model
	Status      string `json:"status"`
	Priority    string `json:"priority"`
	Visibility  string `json:"visibility"`
	IsActive    bool   `json:"is_active"`
	AutoAssign  bool   `json:"auto_assign"`
	AgencyID    uint   `json:"agency_id"`
	CategoryID  uint   `json:"category_id"`
	AssignedTo  uint   `json:"assigned_to"`
}

// GetProceedings returns all proceedings with pagination
func GetProceedings(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON><PERSON><PERSON><PERSON>uer<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.<PERSON><PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total proceedings
	var total int64
	db.Model(&models.Proceeding{}).Count(&total)

	// Get proceedings with pagination
	var proceedings []models.Proceeding
	offset := (page - 1) * perPage
	if err := db.Preload("Agency").
		Preload("Category").
		Preload("InitiatedBy").
		Preload("Owner").
		Order("created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&proceedings).Error; err != nil {
		HandleInternalError(c, "Failed to fetch proceedings: "+err.Error())
		return
	}

	// Convert to response format
	proceedingResponses := make([]gin.H, len(proceedings))
	for i, proceeding := range proceedings {
		proceedingResponses[i] = gin.H{
			"id":                 proceeding.ID,
			"name":               proceeding.Name,
			"unique_id":          proceeding.UniqueID,
			"description":        proceeding.Description,
			"objective":          proceeding.Objective,
			"status":             proceeding.Status,
			"priority":           proceeding.Priority,
			"initiation_date":    proceeding.InitiationDate,
			"planned_start_date": proceeding.PlannedStartDate,
			"planned_end_date":   proceeding.PlannedEndDate,
			"created_at":         proceeding.CreatedAt,
			"updated_at":         proceeding.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       proceedingResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetProceeding returns a single proceeding by ID
func GetProceeding(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get proceeding
	var proceeding models.Proceeding
	if err := db.Preload("Agency").
		Preload("Category").
		Preload("InitiatedBy").
		Preload("Owner").
		First(&proceeding, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	response := gin.H{
		"id":                 proceeding.ID,
		"name":               proceeding.Name,
		"unique_id":          proceeding.UniqueID,
		"description":        proceeding.Description,
		"objective":          proceeding.Objective,
		"status":             proceeding.Status,
		"priority":           proceeding.Priority,
		"initiation_date":    proceeding.InitiationDate,
		"planned_start_date": proceeding.PlannedStartDate,
		"planned_end_date":   proceeding.PlannedEndDate,
		"created_at":         proceeding.CreatedAt,
		"updated_at":         proceeding.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding retrieved successfully",
		Data:    response,
	})
}

// CreateProceeding creates a new proceeding
func CreateProceeding(c *gin.Context) {
	var req ProceedingRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get current user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Generate intelligent objective based on proceeding context
	objective, err := generateProceedingObjective(req.Title, req.Description, req.Type, req.Priority, db)
	if err != nil {
		// Fallback to description if objective generation fails
		objective = req.Description
	}

	// Set default values if not provided
	agencyID := req.AgencyID
	if agencyID == 0 {
		agencyID = 1 // Default agency
	}

	categoryID := req.CategoryID
	if categoryID == 0 {
		categoryID = 1 // Default category
	}

	// Create proceeding
	proceeding := &models.Proceeding{
		Name:           req.Title,
		Description:    req.Description,
		Objective:      objective,
		Status:         models.ProceedingStatus(req.Status),
		Priority:       models.ProceedingPriority(req.Priority),
		InitiationDate: time.Now(),
		AgencyID:       &agencyID,
		CategoryID:     &categoryID,
		InitiatedByID:  userID.(uint),
		OwnerID:        userID.(uint),
	}

	if err := db.Create(proceeding).Error; err != nil {
		HandleInternalError(c, "Failed to create proceeding: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Agency").Preload("Category").Preload("InitiatedBy").Preload("Owner").First(proceeding, proceeding.ID)

	response := gin.H{
		"id":                 proceeding.ID,
		"name":               proceeding.Name,
		"unique_id":          proceeding.UniqueID,
		"description":        proceeding.Description,
		"objective":          proceeding.Objective,
		"status":             proceeding.Status,
		"priority":           proceeding.Priority,
		"initiation_date":    proceeding.InitiationDate,
		"planned_start_date": proceeding.PlannedStartDate,
		"planned_end_date":   proceeding.PlannedEndDate,
		"created_at":         proceeding.CreatedAt,
		"updated_at":         proceeding.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Proceeding created successfully",
		Data:    response,
	})
}

// UpdateProceeding updates an existing proceeding
func UpdateProceeding(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req ProceedingRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing proceeding
	var proceeding models.Proceeding
	if err := db.First(&proceeding, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Generate intelligent objective if description changed
	var objective string
	if proceeding.Description != req.Description {
		generatedObjective, err := generateProceedingObjective(req.Title, req.Description, req.Type, req.Priority, db)
		if err != nil {
			// Fallback to description if objective generation fails
			objective = req.Description
		} else {
			objective = generatedObjective
		}
	} else {
		// Keep existing objective if description unchanged
		objective = proceeding.Objective
	}

	// Update proceeding fields
	proceeding.Name = req.Title
	proceeding.Description = req.Description
	proceeding.Objective = objective
	proceeding.Status = models.ProceedingStatus(req.Status)
	proceeding.Priority = models.ProceedingPriority(req.Priority)

	if err := db.Save(&proceeding).Error; err != nil {
		HandleInternalError(c, "Failed to update proceeding: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Agency").Preload("Category").Preload("InitiatedBy").Preload("Owner").First(&proceeding, proceeding.ID)

	response := gin.H{
		"id":                 proceeding.ID,
		"name":               proceeding.Name,
		"unique_id":          proceeding.UniqueID,
		"description":        proceeding.Description,
		"objective":          proceeding.Objective,
		"status":             proceeding.Status,
		"priority":           proceeding.Priority,
		"initiation_date":    proceeding.InitiationDate,
		"planned_start_date": proceeding.PlannedStartDate,
		"planned_end_date":   proceeding.PlannedEndDate,
		"created_at":         proceeding.CreatedAt,
		"updated_at":         proceeding.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding updated successfully",
		Data:    response,
	})
}

// DeleteProceeding deletes a proceeding
func DeleteProceeding(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if proceeding exists
	var proceeding models.Proceeding
	if err := db.First(&proceeding, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Delete proceeding
	if err := db.Delete(&proceeding).Error; err != nil {
		HandleInternalError(c, "Failed to delete proceeding: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding deleted successfully",
	})
}

// UpdateStepStatusRequest represents a request to update step status
type UpdateStepStatusRequest struct {
	StepID      uint   `json:"step_id" binding:"required"`
	Status      string `json:"status" binding:"required"`
	Notes       string `json:"notes"`
	CompletedBy uint   `json:"completed_by"`
}

// UpdateStepStatus updates a proceeding step status
func UpdateStepStatus(c *gin.Context) {
	// Get proceeding ID and step ID from URL parameters
	proceedingID, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	stepID, valid := ValidateID(c, "step_id")
	if !valid {
		return
	}

	var req struct {
		Status      string `json:"status" binding:"required"`
		Notes       string `json:"notes"`
		CompletedBy uint   `json:"completed_by"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing step and verify it belongs to the proceeding
	var step models.ProceedingStep
	if err := db.Where("id = ? AND proceeding_id = ?", stepID, proceedingID).First(&step).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding step")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding step: "+err.Error())
		return
	}

	// Update step status
	step.Status = models.ProceedingStepStatus(req.Status)
	step.Notes = req.Notes

	if req.Status == "completed" {
		step.CompletedAt = &time.Time{}
		*step.CompletedAt = time.Now()
		if req.CompletedBy != 0 {
			step.CompletedByID = &req.CompletedBy
		} else {
			userIDUint := userID.(uint)
			step.CompletedByID = &userIDUint
		}
	}

	if err := db.Save(&step).Error; err != nil {
		HandleInternalError(c, "Failed to update step status: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CompletedBy").First(&step, step.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Step status updated successfully",
		Data:    step,
	})
}

// LogEntryRequest represents a request to add a log entry
type LogEntryRequest struct {
	ProceedingID uint   `json:"proceeding_id" binding:"required"`
	EntryType    string `json:"entry_type" binding:"required"`
	Title        string `json:"title" binding:"required"`
	Description  string `json:"description"`
	Metadata     string `json:"metadata"`
}

// AddLogEntry adds a log entry to a proceeding
func AddLogEntry(c *gin.Context) {
	var req LogEntryRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify proceeding exists
	var proceeding models.Proceeding
	if err := db.First(&proceeding, req.ProceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Create log entry
	logEntry := models.ProceedingLog{
		ProceedingID: req.ProceedingID,
		LogType:      models.ProceedingLogType(req.EntryType),
		Title:        req.Title,
		Content:      req.Description,
		AuthorID:     userID.(uint),
	}

	if err := db.Create(&logEntry).Error; err != nil {
		HandleInternalError(c, "Failed to create log entry: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Author").Preload("Proceeding").First(&logEntry, logEntry.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Log entry added successfully",
		Data:    logEntry,
	})
}

// GetProceedingRelationships returns proceeding relationships
func GetProceedingRelationships(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get proceeding with all relationships
	var proceeding models.Proceeding
	if err := db.Preload("RelatedTasks").Preload("RelatedDocs").Preload("RelatedRegs").First(&proceeding, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Get interconnections
	var interconnections []models.Interconnect
	db.Where("(source_type = ? AND source_id = ?) OR (target_type = ? AND target_id = ?)",
		"proceeding", id, "proceeding", id).Find(&interconnections)

	relationships := gin.H{
		"proceeding_id":       proceeding.ID,
		"proceeding_title":    proceeding.Name,
		"related_tasks":       proceeding.RelatedTasks,
		"related_documents":   proceeding.RelatedDocs,
		"related_regulations": proceeding.RelatedRegs,
		"interconnections":    interconnections,
		"total_relationships": len(proceeding.RelatedTasks) + len(proceeding.RelatedDocs) + len(proceeding.RelatedRegs) + len(interconnections),
		"retrieved_at":        time.Now(),
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding relationships retrieved successfully",
		Data:    relationships,
	})
}

// LinkProceedingToTaskRequest represents a request to link proceeding to task
type LinkProceedingToTaskRequest struct {
	ProceedingID uint   `json:"proceeding_id" binding:"required"`
	TaskID       uint   `json:"task_id" binding:"required"`
	Relationship string `json:"relationship"`
	Notes        string `json:"notes"`
}

// LinkProceedingToTask links a proceeding to a task
func LinkProceedingToTask(c *gin.Context) {
	// Get proceeding ID from URL parameter
	proceedingID, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		TaskID       uint   `json:"task_id" binding:"required"`
		Relationship string `json:"relationship"`
		Notes        string `json:"notes"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify proceeding and task exist
	var proceeding models.Proceeding
	if err := db.First(&proceeding, proceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	var task models.Task
	if err := db.First(&task, req.TaskID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Create the association using GORM's many-to-many
	if err := db.Model(&proceeding).Association("RelatedTasks").Append(&task); err != nil {
		HandleInternalError(c, "Failed to link proceeding to task: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding linked to task successfully",
		Data: gin.H{
			"proceeding_id": proceedingID,
			"task_id":       req.TaskID,
			"relationship":  req.Relationship,
			"linked_at":     time.Now(),
		},
	})
}

// LinkProceedingToDocumentRequest represents a request to link proceeding to document
type LinkProceedingToDocumentRequest struct {
	ProceedingID uint   `json:"proceeding_id" binding:"required"`
	DocumentID   uint   `json:"document_id" binding:"required"`
	Relationship string `json:"relationship"`
	Notes        string `json:"notes"`
}

// LinkProceedingToDocument links a proceeding to a document
func LinkProceedingToDocument(c *gin.Context) {
	// Get proceeding ID from URL parameter
	proceedingID, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		DocumentID   uint   `json:"document_id" binding:"required"`
		Relationship string `json:"relationship"`
		Notes        string `json:"notes"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify proceeding and document exist
	var proceeding models.Proceeding
	if err := db.First(&proceeding, proceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	var document models.Document
	if err := db.First(&document, req.DocumentID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Create the association using GORM's many-to-many
	if err := db.Model(&proceeding).Association("RelatedDocs").Append(&document); err != nil {
		HandleInternalError(c, "Failed to link proceeding to document: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding linked to document successfully",
		Data: gin.H{
			"proceeding_id": proceedingID,
			"document_id":   req.DocumentID,
			"relationship":  req.Relationship,
			"linked_at":     time.Now(),
		},
	})
}

// LinkProceedingToRegulationRequest represents a request to link proceeding to regulation
type LinkProceedingToRegulationRequest struct {
	ProceedingID uint   `json:"proceeding_id" binding:"required"`
	RegulationID uint   `json:"regulation_id" binding:"required"`
	Relationship string `json:"relationship"`
	Notes        string `json:"notes"`
}

// LinkProceedingToRegulation links a proceeding to a regulation
func LinkProceedingToRegulation(c *gin.Context) {
	// Get proceeding ID from URL parameter
	proceedingID, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		RegulationID uint   `json:"regulation_id" binding:"required"`
		Relationship string `json:"relationship"`
		Notes        string `json:"notes"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify proceeding and regulation exist
	var proceeding models.Proceeding
	if err := db.First(&proceeding, proceedingID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	var regulation models.LawsAndRules
	if err := db.First(&regulation, req.RegulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Create the association using GORM's many-to-many
	if err := db.Model(&proceeding).Association("RelatedRegs").Append(&regulation); err != nil {
		HandleInternalError(c, "Failed to link proceeding to regulation: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Proceeding linked to regulation successfully",
		Data: gin.H{
			"proceeding_id": proceedingID,
			"regulation_id": req.RegulationID,
			"relationship":  req.Relationship,
			"linked_at":     time.Now(),
		},
	})
}

// TriggerReviewReport triggers a review report
func TriggerReviewReport(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get proceeding
	var proceeding models.Proceeding
	if err := db.Preload("ProceedingSteps").First(&proceeding, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Proceeding")
			return
		}
		HandleInternalError(c, "Failed to fetch proceeding: "+err.Error())
		return
	}

	// Generate review report data
	completedSteps := 0
	totalSteps := len(proceeding.ProceedingSteps)

	for _, step := range proceeding.ProceedingSteps {
		if step.Status == models.ProceedingStepCompleted {
			completedSteps++
		}
	}

	progressPercent := float64(0)
	if totalSteps > 0 {
		progressPercent = float64(completedSteps) / float64(totalSteps) * 100
	}

	// Update proceeding review status
	proceeding.ReviewScheduled = true
	proceeding.ReviewDate = &time.Time{}
	*proceeding.ReviewDate = time.Now()
	proceeding.ProgressPercent = progressPercent
	proceeding.CompletedSteps = completedSteps
	proceeding.TotalSteps = totalSteps

	if err := db.Save(&proceeding).Error; err != nil {
		HandleInternalError(c, "Failed to update proceeding review status: "+err.Error())
		return
	}

	// Create review report data
	reviewReport := gin.H{
		"proceeding_id":       proceeding.ID,
		"proceeding_title":    proceeding.Name,
		"review_triggered_at": time.Now(),
		"triggered_by_id":     userID.(uint),
		"total_steps":         totalSteps,
		"completed_steps":     completedSteps,
		"progress_percent":    progressPercent,
		"review_status":       "scheduled",
		"review_date":         proceeding.ReviewDate,
		"summary":             "Review report triggered for proceeding analysis",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Review report triggered successfully",
		Data:    reviewReport,
	})
}

// generateProceedingObjective generates an intelligent objective based on proceeding context
func generateProceedingObjective(title, description, proceedingType, priority string, db *gorm.DB) (string, error) {
	// Base objective templates based on proceeding type
	objectiveTemplates := map[string]string{
		"rulemaking":         "To establish comprehensive regulatory framework for %s through systematic rulemaking process, ensuring compliance with statutory requirements and stakeholder engagement.",
		"enforcement":        "To investigate, assess, and enforce regulatory compliance regarding %s, implementing appropriate corrective measures and penalties as necessary.",
		"investigation":      "To conduct thorough investigation into %s, gathering evidence, analyzing findings, and determining appropriate regulatory response.",
		"review":             "To systematically review and evaluate %s, assessing effectiveness, compliance, and potential improvements to existing regulatory framework.",
		"consultation":       "To facilitate comprehensive stakeholder consultation on %s, gathering input, analyzing feedback, and developing informed regulatory recommendations.",
		"licensing":          "To process, evaluate, and determine licensing requirements for %s, ensuring compliance with regulatory standards and public interest.",
		"adjudication":       "To adjudicate matters related to %s through formal proceedings, ensuring due process and fair resolution of regulatory disputes.",
		"monitoring":         "To establish ongoing monitoring and oversight of %s, ensuring continued compliance and effectiveness of regulatory measures.",
		"policy_development": "To develop comprehensive policy framework for %s, analyzing regulatory gaps and establishing strategic implementation guidelines.",
		"compliance_audit":   "To conduct systematic compliance audit of %s, identifying deficiencies and establishing corrective action requirements.",
	}

	// Extract key subject matter from title and description
	subjectMatter := extractSubjectMatter(title, description)

	// Get base template
	template, exists := objectiveTemplates[strings.ToLower(proceedingType)]
	if !exists {
		template = "To address regulatory matters concerning %s through appropriate administrative proceedings, ensuring compliance with applicable laws and regulations."
	}

	// Generate base objective
	baseObjective := fmt.Sprintf(template, subjectMatter)

	// Enhance objective based on priority
	priorityEnhancements := map[string]string{
		"critical": " This proceeding is designated as critical priority requiring expedited processing and immediate regulatory attention.",
		"urgent":   " This proceeding requires urgent attention with accelerated timelines to address pressing regulatory concerns.",
		"high":     " This proceeding is designated as high priority requiring focused resources and timely completion.",
		"medium":   " This proceeding follows standard regulatory timelines with appropriate resource allocation.",
		"low":      " This proceeding will be processed within standard regulatory frameworks as resources permit.",
	}

	if enhancement, exists := priorityEnhancements[strings.ToLower(priority)]; exists {
		baseObjective += enhancement
	}

	// Add regulatory compliance elements
	complianceElements := []string{
		"ensuring transparency and public participation",
		"maintaining regulatory consistency and predictability",
		"protecting public interest and stakeholder rights",
		"adhering to statutory mandates and procedural requirements",
		"promoting effective and efficient regulatory outcomes",
	}

	// Add 2-3 relevant compliance elements based on proceeding type
	var selectedElements []string
	switch strings.ToLower(proceedingType) {
	case "rulemaking", "policy_development":
		selectedElements = complianceElements[0:3] // transparency, consistency, public interest
	case "enforcement", "adjudication":
		selectedElements = append(selectedElements, complianceElements[2], complianceElements[3]) // public interest, statutory mandates
	case "investigation", "compliance_audit":
		selectedElements = append(selectedElements, complianceElements[1], complianceElements[4]) // consistency, efficiency
	default:
		selectedElements = complianceElements[0:2] // transparency, consistency
	}

	if len(selectedElements) > 0 {
		baseObjective += " The proceeding will prioritize " + strings.Join(selectedElements, ", ") + "."
	}

	// Add measurable outcomes based on analysis of similar proceedings
	measurableOutcomes, err := generateMeasurableOutcomes(proceedingType, subjectMatter, db)
	if err == nil && measurableOutcomes != "" {
		baseObjective += " " + measurableOutcomes
	}

	return baseObjective, nil
}

// extractSubjectMatter extracts the key subject matter from title and description
func extractSubjectMatter(title, description string) string {
	// Combine title and description for analysis
	text := strings.ToLower(title + " " + description)

	// Common regulatory subject patterns
	subjectPatterns := []struct {
		pattern string
		subject string
	}{
		{`(?i)(financial|banking|credit|loan|investment)`, "financial services and banking operations"},
		{`(?i)(environmental|pollution|emission|waste|climate)`, "environmental protection and sustainability"},
		{`(?i)(healthcare|medical|pharmaceutical|drug|patient)`, "healthcare and medical services"},
		{`(?i)(transportation|vehicle|traffic|aviation|maritime)`, "transportation and logistics"},
		{`(?i)(energy|power|electricity|renewable|nuclear)`, "energy production and distribution"},
		{`(?i)(telecommunications|internet|data|privacy|cyber)`, "telecommunications and data protection"},
		{`(?i)(food|agriculture|farming|pesticide|nutrition)`, "food safety and agricultural practices"},
		{`(?i)(education|school|university|student|academic)`, "educational institutions and services"},
		{`(?i)(employment|labor|worker|workplace|safety)`, "employment and workplace standards"},
		{`(?i)(consumer|product|safety|recall|warranty)`, "consumer protection and product safety"},
		{`(?i)(housing|real estate|property|construction|building)`, "housing and construction standards"},
		{`(?i)(insurance|coverage|claim|premium|policy)`, "insurance and risk management"},
	}

	// Find matching subject patterns
	for _, pattern := range subjectPatterns {
		if matched, _ := regexp.MatchString(pattern.pattern, text); matched {
			return pattern.subject
		}
	}

	// Extract key nouns if no pattern matches
	words := strings.Fields(text)
	var keyWords []string

	// Simple keyword extraction (in production, use NLP library)
	commonWords := map[string]bool{
		"the": true, "and": true, "or": true, "but": true, "in": true, "on": true, "at": true, "to": true,
		"for": true, "of": true, "with": true, "by": true, "from": true, "up": true, "about": true, "into": true,
		"through": true, "during": true, "before": true, "after": true, "above": true, "below": true, "between": true,
		"among": true, "is": true, "are": true, "was": true, "were": true, "be": true, "been": true, "being": true,
		"have": true, "has": true, "had": true, "do": true, "does": true, "did": true, "will": true, "would": true,
		"could": true, "should": true, "may": true, "might": true, "must": true, "can": true, "shall": true,
	}

	for _, word := range words {
		cleaned := strings.Trim(word, ".,!?;:")
		if len(cleaned) > 3 && !commonWords[cleaned] {
			keyWords = append(keyWords, cleaned)
			if len(keyWords) >= 3 {
				break
			}
		}
	}

	if len(keyWords) > 0 {
		return strings.Join(keyWords, " and ") + " related matters"
	}

	return "regulatory matters and compliance requirements"
}

// generateMeasurableOutcomes generates specific measurable outcomes based on proceeding type
func generateMeasurableOutcomes(proceedingType, subjectMatter string, db *gorm.DB) (string, error) {
	outcomeTemplates := map[string][]string{
		"rulemaking": {
			"Expected outcomes include publication of proposed rules within 90 days, completion of public comment period, and finalization of regulations within 12 months.",
			"Target deliverables include comprehensive regulatory impact analysis, stakeholder consultation report, and final rule publication.",
		},
		"enforcement": {
			"Expected outcomes include completion of investigation within 180 days, issuance of enforcement actions as warranted, and compliance monitoring implementation.",
			"Target deliverables include investigation report, enforcement recommendations, and compliance improvement plan.",
		},
		"investigation": {
			"Expected outcomes include comprehensive fact-finding within 120 days, analysis of regulatory implications, and recommendations for appropriate action.",
			"Target deliverables include investigation findings, regulatory gap analysis, and action plan recommendations.",
		},
		"review": {
			"Expected outcomes include systematic evaluation within 90 days, effectiveness assessment, and improvement recommendations.",
			"Target deliverables include review report, performance metrics analysis, and regulatory enhancement proposals.",
		},
	}

	if outcomes, exists := outcomeTemplates[strings.ToLower(proceedingType)]; exists {
		// Return first outcome template for now (in production, could use ML to select best fit)
		return outcomes[0], nil
	}

	return "Expected outcomes include timely completion of regulatory proceedings, stakeholder engagement, and effective implementation of regulatory measures.", nil
}
