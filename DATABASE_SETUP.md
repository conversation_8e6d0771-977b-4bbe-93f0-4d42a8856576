# PostgreSQL Database Setup for NoteControl

This guide will help you set up PostgreSQL for the NoteControl Federal Register Clone backend.

## Prerequisites

1. **PostgreSQL installed** - Download from [https://www.postgresql.org/download/windows/](https://www.postgresql.org/download/windows/)
2. **PostgreSQL service running**
3. **psql command available in PATH**

## Option 1: Automated Setup (Recommended)

Run the PowerShell setup script:

```powershell
# Run in PowerShell as Administrator
.\setup_database.ps1
```

This script will:
- Check if PostgreSQL is installed and running
- Create the database `federal_register_db`
- Set up the user with proper permissions
- Install required extensions
- Test the connection

## Option 2: Manual Setup

### Step 1: Connect to PostgreSQL

Open PowerShell and connect to PostgreSQL as the superuser:

```powershell
psql -h localhost -U postgres -d postgres
```

Enter your PostgreSQL superuser password when prompted.

### Step 2: Create Database and User

Run the following SQL commands:

```sql
-- Create the database
CREATE DATABASE federal_register_db;

-- Connect to the new database
\c federal_register_db;

-- Create necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Exit psql
\q
```

### Step 3: Test Connection

Test the connection with the application credentials:

```powershell
# Set password environment variable
$env:PGPASSWORD = "NoteControl2024!"

# Test connection
psql -h localhost -U postgres -d federal_register_db -c "SELECT current_database();"

# Clean up
Remove-Item Env:PGPASSWORD
```

## Database Configuration

The backend is configured with these database settings (in `backend/.env`):

```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=NoteControl2024!
DB_NAME=federal_register_db
DB_SSLMODE=disable
```

## Starting the Backend

After database setup, start the backend server:

```powershell
# Navigate to backend directory
cd backend

# Install dependencies (if needed)
go mod tidy

# Run the server
go run cmd/server/main.go
```

The server will:
- Connect to PostgreSQL
- Run database migrations automatically
- Create all necessary tables
- Set up indexes for performance
- Start the API server on http://localhost:8080

## Verification

Once the server starts, you should see:
- Database connection successful
- Migration completed successfully
- Server starting on localhost:8080

You can verify the setup by:
1. Checking the server logs for successful database connection
2. Visiting http://localhost:8080/api/v1/health (if health endpoint exists)
3. Using psql to check that tables were created:

```sql
\c federal_register_db
\dt
```

## Troubleshooting

### Common Issues:

1. **PostgreSQL not found**: Ensure PostgreSQL is installed and added to PATH
2. **Connection refused**: Check if PostgreSQL service is running
3. **Authentication failed**: Verify the postgres user password
4. **Permission denied**: Ensure the postgres user has necessary privileges

### Useful Commands:

```powershell
# Check PostgreSQL service status
Get-Service -Name "postgresql*"

# Start PostgreSQL service
Start-Service postgresql-x64-14  # Replace with your version

# Check if psql is available
Get-Command psql
```

## Next Steps

After successful database setup:
1. Start the backend server
2. Test API endpoints
3. Set up the frontend to connect to the backend
4. Begin testing the full application stack
