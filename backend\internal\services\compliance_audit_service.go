package services

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// ComplianceAuditService handles compliance and audit operations
type ComplianceAuditService struct {
	db *gorm.DB
}

// NewComplianceAuditService creates a new compliance audit service
func NewComplianceAuditService(db *gorm.DB) *ComplianceAuditService {
	return &ComplianceAuditService{db: db}
}

// LogAuditEvent creates a comprehensive audit log entry
func (s *ComplianceAuditService) LogAuditEvent(req CreateAuditLogRequest) (*models.AuditLog, error) {
	// Generate unique event ID
	eventID, err := s.generateEventID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate event ID: %w", err)
	}

	auditLog := &models.AuditLog{
		EventID:              eventID,
		EventType:            req.EventType,
		EventName:            req.EventName,
		Description:          req.Description,
		Category:             req.Category,
		UserID:               req.UserID,
		Username:             req.Username,
		UserRole:             req.UserRole,
		SessionID:            req.SessionID,
		ImpersonatedByID:     req.ImpersonatedByID,
		ResourceType:         req.ResourceType,
		ResourceID:           req.ResourceID,
		ResourceName:         req.ResourceName,
		ParentResourceType:   req.ParentResourceType,
		ParentResourceID:     req.ParentResourceID,
		IPAddress:            req.IPAddress,
		UserAgent:            req.UserAgent,
		RequestMethod:        req.RequestMethod,
		RequestURL:           req.RequestURL,
		ResponseCode:         req.ResponseCode,
		RequestSize:          req.RequestSize,
		ResponseSize:         req.ResponseSize,
		OldValues:            req.OldValues,
		NewValues:            req.NewValues,
		ChangedFields:        req.ChangedFields,
		ChangeReason:         req.ChangeReason,
		RiskLevel:            req.RiskLevel,
		ComplianceFrameworks: req.ComplianceFrameworks,
		RequiresReview:       req.RequiresReview,
		Country:              req.Country,
		Region:               req.Region,
		City:                 req.City,
		Timezone:             req.Timezone,
		DeviceType:           req.DeviceType,
		OperatingSystem:      req.OperatingSystem,
		Browser:              req.Browser,
		Tags:                 req.Tags,
		Metadata:             req.Metadata,
		CorrelationID:        req.CorrelationID,
		TraceID:              req.TraceID,
		DataClassification:   req.DataClassification,
		ContainsPII:          req.ContainsPII,
		ContainsPHI:          req.ContainsPHI,
		ContainsFinancial:    req.ContainsFinancial,
	}

	if err := s.db.Create(auditLog).Error; err != nil {
		return nil, fmt.Errorf("failed to create audit log: %w", err)
	}

	// Check for compliance violations
	go s.checkComplianceViolations(auditLog)

	return auditLog, nil
}

// GetAuditLogs retrieves audit logs with filtering
func (s *ComplianceAuditService) GetAuditLogs(filter AuditLogFilter) ([]models.AuditLog, error) {
	query := s.db.Preload("User").Preload("ImpersonatedBy").Preload("ReviewedBy")

	if filter.EventType != "" {
		query = query.Where("event_type = ?", filter.EventType)
	}
	if filter.UserID != 0 {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.ResourceType != "" {
		query = query.Where("resource_type = ?", filter.ResourceType)
	}
	if filter.ResourceID != "" {
		query = query.Where("resource_id = ?", filter.ResourceID)
	}
	if filter.RiskLevel != "" {
		query = query.Where("risk_level = ?", filter.RiskLevel)
	}
	if !filter.StartDate.IsZero() {
		query = query.Where("created_at >= ?", filter.StartDate)
	}
	if !filter.EndDate.IsZero() {
		query = query.Where("created_at <= ?", filter.EndDate)
	}
	if filter.RequiresReview {
		query = query.Where("requires_review = ?", true)
	}

	var logs []models.AuditLog
	if err := query.Order("created_at DESC").Limit(filter.Limit).Offset(filter.Offset).Find(&logs).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve audit logs: %w", err)
	}

	return logs, nil
}

// CreateComplianceRule creates a new compliance rule
func (s *ComplianceAuditService) CreateComplianceRule(req CreateComplianceRuleRequest) (*models.ComplianceRule, error) {
	// Generate unique rule ID
	ruleID, err := s.generateRuleID(req.Framework)
	if err != nil {
		return nil, fmt.Errorf("failed to generate rule ID: %w", err)
	}

	rule := &models.ComplianceRule{
		RuleID:          ruleID,
		Name:            req.Name,
		Description:     req.Description,
		Framework:       req.Framework,
		Category:        req.Category,
		Subcategory:     req.Subcategory,
		RuleType:        req.RuleType,
		Severity:        req.Severity,
		Priority:        req.Priority,
		IsActive:        req.IsActive,
		IsAutomated:     req.IsAutomated,
		Conditions:      req.Conditions,
		Actions:         req.Actions,
		ValidationLogic: req.ValidationLogic,
		Remediation:     req.Remediation,
		AppliesTo:       req.AppliesTo,
		ExcludeFrom:     req.ExcludeFrom,
		EffectiveDate:   req.EffectiveDate,
		ExpirationDate:  req.ExpirationDate,
		CheckFrequency:  req.CheckFrequency,
		OwnerID:         req.OwnerID,
		ResponsibleTeam: req.ResponsibleTeam,
		ContactEmail:    req.ContactEmail,
		ReferenceURL:    req.ReferenceURL,
		Documentation:   req.Documentation,
		Examples:        req.Examples,
		Tags:            req.Tags,
	}

	// Calculate next check time
	if rule.IsAutomated && rule.CheckFrequency > 0 {
		nextCheck := time.Now().Add(time.Duration(rule.CheckFrequency) * time.Hour)
		rule.NextCheck = &nextCheck
	}

	if err := s.db.Create(rule).Error; err != nil {
		return nil, fmt.Errorf("failed to create compliance rule: %w", err)
	}

	return rule, nil
}

// CreateComplianceViolation creates a new compliance violation
func (s *ComplianceAuditService) CreateComplianceViolation(req CreateComplianceViolationRequest) (*models.ComplianceViolation, error) {
	// Generate unique violation ID
	violationID, err := s.generateViolationID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate violation ID: %w", err)
	}

	violation := &models.ComplianceViolation{
		ViolationID:        violationID,
		RuleID:             req.RuleID,
		Title:              req.Title,
		Description:        req.Description,
		Severity:           req.Severity,
		Status:             "open",
		ResourceType:       req.ResourceType,
		ResourceID:         req.ResourceID,
		ResourceName:       req.ResourceName,
		DetectedAt:         time.Now(),
		DetectedByID:       req.DetectedByID,
		DetectionMethod:    req.DetectionMethod,
		AuditLogID:         req.AuditLogID,
		ImpactLevel:        req.ImpactLevel,
		BusinessImpact:     req.BusinessImpact,
		TechnicalImpact:    req.TechnicalImpact,
		AffectedUsers:      req.AffectedUsers,
		DataExposed:        req.DataExposed,
		RemediationSteps:   req.RemediationSteps,
		RemediationDueDate: req.RemediationDueDate,
		PreventiveMeasures: req.PreventiveMeasures,
		Evidence:           req.Evidence,
		Attachments:        req.Attachments,
		Tags:               req.Tags,
		Notes:              req.Notes,
	}

	if err := s.db.Create(violation).Error; err != nil {
		return nil, fmt.Errorf("failed to create compliance violation: %w", err)
	}

	// Update rule violation count
	s.db.Model(&models.ComplianceRule{}).Where("id = ?", req.RuleID).
		UpdateColumn("violation_count", gorm.Expr("violation_count + ?", 1))

	return violation, nil
}

// GenerateComplianceReport generates a comprehensive compliance report
func (s *ComplianceAuditService) GenerateComplianceReport(req GenerateComplianceReportRequest) (*models.ComplianceReport, error) {
	startTime := time.Now()

	// Generate unique report ID
	reportID, err := s.generateReportID(req.Framework)
	if err != nil {
		return nil, fmt.Errorf("failed to generate report ID: %w", err)
	}

	// Gather compliance data
	reportData, err := s.gatherComplianceData(req)
	if err != nil {
		return nil, fmt.Errorf("failed to gather compliance data: %w", err)
	}

	report := &models.ComplianceReport{
		ReportID:           reportID,
		Name:               req.Name,
		Description:        req.Description,
		Framework:          req.Framework,
		Type:               req.Type,
		PeriodStart:        req.PeriodStart,
		PeriodEnd:          req.PeriodEnd,
		GeneratedAt:        startTime,
		ValidUntil:         req.ValidUntil,
		Scope:              req.Scope,
		Departments:        req.Departments,
		Systems:            req.Systems,
		TotalEvents:        int(reportData.TotalEvents),
		ComplianceScore:    reportData.ComplianceScore,
		ViolationCount:     int(reportData.ViolationCount),
		ResolvedViolations: int(reportData.ResolvedViolations),
		OpenViolations:     int(reportData.OpenViolations),
		CriticalRisks:      int(reportData.CriticalRisks),
		HighRisks:          int(reportData.HighRisks),
		MediumRisks:        int(reportData.MediumRisks),
		LowRisks:           int(reportData.LowRisks),
		ExecutiveSummary:   reportData.ExecutiveSummary,
		KeyFindings:        reportData.KeyFindings,
		Recommendations:    reportData.Recommendations,
		ActionPlan:         reportData.ActionPlan,
		DetailedData:       reportData.DetailedData,
		GeneratedByID:      req.GeneratedByID,
		ReportFormat:       req.ReportFormat,
		Recipients:         req.Recipients,
		RequiresApproval:   req.RequiresApproval,
		Tags:               req.Tags,
		Metadata:           req.Metadata,
		Version:            req.Version,
		IsPublic:           req.IsPublic,
	}

	// Calculate generation time
	report.GenerationTime = int(time.Since(startTime).Milliseconds())

	if err := s.db.Create(report).Error; err != nil {
		return nil, fmt.Errorf("failed to create compliance report: %w", err)
	}

	return report, nil
}

// Helper functions

func (s *ComplianceAuditService) generateEventID() (string, error) {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return fmt.Sprintf("EVT-%s", hex.EncodeToString(bytes)), nil
}

func (s *ComplianceAuditService) generateRuleID(framework models.ComplianceFramework) (string, error) {
	bytes := make([]byte, 4)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return fmt.Sprintf("%s-%s", framework, hex.EncodeToString(bytes)), nil
}

func (s *ComplianceAuditService) generateViolationID() (string, error) {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return fmt.Sprintf("VIO-%s", hex.EncodeToString(bytes)), nil
}

func (s *ComplianceAuditService) generateReportID(framework models.ComplianceFramework) (string, error) {
	bytes := make([]byte, 6)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return fmt.Sprintf("RPT-%s-%s", framework, hex.EncodeToString(bytes)), nil
}

func (s *ComplianceAuditService) checkComplianceViolations(auditLog *models.AuditLog) {
	// Get applicable compliance rules
	var rules []models.ComplianceRule
	s.db.Where("is_active = ? AND is_automated = ?", true, true).Find(&rules)

	for _, rule := range rules {
		if s.evaluateRuleConditions(rule, auditLog) {
			// Create violation
			s.CreateComplianceViolation(CreateComplianceViolationRequest{
				RuleID:          rule.ID,
				Title:           fmt.Sprintf("Violation of %s", rule.Name),
				Description:     fmt.Sprintf("Automated detection of violation for rule %s", rule.RuleID),
				Severity:        models.RiskLevel(rule.Severity),
				ResourceType:    auditLog.ResourceType,
				ResourceID:      auditLog.ResourceID,
				ResourceName:    auditLog.ResourceName,
				DetectionMethod: "automated",
				AuditLogID:      &auditLog.ID,
				ImpactLevel:     rule.Severity,
			})
		}
	}
}

func (s *ComplianceAuditService) evaluateRuleConditions(rule models.ComplianceRule, auditLog *models.AuditLog) bool {
	// Simple rule evaluation - in production this would be more sophisticated
	if rule.Conditions == "" {
		return false
	}

	var conditions map[string]interface{}
	if err := json.Unmarshal([]byte(rule.Conditions), &conditions); err != nil {
		return false
	}

	// Check event types
	if eventTypes, ok := conditions["event_types"].([]interface{}); ok {
		found := false
		for _, et := range eventTypes {
			if et.(string) == string(auditLog.EventType) {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check resource types
	if resourceTypes, ok := conditions["resource_types"].([]interface{}); ok {
		found := false
		for _, rt := range resourceTypes {
			if rt.(string) == auditLog.ResourceType {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check data classification
	if dataClassifications, ok := conditions["data_classification"].([]interface{}); ok {
		found := false
		for _, dc := range dataClassifications {
			if dc.(string) == auditLog.DataClassification {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check PII/PHI flags
	if containsPII, ok := conditions["contains_pii"].(bool); ok {
		if containsPII && !auditLog.ContainsPII {
			return false
		}
	}

	if containsPHI, ok := conditions["contains_phi"].(bool); ok {
		if containsPHI && !auditLog.ContainsPHI {
			return false
		}
	}

	return true
}

func (s *ComplianceAuditService) gatherComplianceData(req GenerateComplianceReportRequest) (*ComplianceReportData, error) {
	data := &ComplianceReportData{}

	// Count total events in period
	s.db.Model(&models.AuditLog{}).
		Where("created_at BETWEEN ? AND ?", req.PeriodStart, req.PeriodEnd).
		Count(&data.TotalEvents)

	// Count violations by status
	s.db.Model(&models.ComplianceViolation{}).
		Where("detected_at BETWEEN ? AND ?", req.PeriodStart, req.PeriodEnd).
		Count(&data.ViolationCount)

	s.db.Model(&models.ComplianceViolation{}).
		Where("detected_at BETWEEN ? AND ? AND status = ?", req.PeriodStart, req.PeriodEnd, "resolved").
		Count(&data.ResolvedViolations)

	s.db.Model(&models.ComplianceViolation{}).
		Where("detected_at BETWEEN ? AND ? AND status = ?", req.PeriodStart, req.PeriodEnd, "open").
		Count(&data.OpenViolations)

	// Count risks by level
	s.db.Model(&models.ComplianceViolation{}).
		Where("detected_at BETWEEN ? AND ? AND severity = ?", req.PeriodStart, req.PeriodEnd, "critical").
		Count(&data.CriticalRisks)

	s.db.Model(&models.ComplianceViolation{}).
		Where("detected_at BETWEEN ? AND ? AND severity = ?", req.PeriodStart, req.PeriodEnd, "high").
		Count(&data.HighRisks)

	s.db.Model(&models.ComplianceViolation{}).
		Where("detected_at BETWEEN ? AND ? AND severity = ?", req.PeriodStart, req.PeriodEnd, "medium").
		Count(&data.MediumRisks)

	s.db.Model(&models.ComplianceViolation{}).
		Where("detected_at BETWEEN ? AND ? AND severity = ?", req.PeriodStart, req.PeriodEnd, "low").
		Count(&data.LowRisks)

	// Calculate compliance score
	if data.ViolationCount > 0 {
		data.ComplianceScore = float64(data.ResolvedViolations) / float64(data.ViolationCount) * 100
	} else {
		data.ComplianceScore = 100.0
	}

	// Generate summary content
	data.ExecutiveSummary = s.generateExecutiveSummary(data)
	data.KeyFindings = s.generateKeyFindings(data)
	data.Recommendations = s.generateRecommendations(data)
	data.ActionPlan = s.generateActionPlan(data)

	// Serialize detailed data
	if detailedJSON, err := json.Marshal(data); err == nil {
		data.DetailedData = string(detailedJSON)
	}

	return data, nil
}

func (s *ComplianceAuditService) generateExecutiveSummary(data *ComplianceReportData) string {
	return fmt.Sprintf("During the reporting period, %d audit events were recorded with %d compliance violations detected. The overall compliance score is %.1f%%. %d violations remain open and require attention.",
		data.TotalEvents, data.ViolationCount, data.ComplianceScore, data.OpenViolations)
}

func (s *ComplianceAuditService) generateKeyFindings(data *ComplianceReportData) string {
	findings := []string{
		fmt.Sprintf("Total audit events: %d", data.TotalEvents),
		fmt.Sprintf("Compliance violations: %d", data.ViolationCount),
		fmt.Sprintf("Critical risks: %d", data.CriticalRisks),
		fmt.Sprintf("High risks: %d", data.HighRisks),
		fmt.Sprintf("Compliance score: %.1f%%", data.ComplianceScore),
	}

	findingsJSON, _ := json.Marshal(findings)
	return string(findingsJSON)
}

func (s *ComplianceAuditService) generateRecommendations(data *ComplianceReportData) string {
	recommendations := []string{
		"Implement automated compliance monitoring",
		"Enhance user training on compliance requirements",
		"Review and update compliance policies",
		"Strengthen access controls and monitoring",
	}

	if data.CriticalRisks > 0 {
		recommendations = append(recommendations, "Immediate attention required for critical risk violations")
	}

	recommendationsJSON, _ := json.Marshal(recommendations)
	return string(recommendationsJSON)
}

func (s *ComplianceAuditService) generateActionPlan(data *ComplianceReportData) string {
	actionPlan := map[string]interface{}{
		"immediate_actions": []string{
			"Review all open violations",
			"Assign remediation owners",
			"Set remediation deadlines",
		},
		"short_term_actions": []string{
			"Implement process improvements",
			"Update compliance training",
			"Enhance monitoring capabilities",
		},
		"long_term_actions": []string{
			"Automate compliance checks",
			"Implement continuous monitoring",
			"Regular compliance assessments",
		},
	}

	actionPlanJSON, _ := json.Marshal(actionPlan)
	return string(actionPlanJSON)
}

// Request/Response types
type CreateAuditLogRequest struct {
	EventType            models.AuditEventType `json:"event_type"`
	EventName            string                `json:"event_name"`
	Description          string                `json:"description"`
	Category             string                `json:"category"`
	UserID               *uint                 `json:"user_id"`
	Username             string                `json:"username"`
	UserRole             string                `json:"user_role"`
	SessionID            string                `json:"session_id"`
	ImpersonatedByID     *uint                 `json:"impersonated_by_id"`
	ResourceType         string                `json:"resource_type"`
	ResourceID           string                `json:"resource_id"`
	ResourceName         string                `json:"resource_name"`
	ParentResourceType   string                `json:"parent_resource_type"`
	ParentResourceID     string                `json:"parent_resource_id"`
	IPAddress            string                `json:"ip_address"`
	UserAgent            string                `json:"user_agent"`
	RequestMethod        string                `json:"request_method"`
	RequestURL           string                `json:"request_url"`
	ResponseCode         int                   `json:"response_code"`
	RequestSize          int64                 `json:"request_size"`
	ResponseSize         int64                 `json:"response_size"`
	OldValues            string                `json:"old_values"`
	NewValues            string                `json:"new_values"`
	ChangedFields        string                `json:"changed_fields"`
	ChangeReason         string                `json:"change_reason"`
	RiskLevel            models.RiskLevel      `json:"risk_level"`
	ComplianceFrameworks string                `json:"compliance_frameworks"`
	RequiresReview       bool                  `json:"requires_review"`
	Country              string                `json:"country"`
	Region               string                `json:"region"`
	City                 string                `json:"city"`
	Timezone             string                `json:"timezone"`
	DeviceType           string                `json:"device_type"`
	OperatingSystem      string                `json:"operating_system"`
	Browser              string                `json:"browser"`
	Tags                 string                `json:"tags"`
	Metadata             string                `json:"metadata"`
	CorrelationID        string                `json:"correlation_id"`
	TraceID              string                `json:"trace_id"`
	DataClassification   string                `json:"data_classification"`
	ContainsPII          bool                  `json:"contains_pii"`
	ContainsPHI          bool                  `json:"contains_phi"`
	ContainsFinancial    bool                  `json:"contains_financial"`
}

type AuditLogFilter struct {
	EventType      string    `json:"event_type"`
	UserID         uint      `json:"user_id"`
	ResourceType   string    `json:"resource_type"`
	ResourceID     string    `json:"resource_id"`
	RiskLevel      string    `json:"risk_level"`
	StartDate      time.Time `json:"start_date"`
	EndDate        time.Time `json:"end_date"`
	RequiresReview bool      `json:"requires_review"`
	Limit          int       `json:"limit"`
	Offset         int       `json:"offset"`
}

type CreateComplianceRuleRequest struct {
	Name            string                     `json:"name"`
	Description     string                     `json:"description"`
	Framework       models.ComplianceFramework `json:"framework"`
	Category        string                     `json:"category"`
	Subcategory     string                     `json:"subcategory"`
	RuleType        string                     `json:"rule_type"`
	Severity        string                     `json:"severity"`
	Priority        int                        `json:"priority"`
	IsActive        bool                       `json:"is_active"`
	IsAutomated     bool                       `json:"is_automated"`
	Conditions      string                     `json:"conditions"`
	Actions         string                     `json:"actions"`
	ValidationLogic string                     `json:"validation_logic"`
	Remediation     string                     `json:"remediation"`
	AppliesTo       string                     `json:"applies_to"`
	ExcludeFrom     string                     `json:"exclude_from"`
	EffectiveDate   time.Time                  `json:"effective_date"`
	ExpirationDate  *time.Time                 `json:"expiration_date"`
	CheckFrequency  int                        `json:"check_frequency"`
	OwnerID         uint                       `json:"owner_id"`
	ResponsibleTeam string                     `json:"responsible_team"`
	ContactEmail    string                     `json:"contact_email"`
	ReferenceURL    string                     `json:"reference_url"`
	Documentation   string                     `json:"documentation"`
	Examples        string                     `json:"examples"`
	Tags            string                     `json:"tags"`
}

type CreateComplianceViolationRequest struct {
	RuleID             uint             `json:"rule_id"`
	Title              string           `json:"title"`
	Description        string           `json:"description"`
	Severity           models.RiskLevel `json:"severity"`
	ResourceType       string           `json:"resource_type"`
	ResourceID         string           `json:"resource_id"`
	ResourceName       string           `json:"resource_name"`
	DetectedByID       *uint            `json:"detected_by_id"`
	DetectionMethod    string           `json:"detection_method"`
	AuditLogID         *uint            `json:"audit_log_id"`
	ImpactLevel        string           `json:"impact_level"`
	BusinessImpact     string           `json:"business_impact"`
	TechnicalImpact    string           `json:"technical_impact"`
	AffectedUsers      int              `json:"affected_users"`
	DataExposed        bool             `json:"data_exposed"`
	RemediationSteps   string           `json:"remediation_steps"`
	RemediationDueDate *time.Time       `json:"remediation_due_date"`
	PreventiveMeasures string           `json:"preventive_measures"`
	Evidence           string           `json:"evidence"`
	Attachments        string           `json:"attachments"`
	Tags               string           `json:"tags"`
	Notes              string           `json:"notes"`
}

type GenerateComplianceReportRequest struct {
	Name             string                     `json:"name"`
	Description      string                     `json:"description"`
	Framework        models.ComplianceFramework `json:"framework"`
	Type             string                     `json:"type"`
	PeriodStart      time.Time                  `json:"period_start"`
	PeriodEnd        time.Time                  `json:"period_end"`
	ValidUntil       *time.Time                 `json:"valid_until"`
	Scope            string                     `json:"scope"`
	Departments      string                     `json:"departments"`
	Systems          string                     `json:"systems"`
	GeneratedByID    uint                       `json:"generated_by_id"`
	ReportFormat     string                     `json:"report_format"`
	Recipients       string                     `json:"recipients"`
	RequiresApproval bool                       `json:"requires_approval"`
	Tags             string                     `json:"tags"`
	Metadata         string                     `json:"metadata"`
	Version          string                     `json:"version"`
	IsPublic         bool                       `json:"is_public"`
}

type ComplianceReportData struct {
	TotalEvents        int64   `json:"total_events"`
	ComplianceScore    float64 `json:"compliance_score"`
	ViolationCount     int64   `json:"violation_count"`
	ResolvedViolations int64   `json:"resolved_violations"`
	OpenViolations     int64   `json:"open_violations"`
	CriticalRisks      int64   `json:"critical_risks"`
	HighRisks          int64   `json:"high_risks"`
	MediumRisks        int64   `json:"medium_risks"`
	LowRisks           int64   `json:"low_risks"`
	ExecutiveSummary   string  `json:"executive_summary"`
	KeyFindings        string  `json:"key_findings"`
	Recommendations    string  `json:"recommendations"`
	ActionPlan         string  `json:"action_plan"`
	DetailedData       string  `json:"detailed_data"`
}
