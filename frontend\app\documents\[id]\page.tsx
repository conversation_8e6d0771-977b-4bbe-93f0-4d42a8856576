'use client'

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  DocumentTextIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  TagIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ShareIcon,
  PrinterIcon,
  ArrowLeftIcon,
  ClockIcon,
  UserIcon,
  ChatBubbleLeftIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import MarkdownRenderer from '../../components/MarkdownRenderer/MarkdownRenderer';
import { Document } from '../../types';

const DocumentDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [document, setDocument] = useState<Document | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [comments, setComments] = useState<any[]>([]);
  const [newComment, setNewComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);
  
  const documentId = params?.id as string;
  const documentIdNumber = parseInt(documentId);

  useEffect(() => {
    if (!documentId) return;

    const fetchDocument = async () => {
      try {
        setLoading(true);
        const response = await apiService.getDocument(parseInt(documentId));
        setDocument(response);

        // Fetch comments if user is authenticated
        if (isAuthenticated) {
          try {
            const commentsResponse = await apiService.getDocumentComments(parseInt(documentId));
            setComments(commentsResponse.data || []);
          } catch (err) {
            // Comments might not be available for all documents
            console.log('Comments not available');
          }
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch document');
      } finally {
        setLoading(false);
      }
    };

    fetchDocument();
  }, [documentId, isAuthenticated]);

  const handleDelete = async () => {
    if (!document || !confirm('Are you sure you want to delete this document?')) return;
    
    try {
      await apiService.deleteDocument(documentIdNumber );
      router.push('/documents');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete document');
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !document) return;

    try {
      setSubmittingComment(true);
      await apiService.createPublicComment(documentIdNumber, {
        commenter_name: user?.first_name + ' ' + user?.last_name || 'Anonymous',
        commenter_email: user?.email,
        content: newComment
      });
      
      // Refresh comments
      const commentsResponse = await apiService.getDocumentComments(documentIdNumber);
      setComments(commentsResponse.data || []);
      setNewComment('');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to submit comment');
    } finally {
      setSubmittingComment(false);
    }
  };

  const canEdit = () => {
    if (!user || !document) return false;
    return user.role === 'admin' || user.role === 'editor' || document.created_by_id === user.id;
  };

  const canDelete = () => {
    if (!user || !document) return false;
    return user.role === 'admin' || document.created_by_id === user.id;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'rule': return 'bg-blue-100 text-blue-800';
      case 'proposed_rule': return 'bg-purple-100 text-purple-800';
      case 'notice': return 'bg-orange-100 text-orange-800';
      case 'presidential_document': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !document) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Document not found</h3>
            <p className="text-gray-600 mb-4">{error || 'The requested document could not be found.'}</p>
            <Link
              href="/documents"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Documents
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/documents"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Documents
          </Link>
          
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-3">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                  {document.status?.toUpperCase()}
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(document.type)}`}>
                  {document.type?.replace('_', ' ').toUpperCase()}
                </span>
                {document.significant_rule && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    SIGNIFICANT
                  </span>
                )}
                {document.accepts_comments && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    COMMENTS OPEN
                  </span>
                )}
                {document.small_entity_impact && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    SMALL ENTITY IMPACT
                  </span>
                )}
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  document.visibility_level === 1 ? 'bg-green-100 text-green-800' :
                  document.visibility_level === 2 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {document.visibility_level === 1 ? 'PUBLIC' :
                   document.visibility_level === 2 ? 'RESTRICTED' : 'CONFIDENTIAL'}
                </span>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{document.title}</h1>
              
              {document.abstract && (
                <p className="text-lg text-gray-600 mb-6">{document.abstract}</p>
              )}
            </div>
            
            {/* Actions */}
            <div className="flex items-center space-x-2 mt-4 lg:mt-0">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Share">
                <ShareIcon className="h-5 w-5" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Print">
                <PrinterIcon className="h-5 w-5" />
              </button>
              {canEdit() && (
                <Link
                  href={`/documents/${documentId}/edit`}
                  className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                  title="Edit Document"
                >
                  <PencilIcon className="h-5 w-5" />
                </Link>
              )}
              {canDelete() && (
                <button
                  onClick={handleDelete}
                  className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  title="Delete Document"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Abstract */}
            {document.abstract && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 className="text-lg font-semibold text-blue-900 mb-3">Abstract</h2>
                <MarkdownRenderer
                  content={document.abstract}
                  className="text-blue-800 prose prose-sm max-w-none"
                />
              </div>
            )}

            {/* Document Content */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Document Content</h2>
              <MarkdownRenderer
                content={document.content}
                className="prose prose-lg max-w-none"
              />
            </div>

            {/* Comments Section */}
            {document.status === 'published' && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Public Comments ({comments.length})
                </h2>
                
                {/* Comment Form */}
                {isAuthenticated && document.comment_due_date && new Date(document.comment_due_date) > new Date() && (
                  <form onSubmit={handleSubmitComment} className="mb-6">
                    <textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                      placeholder="Submit your public comment..."
                      required
                    />
                    <div className="mt-3 flex justify-end">
                      <button
                        type="submit"
                        disabled={submittingComment}
                        className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
                      >
                        {submittingComment ? 'Submitting...' : 'Submit Comment'}
                      </button>
                    </div>
                  </form>
                )}

                {/* Comments List */}
                <div className="space-y-4">
                  {comments.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No comments yet.</p>
                  ) : (
                    comments.map((comment) => (
                      <div key={comment.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                            <span className="text-sm font-medium text-gray-900">
                              {comment.author?.first_name} {comment.author?.last_name}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {new Date(comment.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{comment.content}</p>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Information</h3>
              
              <div className="space-y-4">
                {document.agency && (
                  <div className="flex items-start">
                    <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Agency</p>
                      <p className="text-sm text-gray-600">{typeof document.agency === 'string' ? document.agency : document.agency?.name || 'Unknown Agency'}</p>
                    </div>
                  </div>
                )}

                {document.categories && document.categories.length > 0 && (
                  <div className="flex items-start">
                    <TagIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Categories</p>
                      <div className="space-y-1">
                        {document.categories.map((category) => (
                          <Link
                            key={category.id}
                            href={`/categories/${category.id}`}
                            className="block text-sm text-primary-600 hover:text-primary-700"
                          >
                            {category.name}
                          </Link>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {document.publication_date && (
                  <div className="flex items-start">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Publication Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(document.publication_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {document.effective_date && (
                  <div className="flex items-start">
                    <ClockIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Effective Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(document.effective_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {document.comment_due_date && (
                  <div className="flex items-start">
                    <ChatBubbleLeftIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Comment Period Ends</p>
                      <p className="text-sm text-gray-600">
                        {new Date(document.comment_due_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {document.termination_date && (
                  <div className="flex items-start">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Termination Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(document.termination_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {document.fr_document_number && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">FR Document Number</p>
                    <p className="text-sm text-gray-600">{document.fr_document_number}</p>
                  </div>
                )}

                {document.fr_citation && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">FR Citation</p>
                    <p className="text-sm text-gray-600">{document.fr_citation}</p>
                  </div>
                )}

                {document.docket_number && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Docket Number</p>
                    <p className="text-sm text-gray-600">{document.docket_number}</p>
                  </div>
                )}

                {document.regulatory_identifier && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Regulatory Identifier (RIN)</p>
                    <p className="text-sm text-gray-600">{document.regulatory_identifier}</p>
                  </div>
                )}

                {document.cfr_citations && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">CFR Citations</p>
                    <p className="text-sm text-gray-600">{document.cfr_citations}</p>
                  </div>
                )}

                {document.economic_impact && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Economic Impact</p>
                    <p className="text-sm text-gray-600">{document.economic_impact}</p>
                  </div>
                )}

                {document.workflow_stage && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Workflow Stage</p>
                    <p className="text-sm text-gray-600">{document.workflow_stage}</p>
                  </div>
                )}

                <div className="border-t border-gray-200 pt-4 mt-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="font-medium text-gray-900">Views</p>
                      <p className="text-gray-600">{document.view_count || 0}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Downloads</p>
                      <p className="text-gray-600">{document.download_count || 0}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Comments</p>
                      <p className="text-gray-600">{document.comment_count || 0}</p>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">Language</p>
                      <p className="text-gray-600">{document.language || 'en'}</p>
                    </div>
                    {document.page_count && (
                      <div>
                        <p className="font-medium text-gray-900">Pages</p>
                        <p className="text-gray-600">{document.page_count}</p>
                      </div>
                    )}
                    {document.word_count && (
                      <div>
                        <p className="font-medium text-gray-900">Words</p>
                        <p className="text-gray-600">{document.word_count.toLocaleString()}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Tags and Subjects */}
            {((document.tags && document.tags.length > 0) || (document.subjects && document.subjects.length > 0)) && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                {document.tags && document.tags.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Tags</h3>
                    <div className="flex flex-wrap gap-2">
                      {document.tags.map((tag) => (
                        <Link
                          key={tag.id}
                          href={`/tags/${tag.id}`}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200"
                        >
                          {tag.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}

                {document.subjects && document.subjects.length > 0 && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3">Subjects</h3>
                    <div className="flex flex-wrap gap-2">
                      {document.subjects.map((subject) => (
                        <Link
                          key={subject.id}
                          href={`/subjects/${subject.id}`}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200"
                        >
                          {subject.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Additional Information */}
            {(document.comment_instructions || document.public_hearing_info) && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Information</h3>

                {document.comment_instructions && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Comment Instructions</h4>
                    <MarkdownRenderer
                      content={document.comment_instructions}
                      className="text-sm text-gray-600 prose prose-sm max-w-none"
                    />
                  </div>
                )}

                {document.public_hearing_info && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">Public Hearing Information</h4>
                    <MarkdownRenderer
                      content={document.public_hearing_info}
                      className="text-sm text-gray-600 prose prose-sm max-w-none"
                    />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default DocumentDetailPage;
