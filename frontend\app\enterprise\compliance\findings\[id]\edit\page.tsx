'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { complianceApi } from '../../../../../services/enterpriseApi';

interface ComplianceFinding {
  id: number;
  created_at: string;
  updated_at: string;
  finding_code: string;
  title: string;
  description?: string;
  assessment_id?: number;
  requirement_id?: number;
  severity: string;
  category: string;
  status: string;
  identified_date: string;
  due_date?: string;
  resolved_date?: string;
  assigned_to_id?: number;
  evidence?: string;
  impact?: string;
  recommendation?: string;
  remediation_plan?: string;
  cost_to_fix?: number;
  currency_code?: string;
  risk_rating?: string;
  compliance_gap?: string;
  root_cause?: string;
  recurrence_risk?: string;
  validation_method?: string;
  is_systemic?: boolean;
  metadata?: string;
}

const EditComplianceFindingPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const findingId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [assessments, setAssessments] = useState<any[]>([]);
  const [requirements, setRequirements] = useState<any[]>([]);
  const [formData, setFormData] = useState<Partial<ComplianceFinding>>({
    finding_code: '',
    title: '',
    description: '',
    assessment_id: undefined,
    requirement_id: undefined,
    severity: 'medium',
    category: 'compliance',
    status: 'open',
    identified_date: '',
    due_date: '',
    resolved_date: '',
    assigned_to_id: undefined,
    evidence: '',
    impact: '',
    recommendation: '',
    remediation_plan: '',
    cost_to_fix: 0,
    currency_code: 'USD',
    risk_rating: 'medium',
    compliance_gap: '',
    root_cause: '',
    recurrence_risk: 'low',
    validation_method: '',
    is_systemic: false,
    metadata: ''
  });

  useEffect(() => {
    if (findingId) {
      fetchFinding();
      fetchAssessments();
      fetchRequirements();
    }
  }, [findingId]);

  const fetchFinding = async () => {
    try {
      setFetchLoading(true);
      const response = await complianceApi.getFinding(findingId);
      const finding = response.data;
      setFormData({
        ...finding,
        identified_date: finding.identified_date?.split('T')[0] || '',
        due_date: finding.due_date?.split('T')[0] || '',
        resolved_date: finding.resolved_date?.split('T')[0] || '',
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch compliance finding');
    } finally {
      setFetchLoading(false);
    }
  };

  const fetchAssessments = async () => {
    try {
      const response = await complianceApi.getAssessments();
      setAssessments(response.data);
    } catch (err: any) {
      console.log('Failed to fetch assessments:', err.message);
    }
  };

  const fetchRequirements = async () => {
    try {
      const response = await complianceApi.getRequirements();
      setRequirements(response.data);
    } catch (err: any) {
      console.log('Failed to fetch requirements:', err.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      await complianceApi.updateFinding(findingId, formData);
      router.push(`/enterprise/compliance/findings/${findingId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update compliance finding');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  if (fetchLoading) return <div className="p-6">Loading compliance finding...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Compliance Finding</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/compliance/findings/${findingId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Finding Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Finding Code *
            </label>
            <input
              type="text"
              name="finding_code"
              value={formData.finding_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Severity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Severity *
            </label>
            <select
              name="severity"
              value={formData.severity}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="closed">Closed</option>
              <option value="deferred">Deferred</option>
            </select>
          </div>

          {/* Due Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Due Date
            </label>
            <input
              type="date"
              name="due_date"
              value={formData.due_date}
              onChange={handleChange}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Cost to Fix */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cost to Fix
            </label>
            <input
              type="number"
              name="cost_to_fix"
              value={formData.cost_to_fix}
              onChange={handleChange}
              step="0.01"
              min="0"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Text Areas */}
        <div className="mt-6 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Recommendation
            </label>
            <textarea
              name="recommendation"
              value={formData.recommendation}
              onChange={handleChange}
              rows={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Remediation Plan
            </label>
            <textarea
              name="remediation_plan"
              value={formData.remediation_plan}
              onChange={handleChange}
              rows={3}
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/compliance/findings/${findingId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Finding'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditComplianceFindingPage;
