package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

var authToken string

func main() {
	fmt.Println("🚀 Testing Fixed Tasks Endpoint")
	fmt.Println("===============================")

	// Step 1: Authenticate
	if !authenticate() {
		fmt.Println("❌ Authentication failed. Exiting.")
		return
	}

	// Step 2: Test tasks endpoint
	fmt.Println("\n📋 Testing Tasks...")
	testTasks()
}

func authenticate() bool {
	loginData := map[string]string{
		"identifier": "<EMAIL>",
		"password":   "password",
	}

	jsonData, _ := json.Marshal(loginData)
	resp, err := http.Post("http://127.0.0.1:8080/api/v1/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Login error: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	if resp.StatusCode != 200 {
		fmt.Printf("Login failed: %d - %s\n", resp.StatusCode, string(body))
		return false
	}

	var loginResponse map[string]interface{}
	json.Unmarshal(body, &loginResponse)

	if token, ok := loginResponse["token"].(string); ok {
		authToken = token
		fmt.Println("✅ Authentication successful")
		return true
	}

	fmt.Println("❌ No token in login response")
	return false
}

func testTasks() {
	req, _ := http.NewRequest("GET", "http://127.0.0.1:8080/api/v1/tasks", nil)
	req.Header.Set("Authorization", "Bearer "+authToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("  ❌ Request error: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		fmt.Printf("  ✅ GET /tasks: Status %d\n", resp.StatusCode)
		
		// Parse response to see how many tasks we got
		var response map[string]interface{}
		if err := json.Unmarshal(body, &response); err == nil {
			if data, ok := response["data"].([]interface{}); ok {
				fmt.Printf("  📊 Found %d tasks\n", len(data))
			}
		}
	} else {
		fmt.Printf("  ❌ GET /tasks: Status %d\n", resp.StatusCode)
		fmt.Printf("  📝 Response: %s\n", string(body))
	}
}
