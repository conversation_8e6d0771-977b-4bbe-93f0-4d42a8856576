package handlers

import (
	"fmt"
	"net/http"
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GetProceedingDefaults returns default values for proceeding creation
func GetProceedingDefaults(c *gin.Context) {
	defaults := gin.H{
		"status":      "draft",
		"is_active":   true,
		"visibility":  "internal",
		"priority":    "medium",
		"auto_assign": false,
	}

	c.<PERSON>(http.StatusOK, SuccessResponse{
		Message: "Proceeding defaults retrieved successfully",
		Data:    defaults,
	})
}

// GetFinanceDefaults returns default values for finance creation
func GetFinanceDefaults(c *gin.Context) {
	defaults := gin.H{
		"currency":    "USD",
		"fiscal_year": time.Now().Year(),
		"status":      "draft",
		"is_approved": false,
		"budget_type": "operational",
	}

	c.<PERSON>(http.StatusOK, SuccessResponse{
		Message: "Finance defaults retrieved successfully",
		Data:    defaults,
	})
}

// GenerateSlug generates a URL-friendly slug from text
func GenerateSlug(c *gin.Context) {
	text := c.Query("text")
	if text == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing text parameter",
			Message: "Query parameter 'text' is required",
		})
		return
	}

	slug := generateSlugFromText(text)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Slug generated successfully",
		Data: gin.H{
			"text": text,
			"slug": slug,
		},
	})
}

// generateSlugFromText creates a URL-friendly slug from a string
func generateSlugFromText(text string) string {
	// Convert to lowercase
	slug := strings.ToLower(text)

	// Replace spaces and special characters with hyphens
	reg := regexp.MustCompile(`[^a-z0-9]+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	return slug
}

// GenerateFRNumber generates a Federal Register number
func GenerateFRNumber(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get current year
	year := time.Now().Year()

	// Count documents for this year to generate sequential number
	var count int64
	db.Model(&models.Document{}).
		Where("EXTRACT(YEAR FROM created_at) = ?", year).
		Count(&count)

	// Generate FR number in format: YYYY-XXX-XXXX
	frNumber := fmt.Sprintf("%d-%03d-%04d", year, 1, count+1)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "FR number generated successfully",
		Data: gin.H{
			"fr_number": frNumber,
			"year":      year,
			"sequence":  count + 1,
		},
	})
}

// GenerateDocketNumber generates a docket number
func GenerateDocketNumber(c *gin.Context) {
	agencyID := c.Query("agency_id")
	categoryID := c.Query("category_id")

	if agencyID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing agency_id parameter",
			Message: "Query parameter 'agency_id' is required",
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get agency
	var agency models.Agency
	if err := db.First(&agency, agencyID).Error; err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid agency",
			Message: "Agency not found",
		})
		return
	}

	// Get category if provided
	var categoryCode string = "GEN"
	if categoryID != "" {
		var category models.Category
		if err := db.First(&category, categoryID).Error; err == nil {
			// Use first 3 characters of category name as code
			categoryCode = strings.ToUpper(category.Name)
			if len(categoryCode) > 3 {
				categoryCode = categoryCode[:3]
			}
		}
	}

	// Count documents for this agency to generate sequential number
	var count int64
	db.Model(&models.Document{}).
		Where("agency_id = ?", agencyID).
		Count(&count)

	// Generate docket number in format: AGENCY-CATEGORY-YYYY-XXXX
	year := time.Now().Year()
	agencyCode := strings.ToUpper(agency.Name)
	if len(agencyCode) > 4 {
		agencyCode = agencyCode[:4]
	}

	docketNumber := fmt.Sprintf("%s-%s-%d-%04d", agencyCode, categoryCode, year, count+1)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Docket number generated successfully",
		Data: gin.H{
			"docket_number": docketNumber,
			"agency_code":   agencyCode,
			"category_code": categoryCode,
			"year":          year,
			"sequence":      count + 1,
		},
	})
}

// GetPublicLawNumber generates a public law number
func GetPublicLawNumber(c *gin.Context) {
	// Get current year and congress session
	year := time.Now().Year()
	congress := ((year - 1789) / 2) + 1

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Count laws for this congress session
	var count int64
	db.Model(&models.LawsAndRules{}).
		Where("type = ? AND EXTRACT(YEAR FROM created_at) >= ?", "law", year-1).
		Count(&count)

	// Generate public law number in format: Pub. L. XXX-XXX
	publicLawNumber := fmt.Sprintf("Pub. L. %d-%d", congress, count+1)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Public law number generated successfully",
		Data: gin.H{
			"public_law_number": publicLawNumber,
			"congress":          congress,
			"year":              year,
			"sequence":          count + 1,
		},
	})
}

// GetRegulatoryIdentifier generates a regulatory identifier
func GetRegulatoryIdentifier(c *gin.Context) {
	agencyID := c.Query("agency_id")

	if agencyID == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing agency_id parameter",
			Message: "Query parameter 'agency_id' is required",
		})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get agency
	var agency models.Agency
	if err := db.First(&agency, agencyID).Error; err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid agency",
			Message: "Agency not found",
		})
		return
	}

	// Count regulations for this agency
	var count int64
	db.Model(&models.LawsAndRules{}).
		Where("type = ? AND agency_id = ?", "regulation", agencyID).
		Count(&count)

	// Generate regulatory identifier in format: XXXX-XXXX
	year := time.Now().Year()
	agencyCode := fmt.Sprintf("%04d", agency.ID)
	sequenceCode := fmt.Sprintf("%04d", count+1)

	regulatoryIdentifier := fmt.Sprintf("%s-%s", agencyCode, sequenceCode)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulatory identifier generated successfully",
		Data: gin.H{
			"regulatory_identifier": regulatoryIdentifier,
			"agency_code":           agencyCode,
			"sequence_code":         sequenceCode,
			"year":                  year,
		},
	})
}

// GetRegulationDocketNumber generates a regulation docket number
func GetRegulationDocketNumber(c *gin.Context) {
	// This is similar to GenerateDocketNumber but specifically for regulations
	GenerateDocketNumber(c)
}
