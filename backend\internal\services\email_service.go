package services

import (
	"crypto/tls"
	"fmt"
	"net/smtp"
	"time"

	"federal-register-clone/internal/config"
)

// EmailService handles email operations
type EmailService struct {
	config *config.Config
	auth   smtp.Auth
}

// NewEmailService creates a new email service
func NewEmailService(cfg *config.Config) *EmailService {
	var auth smtp.Auth
	if cfg.Email.SMTPUsername != "" && cfg.Email.SMTPPassword != "" {
		auth = smtp.PlainAuth("", cfg.Email.SMTPUsername, cfg.Email.SMTPPassword, cfg.Email.SMTPHost)
	}

	return &EmailService{
		config: cfg,
		auth:   auth,
	}
}

// EmailTemplate represents an email template
type EmailTemplate struct {
	Subject string
	Body    string
}

// GetPasswordResetTemplate returns the password reset email template
func (s *EmailService) GetPasswordResetTemplate(resetURL, username string) EmailTemplate {
	subject := "Password Reset Request - NoteControl"
	body := fmt.Sprintf(`
Dear %s,

You have requested to reset your password for your NoteControl account.

Please click the link below to reset your password:
%s

This link will expire in 1 hour for security reasons.

If you did not request this password reset, please ignore this email and your password will remain unchanged.

For security reasons, please do not share this link with anyone.

Best regards,
The NoteControl Team

---
This is an automated message. Please do not reply to this email.
`, username, resetURL)

	return EmailTemplate{
		Subject: subject,
		Body:    body,
	}
}

// GetEmailVerificationTemplate returns the email verification template
func (s *EmailService) GetEmailVerificationTemplate(verificationURL, username string) EmailTemplate {
	subject := "Email Verification - NoteControl"
	body := fmt.Sprintf(`
Dear %s,

Thank you for registering with NoteControl!

Please click the link below to verify your email address:
%s

This link will expire in 24 hours.

If you did not create this account, please ignore this email.

Best regards,
The NoteControl Team

---
This is an automated message. Please do not reply to this email.
`, username, verificationURL)

	return EmailTemplate{
		Subject: subject,
		Body:    body,
	}
}

// GetContactFormNotificationTemplate returns the contact form notification template
func (s *EmailService) GetContactFormNotificationTemplate(name, email, subject, message string) EmailTemplate {
	emailSubject := fmt.Sprintf("New Contact Form Submission: %s", subject)
	body := fmt.Sprintf(`
New contact form submission received:

Name: %s
Email: %s
Subject: %s

Message:
%s

Submitted at: %s

Please respond to this inquiry promptly.

---
NoteControl Contact Form System
`, name, email, subject, message, time.Now().Format("2006-01-02 15:04:05 UTC"))

	return EmailTemplate{
		Subject: emailSubject,
		Body:    body,
	}
}

// SendEmail sends an email using SMTP
func (s *EmailService) SendEmail(to, subject, body string) error {
	if s.config.Email.SMTPHost == "" || s.config.Email.SMTPUsername == "" {
		return fmt.Errorf("email service not configured")
	}

	// Create message
	msg := s.buildMessage(s.config.Email.FromEmail, to, subject, body)

	// Connect to SMTP server
	addr := fmt.Sprintf("%s:%s", s.config.Email.SMTPHost, s.config.Email.SMTPPort)

	// Create TLS config
	tlsConfig := &tls.Config{
		ServerName: s.config.Email.SMTPHost,
	}

	// Connect to server
	conn, err := tls.Dial("tcp", addr, tlsConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer conn.Close()

	// Create SMTP client
	client, err := smtp.NewClient(conn, s.config.Email.SMTPHost)
	if err != nil {
		return fmt.Errorf("failed to create SMTP client: %w", err)
	}
	defer client.Quit()

	// Authenticate if credentials are provided
	if s.auth != nil {
		if err := client.Auth(s.auth); err != nil {
			return fmt.Errorf("SMTP authentication failed: %w", err)
		}
	}

	// Set sender
	if err := client.Mail(s.config.Email.FromEmail); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}

	// Set recipient
	if err := client.Rcpt(to); err != nil {
		return fmt.Errorf("failed to set recipient: %w", err)
	}

	// Send message
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to get data writer: %w", err)
	}
	defer writer.Close()

	if _, err := writer.Write([]byte(msg)); err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	return nil
}

// SendPasswordResetEmail sends a password reset email
func (s *EmailService) SendPasswordResetEmail(to, username, resetToken string) error {
	resetURL := fmt.Sprintf("%s/reset-password?token=%s", s.config.Server.BaseURL, resetToken)
	template := s.GetPasswordResetTemplate(resetURL, username)
	return s.SendEmail(to, template.Subject, template.Body)
}

// SendEmailVerificationEmail sends an email verification email
func (s *EmailService) SendEmailVerificationEmail(to, username, verificationToken string) error {
	verificationURL := fmt.Sprintf("%s/verify-email?token=%s", s.config.Server.BaseURL, verificationToken)
	template := s.GetEmailVerificationTemplate(verificationURL, username)
	return s.SendEmail(to, template.Subject, template.Body)
}

// SendContactFormNotification sends a notification about a new contact form submission
func (s *EmailService) SendContactFormNotification(adminEmail, name, email, subject, message string) error {
	template := s.GetContactFormNotificationTemplate(name, email, subject, message)
	return s.SendEmail(adminEmail, template.Subject, template.Body)
}

// buildMessage builds the email message with proper headers
func (s *EmailService) buildMessage(from, to, subject, body string) string {
	headers := make(map[string]string)
	headers["From"] = from
	headers["To"] = to
	headers["Subject"] = subject
	headers["MIME-Version"] = "1.0"
	headers["Content-Type"] = "text/plain; charset=utf-8"
	headers["Date"] = time.Now().Format(time.RFC1123Z)

	message := ""
	for k, v := range headers {
		message += fmt.Sprintf("%s: %s\r\n", k, v)
	}
	message += "\r\n" + body

	return message
}

// SendBulkEmail sends emails to multiple recipients
func (s *EmailService) SendBulkEmail(recipients []string, subject, body string) error {
	for _, recipient := range recipients {
		if err := s.SendEmail(recipient, subject, body); err != nil {
			return fmt.Errorf("failed to send email to %s: %w", recipient, err)
		}
		// Add small delay to avoid overwhelming SMTP server
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}
