package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// Document Processing handlers

var documentProcessingService *services.DocumentProcessingService

// CreateProcessingJob creates a new document processing job
func CreateProcessingJob(c *gin.Context) {
	var req struct {
		Name            string                `json:"name" binding:"required"`
		Type            models.ProcessingType `json:"type" binding:"required"`
		Priority        int                   `json:"priority"`
		DocumentID      uint                  `json:"document_id" binding:"required"`
		FileID          *uint                 `json:"file_id"`
		Engine          string                `json:"engine"`
		Configuration   string                `json:"configuration"`
		InputFormat     string                `json:"input_format"`
		OutputFormat    string                `json:"output_format"`
		Language        string                `json:"language"`
		OCRConfidence   float64               `json:"ocr_confidence"`
		ExtractImages   bool                  `json:"extract_images"`
		ExtractTables   bool                  `json:"extract_tables"`
		ExtractMetadata bool                  `json:"extract_metadata"`
		PreprocessImage bool                  `json:"preprocess_image"`
		MaxRetries      int                   `json:"max_retries"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Validate document exists
	var document models.Document
	if err := db.First(&document, req.DocumentID).Error; err != nil {
		HandleBadRequest(c, "Invalid document ID")
		return
	}

	// Validate file if provided
	if req.FileID != nil {
		var file models.DocumentFile
		if err := db.Where("id = ? AND document_id = ?", *req.FileID, req.DocumentID).First(&file).Error; err != nil {
			HandleBadRequest(c, "Invalid file ID or file does not belong to document")
			return
		}
	}

	// Set defaults
	if req.Priority == 0 {
		req.Priority = 5 // Medium priority
	}
	if req.Language == "" {
		req.Language = "en"
	}
	if req.OCRConfidence == 0 {
		req.OCRConfidence = 0.8
	}
	if req.MaxRetries == 0 {
		req.MaxRetries = 3
	}
	if req.Engine == "" {
		req.Engine = "default"
	}

	// Generate job ID
	jobID := fmt.Sprintf("job_%d_%d", time.Now().Unix(), req.DocumentID)

	// Create processing job
	job := models.DocumentProcessingJob{
		JobID:           jobID,
		Name:            req.Name,
		Type:            req.Type,
		Status:          models.ProcessingStatusPending,
		Priority:        req.Priority,
		DocumentID:      req.DocumentID,
		FileID:          req.FileID,
		Engine:          req.Engine,
		Configuration:   req.Configuration,
		InputFormat:     req.InputFormat,
		OutputFormat:    req.OutputFormat,
		Language:        req.Language,
		OCRConfidence:   req.OCRConfidence,
		ExtractImages:   req.ExtractImages,
		ExtractTables:   req.ExtractTables,
		ExtractMetadata: req.ExtractMetadata,
		PreprocessImage: req.PreprocessImage,
		MaxRetries:      req.MaxRetries,
		CreatedByID:     userID.(uint),
	}

	if err := db.Create(&job).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create processing job",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Processing job created successfully",
		Data: gin.H{
			"job_id":      job.JobID,
			"id":          job.ID,
			"name":        job.Name,
			"type":        job.Type,
			"status":      job.Status,
			"priority":    job.Priority,
			"document_id": job.DocumentID,
			"created_at":  job.CreatedAt,
		},
	})
}

// GetProcessingJobs returns a list of processing jobs
func GetProcessingJobs(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get pagination parameters
	pagination := GetPaginationParams(c)

	// Build query
	query := db.Model(&models.DocumentProcessingJob{})

	// Apply filters
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if jobType := c.Query("type"); jobType != "" {
		query = query.Where("type = ?", jobType)
	}

	if documentID := c.Query("document_id"); documentID != "" {
		query = query.Where("document_id = ?", documentID)
	}

	if priority := c.Query("priority"); priority != "" {
		query = query.Where("priority = ?", priority)
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to count processing jobs",
			Message: err.Error(),
		})
		return
	}

	// Get jobs with pagination
	var jobs []models.DocumentProcessingJob
	offset := (pagination.Page - 1) * pagination.PerPage
	if err := query.Preload("Document").
		Preload("File").
		Preload("CreatedBy").
		Preload("ProcessedBy").
		Order("priority DESC, created_at DESC").
		Offset(offset).
		Limit(pagination.PerPage).
		Find(&jobs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch processing jobs",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	jobResponses := make([]gin.H, len(jobs))
	for i, job := range jobs {
		response := gin.H{
			"id":               job.ID,
			"job_id":           job.JobID,
			"name":             job.Name,
			"type":             job.Type,
			"status":           job.Status,
			"priority":         job.Priority,
			"document_id":      job.DocumentID,
			"file_id":          job.FileID,
			"engine":           job.Engine,
			"input_format":     job.InputFormat,
			"output_format":    job.OutputFormat,
			"language":         job.Language,
			"ocr_confidence":   job.OCRConfidence,
			"extract_images":   job.ExtractImages,
			"extract_tables":   job.ExtractTables,
			"extract_metadata": job.ExtractMetadata,
			"preprocess_image": job.PreprocessImage,
			"started_at":       job.StartedAt,
			"completed_at":     job.CompletedAt,
			"processing_time":  job.ProcessingTime,
			"retry_count":      job.RetryCount,
			"max_retries":      job.MaxRetries,
			"confidence":       job.Confidence,
			"page_count":       job.PageCount,
			"word_count":       job.WordCount,
			"character_count":  job.CharacterCount,
			"error_message":    job.ErrorMessage,
			"created_at":       job.CreatedAt,
			"updated_at":       job.UpdatedAt,
		}

		if job.Document.ID != 0 {
			response["document"] = gin.H{
				"id":    job.Document.ID,
				"title": job.Document.Title,
				"slug":  job.Document.Slug,
			}
		}

		if job.File != nil && job.File.ID != 0 {
			response["file"] = gin.H{
				"id":            job.File.ID,
				"filename":      job.File.FileName,
				"original_name": job.File.OriginalName,
				"file_size":     job.File.FileSize,
				"mime_type":     job.File.MimeType,
			}
		}

		if job.CreatedBy.ID != 0 {
			response["created_by"] = gin.H{
				"id":    job.CreatedBy.ID,
				"email": job.CreatedBy.Email,
			}
		}

		if job.ProcessedBy != nil && job.ProcessedBy.ID != 0 {
			response["processed_by"] = gin.H{
				"id":    job.ProcessedBy.ID,
				"email": job.ProcessedBy.Email,
			}
		}

		jobResponses[i] = response
	}

	// Create paginated response
	response := CreatePaginationResponse(jobResponses, total, pagination.Page, pagination.PerPage)
	c.JSON(http.StatusOK, response)
}

// GetProcessingJob returns a specific processing job
func GetProcessingJob(c *gin.Context) {
	jobID := c.Param("job_id")
	if jobID == "" {
		HandleBadRequest(c, "Job ID is required")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find job
	var job models.DocumentProcessingJob
	if err := db.Where("job_id = ?", jobID).
		Preload("Document").
		Preload("File").
		Preload("CreatedBy").
		Preload("ProcessedBy").
		First(&job).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Processing job")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch processing job",
			Message: err.Error(),
		})
		return
	}

	// Build response
	response := gin.H{
		"id":               job.ID,
		"job_id":           job.JobID,
		"name":             job.Name,
		"type":             job.Type,
		"status":           job.Status,
		"priority":         job.Priority,
		"document_id":      job.DocumentID,
		"file_id":          job.FileID,
		"engine":           job.Engine,
		"configuration":    job.Configuration,
		"input_format":     job.InputFormat,
		"output_format":    job.OutputFormat,
		"language":         job.Language,
		"ocr_confidence":   job.OCRConfidence,
		"extract_images":   job.ExtractImages,
		"extract_tables":   job.ExtractTables,
		"extract_metadata": job.ExtractMetadata,
		"preprocess_image": job.PreprocessImage,
		"started_at":       job.StartedAt,
		"completed_at":     job.CompletedAt,
		"processing_time":  job.ProcessingTime,
		"retry_count":      job.RetryCount,
		"max_retries":      job.MaxRetries,
		"extracted_text":   job.ExtractedText,
		"confidence":       job.Confidence,
		"page_count":       job.PageCount,
		"word_count":       job.WordCount,
		"character_count":  job.CharacterCount,
		"output_data":      job.OutputData,
		"output_files":     job.OutputFiles,
		"error_message":    job.ErrorMessage,
		"error_details":    job.ErrorDetails,
		"last_error":       job.LastError,
		"created_at":       job.CreatedAt,
		"updated_at":       job.UpdatedAt,
	}

	if job.Document.ID != 0 {
		response["document"] = gin.H{
			"id":    job.Document.ID,
			"title": job.Document.Title,
			"slug":  job.Document.Slug,
			"type":  job.Document.Type,
		}
	}

	if job.File != nil && job.File.ID != 0 {
		response["file"] = gin.H{
			"id":            job.File.ID,
			"filename":      job.File.FileName,
			"original_name": job.File.OriginalName,
			"file_size":     job.File.FileSize,
			"mime_type":     job.File.MimeType,
		}
	}

	if job.CreatedBy.ID != 0 {
		response["created_by"] = gin.H{
			"id":    job.CreatedBy.ID,
			"email": job.CreatedBy.Email,
		}
	}

	if job.ProcessedBy != nil && job.ProcessedBy.ID != 0 {
		response["processed_by"] = gin.H{
			"id":    job.ProcessedBy.ID,
			"email": job.ProcessedBy.Email,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Processing job retrieved successfully",
		Data:    response,
	})
}

// StartProcessingJob starts a processing job
func StartProcessingJob(c *gin.Context) {
	jobID := c.Param("job_id")
	if jobID == "" {
		HandleBadRequest(c, "Job ID is required")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Find job
	var job models.DocumentProcessingJob
	if err := db.Where("job_id = ?", jobID).First(&job).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Processing job")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch processing job",
			Message: err.Error(),
		})
		return
	}

	// Check if job can be started
	if job.Status != models.ProcessingStatusPending {
		HandleBadRequest(c, "Job is not in pending status")
		return
	}

	// Update job status
	now := time.Now()
	processorID := userID.(uint)
	if err := db.Model(&job).Updates(map[string]interface{}{
		"status":          models.ProcessingStatusProcessing,
		"started_at":      &now,
		"processed_by_id": &processorID,
	}).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to start processing job",
			Message: err.Error(),
		})
		return
	}

	// Initialize document processing service if needed
	if documentProcessingService == nil {
		documentProcessingService = services.NewDocumentProcessingService(db)
	}

	// Start real processing job
	if err := documentProcessingService.StartProcessingJob(jobID, 1); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to start processing job",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Processing job started successfully",
		Data: gin.H{
			"job_id":     jobID,
			"status":     models.ProcessingStatusProcessing,
			"started_at": now,
		},
	})
}

// CreateProcessingTemplate creates a processing template
func CreateProcessingTemplate(c *gin.Context) {
	var req struct {
		Name             string   `json:"name" binding:"required"`
		Description      string   `json:"description"`
		ProcessingTypes  []string `json:"processing_types" binding:"required"`
		DefaultEngine    string   `json:"default_engine"`
		Configuration    string   `json:"configuration"`
		SupportedFormats []string `json:"supported_formats"`
		MaxFileSize      int64    `json:"max_file_size"`
		IsActive         bool     `json:"is_active"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("userID")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Properly marshal JSON arrays
	processingTypesJSON, err := json.Marshal(req.ProcessingTypes)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid processing types",
			Message: "Failed to marshal processing types to JSON",
		})
		return
	}

	supportedFormatsJSON, err := json.Marshal(req.SupportedFormats)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid supported formats",
			Message: "Failed to marshal supported formats to JSON",
		})
		return
	}

	// Create template
	template := models.ProcessingTemplate{
		Name:             req.Name,
		Description:      req.Description,
		ProcessingTypes:  string(processingTypesJSON),
		DefaultEngine:    req.DefaultEngine,
		Configuration:    req.Configuration,
		SupportedFormats: string(supportedFormatsJSON),
		MaxFileSize:      req.MaxFileSize,
		IsActive:         req.IsActive,
		CreatedByID:      userID.(uint),
	}

	if err := db.Create(&template).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create processing template",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Processing template created successfully",
		Data: gin.H{
			"id":          template.ID,
			"name":        template.Name,
			"description": template.Description,
			"is_active":   template.IsActive,
			"created_at":  template.CreatedAt,
		},
	})
}

// GetDocumentMetadata returns extracted metadata for a document
func GetDocumentMetadata(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Get extracted metadata
	var metadata []models.ExtractedMetadata
	if err := db.Where("document_id = ?", id).
		Order("created_at DESC").
		Find(&metadata).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document metadata",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	metadataResponses := make([]gin.H, len(metadata))
	for i, meta := range metadata {
		metadataResponses[i] = gin.H{
			"id":            meta.ID,
			"metadata_type": meta.MetadataType,
			"key":           meta.Key,
			"value":         meta.Value,
			"confidence":    meta.Confidence,
			"source":        meta.Source,
			"created_at":    meta.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document metadata retrieved successfully",
		Data:    metadataResponses,
	})
}

// GetDocumentClassification returns classification results for a document
func GetDocumentClassification(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Get classification results
	var classifications []models.DocumentClassification
	if err := db.Where("document_id = ?", id).
		Order("confidence DESC, created_at DESC").
		Find(&classifications).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document classifications",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	classificationResponses := make([]gin.H, len(classifications))
	for i, classification := range classifications {
		classificationResponses[i] = gin.H{
			"id":                 classification.ID,
			"predicted_type":     classification.PredictedType,
			"confidence":         classification.Confidence,
			"predicted_category": classification.PredictedCategory,
			"predicted_agency":   classification.PredictedAgency,
			"model":              classification.ClassificationModel,
			"model_version":      classification.ModelVersion,
			"created_at":         classification.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document classification retrieved successfully",
		Data:    classificationResponses,
	})
}

// GetDocumentEntities returns extracted entities for a document
func GetDocumentEntities(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document",
			Message: err.Error(),
		})
		return
	}

	// Get extracted entities
	var entities []models.ExtractedEntity
	if err := db.Where("document_id = ?", id).
		Order("confidence DESC, start_offset ASC").
		Find(&entities).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch document entities",
			Message: err.Error(),
		})
		return
	}

	// Convert to response format
	entityResponses := make([]gin.H, len(entities))
	for i, entity := range entities {
		entityResponses[i] = gin.H{
			"id":               entity.ID,
			"entity_type":      entity.EntityType,
			"entity_value":     entity.EntityValue,
			"normalized_value": entity.NormalizedValue,
			"confidence":       entity.Confidence,
			"start_offset":     entity.StartOffset,
			"end_offset":       entity.EndOffset,
			"context":          entity.Context,
			"created_at":       entity.CreatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document entities retrieved successfully",
		Data:    entityResponses,
	})
}
