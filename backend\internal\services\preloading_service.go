package services

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/gosimple/slug"
	"gorm.io/gorm"
)

// PreloadingService handles automatic field preloading for all entities
type PreloadingService struct {
	db *gorm.DB
}

// NewPreloadingService creates a new preloading service
func NewPreloadingService(db *gorm.DB) *PreloadingService {
	return &PreloadingService{db: db}
}

// DocumentDefaults represents default values for document creation
type DocumentDefaults struct {
	PublicationDate  *time.Time `json:"publication_date"`
	EffectiveDate    *time.Time `json:"effective_date"`
	CommentDueDate   *time.Time `json:"comment_due_date"`
	FRDocumentNumber string     `json:"fr_document_number"`
	DocketNumber     string     `json:"docket_number"`
	Language         string     `json:"language"`
	OriginalFormat   string     `json:"original_format"`
	Status           string     `json:"status"`
	Type             string     `json:"type"`
	VisibilityLevel  int        `json:"visibility_level"`
	IsPublic         bool       `json:"is_public"`
	AcceptsComments  bool       `json:"accepts_comments"`
}

// RegulationDefaults represents default values for regulation creation
type RegulationDefaults struct {
	EnactmentDate        *time.Time `json:"enactment_date"`
	PublicationDate      *time.Time `json:"publication_date"`
	EffectiveDate        *time.Time `json:"effective_date"`
	VersionNumber        string     `json:"version_number"`
	Status               string     `json:"status"`
	Type                 string     `json:"type"`
	HierarchyLevel       string     `json:"hierarchy_level"`
	IsSignificant        bool       `json:"is_significant"`
	OrderInParent        int        `json:"order_in_parent"`
	PublicLawNumber      string     `json:"public_law_number"`
	RegulatoryIdentifier string     `json:"regulatory_identifier"`
	DocketNumber         string     `json:"docket_number"`
}

// AgencyDefaults represents default values for agency creation
type AgencyDefaults struct {
	EstablishedAt  *time.Time `json:"established_at"`
	Country        string     `json:"country"`
	IsActive       bool       `json:"is_active"`
	AgencyType     string     `json:"agency_type"`
	PrimaryColor   string     `json:"primary_color"`
	SecondaryColor string     `json:"secondary_color"`
}

// CategoryDefaults represents default values for category creation
type CategoryDefaults struct {
	Color     string `json:"color"`
	SortOrder int    `json:"sort_order"`
	IsActive  bool   `json:"is_active"`
}

// ProceedingDefaults represents default values for proceeding creation
type ProceedingDefaults struct {
	InitiationDate          *time.Time `json:"initiation_date"`
	PlannedStartDate        *time.Time `json:"planned_start_date"`
	PlannedEndDate          *time.Time `json:"planned_end_date"`
	UniqueID                string     `json:"unique_id"`
	Status                  string     `json:"status"`
	Priority                string     `json:"priority"`
	RequiresMandatoryReview bool       `json:"requires_mandatory_review"`
	MinimumStepsRequired    int        `json:"minimum_steps_required"`
	SequentialExecution     bool       `json:"sequential_execution"`
	IsPublic                bool       `json:"is_public"`
	TotalSteps              int        `json:"total_steps"`
	CompletedSteps          int        `json:"completed_steps"`
	FailedSteps             int        `json:"failed_steps"`
	ProgressPercent         float64    `json:"progress_percent"`
}

// FinanceDefaults represents default values for finance creation
type FinanceDefaults struct {
	Year                  int     `json:"year"`
	BudgetType            string  `json:"budget_type"`
	PerformancePercentage float64 `json:"performance_percentage"`
	IsAutoCalculated      bool    `json:"is_auto_calculated"`
}

// GetDocumentDefaults returns default values for document creation
func (s *PreloadingService) GetDocumentDefaults(agencyID *uint, categoryIDs []uint) (*DocumentDefaults, error) {
	now := time.Now()
	publicationDate := now
	effectiveDate := now.AddDate(0, 0, 30)  // 30 days after publication
	commentDueDate := now.AddDate(0, 0, 60) // 60 days after publication

	// Generate FR Document Number
	frNumber, err := s.generateFRDocumentNumber()
	if err != nil {
		return nil, err
	}

	// Generate Docket Number
	docketNumber, err := s.generateDocketNumber(agencyID, categoryIDs)
	if err != nil {
		return nil, err
	}

	return &DocumentDefaults{
		PublicationDate:  &publicationDate,
		EffectiveDate:    &effectiveDate,
		CommentDueDate:   &commentDueDate,
		FRDocumentNumber: frNumber,
		DocketNumber:     docketNumber,
		Language:         "en",
		OriginalFormat:   "pdf",
		Status:           "draft",
		Type:             "rule",
		VisibilityLevel:  1,
		IsPublic:         true,
		AcceptsComments:  true,
	}, nil
}

// GetRegulationDefaults returns default values for regulation creation
func (s *PreloadingService) GetRegulationDefaults(parentID *uint, agencyID *uint) (*RegulationDefaults, error) {
	now := time.Now()
	publicationDate := now
	effectiveDate := now.AddDate(0, 0, 30)

	// Generate version number
	versionNumber := "1.0.0"

	// Calculate order in parent
	orderInParent := 0
	if parentID != nil {
		var count int64
		s.db.Table("laws_and_rules").Where("parent_id = ?", *parentID).Count(&count)
		orderInParent = int(count) + 1
	}

	// Generate Public Law Number
	publicLawNumber, err := s.GeneratePublicLawNumber()
	if err != nil {
		return nil, err
	}

	// Generate Regulatory Identifier (RIN)
	regulatoryIdentifier, err := s.GenerateRegulatoryIdentifier(agencyID)
	if err != nil {
		return nil, err
	}

	// Generate Docket Number for regulations
	docketNumber, err := s.GenerateRegulationDocketNumber(agencyID)
	if err != nil {
		return nil, err
	}

	return &RegulationDefaults{
		EnactmentDate:        &now,
		PublicationDate:      &publicationDate,
		EffectiveDate:        &effectiveDate,
		VersionNumber:        versionNumber,
		Status:               "draft",
		Type:                 "regulation",
		HierarchyLevel:       "regulation",
		IsSignificant:        false,
		OrderInParent:        orderInParent,
		PublicLawNumber:      publicLawNumber,
		RegulatoryIdentifier: regulatoryIdentifier,
		DocketNumber:         docketNumber,
	}, nil
}

// GetAgencyDefaults returns default values for agency creation
func (s *PreloadingService) GetAgencyDefaults() *AgencyDefaults {
	now := time.Now()
	return &AgencyDefaults{
		EstablishedAt:  &now,
		Country:        "US",
		IsActive:       true,
		AgencyType:     "federal",
		PrimaryColor:   "#3B82F6",
		SecondaryColor: "#1E40AF",
	}
}

// GetCategoryDefaults returns default values for category creation
func (s *PreloadingService) GetCategoryDefaults() (*CategoryDefaults, error) {
	// Calculate next sort order
	var maxOrder int
	s.db.Table("categories").Select("COALESCE(MAX(sort_order), 0)").Scan(&maxOrder)

	return &CategoryDefaults{
		Color:     "#3B82F6",
		SortOrder: maxOrder + 1,
		IsActive:  true,
	}, nil
}

// GetProceedingDefaults returns default values for proceeding creation
func (s *PreloadingService) GetProceedingDefaults(name string) *ProceedingDefaults {
	now := time.Now()
	plannedEndDate := now.AddDate(0, 0, 90) // 90 days from now

	// Generate unique ID
	uniqueID := fmt.Sprintf("%s %s", name, now.Format("2006-01-02"))

	return &ProceedingDefaults{
		InitiationDate:          &now,
		PlannedStartDate:        &now,
		PlannedEndDate:          &plannedEndDate,
		UniqueID:                uniqueID,
		Status:                  "planning",
		Priority:                "medium",
		RequiresMandatoryReview: true,
		MinimumStepsRequired:    5,
		SequentialExecution:     true,
		IsPublic:                false,
		TotalSteps:              0,
		CompletedSteps:          0,
		FailedSteps:             0,
		ProgressPercent:         0.0,
	}
}

// GetFinanceDefaults returns default values for finance creation
func (s *PreloadingService) GetFinanceDefaults() *FinanceDefaults {
	currentYear := time.Now().Year()
	return &FinanceDefaults{
		Year:                  currentYear,
		BudgetType:            "original",
		PerformancePercentage: 100.00,
		IsAutoCalculated:      false,
	}
}

// generateFRDocumentNumber generates a sequential FR document number
func (s *PreloadingService) generateFRDocumentNumber() (string, error) {
	currentYear := time.Now().Year()

	// Get the latest FR document number for current year
	var latestNumber string
	err := s.db.Table("documents").
		Select("fr_document_number").
		Where("fr_document_number LIKE ?", fmt.Sprintf("%d-%%", currentYear)).
		Order("fr_document_number DESC").
		Limit(1).
		Scan(&latestNumber).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return "", err
	}

	// Parse the latest number and increment
	var sequence int = 1
	if latestNumber != "" {
		parts := strings.Split(latestNumber, "-")
		if len(parts) >= 3 {
			if seq, err := strconv.Atoi(parts[2]); err == nil {
				sequence = seq + 1
			}
		}
	}

	return fmt.Sprintf("%d-001-%04d", currentYear, sequence), nil
}

// GeneratePublicLawNumber generates a sequential public law number
// Format: CONGRESS-SEQUENCE (e.g., 118-001, 118-002)
func (s *PreloadingService) GeneratePublicLawNumber() (string, error) {
	// Current Congress number (118th Congress: 2023-2025, 119th: 2025-2027, etc.)
	currentYear := time.Now().Year()
	congressNumber := 118 + ((currentYear - 2023) / 2)

	// Get the latest public law number for current congress
	var latestNumber string
	pattern := fmt.Sprintf("%d-%%", congressNumber)
	err := s.db.Table("laws_and_rules").
		Where("public_law_number LIKE ?", pattern).
		Order("public_law_number DESC").
		Limit(1).
		Pluck("public_law_number", &latestNumber).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return "", err
	}

	sequence := 1
	if latestNumber != "" {
		// Extract sequence number from format "118-001"
		parts := strings.Split(latestNumber, "-")
		if len(parts) == 2 {
			if seq, err := strconv.Atoi(parts[1]); err == nil {
				sequence = seq + 1
			}
		}
	}

	return fmt.Sprintf("%d-%03d", congressNumber, sequence), nil
}

// GenerateRegulatoryIdentifier generates a RIN (Regulatory Identifier Number)
// Format: AGENCY-CATEGORY (e.g., 2040-AF23, 1234-AB12)
func (s *PreloadingService) GenerateRegulatoryIdentifier(agencyID *uint) (string, error) {
	if agencyID == nil {
		return "", nil
	}

	// Get agency RIN prefix (typically 4 digits)
	var agency struct {
		ShortName string
		RINPrefix string
	}
	err := s.db.Table("agencies").
		Select("short_name, COALESCE(rin_prefix, '') as rin_prefix").
		Where("id = ?", *agencyID).
		Scan(&agency).Error
	if err != nil {
		return "", err
	}

	// Use RIN prefix if available, otherwise generate from agency ID
	var prefix string
	if agency.RINPrefix != "" {
		prefix = agency.RINPrefix
	} else {
		// Generate 4-digit prefix from agency ID (padded)
		prefix = fmt.Sprintf("%04d", *agencyID)
	}

	// Generate 2-letter + 2-digit suffix
	// Get count of existing RINs with this prefix
	var count int64
	pattern := fmt.Sprintf("%s-%%", prefix)
	s.db.Table("laws_and_rules").
		Where("regulatory_identifier LIKE ?", pattern).
		Count(&count)

	// Generate suffix: AA00, AA01, ..., AA99, AB00, etc.
	letterIndex := int(count) / 100
	numberIndex := int(count) % 100

	firstLetter := 'A' + (letterIndex / 26)
	secondLetter := 'A' + (letterIndex % 26)

	suffix := fmt.Sprintf("%c%c%02d", firstLetter, secondLetter, numberIndex)

	return fmt.Sprintf("%s-%s", prefix, suffix), nil
}

// GenerateRegulationDocketNumber generates a docket number specifically for regulations
// Format: AGENCY-REG-SEQUENCE (e.g., EPA-REG-0001, DOE-REG-0002)
func (s *PreloadingService) GenerateRegulationDocketNumber(agencyID *uint) (string, error) {
	if agencyID == nil {
		return "", nil
	}

	// Get agency short name
	var agencyShortName string
	err := s.db.Table("agencies").Select("short_name").Where("id = ?", *agencyID).Scan(&agencyShortName).Error
	if err != nil {
		return "", err
	}

	// Get next sequential number for this agency's regulations
	var count int64
	pattern := fmt.Sprintf("%s-REG-%%", agencyShortName)
	s.db.Table("laws_and_rules").Where("docket_number LIKE ?", pattern).Count(&count)

	return fmt.Sprintf("%s-REG-%04d", agencyShortName, count+1), nil
}

// generateDocketNumber generates a docket number based on agency and category
func (s *PreloadingService) generateDocketNumber(agencyID *uint, categoryIDs []uint) (string, error) {
	if agencyID == nil {
		return "", nil
	}

	// Get agency short name
	var agencyShortName string
	err := s.db.Table("agencies").Select("short_name").Where("id = ?", *agencyID).Scan(&agencyShortName).Error
	if err != nil {
		return "", err
	}

	// Get primary category slug
	categorySlug := "GEN" // Default to "GEN" for general
	if len(categoryIDs) > 0 {
		err := s.db.Table("categories").Select("slug").Where("id = ?", categoryIDs[0]).Scan(&categorySlug).Error
		if err == nil && categorySlug != "" {
			categorySlug = strings.ToUpper(categorySlug)
		}
	}

	// Get next sequential number for this agency-category combination
	var count int64
	pattern := fmt.Sprintf("%s-%s-%%", agencyShortName, categorySlug)
	s.db.Table("documents").Where("docket_number LIKE ?", pattern).Count(&count)

	return fmt.Sprintf("%s-%s-RULE-%04d", agencyShortName, categorySlug, count+1), nil
}

// GenerateSlug generates a URL-friendly slug from a string
func (s *PreloadingService) GenerateSlug(text string) string {
	return slug.Make(text)
}
