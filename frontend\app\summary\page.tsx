'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  NewspaperIcon,
  DocumentTextIcon,
  BuildingOfficeIcon,
  TagIcon,
  ClockIcon,
  EyeIcon,
  CalendarIcon,
  ArrowTrendingUpIcon,
  BellIcon,
  InformationCircleIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';
import MarkdownRenderer from '../components/MarkdownRenderer/MarkdownRenderer';

interface SummaryItem {
  id: number;
  title: string;
  content: string;
  summary_type: 'document_added' | 'document_modified' | 'document_deleted' | 'regulation_added' | 'regulation_modified' | 'agency_added' | 'category_added' | 'system_update';
  entity_type: 'document' | 'regulation' | 'agency' | 'category' | 'system';
  entity_id?: number;
  entity_title?: string;
  created_at: string;
  created_by?: {
    id: number;
    username: string;
    first_name?: string;
    last_name?: string;
  };
  agency?: {
    id: number;
    name: string;
  };
  category?: {
    id: number;
    name: string;
  };
  is_important: boolean;
  tags?: string[];
}

const SummaryPage: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const [summaries, setSummaries] = useState<SummaryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null);
  const [filters, setFilters] = useState({
    type: '',
    entity_type: '',
    agency_id: 0,
    date_from: '',
    date_to: '',
    important_only: false
  });
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    total_pages: 0
  });

  const fetchSummaries = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        per_page: pagination.per_page,
        ...filters
      };

      const response = await apiService.getSummaries(params);
      setSummaries(response.data.map((summary: any) => ({
        ...summary,
        content: summary.content || summary.abstract || '',
        is_important: summary.is_featured || false
      })));
      setPagination({
        page: response.page,
        per_page: response.per_page,
        total: response.total,
        total_pages: response.total_pages
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch summaries');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSummaries();
  }, [pagination.page, filters]);

  const handleFilterChange = (key: string, value: string | boolean) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleDeleteSummary = async (summaryId: number) => {
    if (!confirm('Are you sure you want to delete this summary?')) {
      return;
    }

    try {
      setDeleteLoading(summaryId);
      await apiService.deleteSummary(summaryId);
      // Refresh summaries
      fetchSummaries();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete summary');
    } finally {
      setDeleteLoading(null);
    }
  };

  const getSummaryIcon = (type: string) => {
    switch (type) {
      case 'document_added':
      case 'document_modified':
      case 'document_deleted':
        return <DocumentTextIcon className="h-5 w-5 text-blue-600" />;
      case 'regulation_added':
      case 'regulation_modified':
        return <DocumentTextIcon className="h-5 w-5 text-purple-600" />;
      case 'agency_added':
        return <BuildingOfficeIcon className="h-5 w-5 text-green-600" />;
      case 'category_added':
        return <TagIcon className="h-5 w-5 text-orange-600" />;
      case 'system_update':
        return <InformationCircleIcon className="h-5 w-5 text-gray-600" />;
      default:
        return <NewspaperIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getSummaryTypeColor = (type: string) => {
    switch (type) {
      case 'document_added':
        return 'bg-green-100 text-green-800';
      case 'document_modified':
        return 'bg-blue-100 text-blue-800';
      case 'document_deleted':
        return 'bg-red-100 text-red-800';
      case 'regulation_added':
      case 'regulation_modified':
        return 'bg-purple-100 text-purple-800';
      case 'agency_added':
        return 'bg-green-100 text-green-800';
      case 'category_added':
        return 'bg-orange-100 text-orange-800';
      case 'system_update':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatSummaryType = (type: string) => {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const getEntityLink = (summary: SummaryItem) => {
    if (!summary.entity_id) return null;
    
    switch (summary.entity_type) {
      case 'document':
        return `/documents/${summary.entity_id}`;
      case 'regulation':
        return `/regulations/${summary.entity_id}`;
      case 'agency':
        return `/agencies/${summary.entity_id}`;
      case 'category':
        return `/categories/${summary.entity_id}`;
      default:
        return null;
    }
  };

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">News & Updates</h1>
              <p className="text-gray-600">
                Stay informed about the latest changes to documents, regulations, and system updates
              </p>
            </div>
            {user && (
              <Link
                href="/summary/new"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Summary
              </Link>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <select
              value={filters.type}
              onChange={(e) => handleFilterChange('type', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Activity Types</option>
              <option value="document_added">Document Added</option>
              <option value="document_modified">Document Modified</option>
              <option value="document_deleted">Document Deleted</option>
              <option value="regulation_added">Regulation Added</option>
              <option value="regulation_modified">Regulation Modified</option>
              <option value="agency_added">Agency Added</option>
              <option value="category_added">Category Added</option>
              <option value="system_update">System Update</option>
            </select>

            <select
              value={filters.entity_type}
              onChange={(e) => handleFilterChange('entity_type', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">All Entity Types</option>
              <option value="document">Documents</option>
              <option value="regulation">Regulations</option>
              <option value="agency">Agencies</option>
              <option value="category">Categories</option>
              <option value="system">System</option>
            </select>

            <input
              type="date"
              value={filters.date_from}
              onChange={(e) => handleFilterChange('date_from', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="From Date"
            />

            <input
              type="date"
              value={filters.date_to}
              onChange={(e) => handleFilterChange('date_to', e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="To Date"
            />
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="important_only"
              checked={filters.important_only}
              onChange={(e) => handleFilterChange('important_only', e.target.checked)}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label htmlFor="important_only" className="ml-2 text-sm text-gray-700">
              Show important updates only
            </label>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Summary Feed */}
        {loading ? (
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-white rounded-lg shadow-md p-6 animate-pulse">
                <div className="h-6 bg-gray-200 rounded mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded mb-4 w-3/4"></div>
                <div className="flex space-x-4">
                  <div className="h-4 bg-gray-200 rounded w-20"></div>
                  <div className="h-4 bg-gray-200 rounded w-24"></div>
                </div>
              </div>
            ))}
          </div>
        ) : summaries.length === 0 ? (
          <div className="text-center py-12">
            <NewspaperIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No updates found</h3>
            <p className="text-gray-600">
              No activity matches your current filters. Try adjusting the date range or filters.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {summaries.map((summary) => (
              <div key={summary.id} className={`bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 ${summary.is_important ? 'border-l-4 border-l-yellow-400' : ''}`}>
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="flex-shrink-0 mt-1">
                        {getSummaryIcon(summary.summary_type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSummaryTypeColor(summary.summary_type)}`}>
                            {formatSummaryType(summary.summary_type)}
                          </span>
                          {summary.is_important && (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                              <BellIcon className="h-3 w-3 mr-1" />
                              Important
                            </span>
                          )}
                        </div>
                        
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {summary.entity_id && getEntityLink(summary) ? (
                            <Link 
                              href={getEntityLink(summary)!}
                              className="hover:text-primary-600 transition-colors"
                            >
                              {summary.title}
                            </Link>
                          ) : (
                            summary.title
                          )}
                        </h3>
                        
                        <div className="text-gray-600 mb-4">
                          <MarkdownRenderer
                            content={summary.content}
                            className="prose prose-sm max-w-none text-gray-600"
                          />
                        </div>
                        
                        <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500">
                          {summary.agency && (
                            <span className="flex items-center">
                              <BuildingOfficeIcon className="h-4 w-4 mr-1" />
                              <Link 
                                href={`/agencies/${summary.agency.id}`}
                                className="hover:text-primary-600"
                              >
                                {summary.agency.name}
                              </Link>
                            </span>
                          )}
                          {summary.category && (
                            <span className="flex items-center">
                              <TagIcon className="h-4 w-4 mr-1" />
                              <Link 
                                href={`/categories/${summary.category.id}`}
                                className="hover:text-primary-600"
                              >
                                {summary.category.name}
                              </Link>
                            </span>
                          )}
                          {summary.created_by && (
                            <span>
                              By: {summary.created_by.first_name} {summary.created_by.last_name} ({summary.created_by.username})
                            </span>
                          )}
                          <span className="flex items-center">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            {new Date(summary.created_at).toLocaleDateString()} at {new Date(summary.created_at).toLocaleTimeString()}
                          </span>
                        </div>

                        {summary.tags && summary.tags.length > 0 && (
                          <div className="mt-3 flex flex-wrap gap-2">
                            {summary.tags.map((tag, index) => (
                              <span 
                                key={index}
                                className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                              >
                                #{tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <Link
                        href={`/summary/${summary.id}`}
                        className="p-2 text-gray-400 hover:text-primary-600 transition-colors"
                        title="View Summary"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </Link>

                      {user && (user.role === 'admin' || summary.created_by?.id === user.id) && (
                        <>
                          <Link
                            href={`/summary/${summary.id}/edit`}
                            className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                            title="Edit Summary"
                          >
                            <PencilIcon className="h-5 w-5" />
                          </Link>
                          <button
                            onClick={() => handleDeleteSummary(summary.id)}
                            className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                            title="Delete Summary"
                            disabled={deleteLoading === summary.id}
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </>
                      )}

                      {summary.entity_id && getEntityLink(summary) && (
                        <Link
                          href={getEntityLink(summary)!}
                          className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                          title="View Related Entity"
                        >
                          <ArrowTrendingUpIcon className="h-5 w-5" />
                        </Link>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {pagination.total_pages > 1 && (
          <div className="flex items-center justify-between mt-8">
            <div className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.per_page) + 1} to{' '}
              {Math.min(pagination.page * pagination.per_page, pagination.total)} of{' '}
              {pagination.total} updates
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page - 1 }))}
                disabled={pagination.page === 1}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              <span className="px-3 py-2 text-sm text-gray-700">
                Page {pagination.page} of {pagination.total_pages}
              </span>
              <button
                onClick={() => setPagination(prev => ({ ...prev, page: prev.page + 1 }))}
                disabled={pagination.page === pagination.total_pages}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SummaryPage;
