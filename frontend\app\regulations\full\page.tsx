'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  BookOpenIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  MagnifyingGlassIcon,
  PrinterIcon,
  ShareIcon,
  ArrowLeftIcon,
  DocumentTextIcon,
  FolderIcon,
  DocumentDuplicateIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import MarkdownRenderer from '../../components/MarkdownRenderer/MarkdownRenderer';
import { LawsAndRules as Regulation } from '../../types';

interface TreeNode {
  id: string;
  label: string;
  type: 'cfr_title' | 'cfr_chapter' | 'cfr_part' | 'cfr_section' | 'regulation';
  level: number;
  children: TreeNode[];
  regulations: Regulation[];
  expanded: boolean;
  count: number;
  metadata?: {
    usc_title?: string;
    public_law_number?: string;
    regulatory_identifier?: string;
    docket_number?: string;
  };
}

const RegulationsFullPage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [regulations, setRegulations] = useState<Regulation[]>([]);
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());
  const [selectedRegulation, setSelectedRegulation] = useState<Regulation | null>(null);

  useEffect(() => {
    fetchRegulations();
  }, []);

  const fetchRegulations = async () => {
    try {
      setLoading(true);
      // Use appropriate API based on authentication status
      const response = isAuthenticated
        ? await apiService.getAuthenticatedRegulations()
        : await apiService.getPublicRegulations();
      setRegulations(response.data);
      const tree = buildHierarchicalTree(response.data);
      setTreeData(tree);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch regulations');
    } finally {
      setLoading(false);
    }
  };

  const buildHierarchicalTree = (regulations: Regulation[]): TreeNode[] => {
    const tree: TreeNode[] = [];

    // Group by CFR Title (top level) - this is the proper legal hierarchy
    const cfrTitleGroups = new Map<string, Regulation[]>();
    regulations.forEach(reg => {
      const cfrTitle = reg.cfr_title || 'Unspecified CFR Title';
      if (!cfrTitleGroups.has(cfrTitle)) {
        cfrTitleGroups.set(cfrTitle, []);
      }
      cfrTitleGroups.get(cfrTitle)!.push(reg);
    });

    // Build proper CFR hierarchy: Title → Chapter → Part → Section
    cfrTitleGroups.forEach((cfrRegs, cfrTitle) => {
      const cfrTitleNode: TreeNode = {
        id: `cfr-title-${cfrTitle}`,
        label: `CFR Title ${cfrTitle}`,
        type: 'cfr_title',
        level: 0,
        children: [],
        regulations: [],
        expanded: false,
        count: cfrRegs.length,
        metadata: {}
      };

      // Group by Chapter Number (level 1)
      const chapterGroups = new Map<string, Regulation[]>();
      cfrRegs.forEach(reg => {
        const chapter = reg.chapter_number || 'Unspecified Chapter';
        if (!chapterGroups.has(chapter)) {
          chapterGroups.set(chapter, []);
        }
        chapterGroups.get(chapter)!.push(reg);
      });

      chapterGroups.forEach((chapterRegs, chapter) => {
        const chapterNode: TreeNode = {
          id: `cfr-chapter-${cfrTitle}-${chapter}`,
          label: chapter === 'Unspecified Chapter' ? 'Unspecified Chapter' : `Chapter ${chapter}`,
          type: 'cfr_chapter',
          level: 1,
          children: [],
          regulations: [],
          expanded: false,
          count: chapterRegs.length,
          metadata: {}
        };

        // Group by Part Number (level 2)
        const partGroups = new Map<string, Regulation[]>();
        chapterRegs.forEach(reg => {
          const part = reg.part_number || 'Unspecified Part';
          if (!partGroups.has(part)) {
            partGroups.set(part, []);
          }
          partGroups.get(part)!.push(reg);
        });

        partGroups.forEach((partRegs, part) => {
          const partNode: TreeNode = {
            id: `cfr-part-${cfrTitle}-${chapter}-${part}`,
            label: part === 'Unspecified Part' ? 'Unspecified Part' : `Part ${part}`,
            type: 'cfr_part',
            level: 2,
            children: [],
            regulations: [],
            expanded: false,
            count: partRegs.length,
            metadata: {}
          };

          // Group by Section Number (level 3)
          const sectionGroups = new Map<string, Regulation[]>();
          partRegs.forEach(reg => {
            const section = reg.section_number || 'Unspecified Section';
            if (!sectionGroups.has(section)) {
              sectionGroups.set(section, []);
            }
            sectionGroups.get(section)!.push(reg);
          });

          sectionGroups.forEach((sectionRegs, section) => {
            const sectionNode: TreeNode = {
              id: `cfr-section-${cfrTitle}-${chapter}-${part}-${section}`,
              label: section === 'Unspecified Section' ? 'Unspecified Section' : `Section ${section}`,
              type: 'cfr_section',
              level: 3,
              children: [],
              regulations: sectionRegs,
              expanded: false,
              count: sectionRegs.length,
              metadata: {}
            };

            partNode.children.push(sectionNode);
          });

          chapterNode.children.push(partNode);
        });

        cfrTitleNode.children.push(chapterNode);
      });

      tree.push(cfrTitleNode);
    });

    return tree.sort((a, b) => a.label.localeCompare(b.label));
  };

  const toggleExpanded = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const getNodeIcon = (node: TreeNode) => {
    switch (node.type) {
      case 'cfr_title':
        return BookOpenIcon;
      case 'cfr_chapter':
        return FolderIcon;
      case 'cfr_part':
        return DocumentDuplicateIcon;
      case 'cfr_section':
        return ArchiveBoxIcon;
      case 'regulation':
        return DocumentTextIcon;
      default:
        return DocumentTextIcon;
    }
  };

  const getExpandIcon = (node: TreeNode) => {
    if (node.children.length > 0 || node.regulations.length > 0) {
      return expandedNodes.has(node.id) ? ChevronDownIcon : ChevronRightIcon;
    }
    return null;
  };

  const renderTreeNode = (node: TreeNode, depth: number = 0): React.ReactNode => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children.length > 0 || node.regulations.length > 0;
    const Icon = getNodeIcon(node);
    const ExpandIcon = getExpandIcon(node);
    const indentStyle = { paddingLeft: `${depth * 20 + 12}px` };

    // Filter logic for search
    const matchesSearch = searchTerm === '' ||
      node.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      node.regulations.some(reg =>
        reg.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reg.description.toLowerCase().includes(searchTerm.toLowerCase())
      );

    if (!matchesSearch) return null;

    return (
      <div key={node.id} className="tree-node">
        <div
          className="flex items-center py-2 px-3 hover:bg-gray-50 cursor-pointer border-l-2 border-transparent hover:border-primary-200"
          style={indentStyle}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(node.id);
            }
          }}
        >
          <div className="flex items-center flex-1 min-w-0">
            {ExpandIcon && (
              <ExpandIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
            )}
            <Icon className="h-4 w-4 text-primary-600 mr-2 flex-shrink-0" />
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium text-gray-900 truncate">
                {node.label}
              </div>
              <div className="text-xs text-gray-500">
                {node.count} {node.count === 1 ? 'item' : 'items'}
              </div>
            </div>
          </div>
        </div>

        {hasChildren && isExpanded && (
          <div className="tree-children">
            {/* Render child nodes */}
            {node.children.map(child => renderTreeNode(child, depth + 1))}

            {/* Render regulations at this level */}
            {node.regulations.map(regulation => (
              <div
                key={`reg-${regulation.id}`}
                className={`flex items-center py-2 px-3 hover:bg-gray-50 cursor-pointer border-l-2 ${
                  selectedRegulation?.id === regulation.id ? 'border-primary-500 bg-primary-50' : 'border-transparent'
                }`}
                style={{ paddingLeft: `${(depth + 1) * 20 + 12}px` }}
                onClick={() => setSelectedRegulation(regulation)}
              >
                <DocumentTextIcon className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 truncate">
                    {regulation.title}
                  </div>
                  <div className="text-xs text-gray-500">
                    {regulation.agency.short_name} • {regulation.type}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                    regulation.status === 'effective' ? 'bg-green-100 text-green-800' :
                    regulation.status === 'published' ? 'bg-yellow-100 text-yellow-800' :
                    regulation.status === 'draft' ? 'bg-blue-100 text-blue-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {regulation.status.toUpperCase()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-6"></div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-1">
                <div className="space-y-4">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="h-12 bg-gray-200 rounded"></div>
                  ))}
                </div>
              </div>
              <div className="lg:col-span-2">
                <div className="h-64 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load regulations</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Link
              href="/regulations"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Regulations
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/regulations"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Regulations
          </Link>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Code of Federal Regulations</h1>
              <p className="text-gray-600 mt-1">Complete hierarchical view of all regulations</p>
            </div>
            <div className="flex items-center space-x-3">
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <ShareIcon className="h-4 w-4 mr-2" />
                Share
              </button>
              <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <PrinterIcon className="h-4 w-4 mr-2" />
                Print
              </button>
            </div>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative max-w-md">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Search regulations..."
            />
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Regulation Tree */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div className="px-4 py-3 bg-gray-50 border-b border-gray-200">
                <h2 className="text-lg font-semibold text-gray-900">CFR Hierarchy</h2>
                <p className="text-xs text-gray-500 mt-1">CFR Title → Chapter → Part → Section</p>
              </div>
              <div className="max-h-screen overflow-y-auto">
                {treeData.length === 0 ? (
                  <div className="p-8 text-center">
                    <FolderIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">No regulations found</p>
                  </div>
                ) : (
                  <div className="regulation-tree">
                    {treeData.map(node => renderTreeNode(node))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content Panel */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md">
              {selectedRegulation ? (
                <div className="p-6">
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-2xl font-bold text-gray-900">
                        {selectedRegulation.title}
                      </h2>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          selectedRegulation.status === 'effective' ? 'bg-green-100 text-green-800' :
                          selectedRegulation.status === 'published' ? 'bg-yellow-100 text-yellow-800' :
                          selectedRegulation.status === 'draft' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {selectedRegulation.status.toUpperCase()}
                        </span>
                        <Link
                          href={`/regulations/${selectedRegulation?.id}`}
                          className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>

                    {/* Regulation Metadata */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div className="space-y-2">
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">Agency:</span>
                          <span className="ml-2 text-gray-900">{selectedRegulation.agency.name}</span>
                        </div>
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">Type:</span>
                          <span className="ml-2 text-gray-900 capitalize">{selectedRegulation.type}</span>
                        </div>
                        {selectedRegulation.cfr_title && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">CFR Title:</span>
                            <span className="ml-2 text-gray-900">{selectedRegulation.cfr_title}</span>
                          </div>
                        )}
                        {selectedRegulation.usc_title && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">USC Title:</span>
                            <span className="ml-2 text-gray-900">{selectedRegulation.usc_title}</span>
                          </div>
                        )}
                      </div>
                      <div className="space-y-2">
                        {selectedRegulation.public_law_number && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">Public Law:</span>
                            <span className="ml-2 text-gray-900">{selectedRegulation.public_law_number}</span>
                          </div>
                        )}
                        {selectedRegulation.regulatory_identifier && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">RIN:</span>
                            <span className="ml-2 text-gray-900">{selectedRegulation.regulatory_identifier}</span>
                          </div>
                        )}
                        {selectedRegulation.docket_number && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">Docket:</span>
                            <span className="ml-2 text-gray-900">{selectedRegulation.docket_number}</span>
                          </div>
                        )}
                        {selectedRegulation.effective_date && (
                          <div className="text-sm">
                            <span className="font-medium text-gray-700">Effective Date:</span>
                            <span className="ml-2 text-gray-900">
                              {new Date(selectedRegulation.effective_date).toLocaleDateString()}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Regulation Content */}
                  {selectedRegulation.description ? (
                    <div className="prose prose-lg max-w-none">
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                      <MarkdownRenderer
                        content={selectedRegulation.description}
                        className="text-gray-700 leading-relaxed prose prose-lg max-w-none"
                      />
                      {selectedRegulation.notes && (
                        <div className="mt-6">
                          <h4 className="text-md font-semibold text-gray-900 mb-2">Notes</h4>
                          <MarkdownRenderer
                            content={selectedRegulation.notes}
                            className="text-gray-600 text-sm prose prose-sm max-w-none"
                          />
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">No description available for this regulation.</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-12 text-center">
                  <BookOpenIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Regulation</h3>
                  <p className="text-gray-600">
                    Choose a regulation from the hierarchy on the left to view its details.
                  </p>
                  <div className="mt-6 text-sm text-gray-500">
                    <p className="mb-2">The CFR hierarchy is organized as follows:</p>
                    <div className="space-y-1">
                      <div className="flex items-center justify-center">
                        <BookOpenIcon className="h-4 w-4 mr-2" />
                        <span>CFR Title (Broad subject areas)</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <FolderIcon className="h-4 w-4 mr-2" />
                        <span>Chapter (Agency subdivisions)</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
                        <span>Part (Specific regulatory topics)</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <ArchiveBoxIcon className="h-4 w-4 mr-2" />
                        <span>Section (Individual regulations)</span>
                      </div>
                    </div>
                    <div className="mt-4 text-xs text-gray-400">
                      <p>Additional metadata includes USC Title, Public Law Number, RIN, and Docket Number</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default RegulationsFullPage;
