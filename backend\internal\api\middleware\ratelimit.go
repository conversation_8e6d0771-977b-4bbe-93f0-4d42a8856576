package middleware

import (
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
)

// RateLimiter represents a simple in-memory rate limiter
type RateLimiter struct {
	visitors map[string]*Visitor
	mu       sync.RWMutex
	rate     int           // requests per minute
	burst    int           // burst capacity
	cleanup  time.Duration // cleanup interval
}

// Visitor represents a visitor with rate limiting info
type Visitor struct {
	tokens   int
	lastSeen time.Time
	mu       sync.Mutex
}

var limiter *RateLimiter

func init() {
	limiter = &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     300,             // 300 requests per minute (more generous)
		burst:    50,              // burst of 50 requests (more generous)
		cleanup:  time.Minute * 5, // cleanup every 5 minutes
	}

	// Start cleanup goroutine
	go limiter.cleanupVisitors()
}

// RateLimit middleware implements rate limiting
func RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get client IP
		ip := c.ClientIP()

		// Check if request is allowed
		if !limiter.allow(ip) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded. Please try again later.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// allow checks if a request from the given IP is allowed
func (rl *RateLimiter) allow(ip string) bool {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	visitor, exists := rl.visitors[ip]
	if !exists {
		visitor = &Visitor{
			tokens:   rl.burst,
			lastSeen: time.Now(),
		}
		rl.visitors[ip] = visitor
	}

	visitor.mu.Lock()
	defer visitor.mu.Unlock()

	now := time.Now()

	// Calculate tokens to add based on time elapsed
	elapsed := now.Sub(visitor.lastSeen)
	tokensToAdd := int(elapsed.Minutes() * float64(rl.rate)) // rate per minute

	visitor.tokens += tokensToAdd
	if visitor.tokens > rl.burst {
		visitor.tokens = rl.burst
	}

	visitor.lastSeen = now

	// Check if request is allowed
	if visitor.tokens > 0 {
		visitor.tokens--
		return true
	}

	return false
}

// cleanupVisitors removes old visitors to prevent memory leaks
func (rl *RateLimiter) cleanupVisitors() {
	ticker := time.NewTicker(rl.cleanup)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rl.mu.Lock()
			cutoff := time.Now().Add(-rl.cleanup)

			for ip, visitor := range rl.visitors {
				visitor.mu.Lock()
				if visitor.lastSeen.Before(cutoff) {
					delete(rl.visitors, ip)
				}
				visitor.mu.Unlock()
			}
			rl.mu.Unlock()
		}
	}
}

// CustomRateLimit creates a rate limiter with custom settings
func CustomRateLimit(rate, burst int) gin.HandlerFunc {
	customLimiter := &RateLimiter{
		visitors: make(map[string]*Visitor),
		rate:     rate,
		burst:    burst,
		cleanup:  time.Minute * 5,
	}

	go customLimiter.cleanupVisitors()

	return func(c *gin.Context) {
		ip := c.ClientIP()

		if !customLimiter.allow(ip) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error": "Rate limit exceeded. Please try again later.",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
