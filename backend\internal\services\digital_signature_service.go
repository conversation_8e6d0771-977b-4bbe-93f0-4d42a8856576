package services

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"federal-register-clone/internal/models"

	"gorm.io/gorm"
)

// DigitalSignatureService handles digital signature operations
type DigitalSignatureService struct {
	db *gorm.DB
}

// NewDigitalSignatureService creates a new digital signature service
func NewDigitalSignatureService(db *gorm.DB) *DigitalSignatureService {
	return &DigitalSignatureService{db: db}
}

// CreateSignatureRequest creates a new signature request
func (s *DigitalSignatureService) CreateSignatureRequest(req CreateSignatureRequest) (*models.DigitalSignature, error) {
	// Generate unique signature ID
	signatureID, err := s.generateSignatureID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate signature ID: %w", err)
	}

	// Calculate document hash
	documentHash, err := s.calculateDocumentHash(req.DocumentID)
	if err != nil {
		return nil, fmt.Erro<PERSON>("failed to calculate document hash: %w", err)
	}

	signature := &models.DigitalSignature{
		SignatureID:     signatureID,
		Type:            req.Type,
		Status:          models.SignatureStatusPending,
		DocumentID:      req.DocumentID,
		SignerID:        req.SignerID,
		SignerName:      req.SignerName,
		SignerEmail:     req.SignerEmail,
		SignerTitle:     req.SignerTitle,
		DocumentHash:    documentHash,
		RequestedAt:     time.Now(),
		ExpiresAt:       req.ExpiresAt,
		WorkflowStepID:  req.WorkflowStepID,
		SigningOrder:    req.SigningOrder,
		IsRequired:      req.IsRequired,
		RequiresWitness: req.RequiresWitness,
		RequestedByID:   req.RequestedByID,
		Reason:          req.Reason,
		Notes:           req.Notes,
		IsVisible:       req.IsVisible,
		PageNumber:      req.PageNumber,
		XPosition:       req.XPosition,
		YPosition:       req.YPosition,
	}

	if err := s.db.Create(signature).Error; err != nil {
		return nil, fmt.Errorf("failed to create signature request: %w", err)
	}

	// Log the signature request creation
	s.logSignatureAction(&signature.ID, signature.DocumentID, req.RequestedByID, "signature_requested", "Signature request created", req.IPAddress, req.UserAgent)

	return signature, nil
}

// SignDocument signs a document with the provided signature data
func (s *DigitalSignatureService) SignDocument(signatureID string, req SignDocumentRequest) error {
	var signature models.DigitalSignature
	if err := s.db.Where("signature_id = ?", signatureID).First(&signature).Error; err != nil {
		return fmt.Errorf("signature not found: %w", err)
	}

	// Verify signature is in pending status
	if signature.Status != models.SignatureStatusPending {
		return fmt.Errorf("signature is not in pending status")
	}

	// Verify signer authorization
	if signature.SignerID != req.SignerID {
		return fmt.Errorf("unauthorized signer")
	}

	// Check if signature has expired
	if signature.ExpiresAt != nil && time.Now().After(*signature.ExpiresAt) {
		signature.Status = models.SignatureStatusExpired
		s.db.Save(&signature)
		return fmt.Errorf("signature request has expired")
	}

	// Verify document hash hasn't changed
	currentDocHash, err := s.calculateDocumentHash(signature.DocumentID)
	if err != nil {
		return fmt.Errorf("failed to verify document integrity: %w", err)
	}
	if currentDocHash != signature.DocumentHash {
		return fmt.Errorf("document has been modified since signature request")
	}

	// Generate signature hash
	signatureHash := s.calculateSignatureHash(req.SignatureData)

	// Update signature with signing data
	now := time.Now()
	signature.Status = models.SignatureStatusSigned
	signature.SignatureData = req.SignatureData
	signature.SignatureHash = signatureHash
	signature.SignatureMethod = req.SignatureMethod
	signature.SignatureLocation = req.SignatureLocation
	signature.SignedAt = &now
	signature.CertificateID = req.CertificateID
	signature.CertificateSerial = req.CertificateSerial
	signature.CertificateIssuer = req.CertificateIssuer
	signature.IsValid = true
	signature.ValidationStatus = "signed"
	signature.ValidatedAt = &now

	if err := s.db.Save(&signature).Error; err != nil {
		return fmt.Errorf("failed to save signature: %w", err)
	}

	// Update certificate usage
	if req.CertificateID != nil {
		s.updateCertificateUsage(*req.CertificateID)
	}

	// Log the signing action
	s.logSignatureAction(&signature.ID, signature.DocumentID, req.SignerID, "document_signed", "Document signed successfully", req.IPAddress, req.UserAgent)

	// Check if this completes a workflow
	s.checkWorkflowCompletion(signature.DocumentID)

	return nil
}

// RejectSignature rejects a signature request
func (s *DigitalSignatureService) RejectSignature(signatureID string, req RejectSignatureRequest) error {
	var signature models.DigitalSignature
	if err := s.db.Where("signature_id = ?", signatureID).First(&signature).Error; err != nil {
		return fmt.Errorf("signature not found: %w", err)
	}

	// Verify signer authorization
	if signature.SignerID != req.SignerID {
		return fmt.Errorf("unauthorized signer")
	}

	signature.Status = models.SignatureStatusRejected
	signature.Notes = req.Reason

	if err := s.db.Save(&signature).Error; err != nil {
		return fmt.Errorf("failed to reject signature: %w", err)
	}

	// Log the rejection
	s.logSignatureAction(&signature.ID, signature.DocumentID, req.SignerID, "signature_rejected", fmt.Sprintf("Signature rejected: %s", req.Reason), req.IPAddress, req.UserAgent)

	return nil
}

// ValidateSignature validates an existing signature
func (s *DigitalSignatureService) ValidateSignature(signatureID string, validatorID uint) (*models.DigitalSignature, error) {
	var signature models.DigitalSignature
	if err := s.db.Preload("Certificate").Where("signature_id = ?", signatureID).First(&signature).Error; err != nil {
		return nil, fmt.Errorf("signature not found: %w", err)
	}

	// Perform validation checks
	isValid := true
	validationStatus := "valid"

	// Check document integrity
	currentDocHash, err := s.calculateDocumentHash(signature.DocumentID)
	if err != nil {
		isValid = false
		validationStatus = "document_hash_error"
	} else if currentDocHash != signature.DocumentHash {
		isValid = false
		validationStatus = "document_modified"
	}

	// Check certificate validity if present
	if signature.Certificate != nil {
		if signature.Certificate.Status != models.CertificateStatusActive {
			isValid = false
			validationStatus = "certificate_inactive"
		} else if time.Now().After(signature.Certificate.NotAfter) {
			isValid = false
			validationStatus = "certificate_expired"
		}
	}

	// Check signature expiry
	if signature.ValidUntil != nil && time.Now().After(*signature.ValidUntil) {
		isValid = false
		validationStatus = "signature_expired"
	}

	// Update validation status
	now := time.Now()
	signature.IsValid = isValid
	signature.ValidationStatus = validationStatus
	signature.ValidatedAt = &now
	signature.ValidatedByID = &validatorID

	if err := s.db.Save(&signature).Error; err != nil {
		return nil, fmt.Errorf("failed to update validation status: %w", err)
	}

	// Log validation
	s.logSignatureAction(&signature.ID, signature.DocumentID, validatorID, "signature_validated", fmt.Sprintf("Signature validation: %s", validationStatus), "", "")

	return &signature, nil
}

// GetDocumentSignatures retrieves all signatures for a document
func (s *DigitalSignatureService) GetDocumentSignatures(documentID uint) ([]models.DigitalSignature, error) {
	var signatures []models.DigitalSignature
	if err := s.db.Preload("Signer").Preload("Certificate").Preload("RequestedBy").
		Where("document_id = ?", documentID).
		Order("signing_order ASC, created_at ASC").
		Find(&signatures).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve signatures: %w", err)
	}

	return signatures, nil
}

// GetUserSignatures retrieves signatures for a specific user
func (s *DigitalSignatureService) GetUserSignatures(userID uint, status string) ([]models.DigitalSignature, error) {
	query := s.db.Preload("Document").Preload("Certificate").Where("signer_id = ?", userID)

	if status != "" {
		query = query.Where("status = ?", status)
	}

	var signatures []models.DigitalSignature
	if err := query.Order("created_at DESC").Find(&signatures).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve user signatures: %w", err)
	}

	return signatures, nil
}

// Helper functions

func (s *DigitalSignatureService) generateSignatureID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return fmt.Sprintf("SIG-%s", hex.EncodeToString(bytes)), nil
}

func (s *DigitalSignatureService) calculateDocumentHash(documentID uint) (string, error) {
	var document models.Document
	if err := s.db.Select("content, updated_at").Where("id = ?", documentID).First(&document).Error; err != nil {
		return "", err
	}

	hasher := sha256.New()
	hasher.Write([]byte(document.Content))
	hasher.Write([]byte(document.UpdatedAt.Format(time.RFC3339)))
	return hex.EncodeToString(hasher.Sum(nil)), nil
}

func (s *DigitalSignatureService) calculateSignatureHash(signatureData string) string {
	hasher := sha256.New()
	hasher.Write([]byte(signatureData))
	return hex.EncodeToString(hasher.Sum(nil))
}

func (s *DigitalSignatureService) updateCertificateUsage(certificateID uint) {
	now := time.Now()
	s.db.Model(&models.DigitalCertificate{}).
		Where("id = ?", certificateID).
		Updates(map[string]interface{}{
			"signature_count": gorm.Expr("signature_count + 1"),
			"last_used_at":    now,
		})
}

func (s *DigitalSignatureService) logSignatureAction(signatureID *uint, documentID uint, userID uint, action, description, ipAddress, userAgent string) {
	log := &models.SignatureAuditLog{
		SignatureID: signatureID,
		DocumentID:  documentID,
		Action:      action,
		Description: description,
		UserID:      userID,
		IPAddress:   ipAddress,
		UserAgent:   userAgent,
	}
	s.db.Create(log)
}

func (s *DigitalSignatureService) checkWorkflowCompletion(documentID uint) {
	// Check if all required signatures for the document are complete
	var pendingCount int64
	s.db.Model(&models.DigitalSignature{}).
		Where("document_id = ? AND status = ? AND is_required = ?", documentID, models.SignatureStatusPending, true).
		Count(&pendingCount)

	if pendingCount == 0 {
		// All required signatures are complete, update document status
		s.db.Model(&models.Document{}).
			Where("id = ?", documentID).
			Update("status", models.StatusApproved)
	}
}

// Request/Response types
type CreateSignatureRequest struct {
	Type            models.SignatureType `json:"type"`
	DocumentID      uint                 `json:"document_id"`
	SignerID        uint                 `json:"signer_id"`
	SignerName      string               `json:"signer_name"`
	SignerEmail     string               `json:"signer_email"`
	SignerTitle     string               `json:"signer_title"`
	ExpiresAt       *time.Time           `json:"expires_at"`
	WorkflowStepID  *uint                `json:"workflow_step_id"`
	SigningOrder    int                  `json:"signing_order"`
	IsRequired      bool                 `json:"is_required"`
	RequiresWitness bool                 `json:"requires_witness"`
	RequestedByID   uint                 `json:"requested_by_id"`
	Reason          string               `json:"reason"`
	Notes           string               `json:"notes"`
	IsVisible       bool                 `json:"is_visible"`
	PageNumber      *int                 `json:"page_number"`
	XPosition       *float64             `json:"x_position"`
	YPosition       *float64             `json:"y_position"`
	IPAddress       string               `json:"ip_address"`
	UserAgent       string               `json:"user_agent"`
}

type SignDocumentRequest struct {
	SignerID          uint   `json:"signer_id"`
	SignatureData     string `json:"signature_data"`
	SignatureMethod   string `json:"signature_method"`
	SignatureLocation string `json:"signature_location"`
	CertificateID     *uint  `json:"certificate_id"`
	CertificateSerial string `json:"certificate_serial"`
	CertificateIssuer string `json:"certificate_issuer"`
	IPAddress         string `json:"ip_address"`
	UserAgent         string `json:"user_agent"`
}

type RejectSignatureRequest struct {
	SignerID  uint   `json:"signer_id"`
	Reason    string `json:"reason"`
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
}
