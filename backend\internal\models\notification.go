package models

import (
	"time"

	"gorm.io/gorm"
)

// Notification represents a system notification
type Notification struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Notification details
	Type        string `json:"type" gorm:"not null"`        // email, in_app, sms, push, webhook
	Priority    string `json:"priority" gorm:"default:'normal'"` // low, normal, high, critical
	Subject     string `json:"subject" gorm:"not null"`
	Message     string `json:"message" gorm:"type:text;not null"`
	Recipients  string `json:"recipients" gorm:"type:text"` // JSON array of recipients
	Data        string `json:"data" gorm:"type:text"`       // JSON data for the notification

	// Status and timing
	Status       string     `json:"status" gorm:"default:'pending'"` // pending, sent, failed, cancelled
	ScheduledAt  *time.Time `json:"scheduled_at"`
	SentAt       *time.Time `json:"sent_at"`
	DeliveredAt  *time.Time `json:"delivered_at"`
	ExpiresAt    *time.Time `json:"expires_at"`
	ErrorMessage string     `json:"error_message"`

	// Categorization and tracking
	Category string `json:"category"`           // retention_policy, training, workflow, etc.
	Source   string `json:"source"`             // service that created the notification
	SourceID string `json:"source_id"`          // ID of the source entity
	Tags     string `json:"tags" gorm:"type:text"` // JSON array of tags

	// Retry mechanism
	RetryCount    int        `json:"retry_count" gorm:"default:0"`
	MaxRetries    int        `json:"max_retries" gorm:"default:3"`
	LastRetryAt   *time.Time `json:"last_retry_at"`
	NextRetryAt   *time.Time `json:"next_retry_at"`

	// Template information
	TemplateID   *uint   `json:"template_id"`
	TemplateData string  `json:"template_data" gorm:"type:text"` // JSON data for template rendering

	// Tracking and analytics
	OpenedAt    *time.Time `json:"opened_at"`
	ClickedAt   *time.Time `json:"clicked_at"`
	UnsubscribedAt *time.Time `json:"unsubscribed_at"`
	
	// Metadata
	UserAgent   string `json:"user_agent"`
	IPAddress   string `json:"ip_address"`
	DeviceType  string `json:"device_type"`
	Platform    string `json:"platform"`
}

// UserNotification represents an in-app notification for a specific user
type UserNotification struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// User and notification reference
	UserID         uint         `json:"user_id" gorm:"not null;index"`
	User           User         `json:"user" gorm:"foreignKey:UserID"`
	NotificationID uint         `json:"notification_id" gorm:"not null;index"`
	Notification   Notification `json:"notification" gorm:"foreignKey:NotificationID"`

	// Notification content (denormalized for performance)
	Title    string `json:"title" gorm:"not null"`
	Message  string `json:"message" gorm:"type:text;not null"`
	Type     string `json:"type" gorm:"not null"`
	Priority string `json:"priority" gorm:"default:'normal'"`
	Category string `json:"category"`

	// User interaction
	IsRead      bool       `json:"is_read" gorm:"default:false;index"`
	ReadAt      *time.Time `json:"read_at"`
	IsArchived  bool       `json:"is_archived" gorm:"default:false"`
	ArchivedAt  *time.Time `json:"archived_at"`
	IsPinned    bool       `json:"is_pinned" gorm:"default:false"`
	PinnedAt    *time.Time `json:"pinned_at"`

	// Action tracking
	ActionTaken   bool       `json:"action_taken" gorm:"default:false"`
	ActionTakenAt *time.Time `json:"action_taken_at"`
	ActionType    string     `json:"action_type"`
	ActionData    string     `json:"action_data" gorm:"type:text"`

	// Display settings
	DisplayUntil *time.Time `json:"display_until"`
	IsSticky     bool       `json:"is_sticky" gorm:"default:false"`
	
	// Metadata
	SourceURL   string `json:"source_url"`
	IconURL     string `json:"icon_url"`
	ImageURL    string `json:"image_url"`
	ButtonText  string `json:"button_text"`
	ButtonURL   string `json:"button_url"`
}

// NotificationTemplate represents a reusable notification template
type NotificationTemplate struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Template identification
	Name        string `json:"name" gorm:"not null;uniqueIndex"`
	Description string `json:"description"`
	Category    string `json:"category" gorm:"index"`
	Type        string `json:"type" gorm:"not null"` // email, in_app, sms, push

	// Template content
	Subject      string `json:"subject" gorm:"not null"`
	BodyTemplate string `json:"body_template" gorm:"type:text;not null"`
	HTMLTemplate string `json:"html_template" gorm:"type:text"`
	
	// Template configuration
	Variables    string `json:"variables" gorm:"type:text"`    // JSON array of available variables
	DefaultData  string `json:"default_data" gorm:"type:text"` // JSON object with default values
	Language     string `json:"language" gorm:"default:'en'"`
	
	// Template settings
	IsActive     bool   `json:"is_active" gorm:"default:true"`
	IsDefault    bool   `json:"is_default" gorm:"default:false"`
	Priority     string `json:"priority" gorm:"default:'normal'"`
	
	// Usage tracking
	UsageCount   int        `json:"usage_count" gorm:"default:0"`
	LastUsedAt   *time.Time `json:"last_used_at"`
	
	// Versioning
	Version      int    `json:"version" gorm:"default:1"`
	ParentID     *uint  `json:"parent_id"`
	VersionNotes string `json:"version_notes"`
	
	// Owner and permissions
	CreatedByID  uint `json:"created_by_id" gorm:"not null"`
	CreatedBy    User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	IsPublic     bool `json:"is_public" gorm:"default:false"`
	
	// Approval workflow
	Status       string     `json:"status" gorm:"default:'draft'"` // draft, pending, approved, rejected
	ApprovedByID *uint      `json:"approved_by_id"`
	ApprovedBy   *User      `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`
	ApprovedAt   *time.Time `json:"approved_at"`
	RejectedAt   *time.Time `json:"rejected_at"`
	RejectionReason string  `json:"rejection_reason"`
}

// NotificationPreference represents user notification preferences
type NotificationPreference struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// User reference
	UserID uint `json:"user_id" gorm:"not null;uniqueIndex:idx_user_category"`
	User   User `json:"user" gorm:"foreignKey:UserID"`

	// Preference settings
	Category string `json:"category" gorm:"not null;uniqueIndex:idx_user_category"` // training, workflow, retention, etc.
	
	// Channel preferences
	EmailEnabled   bool `json:"email_enabled" gorm:"default:true"`
	InAppEnabled   bool `json:"in_app_enabled" gorm:"default:true"`
	SMSEnabled     bool `json:"sms_enabled" gorm:"default:false"`
	PushEnabled    bool `json:"push_enabled" gorm:"default:true"`
	
	// Timing preferences
	QuietHoursStart *time.Time `json:"quiet_hours_start"`
	QuietHoursEnd   *time.Time `json:"quiet_hours_end"`
	TimeZone        string     `json:"timezone" gorm:"default:'UTC'"`
	
	// Frequency preferences
	Frequency       string `json:"frequency" gorm:"default:'immediate'"` // immediate, hourly, daily, weekly
	DigestEnabled   bool   `json:"digest_enabled" gorm:"default:false"`
	DigestFrequency string `json:"digest_frequency" gorm:"default:'daily'"`
	
	// Priority filtering
	MinPriority     string `json:"min_priority" gorm:"default:'low'"` // low, normal, high, critical
	
	// Advanced settings
	Keywords        string `json:"keywords" gorm:"type:text"`        // JSON array of keywords to filter
	ExcludeKeywords string `json:"exclude_keywords" gorm:"type:text"` // JSON array of keywords to exclude
	IsActive        bool   `json:"is_active" gorm:"default:true"`
}

// NotificationLog represents a log entry for notification delivery
type NotificationLog struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Notification reference
	NotificationID uint         `json:"notification_id" gorm:"not null;index"`
	Notification   Notification `json:"notification" gorm:"foreignKey:NotificationID"`

	// Delivery details
	Recipient    string `json:"recipient" gorm:"not null"`
	Channel      string `json:"channel" gorm:"not null"` // email, sms, push, etc.
	Status       string `json:"status" gorm:"not null"`  // sent, delivered, failed, bounced
	
	// Timing
	SentAt       time.Time  `json:"sent_at"`
	DeliveredAt  *time.Time `json:"delivered_at"`
	FailedAt     *time.Time `json:"failed_at"`
	
	// Error handling
	ErrorCode    string `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	RetryCount   int    `json:"retry_count" gorm:"default:0"`
	
	// Provider details
	ProviderID       string `json:"provider_id"`       // External provider message ID
	ProviderResponse string `json:"provider_response"` // Raw provider response
	
	// Tracking
	OpenedAt    *time.Time `json:"opened_at"`
	ClickedAt   *time.Time `json:"clicked_at"`
	UnsubscribedAt *time.Time `json:"unsubscribed_at"`
	
	// Metadata
	UserAgent string `json:"user_agent"`
	IPAddress string `json:"ip_address"`
	Cost      float64 `json:"cost"` // Cost of sending (for SMS, etc.)
	Currency  string  `json:"currency"`
}
