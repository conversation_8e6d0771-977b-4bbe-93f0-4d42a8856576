package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// RelationshipRequest represents the request structure for relationships
type RelationshipRequest struct {
	SourceType   string `json:"source_type" binding:"required"`
	SourceID     uint   `json:"source_id" binding:"required"`
	TargetType   string `json:"target_type" binding:"required"`
	TargetID     uint   `json:"target_id" binding:"required"`
	Relationship string `json:"relationship" binding:"required"`
	Description  string `json:"description"`
	Strength     int    `json:"strength"`
	IsActive     bool   `json:"is_active"`
}

// GetRelationships returns all relationships with pagination
func GetRelationships(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.<PERSON><PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total relationships
	var total int64
	db.Model(&models.Interconnect{}).Count(&total)

	// Get relationships with pagination
	var relationships []models.Interconnect
	offset := (page - 1) * perPage
	if err := db.Order("created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&relationships).Error; err != nil {
		HandleInternalError(c, "Failed to fetch relationships: "+err.Error())
		return
	}

	// Convert to response format
	relationshipResponses := make([]gin.H, len(relationships))
	for i, relationship := range relationships {
		relationshipResponses[i] = gin.H{
			"id":           relationship.ID,
			"source_type":  relationship.SourceType,
			"source_id":    relationship.SourceID,
			"target_type":  relationship.TargetType,
			"target_id":    relationship.TargetID,
			"relationship": relationship.Relationship,
			"description":  relationship.Description,
			"is_active":    relationship.IsActive,
			"created_at":   relationship.CreatedAt,
			"updated_at":   relationship.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       relationshipResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetRelationship returns a single relationship by ID
func GetRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get relationship
	var relationship models.Interconnect
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	response := gin.H{
		"id":           relationship.ID,
		"source_type":  relationship.SourceType,
		"source_id":    relationship.SourceID,
		"target_type":  relationship.TargetType,
		"target_id":    relationship.TargetID,
		"relationship": relationship.Relationship,
		"description":  relationship.Description,
		"is_active":    relationship.IsActive,
		"created_at":   relationship.CreatedAt,
		"updated_at":   relationship.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Relationship retrieved successfully",
		Data:    response,
	})
}

// CreateRelationship creates a new relationship
func CreateRelationship(c *gin.Context) {
	var req RelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create relationship
	relationship := &models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
	}

	if err := db.Create(relationship).Error; err != nil {
		HandleInternalError(c, "Failed to create relationship: "+err.Error())
		return
	}

	response := gin.H{
		"id":           relationship.ID,
		"source_type":  relationship.SourceType,
		"source_id":    relationship.SourceID,
		"target_type":  relationship.TargetType,
		"target_id":    relationship.TargetID,
		"relationship": relationship.Relationship,
		"description":  relationship.Description,
		"is_active":    relationship.IsActive,
		"created_at":   relationship.CreatedAt,
		"updated_at":   relationship.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Relationship created successfully",
		Data:    response,
	})
}

// UpdateRelationship updates an existing relationship
func UpdateRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RelationshipRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing relationship
	var relationship models.Interconnect
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Update relationship fields
	relationship.SourceType = req.SourceType
	relationship.SourceID = req.SourceID
	relationship.TargetType = req.TargetType
	relationship.TargetID = req.TargetID
	relationship.Relationship = req.Relationship
	relationship.Description = req.Description
	relationship.IsActive = req.IsActive

	if err := db.Save(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to update relationship: "+err.Error())
		return
	}

	response := gin.H{
		"id":           relationship.ID,
		"source_type":  relationship.SourceType,
		"source_id":    relationship.SourceID,
		"target_type":  relationship.TargetType,
		"target_id":    relationship.TargetID,
		"relationship": relationship.Relationship,
		"description":  relationship.Description,
		"is_active":    relationship.IsActive,
		"created_at":   relationship.CreatedAt,
		"updated_at":   relationship.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Relationship updated successfully",
		Data:    response,
	})
}

// DeleteRelationship deletes a relationship
func DeleteRelationship(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if relationship exists
	var relationship models.Interconnect
	if err := db.First(&relationship, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Relationship")
			return
		}
		HandleInternalError(c, "Failed to fetch relationship: "+err.Error())
		return
	}

	// Delete relationship
	if err := db.Delete(&relationship).Error; err != nil {
		HandleInternalError(c, "Failed to delete relationship: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Relationship deleted successfully",
	})
}
