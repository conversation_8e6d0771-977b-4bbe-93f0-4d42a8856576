-- Migration to enhance entity relationships and add proper foreign key constraints
-- This migration strengthens the relationships between all entities in the system

-- Add missing foreign key constraints with proper cascade operations

-- Enhance documents table relationships
ALTER TABLE documents 
ADD CONSTRAINT fk_documents_parent 
FOREIGN KEY (parent_document_id) REFERENCES documents(id) ON DELETE SET NULL;

-- Ensure proper indexing for relationship queries
CREATE INDEX IF NOT EXISTS idx_documents_parent_id ON documents(parent_document_id);
CREATE INDEX IF NOT EXISTS idx_documents_agency_id ON documents(agency_id);
CREATE INDEX IF NOT EXISTS idx_documents_created_by ON documents(created_by_id);
CREATE INDEX IF NOT EXISTS idx_documents_updated_by ON documents(updated_by_id);

-- Enhance tasks table relationships
ALTER TABLE tasks 
ADD CONSTRAINT fk_tasks_document 
FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE;

ALTER TABLE tasks 
ADD CONSTRAINT fk_tasks_regulation 
FOREIGN KEY (regulation_id) REFERENCES laws_and_rules(id) ON DELETE CASCADE;

ALTER TABLE tasks 
ADD CONSTRAINT fk_tasks_agency 
FOREIGN KEY (agency_id) REFERENCES agencies(id) ON DELETE SET NULL;

ALTER TABLE tasks 
ADD CONSTRAINT fk_tasks_category 
FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL;

ALTER TABLE tasks 
ADD CONSTRAINT fk_tasks_parent 
FOREIGN KEY (parent_task_id) REFERENCES tasks(id) ON DELETE CASCADE;

-- Add indexes for task relationships
CREATE INDEX IF NOT EXISTS idx_tasks_document_id ON tasks(document_id);
CREATE INDEX IF NOT EXISTS idx_tasks_regulation_id ON tasks(regulation_id);
CREATE INDEX IF NOT EXISTS idx_tasks_agency_id ON tasks(agency_id);
CREATE INDEX IF NOT EXISTS idx_tasks_category_id ON tasks(category_id);
CREATE INDEX IF NOT EXISTS idx_tasks_parent_id ON tasks(parent_task_id);
CREATE INDEX IF NOT EXISTS idx_tasks_source_type_id ON tasks(source_type, source_id);
CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);

-- Enhance finances table relationships
ALTER TABLE finances 
ADD CONSTRAINT fk_finances_document 
FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE;

ALTER TABLE finances 
ADD CONSTRAINT fk_finances_regulation 
FOREIGN KEY (regulation_id) REFERENCES laws_and_rules(id) ON DELETE CASCADE;

ALTER TABLE finances 
ADD CONSTRAINT fk_finances_source 
FOREIGN KEY (source_finance_id) REFERENCES finances(id) ON DELETE SET NULL;

ALTER TABLE finances 
ADD CONSTRAINT fk_finances_category 
FOREIGN KEY (category_id) REFERENCES finance_categories(id) ON DELETE SET NULL;

-- Add indexes for finance relationships
CREATE INDEX IF NOT EXISTS idx_finances_document_id ON finances(document_id);
CREATE INDEX IF NOT EXISTS idx_finances_regulation_id ON finances(regulation_id);
CREATE INDEX IF NOT EXISTS idx_finances_source_id ON finances(source_finance_id);
CREATE INDEX IF NOT EXISTS idx_finances_category_id ON finances(category_id);
CREATE INDEX IF NOT EXISTS idx_finances_year ON finances(year);
CREATE INDEX IF NOT EXISTS idx_finances_budget_type ON finances(budget_type);

-- Enhance finance_performances table relationships
ALTER TABLE finance_performances 
ADD CONSTRAINT fk_finance_performances_document 
FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE;

ALTER TABLE finance_performances 
ADD CONSTRAINT fk_finance_performances_regulation 
FOREIGN KEY (regulation_id) REFERENCES laws_and_rules(id) ON DELETE CASCADE;

ALTER TABLE finance_performances 
ADD CONSTRAINT fk_finance_performances_evaluated_by 
FOREIGN KEY (evaluated_by_id) REFERENCES users(id) ON DELETE SET NULL;

-- Add indexes for finance performance relationships
CREATE INDEX IF NOT EXISTS idx_finance_performances_document_id ON finance_performances(document_id);
CREATE INDEX IF NOT EXISTS idx_finance_performances_regulation_id ON finance_performances(regulation_id);
CREATE INDEX IF NOT EXISTS idx_finance_performances_year ON finance_performances(year);
CREATE INDEX IF NOT EXISTS idx_finance_performances_evaluation_date ON finance_performances(evaluation_date);

-- Enhance summaries table relationships
ALTER TABLE summaries 
ADD CONSTRAINT fk_summaries_created_by 
FOREIGN KEY (created_by_id) REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE summaries 
ADD CONSTRAINT fk_summaries_agency 
FOREIGN KEY (agency_id) REFERENCES agencies(id) ON DELETE SET NULL;

ALTER TABLE summaries 
ADD CONSTRAINT fk_summaries_category 
FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL;

-- Add indexes for summary relationships
CREATE INDEX IF NOT EXISTS idx_summaries_entity_type_id ON summaries(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_summaries_action_type ON summaries(action_type);
CREATE INDEX IF NOT EXISTS idx_summaries_summary_type ON summaries(summary_type);
CREATE INDEX IF NOT EXISTS idx_summaries_publication_date ON summaries(publication_date);
CREATE INDEX IF NOT EXISTS idx_summaries_is_public ON summaries(is_public);
CREATE INDEX IF NOT EXISTS idx_summaries_priority ON summaries(priority);

-- Enhance regulation relationships
ALTER TABLE regulation_document_relationships 
ADD CONSTRAINT fk_reg_doc_rel_regulation 
FOREIGN KEY (regulation_id) REFERENCES laws_and_rules(id) ON DELETE CASCADE;

ALTER TABLE regulation_document_relationships 
ADD CONSTRAINT fk_reg_doc_rel_document 
FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE;

ALTER TABLE regulation_document_relationships 
ADD CONSTRAINT fk_reg_doc_rel_created_by 
FOREIGN KEY (created_by_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add indexes for regulation-document relationships
CREATE INDEX IF NOT EXISTS idx_reg_doc_rel_regulation_id ON regulation_document_relationships(regulation_id);
CREATE INDEX IF NOT EXISTS idx_reg_doc_rel_document_id ON regulation_document_relationships(document_id);
CREATE INDEX IF NOT EXISTS idx_reg_doc_rel_type ON regulation_document_relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_reg_doc_rel_active ON regulation_document_relationships(is_active);

-- Enhance regulation-agency relationships
ALTER TABLE regulation_agency_relationships 
ADD CONSTRAINT fk_reg_agency_rel_regulation 
FOREIGN KEY (regulation_id) REFERENCES laws_and_rules(id) ON DELETE CASCADE;

ALTER TABLE regulation_agency_relationships 
ADD CONSTRAINT fk_reg_agency_rel_agency 
FOREIGN KEY (agency_id) REFERENCES agencies(id) ON DELETE CASCADE;

ALTER TABLE regulation_agency_relationships 
ADD CONSTRAINT fk_reg_agency_rel_created_by 
FOREIGN KEY (created_by_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add indexes for regulation-agency relationships
CREATE INDEX IF NOT EXISTS idx_reg_agency_rel_regulation_id ON regulation_agency_relationships(regulation_id);
CREATE INDEX IF NOT EXISTS idx_reg_agency_rel_agency_id ON regulation_agency_relationships(agency_id);
CREATE INDEX IF NOT EXISTS idx_reg_agency_rel_type ON regulation_agency_relationships(relationship_type);

-- Enhance regulation-category relationships
ALTER TABLE regulation_category_relationships 
ADD CONSTRAINT fk_reg_cat_rel_regulation 
FOREIGN KEY (regulation_id) REFERENCES laws_and_rules(id) ON DELETE CASCADE;

ALTER TABLE regulation_category_relationships 
ADD CONSTRAINT fk_reg_cat_rel_category 
FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE;

ALTER TABLE regulation_category_relationships 
ADD CONSTRAINT fk_reg_cat_rel_created_by 
FOREIGN KEY (created_by_id) REFERENCES users(id) ON DELETE CASCADE;

-- Add indexes for regulation-category relationships
CREATE INDEX IF NOT EXISTS idx_reg_cat_rel_regulation_id ON regulation_category_relationships(regulation_id);
CREATE INDEX IF NOT EXISTS idx_reg_cat_rel_category_id ON regulation_category_relationships(category_id);
CREATE INDEX IF NOT EXISTS idx_reg_cat_rel_type ON regulation_category_relationships(relationship_type);

-- Create system events table for event tracking
CREATE TABLE IF NOT EXISTS system_events (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    event_type TEXT NOT NULL,
    entity_id BIGINT,
    user_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    data TEXT, -- JSON data
    processed BOOLEAN DEFAULT false,
    error TEXT
);

-- Add indexes for system events
CREATE INDEX IF NOT EXISTS idx_system_events_type ON system_events(event_type);
CREATE INDEX IF NOT EXISTS idx_system_events_entity_id ON system_events(entity_id);
CREATE INDEX IF NOT EXISTS idx_system_events_user_id ON system_events(user_id);
CREATE INDEX IF NOT EXISTS idx_system_events_processed ON system_events(processed);
CREATE INDEX IF NOT EXISTS idx_system_events_created_at ON system_events(created_at);

-- Create entity relationships table for generic relationships
CREATE TABLE IF NOT EXISTS entity_relationships (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    source_entity_type TEXT NOT NULL,
    source_entity_id BIGINT NOT NULL,
    target_entity_type TEXT NOT NULL,
    target_entity_id BIGINT NOT NULL,
    relationship_type TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_by_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- Add indexes for entity relationships
CREATE INDEX IF NOT EXISTS idx_entity_rel_source ON entity_relationships(source_entity_type, source_entity_id);
CREATE INDEX IF NOT EXISTS idx_entity_rel_target ON entity_relationships(target_entity_type, target_entity_id);
CREATE INDEX IF NOT EXISTS idx_entity_rel_type ON entity_relationships(relationship_type);
CREATE INDEX IF NOT EXISTS idx_entity_rel_active ON entity_relationships(is_active);

-- Add unique constraint to prevent duplicate relationships
CREATE UNIQUE INDEX IF NOT EXISTS idx_entity_rel_unique 
ON entity_relationships(source_entity_type, source_entity_id, target_entity_type, target_entity_id, relationship_type)
WHERE is_active = true;

-- Create auto-generation configuration table
CREATE TABLE IF NOT EXISTS auto_generation_configs (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    user_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    entity_type TEXT NOT NULL,
    enable_summary_generation BOOLEAN DEFAULT true,
    enable_task_generation BOOLEAN DEFAULT true,
    enable_calendar_generation BOOLEAN DEFAULT true,
    enable_finance_generation BOOLEAN DEFAULT true,
    enable_relationship_generation BOOLEAN DEFAULT true,
    config_data TEXT -- JSON configuration
);

-- Add indexes for auto-generation configs
CREATE INDEX IF NOT EXISTS idx_auto_gen_config_user_id ON auto_generation_configs(user_id);
CREATE INDEX IF NOT EXISTS idx_auto_gen_config_entity_type ON auto_generation_configs(entity_type);

-- Add unique constraint for user-entity type combinations
CREATE UNIQUE INDEX IF NOT EXISTS idx_auto_gen_config_unique 
ON auto_generation_configs(user_id, entity_type);

-- Update existing tables to ensure proper data types and constraints

-- Ensure all date fields are properly typed
ALTER TABLE documents ALTER COLUMN publication_date TYPE TIMESTAMPTZ;
ALTER TABLE documents ALTER COLUMN effective_date TYPE TIMESTAMPTZ;
ALTER TABLE documents ALTER COLUMN termination_date TYPE TIMESTAMPTZ;
ALTER TABLE documents ALTER COLUMN comment_due_date TYPE TIMESTAMPTZ;

ALTER TABLE laws_and_rules ALTER COLUMN effective_date TYPE TIMESTAMPTZ;
ALTER TABLE laws_and_rules ALTER COLUMN termination_date TYPE TIMESTAMPTZ;

ALTER TABLE tasks ALTER COLUMN due_date TYPE TIMESTAMPTZ;
ALTER TABLE tasks ALTER COLUMN start_date TYPE TIMESTAMPTZ;
ALTER TABLE tasks ALTER COLUMN end_date TYPE TIMESTAMPTZ;
ALTER TABLE tasks ALTER COLUMN recurrence_end TYPE TIMESTAMPTZ;
ALTER TABLE tasks ALTER COLUMN reminder_time TYPE TIMESTAMPTZ;
ALTER TABLE tasks ALTER COLUMN completed_at TYPE TIMESTAMPTZ;

-- Add performance optimization indexes
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(type);
CREATE INDEX IF NOT EXISTS idx_documents_publication_date ON documents(publication_date);
CREATE INDEX IF NOT EXISTS idx_documents_effective_date ON documents(effective_date);

CREATE INDEX IF NOT EXISTS idx_laws_and_rules_effective_date ON laws_and_rules(effective_date);
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_is_significant ON laws_and_rules(is_significant);

-- Add full-text search indexes for content
CREATE INDEX IF NOT EXISTS idx_documents_content_search ON documents USING gin(to_tsvector('english', title || ' ' || COALESCE(content, '')));
CREATE INDEX IF NOT EXISTS idx_laws_and_rules_content_search ON laws_and_rules USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- Add comments to document the enhanced relationships
COMMENT ON TABLE entity_relationships IS 'Generic table for tracking relationships between any entities in the system';
COMMENT ON TABLE system_events IS 'Event log for tracking all system activities and triggering auto-generation';
COMMENT ON TABLE auto_generation_configs IS 'User-specific configuration for auto-generation features';

-- Create views for common relationship queries
CREATE OR REPLACE VIEW document_relationship_summary AS
SELECT 
    d.id as document_id,
    d.title as document_title,
    COUNT(DISTINCT rdr.regulation_id) as related_regulations,
    COUNT(DISTINCT t.id) as related_tasks,
    COUNT(DISTINCT f.id) as related_finances,
    COUNT(DISTINCT s.id) as related_summaries
FROM documents d
LEFT JOIN regulation_document_relationships rdr ON d.id = rdr.document_id AND rdr.is_active = true
LEFT JOIN tasks t ON d.id = t.document_id
LEFT JOIN finances f ON d.id = f.document_id
LEFT JOIN summaries s ON d.id = s.entity_id AND s.entity_type = 'document'
GROUP BY d.id, d.title;

CREATE OR REPLACE VIEW regulation_relationship_summary AS
SELECT 
    r.id as regulation_id,
    r.title as regulation_title,
    COUNT(DISTINCT rdr.document_id) as related_documents,
    COUNT(DISTINCT t.id) as related_tasks,
    COUNT(DISTINCT f.id) as related_finances,
    COUNT(DISTINCT s.id) as related_summaries
FROM laws_and_rules r
LEFT JOIN regulation_document_relationships rdr ON r.id = rdr.regulation_id AND rdr.is_active = true
LEFT JOIN tasks t ON r.id = t.regulation_id
LEFT JOIN finances f ON r.id = f.regulation_id
LEFT JOIN summaries s ON r.id = s.entity_id AND s.entity_type = 'regulation'
GROUP BY r.id, r.title;
