-- Migration 008: Task Management System
-- This migration adds comprehensive task and calendar management functionality

-- Create tasks table
CREATE TABLE tasks (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    -- Basic task information
    title TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL DEFAULT 'general',
    status TEXT NOT NULL DEFAULT 'pending',
    priority TEXT NOT NULL DEFAULT 'medium',

    -- Timing information
    due_date TIMESTAMPTZ,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    duration INTEGER, -- Duration in minutes
    is_all_day BOOLEAN NOT NULL DEFAULT false,
    time_zone TEXT NOT NULL DEFAULT 'UTC',

    -- Recurrence information
    is_recurring BOOLEAN NOT NULL DEFAULT false,
    recurrence_rule TEXT, -- RRULE format
    recurrence_end TIMESTAMPTZ,
    parent_task_id BIGINT REFERENCES tasks(id) ON DELETE CASCADE,

    -- Source information (what triggered this task)
    source_type TEXT, -- "document", "regulation", "manual", "parsed_text"
    source_id BIGINT, -- ID of the source entity
    source_text TEXT, -- Original text that generated this task
    parsed_from_text BOOLEAN NOT NULL DEFAULT false,

    -- Relationships
    assigned_to_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    created_by_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    regulation_id BIGINT REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    agency_id BIGINT REFERENCES agencies(id) ON DELETE SET NULL,
    category_id BIGINT REFERENCES categories(id) ON DELETE SET NULL,

    -- Notification and reminder settings
    reminder_enabled BOOLEAN NOT NULL DEFAULT false,
    reminder_time TIMESTAMPTZ,
    notification_sent BOOLEAN NOT NULL DEFAULT false,

    -- Additional metadata
    location TEXT,
    url TEXT,
    notes TEXT,
    tags TEXT, -- Comma-separated tags
    is_public BOOLEAN NOT NULL DEFAULT false,
    completed_at TIMESTAMPTZ,
    completed_by BIGINT REFERENCES users(id) ON DELETE SET NULL
);

-- Create task_attachments table
CREATE TABLE task_attachments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    task_id BIGINT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    mime_type TEXT,
    uploaded_by BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- Create task_comments table
CREATE TABLE task_comments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    task_id BIGINT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    author_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_tasks_deleted_at ON tasks(deleted_at);
CREATE INDEX idx_tasks_assigned_to_id ON tasks(assigned_to_id);
CREATE INDEX idx_tasks_created_by_id ON tasks(created_by_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_type ON tasks(type);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_start_date ON tasks(start_date);
CREATE INDEX idx_tasks_document_id ON tasks(document_id);
CREATE INDEX idx_tasks_regulation_id ON tasks(regulation_id);
CREATE INDEX idx_tasks_agency_id ON tasks(agency_id);
CREATE INDEX idx_tasks_category_id ON tasks(category_id);
CREATE INDEX idx_tasks_parsed_from_text ON tasks(parsed_from_text);
CREATE INDEX idx_tasks_is_public ON tasks(is_public);
CREATE INDEX idx_tasks_source_type ON tasks(source_type);
CREATE INDEX idx_tasks_source_id ON tasks(source_id);

-- Indexes for task_attachments
CREATE INDEX idx_task_attachments_deleted_at ON task_attachments(deleted_at);
CREATE INDEX idx_task_attachments_task_id ON task_attachments(task_id);
CREATE INDEX idx_task_attachments_uploaded_by ON task_attachments(uploaded_by);

-- Indexes for task_comments
CREATE INDEX idx_task_comments_deleted_at ON task_comments(deleted_at);
CREATE INDEX idx_task_comments_task_id ON task_comments(task_id);
CREATE INDEX idx_task_comments_author_id ON task_comments(author_id);

-- Add constraints for task types
ALTER TABLE tasks ADD CONSTRAINT check_task_type 
    CHECK (type IN (
        'review', 'deadline', 'hearing', 'comment', 'general', 
        'reminder', 'follow_up', 'task', 'effective', 'termination', 'meeting'
    ));

-- Add constraints for task status
ALTER TABLE tasks ADD CONSTRAINT check_task_status 
    CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled', 'overdue'));

-- Add constraints for task priority
ALTER TABLE tasks ADD CONSTRAINT check_task_priority 
    CHECK (priority IN ('low', 'medium', 'high', 'urgent'));

-- Add constraints for source type
ALTER TABLE tasks ADD CONSTRAINT check_source_type 
    CHECK (source_type IS NULL OR source_type IN ('document', 'regulation', 'manual', 'parsed_text'));

-- Add constraint to ensure end_date is after start_date
ALTER TABLE tasks ADD CONSTRAINT check_date_order 
    CHECK (start_date IS NULL OR end_date IS NULL OR end_date >= start_date);

-- Add constraint to ensure due_date is reasonable
ALTER TABLE tasks ADD CONSTRAINT check_due_date_reasonable 
    CHECK (due_date IS NULL OR due_date >= '2020-01-01'::date);

-- Add constraint for duration (must be positive)
ALTER TABLE tasks ADD CONSTRAINT check_duration_positive 
    CHECK (duration IS NULL OR duration > 0);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_attachments_updated_at BEFORE UPDATE ON task_attachments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_task_comments_updated_at BEFORE UPDATE ON task_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a view for task statistics
CREATE VIEW task_statistics AS
SELECT 
    COUNT(*) as total_tasks,
    COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_tasks,
    COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_tasks,
    COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_tasks,
    COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_tasks,
    COUNT(CASE WHEN parsed_from_text = true THEN 1 END) as auto_generated_tasks,
    COUNT(CASE WHEN due_date IS NOT NULL AND due_date < NOW() AND status IN ('pending', 'in_progress') THEN 1 END) as tasks_past_due
FROM tasks
WHERE deleted_at IS NULL;

-- Create a view for upcoming tasks (next 30 days)
CREATE VIEW upcoming_tasks AS
SELECT 
    t.*,
    u.username as assigned_to_username,
    u.email as assigned_to_email,
    c.username as created_by_username,
    d.title as document_title,
    r.title as regulation_title,
    a.name as agency_name,
    cat.name as category_name
FROM tasks t
LEFT JOIN users u ON t.assigned_to_id = u.id
LEFT JOIN users c ON t.created_by_id = c.id
LEFT JOIN documents d ON t.document_id = d.id
LEFT JOIN laws_and_rules r ON t.regulation_id = r.id
LEFT JOIN agencies a ON t.agency_id = a.id
LEFT JOIN categories cat ON t.category_id = cat.id
WHERE t.deleted_at IS NULL
    AND t.due_date IS NOT NULL
    AND t.due_date BETWEEN NOW() AND NOW() + INTERVAL '30 days'
    AND t.status IN ('pending', 'in_progress')
ORDER BY t.due_date ASC;

-- Create a view for overdue tasks
CREATE VIEW overdue_tasks AS
SELECT 
    t.*,
    u.username as assigned_to_username,
    u.email as assigned_to_email,
    c.username as created_by_username,
    d.title as document_title,
    r.title as regulation_title,
    a.name as agency_name,
    cat.name as category_name,
    EXTRACT(DAYS FROM NOW() - t.due_date) as days_overdue
FROM tasks t
LEFT JOIN users u ON t.assigned_to_id = u.id
LEFT JOIN users c ON t.created_by_id = c.id
LEFT JOIN documents d ON t.document_id = d.id
LEFT JOIN laws_and_rules r ON t.regulation_id = r.id
LEFT JOIN agencies a ON t.agency_id = a.id
LEFT JOIN categories cat ON t.category_id = cat.id
WHERE t.deleted_at IS NULL
    AND t.due_date IS NOT NULL
    AND t.due_date < NOW()
    AND t.status IN ('pending', 'in_progress')
ORDER BY t.due_date ASC;

-- Add some sample tasks for testing
INSERT INTO tasks (title, description, type, priority, due_date, created_by_id, source_type, parsed_from_text) VALUES
('Review Environmental Impact Assessment', 'Review the environmental impact assessment for the new regulation', 'review', 'high', NOW() + INTERVAL '7 days', 1, 'manual', false),
('Public Comment Period Ends', 'Final day to submit comments on proposed rule changes', 'comment', 'urgent', NOW() + INTERVAL '14 days', 1, 'manual', false),
('Quarterly Compliance Report', 'Prepare and submit quarterly compliance report', 'deadline', 'medium', NOW() + INTERVAL '21 days', 1, 'manual', false);

-- Grant permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON tasks TO federal_register_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON task_attachments TO federal_register_user;
GRANT SELECT, INSERT, UPDATE, DELETE ON task_comments TO federal_register_user;
GRANT SELECT ON task_statistics TO federal_register_user;
GRANT SELECT ON upcoming_tasks TO federal_register_user;
GRANT SELECT ON overdue_tasks TO federal_register_user;
GRANT USAGE, SELECT ON SEQUENCE tasks_id_seq TO federal_register_user;
GRANT USAGE, SELECT ON SEQUENCE task_attachments_id_seq TO federal_register_user;
GRANT USAGE, SELECT ON SEQUENCE task_comments_id_seq TO federal_register_user;
