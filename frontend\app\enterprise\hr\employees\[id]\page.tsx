'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { hrApi } from '../../../../services/enterpriseApi';
import { Employee } from '../../../../types/enterprise';

const EmployeeViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const employeeId = parseInt(params.id as string);
  
  const [employee, setEmployee] = useState<Employee | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (employeeId) {
      fetchEmployee();
    }
  }, [employeeId]);

  const fetchEmployee = async () => {
    try {
      setLoading(true);
      const response = await hrApi.getEmployee(employeeId);
      setEmployee(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch employee');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this employee?')) return;
    
    try {
      await hrApi.deleteEmployee(employeeId);
      router.push('/enterprise/hr/employees');
    } catch (err: any) {
      setError(err.message || 'Failed to delete employee');
    }
  };

  const handleTerminate = async () => {
    const terminationDate = prompt('Enter termination date (YYYY-MM-DD):');
    const terminationReason = prompt('Enter termination reason:');
    
    if (terminationDate && terminationReason) {
      try {
        await hrApi.terminateEmployee(employeeId, {
          termination_date: terminationDate,
          termination_reason: terminationReason
        });
        await fetchEmployee(); // Refresh data
      } catch (err: any) {
        setError(err.message || 'Failed to terminate employee');
      }
    }
  };

  if (loading) return <div className="p-6">Loading employee...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!employee) return <div className="p-6">Employee not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Employee Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/hr/employees')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Employees
          </button>
          <button
            onClick={() => router.push(`/enterprise/hr/employees/${employeeId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Employee
          </button>
          {employee.employment_status === 'active' && (
            <button
              onClick={handleTerminate}
              className="bg-orange-600 text-white px-4 py-2 rounded hover:bg-orange-700"
            >
              Terminate
            </button>
          )}
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Employee
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {employee.first_name} {employee.middle_name} {employee.last_name}
              </h2>
              <p className="text-sm text-gray-600">Employee ID: {employee.employee_id}</p>
              <p className="text-sm text-gray-600">{employee.job_title}</p>
            </div>
            <div className="text-right">
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                employee.employment_status === 'active' 
                  ? 'bg-green-100 text-green-800'
                  : employee.employment_status === 'inactive'
                  ? 'bg-red-100 text-red-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {employee.employment_status}
              </span>
            </div>
          </div>
        </div>

        {/* Personal Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Work Email</label>
              <p className="mt-1 text-sm text-gray-900">{employee.email}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Work Phone</label>
              <p className="mt-1 text-sm text-gray-900">{employee.phone || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Personal Email</label>
              <p className="mt-1 text-sm text-gray-900">{employee.personal_email || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Personal Phone</label>
              <p className="mt-1 text-sm text-gray-900">{employee.personal_phone || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Date of Birth</label>
              <p className="mt-1 text-sm text-gray-900">
                {employee.date_of_birth ? new Date(employee.date_of_birth).toLocaleDateString() : 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Gender</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{employee.gender || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Marital Status</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{employee.marital_status || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Nationality</label>
              <p className="mt-1 text-sm text-gray-900">{employee.nationality || 'N/A'}</p>
            </div>
          </div>

          {employee.address && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Address</label>
              <p className="mt-1 text-sm text-gray-900">{employee.address}</p>
            </div>
          )}

          {employee.emergency_contact && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Emergency Contact</label>
              <p className="mt-1 text-sm text-gray-900">{employee.emergency_contact}</p>
            </div>
          )}
        </div>

        {/* Employment Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Employment Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Hire Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(employee.hire_date).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Employment Type</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{employee.employment_type.replace('_', ' ')}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Department</label>
              <p className="mt-1 text-sm text-gray-900">
                {employee.department?.department_name || 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Position</label>
              <p className="mt-1 text-sm text-gray-900">
                {employee.position?.position_title || 'N/A'}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Manager</label>
              <p className="mt-1 text-sm text-gray-900">
                {employee.manager ? 
                  `${employee.manager.first_name} ${employee.manager.last_name}` : 
                  'N/A'
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Work Location</label>
              <p className="mt-1 text-sm text-gray-900">{employee.work_location || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Time Zone</label>
              <p className="mt-1 text-sm text-gray-900">{employee.time_zone || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Access Level</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{employee.access_level}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Security Clearance</label>
              <p className="mt-1 text-sm text-gray-900">{employee.security_clearance || 'N/A'}</p>
            </div>
          </div>

          {employee.job_description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Job Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {employee.job_description}
              </div>
            </div>
          )}

          {employee.work_schedule && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Work Schedule</label>
              <p className="mt-1 text-sm text-gray-900">{employee.work_schedule}</p>
            </div>
          )}
        </div>

        {/* Compensation */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Compensation</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {employee.currency} {employee.base_salary.toLocaleString()}
              </div>
              <div className="text-sm text-green-600">Base Salary</div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Pay Frequency</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{employee.pay_frequency.replace('_', ' ')}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Performance Rating</label>
              <p className="mt-1 text-sm text-gray-900">
                {employee.performance_rating ? `${employee.performance_rating}/5` : 'N/A'}
              </p>
            </div>
          </div>
        </div>

        {/* Benefits & Leave */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Benefits & Leave</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{employee.vacation_days}</div>
              <div className="text-sm text-blue-600">Vacation Days</div>
            </div>
            
            <div className="bg-yellow-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{employee.sick_days}</div>
              <div className="text-sm text-yellow-600">Sick Days</div>
            </div>
            
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{employee.personal_days}</div>
              <div className="text-sm text-purple-600">Personal Days</div>
            </div>
          </div>
        </div>

        {/* Employment Dates */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Important Dates</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Hire Date</label>
              <p className="mt-1 text-sm text-gray-900">
                {new Date(employee.hire_date).toLocaleDateString()}
              </p>
            </div>
            
            {employee.probation_end_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Probation End Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(employee.probation_end_date).toLocaleDateString()}
                </p>
              </div>
            )}
            
            {employee.termination_date && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Termination Date</label>
                <p className="mt-1 text-sm text-gray-900">
                  {new Date(employee.termination_date).toLocaleDateString()}
                </p>
              </div>
            )}
          </div>

          {employee.termination_reason && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Termination Reason</label>
              <p className="mt-1 text-sm text-gray-900">{employee.termination_reason}</p>
            </div>
          )}
        </div>

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(employee.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(employee.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeViewPage;
