package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// TaskCommentRequest represents the request structure for task comments
type TaskCommentRequest struct {
	TaskID  uint   `json:"task_id" binding:"required"`
	Content string `json:"content" binding:"required"`
	UserID  uint   `json:"user_id" binding:"required"`
}

// GetTaskComments returns comments for a task
func GetTaskComments(c *gin.Context) {
	taskID, valid := ValidateID(c, "task_id")
	if !valid {
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.Default<PERSON>uery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify task exists
	var task models.Task
	if err := db.First(&task, taskID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Count total comments for this task
	var total int64
	db.Model(&models.TaskComment{}).Where("task_id = ?", taskID).Count(&total)

	// Get comments with pagination
	var comments []models.TaskComment
	offset := (page - 1) * perPage
	if err := db.Preload("User").
		Where("task_id = ?", taskID).
		Order("created_at DESC").
		Limit(perPage).
		Offset(offset).
		Find(&comments).Error; err != nil {
		HandleInternalError(c, "Failed to fetch task comments: "+err.Error())
		return
	}

	// Convert to response format
	commentResponses := make([]gin.H, len(comments))
	for i, comment := range comments {
		commentResponses[i] = gin.H{
			"id":         comment.ID,
			"task_id":    comment.TaskID,
			"content":    comment.Content,
			"author":     comment.Author,
			"author_id":  comment.AuthorID,
			"created_at": comment.CreatedAt,
			"updated_at": comment.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       commentResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateTaskComment creates a new task comment
func CreateTaskComment(c *gin.Context) {
	var req TaskCommentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify task exists
	var task models.Task
	if err := db.First(&task, req.TaskID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid task",
				Message: "Task not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Create task comment
	comment := &models.TaskComment{
		TaskID:   req.TaskID,
		Content:  req.Content,
		AuthorID: req.UserID,
	}

	if err := db.Create(comment).Error; err != nil {
		HandleInternalError(c, "Failed to create task comment: "+err.Error())
		return
	}

	// Load related data
	db.Preload("User").First(comment, comment.ID)

	response := gin.H{
		"id":         comment.ID,
		"task_id":    comment.TaskID,
		"content":    comment.Content,
		"author":     comment.Author,
		"author_id":  comment.AuthorID,
		"created_at": comment.CreatedAt,
		"updated_at": comment.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Task comment created successfully",
		Data:    response,
	})
}

// UpdateTaskComment updates an existing task comment
func UpdateTaskComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req TaskCommentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing task comment
	var comment models.TaskComment
	if err := db.First(&comment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task comment")
			return
		}
		HandleInternalError(c, "Failed to fetch task comment: "+err.Error())
		return
	}

	// Update task comment fields
	comment.Content = req.Content

	if err := db.Save(&comment).Error; err != nil {
		HandleInternalError(c, "Failed to update task comment: "+err.Error())
		return
	}

	// Load related data
	db.Preload("User").First(&comment, comment.ID)

	response := gin.H{
		"id":         comment.ID,
		"task_id":    comment.TaskID,
		"content":    comment.Content,
		"author":     comment.Author,
		"author_id":  comment.AuthorID,
		"created_at": comment.CreatedAt,
		"updated_at": comment.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task comment updated successfully",
		Data:    response,
	})
}

// DeleteTaskComment deletes a task comment
func DeleteTaskComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if task comment exists
	var comment models.TaskComment
	if err := db.First(&comment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task comment")
			return
		}
		HandleInternalError(c, "Failed to fetch task comment: "+err.Error())
		return
	}

	// Delete task comment
	if err := db.Delete(&comment).Error; err != nil {
		HandleInternalError(c, "Failed to delete task comment: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task comment deleted successfully",
	})
}
