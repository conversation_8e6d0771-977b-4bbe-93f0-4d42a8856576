package services

import (
	"crypto/md5"
	"crypto/sha256"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"

	"federal-register-clone/internal/config"
)

// FileStorageService handles file storage operations
type FileStorageService struct {
	config    *config.Config
	uploadDir string
}

// NewFileStorageService creates a new file storage service
func NewFileStorageService(cfg *config.Config) *FileStorageService {
	uploadDir := cfg.Upload.Path
	if uploadDir == "" {
		uploadDir = "./uploads"
	}

	return &FileStorageService{
		config:    cfg,
		uploadDir: uploadDir,
	}
}

// StoredFile represents a stored file with metadata
type StoredFile struct {
	Path         string
	OriginalName string
	Size         int64
	MimeType     string
	Checksum     string
	SHA256Hash   string
}

// StoreFile stores a file and returns metadata
func (s *FileStorageService) StoreFile(file *multipart.FileHeader, subDir string) (*StoredFile, error) {
	// Validate file type
	if !s.isAllowedFileType(file.Filename) {
		return nil, fmt.Errorf("file type not allowed: %s", filepath.Ext(file.Filename))
	}

	// Validate file size
	maxSize := s.parseMaxFileSize()
	if file.Size > maxSize {
		return nil, fmt.Errorf("file size %d exceeds maximum allowed size %d", file.Size, maxSize)
	}

	// Create directory structure
	targetDir := filepath.Join(s.uploadDir, subDir)
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory: %w", err)
	}

	// Generate unique filename
	ext := filepath.Ext(file.Filename)
	baseName := strings.TrimSuffix(file.Filename, ext)
	timestamp := time.Now().Unix()
	uniqueFilename := fmt.Sprintf("%s_%d%s", s.sanitizeFilename(baseName), timestamp, ext)
	targetPath := filepath.Join(targetDir, uniqueFilename)

	// Open source file
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open source file: %w", err)
	}
	defer src.Close()

	// Create destination file
	dst, err := os.Create(targetPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create destination file: %w", err)
	}
	defer dst.Close()

	// Create hash writers
	md5Hash := md5.New()
	sha256Hash := sha256.New()
	multiWriter := io.MultiWriter(dst, md5Hash, sha256Hash)

	// Copy file content and calculate hashes
	size, err := io.Copy(multiWriter, src)
	if err != nil {
		// Clean up on error
		os.Remove(targetPath)
		return nil, fmt.Errorf("failed to copy file: %w", err)
	}

	// Verify file size
	if size != file.Size {
		os.Remove(targetPath)
		return nil, fmt.Errorf("file size mismatch: expected %d, got %d", file.Size, size)
	}

	return &StoredFile{
		Path:         targetPath,
		OriginalName: file.Filename,
		Size:         size,
		MimeType:     file.Header.Get("Content-Type"),
		Checksum:     fmt.Sprintf("%x", md5Hash.Sum(nil)),
		SHA256Hash:   fmt.Sprintf("%x", sha256Hash.Sum(nil)),
	}, nil
}

// GetFile retrieves a file from storage
func (s *FileStorageService) GetFile(filePath string) (*os.File, error) {
	// Validate path is within upload directory
	absUploadDir, err := filepath.Abs(s.uploadDir)
	if err != nil {
		return nil, fmt.Errorf("failed to get absolute upload directory: %w", err)
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get absolute file path: %w", err)
	}

	if !strings.HasPrefix(absFilePath, absUploadDir) {
		return nil, fmt.Errorf("file path is outside upload directory")
	}

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return nil, fmt.Errorf("file not found: %s", filePath)
	}

	// Open file
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}

	return file, nil
}

// DeleteFile removes a file from storage
func (s *FileStorageService) DeleteFile(filePath string) error {
	// Validate path is within upload directory
	absUploadDir, err := filepath.Abs(s.uploadDir)
	if err != nil {
		return fmt.Errorf("failed to get absolute upload directory: %w", err)
	}

	absFilePath, err := filepath.Abs(filePath)
	if err != nil {
		return fmt.Errorf("failed to get absolute file path: %w", err)
	}

	if !strings.HasPrefix(absFilePath, absUploadDir) {
		return fmt.Errorf("file path is outside upload directory")
	}

	// Remove file
	if err := os.Remove(filePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete file: %w", err)
	}

	return nil
}

// VerifyFileIntegrity verifies file integrity using checksum
func (s *FileStorageService) VerifyFileIntegrity(filePath, expectedChecksum string) (bool, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return false, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return false, fmt.Errorf("failed to calculate checksum: %w", err)
	}

	actualChecksum := fmt.Sprintf("%x", hash.Sum(nil))
	return actualChecksum == expectedChecksum, nil
}

// GetFileInfo returns file information
func (s *FileStorageService) GetFileInfo(filePath string) (*StoredFile, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get file info: %w", err)
	}

	// Calculate checksum
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	md5Hash := md5.New()
	sha256Hash := sha256.New()
	multiWriter := io.MultiWriter(md5Hash, sha256Hash)

	if _, err := io.Copy(multiWriter, file); err != nil {
		return nil, fmt.Errorf("failed to calculate hashes: %w", err)
	}

	return &StoredFile{
		Path:         filePath,
		OriginalName: filepath.Base(filePath),
		Size:         info.Size(),
		Checksum:     fmt.Sprintf("%x", md5Hash.Sum(nil)),
		SHA256Hash:   fmt.Sprintf("%x", sha256Hash.Sum(nil)),
	}, nil
}

// isAllowedFileType checks if the file type is allowed
func (s *FileStorageService) isAllowedFileType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	ext = strings.TrimPrefix(ext, ".")

	for _, allowedType := range s.config.Upload.AllowedFileTypes {
		if ext == strings.ToLower(allowedType) {
			return true
		}
	}
	return false
}

// parseMaxFileSize parses the max file size from config
func (s *FileStorageService) parseMaxFileSize() int64 {
	sizeStr := s.config.Upload.MaxFileSize
	if sizeStr == "" {
		return 50 * 1024 * 1024 // Default 50MB
	}

	// Parse size string (e.g., "50MB", "10GB")
	sizeStr = strings.ToUpper(sizeStr)
	var multiplier int64 = 1

	if strings.HasSuffix(sizeStr, "KB") {
		multiplier = 1024
		sizeStr = strings.TrimSuffix(sizeStr, "KB")
	} else if strings.HasSuffix(sizeStr, "MB") {
		multiplier = 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "MB")
	} else if strings.HasSuffix(sizeStr, "GB") {
		multiplier = 1024 * 1024 * 1024
		sizeStr = strings.TrimSuffix(sizeStr, "GB")
	}

	// Try to parse the number
	var size int64 = 50 // Default to 50 if parsing fails
	fmt.Sscanf(sizeStr, "%d", &size)

	return size * multiplier
}

// sanitizeFilename removes unsafe characters from filename
func (s *FileStorageService) sanitizeFilename(filename string) string {
	// Replace unsafe characters
	unsafe := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", " "}
	result := filename

	for _, char := range unsafe {
		result = strings.ReplaceAll(result, char, "_")
	}

	// Limit length
	if len(result) > 100 {
		result = result[:100]
	}

	return result
}
