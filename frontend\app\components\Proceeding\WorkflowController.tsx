'use client'

import React, { useState, useEffect } from 'react';
import {
  Check<PERSON>ircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  ArrowRightIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { ProceedingValidationService } from '../../services/proceedingValidation';

interface ProceedingStep {
  id: number;
  step_order: number;
  step_type: string;
  name: string;
  status: string;
  requires_previous_completion: boolean;
  is_mandatory: boolean;
  allows_parallel_execution: boolean;
  requires_review: boolean;
  review_completed: boolean;
  completion_criteria: string;
  completion_evidence?: string;
}

interface Proceeding {
  id: number;
  name: string;
  status: string;
  requires_mandatory_review: boolean;
  minimum_steps_required: number;
  sequential_execution: boolean;
  proceeding_steps: ProceedingStep[];
  progress_percent: number;
}

interface WorkflowControllerProps {
  proceeding: Proceeding;
  onStepStatusChange: (stepId: number, newStatus: string) => Promise<void>;
  onProceedingStatusChange: (newStatus: string) => Promise<void>;
  canEdit?: boolean;
}

const WorkflowController: React.FC<WorkflowControllerProps> = ({
  proceeding,
  onStepStatusChange,
  onProceedingStatusChange,
  canEdit = false
}) => {
  const [validationResult, setValidationResult] = useState<any>(null);
  const [nextAvailableStep, setNextAvailableStep] = useState<ProceedingStep | null>(null);
  const [completionCheck, setCompletionCheck] = useState<any>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    // Validate proceeding whenever it changes
    const validation = ProceedingValidationService.validateProceeding(proceeding);
    setValidationResult(validation);

    // Find next available step
    const nextStep = ProceedingValidationService.getNextAvailableStep(proceeding.proceeding_steps);
    if (nextStep && 'name' in nextStep && 'id' in nextStep && 'step_order' in nextStep) {
      setNextAvailableStep(nextStep as ProceedingStep);
    }

    // Check if proceeding can be completed
    const completion = ProceedingValidationService.canCompleteProceeding(proceeding);
    setCompletionCheck(completion);
  }, [proceeding]);

  const handleStepTransition = async (step: ProceedingStep, newStatus: string) => {
    if (!canEdit) return;

    const transitionCheck = ProceedingValidationService.canTransitionStepStatus(
      step, 
      newStatus, 
      proceeding.proceeding_steps
    );

    if (!transitionCheck.canTransition) {
      alert(`Cannot transition step: ${transitionCheck.reason}`);
      return;
    }

    setIsProcessing(true);
    try {
      await onStepStatusChange(step.id, newStatus);
    } catch (error) {
      console.error('Failed to update step status:', error);
      alert('Failed to update step status. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleStartNextStep = async () => {
    if (!nextAvailableStep || !canEdit) return;
    await handleStepTransition(nextAvailableStep, 'in_progress');
  };

  const handleCompleteProceeding = async () => {
    if (!completionCheck?.isValid || !canEdit) return;

    const confirmed = window.confirm(
      'Are you sure you want to mark this proceeding as completed? This action cannot be undone.'
    );

    if (confirmed) {
      setIsProcessing(true);
      try {
        await onProceedingStatusChange('completed');
      } catch (error) {
        console.error('Failed to complete proceeding:', error);
        alert('Failed to complete proceeding. Please try again.');
      } finally {
        setIsProcessing(false);
      }
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-100';
      case 'in_progress':
        return 'text-blue-600 bg-blue-100';
      case 'under_review':
        return 'text-yellow-600 bg-yellow-100';
      case 'failed':
        return 'text-red-600 bg-red-100';
      case 'on_hold':
        return 'text-gray-600 bg-gray-100';
      default:
        return 'text-gray-500 bg-gray-50';
    }
  };

  const getStepTypeName = (stepType: string) => {
    switch (stepType) {
      case 'notice_of_intent':
        return 'Notice of Intent';
      case 'notice_proposed_rule':
        return 'Notice of Proposed Rulemaking';
      case 'public_comment':
        return 'Public Comment Period';
      case 'final_rule':
        return 'Final Rule';
      case 'regulation_publication':
        return 'Regulation Publication';
      default:
        return stepType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  return (
    <div className="space-y-6">
      {/* Proceeding Status Overview */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Workflow Status
          </h3>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(proceeding.status)}`}>
            {proceeding.status.replace('_', ' ').toUpperCase()}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600 dark:text-gray-400">Overall Progress</span>
            <span className="text-sm font-medium text-gray-900 dark:text-white">
              {proceeding.progress_percent}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
            <div
              className="bg-blue-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${proceeding.progress_percent}%` }}
            />
          </div>
        </div>

        {/* Step Summary */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {['not_started', 'in_progress', 'under_review', 'completed', 'failed'].map(status => {
            const count = proceeding.proceeding_steps.filter(s => s.status === status).length;
            return (
              <div key={status} className="text-center">
                <div className={`text-xl font-bold ${getStatusColor(status).split(' ')[0]}`}>
                  {count}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                  {status.replace('_', ' ')}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Validation Results */}
      {validationResult && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Validation Status
          </h4>
          
          {validationResult.isValid ? (
            <div className="flex items-center space-x-2 text-green-600">
              <CheckCircleIcon className="h-5 w-5" />
              <span className="text-sm">All validation checks passed</span>
            </div>
          ) : (
            <div className="space-y-3">
              {validationResult.errors.map((error: string, index: number) => (
                <div key={index} className="flex items-start space-x-2 text-red-600">
                  <ExclamationTriangleIcon className="h-5 w-5 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{error}</span>
                </div>
              ))}
            </div>
          )}

          {validationResult.warnings && validationResult.warnings.length > 0 && (
            <div className="mt-4 space-y-2">
              {validationResult.warnings.map((warning: string, index: number) => (
                <div key={index} className="flex items-start space-x-2 text-yellow-600">
                  <InformationCircleIcon className="h-5 w-5 mt-0.5 flex-shrink-0" />
                  <span className="text-sm">{warning}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Workflow Actions */}
      {canEdit && (
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
            Workflow Actions
          </h4>
          
          <div className="space-y-4">
            {/* Next Step Action */}
            {nextAvailableStep && (
              <div className="flex items-center justify-between p-4 border border-blue-200 dark:border-blue-700 rounded-lg bg-blue-50 dark:bg-blue-900">
                <div className="flex items-center space-x-3">
                  <ClockIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  <div>
                    <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                      Next Step Available
                    </p>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      {getStepTypeName(nextAvailableStep.step_type)}
                    </p>
                  </div>
                </div>
                <button
                  onClick={handleStartNextStep}
                  disabled={isProcessing}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  Start Step
                </button>
              </div>
            )}

            {/* Completion Action */}
            {completionCheck?.isValid && (
              <div className="flex items-center justify-between p-4 border border-green-200 dark:border-green-700 rounded-lg bg-green-50 dark:bg-green-900">
                <div className="flex items-center space-x-3">
                  <CheckCircleIcon className="h-5 w-5 text-green-600 dark:text-green-400" />
                  <div>
                    <p className="text-sm font-medium text-green-900 dark:text-green-100">
                      Ready for Completion
                    </p>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      All mandatory steps completed and reviewed
                    </p>
                  </div>
                </div>
                <button
                  onClick={handleCompleteProceeding}
                  disabled={isProcessing}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  <CheckCircleIcon className="h-4 w-4 mr-2" />
                  Complete Proceeding
                </button>
              </div>
            )}

            {/* Completion Blocked */}
            {completionCheck && !completionCheck.isValid && (
              <div className="p-4 border border-red-200 dark:border-red-700 rounded-lg bg-red-50 dark:bg-red-900">
                <div className="flex items-start space-x-3">
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="text-sm font-medium text-red-900 dark:text-red-100 mb-2">
                      Cannot Complete Proceeding
                    </p>
                    <div className="space-y-1">
                      {completionCheck.errors.map((error: string, index: number) => (
                        <p key={index} className="text-sm text-red-700 dark:text-red-300">
                          • {error}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Sequential Flow Visualization */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-white mb-4">
          Sequential Flow
        </h4>
        
        <div className="flex flex-wrap items-center gap-2">
          {proceeding.proceeding_steps
            .sort((a, b) => a.step_order - b.step_order)
            .map((step, index) => (
              <React.Fragment key={step.id}>
                <div className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${getStatusColor(step.status)} border-current`}>
                  <span className="text-xs font-medium">
                    {step.step_order}
                  </span>
                  <span className="text-xs">
                    {getStepTypeName(step.step_type)}
                  </span>
                  {step.is_mandatory && (
                    <span className="text-xs">*</span>
                  )}
                </div>
                {index < proceeding.proceeding_steps.length - 1 && (
                  <ArrowRightIcon className="h-4 w-4 text-gray-400" />
                )}
              </React.Fragment>
            ))}
        </div>
        
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
          * Mandatory steps
        </p>
      </div>
    </div>
  );
};

export default WorkflowController;
