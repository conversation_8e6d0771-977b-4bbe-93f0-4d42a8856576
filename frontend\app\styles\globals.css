@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import component styles */
@import './components.css';

/* Custom CSS Variables for consistent theming */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;
}

/* Dark mode variables */
.dark {
  --primary-50: #172554;
  --primary-100: #1e3a8a;
  --primary-200: #1e40af;
  --primary-300: #1d4ed8;
  --primary-400: #2563eb;
  --primary-500: #3b82f6;
  --primary-600: #60a5fa;
  --primary-700: #93c5fd;
  --primary-800: #bfdbfe;
  --primary-900: #dbeafe;
  --primary-950: #eff6ff;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom component styles */
@layer components {
  /* Button styles */
  .btn-primary {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  .btn-secondary {
    @apply inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  .btn-outline {
    @apply inline-flex items-center px-4 py-2 border border-primary-600 text-sm font-medium rounded-md text-primary-600 dark:text-primary-400 bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700;
  }

  .card-hover {
    @apply card hover:shadow-lg transition-shadow duration-200;
  }

  /* Input styles */
  .input-primary {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300;
  }

  .badge-success {
    @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
  }

  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
  }

  .badge-error {
    @apply badge bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
  }

  .badge-gray {
    @apply badge bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300;
  }

  /* Link styles */
  .link-primary {
    @apply text-primary-600 dark:text-primary-400 hover:text-primary-500 dark:hover:text-primary-300 transition-colors duration-200;
  }

  /* Text utilities */
  .text-muted {
    @apply text-gray-600 dark:text-gray-400;
  }

  .text-subtle {
    @apply text-gray-500 dark:text-gray-500;
  }

  /* Layout utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Loading animation */
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(107 114 128);
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Custom utilities */
@layer utilities {
  /* Focus visible utilities */
  .focus-visible-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary-500;
  }

  /* Transition utilities */
  .transition-base {
    @apply transition-colors duration-200 ease-in-out;
  }

  .transition-transform {
    @apply transition duration-200 ease-in-out;
    transition-property: transform;
  }

  /* Shadow utilities */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  /* Gradient utilities */
  .gradient-primary {
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break-before {
    page-break-before: always;
  }

  .print-break-after {
    page-break-after: always;
  }

  body {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    @apply border-2 border-gray-900 dark:border-gray-100;
  }

  .btn-primary {
    @apply border-2 border-primary-800;
  }

  .btn-secondary {
    @apply border-2 border-gray-800 dark:border-gray-200;
  }
}

/* Focus styles for keyboard navigation */
.focus-visible:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Custom selection colors */
::selection {
  background-color: var(--primary-200);
  color: var(--primary-900);
}

.dark ::selection {
  background-color: var(--primary-800);
  color: var(--primary-100);
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin 2s linear infinite;
}

/* Fade in animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Slide in animation */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Scale in animation */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}
