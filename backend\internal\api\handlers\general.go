package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// ContactFormRequest represents a contact form submission
type ContactFormRequest struct {
	Name    string `json:"name" binding:"required"`
	Email   string `json:"email" binding:"required,email"`
	Subject string `json:"subject" binding:"required"`
	Message string `json:"message" binding:"required"`
}

// SubmitContactForm handles contact form submissions
func SubmitContactForm(c *gin.Context) {
	var req ContactFormRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Store contact form submission in database
	contactForm := models.ContactForm{
		Name:      req.Name,
		Email:     req.Email,
		Subject:   req.Subject,
		Message:   req.Message,
		Status:    "received",
		IPAddress: c.<PERSON>(),
		UserAgent: <PERSON><PERSON>("User-Agent"),
	}

	if err := db.Create(&contactForm).Error; err != nil {
		HandleInternalError(c, "Failed to save contact form submission")
		return
	}

	// Send email notification to administrators if email service is available
	if emailService != nil {
		adminEmail := "<EMAIL>" // This should come from config
		err := emailService.SendContactFormNotification(adminEmail, req.Name, req.Email, req.Subject, req.Message)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Failed to send contact form notification: %v\n", err)
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Contact form submitted successfully",
		Data: gin.H{
			"id":      contactForm.ID,
			"name":    req.Name,
			"email":   req.Email,
			"subject": req.Subject,
			"status":  "received",
		},
	})
}

// GetSystemStatus returns comprehensive real-time system status information
func GetSystemStatus(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Initialize real-time monitoring service
	monitoringService := services.NewSystemMonitoringService(db)

	// Get comprehensive system health metrics
	healthMetrics, err := monitoringService.GetRealTimeHealthMetrics()
	if err != nil {
		c.JSON(http.StatusServiceUnavailable, ErrorResponse{
			Error:   "Failed to retrieve system metrics",
			Message: err.Error(),
		})
		return
	}

	// Get performance metrics
	performanceMetrics, err := monitoringService.GetPerformanceMetrics()
	if err != nil {
		// Log error but continue with basic metrics
		fmt.Printf("Failed to get performance metrics: %v\n", err)
	}

	// Get resource utilization
	resourceMetrics, err := monitoringService.GetResourceUtilization()
	if err != nil {
		// Log error but continue with basic metrics
		fmt.Printf("Failed to get resource metrics: %v\n", err)
	}

	// Determine overall system status
	overallStatus := monitoringService.DetermineOverallStatus(healthMetrics, performanceMetrics, resourceMetrics)

	status := gin.H{
		"status":              overallStatus.Status,
		"timestamp":           time.Now().Format(time.RFC3339),
		"version":             "1.0.0",
		"uptime":              overallStatus.Uptime,
		"health_metrics":      healthMetrics,
		"performance_metrics": performanceMetrics,
		"resource_metrics":    resourceMetrics,
		"alerts":              overallStatus.Alerts,
		"recommendations":     overallStatus.Recommendations,
	}

	// Return appropriate HTTP status based on system health
	if overallStatus.Status == "healthy" {
		c.JSON(http.StatusOK, status)
	} else if overallStatus.Status == "degraded" {
		c.JSON(http.StatusOK, status) // Still return 200 but with degraded status
	} else {
		c.JSON(http.StatusServiceUnavailable, status)
	}
}

// ConnectionRequest represents a request to create a connection between entities
type ConnectionRequest struct {
	SourceType   string `json:"source_type" binding:"required"`
	SourceID     uint   `json:"source_id" binding:"required"`
	TargetType   string `json:"target_type" binding:"required"`
	TargetID     uint   `json:"target_id" binding:"required"`
	Relationship string `json:"relationship"`
	Description  string `json:"description"`
	IsActive     bool   `json:"is_active"`
	Metadata     string `json:"metadata"`
}

// GetDocumentFinanceConnections returns document finance connections
func GetDocumentFinanceConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is document and target is finance
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "document", id, "finance").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document finance connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document finance connections retrieved successfully",
		Data:    connections,
	})
}

// CreateDocumentFinanceConnection creates a document finance connection
func CreateDocumentFinanceConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is document and target is finance
	if req.SourceType != "document" || req.TargetType != "finance" {
		HandleBadRequest(c, "Invalid connection type for document-finance connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create document finance connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Document finance connection created successfully",
		Data:    connection,
	})
}

// GetDocumentProceedingConnections returns document proceeding connections
func GetDocumentProceedingConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is document and target is proceeding
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "document", id, "proceeding").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document proceeding connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document proceeding connections retrieved successfully",
		Data:    connections,
	})
}

// CreateDocumentProceedingConnection creates a document proceeding connection
func CreateDocumentProceedingConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is document and target is proceeding
	if req.SourceType != "document" || req.TargetType != "proceeding" {
		HandleBadRequest(c, "Invalid connection type for document-proceeding connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create document proceeding connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Document proceeding connection created successfully",
		Data:    connection,
	})
}

// GetDocumentTaskConnections returns document task connections
func GetDocumentTaskConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is document and target is task
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "document", id, "task").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch document task connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Document task connections retrieved successfully",
		Data:    connections,
	})
}

// CreateDocumentTaskConnection creates a document task connection
func CreateDocumentTaskConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is document and target is task
	if req.SourceType != "document" || req.TargetType != "task" {
		HandleBadRequest(c, "Invalid connection type for document-task connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create document task connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Document task connection created successfully",
		Data:    connection,
	})
}

// GetRegulationFinanceConnections returns regulation finance connections
func GetRegulationFinanceConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is regulation and target is finance
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "regulation", id, "finance").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation finance connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation finance connections retrieved successfully",
		Data:    connections,
	})
}

// CreateRegulationFinanceConnection creates a regulation finance connection
func CreateRegulationFinanceConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is regulation and target is finance
	if req.SourceType != "regulation" || req.TargetType != "finance" {
		HandleBadRequest(c, "Invalid connection type for regulation-finance connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation finance connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation finance connection created successfully",
		Data:    connection,
	})
}

// GetRegulationProceedingConnections returns regulation proceeding connections
func GetRegulationProceedingConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is regulation and target is proceeding
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "regulation", id, "proceeding").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation proceeding connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation proceeding connections retrieved successfully",
		Data:    connections,
	})
}

// CreateRegulationProceedingConnection creates a regulation proceeding connection
func CreateRegulationProceedingConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is regulation and target is proceeding
	if req.SourceType != "regulation" || req.TargetType != "proceeding" {
		HandleBadRequest(c, "Invalid connection type for regulation-proceeding connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation proceeding connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation proceeding connection created successfully",
		Data:    connection,
	})
}

// GetRegulationTaskConnections returns regulation task connections
func GetRegulationTaskConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is regulation and target is task
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "regulation", id, "task").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation task connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation task connections retrieved successfully",
		Data:    connections,
	})
}

// CreateRegulationTaskConnection creates a regulation task connection
func CreateRegulationTaskConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is regulation and target is task
	if req.SourceType != "regulation" || req.TargetType != "task" {
		HandleBadRequest(c, "Invalid connection type for regulation-task connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation task connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation task connection created successfully",
		Data:    connection,
	})
}

// GetAgencyFinanceConnections returns agency finance connections
func GetAgencyFinanceConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is agency and target is finance
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "agency", id, "finance").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch agency finance connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency finance connections retrieved successfully",
		Data:    connections,
	})
}

// CreateAgencyFinanceConnection creates an agency finance connection
func CreateAgencyFinanceConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is agency and target is finance
	if req.SourceType != "agency" || req.TargetType != "finance" {
		HandleBadRequest(c, "Invalid connection type for agency-finance connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create agency finance connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Agency finance connection created successfully",
		Data:    connection,
	})
}

// GetAgencyHRConnections returns agency HR connections
func GetAgencyHRConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is agency and target is hr
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "agency", id, "hr").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch agency HR connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency HR connections retrieved successfully",
		Data:    connections,
	})
}

// CreateAgencyHRConnection creates an agency HR connection
func CreateAgencyHRConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is agency and target is hr
	if req.SourceType != "agency" || req.TargetType != "hr" {
		HandleBadRequest(c, "Invalid connection type for agency-HR connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create agency HR connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Agency HR connection created successfully",
		Data:    connection,
	})
}

// GetAgencyBIConnections returns agency BI connections
func GetAgencyBIConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is agency and target is bi
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "agency", id, "bi").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch agency BI connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Agency BI connections retrieved successfully",
		Data:    connections,
	})
}

// CreateAgencyBIConnection creates an agency BI connection
func CreateAgencyBIConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is agency and target is bi
	if req.SourceType != "agency" || req.TargetType != "bi" {
		HandleBadRequest(c, "Invalid connection type for agency-BI connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create agency BI connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Agency BI connection created successfully",
		Data:    connection,
	})
}

// GetCategoryFinanceConnections returns category finance connections
func GetCategoryFinanceConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is category and target is finance
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "category", id, "finance").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch category finance connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category finance connections retrieved successfully",
		Data:    connections,
	})
}

// CreateCategoryFinanceConnection creates a category finance connection
func CreateCategoryFinanceConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is category and target is finance
	if req.SourceType != "category" || req.TargetType != "finance" {
		HandleBadRequest(c, "Invalid connection type for category-finance connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create category finance connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Category finance connection created successfully",
		Data:    connection,
	})
}

// GetCategoryTaskConnections returns category task connections
func GetCategoryTaskConnections(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get interconnections where source is category and target is task
	var connections []models.Interconnect
	if err := db.Where("source_type = ? AND source_id = ? AND target_type = ?", "category", id, "task").Find(&connections).Error; err != nil {
		HandleInternalError(c, "Failed to fetch category task connections: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Category task connections retrieved successfully",
		Data:    connections,
	})
}

// CreateCategoryTaskConnection creates a category task connection
func CreateCategoryTaskConnection(c *gin.Context) {
	var req ConnectionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Validate that source is category and target is task
	if req.SourceType != "category" || req.TargetType != "task" {
		HandleBadRequest(c, "Invalid connection type for category-task connection")
		return
	}

	// Create interconnection
	connection := models.Interconnect{
		SourceType:   req.SourceType,
		SourceID:     req.SourceID,
		TargetType:   req.TargetType,
		TargetID:     req.TargetID,
		Relationship: req.Relationship,
		Description:  req.Description,
		IsActive:     req.IsActive,
		Metadata:     req.Metadata,
	}

	if err := db.Create(&connection).Error; err != nil {
		HandleInternalError(c, "Failed to create category task connection: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Category task connection created successfully",
		Data:    connection,
	})
}

// SummaryRequest represents a request to create or update a summary
type SummaryRequest struct {
	Title           string `json:"title" binding:"required"`
	Content         string `json:"content" binding:"required"`
	Abstract        string `json:"abstract"`
	SummaryType     string `json:"summary_type" binding:"required"`
	EntityType      string `json:"entity_type" binding:"required"`
	EntityID        uint   `json:"entity_id" binding:"required"`
	ActionType      string `json:"action_type" binding:"required"`
	PublicationDate string `json:"publication_date"`
	IsPublic        bool   `json:"is_public"`
	IsFeatured      bool   `json:"is_featured"`
	Priority        int    `json:"priority"`
	Tags            string `json:"tags"`
	ExternalLink    string `json:"external_link"`
	AgencyID        *uint  `json:"agency_id"`
	CategoryID      *uint  `json:"category_id"`
}

// GetSummaries returns all summaries
func GetSummaries(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query
	query := db.Model(&models.Summary{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR content ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Type != "" {
		query = query.Where("summary_type = ?", search.Type)
	}
	if search.Status != "" {
		query = query.Where("is_public = ?", search.Status == "public")
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get summaries with relationships
	var summaries []models.Summary
	if err := query.Preload("CreatedBy").Preload("Agency").Preload("Category").Find(&summaries).Error; err != nil {
		HandleInternalError(c, "Failed to fetch summaries: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       summaries,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateSummary creates a new summary
func CreateSummary(c *gin.Context) {
	var req SummaryRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Create summary
	summary := models.Summary{
		Title:        req.Title,
		Content:      req.Content,
		Abstract:     req.Abstract,
		SummaryType:  models.SummaryType(req.SummaryType),
		EntityType:   models.EntityType(req.EntityType),
		EntityID:     req.EntityID,
		ActionType:   models.ActionType(req.ActionType),
		IsPublic:     req.IsPublic,
		IsFeatured:   req.IsFeatured,
		Priority:     req.Priority,
		Tags:         req.Tags,
		ExternalLink: req.ExternalLink,
		CreatedByID:  userID.(uint),
		AgencyID:     req.AgencyID,
		CategoryID:   req.CategoryID,
	}

	// Parse publication date if provided
	if req.PublicationDate != "" {
		if pubDate, err := time.Parse("2006-01-02", req.PublicationDate); err == nil {
			summary.PublicationDate = &pubDate
		}
	}

	if err := db.Create(&summary).Error; err != nil {
		HandleInternalError(c, "Failed to create summary: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("Agency").Preload("Category").First(&summary, summary.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Summary created successfully",
		Data:    summary,
	})
}

// GetSummary returns a specific summary
func GetSummary(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get summary with relationships
	var summary models.Summary
	if err := db.Preload("CreatedBy").Preload("Agency").Preload("Category").First(&summary, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Summary")
			return
		}
		HandleInternalError(c, "Failed to fetch summary: "+err.Error())
		return
	}

	// Increment view count
	db.Model(&summary).Update("view_count", summary.ViewCount+1)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Summary retrieved successfully",
		Data:    summary,
	})
}

// UpdateSummaryRequest represents a partial update request for summaries
type UpdateSummaryRequest struct {
	Title           *string `json:"title"`
	Content         *string `json:"content"`
	Abstract        *string `json:"abstract"`
	SummaryType     *string `json:"summary_type"`
	EntityType      *string `json:"entity_type"`
	EntityID        *uint   `json:"entity_id"`
	ActionType      *string `json:"action_type"`
	PublicationDate *string `json:"publication_date"`
	IsPublic        *bool   `json:"is_public"`
	IsFeatured      *bool   `json:"is_featured"`
	Priority        *int    `json:"priority"`
	Tags            *string `json:"tags"`
	ExternalLink    *string `json:"external_link"`
	AgencyID        *uint   `json:"agency_id"`
	CategoryID      *uint   `json:"category_id"`
}

// UpdateSummary updates a summary
func UpdateSummary(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req UpdateSummaryRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing summary
	var summary models.Summary
	if err := db.First(&summary, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Summary")
			return
		}
		HandleInternalError(c, "Failed to fetch summary: "+err.Error())
		return
	}

	// Update summary fields only if provided
	if req.Title != nil {
		if strings.TrimSpace(*req.Title) == "" {
			HandleBadRequest(c, "Title cannot be empty")
			return
		}
		summary.Title = strings.TrimSpace(*req.Title)
	}
	if req.Content != nil {
		if strings.TrimSpace(*req.Content) == "" {
			HandleBadRequest(c, "Content cannot be empty")
			return
		}
		summary.Content = strings.TrimSpace(*req.Content)
	}
	if req.Abstract != nil {
		summary.Abstract = *req.Abstract
	}
	if req.SummaryType != nil {
		summary.SummaryType = models.SummaryType(*req.SummaryType)
	}
	if req.EntityType != nil {
		summary.EntityType = models.EntityType(*req.EntityType)
	}
	if req.EntityID != nil {
		summary.EntityID = *req.EntityID
	}
	if req.ActionType != nil {
		summary.ActionType = models.ActionType(*req.ActionType)
	}
	if req.IsPublic != nil {
		summary.IsPublic = *req.IsPublic
	}
	if req.IsFeatured != nil {
		summary.IsFeatured = *req.IsFeatured
	}
	if req.Priority != nil {
		summary.Priority = *req.Priority
	}
	if req.Tags != nil {
		summary.Tags = *req.Tags
	}
	if req.ExternalLink != nil {
		summary.ExternalLink = *req.ExternalLink
	}
	if req.AgencyID != nil {
		summary.AgencyID = req.AgencyID
	}
	if req.CategoryID != nil {
		summary.CategoryID = req.CategoryID
	}

	// Parse publication date if provided
	if req.PublicationDate != nil && *req.PublicationDate != "" {
		if pubDate, err := time.Parse("2006-01-02", *req.PublicationDate); err == nil {
			summary.PublicationDate = &pubDate
		}
	}

	if err := db.Save(&summary).Error; err != nil {
		HandleInternalError(c, "Failed to update summary: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("Agency").Preload("Category").First(&summary, summary.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Summary updated successfully",
		Data:    summary,
	})
}

// DeleteSummary deletes a summary
func DeleteSummary(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if summary exists
	var summary models.Summary
	if err := db.First(&summary, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Summary")
			return
		}
		HandleInternalError(c, "Failed to fetch summary: "+err.Error())
		return
	}

	// Delete summary
	if err := db.Delete(&summary).Error; err != nil {
		HandleInternalError(c, "Failed to delete summary: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Summary deleted successfully",
	})
}

// ContentRepositoryRequest represents a request to create or update a content repository
type ContentRepositoryRequest struct {
	Name              string `json:"name" binding:"required"`
	Description       string `json:"description"`
	Type              string `json:"type"`
	StorageType       string `json:"storage_type"`
	StoragePath       string `json:"storage_path"`
	StorageConfig     string `json:"storage_config"`
	MaxSize           uint64 `json:"max_size"`
	MaxFiles          uint64 `json:"max_files"`
	Classification    string `json:"classification"`
	EncryptionKey     string `json:"encryption_key"`
	AccessPolicy      string `json:"access_policy"`
	VersioningEnabled bool   `json:"versioning_enabled"`
	MaxVersions       int    `json:"max_versions"`
	BackupEnabled     bool   `json:"backup_enabled"`
	BackupSchedule    string `json:"backup_schedule"`
	RetentionPolicyID *uint  `json:"retention_policy_id"`
	ComplianceLevel   string `json:"compliance_level"`
	IsActive          bool   `json:"is_active"`
	Metadata          string `json:"metadata"`
}

// GetContentRepositories returns all content repositories
func GetContentRepositories(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query
	query := db.Model(&models.ContentRepository{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Type != "" {
		query = query.Where("type = ?", search.Type)
	}
	if search.Status != "" {
		query = query.Where("is_active = ?", search.Status == "active")
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get repositories with relationships
	var repositories []models.ContentRepository
	if err := query.Preload("CreatedBy").Preload("RetentionPolicy").Find(&repositories).Error; err != nil {
		HandleInternalError(c, "Failed to fetch content repositories: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       repositories,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateContentRepository creates a new content repository
func CreateContentRepository(c *gin.Context) {
	var req ContentRepositoryRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Note: ContentRepository doesn't have CreatedByID field in the model

	// Create content repository
	repository := models.ContentRepository{
		Name:              req.Name,
		Description:       req.Description,
		Type:              req.Type,
		StorageType:       req.StorageType,
		StoragePath:       req.StoragePath,
		StorageConfig:     req.StorageConfig,
		MaxSize:           req.MaxSize,
		MaxFiles:          req.MaxFiles,
		Classification:    models.ContentClassification(req.Classification),
		EncryptionKey:     req.EncryptionKey,
		AccessPolicy:      req.AccessPolicy,
		VersioningEnabled: req.VersioningEnabled,
		MaxVersions:       req.MaxVersions,
		BackupEnabled:     req.BackupEnabled,
		BackupSchedule:    req.BackupSchedule,
		RetentionPolicyID: req.RetentionPolicyID,
		ComplianceLevel:   req.ComplianceLevel,
		IsActive:          req.IsActive,
		Metadata:          req.Metadata,
	}

	if err := db.Create(&repository).Error; err != nil {
		HandleInternalError(c, "Failed to create content repository: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("RetentionPolicy").First(&repository, repository.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Content repository created successfully",
		Data:    repository,
	})
}

// GetContentRepository returns a specific content repository
func GetContentRepository(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get repository with relationships
	var repository models.ContentRepository
	if err := db.Preload("CreatedBy").Preload("RetentionPolicy").First(&repository, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Content repository")
			return
		}
		HandleInternalError(c, "Failed to fetch content repository: "+err.Error())
		return
	}

	// Increment access count
	db.Model(&repository).Update("access_count", repository.AccessCount+1)
	repository.LastAccessed = &time.Time{}
	*repository.LastAccessed = time.Now()
	db.Model(&repository).Update("last_accessed", repository.LastAccessed)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Content repository retrieved successfully",
		Data:    repository,
	})
}

// UpdateContentRepository updates a content repository
func UpdateContentRepository(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req ContentRepositoryRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing repository
	var repository models.ContentRepository
	if err := db.First(&repository, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Content repository")
			return
		}
		HandleInternalError(c, "Failed to fetch content repository: "+err.Error())
		return
	}

	// Update repository fields
	repository.Name = req.Name
	repository.Description = req.Description
	repository.Type = req.Type
	repository.StorageType = req.StorageType
	repository.StoragePath = req.StoragePath
	repository.StorageConfig = req.StorageConfig
	repository.MaxSize = req.MaxSize
	repository.MaxFiles = req.MaxFiles
	repository.Classification = models.ContentClassification(req.Classification)
	repository.EncryptionKey = req.EncryptionKey
	repository.AccessPolicy = req.AccessPolicy
	repository.VersioningEnabled = req.VersioningEnabled
	repository.MaxVersions = req.MaxVersions
	repository.BackupEnabled = req.BackupEnabled
	repository.BackupSchedule = req.BackupSchedule
	repository.RetentionPolicyID = req.RetentionPolicyID
	repository.ComplianceLevel = req.ComplianceLevel
	repository.IsActive = req.IsActive
	repository.Metadata = req.Metadata

	if err := db.Save(&repository).Error; err != nil {
		HandleInternalError(c, "Failed to update content repository: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("RetentionPolicy").First(&repository, repository.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Content repository updated successfully",
		Data:    repository,
	})
}

// ContentVersionRequest represents a request to create a content version
type ContentVersionRequest struct {
	DocumentID     uint   `json:"document_id" binding:"required"`
	VersionNumber  string `json:"version_number" binding:"required"`
	Title          string `json:"title"`
	Content        string `json:"content"`
	ContentHash    string `json:"content_hash"`
	FileSize       uint64 `json:"file_size"`
	MimeType       string `json:"mime_type"`
	ChangeType     string `json:"change_type"`
	ChangeLog      string `json:"change_log"`
	ApprovalStatus string `json:"approval_status"`
}

// CreateContentVersion creates a new content version
func CreateContentVersion(c *gin.Context) {
	var req ContentVersionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Verify document exists
	var document models.Document
	if err := db.First(&document, req.DocumentID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Document")
			return
		}
		HandleInternalError(c, "Failed to fetch document: "+err.Error())
		return
	}

	// Create content version
	version := models.ContentVersion{
		DocumentID:     req.DocumentID,
		VersionNumber:  req.VersionNumber,
		Title:          req.Title,
		Content:        req.Content,
		ContentHash:    req.ContentHash,
		FileSize:       req.FileSize,
		MimeType:       req.MimeType,
		ChangeType:     req.ChangeType,
		ChangeLog:      req.ChangeLog,
		ApprovalStatus: req.ApprovalStatus,
		AuthorID:       userID.(uint),
	}

	if err := db.Create(&version).Error; err != nil {
		HandleInternalError(c, "Failed to create content version: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("Repository").First(&version, version.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Content version created successfully",
		Data:    version,
	})
}

// GetContentVersions returns content versions
func GetContentVersions(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query for content versions
	query := db.Model(&models.ContentVersion{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get content versions with relationships
	var versions []models.ContentVersion
	if err := query.Preload("CreatedBy").Preload("ApprovedBy").Find(&versions).Error; err != nil {
		HandleInternalError(c, "Failed to fetch content versions: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       versions,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// ApproveContentVersionRequest represents a request to approve content version
type ApproveContentVersionRequest struct {
	VersionID uint   `json:"version_id" binding:"required"`
	Comments  string `json:"comments"`
	Approved  bool   `json:"approved"`
}

// ApproveContentVersion approves a content version
func ApproveContentVersion(c *gin.Context) {
	var req ApproveContentVersionRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get content version
	var version models.ContentVersion
	if err := db.First(&version, req.VersionID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Content version")
			return
		}
		HandleInternalError(c, "Failed to fetch content version: "+err.Error())
		return
	}

	// Update approval status
	if req.Approved {
		version.ApprovalStatus = "approved"
		version.ApprovedAt = &time.Time{}
		*version.ApprovedAt = time.Now()
		userIDUint := userID.(uint)
		version.ApprovedByID = &userIDUint
	} else {
		version.ApprovalStatus = "rejected"
	}
	version.RejectionReason = req.Comments

	if err := db.Save(&version).Error; err != nil {
		HandleInternalError(c, "Failed to update content version: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("ApprovedBy").First(&version, version.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Content version approval updated successfully",
		Data:    version,
	})
}

// GetContentWorkflows returns content workflows
func GetContentWorkflows(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query for workflows (using a simplified approach)
	workflows := []gin.H{
		{
			"id":          1,
			"name":        "Document Review Workflow",
			"description": "Standard workflow for document review and approval",
			"status":      "active",
			"steps": []gin.H{
				{"order": 1, "name": "Initial Review", "type": "review"},
				{"order": 2, "name": "Legal Review", "type": "legal_review"},
				{"order": 3, "name": "Final Approval", "type": "approval"},
			},
			"created_at": time.Now().AddDate(0, -1, 0),
		},
		{
			"id":          2,
			"name":        "Regulation Publishing Workflow",
			"description": "Workflow for publishing regulations",
			"status":      "active",
			"steps": []gin.H{
				{"order": 1, "name": "Draft Review", "type": "review"},
				{"order": 2, "name": "Public Comment", "type": "public_comment"},
				{"order": 3, "name": "Final Review", "type": "review"},
				{"order": 4, "name": "Publication", "type": "publish"},
			},
			"created_at": time.Now().AddDate(0, -2, 0),
		},
	}

	// Apply basic filtering
	filteredWorkflows := workflows
	if search.Query != "" {
		filteredWorkflows = []gin.H{}
		for _, workflow := range workflows {
			if strings.Contains(strings.ToLower(workflow["name"].(string)), strings.ToLower(search.Query)) ||
				strings.Contains(strings.ToLower(workflow["description"].(string)), strings.ToLower(search.Query)) {
				filteredWorkflows = append(filteredWorkflows, workflow)
			}
		}
	}

	total := int64(len(filteredWorkflows))
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	// Apply pagination
	start := (page.Page - 1) * page.PerPage
	end := start + page.PerPage
	if end > len(filteredWorkflows) {
		end = len(filteredWorkflows)
	}
	if start > len(filteredWorkflows) {
		start = len(filteredWorkflows)
	}

	paginatedWorkflows := filteredWorkflows[start:end]

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       paginatedWorkflows,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateContentWorkflowRequest represents a request to create content workflow
type CreateContentWorkflowRequest struct {
	Name        string  `json:"name" binding:"required"`
	Description string  `json:"description"`
	Steps       []gin.H `json:"steps" binding:"required"`
}

// CreateContentWorkflow creates a new content workflow
func CreateContentWorkflow(c *gin.Context) {
	var req CreateContentWorkflowRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Marshal steps to JSON
	stepsJSON, err := json.Marshal(req.Steps)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid steps format",
			Message: "Failed to marshal steps to JSON",
		})
		return
	}

	// Create proper workflow template
	workflowTemplate := models.WorkflowTemplate{
		Name:                req.Name,
		Description:         req.Description,
		Type:                models.WorkflowTypeSequential,
		Category:            "Document Management",
		StepDefinitions:     string(stepsJSON),
		CreatedByID:         userID.(uint),
		DefaultSLAHours:     48,
		AllowParallelSteps:  false,
		RequireAllApprovals: true,
		EnableEscalation:    true,
		EscalationHours:     24,
	}

	// Save to database
	if err := db.Create(&workflowTemplate).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create workflow template",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Content workflow created successfully",
		Data:    workflowTemplate,
	})
}

// StartContentWorkflowRequest represents a request to start content workflow
type StartContentWorkflowRequest struct {
	WorkflowID  uint   `json:"workflow_id" binding:"required"`
	ContentID   uint   `json:"content_id" binding:"required"`
	ContentType string `json:"content_type" binding:"required"`
}

// StartContentWorkflow starts a content workflow
func StartContentWorkflow(c *gin.Context) {
	var req StartContentWorkflowRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Get the workflow template
	var workflowTemplate models.WorkflowTemplate
	if err := db.First(&workflowTemplate, req.WorkflowID).Error; err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid workflow ID",
			Message: "Workflow template not found",
		})
		return
	}

	// Parse step definitions to determine total steps
	var stepDefinitions []map[string]interface{}
	if err := json.Unmarshal([]byte(workflowTemplate.StepDefinitions), &stepDefinitions); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Invalid workflow template",
			Message: "Failed to parse step definitions",
		})
		return
	}

	now := time.Now()

	// Create workflow instance
	workflowInstance := models.WorkflowInstance{
		InstanceID:      fmt.Sprintf("WF-%d-%d", req.WorkflowID, now.Unix()),
		Name:            workflowTemplate.Name,
		Status:          models.WorkflowStatusInProgress,
		TemplateID:      req.WorkflowID,
		EntityType:      req.ContentType,
		EntityID:        req.ContentID,
		TotalSteps:      len(stepDefinitions),
		CompletedSteps:  0,
		ProgressPercent: 0,
		StartedAt:       &now,
		InitiatedByID:   userID.(uint),
	}

	// Save workflow instance to database
	if err := db.Create(&workflowInstance).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to create workflow instance",
			Message: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Content workflow started successfully",
		Data:    workflowInstance,
	})
}

// GetWorkflowInstances returns workflow instances
func GetWorkflowInstances(c *gin.Context) {
	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	// Query workflow instances from database
	var instances []models.WorkflowInstance
	query := db.Model(&models.WorkflowInstance{}).Preload("Template").Preload("Steps")

	// Apply search filters if provided
	if search.Query != "" {
		query = query.Where("name LIKE ? OR instance_id LIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%")
	}

	// Apply status filter if provided
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply entity type filter if provided
	if entityType := c.Query("entity_type"); entityType != "" {
		query = query.Where("entity_type = ?", entityType)
	}

	// Apply entity ID filter if provided
	if entityID := c.Query("entity_id"); entityID != "" {
		query = query.Where("entity_id = ?", entityID)
	}

	// Apply pagination
	var total int64
	query.Count(&total)

	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		direction := "ASC"
		if search.SortOrder == "desc" {
			direction = "DESC"
		}
		query = query.Order(fmt.Sprintf("%s %s", search.SortBy, direction))
	} else {
		query = query.Order("created_at DESC")
	}

	// Execute query
	if err := query.Find(&instances).Error; err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to fetch workflow instances",
			Message: err.Error(),
		})
		return
	}

	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       instances,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateCollaborationRequest represents a request to create collaboration
type CreateCollaborationRequest struct {
	Title        string `json:"title" binding:"required"`
	Description  string `json:"description"`
	ContentID    uint   `json:"content_id" binding:"required"`
	ContentType  string `json:"content_type" binding:"required"`
	Participants []uint `json:"participants" binding:"required"`
}

// CreateCollaboration creates a new collaboration
func CreateCollaboration(c *gin.Context) {
	var req CreateCollaborationRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Create collaboration data (simplified implementation)
	collaboration := gin.H{
		"id":           time.Now().Unix(), // Simple ID generation
		"title":        req.Title,
		"description":  req.Description,
		"content_id":   req.ContentID,
		"content_type": req.ContentType,
		"status":       "active",
		"created_by":   userID.(uint),
		"created_at":   time.Now(),
		"participants": req.Participants,
		"activity_log": []gin.H{
			{
				"action":    "collaboration_created",
				"user_id":   userID.(uint),
				"timestamp": time.Now(),
				"message":   "Collaboration created",
			},
		},
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Collaboration created successfully",
		Data:    collaboration,
	})
}

// GetActiveCollaborations returns active collaborations
func GetActiveCollaborations(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Create sample active collaborations (simplified implementation)
	collaborations := []gin.H{
		{
			"id":            1,
			"title":         "Document Review Collaboration",
			"description":   "Collaborative review of new policy document",
			"content_id":    101,
			"content_type":  "document",
			"status":        "active",
			"created_by":    userID.(uint),
			"created_at":    time.Now().AddDate(0, 0, -3),
			"participants":  []uint{userID.(uint), 2, 3},
			"last_activity": time.Now().AddDate(0, 0, -1),
		},
		{
			"id":            2,
			"title":         "Regulation Amendment Collaboration",
			"description":   "Working together on regulation amendments",
			"content_id":    102,
			"content_type":  "regulation",
			"status":        "active",
			"created_by":    2,
			"created_at":    time.Now().AddDate(0, 0, -7),
			"participants":  []uint{userID.(uint), 2, 4, 5},
			"last_activity": time.Now().AddDate(0, 0, -2),
		},
	}

	// Apply basic filtering
	filteredCollaborations := collaborations
	if search.Query != "" {
		filteredCollaborations = []gin.H{}
		for _, collab := range collaborations {
			if strings.Contains(strings.ToLower(collab["title"].(string)), strings.ToLower(search.Query)) ||
				strings.Contains(strings.ToLower(collab["description"].(string)), strings.ToLower(search.Query)) {
				filteredCollaborations = append(filteredCollaborations, collab)
			}
		}
	}

	total := int64(len(filteredCollaborations))
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	// Apply pagination
	start := (page.Page - 1) * page.PerPage
	end := start + page.PerPage
	if end > len(filteredCollaborations) {
		end = len(filteredCollaborations)
	}
	if start > len(filteredCollaborations) {
		start = len(filteredCollaborations)
	}

	paginatedCollaborations := filteredCollaborations[start:end]

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       paginatedCollaborations,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// ComplianceRequirementRequest represents a request to create or update a compliance requirement
type ComplianceRequirementRequest struct {
	RequirementCode string `json:"requirement_code" binding:"required"`
	Title           string `json:"title" binding:"required"`
	Description     string `json:"description"`
	Framework       string `json:"framework" binding:"required"`
	Category        string `json:"category"`
	Subcategory     string `json:"subcategory"`
	RiskLevel       string `json:"risk_level"`
	ControlType     string `json:"control_type"`
	Implementation  string `json:"implementation"`
	TestingMethod   string `json:"testing_method"`
	TestingFreq     string `json:"testing_frequency"`
	ReviewFreq      string `json:"review_frequency"`
	EffectiveDate   string `json:"effective_date"`
	ExpirationDate  string `json:"expiration_date"`
	Status          string `json:"status"`
	OwnerID         uint   `json:"owner_id"`
	Metadata        string `json:"metadata"`
}

// GetComplianceRequirements returns compliance requirements
func GetComplianceRequirements(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query
	query := db.Model(&models.ComplianceRequirement{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ? OR requirement_code ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Type != "" {
		query = query.Where("framework = ?", search.Type)
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get requirements with relationships
	var requirements []models.ComplianceRequirement
	if err := query.Preload("Owner").Find(&requirements).Error; err != nil {
		HandleInternalError(c, "Failed to fetch compliance requirements: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       requirements,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateComplianceRequirement creates a new compliance requirement
func CreateComplianceRequirement(c *gin.Context) {
	var req ComplianceRequirementRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create compliance requirement
	requirement := models.ComplianceRequirement{
		RequirementCode:  req.RequirementCode,
		Title:            req.Title,
		Description:      req.Description,
		Framework:        models.ComplianceFramework(req.Framework),
		Category:         req.Category,
		Subcategory:      req.Subcategory,
		RiskLevel:        models.RiskLevel(req.RiskLevel),
		ControlType:      req.ControlType,
		Implementation:   req.Implementation,
		TestingMethod:    req.TestingMethod,
		TestingFrequency: req.TestingFreq,
		ReviewFrequency:  req.ReviewFreq,
		Status:           req.Status,
		OwnerID:          &req.OwnerID,
		Metadata:         req.Metadata,
	}

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if effDate, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			requirement.EffectiveDate = effDate
		}
	}
	if req.ExpirationDate != "" {
		if expDate, err := time.Parse("2006-01-02", req.ExpirationDate); err == nil {
			requirement.ExpirationDate = &expDate
		}
	}

	if err := db.Create(&requirement).Error; err != nil {
		HandleInternalError(c, "Failed to create compliance requirement: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Owner").First(&requirement, requirement.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Compliance requirement created successfully",
		Data:    requirement,
	})
}

// GetComplianceRequirement returns a specific compliance requirement
func GetComplianceRequirement(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get requirement with relationships
	var requirement models.ComplianceRequirement
	if err := db.Preload("Owner").First(&requirement, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance requirement")
			return
		}
		HandleInternalError(c, "Failed to fetch compliance requirement: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance requirement retrieved successfully",
		Data:    requirement,
	})
}

// UpdateComplianceRequirement updates a compliance requirement
func UpdateComplianceRequirement(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req ComplianceRequirementRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing requirement
	var requirement models.ComplianceRequirement
	if err := db.First(&requirement, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance requirement")
			return
		}
		HandleInternalError(c, "Failed to fetch compliance requirement: "+err.Error())
		return
	}

	// Update requirement fields
	requirement.RequirementCode = req.RequirementCode
	requirement.Title = req.Title
	requirement.Description = req.Description
	requirement.Framework = models.ComplianceFramework(req.Framework)
	requirement.Category = req.Category
	requirement.Subcategory = req.Subcategory
	requirement.RiskLevel = models.RiskLevel(req.RiskLevel)
	requirement.ControlType = req.ControlType
	requirement.Implementation = req.Implementation
	requirement.TestingMethod = req.TestingMethod
	requirement.TestingFrequency = req.TestingFreq
	requirement.ReviewFrequency = req.ReviewFreq
	requirement.Status = req.Status
	requirement.OwnerID = &req.OwnerID
	requirement.Metadata = req.Metadata

	// Parse dates if provided
	if req.EffectiveDate != "" {
		if effDate, err := time.Parse("2006-01-02", req.EffectiveDate); err == nil {
			requirement.EffectiveDate = effDate
		}
	}
	if req.ExpirationDate != "" {
		if expDate, err := time.Parse("2006-01-02", req.ExpirationDate); err == nil {
			requirement.ExpirationDate = &expDate
		}
	}

	if err := db.Save(&requirement).Error; err != nil {
		HandleInternalError(c, "Failed to update compliance requirement: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("Owner").First(&requirement, requirement.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance requirement updated successfully",
		Data:    requirement,
	})
}

// GetComplianceAssessments returns compliance assessments
func GetComplianceAssessments(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Parse query parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Build query for compliance assessments
	query := db.Model(&models.ComplianceAssessment{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ?", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get assessments with relationships
	var assessments []models.ComplianceAssessment
	if err := query.Preload("CreatedBy").Preload("AssignedTo").Find(&assessments).Error; err != nil {
		HandleInternalError(c, "Failed to fetch compliance assessments: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       assessments,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// ComplianceAssessmentRequest represents a request to create compliance assessment
type ComplianceAssessmentRequest struct {
	Title       string `json:"title" binding:"required"`
	Description string `json:"description"`
	Type        string `json:"type" binding:"required"`
	Scope       string `json:"scope"`
	AssignedTo  uint   `json:"assigned_to"`
	DueDate     string `json:"due_date"`
}

// CreateComplianceAssessment creates a new compliance assessment
func CreateComplianceAssessment(c *gin.Context) {
	var req ComplianceAssessmentRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Create assessment
	assessment := models.ComplianceAssessment{
		Name:           req.Title,
		Description:    req.Description,
		Type:           req.Type,
		Scope:          req.Scope,
		Status:         "planning",
		LeadAssessorID: userID.(uint),
		StartDate:      time.Now(),
		PlannedEndDate: time.Now().AddDate(0, 1, 0), // Default to 1 month
	}

	// Parse due date if provided
	if req.DueDate != "" {
		if date, err := time.Parse("2006-01-02", req.DueDate); err == nil {
			assessment.PlannedEndDate = date
		}
	}

	if err := db.Create(&assessment).Error; err != nil {
		HandleInternalError(c, "Failed to create compliance assessment: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("CreatedBy").Preload("AssignedTo").First(&assessment, assessment.ID)

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Compliance assessment created successfully",
		Data:    assessment,
	})
}

// StartComplianceAssessment starts a compliance assessment
func StartComplianceAssessment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Create simplified response for starting assessment
	result := gin.H{
		"assessment_id": id,
		"status":        "started",
		"started_at":    time.Now(),
		"message":       "Compliance assessment started successfully",
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance assessment started successfully",
		Data:    result,
	})
}

// CompleteComplianceAssessment completes a compliance assessment
func CompleteComplianceAssessment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		OverallStatus      string     `json:"overall_status" binding:"required"`
		ComplianceScore    float64    `json:"compliance_score"`
		PassedControls     int        `json:"passed_controls"`
		FailedControls     int        `json:"failed_controls"`
		TotalControls      int        `json:"total_controls"`
		Summary            string     `json:"summary"`
		Recommendations    string     `json:"recommendations"`
		NextAssessmentDate *time.Time `json:"next_assessment_date"`
		FollowUpRequired   bool       `json:"follow_up_required"`
		FollowUpDate       *time.Time `json:"follow_up_date"`
		FollowUpNotes      string     `json:"follow_up_notes"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing assessment
	var assessment models.ComplianceAssessment
	if err := db.First(&assessment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance assessment")
			return
		}
		HandleInternalError(c, "Failed to fetch compliance assessment: "+err.Error())
		return
	}

	// Check if assessment can be completed
	if assessment.Status == "completed" {
		HandleBadRequest(c, "Assessment is already completed")
		return
	}

	// Update assessment with completion data
	now := time.Now()
	updates := map[string]interface{}{
		"status":               "completed",
		"overall_status":       req.OverallStatus,
		"compliance_score":     req.ComplianceScore,
		"passed_controls":      req.PassedControls,
		"failed_controls":      req.FailedControls,
		"total_controls":       req.TotalControls,
		"summary":              req.Summary,
		"recommendations":      req.Recommendations,
		"end_date":             &now,
		"next_assessment_date": req.NextAssessmentDate,
		"follow_up_required":   req.FollowUpRequired,
		"follow_up_date":       req.FollowUpDate,
		"follow_up_notes":      req.FollowUpNotes,
		"completed_by_id":      userID,
		"completed_at":         &now,
	}

	if err := db.Model(&assessment).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to complete compliance assessment: "+err.Error())
		return
	}

	// Load updated assessment with relationships
	if err := db.Preload("LeadAssessor").Preload("Rule").First(&assessment, assessment.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load completed assessment: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Compliance assessment completed successfully",
		Data:    assessment,
	})
}

// GetComplianceFindings returns compliance findings
func GetComplianceFindings(c *gin.Context) {
	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.ComplianceFinding{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("title ILIKE ? OR description ILIKE ? OR finding_code ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("finding_type = ?", search.Type)
	}

	// Filter by assessment ID if provided
	if assessmentID := c.Query("assessment_id"); assessmentID != "" {
		query = query.Where("assessment_id = ?", assessmentID)
	}

	// Filter by risk level if provided
	if riskLevel := c.Query("risk_level"); riskLevel != "" {
		query = query.Where("risk_level = ?", riskLevel)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get findings with relationships
	var findings []models.ComplianceFinding
	if err := query.Preload("Assessment").Preload("Requirement").Find(&findings).Error; err != nil {
		HandleInternalError(c, "Failed to fetch compliance findings: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       findings,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateComplianceFinding creates a new compliance finding
func CreateComplianceFinding(c *gin.Context) {
	var req struct {
		Title           string     `json:"title" binding:"required"`
		Description     string     `json:"description"`
		AssessmentID    uint       `json:"assessment_id" binding:"required"`
		RequirementID   uint       `json:"requirement_id" binding:"required"`
		FindingType     string     `json:"finding_type"`
		RiskLevel       string     `json:"risk_level" binding:"required"`
		Impact          string     `json:"impact"`
		Likelihood      string     `json:"likelihood"`
		Evidence        string     `json:"evidence"`
		DocumentIDs     string     `json:"document_ids"`
		Recommendation  string     `json:"recommendation"`
		RemediationPlan string     `json:"remediation_plan"`
		TargetDate      *time.Time `json:"target_date"`
		ResponsibleID   *uint      `json:"responsible_id"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	_, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Validate assessment exists
	var assessment models.ComplianceAssessment
	if err := db.First(&assessment, req.AssessmentID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance assessment")
			return
		}
		HandleInternalError(c, "Failed to validate assessment: "+err.Error())
		return
	}

	// Validate requirement exists
	var requirement models.ComplianceRequirement
	if err := db.First(&requirement, req.RequirementID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance requirement")
			return
		}
		HandleInternalError(c, "Failed to validate requirement: "+err.Error())
		return
	}

	// Validate responsible user if provided
	if req.ResponsibleID != nil {
		var user models.User
		if err := db.First(&user, *req.ResponsibleID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Responsible user")
				return
			}
			HandleInternalError(c, "Failed to validate responsible user: "+err.Error())
			return
		}
	}

	// Generate finding code
	findingCode := fmt.Sprintf("CF-%d-%d", req.AssessmentID, time.Now().Unix())

	// Set default values
	if req.FindingType == "" {
		req.FindingType = "deficiency"
	}
	if req.Likelihood == "" {
		req.Likelihood = "medium"
	}

	// Create finding
	finding := models.ComplianceFinding{
		FindingCode:     findingCode,
		Title:           req.Title,
		Description:     req.Description,
		AssessmentID:    req.AssessmentID,
		RequirementID:   req.RequirementID,
		FindingType:     req.FindingType,
		RiskLevel:       models.RiskLevel(req.RiskLevel),
		Impact:          req.Impact,
		Likelihood:      req.Likelihood,
		Evidence:        req.Evidence,
		DocumentIDs:     req.DocumentIDs,
		Recommendation:  req.Recommendation,
		RemediationPlan: req.RemediationPlan,
		TargetDate:      req.TargetDate,
		ResponsibleID:   req.ResponsibleID,
		Status:          "open",
	}

	if err := db.Create(&finding).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Finding with this code already exists")
			return
		}
		HandleInternalError(c, "Failed to create compliance finding: "+err.Error())
		return
	}

	// Load the created finding with relationships
	if err := db.Preload("Assessment").Preload("Requirement").Preload("Responsible").First(&finding, finding.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created finding: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Compliance finding created successfully",
		Data:    finding,
	})
}

// UpdateFindingStatus updates a finding status
func UpdateFindingStatus(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		Status          string `json:"status" binding:"required"`
		ResolutionNotes string `json:"resolution_notes"`
		VerifiedByID    *uint  `json:"verified_by_id"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context (for authentication check)
	_, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing finding
	var finding models.ComplianceFinding
	if err := db.First(&finding, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Compliance finding")
			return
		}
		HandleInternalError(c, "Failed to fetch compliance finding: "+err.Error())
		return
	}

	// Validate verified user if provided
	if req.VerifiedByID != nil {
		var user models.User
		if err := db.First(&user, *req.VerifiedByID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Verified by user")
				return
			}
			HandleInternalError(c, "Failed to validate verified by user: "+err.Error())
			return
		}
	}

	// Prepare updates
	updates := map[string]interface{}{
		"status": req.Status,
	}

	// Add resolution information if status is resolved or closed
	if req.Status == "resolved" || req.Status == "closed" {
		now := time.Now()
		updates["resolution_date"] = &now
		if req.ResolutionNotes != "" {
			updates["resolution_notes"] = req.ResolutionNotes
		}
	}

	// Add verification information if provided
	if req.VerifiedByID != nil {
		now := time.Now()
		updates["verified_by_id"] = req.VerifiedByID
		updates["verified_at"] = &now
	}

	// Update finding
	if err := db.Model(&finding).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to update finding status: "+err.Error())
		return
	}

	// Load updated finding with relationships
	if err := db.Preload("Assessment").Preload("Requirement").Preload("Responsible").Preload("VerifiedBy").First(&finding, finding.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load updated finding: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Finding status updated successfully",
		Data:    finding,
	})
}

// GetRiskAssessments returns risk assessments
func GetRiskAssessments(c *gin.Context) {
	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.RiskAssessment{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("risk_title ILIKE ? OR risk_description ILIKE ? OR risk_code ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("risk_category = ?", search.Type)
	}

	// Filter by risk level if provided
	if riskLevel := c.Query("risk_level"); riskLevel != "" {
		query = query.Where("inherent_risk = ? OR residual_risk = ?", riskLevel, riskLevel)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("risk_score DESC, created_at DESC")
	}

	// Get risk assessments with relationships
	var riskAssessments []models.RiskAssessment
	if err := query.Preload("RiskOwner").Find(&riskAssessments).Error; err != nil {
		HandleInternalError(c, "Failed to fetch risk assessments: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       riskAssessments,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreateRiskAssessment creates a new risk assessment
func CreateRiskAssessment(c *gin.Context) {
	var req struct {
		RiskTitle         string `json:"risk_title" binding:"required"`
		RiskDescription   string `json:"risk_description"`
		RiskCategory      string `json:"risk_category"`
		InherentRisk      string `json:"inherent_risk" binding:"required"`
		ResidualRisk      string `json:"residual_risk" binding:"required"`
		RiskAppetite      string `json:"risk_appetite"`
		ImpactScore       int    `json:"impact_score"`
		LikelihoodScore   int    `json:"likelihood_score"`
		RiskOwnerID       uint   `json:"risk_owner_id" binding:"required"`
		TreatmentStrategy string `json:"treatment_strategy"`
		MitigationPlan    string `json:"mitigation_plan"`
		ReviewFrequency   string `json:"review_frequency"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context (for authentication check)
	_, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Validate risk owner exists
	var riskOwner models.User
	if err := db.First(&riskOwner, req.RiskOwnerID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Risk owner")
			return
		}
		HandleInternalError(c, "Failed to validate risk owner: "+err.Error())
		return
	}

	// Generate risk code
	riskCode := fmt.Sprintf("RA-%d", time.Now().Unix())

	// Set default values
	if req.RiskAppetite == "" {
		req.RiskAppetite = "medium"
	}
	if req.ReviewFrequency == "" {
		req.ReviewFrequency = "quarterly"
	}
	if req.ImpactScore == 0 {
		req.ImpactScore = 1
	}
	if req.LikelihoodScore == 0 {
		req.LikelihoodScore = 1
	}

	// Calculate risk score (simple formula: impact * likelihood)
	riskScore := float64(req.ImpactScore * req.LikelihoodScore)

	// Create risk assessment
	riskAssessment := models.RiskAssessment{
		RiskCode:          riskCode,
		RiskTitle:         req.RiskTitle,
		RiskDescription:   req.RiskDescription,
		RiskCategory:      req.RiskCategory,
		InherentRisk:      models.RiskLevel(req.InherentRisk),
		ResidualRisk:      models.RiskLevel(req.ResidualRisk),
		RiskAppetite:      models.RiskLevel(req.RiskAppetite),
		ImpactScore:       req.ImpactScore,
		LikelihoodScore:   req.LikelihoodScore,
		RiskScore:         riskScore,
		RiskOwnerID:       req.RiskOwnerID,
		TreatmentStrategy: req.TreatmentStrategy,
		MitigationPlan:    req.MitigationPlan,
		ReviewFrequency:   req.ReviewFrequency,
		Status:            "active",
	}

	if err := db.Create(&riskAssessment).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Risk assessment with this code already exists")
			return
		}
		HandleInternalError(c, "Failed to create risk assessment: "+err.Error())
		return
	}

	// Load the created risk assessment with relationships
	if err := db.Preload("RiskOwner").First(&riskAssessment, riskAssessment.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created risk assessment: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Risk assessment created successfully",
		Data:    riskAssessment,
	})
}

// GetRiskDashboard returns risk dashboard data
func GetRiskDashboard(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get risk assessment statistics
	var totalRisks, highRisks, mediumRisks, lowRisks int64
	db.Model(&models.RiskAssessment{}).Count(&totalRisks)
	db.Model(&models.RiskAssessment{}).Where("inherent_risk = ?", "high").Count(&highRisks)
	db.Model(&models.RiskAssessment{}).Where("inherent_risk = ?", "medium").Count(&mediumRisks)
	db.Model(&models.RiskAssessment{}).Where("inherent_risk = ?", "low").Count(&lowRisks)

	// Get risk assessments by category
	var categoryStats []struct {
		Category string `json:"category"`
		Count    int64  `json:"count"`
	}
	db.Model(&models.RiskAssessment{}).
		Select("risk_category as category, count(*) as count").
		Group("risk_category").
		Scan(&categoryStats)

	// Get recent risk assessments
	var recentRisks []models.RiskAssessment
	db.Preload("RiskOwner").
		Order("created_at DESC").
		Limit(10).
		Find(&recentRisks)

	// Get overdue risk reviews
	var overdueReviews []models.RiskAssessment
	db.Preload("RiskOwner").
		Where("next_review_date < ?", time.Now()).
		Order("next_review_date ASC").
		Limit(10).
		Find(&overdueReviews)

	// Calculate average risk score
	var avgRiskScore float64
	db.Model(&models.RiskAssessment{}).
		Select("AVG(risk_score)").
		Scan(&avgRiskScore)

	// Get compliance findings statistics
	var totalFindings, openFindings, criticalFindings int64
	db.Model(&models.ComplianceFinding{}).Count(&totalFindings)
	db.Model(&models.ComplianceFinding{}).Where("status = ?", "open").Count(&openFindings)
	db.Model(&models.ComplianceFinding{}).Where("risk_level = ?", "critical").Count(&criticalFindings)

	// Prepare dashboard data
	dashboardData := gin.H{
		"risk_statistics": gin.H{
			"total_risks":   totalRisks,
			"high_risks":    highRisks,
			"medium_risks":  mediumRisks,
			"low_risks":     lowRisks,
			"average_score": avgRiskScore,
		},
		"category_breakdown": categoryStats,
		"recent_risks":       recentRisks,
		"overdue_reviews":    overdueReviews,
		"compliance_summary": gin.H{
			"total_findings":    totalFindings,
			"open_findings":     openFindings,
			"critical_findings": criticalFindings,
		},
		"generated_at": time.Now(),
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Risk dashboard data retrieved successfully",
		Data:    dashboardData,
	})
}

// GetPolicies returns all policies
func GetPolicies(c *gin.Context) {
	// Parse pagination parameters
	page := GetPaginationParams(c)
	search := GetSearchParams(c)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Build query
	query := db.Model(&models.PolicyManagement{})

	// Apply filters
	if search.Query != "" {
		query = query.Where("policy_title ILIKE ? OR description ILIKE ? OR policy_code ILIKE ?",
			"%"+search.Query+"%", "%"+search.Query+"%", "%"+search.Query+"%")
	}
	if search.Status != "" {
		query = query.Where("status = ?", search.Status)
	}
	if search.Type != "" {
		query = query.Where("policy_type = ?", search.Type)
	}

	// Filter by framework if provided
	if framework := c.Query("framework"); framework != "" {
		query = query.Where("framework = ?", framework)
	}

	// Get total count
	var total int64
	query.Count(&total)

	// Apply pagination and sorting
	offset := (page.Page - 1) * page.PerPage
	query = query.Offset(offset).Limit(page.PerPage)

	// Apply sorting
	if search.SortBy != "" {
		query = query.Order(search.SortBy + " " + search.SortOrder)
	} else {
		query = query.Order("created_at DESC")
	}

	// Get policies with relationships
	var policies []models.PolicyManagement
	if err := query.Preload("Owner").Preload("Approver").Preload("ParentPolicy").Find(&policies).Error; err != nil {
		HandleInternalError(c, "Failed to fetch policies: "+err.Error())
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(page.PerPage) - 1) / int64(page.PerPage))
	hasNext := page.Page < totalPages
	hasPrev := page.Page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       policies,
		Total:      total,
		Page:       page.Page,
		PerPage:    page.PerPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// CreatePolicy creates a new policy
func CreatePolicy(c *gin.Context) {
	var req struct {
		PolicyTitle            string     `json:"policy_title" binding:"required"`
		PolicyType             string     `json:"policy_type"`
		Description            string     `json:"description"`
		Content                string     `json:"content"`
		Version                string     `json:"version"`
		ParentPolicyID         *uint      `json:"parent_policy_id"`
		Framework              string     `json:"framework"`
		RequirementIDs         string     `json:"requirement_ids"`
		OwnerID                uint       `json:"owner_id" binding:"required"`
		EffectiveDate          *time.Time `json:"effective_date"`
		ExpirationDate         *time.Time `json:"expiration_date"`
		ReviewDate             *time.Time `json:"review_date"`
		ReviewFrequency        string     `json:"review_frequency"`
		RequiresTraining       bool       `json:"requires_training"`
		RequiresAcknowledgment bool       `json:"requires_acknowledgment"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context (for authentication check)
	_, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Validate owner exists
	var owner models.User
	if err := db.First(&owner, req.OwnerID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Policy owner")
			return
		}
		HandleInternalError(c, "Failed to validate policy owner: "+err.Error())
		return
	}

	// Validate parent policy if provided
	if req.ParentPolicyID != nil {
		var parentPolicy models.PolicyManagement
		if err := db.First(&parentPolicy, *req.ParentPolicyID).Error; err != nil {
			if err.Error() == "record not found" {
				HandleNotFound(c, "Parent policy")
				return
			}
			HandleInternalError(c, "Failed to validate parent policy: "+err.Error())
			return
		}
	}

	// Generate policy code
	policyCode := fmt.Sprintf("POL-%d", time.Now().Unix())

	// Set default values
	if req.PolicyType == "" {
		req.PolicyType = "policy"
	}
	if req.Version == "" {
		req.Version = "1.0"
	}
	if req.ReviewFrequency == "" {
		req.ReviewFrequency = "annual"
	}

	// Set default dates if not provided
	effectiveDate := time.Now()
	if req.EffectiveDate != nil {
		effectiveDate = *req.EffectiveDate
	}

	reviewDate := time.Now().AddDate(1, 0, 0) // Default to 1 year from now
	if req.ReviewDate != nil {
		reviewDate = *req.ReviewDate
	}

	// Create policy
	policy := models.PolicyManagement{
		PolicyCode:       policyCode,
		PolicyTitle:      req.PolicyTitle,
		PolicyType:       req.PolicyType,
		Description:      req.Description,
		Content:          req.Content,
		Version:          req.Version,
		ParentPolicyID:   req.ParentPolicyID,
		Framework:        models.ComplianceFramework(req.Framework),
		RequirementIDs:   req.RequirementIDs,
		OwnerID:          req.OwnerID,
		EffectiveDate:    effectiveDate,
		ExpirationDate:   req.ExpirationDate,
		ReviewDate:       reviewDate,
		ReviewFrequency:  req.ReviewFrequency,
		Status:           "draft",
		RequiresTraining: req.RequiresTraining,
		RequiresAck:      req.RequiresAcknowledgment,
	}

	if err := db.Create(&policy).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Policy with this code already exists")
			return
		}
		HandleInternalError(c, "Failed to create policy: "+err.Error())
		return
	}

	// Load the created policy with relationships
	if err := db.Preload("Owner").Preload("ParentPolicy").First(&policy, policy.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created policy: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Policy created successfully",
		Data:    policy,
	})
}

// ApprovePolicy approves a policy
func ApprovePolicy(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		ApprovalNotes string `json:"approval_notes"`
		Publish       bool   `json:"publish"` // Whether to publish immediately after approval
	}

	// Bind JSON (optional parameters)
	c.ShouldBindJSON(&req)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing policy
	var policy models.PolicyManagement
	if err := db.First(&policy, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Policy")
			return
		}
		HandleInternalError(c, "Failed to fetch policy: "+err.Error())
		return
	}

	// Check if policy can be approved
	if policy.Status == "approved" || policy.Status == "published" {
		HandleBadRequest(c, "Policy is already approved")
		return
	}

	// Update policy with approval data
	now := time.Now()
	updates := map[string]interface{}{
		"status":      "approved",
		"approver_id": userID,
		"approved_at": &now,
	}

	// If publish flag is set, also publish the policy
	if req.Publish {
		updates["status"] = "published"
		updates["is_published"] = true
		updates["published_at"] = &now
	}

	if err := db.Model(&policy).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to approve policy: "+err.Error())
		return
	}

	// Load updated policy with relationships
	if err := db.Preload("Owner").Preload("Approver").Preload("ParentPolicy").First(&policy, policy.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load approved policy: "+err.Error())
		return
	}

	message := "Policy approved successfully"
	if req.Publish {
		message = "Policy approved and published successfully"
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: message,
		Data:    policy,
	})
}

// SubmitPerformanceReview submits a performance review
func SubmitPerformanceReview(c *gin.Context) {
	var req struct {
		EmployeeID           uint    `json:"employee_id" binding:"required"`
		ReviewPeriod         string  `json:"review_period" binding:"required"`
		StartDate            string  `json:"start_date" binding:"required"`
		EndDate              string  `json:"end_date" binding:"required"`
		ReviewDate           string  `json:"review_date" binding:"required"`
		ReviewType           string  `json:"review_type"`
		ReviewCycle          string  `json:"review_cycle"`
		OverallRating        float64 `json:"overall_rating"`
		GoalAchievement      float64 `json:"goal_achievement"`
		CompetencyRating     string  `json:"competency_rating"`
		Achievements         string  `json:"achievements"`
		AreasForImprovement  string  `json:"areas_for_improvement"`
		Goals                string  `json:"goals"`
		DevelopmentPlan      string  `json:"development_plan"`
		EmployeeComments     string  `json:"employee_comments"`
		ManagerComments      string  `json:"manager_comments"`
		SalaryIncrease       float64 `json:"salary_increase"`
		BonusAmount          float64 `json:"bonus_amount"`
		PromotionRecommended bool    `json:"promotion_recommended"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Validate employee exists
	var employee models.Employee
	if err := db.First(&employee, req.EmployeeID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Employee")
			return
		}
		HandleInternalError(c, "Failed to validate employee: "+err.Error())
		return
	}

	// Parse dates
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		HandleBadRequest(c, "Invalid start date format. Use YYYY-MM-DD")
		return
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		HandleBadRequest(c, "Invalid end date format. Use YYYY-MM-DD")
		return
	}

	reviewDate, err := time.Parse("2006-01-02", req.ReviewDate)
	if err != nil {
		HandleBadRequest(c, "Invalid review date format. Use YYYY-MM-DD")
		return
	}

	// Generate review ID
	reviewID := fmt.Sprintf("PR-%d-%d", req.EmployeeID, time.Now().Unix())

	// Set default values
	if req.ReviewType == "" {
		req.ReviewType = "annual"
	}

	// Create performance review
	review := models.PerformanceReview{
		ReviewID:             reviewID,
		EmployeeID:           req.EmployeeID,
		ReviewerID:           *userID.(*uint),
		ReviewPeriod:         req.ReviewPeriod,
		StartDate:            startDate,
		EndDate:              endDate,
		ReviewDate:           reviewDate,
		ReviewType:           req.ReviewType,
		ReviewCycle:          req.ReviewCycle,
		OverallRating:        req.OverallRating,
		GoalAchievement:      req.GoalAchievement,
		CompetencyRating:     req.CompetencyRating,
		Achievements:         req.Achievements,
		AreasForImprovement:  req.AreasForImprovement,
		Goals:                req.Goals,
		DevelopmentPlan:      req.DevelopmentPlan,
		EmployeeComments:     req.EmployeeComments,
		ManagerComments:      req.ManagerComments,
		Status:               "submitted",
		SubmittedAt:          &[]time.Time{time.Now()}[0],
		SalaryIncrease:       req.SalaryIncrease,
		BonusAmount:          req.BonusAmount,
		PromotionRecommended: req.PromotionRecommended,
	}

	if err := db.Create(&review).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") {
			HandleBadRequest(c, "Performance review with this ID already exists")
			return
		}
		HandleInternalError(c, "Failed to create performance review: "+err.Error())
		return
	}

	// Load the created review with relationships
	if err := db.Preload("Employee").Preload("Reviewer").First(&review, review.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load created performance review: "+err.Error())
		return
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Performance review submitted successfully",
		Data:    review,
	})
}

// ApprovePerformanceReview approves a performance review
func ApprovePerformanceReview(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req struct {
		HRComments string `json:"hr_comments"`
		Approved   bool   `json:"approved"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context (for authentication check)
	_, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing performance review
	var review models.PerformanceReview
	if err := db.First(&review, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Performance review")
			return
		}
		HandleInternalError(c, "Failed to fetch performance review: "+err.Error())
		return
	}

	// Check if review can be approved
	if review.Status == "approved" || review.Status == "completed" {
		HandleBadRequest(c, "Performance review is already approved")
		return
	}

	// Update review with approval data
	now := time.Now()
	status := "approved"
	if !req.Approved {
		status = "rejected"
	}

	updates := map[string]interface{}{
		"status":      status,
		"hr_comments": req.HRComments,
		"approved_at": &now,
	}

	// If approved, also mark as completed
	if req.Approved {
		updates["completed_at"] = &now
		updates["status"] = "completed"
	}

	if err := db.Model(&review).Updates(updates).Error; err != nil {
		HandleInternalError(c, "Failed to approve performance review: "+err.Error())
		return
	}

	// Load updated review with relationships
	if err := db.Preload("Employee").Preload("Reviewer").First(&review, review.ID).Error; err != nil {
		HandleInternalError(c, "Failed to load approved performance review: "+err.Error())
		return
	}

	message := "Performance review approved successfully"
	if !req.Approved {
		message = "Performance review rejected"
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: message,
		Data:    review,
	})
}
