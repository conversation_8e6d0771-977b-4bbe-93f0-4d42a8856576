-- Compliance and Audit System Migration
-- This migration creates tables for comprehensive audit logging, compliance reporting,
-- and regulatory compliance tracking

-- Audit Logs Table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Event identification
    event_id VARCHAR(255) UNIQUE NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    
    -- User and session information
    user_id INTEGER REFERENCES users(id),
    username VARCHAR(255),
    user_role VARCHAR(100),
    session_id VARCHAR(255),
    impersonated_by_id INTEGER REFERENCES users(id),
    
    -- Target resource information
    resource_type VARCHAR(100),
    resource_id VARCHAR(255),
    resource_name VARCHAR(500),
    parent_resource_type VARCHAR(100),
    parent_resource_id VARCHAR(255),
    
    -- Technical details
    ip_address INET,
    user_agent TEXT,
    request_method VARCHAR(10),
    request_url TEXT,
    response_code INTEGER,
    request_size BIGINT DEFAULT 0,
    response_size BIGINT DEFAULT 0,
    
    -- Change tracking
    old_values TEXT,
    new_values TEXT,
    changed_fields TEXT,
    change_reason VARCHAR(500),
    
    -- Risk and compliance
    risk_level VARCHAR(20) DEFAULT 'low',
    compliance_frameworks TEXT,
    requires_review BOOLEAN DEFAULT FALSE,
    reviewed_at TIMESTAMP WITH TIME ZONE,
    reviewed_by_id INTEGER REFERENCES users(id),
    review_notes TEXT,
    
    -- Geolocation and device
    country VARCHAR(100),
    region VARCHAR(100),
    city VARCHAR(100),
    timezone VARCHAR(50),
    device_type VARCHAR(50),
    operating_system VARCHAR(100),
    browser VARCHAR(100),
    
    -- Additional context
    tags TEXT,
    metadata TEXT,
    correlation_id VARCHAR(255),
    trace_id VARCHAR(255),
    
    -- Data classification
    data_classification VARCHAR(50),
    contains_pii BOOLEAN DEFAULT FALSE,
    contains_phi BOOLEAN DEFAULT FALSE,
    contains_financial BOOLEAN DEFAULT FALSE,
    
    -- Retention and archival
    retention_period_days INTEGER DEFAULT 2555,
    archive_after_days INTEGER DEFAULT 365,
    archived_at TIMESTAMP WITH TIME ZONE,
    purge_after TIMESTAMP WITH TIME ZONE
);

-- Compliance Rules Table
CREATE TABLE IF NOT EXISTS compliance_rules (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Rule identification
    rule_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    framework VARCHAR(50) NOT NULL,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    
    -- Rule definition
    rule_type VARCHAR(50),
    severity VARCHAR(20),
    priority INTEGER DEFAULT 3,
    is_active BOOLEAN DEFAULT TRUE,
    is_automated BOOLEAN DEFAULT FALSE,
    
    -- Rule logic
    conditions TEXT,
    actions TEXT,
    validation_logic TEXT,
    remediation TEXT,
    
    -- Applicability
    applies_to TEXT,
    exclude_from TEXT,
    effective_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expiration_date TIMESTAMP WITH TIME ZONE,
    
    -- Compliance tracking
    last_checked TIMESTAMP WITH TIME ZONE,
    next_check TIMESTAMP WITH TIME ZONE,
    check_frequency_hours INTEGER DEFAULT 24,
    violation_count INTEGER DEFAULT 0,
    compliance_rate DECIMAL(5,2) DEFAULT 0,
    
    -- Rule ownership
    owner_id INTEGER NOT NULL REFERENCES users(id),
    responsible_team VARCHAR(255),
    contact_email VARCHAR(255),
    
    -- Documentation
    reference_url VARCHAR(500),
    documentation TEXT,
    examples TEXT,
    tags TEXT
);

-- Compliance Violations Table
CREATE TABLE IF NOT EXISTS compliance_violations (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Violation identification
    violation_id VARCHAR(255) UNIQUE NOT NULL,
    rule_id INTEGER NOT NULL REFERENCES compliance_rules(id) ON DELETE CASCADE,
    
    -- Violation details
    title VARCHAR(255) NOT NULL,
    description TEXT,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(50) DEFAULT 'open',
    
    -- Affected resource
    resource_type VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255) NOT NULL,
    resource_name VARCHAR(500),
    
    -- Detection information
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    detected_by_id INTEGER REFERENCES users(id),
    detection_method VARCHAR(50),
    audit_log_id INTEGER REFERENCES audit_logs(id),
    
    -- Resolution tracking
    assigned_to_id INTEGER REFERENCES users(id),
    resolved_at TIMESTAMP WITH TIME ZONE,
    resolved_by_id INTEGER REFERENCES users(id),
    resolution TEXT,
    resolution_notes TEXT,
    
    -- Impact assessment
    impact_level VARCHAR(20),
    business_impact TEXT,
    technical_impact TEXT,
    affected_users INTEGER DEFAULT 0,
    data_exposed BOOLEAN DEFAULT FALSE,
    
    -- Remediation
    remediation_steps TEXT,
    remediation_due_date TIMESTAMP WITH TIME ZONE,
    preventive_measures TEXT,
    
    -- Additional context
    evidence TEXT,
    attachments TEXT,
    tags TEXT,
    notes TEXT
);

-- Compliance Assessments Table
CREATE TABLE IF NOT EXISTS compliance_assessments (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Assessment identification
    assessment_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    framework VARCHAR(50) NOT NULL,
    type VARCHAR(50),
    
    -- Assessment scope
    scope TEXT,
    start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    end_date TIMESTAMP WITH TIME ZONE,
    planned_end_date TIMESTAMP WITH TIME ZONE,
    status VARCHAR(50) DEFAULT 'planning',
    
    -- Assessment team
    lead_assessor_id INTEGER NOT NULL REFERENCES users(id),
    assessment_team TEXT,
    external_auditor VARCHAR(255),
    
    -- Rules and controls
    rule_id INTEGER REFERENCES compliance_rules(id),
    rules_assessed TEXT,
    controls_tested TEXT,
    
    -- Results
    overall_status VARCHAR(50) DEFAULT 'unknown',
    compliance_score DECIMAL(5,2) DEFAULT 0,
    passed_controls INTEGER DEFAULT 0,
    failed_controls INTEGER DEFAULT 0,
    total_controls INTEGER DEFAULT 0,
    
    -- Findings
    critical_findings INTEGER DEFAULT 0,
    high_findings INTEGER DEFAULT 0,
    medium_findings INTEGER DEFAULT 0,
    low_findings INTEGER DEFAULT 0,
    recommendations TEXT,
    action_items TEXT,
    
    -- Documentation
    report_url VARCHAR(500),
    evidence_url VARCHAR(500),
    certificate_url VARCHAR(500),
    executive_summary TEXT,
    technical_findings TEXT,
    
    -- Follow-up
    next_assessment_date TIMESTAMP WITH TIME ZONE,
    follow_up_required BOOLEAN DEFAULT FALSE,
    follow_up_date TIMESTAMP WITH TIME ZONE,
    follow_up_notes TEXT,
    
    -- Additional metadata
    tags TEXT,
    metadata TEXT,
    cost DECIMAL(12,2) DEFAULT 0,
    currency VARCHAR(10) DEFAULT 'USD'
);

-- Compliance Reports Table
CREATE TABLE IF NOT EXISTS compliance_reports (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE,
    
    -- Report identification
    report_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    framework VARCHAR(50) NOT NULL,
    type VARCHAR(50),
    
    -- Report period
    period_start TIMESTAMP WITH TIME ZONE,
    period_end TIMESTAMP WITH TIME ZONE,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    valid_until TIMESTAMP WITH TIME ZONE,
    
    -- Report scope
    scope TEXT,
    departments TEXT,
    systems TEXT,
    
    -- Report data
    total_events INTEGER DEFAULT 0,
    compliance_score DECIMAL(5,2) DEFAULT 0,
    violation_count INTEGER DEFAULT 0,
    resolved_violations INTEGER DEFAULT 0,
    open_violations INTEGER DEFAULT 0,
    
    -- Risk metrics
    critical_risks INTEGER DEFAULT 0,
    high_risks INTEGER DEFAULT 0,
    medium_risks INTEGER DEFAULT 0,
    low_risks INTEGER DEFAULT 0,
    
    -- Report content
    executive_summary TEXT,
    key_findings TEXT,
    recommendations TEXT,
    action_plan TEXT,
    detailed_data TEXT,
    
    -- Report generation
    generated_by_id INTEGER NOT NULL REFERENCES users(id),
    generation_time_ms INTEGER DEFAULT 0,
    report_format VARCHAR(20) DEFAULT 'json',
    report_url VARCHAR(500),
    report_size BIGINT DEFAULT 0,
    
    -- Distribution
    recipients TEXT,
    distributed_at TIMESTAMP WITH TIME ZONE,
    distribution_method VARCHAR(50),
    
    -- Approval and review
    requires_approval BOOLEAN DEFAULT FALSE,
    approved_at TIMESTAMP WITH TIME ZONE,
    approved_by_id INTEGER REFERENCES users(id),
    review_notes TEXT,
    
    -- Additional metadata
    tags TEXT,
    metadata TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    is_public BOOLEAN DEFAULT FALSE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_id ON audit_logs(event_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_event_type ON audit_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_audit_logs_risk_level ON audit_logs(risk_level);
CREATE INDEX IF NOT EXISTS idx_audit_logs_requires_review ON audit_logs(requires_review);
CREATE INDEX IF NOT EXISTS idx_audit_logs_ip_address ON audit_logs(ip_address);
CREATE INDEX IF NOT EXISTS idx_audit_logs_session_id ON audit_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_correlation_id ON audit_logs(correlation_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_deleted_at ON audit_logs(deleted_at);

CREATE INDEX IF NOT EXISTS idx_compliance_rules_rule_id ON compliance_rules(rule_id);
CREATE INDEX IF NOT EXISTS idx_compliance_rules_framework ON compliance_rules(framework);
CREATE INDEX IF NOT EXISTS idx_compliance_rules_is_active ON compliance_rules(is_active);
CREATE INDEX IF NOT EXISTS idx_compliance_rules_severity ON compliance_rules(severity);
CREATE INDEX IF NOT EXISTS idx_compliance_rules_owner_id ON compliance_rules(owner_id);
CREATE INDEX IF NOT EXISTS idx_compliance_rules_next_check ON compliance_rules(next_check);
CREATE INDEX IF NOT EXISTS idx_compliance_rules_deleted_at ON compliance_rules(deleted_at);

CREATE INDEX IF NOT EXISTS idx_compliance_violations_violation_id ON compliance_violations(violation_id);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_rule_id ON compliance_violations(rule_id);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_status ON compliance_violations(status);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_severity ON compliance_violations(severity);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_resource ON compliance_violations(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_detected_at ON compliance_violations(detected_at);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_assigned_to_id ON compliance_violations(assigned_to_id);
CREATE INDEX IF NOT EXISTS idx_compliance_violations_deleted_at ON compliance_violations(deleted_at);

CREATE INDEX IF NOT EXISTS idx_compliance_assessments_assessment_id ON compliance_assessments(assessment_id);
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_framework ON compliance_assessments(framework);
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_status ON compliance_assessments(status);
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_lead_assessor_id ON compliance_assessments(lead_assessor_id);
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_start_date ON compliance_assessments(start_date);
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_deleted_at ON compliance_assessments(deleted_at);

CREATE INDEX IF NOT EXISTS idx_compliance_reports_report_id ON compliance_reports(report_id);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_framework ON compliance_reports(framework);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_generated_by_id ON compliance_reports(generated_by_id);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_period ON compliance_reports(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_generated_at ON compliance_reports(generated_at);
CREATE INDEX IF NOT EXISTS idx_compliance_reports_deleted_at ON compliance_reports(deleted_at);

-- Create triggers for updated_at timestamps
CREATE TRIGGER update_audit_logs_updated_at BEFORE UPDATE ON audit_logs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_rules_updated_at BEFORE UPDATE ON compliance_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_violations_updated_at BEFORE UPDATE ON compliance_violations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_assessments_updated_at BEFORE UPDATE ON compliance_assessments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_reports_updated_at BEFORE UPDATE ON compliance_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample compliance rules
INSERT INTO compliance_rules (
    rule_id,
    name,
    description,
    framework,
    category,
    rule_type,
    severity,
    is_active,
    is_automated,
    conditions,
    actions,
    owner_id,
    responsible_team,
    documentation
) VALUES 
(
    'SOX-404-001',
    'Document Access Control',
    'All document access must be logged and monitored for SOX compliance',
    'SOX',
    'Access Control',
    'control',
    'high',
    TRUE,
    TRUE,
    '{"event_types": ["access", "download", "export"], "resource_types": ["document"], "data_classification": ["confidential", "restricted"]}',
    '{"log_event": true, "require_approval": false, "notify_security": false}',
    1,
    'Security Team',
    'Sarbanes-Oxley Act Section 404 requires proper internal controls over financial reporting'
),
(
    'GDPR-ART6-001',
    'Personal Data Processing Consent',
    'Processing of personal data requires valid legal basis under GDPR Article 6',
    'GDPR',
    'Data Protection',
    'requirement',
    'critical',
    TRUE,
    TRUE,
    '{"contains_pii": true, "event_types": ["create", "update", "access", "export"]}',
    '{"require_consent": true, "log_event": true, "data_protection_review": true}',
    1,
    'Privacy Team',
    'GDPR Article 6 defines lawful basis for processing personal data'
),
(
    'HIPAA-164-001',
    'PHI Access Logging',
    'All access to Protected Health Information must be logged per HIPAA requirements',
    'HIPAA',
    'Access Control',
    'control',
    'high',
    TRUE,
    TRUE,
    '{"contains_phi": true, "event_types": ["access", "read", "download", "export", "print"]}',
    '{"log_event": true, "require_audit_trail": true, "minimum_necessary": true}',
    1,
    'Compliance Team',
    'HIPAA Security Rule 45 CFR 164.312(a)(2)(i) requires access control and audit logs'
) ON CONFLICT (rule_id) DO NOTHING;

-- Add comments for documentation
COMMENT ON TABLE audit_logs IS 'Comprehensive audit trail for all system activities and compliance monitoring';
COMMENT ON TABLE compliance_rules IS 'Compliance rules and requirements for various regulatory frameworks';
COMMENT ON TABLE compliance_violations IS 'Detected violations of compliance rules with resolution tracking';
COMMENT ON TABLE compliance_assessments IS 'Compliance assessments and audits with results and findings';
COMMENT ON TABLE compliance_reports IS 'Generated compliance reports for regulatory and management review';
