import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/router';
import RegulationsPage from '../app/regulations/page';
import regulationApi from '../app/services/regulationApi';
import apiService from '../app/services/api';
import { useUIStore } from '../app/stores/uiStore';
import { UserRole } from '../app/types';

// Mock dependencies
jest.mock('next/router');
jest.mock('../app/services/regulationApi');
jest.mock('../app/services/api');
jest.mock('../app/stores/uiStore');

const mockRouter = {
  push: jest.fn(),
  query: {},
  pathname: '/regulations'
};

const mockUseUIStore = {
  setGlobalLoading: jest.fn()
};

const mockedRegulationApi = regulationApi as jest.Mocked<typeof regulationApi>;
const mockedApiService = apiService as jest.Mocked<typeof apiService>;

describe('RegulationsPage', () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useUIStore as unknown as jest.Mock).mockReturnValue(mockUseUIStore);
    jest.clearAllMocks();
  });

  const mockRegulations = [
    {
      id: 1,
      title: 'Environmental Protection Standards',
      short_title: 'EPA Standards',
      type: 'rule' as const,
      status: 'effective' as const,
      cfr_title: '40',
      agency_id: 1,
      agency: {
        id: 1,
        name: 'Environmental Protection Agency',
        short_name: 'EPA'
      },
      created_by_id: 1,
      created_by: {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: 'admin' as UserRole,
        is_active: true,
        is_verified: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
      description: 'Comprehensive environmental protection standards',
      effective_date: '2024-01-01T00:00:00Z',
      is_significant: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      hierarchy_level: 'section' as const,
      order_in_parent: 1
    },
    {
      id: 2,
      title: 'Telecommunications Regulations',
      short_title: 'FCC Rules',
      type: 'regulation' as const,
      status: 'effective' as const,
      cfr_title: '47',
      agency_id: 2,
      agency: {
        id: 2,
        name: 'Federal Communications Commission',
        short_name: 'FCC'
      },
      created_by_id: 1,
      created_by: {
        id: 1,
        username: 'testuser',
        email: '<EMAIL>',
        first_name: 'Test',
        last_name: 'User',
        role: 'admin' as UserRole,
        is_active: true,
        is_verified: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
      description: 'Federal Communications Commission regulations',
      effective_date: '2024-02-01T00:00:00Z',
      is_significant: false,
      created_at: '2024-02-01T00:00:00Z',
      updated_at: '2024-02-01T00:00:00Z',
      hierarchy_level: 'section' as const,
      order_in_parent: 2
    }
  ];

  const mockAgencies = [
    {
      id: 1,
      name: 'Environmental Protection Agency',
      short_name: 'EPA'
    },
    {
      id: 2,
      name: 'Federal Communications Commission',
      short_name: 'FCC'
    }
  ];

  const mockPaginatedResponse = {
    data: mockRegulations,
    total: 2,
    page: 1,
    per_page: 25,
    total_pages: 1,
    has_next: false,
    has_prev: false
  };

  it('should render regulations page with title and description', async () => {
    mockedRegulationApi.getPublicRegulations.mockResolvedValue(mockPaginatedResponse);
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([
      { value: 'rule', label: 'Rule' },
      { value: 'regulation', label: 'Regulation' }
    ]);

    render(<RegulationsPage />);

    expect(screen.getByText('Federal Regulations')).toBeInTheDocument();
    expect(screen.getByText(/Browse published laws, rules, and regulations/)).toBeInTheDocument();
  });

  it('should display regulations list', async () => {
    mockedRegulationApi.getPublicRegulations.mockResolvedValue(mockPaginatedResponse);
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([]);
    mockedRegulationApi.formatRegulationTitle.mockImplementation((reg) => 
      reg.short_title ? `${reg.title} (${reg.short_title})` : reg.title
    );
    mockedRegulationApi.formatDate.mockImplementation((date) => 
      date ? new Date(date).toLocaleDateString() : 'Not set'
    );
    mockedRegulationApi.getEffectivenessStatus.mockReturnValue('effective');
    mockedRegulationApi.getTypeColor.mockReturnValue('blue');

    render(<RegulationsPage />);

    await waitFor(() => {
      expect(screen.getByText('Environmental Protection Standards (EPA Standards)')).toBeInTheDocument();
      expect(screen.getByText('Telecommunications Regulations (FCC Rules)')).toBeInTheDocument();
    });

    expect(screen.getByText('Comprehensive environmental protection standards')).toBeInTheDocument();
    expect(screen.getByText('Federal Communications Commission regulations')).toBeInTheDocument();
  });

  it('should handle search functionality', async () => {
    mockedRegulationApi.getPublicRegulations.mockResolvedValue(mockPaginatedResponse);
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([]);

    render(<RegulationsPage />);

    const searchInput = screen.getByPlaceholderText(/Search regulations by title/);
    const searchButton = screen.getByText('Search');

    fireEvent.change(searchInput, { target: { value: 'environmental' } });
    fireEvent.click(searchButton);

    await waitFor(() => {
      expect(mockedRegulationApi.getPublicRegulations).toHaveBeenCalledWith(
        expect.objectContaining({
          search: 'environmental',
          page: 1
        })
      );
    });
  });

  it('should handle filter functionality', async () => {
    mockedRegulationApi.getPublicRegulations.mockResolvedValue(mockPaginatedResponse);
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([
      { value: 'rule', label: 'Rule' },
      { value: 'regulation', label: 'Regulation' }
    ]);

    render(<RegulationsPage />);

    // Open filters
    const filtersButton = screen.getByText('Filters');
    fireEvent.click(filtersButton);

    await waitFor(() => {
      expect(screen.getByText('Type')).toBeInTheDocument();
      expect(screen.getByText('Agency')).toBeInTheDocument();
    });

    // Select type filter
    const typeSelect = screen.getByDisplayValue('All Types');
    fireEvent.change(typeSelect, { target: { value: 'rule' } });

    await waitFor(() => {
      expect(mockedRegulationApi.getPublicRegulations).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'rule',
          page: 1
        })
      );
    });
  });

  it('should handle pagination', async () => {
    const paginatedResponse = {
      ...mockPaginatedResponse,
      page: 1,
      total_pages: 3,
      has_next: true,
      has_prev: false
    };

    mockedRegulationApi.getPublicRegulations.mockResolvedValue(paginatedResponse);
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([]);

    render(<RegulationsPage />);

    await waitFor(() => {
      expect(screen.getByText('Page 1 of 3')).toBeInTheDocument();
    });

    const nextButton = screen.getByText('Next');
    expect(nextButton).not.toBeDisabled();

    fireEvent.click(nextButton);

    await waitFor(() => {
      expect(mockedRegulationApi.getPublicRegulations).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 2
        })
      );
    });
  });

  it('should display loading state', () => {
    mockedRegulationApi.getPublicRegulations.mockImplementation(() => 
      new Promise(() => {}) // Never resolves to keep loading state
    );
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([]);

    render(<RegulationsPage />);

    expect(screen.getByText('Loading regulations...')).toBeInTheDocument();
  });

  it('should display empty state when no regulations found', async () => {
    const emptyResponse = {
      data: [],
      total: 0,
      page: 1,
      per_page: 25,
      total_pages: 0,
      has_next: false,
      has_prev: false
    };

    mockedRegulationApi.getPublicRegulations.mockResolvedValue(emptyResponse);
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([]);

    render(<RegulationsPage />);

    await waitFor(() => {
      expect(screen.getByText('No regulations found')).toBeInTheDocument();
      expect(screen.getByText('Try adjusting your search criteria or filters.')).toBeInTheDocument();
    });
  });

  it('should clear filters when clear button is clicked', async () => {
    mockedRegulationApi.getPublicRegulations.mockResolvedValue(mockPaginatedResponse);
    mockedApiService.get.mockResolvedValue({ data: mockAgencies });
    mockedRegulationApi.getRegulationTypes.mockReturnValue([
      { value: 'rule', label: 'Rule' }
    ]);

    render(<RegulationsPage />);

    // Open filters
    const filtersButton = screen.getByText('Filters');
    fireEvent.click(filtersButton);

    // Set a filter
    const typeSelect = screen.getByDisplayValue('All Types');
    fireEvent.change(typeSelect, { target: { value: 'rule' } });

    // Clear filters
    const clearButton = screen.getByText('Clear Filters');
    fireEvent.click(clearButton);

    await waitFor(() => {
      expect(mockedRegulationApi.getPublicRegulations).toHaveBeenCalledWith(
        expect.objectContaining({
          page: 1,
          per_page: 25,
          sort_by: 'effective_date',
          sort_order: 'desc'
        })
      );
    });
  });
});
