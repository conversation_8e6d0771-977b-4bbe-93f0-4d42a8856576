package services

import (
	"encoding/json"
	"fmt"
	"time"

	"federal-register-clone/internal/config"
	"federal-register-clone/internal/models"
	"gorm.io/gorm"
)

// NotificationService handles all notification operations
type NotificationService struct {
	db           *gorm.DB
	emailService *EmailService
	config       *config.Config
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *gorm.DB, cfg *config.Config) *NotificationService {
	return &NotificationService{
		db:           db,
		emailService: NewEmailService(cfg),
		config:       cfg,
	}
}

// NotificationType represents different types of notifications
type NotificationType string

const (
	NotificationTypeEmail    NotificationType = "email"
	NotificationTypeInApp    NotificationType = "in_app"
	NotificationTypeSMS      NotificationType = "sms"
	NotificationTypePush     NotificationType = "push"
	NotificationTypeWebhook  NotificationType = "webhook"
)

// NotificationPriority represents notification priority levels
type NotificationPriority string

const (
	PriorityLow      NotificationPriority = "low"
	PriorityNormal   NotificationPriority = "normal"
	PriorityHigh     NotificationPriority = "high"
	PriorityCritical NotificationPriority = "critical"
)

// NotificationRequest represents a notification request
type NotificationRequest struct {
	Type        NotificationType     `json:"type"`
	Priority    NotificationPriority `json:"priority"`
	Recipients  []string             `json:"recipients"`
	Subject     string               `json:"subject"`
	Message     string               `json:"message"`
	Data        map[string]interface{} `json:"data"`
	ScheduledAt *time.Time           `json:"scheduled_at"`
	ExpiresAt   *time.Time           `json:"expires_at"`
	Category    string               `json:"category"`
	Source      string               `json:"source"`
	SourceID    string               `json:"source_id"`
}

// NotificationResult represents the result of sending a notification
type NotificationResult struct {
	ID          string    `json:"id"`
	Status      string    `json:"status"`
	SentAt      time.Time `json:"sent_at"`
	DeliveredAt *time.Time `json:"delivered_at"`
	Error       string    `json:"error,omitempty"`
}

// SendNotification sends a notification
func (s *NotificationService) SendNotification(req NotificationRequest) (*NotificationResult, error) {
	// Create notification record
	notification := &models.Notification{
		Type:        string(req.Type),
		Priority:    string(req.Priority),
		Subject:     req.Subject,
		Message:     req.Message,
		Category:    req.Category,
		Source:      req.Source,
		SourceID:    req.SourceID,
		Status:      "pending",
		ScheduledAt: req.ScheduledAt,
		ExpiresAt:   req.ExpiresAt,
	}

	// Serialize data
	if req.Data != nil {
		if dataJSON, err := json.Marshal(req.Data); err == nil {
			notification.Data = string(dataJSON)
		}
	}

	// Serialize recipients
	if recipientsJSON, err := json.Marshal(req.Recipients); err == nil {
		notification.Recipients = string(recipientsJSON)
	}

	// Save notification
	if err := s.db.Create(notification).Error; err != nil {
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	// Send immediately if not scheduled
	if req.ScheduledAt == nil || req.ScheduledAt.Before(time.Now()) {
		return s.deliverNotification(notification, req)
	}

	// Return pending result for scheduled notifications
	return &NotificationResult{
		ID:     fmt.Sprintf("%d", notification.ID),
		Status: "scheduled",
		SentAt: time.Now(),
	}, nil
}

// deliverNotification actually delivers the notification
func (s *NotificationService) deliverNotification(notification *models.Notification, req NotificationRequest) (*NotificationResult, error) {
	result := &NotificationResult{
		ID:     fmt.Sprintf("%d", notification.ID),
		SentAt: time.Now(),
	}

	var err error
	switch NotificationType(notification.Type) {
	case NotificationTypeEmail:
		err = s.sendEmailNotification(req)
	case NotificationTypeInApp:
		err = s.sendInAppNotification(notification, req)
	case NotificationTypeSMS:
		err = s.sendSMSNotification(req)
	case NotificationTypePush:
		err = s.sendPushNotification(req)
	case NotificationTypeWebhook:
		err = s.sendWebhookNotification(req)
	default:
		err = fmt.Errorf("unsupported notification type: %s", notification.Type)
	}

	// Update notification status
	if err != nil {
		result.Status = "failed"
		result.Error = err.Error()
		notification.Status = "failed"
		notification.ErrorMessage = err.Error()
	} else {
		result.Status = "sent"
		result.DeliveredAt = &result.SentAt
		notification.Status = "sent"
		notification.SentAt = &result.SentAt
	}

	// Save updated notification
	s.db.Save(notification)

	return result, err
}

// sendEmailNotification sends an email notification
func (s *NotificationService) sendEmailNotification(req NotificationRequest) error {
	for _, recipient := range req.Recipients {
		if err := s.emailService.SendEmail(recipient, req.Subject, req.Message); err != nil {
			return fmt.Errorf("failed to send email to %s: %w", recipient, err)
		}
	}
	return nil
}

// sendInAppNotification creates an in-app notification
func (s *NotificationService) sendInAppNotification(notification *models.Notification, req NotificationRequest) error {
	// Create in-app notifications for each recipient
	for _, recipient := range req.Recipients {
		// Find user by email or username
		var user models.User
		if err := s.db.Where("email = ? OR username = ?", recipient, recipient).First(&user).Error; err != nil {
			continue // Skip if user not found
		}

		inAppNotification := &models.UserNotification{
			UserID:         user.ID,
			NotificationID: notification.ID,
			Title:          req.Subject,
			Message:        req.Message,
			Type:           notification.Type,
			Priority:       notification.Priority,
			IsRead:         false,
			Category:       notification.Category,
		}

		s.db.Create(inAppNotification)
	}
	return nil
}

// sendSMSNotification sends an SMS notification (placeholder)
func (s *NotificationService) sendSMSNotification(req NotificationRequest) error {
	// In a real implementation, integrate with SMS providers like Twilio, AWS SNS, etc.
	// For now, log the SMS
	for _, recipient := range req.Recipients {
		fmt.Printf("SMS to %s: %s\n", recipient, req.Message)
	}
	return nil
}

// sendPushNotification sends a push notification (placeholder)
func (s *NotificationService) sendPushNotification(req NotificationRequest) error {
	// In a real implementation, integrate with push notification services
	// For now, log the push notification
	for _, recipient := range req.Recipients {
		fmt.Printf("Push notification to %s: %s\n", recipient, req.Subject)
	}
	return nil
}

// sendWebhookNotification sends a webhook notification (placeholder)
func (s *NotificationService) sendWebhookNotification(req NotificationRequest) error {
	// In a real implementation, send HTTP POST to webhook URLs
	// For now, log the webhook
	fmt.Printf("Webhook notification: %s - %s\n", req.Subject, req.Message)
	return nil
}

// SendRetentionPolicyNotification sends retention policy notifications
func (s *NotificationService) SendRetentionPolicyNotification(documentID uint, policyName, action string, dueDate time.Time) error {
	// Get document details
	var document models.Document
	if err := s.db.Preload("CreatedBy").First(&document, documentID).Error; err != nil {
		return fmt.Errorf("document not found: %w", err)
	}

	// Create notification request
	req := NotificationRequest{
		Type:     NotificationTypeEmail,
		Priority: PriorityNormal,
		Recipients: []string{document.CreatedBy.Email},
		Subject:  fmt.Sprintf("Document Retention Action Required: %s", document.Title),
		Message: fmt.Sprintf(`
Document: %s
Retention Policy: %s
Action Required: %s
Due Date: %s

Please take the required action before the due date.

Document ID: %d
Policy: %s
`, document.Title, policyName, action, dueDate.Format("2006-01-02"), documentID, policyName),
		Category: "retention_policy",
		Source:   "retention_service",
		SourceID: fmt.Sprintf("doc_%d", documentID),
	}

	_, err := s.SendNotification(req)
	return err
}

// SendTrainingEnrollmentNotification sends training enrollment notifications
func (s *NotificationService) SendTrainingEnrollmentNotification(employeeID uint, trainingName string, enrollmentType string) error {
	// Get employee details
	var employee models.Employee
	if err := s.db.First(&employee, employeeID).Error; err != nil {
		return fmt.Errorf("employee not found: %w", err)
	}

	var subject, message string
	switch enrollmentType {
	case "enrolled":
		subject = fmt.Sprintf("Training Enrollment Confirmation: %s", trainingName)
		message = fmt.Sprintf(`
Dear %s %s,

You have been successfully enrolled in the training program: %s

Please check your training dashboard for more details and schedule information.

Best regards,
HR Training Team
`, employee.FirstName, employee.LastName, trainingName)
	case "completed":
		subject = fmt.Sprintf("Training Completion Confirmation: %s", trainingName)
		message = fmt.Sprintf(`
Dear %s %s,

Congratulations! You have successfully completed the training program: %s

Your completion certificate will be available in your training records.

Best regards,
HR Training Team
`, employee.FirstName, employee.LastName, trainingName)
	}

	req := NotificationRequest{
		Type:       NotificationTypeEmail,
		Priority:   PriorityNormal,
		Recipients: []string{employee.PersonalEmail},
		Subject:    subject,
		Message:    message,
		Category:   "training",
		Source:     "hr_service",
		SourceID:   fmt.Sprintf("emp_%d", employeeID),
	}

	_, err := s.SendNotification(req)
	return err
}

// SendWorkflowNotification sends workflow-related notifications
func (s *NotificationService) SendWorkflowNotification(userEmail, workflowName, stepName, action string, workflowID uint) error {
	subject := fmt.Sprintf("Workflow Action Required: %s", workflowName)
	message := fmt.Sprintf(`
A workflow step requires your attention:

Workflow: %s
Step: %s
Action Required: %s

Please log in to the system to complete this action.

Workflow ID: %d
`, workflowName, stepName, action, workflowID)

	req := NotificationRequest{
		Type:       NotificationTypeEmail,
		Priority:   PriorityHigh,
		Recipients: []string{userEmail},
		Subject:    subject,
		Message:    message,
		Category:   "workflow",
		Source:     "workflow_engine",
		SourceID:   fmt.Sprintf("workflow_%d", workflowID),
	}

	_, err := s.SendNotification(req)
	return err
}

// ProcessScheduledNotifications processes notifications that are scheduled to be sent
func (s *NotificationService) ProcessScheduledNotifications() error {
	var notifications []models.Notification
	
	// Get notifications that are scheduled and due
	err := s.db.Where("status = ? AND scheduled_at <= ?", "pending", time.Now()).
		Find(&notifications).Error
	if err != nil {
		return fmt.Errorf("failed to get scheduled notifications: %w", err)
	}

	for _, notification := range notifications {
		// Reconstruct notification request
		var recipients []string
		json.Unmarshal([]byte(notification.Recipients), &recipients)
		
		var data map[string]interface{}
		if notification.Data != "" {
			json.Unmarshal([]byte(notification.Data), &data)
		}

		req := NotificationRequest{
			Type:       NotificationType(notification.Type),
			Priority:   NotificationPriority(notification.Priority),
			Recipients: recipients,
			Subject:    notification.Subject,
			Message:    notification.Message,
			Data:       data,
			Category:   notification.Category,
			Source:     notification.Source,
			SourceID:   notification.SourceID,
		}

		// Deliver the notification
		s.deliverNotification(&notification, req)
	}

	return nil
}
