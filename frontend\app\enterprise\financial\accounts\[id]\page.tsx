'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { financialApi } from '../../../../services/enterpriseApi';
import { ChartOfAccounts } from '../../../../types/enterprise';

const AccountViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const accountId = parseInt(params.id as string);
  
  const [account, setAccount] = useState<ChartOfAccounts | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (accountId) {
      fetchAccount();
    }
  }, [accountId]);

  const fetchAccount = async () => {
    try {
      setLoading(true);
      const response = await financialApi.getAccount(accountId);
      setAccount(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch account');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this account?')) return;
    
    try {
      await financialApi.deleteAccount(accountId);
      router.push('/enterprise/financial/accounts');
    } catch (err: any) {
      setError(err.message || 'Failed to delete account');
    }
  };

  if (loading) return <div className="p-6">Loading account...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!account) return <div className="p-6">Account not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Account Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/financial/accounts')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Accounts
          </button>
          <button
            onClick={() => router.push(`/enterprise/financial/accounts/${accountId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Account
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Account
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{account.account_name}</h2>
              <p className="text-sm text-gray-600">Account Code: {account.account_code}</p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-gray-900">
                {account.currency_code} {account.current_balance.toLocaleString()}
              </div>
              <div className="text-sm text-gray-600">Current Balance</div>
            </div>
          </div>
        </div>

        {/* Account Information */}
        <div className="px-6 py-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Account Type</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{account.account_type}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Normal Balance</label>
              <p className="mt-1 text-sm text-gray-900 capitalize">{account.normal_balance}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Level</label>
              <p className="mt-1 text-sm text-gray-900">{account.level}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Currency</label>
              <p className="mt-1 text-sm text-gray-900">{account.currency_code}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Opening Balance</label>
              <p className="mt-1 text-sm text-gray-900">
                {account.currency_code} {account.opening_balance.toLocaleString()}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                account.is_active 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {account.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>
          </div>

          {account.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <p className="mt-1 text-sm text-gray-900">{account.description}</p>
            </div>
          )}
        </div>

        {/* Account Properties */}
        <div className="px-6 py-4 border-t border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Account Properties</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-2 ${
                account.is_control_account ? 'bg-blue-500' : 'bg-gray-300'
              }`}></div>
              <span className="text-sm text-gray-900">Control Account</span>
            </div>
            
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-2 ${
                account.allow_posting ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <span className="text-sm text-gray-900">Allow Posting</span>
            </div>
            
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-2 ${
                account.is_active ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="text-sm text-gray-900">Active</span>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        {(account.tax_code || account.reporting_code || account.gl_class) && (
          <div className="px-6 py-4 border-t border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Additional Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {account.tax_code && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Tax Code</label>
                  <p className="mt-1 text-sm text-gray-900">{account.tax_code}</p>
                </div>
              )}
              
              {account.reporting_code && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">Reporting Code</label>
                  <p className="mt-1 text-sm text-gray-900">{account.reporting_code}</p>
                </div>
              )}
              
              {account.gl_class && (
                <div>
                  <label className="block text-sm font-medium text-gray-700">GL Class</label>
                  <p className="mt-1 text-sm text-gray-900">{account.gl_class}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(account.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(account.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountViewPage;
