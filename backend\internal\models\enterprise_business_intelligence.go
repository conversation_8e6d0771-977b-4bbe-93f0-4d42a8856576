package models

import (
	"time"

	"gorm.io/gorm"
)

// DataSourceType represents different types of data sources
type DataSourceType string

const (
	DataSourceDatabase   DataSourceType = "database"
	DataSourceAPI        DataSourceType = "api"
	DataSourceFile       DataSourceType = "file"
	DataSourceStream     DataSourceType = "stream"
	DataSourceWebService DataSourceType = "webservice"
)

// AnalyticsType represents different types of analytics
type AnalyticsType string

const (
	AnalyticsDescriptive  AnalyticsType = "descriptive"   // What happened?
	AnalyticsDiagnostic   AnalyticsType = "diagnostic"    // Why did it happen?
	AnalyticsPredictive   AnalyticsType = "predictive"    // What will happen?
	AnalyticsPrescriptive AnalyticsType = "prescriptive"  // What should we do?
)

// DataWarehouse represents enterprise data warehouse
type DataWarehouse struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Warehouse identification
	WarehouseName   string `json:"warehouse_name" gorm:"not null"`
	Description     string `json:"description" gorm:"type:text"`
	
	// Technical configuration
	DatabaseType    string `json:"database_type" gorm:"default:'postgresql'"`   // "postgresql", "mysql", "oracle", "snowflake"
	ConnectionString string `json:"connection_string"`                          // Encrypted connection string
	SchemaName      string `json:"schema_name" gorm:"default:'public'"`
	
	// Capacity and performance
	StorageSize     uint64  `json:"storage_size" gorm:"default:0"`              // Bytes
	MaxConnections  int     `json:"max_connections" gorm:"default:100"`
	QueryTimeout    int     `json:"query_timeout" gorm:"default:300"`           // Seconds
	
	// Data retention
	RetentionPeriod int     `json:"retention_period" gorm:"default:2555"`       // Days (7 years default)
	ArchivalEnabled bool    `json:"archival_enabled" gorm:"default:true"`
	CompressionType string  `json:"compression_type" gorm:"default:'gzip'"`
	
	// Security and access
	EncryptionEnabled bool   `json:"encryption_enabled" gorm:"default:true"`
	AccessPolicy      string `json:"access_policy" gorm:"type:text"`            // JSON access policy
	
	// Monitoring and maintenance
	IsActive          bool       `json:"is_active" gorm:"default:true"`
	LastMaintenance   *time.Time `json:"last_maintenance"`
	NextMaintenance   time.Time  `json:"next_maintenance"`
	
	// Performance metrics
	QueryCount        uint64     `json:"query_count" gorm:"default:0"`
	AvgQueryTime      float64    `json:"avg_query_time" gorm:"default:0"`        // Milliseconds
	LastOptimized     *time.Time `json:"last_optimized"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// DataSource represents data sources for analytics
type DataSource struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Source identification
	SourceName      string         `json:"source_name" gorm:"not null"`
	Description     string         `json:"description" gorm:"type:text"`
	SourceType      DataSourceType `json:"source_type" gorm:"not null"`
	
	// Connection details
	ConnectionString string `json:"connection_string"`                          // Encrypted connection details
	AuthMethod      string `json:"auth_method" gorm:"default:'basic'"`          // "basic", "oauth", "token", "certificate"
	Credentials     string `json:"credentials"`                                 // Encrypted credentials
	
	// Data structure
	Schema          string `json:"schema" gorm:"type:text"`                     // JSON schema definition
	TableMapping    string `json:"table_mapping" gorm:"type:text"`              // JSON table mapping
	FieldMapping    string `json:"field_mapping" gorm:"type:text"`              // JSON field mapping
	
	// Synchronization
	SyncEnabled     bool   `json:"sync_enabled" gorm:"default:true"`
	SyncFrequency   string `json:"sync_frequency" gorm:"default:'hourly'"`      // "realtime", "hourly", "daily", "weekly"
	LastSyncAt      *time.Time `json:"last_sync_at"`
	NextSyncAt      *time.Time `json:"next_sync_at"`
	
	// Data quality
	QualityRules    string `json:"quality_rules" gorm:"type:text"`              // JSON quality rules
	ErrorThreshold  float64 `json:"error_threshold" gorm:"default:5.0"`         // Percentage
	LastQualityCheck *time.Time `json:"last_quality_check"`
	QualityScore    float64 `json:"quality_score" gorm:"default:0"`             // 0-100 score
	
	// Performance and monitoring
	IsActive        bool       `json:"is_active" gorm:"default:true"`
	RecordCount     uint64     `json:"record_count" gorm:"default:0"`
	LastRecordAt    *time.Time `json:"last_record_at"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// Dashboard represents business intelligence dashboards
type Dashboard struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Dashboard identification
	DashboardName   string `json:"dashboard_name" gorm:"not null"`
	Description     string `json:"description" gorm:"type:text"`
	Category        string `json:"category"`                                    // "executive", "operational", "financial", "compliance"
	
	// Dashboard configuration
	Layout          string `json:"layout" gorm:"type:text"`                     // JSON layout configuration
	Widgets         string `json:"widgets" gorm:"type:text"`                    // JSON widget definitions
	Filters         string `json:"filters" gorm:"type:text"`                    // JSON filter definitions
	
	// Access and sharing
	IsPublic        bool   `json:"is_public" gorm:"default:false"`
	ShareToken      string `json:"share_token"`                                 // For public sharing
	AccessList      string `json:"access_list" gorm:"type:text"`                // JSON access list
	
	// Refresh and caching
	RefreshInterval int        `json:"refresh_interval" gorm:"default:300"`     // Seconds
	CacheEnabled    bool       `json:"cache_enabled" gorm:"default:true"`
	CacheDuration   int        `json:"cache_duration" gorm:"default:3600"`      // Seconds
	LastRefreshed   *time.Time `json:"last_refreshed"`
	
	// Owner and permissions
	OwnerID         uint `json:"owner_id" gorm:"not null"`
	Owner           User `json:"owner" gorm:"foreignKey:OwnerID"`
	
	// Usage analytics
	ViewCount       uint64     `json:"view_count" gorm:"default:0"`
	LastViewed      *time.Time `json:"last_viewed"`
	AvgLoadTime     float64    `json:"avg_load_time" gorm:"default:0"`          // Milliseconds
	
	// Status
	IsActive        bool `json:"is_active" gorm:"default:true"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// Report represents analytical reports
type Report struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Report identification
	ReportName      string        `json:"report_name" gorm:"not null"`
	Description     string        `json:"description" gorm:"type:text"`
	ReportType      string        `json:"report_type" gorm:"default:'standard'"`  // "standard", "adhoc", "scheduled"
	AnalyticsType   AnalyticsType `json:"analytics_type" gorm:"default:'descriptive'"`
	
	// Report definition
	Query           string `json:"query" gorm:"type:text"`                      // SQL query or query definition
	DataSources     string `json:"data_sources" gorm:"type:text"`               // JSON array of data source IDs
	Parameters      string `json:"parameters" gorm:"type:text"`                 // JSON parameters
	
	// Report format and output
	OutputFormat    string `json:"output_format" gorm:"default:'pdf'"`          // "pdf", "excel", "csv", "json"
	Template        string `json:"template" gorm:"type:text"`                   // Report template
	Visualization   string `json:"visualization" gorm:"type:text"`              // JSON visualization config
	
	// Scheduling
	IsScheduled     bool   `json:"is_scheduled" gorm:"default:false"`
	Schedule        string `json:"schedule"`                                    // Cron expression
	NextRun         *time.Time `json:"next_run"`
	LastRun         *time.Time `json:"last_run"`
	
	// Distribution
	Recipients      string `json:"recipients" gorm:"type:text"`                 // JSON recipient list
	DeliveryMethod  string `json:"delivery_method" gorm:"default:'email'"`      // "email", "ftp", "api"
	
	// Owner and permissions
	CreatedByID     uint `json:"created_by_id" gorm:"not null"`
	CreatedBy       User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	
	// Execution tracking
	ExecutionCount  uint64     `json:"execution_count" gorm:"default:0"`
	AvgExecutionTime float64   `json:"avg_execution_time" gorm:"default:0"`     // Milliseconds
	LastError       string     `json:"last_error" gorm:"type:text"`
	
	// Status
	Status          string `json:"status" gorm:"default:'active'"`              // "active", "paused", "error"
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// KPI represents key performance indicators
type KPI struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// KPI identification
	KPIName         string `json:"kpi_name" gorm:"not null"`
	Description     string `json:"description" gorm:"type:text"`
	Category        string `json:"category"`                                    // "financial", "operational", "customer", "learning"
	
	// KPI definition
	Formula         string `json:"formula" gorm:"type:text"`                    // Calculation formula
	DataSource      string `json:"data_source" gorm:"type:text"`                // JSON data source configuration
	Unit            string `json:"unit"`                                        // "percentage", "currency", "count", "ratio"
	
	// Target and thresholds
	TargetValue     float64 `json:"target_value"`
	MinThreshold    float64 `json:"min_threshold"`
	MaxThreshold    float64 `json:"max_threshold"`
	CriticalMin     float64 `json:"critical_min"`
	CriticalMax     float64 `json:"critical_max"`
	
	// Current values
	CurrentValue    float64    `json:"current_value" gorm:"default:0"`
	PreviousValue   float64    `json:"previous_value" gorm:"default:0"`
	Trend           string     `json:"trend" gorm:"default:'stable'"`           // "up", "down", "stable"
	LastCalculated  *time.Time `json:"last_calculated"`
	
	// Calculation frequency
	UpdateFrequency string `json:"update_frequency" gorm:"default:'daily'"`     // "realtime", "hourly", "daily", "weekly"
	NextUpdate      *time.Time `json:"next_update"`
	
	// Owner and responsibility
	OwnerID         uint `json:"owner_id" gorm:"not null"`
	Owner           User `json:"owner" gorm:"foreignKey:OwnerID"`
	
	// Alerting
	AlertEnabled    bool    `json:"alert_enabled" gorm:"default:false"`
	AlertThreshold  float64 `json:"alert_threshold"`
	LastAlert       *time.Time `json:"last_alert"`
	
	// Status
	IsActive        bool `json:"is_active" gorm:"default:true"`
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// DataMining represents data mining and machine learning models
type DataMining struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Model identification
	ModelName       string        `json:"model_name" gorm:"not null"`
	Description     string        `json:"description" gorm:"type:text"`
	ModelType       string        `json:"model_type" gorm:"not null"`           // "classification", "regression", "clustering", "association"
	Algorithm       string        `json:"algorithm"`                            // "random_forest", "neural_network", "svm", etc.
	AnalyticsType   AnalyticsType `json:"analytics_type" gorm:"default:'predictive'"`
	
	// Model configuration
	Parameters      string `json:"parameters" gorm:"type:text"`                 // JSON model parameters
	Features        string `json:"features" gorm:"type:text"`                   // JSON feature definitions
	TrainingData    string `json:"training_data" gorm:"type:text"`              // Training data configuration
	
	// Model performance
	Accuracy        float64    `json:"accuracy" gorm:"default:0"`               // Model accuracy (0-1)
	Precision       float64    `json:"precision" gorm:"default:0"`              // Model precision (0-1)
	Recall          float64    `json:"recall" gorm:"default:0"`                 // Model recall (0-1)
	F1Score         float64    `json:"f1_score" gorm:"default:0"`               // F1 score (0-1)
	
	// Training and deployment
	TrainedAt       *time.Time `json:"trained_at"`
	DeployedAt      *time.Time `json:"deployed_at"`
	LastRetrained   *time.Time `json:"last_retrained"`
	RetrainingFreq  string     `json:"retraining_frequency" gorm:"default:'monthly'"`
	
	// Model artifacts
	ModelPath       string `json:"model_path"`                                  // Path to model file
	ModelVersion    string `json:"model_version" gorm:"default:'1.0'"`
	ModelSize       uint64 `json:"model_size" gorm:"default:0"`                 // Bytes
	
	// Usage tracking
	PredictionCount uint64     `json:"prediction_count" gorm:"default:0"`
	LastPrediction  *time.Time `json:"last_prediction"`
	AvgResponseTime float64    `json:"avg_response_time" gorm:"default:0"`      // Milliseconds
	
	// Owner and permissions
	CreatedByID     uint `json:"created_by_id" gorm:"not null"`
	CreatedBy       User `json:"created_by" gorm:"foreignKey:CreatedByID"`
	
	// Status
	Status          string `json:"status" gorm:"default:'training'"`            // "training", "deployed", "retired"
	
	// Additional metadata
	Metadata string `json:"metadata" gorm:"type:text"`                          // JSON metadata
}

// TableName returns the table name for DataWarehouse model
func (DataWarehouse) TableName() string {
	return "data_warehouses"
}

// TableName returns the table name for DataSource model
func (DataSource) TableName() string {
	return "data_sources"
}

// TableName returns the table name for Dashboard model
func (Dashboard) TableName() string {
	return "dashboards"
}

// TableName returns the table name for Report model
func (Report) TableName() string {
	return "reports"
}

// TableName returns the table name for KPI model
func (KPI) TableName() string {
	return "kpis"
}

// TableName returns the table name for DataMining model
func (DataMining) TableName() string {
	return "data_mining"
}
