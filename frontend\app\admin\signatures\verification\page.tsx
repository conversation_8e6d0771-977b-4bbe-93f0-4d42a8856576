'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  MagnifyingGlassIcon,
  EyeIcon,
  TrashIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  XCircleIcon,
  CheckCircleIcon,
  DocumentIcon,
  UserIcon,
  ClockIcon,
  FunnelIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface SignatureVerification {
  id: number;
  workflow_id: number;
  workflow_name: string;
  document_id: number;
  document_name: string;
  signer_id: number;
  signer_name: string;
  signer_email: string;
  signature_hash: string;
  verification_status: 'valid' | 'invalid' | 'expired' | 'revoked' | 'pending';
  verification_method: 'digital_certificate' | 'biometric' | 'otp' | 'email' | 'sms';
  signed_at: string;
  verified_at: string;
  ip_address: string;
  user_agent: string;
  certificate_info: any;
  trust_level: 'high' | 'medium' | 'low';
  created_at: string;
}

const SignatureVerificationPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [verifications, setVerifications] = useState<SignatureVerification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [trustFilter, setTrustFilter] = useState('');
  const [methodFilter, setMethodFilter] = useState('');

  useEffect(() => {
    fetchVerifications();
  }, []);

  const fetchVerifications = async () => {
    try {
      setLoading(true);

      // Fetch real signature verification data from API
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found');
        return;
      }

      // Fetch digital signatures with validation data
      const signaturesResponse = await fetch('/api/v1/digital-signatures', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!signaturesResponse.ok) {
        throw new Error(`Failed to fetch signatures: ${signaturesResponse.status}`);
      }

      const signaturesData = await signaturesResponse.json();
      const signatures = signaturesData.data || [];

      // Transform API data to match our interface
      const verifications: SignatureVerification[] = await Promise.all(
        signatures.map(async (sig: any) => {
          // Fetch additional validation details for each signature
          let validationData = null;
          try {
            const validationResponse = await fetch(`/api/v1/signatures/${sig.signature_id}/validate`, {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            });

            if (validationResponse.ok) {
              const validationResult = await validationResponse.json();
              validationData = validationResult.data;
            }
          } catch (error) {
            console.warn(`Failed to fetch validation for signature ${sig.signature_id}:`, error);
          }

          // Fetch certificate information if available
          let certificateInfo = null;
          if (sig.certificate_id) {
            try {
              const certResponse = await fetch(`/api/v1/certificates/${sig.certificate_id}`, {
                headers: {
                  'Authorization': `Bearer ${token}`,
                  'Content-Type': 'application/json'
                }
              });

              if (certResponse.ok) {
                const certData = await certResponse.json();
                const cert = certData.data;
                certificateInfo = {
                  issuer: cert.issuer || 'Unknown',
                  serial_number: cert.serial_number || 'Unknown',
                  valid_from: cert.not_before || cert.created_at,
                  valid_to: cert.not_after || 'Unknown',
                  algorithm: cert.signature_algorithm || 'Unknown'
                };
              }
            } catch (error) {
              console.warn(`Failed to fetch certificate ${sig.certificate_id}:`, error);
            }
          }

          return {
            id: sig.id,
            workflow_id: sig.workflow_id || null,
            workflow_name: sig.workflow_name || 'Direct Signature',
            document_id: sig.document_id,
            document_name: sig.document?.title || `Document ${sig.document_id}`,
            signer_id: sig.signer_id,
            signer_name: sig.signer_name,
            signer_email: sig.signer_email,
            signature_hash: sig.signature_data ? `sha256:${sig.signature_data.substring(0, 16)}...` : 'N/A',
            verification_status: sig.validation_status || (sig.is_valid ? 'valid' : 'invalid'),
            verification_method: sig.signature_method || 'digital_certificate',
            signed_at: sig.signed_at || sig.created_at,
            verified_at: sig.validated_at || sig.updated_at,
            ip_address: sig.ip_address || 'Unknown',
            user_agent: sig.user_agent || 'Unknown',
            certificate_info: certificateInfo || {
              issuer: 'Unknown',
              serial_number: 'Unknown',
              valid_from: 'Unknown',
              valid_to: 'Unknown',
              algorithm: 'Unknown'
            },
            trust_level: sig.is_valid ? 'high' : 'low',
            created_at: sig.created_at
          };
        })
      );

      setVerifications(verifications);
    } catch (err: any) {
      console.error('Error fetching signature verifications:', err);
      setError(err.message || 'Failed to fetch signature verifications');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this verification record?')) return;
    
    try {
      // await apiService.deleteSignatureVerification(id);
      setVerifications(verifications.filter(verification => verification.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete verification record');
    }
  };

  const handleExportVerifications = async () => {
    try {
      // await apiService.exportSignatureVerifications(filteredVerifications.map(v => v.id));
      alert('Signature verifications exported successfully!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to export verifications');
    }
  };

  const filteredVerifications = verifications.filter(verification => {
    const matchesSearch = verification.signer_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         verification.document_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         verification.workflow_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = !statusFilter || verification.verification_status === statusFilter;
    const matchesTrust = !trustFilter || verification.trust_level === trustFilter;
    const matchesMethod = !methodFilter || verification.verification_method === methodFilter;
    
    return matchesSearch && matchesStatus && matchesTrust && matchesMethod;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'bg-green-100 text-green-800';
      case 'invalid':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-yellow-100 text-yellow-800';
      case 'revoked':
        return 'bg-red-200 text-red-900';
      case 'pending':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valid':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'invalid':
      case 'revoked':
        return <XCircleIcon className="h-4 w-4" />;
      case 'expired':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'pending':
        return <ClockIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const getTrustLevelColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'bg-green-100 text-green-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'digital_certificate':
        return 'bg-blue-100 text-blue-800';
      case 'biometric':
        return 'bg-purple-100 text-purple-800';
      case 'otp':
        return 'bg-green-100 text-green-800';
      case 'email':
        return 'bg-orange-100 text-orange-800';
      case 'sms':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view signature verifications.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Signature Verification</h1>
              <p className="text-gray-600 mt-1">Monitor and verify digital signature authenticity and integrity</p>
            </div>
            <button
              onClick={handleExportVerifications}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
              Export Verifications
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 grid grid-cols-1 md:grid-cols-5 gap-4">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search verifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>

          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Statuses</option>
            <option value="valid">Valid</option>
            <option value="invalid">Invalid</option>
            <option value="expired">Expired</option>
            <option value="revoked">Revoked</option>
            <option value="pending">Pending</option>
          </select>

          <select
            value={trustFilter}
            onChange={(e) => setTrustFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Trust Levels</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>

          <select
            value={methodFilter}
            onChange={(e) => setMethodFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="">All Methods</option>
            <option value="digital_certificate">Digital Certificate</option>
            <option value="biometric">Biometric</option>
            <option value="otp">OTP</option>
            <option value="email">Email</option>
            <option value="sms">SMS</option>
          </select>

          <div className="flex items-center text-sm text-gray-600">
            <FunnelIcon className="h-4 w-4 mr-1" />
            {filteredVerifications.length} of {verifications.length} records
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading signature verifications...</p>
          </div>
        ) : (
          /* Verifications Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Signer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Verification Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Method
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trust Level
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Signed At
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    IP Address
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredVerifications.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                      No signature verifications found matching the current filters.
                    </td>
                  </tr>
                ) : (
                  filteredVerifications.map((verification) => (
                    <tr key={verification.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <UserIcon className="h-8 w-8 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{verification.signer_name}</div>
                            <div className="text-sm text-gray-500">{verification.signer_email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <DocumentIcon className="h-4 w-4 text-gray-400 mr-2" />
                          <div>
                            <Link
                              href={`/documents/${verification.document_id}`}
                              className="text-sm font-medium text-primary-600 hover:text-primary-500 truncate max-w-xs"
                            >
                              {verification.document_name}
                            </Link>
                            <div className="text-sm text-gray-500">
                              <Link
                                href={`/admin/signatures/workflows/${verification.workflow_id}`}
                                className="text-primary-600 hover:text-primary-500"
                              >
                                {verification.workflow_name}
                              </Link>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(verification.verification_status)}`}>
                          {getStatusIcon(verification.verification_status)}
                          <span className="ml-1">{verification.verification_status}</span>
                        </span>
                        {verification.verified_at && (
                          <div className="text-xs text-gray-500 mt-1">
                            Verified: {formatDate(verification.verified_at)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMethodColor(verification.verification_method)}`}>
                          {verification.verification_method.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTrustLevelColor(verification.trust_level)}`}>
                          <ShieldCheckIcon className="h-3 w-3 mr-1" />
                          {verification.trust_level}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(verification.signed_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{verification.ip_address}</div>
                        <div className="text-xs text-gray-500 truncate max-w-xs" title={verification.user_agent}>
                          {verification.user_agent.split(' ')[0]}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/signatures/verification/${verification.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                            title="View Verification Details"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(verification.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Verification Record"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SignatureVerificationPage;
