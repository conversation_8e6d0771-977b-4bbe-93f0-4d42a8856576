
'use client'

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  CalendarIcon,
  UserIcon,
  DocumentTextIcon,
  PencilIcon,
  TrashIcon,
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  BuildingOfficeIcon,
  TagIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import MarkdownRenderer from '../../components/MarkdownRenderer/MarkdownRenderer';
import { TaskPerformanceGauge, TaskPerformanceHistoryComponent as TaskPerformanceHistory } from '../../components/Tasks/TaskPerformanceIndicator';
import { Task, TaskPerformanceHistory as TaskPerformanceHistoryType } from '../../types';

const TaskDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [task, setTask] = useState<Task | null>(null);
  const [comments, setComments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [newComment, setNewComment] = useState('');
  const [submittingComment, setSubmittingComment] = useState(false);
  const [performanceHistory, setPerformanceHistory] = useState<TaskPerformanceHistoryType[]>([]);
  const [loadingPerformance, setLoadingPerformance] = useState(false);

  const taskId = params?.id as string;

  useEffect(() => {
    if (!taskId) return;

    const fetchTask = async () => {
      try {
        setLoading(true);
        const response = await apiService.getTask(parseInt(taskId));
        setTask(response);

        // Fetch task comments if user is authenticated
        if (isAuthenticated) {
          try {
            const commentsResponse = await apiService.getTaskComments(parseInt(taskId));
            setComments(commentsResponse.data || []);
          } catch (err) {
            console.log('Comments not available for this task');
          }

          // Fetch performance history
          try {
            const performanceResponse = await apiService.getTaskPerformanceHistory(parseInt(taskId));
            setPerformanceHistory(performanceResponse.data || []);
          } catch (err) {
            console.log('Performance history not available for this task');
          }
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch task');
      } finally {
        setLoading(false);
      }
    };

    fetchTask();
  }, [taskId, isAuthenticated]);

  const handleDelete = async () => {
    if (!task || !confirm('Are you sure you want to delete this task?')) return;
    
    try {
      await apiService.deleteTask(task.id);
      router.push('/tasks');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete task');
    }
  };

  const handleCompleteTask = async () => {
    if (!task) return;

    try {
      await apiService.completeTask(task.id);
      // Refresh task data
      const response = await apiService.getTask(task.id);
      setTask(response);

      // Refresh performance history after completion
      try {
        const performanceResponse = await apiService.getTaskPerformanceHistory(task.id);
        setPerformanceHistory(performanceResponse.data || []);
      } catch (err) {
        console.log('Failed to refresh performance history');
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to complete task');
    }
  };

  const handleRecalculatePerformance = async () => {
    if (!task) return;

    try {
      setLoadingPerformance(true);
      await apiService.calculateTaskPerformance(task.id);

      // Refresh task data
      const response = await apiService.getTask(task.id);
      setTask(response);

      // Refresh performance history
      const performanceResponse = await apiService.getTaskPerformanceHistory(task.id);
      setPerformanceHistory(performanceResponse.data || []);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to recalculate performance');
    } finally {
      setLoadingPerformance(false);
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || !task) return;

    try {
      setSubmittingComment(true);
      await apiService.createTaskComment(task.id, {
        content: newComment,
        comment_type: 'internal'
      });
      
      // Refresh comments
      const commentsResponse = await apiService.getTaskComments(task.id);
      setComments(commentsResponse.data || []);
      setNewComment('');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to submit comment');
    } finally {
      setSubmittingComment(false);
    }
  };

  const canEdit = () => {
    if (!user || !task) return false;
    return user.role === 'admin' || user.role === 'editor' || task.created_by_id === user.id || task.assigned_to_id === user.id;
  };

  const canDelete = () => {
    if (!user || !task) return false;
    return user.role === 'admin' || task.created_by_id === user.id;
  };

  const canComplete = () => {
    if (!user || !task) return false;
    return task.status !== 'completed' && (user.role === 'admin' || user.role === 'editor' || task.assigned_to_id === user.id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'on_hold': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'review': return 'bg-blue-100 text-blue-800';
      case 'approval': return 'bg-purple-100 text-purple-800';
      case 'publication': return 'bg-green-100 text-green-800';
      case 'meeting': return 'bg-orange-100 text-orange-800';
      case 'deadline': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !task) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Task not found</h3>
            <p className="text-gray-600 mb-4">{error || 'The requested task could not be found.'}</p>
            <Link
              href="/tasks"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Tasks
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/tasks"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Tasks
          </Link>
          
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-3">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                  {task.status?.toUpperCase()}
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                  {task.priority?.toUpperCase()}
                </span>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(task.type)}`}>
                  {task.type?.replace('_', ' ').toUpperCase()}
                </span>
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{task.title}</h1>
              
              {task.description && (
                <p className="text-lg text-gray-600 mb-6">{task.description}</p>
              )}
            </div>
            
            {/* Actions */}
            <div className="flex items-center space-x-2 mt-4 lg:mt-0">
              {canComplete() && (
                <button
                  onClick={handleCompleteTask}
                  className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                  title="Mark as Complete"
                >
                  <CheckCircleIcon className="h-5 w-5" />
                </button>
              )}
              {canEdit() && (
                <Link
                  href={`/tasks/${task.id}/edit`}
                  className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                  title="Edit Task"
                >
                  <PencilIcon className="h-5 w-5" />
                </Link>
              )}
              {canDelete() && (
                <button
                  onClick={handleDelete}
                  className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                  title="Delete Task"
                >
                  <TrashIcon className="h-5 w-5" />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Task Details */}
            {task.notes && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Notes</h2>
                <MarkdownRenderer
                  content={task.notes}
                  className="prose prose-lg max-w-none"
                />
              </div>
            )}

            {/* Comments Section */}
            {isAuthenticated && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">
                  Comments ({comments.length})
                </h2>
                
                {/* Comment Form */}
                <form onSubmit={handleSubmitComment} className="mb-6">
                  <textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Add a comment..."
                    required
                  />
                  <div className="mt-3 flex justify-end">
                    <button
                      type="submit"
                      disabled={submittingComment}
                      className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:opacity-50"
                    >
                      {submittingComment ? 'Adding...' : 'Add Comment'}
                    </button>
                  </div>
                </form>

                {/* Comments List */}
                <div className="space-y-4">
                  {comments.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No comments yet.</p>
                  ) : (
                    comments.map((comment) => (
                      <div key={comment.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <UserIcon className="h-4 w-4 text-gray-400 mr-2" />
                            <span className="text-sm font-medium text-gray-900">
                              {comment.author?.first_name} {comment.author?.last_name}
                            </span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {new Date(comment.created_at).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-gray-700">{comment.content}</p>
                      </div>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Performance Evaluation */}
            {task.status === 'completed' && (
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Performance</h3>
                  {canEdit() && (
                    <button
                      onClick={handleRecalculatePerformance}
                      disabled={loadingPerformance}
                      className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
                      title="Recalculate Performance"
                    >
                      {loadingPerformance ? 'Calculating...' : 'Recalculate'}
                    </button>
                  )}
                </div>
                <TaskPerformanceGauge
                  percentage={task.performance_percentage || 0}
                  size={100}
                  className="mb-4"
                />
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Deadline Adherence:</span>
                    <span className="font-medium">{(task.deadline_adherence_score || 0).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Quality Score:</span>
                    <span className="font-medium">{(task.quality_score || 0).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Efficiency:</span>
                    <span className="font-medium">{(task.completion_efficiency || 0).toFixed(1)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Priority Handling:</span>
                    <span className="font-medium">{(task.priority_handling_score || 0).toFixed(1)}%</span>
                  </div>
                </div>
              </div>
            )}

            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Task Information</h3>
              
              <div className="space-y-4">
                {task.assigned_to && (
                  <div className="flex items-start">
                    <UserIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Assigned To</p>
                      <p className="text-sm text-gray-600">
                        {task.assigned_to.first_name} {task.assigned_to.last_name}
                      </p>
                    </div>
                  </div>
                )}

                {task.due_date && (
                  <div className="flex items-start">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Due Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(task.due_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {task.reminder_date && (
                  <div className="flex items-start">
                    <ClockIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Reminder Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(task.reminder_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {task.document && (
                  <div className="flex items-start">
                    <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Related Document</p>
                      <Link
                        href={`/documents/${parseInt(task.document.id.toString())}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {task.document.title}
                      </Link>
                    </div>
                  </div>
                )}

                {task.agency && (
                  <div className="flex items-start">
                    <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Agency</p>
                      <Link
                        href={`/agencies/${parseInt(task.agency.id.toString())}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {task.agency.name}
                      </Link>
                    </div>
                  </div>
                )}

                {task.category && (
                  <div className="flex items-start">
                    <TagIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Category</p>
                      <Link
                        href={`/categories/${parseInt(task.category.id.toString())}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {task.category.name}
                      </Link>
                    </div>
                  </div>
                )}

                {task.is_recurring && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Recurrence</p>
                    <p className="text-sm text-gray-600">{task.recurrence_pattern}</p>
                  </div>
                )}

                {task.created_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Created</p>
                    <p className="text-sm text-gray-600">
                      {new Date(task.created_at).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Tags */}
            {task.tags && (Array.isArray(task.tags) ? task.tags.length > 0 : task.tags.length > 0) && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {(Array.isArray(task.tags) ? task.tags : task.tags.split(',')).map((tag, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Performance History */}
            {isAuthenticated && task.status === 'completed' && performanceHistory.length > 0 && (
              <TaskPerformanceHistory
                history={performanceHistory}
                className="mt-8"
              />
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TaskDetailPage;
