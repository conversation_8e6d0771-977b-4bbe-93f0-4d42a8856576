# Deployment Guide

This guide covers deploying the Federal Register Clone application in various environments.

## Prerequisites

- <PERSON><PERSON> and Docker Compose
- PostgreSQL 15+
- Go 1.21+ (for local development)
- Node.js 18+ (for local development)
- Git

## Environment Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd federal-register-clone
```

### 2. Environment Configuration

Copy the example environment files and configure them:

```bash
# Backend configuration
cp backend/.env.example backend/.env

# Frontend configuration
cp frontend/.env.example frontend/.env.local
```

### 3. Configure Environment Variables

#### Backend (.env)
```bash
# Database
DB_HOST=postgres
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_secure_password
DB_NAME=federal_register_db
DB_SSLMODE=disable

# Server
SERVER_PORT=8080
SERVER_HOST=0.0.0.0
GIN_MODE=release

# JWT (IMPORTANT: Change in production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY_HOURS=24
JWT_REFRESH_EXPIRY_HOURS=168

# File Upload
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=50MB
ALLOWED_FILE_TYPES=pdf,doc,docx,txt

# CORS
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Environment
ENVIRONMENT=production
```

#### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_URL=https://api.yourdomain.com/api/v1
NEXT_PUBLIC_APP_NAME=Federal Register Clone
NEXT_PUBLIC_ENVIRONMENT=production
```

## Deployment Options

### Option 1: Docker Compose (Recommended)

This is the easiest way to deploy the entire stack.

#### 1. Production Docker Compose

Create a `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    restart: unless-stopped
    environment:
      POSTGRES_DB: federal_register_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app_network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    restart: unless-stopped
    environment:
      - DB_HOST=postgres
      - DB_PASSWORD=${DB_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - ENVIRONMENT=production
    volumes:
      - backend_uploads:/app/uploads
    depends_on:
      - postgres
    networks:
      - app_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NEXT_PUBLIC_API_URL=${API_URL}
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - app_network

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - frontend
      - backend
    networks:
      - app_network

volumes:
  postgres_data:
  backend_uploads:

networks:
  app_network:
    driver: bridge
```

#### 2. Deploy with Docker Compose

```bash
# Set environment variables
export DB_PASSWORD=your_secure_password
export JWT_SECRET=your_super_secret_jwt_key
export API_URL=https://api.yourdomain.com/api/v1

# Deploy
docker-compose -f docker-compose.prod.yml up -d
```

### Option 2: Kubernetes Deployment

#### 1. Create Kubernetes Manifests

**namespace.yaml**
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: federal-register
```

**postgres.yaml**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: federal-register
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: federal_register_db
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: federal-register
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
```

#### 2. Deploy to Kubernetes

```bash
kubectl apply -f k8s/
```

### Option 3: Cloud Deployment

#### AWS ECS with Fargate

1. **Build and push images to ECR**
```bash
# Build and tag images
docker build -t federal-register-backend ./backend
docker build -t federal-register-frontend ./frontend

# Tag for ECR
docker tag federal-register-backend:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/federal-register-backend:latest
docker tag federal-register-frontend:latest 123456789012.dkr.ecr.us-west-2.amazonaws.com/federal-register-frontend:latest

# Push to ECR
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/federal-register-backend:latest
docker push 123456789012.dkr.ecr.us-west-2.amazonaws.com/federal-register-frontend:latest
```

2. **Create ECS Task Definition**
3. **Set up Application Load Balancer**
4. **Configure RDS PostgreSQL instance**
5. **Deploy ECS Service**

#### Google Cloud Run

```bash
# Build and deploy backend
gcloud builds submit --tag gcr.io/PROJECT_ID/federal-register-backend ./backend
gcloud run deploy federal-register-backend --image gcr.io/PROJECT_ID/federal-register-backend --platform managed

# Build and deploy frontend
gcloud builds submit --tag gcr.io/PROJECT_ID/federal-register-frontend ./frontend
gcloud run deploy federal-register-frontend --image gcr.io/PROJECT_ID/federal-register-frontend --platform managed
```

## Database Setup

### 1. Database Migration

The application automatically runs migrations on startup. For manual migration:

```bash
# Using Docker
docker exec -it federal_register_backend ./main migrate

# Or using Go directly
cd backend
go run cmd/server/main.go migrate
```

### 2. Seed Data (Optional)

```bash
# Create sample data
docker exec -it federal_register_backend ./main seed
```

## SSL/TLS Configuration

### Using Let's Encrypt with Certbot

```bash
# Install certbot
sudo apt-get install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Nginx Configuration

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # Frontend
    location / {
        proxy_pass http://frontend:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Backend API
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Monitoring and Logging

### 1. Application Logs

```bash
# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# With specific service
docker logs federal_register_backend
```

### 2. Health Checks

The application includes health check endpoints:
- Backend: `GET /health`
- Frontend: `GET /api/health`

### 3. Monitoring Setup

Consider setting up:
- **Prometheus** for metrics collection
- **Grafana** for visualization
- **ELK Stack** for log aggregation
- **Uptime monitoring** services

## Backup and Recovery

### Database Backup

```bash
# Create backup
docker exec postgres pg_dump -U postgres federal_register_db > backup.sql

# Restore backup
docker exec -i postgres psql -U postgres federal_register_db < backup.sql
```

### File Backup

```bash
# Backup uploaded files
docker cp federal_register_backend:/app/uploads ./uploads_backup
```

## Performance Optimization

### 1. Database Optimization

- Enable connection pooling
- Add appropriate indexes
- Regular VACUUM and ANALYZE
- Monitor slow queries

### 2. Caching

- Redis for session storage
- CDN for static assets
- Application-level caching

### 3. Load Balancing

For high-traffic deployments:
- Multiple backend instances
- Load balancer (Nginx, HAProxy, or cloud LB)
- Database read replicas

## Security Considerations

### 1. Environment Security

- Use strong, unique passwords
- Rotate JWT secrets regularly
- Enable firewall rules
- Regular security updates

### 2. Application Security

- Input validation
- SQL injection prevention
- XSS protection
- CSRF protection
- Rate limiting

### 3. Infrastructure Security

- VPC/network isolation
- Security groups/firewall rules
- Regular vulnerability scans
- Access logging

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials
   - Verify network connectivity
   - Check database service status

2. **File Upload Issues**
   - Check upload directory permissions
   - Verify file size limits
   - Check disk space

3. **Authentication Problems**
   - Verify JWT secret configuration
   - Check token expiration settings
   - Validate CORS settings

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Backend
GIN_MODE=debug

# Frontend
NODE_ENV=development
NEXT_PUBLIC_ENABLE_DEBUG=true
```

## Scaling

### Horizontal Scaling

- Multiple backend instances behind load balancer
- Database read replicas
- CDN for static content
- Microservices architecture (future)

### Vertical Scaling

- Increase CPU/memory resources
- Optimize database configuration
- Tune application settings

## Maintenance

### Regular Tasks

- Database maintenance (VACUUM, ANALYZE)
- Log rotation
- Security updates
- Backup verification
- Performance monitoring
- SSL certificate renewal

### Update Procedure

1. Backup database and files
2. Test updates in staging environment
3. Deploy during maintenance window
4. Verify functionality
5. Monitor for issues

For support during deployment, please refer to the troubleshooting section or contact the development team.
