package models

import (
	"time"

	"gorm.io/gorm"
)

// SummaryType represents the type of summary
type SummaryType string

const (
	SummaryTypeNews         SummaryType = "news"
	SummaryTypeUpdate       SummaryType = "update"
	SummaryTypeAnnouncement SummaryType = "announcement"
	SummaryTypeAlert        SummaryType = "alert"
)

// EntityType represents the type of entity the summary is about
type EntityType string

const (
	EntityTypeDocument   EntityType = "document"
	EntityTypeRegulation EntityType = "regulation"
	EntityTypeAgency     EntityType = "agency"
	EntityTypeCategory   EntityType = "category"
	EntityTypeTask       EntityType = "task"
)

// ActionType represents the action that triggered the summary
type ActionType string

const (
	ActionTypeCreate  ActionType = "create"
	ActionTypeUpdate  ActionType = "update"
	ActionTypeDelete  ActionType = "delete"
	ActionTypePublish ActionType = "publish"
	ActionTypeArchive ActionType = "archive"
)

// Summary represents a news-like summary of system activities
type Summary struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Basic summary information
	Title       string      `json:"title" gorm:"not null"`
	Content     string      `json:"content" gorm:"type:text;not null"`
	Abstract    string      `json:"abstract" gorm:"type:text"`
	SummaryType SummaryType `json:"summary_type" gorm:"not null"`

	// Entity information
	EntityType EntityType `json:"entity_type" gorm:"not null"`
	EntityID   uint       `json:"entity_id" gorm:"not null"`
	ActionType ActionType `json:"action_type" gorm:"not null"`

	// Publication information
	PublicationDate *time.Time `json:"publication_date"`
	IsPublic        bool       `json:"is_public" gorm:"default:true"`
	IsFeatured      bool       `json:"is_featured" gorm:"default:false"`

	// Metadata
	ViewCount    int64  `json:"view_count" gorm:"default:0"`
	Priority     int    `json:"priority" gorm:"default:0"` // Higher number = higher priority
	Tags         string `json:"tags"`                      // Comma-separated tags
	ExternalLink string `json:"external_link"`             // Optional link to external resource

	// Relationships
	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`

	// Optional agency relationship for context
	AgencyID *uint   `json:"agency_id"`
	Agency   *Agency `json:"agency,omitempty" gorm:"foreignKey:AgencyID"`

	// Optional category relationship for context
	CategoryID *uint     `json:"category_id"`
	Category   *Category `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
}

// TableName returns the table name for the Summary model
func (Summary) TableName() string {
	return "summaries"
}

// GetEntityName returns a human-readable name for the entity
func (s *Summary) GetEntityName() string {
	switch s.EntityType {
	case EntityTypeDocument:
		return "Document"
	case EntityTypeRegulation:
		return "Regulation"
	case EntityTypeAgency:
		return "Agency"
	case EntityTypeCategory:
		return "Category"
	case EntityTypeTask:
		return "Task"
	default:
		return "Unknown"
	}
}

// GetActionName returns a human-readable name for the action
func (s *Summary) GetActionName() string {
	switch s.ActionType {
	case ActionTypeCreate:
		return "Created"
	case ActionTypeUpdate:
		return "Updated"
	case ActionTypeDelete:
		return "Deleted"
	case ActionTypePublish:
		return "Published"
	case ActionTypeArchive:
		return "Archived"
	default:
		return "Modified"
	}
}

// IsEffective returns true if the summary should be displayed
func (s *Summary) IsEffective() bool {
	if !s.IsPublic {
		return false
	}
	if s.PublicationDate != nil && s.PublicationDate.After(time.Now()) {
		return false
	}
	return true
}
