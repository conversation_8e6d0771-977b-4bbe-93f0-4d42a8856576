package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// GeneralSearch performs a general search across multiple entity types
func GeneralSearch(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.J<PERSON>(http.StatusBadRequest, ErrorResponse{
			Error:   "Missing search query",
			Message: "Query parameter 'q' is required",
		})
		return
	}

	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.<PERSON>("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Parse entity type filter
	entityType := c.Query("type")

	// Get database connection
	db := database.GetDB()
	if db == nil {
		c.<PERSON>(http.StatusInternalServerError, ErrorResponse{
			Error:   "Database not available",
			Message: "Database connection is not initialized",
		})
		return
	}

	searchQuery := "%" + query + "%"
	results := gin.H{
		"query":   query,
		"results": []gin.H{},
		"total":   0,
	}

	// Search documents if no specific type or if type is "documents"
	if entityType == "" || entityType == "documents" {
		var documents []models.Document
		var docCount int64

		docQuery := db.Model(&models.Document{}).
			Preload("Agency").
			Where("is_public = ? AND visibility_level = ?", true, 1).
			Where("title ILIKE ? OR abstract ILIKE ? OR content ILIKE ?", searchQuery, searchQuery, searchQuery)

		docQuery.Count(&docCount)

		if err := docQuery.Order("publication_date DESC, created_at DESC").
			Limit(perPage).
			Find(&documents).Error; err == nil {

			documentResults := make([]gin.H, len(documents))
			for i, doc := range documents {
				documentResults[i] = gin.H{
					"type":             "document",
					"id":               doc.ID,
					"title":            doc.Title,
					"slug":             doc.Slug,
					"abstract":         doc.Abstract,
					"publication_date": doc.PublicationDate,
					"agency": gin.H{
						"id":   doc.Agency.ID,
						"name": doc.Agency.Name,
					},
				}
			}

			results["results"] = documentResults
			results["total"] = docCount
		}
	}

	// Search agencies if no specific type or if type is "agencies"
	if entityType == "" || entityType == "agencies" {
		var agencies []models.Agency
		var agencyCount int64

		agencyQuery := db.Model(&models.Agency{}).
			Where("is_active = ?", true).
			Where("name ILIKE ? OR description ILIKE ?", searchQuery, searchQuery)

		agencyQuery.Count(&agencyCount)

		if err := agencyQuery.Order("name ASC").
			Limit(perPage).
			Find(&agencies).Error; err == nil {

			agencyResults := make([]gin.H, len(agencies))
			for i, agency := range agencies {
				agencyResults[i] = gin.H{
					"type":        "agency",
					"id":          agency.ID,
					"name":        agency.Name,
					"slug":        agency.Slug,
					"description": agency.Description,
					"website":     agency.Website,
				}
			}

			// If searching all types, combine results
			if entityType == "" {
				existingResults := results["results"].([]gin.H)
				results["results"] = append(existingResults, agencyResults...)
				results["total"] = results["total"].(int64) + agencyCount
			} else {
				results["results"] = agencyResults
				results["total"] = agencyCount
			}
		}
	}

	// Search categories if no specific type or if type is "categories"
	if entityType == "" || entityType == "categories" {
		var categories []models.Category
		var categoryCount int64

		categoryQuery := db.Model(&models.Category{}).
			Where("is_active = ?", true).
			Where("name ILIKE ? OR description ILIKE ?", searchQuery, searchQuery)

		categoryQuery.Count(&categoryCount)

		if err := categoryQuery.Order("name ASC").
			Limit(perPage).
			Find(&categories).Error; err == nil {

			categoryResults := make([]gin.H, len(categories))
			for i, category := range categories {
				categoryResults[i] = gin.H{
					"type":        "category",
					"id":          category.ID,
					"name":        category.Name,
					"slug":        category.Slug,
					"description": category.Description,
					"color":       category.Color,
				}
			}

			// If searching all types, combine results
			if entityType == "" {
				existingResults := results["results"].([]gin.H)
				results["results"] = append(existingResults, categoryResults...)
				results["total"] = results["total"].(int64) + categoryCount
			} else {
				results["results"] = categoryResults
				results["total"] = categoryCount
			}
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Search completed successfully",
		Data:    results,
	})
}
