-- Indexes and Constraints for Federal Register Clone
-- Performance and search optimization

-- Add foreign key constraints that were simplified
ALTER TABLE categories ADD CONSTRAINT fk_categories_parent 
    FOREIGN KEY (parent_category_id) REFERENCES categories(id);

ALTER TABLE subjects ADD CONSTRAINT fk_subjects_parent 
    FOREIGN KEY (parent_subject_id) REFERENCES subjects(id);

ALTER TABLE documents ADD CONSTRAINT fk_documents_parent 
    FOREIGN KEY (parent_document_id) REFERENCES documents(id);

-- Create indexes for better performance

-- Agency indexes
CREATE INDEX idx_agencies_slug ON agencies(slug);
CREATE INDEX idx_agencies_short_name ON agencies(short_name);
CREATE INDEX idx_agencies_is_active ON agencies(is_active);
CREATE INDEX idx_agencies_parent_id ON agencies(parent_agency_id);
CREATE INDEX idx_agencies_deleted_at ON agencies(deleted_at);

-- Agency contact indexes
CREATE INDEX idx_agency_contacts_agency_id ON agency_contacts(agency_id);
CREATE INDEX idx_agency_contacts_contact_type ON agency_contacts(contact_type);
CREATE INDEX idx_agency_contacts_is_public ON agency_contacts(is_public);
CREATE INDEX idx_agency_contacts_is_primary ON agency_contacts(is_primary);
CREATE INDEX idx_agency_contacts_deleted_at ON agency_contacts(deleted_at);

-- User indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_agency_id ON users(agency_id);
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

-- User session indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_token ON user_sessions(token);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_deleted_at ON user_sessions(deleted_at);

-- Category indexes
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_parent_id ON categories(parent_category_id);
CREATE INDEX idx_categories_is_active ON categories(is_active);
CREATE INDEX idx_categories_deleted_at ON categories(deleted_at);

-- Tag indexes
CREATE INDEX idx_tags_slug ON tags(slug);
CREATE INDEX idx_tags_name ON tags(name);
CREATE INDEX idx_tags_usage_count ON tags(usage_count);
CREATE INDEX idx_tags_created_by_id ON tags(created_by_id);
CREATE INDEX idx_tags_deleted_at ON tags(deleted_at);

-- Subject indexes
CREATE INDEX idx_subjects_slug ON subjects(slug);
CREATE INDEX idx_subjects_name ON subjects(name);
CREATE INDEX idx_subjects_parent_id ON subjects(parent_subject_id);
CREATE INDEX idx_subjects_deleted_at ON subjects(deleted_at);

-- Document indexes (most important for performance)
CREATE INDEX idx_documents_slug ON documents(slug);
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_type ON documents(type);
CREATE INDEX idx_documents_agency_id ON documents(agency_id);
CREATE INDEX idx_documents_created_by_id ON documents(created_by_id);
CREATE INDEX idx_documents_publication_date ON documents(publication_date);
CREATE INDEX idx_documents_effective_date ON documents(effective_date);
CREATE INDEX idx_documents_created_at ON documents(created_at);
CREATE INDEX idx_documents_fr_document_number ON documents(fr_document_number);
CREATE INDEX idx_documents_workflow_stage ON documents(workflow_stage);
CREATE INDEX idx_documents_accepts_comments ON documents(accepts_comments);
CREATE INDEX idx_documents_deleted_at ON documents(deleted_at);

-- Full-text search indexes
CREATE INDEX idx_documents_search 
    ON documents USING gin(to_tsvector('english', title || ' ' || COALESCE(abstract, '') || ' ' || COALESCE(content, '')));

CREATE INDEX idx_documents_title_search 
    ON documents USING gin(to_tsvector('english', title));

CREATE INDEX idx_agencies_search 
    ON agencies USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

CREATE INDEX idx_categories_search 
    ON categories USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));

-- Junction table indexes
CREATE INDEX idx_doc_cat_assignments_document_id ON document_category_assignments(document_id);
CREATE INDEX idx_doc_cat_assignments_category_id ON document_category_assignments(category_id);

CREATE INDEX idx_doc_tag_assignments_document_id ON document_tag_assignments(document_id);
CREATE INDEX idx_doc_tag_assignments_tag_id ON document_tag_assignments(tag_id);

CREATE INDEX idx_doc_subject_assignments_document_id ON document_subject_assignments(document_id);
CREATE INDEX idx_doc_subject_assignments_subject_id ON document_subject_assignments(subject_id);

-- Update search vector trigger for documents
CREATE OR REPLACE FUNCTION update_document_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        NEW.title || ' ' || 
        COALESCE(NEW.abstract, '') || ' ' || 
        COALESCE(NEW.content, '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_document_search_vector
    BEFORE INSERT OR UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION update_document_search_vector();

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add updated_at triggers to all tables
CREATE TRIGGER trigger_agencies_updated_at
    BEFORE UPDATE ON agencies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_agency_contacts_updated_at
    BEFORE UPDATE ON agency_contacts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_sessions_updated_at
    BEFORE UPDATE ON user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_preferences_updated_at
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_categories_updated_at
    BEFORE UPDATE ON categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_tags_updated_at
    BEFORE UPDATE ON tags
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_subjects_updated_at
    BEFORE UPDATE ON subjects
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_documents_updated_at
    BEFORE UPDATE ON documents
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
