'use client';

import React from 'react';
import { Finance, FinanceTableData } from '../../types';
import {
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  ScaleIcon
} from '@heroicons/react/24/outline';

interface FinanceTableProps {
  data: Finance[] | FinanceTableData[];
  type: 'budget' | 'actual' | 'entities';
  onEdit?: (item: Finance | FinanceTableData) => void;
  onDelete?: (id: number) => void;
  canEdit?: boolean;
}

const FinanceTable: React.FC<FinanceTableProps> = ({
  data,
  type,
  onEdit,
  onDelete,
  canEdit = false
}) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number) => {
    return `${percentage.toFixed(1)}%`;
  };

  const getPerformanceColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 bg-green-100';
    if (percentage >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const renderBudgetTable = (finances: Finance[]) => (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Entity
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Category
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Amount
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Performance
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Year
            </th>
            {canEdit && (
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            )}
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {finances.map((finance) => (
            <tr key={finance.id} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {finance.description || 'No description'}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="flex items-center">
                  {finance.document ? (
                    <>
                      <DocumentTextIcon className="h-4 w-4 text-blue-500 mr-2" />
                      <span>{finance.document.title}</span>
                    </>
                  ) : finance.regulation ? (
                    <>
                      <ScaleIcon className="h-4 w-4 text-purple-500 mr-2" />
                      <span>{finance.regulation.title}</span>
                    </>
                  ) : (
                    <span className="text-gray-400">No entity linked</span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {finance.category ? (
                  <span
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    style={{
                      backgroundColor: finance.category.color + '20',
                      color: finance.category.color
                    }}
                  >
                    {finance.category.name}
                  </span>
                ) : (
                  <span className="text-gray-400">Uncategorized</span>
                )}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {formatCurrency(finance.amount)}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPerformanceColor(finance.performance_percentage)}`}>
                  {formatPercentage(finance.performance_percentage)}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {finance.year}
              </td>
              {canEdit && (
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => onEdit?.(finance)}
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => onDelete?.(finance.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              )}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  const renderEntityTable = (entities: FinanceTableData[]) => (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Entity
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Type
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Category
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Original Budget
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actual Budget
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Performance
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Variance
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {entities.map((entity) => {
            const variance = entity.original_budget - entity.actual_budget;
            const variancePercentage = entity.original_budget > 0 
              ? (variance / entity.original_budget) * 100 
              : 0;

            return (
              <tr key={`${entity.type}-${entity.entity_id}`} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div className="flex items-center">
                    {entity.type === 'document' ? (
                      <DocumentTextIcon className="h-4 w-4 text-blue-500 mr-2" />
                    ) : (
                      <ScaleIcon className="h-4 w-4 text-purple-500 mr-2" />
                    )}
                    <span className="font-medium">{entity.title}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    entity.type === 'document' 
                      ? 'text-blue-800 bg-blue-100' 
                      : 'text-purple-800 bg-purple-100'
                  }`}>
                    {entity.type === 'document' ? 'Document' : 'Regulation'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    style={{
                      backgroundColor: entity.category_color + '20',
                      color: entity.category_color
                    }}
                  >
                    {entity.category}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatCurrency(entity.original_budget)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {formatCurrency(entity.actual_budget)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPerformanceColor(entity.performance_percentage)}`}>
                    {formatPercentage(entity.performance_percentage)}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div className="flex flex-col">
                    <span className={`font-medium ${variance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {variance >= 0 ? '+' : ''}{formatCurrency(variance)}
                    </span>
                    <span className={`text-xs ${variancePercentage >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                      ({variancePercentage >= 0 ? '+' : ''}{variancePercentage.toFixed(1)}%)
                    </span>
                  </div>
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">
          No {type === 'entities' ? 'entity finance data' : `${type} data`} found for the selected year.
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      {type === 'entities' 
        ? renderEntityTable(data as FinanceTableData[])
        : renderBudgetTable(data as Finance[])
      }
    </div>
  );
};

export default FinanceTable;
