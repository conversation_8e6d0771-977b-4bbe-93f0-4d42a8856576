package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

func testSummariesMain() {
	// Get fresh token by logging in
	loginData := map[string]interface{}{
		"identifier": "<EMAIL>",
		"password":   "password",
	}

	loginBody, _ := json.Marshal(loginData)
	loginReq, _ := http.NewRequest("POST", "http://127.0.0.1:8080/api/v1/auth/login", bytes.NewBuffer(loginBody))
	loginReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	loginResp, err := client.Do(loginReq)
	if err != nil {
		fmt.Printf("Error logging in: %v\n", err)
		return
	}
	defer loginResp.Body.Close()

	loginRespBody, _ := io.ReadAll(loginResp.Body)
	if loginResp.StatusCode != 200 {
		fmt.Printf("Login failed: %s\n", string(loginRespBody))
		return
	}

	fmt.Printf("Login response: %s\n", string(loginRespBody))

	var loginResult map[string]interface{}
	json.Unmarshal(loginRespBody, &loginResult)

	var token string
	// Try direct token field first
	if tokenStr, ok := loginResult["token"].(string); ok {
		token = tokenStr
	} else if data, ok := loginResult["data"].(map[string]interface{}); ok {
		if tokenStr, ok := data["token"].(string); ok {
			token = tokenStr
		}
	}

	if token == "" {
		fmt.Println("Could not get token from login response")
		fmt.Printf("Response structure: %+v\n", loginResult)
		return
	}

	fmt.Printf("Got fresh token: %s...\n", token[:20])

	// First, create a summary to test update
	createData := map[string]interface{}{
		"title":        "Test Summary for Update",
		"content":      "This is test content",
		"summary_type": "news",
		"entity_type":  "document",
		"entity_id":    1,
		"action_type":  "create",
		"is_public":    true,
		"is_featured":  false,
	}

	createBody, _ := json.Marshal(createData)
	createReq, _ := http.NewRequest("POST", "http://127.0.0.1:8080/api/v1/summaries", bytes.NewBuffer(createBody))
	createReq.Header.Set("Authorization", "Bearer "+token)
	createReq.Header.Set("Content-Type", "application/json")

	createResp, err := client.Do(createReq)
	if err != nil {
		fmt.Printf("Error creating summary: %v\n", err)
		return
	}
	defer createResp.Body.Close()

	createRespBody, _ := io.ReadAll(createResp.Body)
	fmt.Printf("CREATE Response Status: %d\n", createResp.StatusCode)
	fmt.Printf("CREATE Response Body: %s\n", string(createRespBody))

	if createResp.StatusCode != 201 {
		fmt.Println("Failed to create summary, cannot test update")
		return
	}

	// Parse the response to get the ID
	var createResult map[string]interface{}
	json.Unmarshal(createRespBody, &createResult)

	var summaryID float64
	if data, ok := createResult["data"].(map[string]interface{}); ok {
		if id, ok := data["id"].(float64); ok {
			summaryID = id
		}
	}

	if summaryID == 0 {
		fmt.Println("Could not get summary ID from create response")
		return
	}

	fmt.Printf("Created summary with ID: %.0f\n", summaryID)

	// Now test the update
	updateData := map[string]interface{}{
		"title":       "Updated Test Summary",
		"is_featured": true,
	}

	updateBody, _ := json.Marshal(updateData)
	updateURL := fmt.Sprintf("http://127.0.0.1:8080/api/v1/summaries/%.0f", summaryID)
	updateReq, _ := http.NewRequest("PUT", updateURL, bytes.NewBuffer(updateBody))
	updateReq.Header.Set("Authorization", "Bearer "+token)
	updateReq.Header.Set("Content-Type", "application/json")

	updateResp, err := client.Do(updateReq)
	if err != nil {
		fmt.Printf("Error updating summary: %v\n", err)
		return
	}
	defer updateResp.Body.Close()

	updateRespBody, _ := io.ReadAll(updateResp.Body)
	fmt.Printf("UPDATE Response Status: %d\n", updateResp.StatusCode)
	fmt.Printf("UPDATE Response Body: %s\n", string(updateRespBody))
}
