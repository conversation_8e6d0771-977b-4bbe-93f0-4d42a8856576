'use client';

import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import financeApi from '../../services/financeApi';
import {
  CreatePerformanceRequest,
  Document,
  LawsAndRules,
  User
} from '../../types';

interface PerformanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  documentId?: number;
  regulationId?: number;
  year: number;
  currentUser?: User;
}

const PerformanceModal: React.FC<PerformanceModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  documentId,
  regulationId,
  year,
  currentUser
}) => {
  const [formData, setFormData] = useState<CreatePerformanceRequest>({
    document_id: documentId,
    regulation_id: regulationId,
    year,
    performance_percentage: 100,
    performance_notes: '',
    evaluated_by_id: currentUser?.id
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      setFormData({
        document_id: documentId,
        regulation_id: regulationId,
        year,
        performance_percentage: 100,
        performance_notes: '',
        evaluated_by_id: currentUser?.id
      });
      setError(null);
    }
  }, [isOpen, documentId, regulationId, year, currentUser]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.document_id && !formData.regulation_id) {
      setError('Either document or regulation must be selected');
      return;
    }

    if (formData.performance_percentage < 0 || formData.performance_percentage > 200) {
      setError('Performance percentage must be between 0% and 200%');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      await financeApi.createPerformance(formData);
      onSuccess();
      onClose();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to create performance record');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'performance_percentage' ? parseFloat(value) || 0 : value
    }));
  };

  const getPerformanceLevel = (percentage: number) => {
    if (percentage >= 80) return { level: 'Excellent', color: 'text-green-600' };
    if (percentage >= 60) return { level: 'Good', color: 'text-blue-600' };
    if (percentage >= 40) return { level: 'Fair', color: 'text-yellow-600' };
    return { level: 'Poor', color: 'text-red-600' };
  };

  const performanceInfo = getPerformanceLevel(formData.performance_percentage);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Set Performance Evaluation
              </h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Error message */}
            {error && (
              <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                <div className="text-red-800 text-sm">{error}</div>
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Year (read-only) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Year
                </label>
                <input
                  type="number"
                  value={year}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                />
              </div>

              {/* Performance Percentage */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Performance Percentage
                </label>
                <div className="relative">
                  <input
                    type="number"
                    name="performance_percentage"
                    value={formData.performance_percentage}
                    onChange={handleChange}
                    min="0"
                    max="200"
                    step="0.1"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <span className="text-gray-500 text-sm">%</span>
                  </div>
                </div>
                <div className="mt-1 flex items-center justify-between">
                  <span className={`text-sm font-medium ${performanceInfo.color}`}>
                    {performanceInfo.level}
                  </span>
                  <span className="text-xs text-gray-500">
                    0% - 200% range
                  </span>
                </div>
              </div>

              {/* Performance Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Performance Notes
                </label>
                <textarea
                  name="performance_notes"
                  value={formData.performance_notes}
                  onChange={handleChange}
                  rows={3}
                  placeholder="Add notes about the performance evaluation..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Impact Preview */}
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <h4 className="text-sm font-medium text-blue-900 mb-2">
                  Budget Impact Preview
                </h4>
                <p className="text-sm text-blue-700">
                  Setting performance to {formData.performance_percentage}% will automatically 
                  calculate actual budgets as {formData.performance_percentage}% of original budgets 
                  for this {documentId ? 'document' : 'regulation'} in {year}.
                </p>
              </div>

              {/* Action buttons */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-4 py-2 text-sm font-medium text-white bg-primary-600 border border-transparent rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Saving...' : 'Set Performance'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceModal;
