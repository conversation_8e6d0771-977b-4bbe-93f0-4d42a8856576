'use client';

import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../stores/authStore';
import Layout from '../components/Layout/Layout';
import FinanceTable from '../components/Finance/FinanceTable';
import FinanceModal from '../components/Finance/FinanceModal';
import PerformanceModal from '../components/Finance/PerformanceModal';
import financeApi from '../services/financeApi';
import {
  Finance,
  FinanceTableData,
  BudgetSummary,
  FinanceCategory
} from '../types';
import {
  CurrencyDollarIcon,
  ChartBarIcon,
  DocumentTextIcon,
  PlusIcon,
  CalendarIcon,
  ChartPieIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

const FinancePage: React.FC = () => {
  const { user, isAuthenticated } = useAuthStore();
  const [activeTab, setActiveTab] = useState<'budget' | 'actual' | 'entities'>('budget');
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Data states
  const [budgetFinances, setBudgetFinances] = useState<Finance[]>([]);
  const [actualFinances, setActualFinances] = useState<Finance[]>([]);
  const [entityFinances, setEntityFinances] = useState<FinanceTableData[]>([]);
  const [budgetSummary, setBudgetSummary] = useState<BudgetSummary | null>(null);
  const [categories, setCategories] = useState<FinanceCategory[]>([]);

  // Modal states
  const [financeModalOpen, setFinanceModalOpen] = useState(false);
  const [performanceModalOpen, setPerformanceModalOpen] = useState(false);
  const [editingFinance, setEditingFinance] = useState<Finance | undefined>();
  const [performanceTarget, setPerformanceTarget] = useState<{
    documentId?: number;
    regulationId?: number;
  }>({});

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 5 }, (_, i) => currentYear - 2 + i);

  useEffect(() => {
    if (isAuthenticated) {
      fetchFinanceData();
    }
  }, [isAuthenticated, selectedYear, activeTab]);

  const fetchFinanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch categories first with error handling
      try {
        const categoriesData = await financeApi.getFinanceCategories();
        setCategories(categoriesData || []);
      } catch (err) {
        console.warn('Failed to fetch finance categories:', err);
        setCategories([]);
      }

      // Fetch budget summary with error handling
      try {
        const summary = await financeApi.getBudgetSummary(selectedYear);
        setBudgetSummary(summary);
      } catch (err) {
        console.warn('Failed to fetch budget summary:', err);
        setBudgetSummary(null);
      }

      // Fetch data based on active tab with error handling
      if (activeTab === 'budget') {
        try {
          const budgetData = await financeApi.getFinances({
            year: selectedYear,
            budget_type: 'original',
            per_page: 100
          });
          setBudgetFinances(budgetData.data || []);
        } catch (err) {
          console.warn('Failed to fetch budget data:', err);
          setBudgetFinances([]);
        }
      } else if (activeTab === 'actual') {
        try {
          const actualData = await financeApi.getFinances({
            year: selectedYear,
            budget_type: 'actual',
            per_page: 100
          });
          setActualFinances(actualData.data || []);
        } catch (err) {
          console.warn('Failed to fetch actual data:', err);
          setActualFinances([]);
        }
      } else if (activeTab === 'entities') {
        try {
          const entityData = await financeApi.getFinanceTableData(selectedYear);
          setEntityFinances(entityData || []);
        } catch (err) {
          console.warn('Failed to fetch entity data:', err);
          setEntityFinances([]);
        }
      }
    } catch (err: any) {
      console.error('Finance data fetch error:', err);
      setError(err.response?.data?.message || 'Failed to fetch finance data. Some features may not be available.');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number | undefined | null) => {
    if (amount === undefined || amount === null || isNaN(amount)) {
      return '$0';
    }
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (percentage: number | undefined | null) => {
    if (percentage === undefined || percentage === null || isNaN(percentage)) {
      return '0.0%';
    }
    return `${percentage.toFixed(1)}%`;
  };

  const tabs = [
    { id: 'budget', name: 'Budget Table', icon: CurrencyDollarIcon },
    { id: 'actual', name: 'Actual Table', icon: ChartBarIcon },
    { id: 'entities', name: 'Document/Regulation Finance', icon: DocumentTextIcon },
  ];

  if (!isAuthenticated) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h1>
            <p className="text-gray-600">Please log in to access the finance section.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Finance Management</h1>
            <p className="text-gray-600">
              Manage budgets, track performance, and monitor financial allocations
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {/* Year Selector */}
            <div className="flex items-center space-x-2">
              <CalendarIcon className="h-5 w-5 text-gray-400" />
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {years.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
            
            {user?.role === 'admin' || user?.role === 'editor' ? (
              <div className="flex space-x-3">
                <Link
                  href="/finance/new"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Finance Entry
                </Link>
                <button
                  onClick={() => setPerformanceModalOpen(true)}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  <ChartPieIcon className="h-4 w-4 mr-2" />
                  Set Performance
                </button>
              </div>
            ) : null}
          </div>
        </div>

        {/* Budget Summary Cards */}
        {budgetSummary && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Original Budget</dt>
                      <dd className="text-lg font-medium text-gray-900">{formatCurrency(budgetSummary.original_total)}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ChartBarIcon className="h-6 w-6 text-gray-400" />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Actual Budget</dt>
                      <dd className="text-lg font-medium text-gray-900">{formatCurrency(budgetSummary.actual_total)}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`h-6 w-6 rounded-full ${budgetSummary.efficiency_percentage >= 80 ? 'bg-green-500' : budgetSummary.efficiency_percentage >= 60 ? 'bg-yellow-500' : 'bg-red-500'}`} />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">Efficiency</dt>
                      <dd className="text-lg font-medium text-gray-900">{formatPercentage(budgetSummary.efficiency_percentage)}</dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CurrencyDollarIcon className={`h-6 w-6 ${budgetSummary.savings >= 0 ? 'text-green-500' : 'text-red-500'}`} />
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {budgetSummary.savings >= 0 ? 'Savings' : 'Overspend'}
                      </dt>
                      <dd className={`text-lg font-medium ${budgetSummary.savings >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {formatCurrency(Math.abs(budgetSummary.savings))}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-red-800">{error}</div>
          </div>
        ) : (
          <>
            {activeTab === 'budget' && (
              <FinanceTable
                data={budgetFinances}
                type="budget"
                canEdit={user?.role === 'admin' || user?.role === 'editor'}
                onEdit={(finance) => {
                  setEditingFinance(finance as Finance);
                  setFinanceModalOpen(true);
                }}
                onDelete={async (id) => {
                  if (confirm('Are you sure you want to delete this finance entry?')) {
                    try {
                      await financeApi.deleteFinance(id);
                      fetchFinanceData();
                    } catch (err: any) {
                      setError(err.response?.data?.message || 'Failed to delete finance entry');
                    }
                  }
                }}
              />
            )}

            {activeTab === 'actual' && (
              <FinanceTable
                data={actualFinances}
                type="actual"
                canEdit={user?.role === 'admin' || user?.role === 'editor'}
                onEdit={(finance) => {
                  setEditingFinance(finance as Finance);
                  setFinanceModalOpen(true);
                }}
                onDelete={async (id) => {
                  if (confirm('Are you sure you want to delete this finance entry?')) {
                    try {
                      await financeApi.deleteFinance(id);
                      fetchFinanceData();
                    } catch (err: any) {
                      setError(err.response?.data?.message || 'Failed to delete finance entry');
                    }
                  }
                }}
              />
            )}

            {activeTab === 'entities' && (
              <FinanceTable
                data={entityFinances}
                type="entities"
                canEdit={false} // Entity table is read-only
              />
            )}
          </>
        )}

        {/* Finance Modal */}
        <FinanceModal
          isOpen={financeModalOpen}
          onClose={() => {
            setFinanceModalOpen(false);
            setEditingFinance(undefined);
          }}
          onSuccess={() => {
            fetchFinanceData();
          }}
          finance={editingFinance}
          year={selectedYear}
        />

        {/* Performance Modal */}
        <PerformanceModal
          isOpen={performanceModalOpen}
          onClose={() => {
            setPerformanceModalOpen(false);
            setPerformanceTarget({});
          }}
          onSuccess={() => {
            fetchFinanceData();
          }}
          documentId={performanceTarget.documentId}
          regulationId={performanceTarget.regulationId}
          year={selectedYear}
          currentUser={user}
        />
      </div>
    </Layout>
  );
};

export default FinancePage;
