'use client'

import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  BookOpenIcon,
  PencilIcon,
  TrashIcon,
  ArrowLeftIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  TagIcon,
  DocumentTextIcon,
  ShareIcon,
  PrinterIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import Layout from '../../components/Layout/Layout';
import { useAuthStore } from '../../stores/authStore';
import apiService from '../../services/api';
import { LawsAndRules } from '../../types';
import MarkdownRenderer from '../../components/MarkdownRenderer/MarkdownRenderer';

const RegulationDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const { user, isAuthenticated } = useAuthStore();
  const [regulation, setRegulation] = useState<LawsAndRules | null>(null);
  const [relationships, setRelationships] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const regulationId = params?.id as string;

  useEffect(() => {
    if (!regulationId) return;

    const fetchRegulation = async () => {
      try {
        setLoading(true);
        // Use public API endpoint for regulation details
        const response = await apiService.getRegulation(parseInt(regulationId));

        // The response is a RegulationWithChunks object, extract the regulation data
        const regulationData = response.regulation || response;
        setRegulation(regulationData);

        // Fetch regulation relationships if authenticated
        if (isAuthenticated) {
          try {
            const relationshipsResponse = await apiService.getRegulationRelationships(parseInt(regulationId));
            setRelationships(relationshipsResponse.data || []);
          } catch (err) {
            console.log('Relationships not available for this regulation');
          }
        }
      } catch (err: any) {
        setError(err.response?.data?.message || 'Failed to fetch regulation');
      } finally {
        setLoading(false);
      }
    };

    fetchRegulation();
  }, [regulationId]);

  const handleDelete = async () => {
    if (!regulation || !confirm('Are you sure you want to delete this regulation?')) return;
    
    try {
      await apiService.deleteRegulation(regulation.id);
      router.push('/regulations');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete regulation');
    }
  };

  const canEdit = () => {
    if (!user || !regulation) return false;
    return user.role === 'admin' || user.role === 'editor';
  };

  const canDelete = () => {
    if (!user || !regulation) return false;
    return user.role === 'admin';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'effective': return 'bg-green-100 text-green-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'published': return 'bg-blue-100 text-blue-800';
      case 'final': return 'bg-blue-100 text-blue-800';
      case 'approved': return 'bg-purple-100 text-purple-800';
      case 'under_review': return 'bg-orange-100 text-orange-800';
      case 'proposed': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'superseded': return 'bg-red-100 text-red-800';
      case 'terminated': return 'bg-red-100 text-red-800';
      case 'withdrawn': return 'bg-red-100 text-red-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded mb-4"></div>
            <div className="h-6 bg-gray-200 rounded mb-6 w-3/4"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded"></div>
              <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              <div className="h-4 bg-gray-200 rounded w-4/6"></div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !regulation) {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <BookOpenIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Regulation not found</h3>
            <p className="text-gray-600 mb-4">{error || 'The requested regulation could not be found.'}</p>
            <Link
              href="/regulations"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Back to Regulations
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <Link
            href="/regulations"
            className="inline-flex items-center text-sm text-primary-600 hover:text-primary-500 mb-4"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Regulations
          </Link>
          
          <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-3">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(regulation.status)}`}>
                  {regulation.status?.toUpperCase()}
                </span>
                {regulation.is_significant && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    SIGNIFICANT
                  </span>
                )}
                {regulation.version && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    v{regulation.version}
                  </span>
                )}
              </div>
              
              <h1 className="text-3xl font-bold text-gray-900 mb-4">{regulation.title}</h1>
              
              {regulation.description && (
                <p className="text-lg text-gray-600 mb-6">{regulation.description}</p>
              )}
            </div>
            
            {/* Actions */}
            {regulation && (
              <div className="flex items-center space-x-2 mt-4 lg:mt-0">
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Share">
                  <ShareIcon className="h-5 w-5" />
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors" title="Print">
                  <PrinterIcon className="h-5 w-5" />
                </button>
                {canEdit() && (
                  <Link
                    href={`/regulations/${regulation.id}/edit`}
                    className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    title="Edit Regulation"
                  >
                    <PencilIcon className="h-5 w-5" />
                  </Link>
                )}
                {canDelete() && (
                  <button
                    onClick={handleDelete}
                    className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete Regulation"
                  >
                    <TrashIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Regulation Content */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Regulation Content</h2>
              {regulation.content ? (
                <MarkdownRenderer
                  content={regulation.content}
                  className="prose prose-lg max-w-none"
                />
              ) : regulation.description ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Description</h3>
                  <MarkdownRenderer
                    content={regulation.description}
                    className="prose prose-lg max-w-none"
                  />
                </div>
              ) : regulation.notes ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Notes</h3>
                  <MarkdownRenderer
                    content={regulation.notes}
                    className="prose prose-lg max-w-none"
                  />
                </div>
              ) : regulation.chunks && regulation.chunks.length > 0 ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Regulation Structure</h3>
                  <div className="space-y-4">
                    {regulation.chunks.map((chunk: any, index: number) => (
                      <div key={chunk.id || index} className="border-l-4 border-primary-500 pl-4">
                        {chunk.title && (
                          <h4 className="font-semibold text-gray-900 mb-2">{chunk.title}</h4>
                        )}
                        {chunk.current_chunk_content_version?.content && (
                          <MarkdownRenderer
                            content={chunk.current_chunk_content_version.content}
                            className="prose max-w-none"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <DocumentTextIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 italic">No content available for this regulation.</p>
                  <p className="text-sm text-gray-400 mt-2">
                    This regulation may be in draft status or content has not been added yet.
                  </p>
                </div>
              )}
            </div>

            {/* Relationships */}
            {relationships.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Related Regulations</h2>
                <div className="space-y-4">
                  {relationships.map((relationship) => (
                    <div key={relationship.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="text-sm font-medium text-gray-900">
                              {relationship.relationship_type?.replace('_', ' ').toUpperCase()}
                            </span>
                            <span className="text-sm text-gray-500">•</span>
                            <span className="text-sm text-gray-500">
                              {relationship.related_regulation?.cfr_citation}
                            </span>
                          </div>
                          {relationship.related_regulation?.id ? (
                            <Link
                              href={`/regulations/${relationship.related_regulation.id}`}
                              className="text-lg font-medium text-primary-600 hover:text-primary-700"
                            >
                              {relationship.related_regulation.title}
                            </Link>
                          ) : (
                            <span className="text-lg font-medium text-gray-600">
                              {relationship.related_regulation?.title || 'Unknown Regulation'}
                            </span>
                          )}
                          {relationship.description && (
                            <p className="text-sm text-gray-600 mt-1">{relationship.description}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Regulation Information</h3>
              
              <div className="space-y-4">
                {/* Legal Identifiers */}
                {regulation.cfr_title && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">CFR Title</p>
                    <p className="text-sm text-gray-600">{regulation.cfr_title}</p>
                  </div>
                )}

                {regulation.usc_title && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">USC Title</p>
                    <p className="text-sm text-gray-600">{regulation.usc_title}</p>
                  </div>
                )}

                {regulation.public_law_number && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Public Law Number</p>
                    <p className="text-sm text-gray-600">{regulation.public_law_number}</p>
                  </div>
                )}

                {regulation.regulatory_identifier && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Regulatory Identifier (RIN)</p>
                    <p className="text-sm text-gray-600">{regulation.regulatory_identifier}</p>
                  </div>
                )}

                {regulation.docket_number && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Docket Number</p>
                    <p className="text-sm text-gray-600">{regulation.docket_number}</p>
                  </div>
                )}

                {/* Hierarchical Structure */}
                {regulation.chapter_number && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Chapter</p>
                    <p className="text-sm text-gray-600">Chapter {regulation.chapter_number}</p>
                  </div>
                )}

                {regulation.subchapter && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Subchapter</p>
                    <p className="text-sm text-gray-600">Subchapter {regulation.subchapter}</p>
                  </div>
                )}

                {regulation.part_number && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Part</p>
                    <p className="text-sm text-gray-600">Part {regulation.part_number}</p>
                  </div>
                )}

                {regulation.section_number && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Section</p>
                    <p className="text-sm text-gray-600">Section {regulation.section_number}</p>
                  </div>
                )}

                {regulation.subsection && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Subsection</p>
                    <p className="text-sm text-gray-600">{regulation.subsection}</p>
                  </div>
                )}

                {regulation.agency && (
                  <div className="flex items-start">
                    <BuildingOfficeIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Agency</p>
                      <Link 
                        href={`/agencies/${regulation.agency.id}`}
                        className="text-sm text-primary-600 hover:text-primary-700"
                      >
                        {regulation.agency.name}
                      </Link>
                    </div>
                  </div>
                )}

                {regulation.category_relationships && regulation.category_relationships.length > 0 && (
                  <div className="flex items-start">
                    <TagIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Categories</p>
                      {regulation.category_relationships.map((rel, index) => (
                        <div key={rel.id}>
                          {rel.category?.id ? (
                            <Link
                              href={`/categories/${rel.category.id}`}
                              className="text-sm text-primary-600 hover:text-primary-700"
                            >
                              {rel.category.name}
                            </Link>
                          ) : (
                            <span className="text-sm text-gray-600">
                              {rel.category?.name || 'Unknown Category'}
                            </span>
                          )}
                          {index < regulation.category_relationships!.length - 1 && ', '}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Dates */}
                {regulation.enactment_date && (
                  <div className="flex items-start">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Enactment Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(regulation.enactment_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {regulation.publication_date && (
                  <div className="flex items-start">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Publication Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(regulation.publication_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {regulation.effective_date && (
                  <div className="flex items-start">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Effective Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(regulation.effective_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {regulation.termination_date && (
                  <div className="flex items-start">
                    <CalendarIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Termination Date</p>
                      <p className="text-sm text-gray-600">
                        {new Date(regulation.termination_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {regulation.comment_end_date && (
                  <div className="flex items-start">
                    <ClockIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Comment Period Ends</p>
                      <p className="text-sm text-gray-600">
                        {new Date(regulation.comment_end_date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                )}

                {regulation.parent_regulation && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Parent Regulation</p>
                    <Link 
                      href={`/regulations/${regulation.parent_regulation.id}`}
                      className="text-sm text-primary-600 hover:text-primary-700"
                    >
                      {regulation.parent_regulation.title}
                    </Link>
                  </div>
                )}

                {regulation.supersedes_regulation && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Supersedes</p>
                    <Link 
                      href={`/regulations/${regulation.supersedes_regulation.id}`}
                      className="text-sm text-primary-600 hover:text-primary-700"
                    >
                      {regulation.supersedes_regulation.title}
                    </Link>
                  </div>
                )}

                {regulation.created_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-900">Created</p>
                    <p className="text-sm text-gray-600">
                      {new Date(regulation.created_at).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Regulatory Analysis */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Regulatory Analysis</h3>
              
              <div className="space-y-3">
                {regulation.regulatory_impact_analysis && (
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-700">Regulatory Impact Analysis</span>
                  </div>
                )}
                {regulation.small_business_impact && (
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-700">Small Business Impact</span>
                  </div>
                )}
                {regulation.environmental_impact && (
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-700">Environmental Impact</span>
                  </div>
                )}
                {regulation.federalism_implications && (
                  <div className="flex items-center">
                    <div className="w-2 h-2 bg-purple-500 rounded-full mr-3"></div>
                    <span className="text-sm text-gray-700">Federalism Implications</span>
                  </div>
                )}
              </div>
            </div>

            {/* Tags */}
            {regulation.tags && regulation.tags.length > 0 && (
              <div className="bg-white rounded-lg shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                <div className="flex flex-wrap gap-2">
                  {regulation.tags.map((tag: string, index: number) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default RegulationDetailPage;
