import React, { useState, useEffect } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import {
  Document,
  Agency,
  Category,
  DocumentRelationshipType,
  AgencyRelationshipType,
  CategoryRelationshipType,
  CreateRegulationDocumentRelationshipRequest,
  CreateRegulationAgencyRelationshipRequest,
  CreateRegulationCategoryRelationshipRequest,
  PaginatedResponse
} from '../../types';
import regulationApi from '../../services/regulationApi';
import apiService from '../../services/api';

interface AddRelationshipFormProps {
  regulationId: number;
  onClose: () => void;
  onSuccess: () => void;
}

const AddRelationshipForm: React.FC<AddRelationshipFormProps> = ({
  regulationId,
  onClose,
  onSuccess
}) => {
  const [relationshipType, setRelationshipType] = useState<'document' | 'agency' | 'category'>('document');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  // Entity lists
  const [documents, setDocuments] = useState<Document[]>([]);
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);

  // Form data
  const [formData, setFormData] = useState({
    entityId: '',
    relationshipTypeValue: '',
    effectiveDate: '',
    terminationDate: '',
    description: '',
    citationText: '',
    authorityScope: '',
    scopeDefinition: ''
  });

  // Load entities
  useEffect(() => {
    loadEntities();
  }, [relationshipType]);

  const loadEntities = async () => {
    try {
      setLoading(true);
      switch (relationshipType) {
        case 'document':
          const docResponse = await apiService.get<PaginatedResponse<Document>>('/public/documents?per_page=100');
          setDocuments(docResponse.data);
          break;
        case 'agency':
          const agencyResponse = await apiService.get<PaginatedResponse<Agency>>('/public/agencies?per_page=100');
          setAgencies(agencyResponse.data);
          break;
        case 'category':
          const categoryResponse = await apiService.get<PaginatedResponse<Category>>('/public/categories?per_page=100');
          setCategories(categoryResponse.data);
          break;
      }
    } catch (error) {
      console.error('Failed to load entities:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.entityId || !formData.relationshipTypeValue) {
      alert('Please select an entity and relationship type');
      return;
    }

    try {
      setSubmitting(true);

      switch (relationshipType) {
        case 'document':
          const docRequest: CreateRegulationDocumentRelationshipRequest = {
            document_id: parseInt(formData.entityId),
            relationship_type: formData.relationshipTypeValue as DocumentRelationshipType,
            effective_date: formData.effectiveDate || undefined,
            termination_date: formData.terminationDate || undefined,
            description: formData.description || undefined,
            citation_text: formData.citationText || undefined
          };
          await regulationApi.createRegulationDocumentRelationship(regulationId, docRequest);
          break;

        case 'agency':
          const agencyRequest: CreateRegulationAgencyRelationshipRequest = {
            agency_id: parseInt(formData.entityId),
            relationship_type: formData.relationshipTypeValue as AgencyRelationshipType,
            effective_date: formData.effectiveDate || undefined,
            termination_date: formData.terminationDate || undefined,
            description: formData.description || undefined,
            authority_scope: formData.authorityScope || undefined
          };
          await regulationApi.createRegulationAgencyRelationship(regulationId, agencyRequest);
          break;

        case 'category':
          const categoryRequest: CreateRegulationCategoryRelationshipRequest = {
            category_id: parseInt(formData.entityId),
            relationship_type: formData.relationshipTypeValue as CategoryRelationshipType,
            effective_date: formData.effectiveDate || undefined,
            termination_date: formData.terminationDate || undefined,
            description: formData.description || undefined,
            scope_definition: formData.scopeDefinition || undefined
          };
          await regulationApi.createRegulationCategoryRelationship(regulationId, categoryRequest);
          break;
      }

      onSuccess();
      onClose();
    } catch (error) {
      console.error('Failed to create relationship:', error);
      alert('Failed to create relationship. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const getRelationshipTypeOptions = () => {
    switch (relationshipType) {
      case 'document':
        return [
          { value: 'implements', label: 'Implements' },
          { value: 'based_on', label: 'Based On' },
          { value: 'amends', label: 'Amends' },
          { value: 'repeals', label: 'Repeals' },
          { value: 'references', label: 'References' },
          { value: 'supersedes', label: 'Supersedes' }
        ];
      case 'agency':
        return [
          { value: 'established_by', label: 'Established By' },
          { value: 'authorized_by', label: 'Authorized By' },
          { value: 'modified_by', label: 'Modified By' },
          { value: 'abolished_by', label: 'Abolished By' }
        ];
      case 'category':
        return [
          { value: 'created_by', label: 'Created By' },
          { value: 'modified_by', label: 'Modified By' },
          { value: 'abolished_by', label: 'Abolished By' },
          { value: 'governed_by', label: 'Governed By' }
        ];
      default:
        return [];
    }
  };

  const getEntityOptions = () => {
    switch (relationshipType) {
      case 'document':
        return documents.map(doc => ({ value: doc.id.toString(), label: doc.title }));
      case 'agency':
        return agencies.map(agency => ({ value: agency.id.toString(), label: agency.name }));
      case 'category':
        return categories.map(category => ({ value: category.id.toString(), label: category.name }));
      default:
        return [];
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Add Regulation Relationship
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Relationship Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Relationship Type
            </label>
            <div className="grid grid-cols-3 gap-3">
              {(['document', 'agency', 'category'] as const).map((type) => (
                <button
                  key={type}
                  type="button"
                  onClick={() => {
                    setRelationshipType(type);
                    setFormData(prev => ({ ...prev, entityId: '', relationshipTypeValue: '' }));
                  }}
                  className={`p-3 text-sm font-medium rounded-lg border ${
                    relationshipType === type
                      ? 'bg-blue-50 border-blue-200 text-blue-700 dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-600'
                  }`}
                >
                  {type.charAt(0).toUpperCase() + type.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {/* Entity Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Select {relationshipType.charAt(0).toUpperCase() + relationshipType.slice(1)}
            </label>
            <select
              value={formData.entityId}
              onChange={(e) => setFormData(prev => ({ ...prev, entityId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
              disabled={loading}
            >
              <option value="">
                {loading ? 'Loading...' : `Select a ${relationshipType}`}
              </option>
              {getEntityOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Relationship Type Value */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Relationship Nature
            </label>
            <select
              value={formData.relationshipTypeValue}
              onChange={(e) => setFormData(prev => ({ ...prev, relationshipTypeValue: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              required
            >
              <option value="">Select relationship nature</option>
              {getRelationshipTypeOptions().map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Effective Date
              </label>
              <input
                type="date"
                value={formData.effectiveDate}
                onChange={(e) => setFormData(prev => ({ ...prev, effectiveDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Termination Date
              </label>
              <input
                type="date"
                value={formData.terminationDate}
                onChange={(e) => setFormData(prev => ({ ...prev, terminationDate: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              placeholder="Optional description of the relationship"
            />
          </div>

          {/* Conditional fields based on relationship type */}
          {relationshipType === 'document' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Citation Text
              </label>
              <input
                type="text"
                value={formData.citationText}
                onChange={(e) => setFormData(prev => ({ ...prev, citationText: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="e.g., Section 123(a)"
              />
            </div>
          )}

          {relationshipType === 'agency' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Authority Scope
              </label>
              <input
                type="text"
                value={formData.authorityScope}
                onChange={(e) => setFormData(prev => ({ ...prev, authorityScope: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Scope of authority granted or modified"
              />
            </div>
          )}

          {relationshipType === 'category' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Scope Definition
              </label>
              <input
                type="text"
                value={formData.scopeDefinition}
                onChange={(e) => setFormData(prev => ({ ...prev, scopeDefinition: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Definition or scope of the category"
              />
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? 'Creating...' : 'Create Relationship'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddRelationshipForm;
