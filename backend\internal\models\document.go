package models

import (
	"time"

	"gorm.io/gorm"
)

// DocumentType represents the type of document (similar to Federal Register)
type DocumentType string

const (
	DocumentTypeRule         DocumentType = "rule"
	DocumentTypeProposedRule DocumentType = "proposed_rule"
	DocumentTypeNotice       DocumentType = "notice"
	DocumentTypePresidential DocumentType = "presidential"
	DocumentTypeCorrection   DocumentType = "correction"
	DocumentTypeOther        DocumentType = "other"
)

// DocumentStatus represents the current status of a document
type DocumentStatus string

const (
	StatusDraft            DocumentStatus = "draft"
	StatusUnderReview      DocumentStatus = "under_review"
	StatusApproved         DocumentStatus = "approved"
	StatusPublished        DocumentStatus = "published"
	StatusWithdrawn        DocumentStatus = "withdrawn"
	StatusSuperseded       DocumentStatus = "superseded"
	StatusPublicInspection DocumentStatus = "public_inspection"
)

// Document represents a federal register document
type Document struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Basic document information
	Title    string         `json:"title" gorm:"not null"`
	Slug     string         `json:"slug" gorm:"uniqueIndex;not null"`
	Abstract string         `json:"abstract"`
	Content  string         `json:"content" gorm:"type:text"`
	Type     DocumentType   `json:"type" gorm:"not null"`
	Status   DocumentStatus `json:"status" gorm:"default:'draft'"`

	// Federal Register specific fields
	FRDocumentNumber string     `json:"fr_document_number" gorm:"uniqueIndex"`
	FRCitation       string     `json:"fr_citation"`
	CFRCitations     string     `json:"cfr_citations"` // JSON array of CFR citations
	PublicationDate  *time.Time `json:"publication_date"`
	EffectiveDate    *time.Time `json:"effective_date"`
	TerminationDate  *time.Time `json:"termination_date"`
	CommentDueDate   *time.Time `json:"comment_due_date"`

	// Document metadata
	PageCount      int    `json:"page_count"`
	WordCount      int    `json:"word_count"`
	Language       string `json:"language" gorm:"default:'en'"`
	OriginalFormat string `json:"original_format"` // pdf, doc, txt, etc.
	FileSize       int64  `json:"file_size"`
	Checksum       string `json:"checksum"`

	// Relationships
	AgencyID    uint   `json:"agency_id" gorm:"not null"`
	Agency      Agency `json:"agency" gorm:"foreignKey:AgencyID"`
	CreatedByID uint   `json:"created_by_id" gorm:"not null"`
	CreatedBy   User   `json:"created_by" gorm:"foreignKey:CreatedByID"`
	UpdatedByID *uint  `json:"updated_by_id"`
	UpdatedBy   *User  `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID"`

	// Document hierarchy
	ParentDocumentID *uint      `json:"parent_document_id"`
	ParentDocument   *Document  `json:"parent_document,omitempty" gorm:"foreignKey:ParentDocumentID"`
	ChildDocuments   []Document `json:"child_documents,omitempty" gorm:"foreignKey:ParentDocumentID"`

	// Categories, tags, and subjects
	Categories []Category `json:"categories,omitempty" gorm:"many2many:document_category_assignments;"`
	Tags       []Tag      `json:"tags,omitempty" gorm:"many2many:document_tag_assignments;"`
	Subjects   []Subject  `json:"subjects,omitempty" gorm:"many2many:document_subject_assignments;"`

	// Files and attachments
	Files []DocumentFile `json:"files,omitempty" gorm:"foreignKey:DocumentID"`

	// Reviews and comments
	Reviews  []DocumentReview  `json:"reviews,omitempty" gorm:"foreignKey:DocumentID"`
	Comments []DocumentComment `json:"comments,omitempty" gorm:"foreignKey:DocumentID"`

	// Search and indexing
	SearchVector  string `json:"-" gorm:"type:tsvector"`
	ViewCount     int    `json:"view_count" gorm:"default:0"`
	DownloadCount int    `json:"download_count" gorm:"default:0"`

	// Workflow and approval
	WorkflowStage string     `json:"workflow_stage"`
	ApprovedAt    *time.Time `json:"approved_at"`
	ApprovedByID  *uint      `json:"approved_by_id"`
	ApprovedBy    *User      `json:"approved_by,omitempty" gorm:"foreignKey:ApprovedByID"`
	PublishedAt   *time.Time `json:"published_at"`
	PublishedByID *uint      `json:"published_by_id"`
	PublishedBy   *User      `json:"published_by,omitempty" gorm:"foreignKey:PublishedByID"`

	// Regulatory information
	RegulatoryIdentifier string `json:"regulatory_identifier"` // RIN number
	DocketNumber         string `json:"docket_number"`
	SignificantRule      bool   `json:"significant_rule" gorm:"default:false"`
	EconomicImpact       string `json:"economic_impact"`
	SmallEntityImpact    bool   `json:"small_entity_impact" gorm:"default:false"`

	// Public participation
	AcceptsComments     bool       `json:"accepts_comments" gorm:"default:false"`
	CommentCount        int        `json:"comment_count" gorm:"default:0"`
	CommentInstructions string     `json:"comment_instructions"`
	PublicHearingDate   *time.Time `json:"public_hearing_date"`
	PublicHearingInfo   string     `json:"public_hearing_info"`

	// Document visibility and access control
	VisibilityLevel int  `json:"visibility_level" gorm:"default:1"` // 1=public, 2=restricted, 3=confidential
	IsPublic        bool `json:"is_public" gorm:"default:true"`

	// Regulation relationships
	RegulationCount         int                              `json:"regulation_count" gorm:"default:0"`
	RegulationRelationships []RegulationDocumentRelationship `json:"regulation_relationships,omitempty" gorm:"foreignKey:DocumentID"`
}

// DocumentFile represents files attached to a document
type DocumentFile struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	FileName     string `json:"file_name" gorm:"not null"`
	OriginalName string `json:"original_name" gorm:"not null"`
	FilePath     string `json:"file_path" gorm:"not null"`
	FileType     string `json:"file_type" gorm:"not null"`
	FileSize     int64  `json:"file_size"`
	MimeType     string `json:"mime_type"`
	Checksum     string `json:"checksum"`

	// File metadata
	Description string `json:"description"`
	IsPublic    bool   `json:"is_public" gorm:"default:true"`
	IsPrimary   bool   `json:"is_primary" gorm:"default:false"`
	SortOrder   int    `json:"sort_order" gorm:"default:0"`

	// Upload information
	UploadedByID uint `json:"uploaded_by_id" gorm:"not null"`
	UploadedBy   User `json:"uploaded_by" gorm:"foreignKey:UploadedByID"`

	// Download tracking
	DownloadCount int `json:"download_count" gorm:"default:0"`
}

// DocumentVersion represents different versions of a document
type DocumentVersion struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	VersionNumber int    `json:"version_number" gorm:"not null"`
	Title         string `json:"title"`
	Content       string `json:"content" gorm:"type:text"`
	ChangeLog     string `json:"change_log"`
	IsCurrent     bool   `json:"is_current" gorm:"default:false"`

	CreatedByID uint `json:"created_by_id" gorm:"not null"`
	CreatedBy   User `json:"created_by" gorm:"foreignKey:CreatedByID"`
}

// TableName returns the table name for Document model
func (Document) TableName() string {
	return "documents"
}

// TableName returns the table name for DocumentFile model
func (DocumentFile) TableName() string {
	return "document_files"
}

// DocumentReview represents a review of a document
type DocumentReview struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	ReviewerID uint `json:"reviewer_id" gorm:"not null"`
	Reviewer   User `json:"reviewer" gorm:"foreignKey:ReviewerID"`

	Status   string `json:"status"` // pending, approved, rejected, needs_changes
	Comments string `json:"comments"`
	Rating   int    `json:"rating"` // 1-5 scale

	ReviewedAt *time.Time `json:"reviewed_at"`
}

// DocumentComment represents public comments on a document
type DocumentComment struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	DocumentID uint     `json:"document_id" gorm:"not null"`
	Document   Document `json:"document" gorm:"foreignKey:DocumentID"`

	// Commenter information
	CommenterName  string `json:"commenter_name" gorm:"not null"`
	CommenterEmail string `json:"commenter_email"`
	Organization   string `json:"organization"`

	// Comment content
	Subject string `json:"subject"`
	Content string `json:"content" gorm:"type:text;not null"`

	// Comment metadata
	IsPublic   bool   `json:"is_public" gorm:"default:true"`
	IsVerified bool   `json:"is_verified" gorm:"default:false"`
	IPAddress  string `json:"ip_address"`

	// Moderation
	IsModerated bool       `json:"is_moderated" gorm:"default:false"`
	ModeratedBy *uint      `json:"moderated_by"`
	Moderator   *User      `json:"moderator,omitempty" gorm:"foreignKey:ModeratedBy"`
	ModeratedAt *time.Time `json:"moderated_at"`
}

// TableName returns the table name for DocumentVersion model
func (DocumentVersion) TableName() string {
	return "document_versions"
}

// TableName returns the table name for DocumentReview model
func (DocumentReview) TableName() string {
	return "document_reviews"
}

// TableName returns the table name for DocumentComment model
func (DocumentComment) TableName() string {
	return "document_comments"
}
