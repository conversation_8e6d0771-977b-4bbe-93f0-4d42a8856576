'use client'

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  BuildingOfficeIcon,
  TagIcon,
  ClockIcon,
  CalendarIcon
} from '@heroicons/react/24/outline';
import Layout from '../components/Layout/Layout';
import { useAuthStore } from '../stores/authStore';
import apiService from '../services/api';
import { Document } from '../types';
import DataTable, { Column, ActionButton } from '../components/UI/DataTable';
import StatusBadge from '../components/UI/StatusBadge';
import SearchFilter, { FilterOption } from '../components/UI/SearchFilter';

const DocumentsPage: React.FC = () => {
  const { isAuthenticated, user } = useAuthStore();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    agency_id: '',
    category_id: '',
    type: '',
    status: '',
    sort: 'created_at',
    order: 'desc' as 'asc' | 'desc'
  });
  const [pagination, setPagination] = useState({
    page: 1,
    per_page: 20,
    total: 0,
    total_pages: 0
  });

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        per_page: pagination.per_page,
        search: searchTerm,
        ...filters
      };

      const response = isAuthenticated 
        ? await apiService.getDocuments(params)
        : await apiService.getPublicDocuments(params);
      
      setDocuments(response.data);
      setPagination({
        page: response.page,
        per_page: response.per_page,
        total: response.total,
        total_pages: response.total_pages
      });
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch documents');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDocuments();
  }, [pagination.page, searchTerm, filters, isAuthenticated]);



  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this document?')) return;

    try {
      await apiService.deleteDocument(id);
      fetchDocuments();
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete document');
    }
  };

  // Define table columns
  const columns: Column<Document>[] = [
    {
      key: 'title',
      label: 'Title',
      sortable: true,
      render: (value, item) => (
        <div>
          <Link href={`/documents/${item.id}`} className="text-blue-600 hover:text-blue-800 font-medium">
            {value}
          </Link>
          {item.abstract && (
            <p className="text-sm text-gray-500 mt-1 line-clamp-2">{item.abstract}</p>
          )}
        </div>
      )
    },
    {
      key: 'type',
      label: 'Type',
      sortable: true,
      render: (value) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {value?.replace('_', ' ').toUpperCase()}
        </span>
      )
    },
    {
      key: 'agency',
      label: 'Agency',
      render: (value) => {
        if (typeof value === 'object' && value?.name) {
          return value.name;
        }
        return value || '-';
      }
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (value) => <StatusBadge status={value} size="sm" />
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => new Date(value).toLocaleDateString()
    },
    {
      key: 'view_count',
      label: 'Views',
      sortable: true,
      className: 'text-center'
    }
  ];

  // Define table actions
  const actions: ActionButton<Document>[] = [
    {
      label: 'View',
      icon: EyeIcon,
      href: (item) => `/documents/${parseInt(item.id.toString())}`,
      className: 'text-blue-600 hover:text-blue-800'
    },
    {
      label: 'Edit',
      icon: PencilIcon,
      href: (item) => `/documents/${parseInt(item.id.toString())}/edit`,
      className: 'text-green-600 hover:text-green-800',
      show: (item) => isAuthenticated && user?.role !== 'viewer'
    },
    {
      label: 'Delete',
      icon: TrashIcon,
      onClick: (item) => handleDelete(item.id),
      className: 'text-red-600 hover:text-red-800',
      show: (item) => isAuthenticated && (user?.role === 'admin' || user?.role === 'editor')
    }
  ];

  // Define filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'type',
      label: 'Document Type',
      type: 'select',
      options: [
        { value: 'rule', label: 'Rule' },
        { value: 'proposed_rule', label: 'Proposed Rule' },
        { value: 'notice', label: 'Notice' },
        { value: 'presidential', label: 'Presidential' },
        { value: 'correction', label: 'Correction' },
        { value: 'other', label: 'Other' }
      ]
    },
    {
      key: 'status',
      label: 'Status',
      type: 'select',
      options: [
        { value: 'draft', label: 'Draft' },
        { value: 'under_review', label: 'Under Review' },
        { value: 'approved', label: 'Approved' },
        { value: 'published', label: 'Published' },
        { value: 'withdrawn', label: 'Withdrawn' },
        { value: 'superseded', label: 'Superseded' }
      ]
    },
    {
      key: 'agency_id',
      label: 'Agency',
      type: 'select',
      options: [] // Will be populated from API
    },
    {
      key: 'publication_date',
      label: 'Publication Date',
      type: 'daterange'
    }
  ];

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Documents</h1>
            <p className="text-gray-600">
              Browse and manage federal documents and regulations
            </p>
          </div>
          {isAuthenticated && user?.role !== 'viewer' && (
            <Link
              href="/documents/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Document
            </Link>
          )}
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <SearchFilter
            searchValue={searchTerm}
            onSearchChange={setSearchTerm}
            filters={filterOptions}
            filterValues={filters}
            onFilterChange={(newFilters) => {
              setFilters(prev => ({ ...prev, ...newFilters }));
              setPagination(prev => ({ ...prev, page: 1 }));
            }}
            placeholder="Search documents..."
          />
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
            {error}
          </div>
        )}

        {/* Documents Table */}
        <DataTable
          data={documents}
          columns={columns}
          actions={actions}
          loading={loading}
          error={error}
          pagination={pagination}
          onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
          onSort={(column, direction) => {
            setFilters(prev => ({ ...prev, sort: column, order: direction }));
            setPagination(prev => ({ ...prev, page: 1 }));
          }}
          sortColumn={filters.sort}
          sortDirection={filters.order}
          emptyMessage={
            searchTerm
              ? 'No documents found matching your search criteria. Try adjusting your search terms or filters.'
              : 'No documents found. Get started by creating your first document.'
          }
        />
      </div>
    </Layout>
  );
};

export default DocumentsPage;
