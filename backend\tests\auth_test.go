package tests

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"federal-register-clone/internal/api"
	"federal-register-clone/internal/config"
	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

type AuthTestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *gin.Engine
}

func (suite *AuthTestSuite) SetupSuite() {
	// Setup test database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	suite.Require().NoError(err)

	// Set global database
	database.DB = db
	suite.db = db

	// Run migrations
	err = db.AutoMigrate(
		&models.User{},
		&models.UserSession{},
		&models.UserPreferences{},
		&models.Agency{},
		&models.Document{},
	)
	suite.Require().NoError(err)

	// Setup router
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:             "test-secret",
			ExpiryHours:        24,
			RefreshExpiryHours: 168,
		},
	}
	suite.router = api.SetupRouter(cfg)

	// Set Gin to test mode
	gin.SetMode(gin.TestMode)
}

func (suite *AuthTestSuite) TearDownSuite() {
	// Clean up database
	sqlDB, _ := suite.db.DB()
	sqlDB.Close()
}

func (suite *AuthTestSuite) SetupTest() {
	// Clean up data before each test
	suite.db.Exec("DELETE FROM users")
	suite.db.Exec("DELETE FROM user_sessions")
	suite.db.Exec("DELETE FROM user_preferences")
}

func (suite *AuthTestSuite) TestRegisterUser() {
	// Test successful registration
	registerData := map[string]interface{}{
		"username":  "testuser",
		"email":     "<EMAIL>",
		"password":  "password123",
		"first_name": "Test",
		"last_name":  "User",
	}

	jsonData, _ := json.Marshal(registerData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.Contains(suite.T(), response, "token")
	assert.Contains(suite.T(), response, "user")

	// Verify user was created in database
	var user models.User
	err = suite.db.Where("username = ?", "testuser").First(&user).Error
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "testuser", user.Username)
	assert.Equal(suite.T(), "<EMAIL>", user.Email)
	assert.True(suite.T(), user.IsActive)
}

func (suite *AuthTestSuite) TestRegisterUserDuplicateUsername() {
	// Create existing user
	existingUser := &models.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		PasswordHash: "hashedpassword",
		Role:     models.RoleViewer,
		IsActive: true,
	}
	suite.db.Create(existingUser)

	// Try to register with same username
	registerData := map[string]interface{}{
		"username": "testuser",
		"email":    "<EMAIL>",
		"password": "password123",
	}

	jsonData, _ := json.Marshal(registerData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/register", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusConflict, w.Code)
}

func (suite *AuthTestSuite) TestLoginUser() {
	// Create test user
	user := &models.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		PasswordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // "password"
		Role:     models.RoleViewer,
		IsActive: true,
	}
	suite.db.Create(user)

	// Test successful login
	loginData := map[string]interface{}{
		"identifier": "testuser",
		"password":   "password",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(suite.T(), err)

	assert.Contains(suite.T(), response, "token")
	assert.Contains(suite.T(), response, "refresh_token")
	assert.Contains(suite.T(), response, "user")
}

func (suite *AuthTestSuite) TestLoginInvalidCredentials() {
	// Test login with invalid credentials
	loginData := map[string]interface{}{
		"identifier": "nonexistent",
		"password":   "wrongpassword",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

func (suite *AuthTestSuite) TestGetCurrentUser() {
	// Create test user
	user := &models.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		PasswordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
		Role:     models.RoleViewer,
		IsActive: true,
	}
	suite.db.Create(user)

	// Login to get token
	loginData := map[string]interface{}{
		"identifier": "testuser",
		"password":   "password",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	var loginResponse map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &loginResponse)
	token := loginResponse["token"].(string)

	// Test getting current user
	req, _ = http.NewRequest("GET", "/api/v1/auth/me", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var userResponse models.User
	err := json.Unmarshal(w.Body.Bytes(), &userResponse)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "testuser", userResponse.Username)
	assert.Equal(suite.T(), "<EMAIL>", userResponse.Email)
}

func (suite *AuthTestSuite) TestGetCurrentUserUnauthorized() {
	// Test getting current user without token
	req, _ := http.NewRequest("GET", "/api/v1/auth/me", nil)

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusUnauthorized, w.Code)
}

func (suite *AuthTestSuite) TestLogout() {
	// Create test user and login
	user := &models.User{
		Username: "testuser",
		Email:    "<EMAIL>",
		PasswordHash: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi",
		Role:     models.RoleViewer,
		IsActive: true,
	}
	suite.db.Create(user)

	// Login to get token
	loginData := map[string]interface{}{
		"identifier": "testuser",
		"password":   "password",
	}

	jsonData, _ := json.Marshal(loginData)
	req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	var loginResponse map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &loginResponse)
	token := loginResponse["token"].(string)

	// Test logout
	req, _ = http.NewRequest("POST", "/api/v1/auth/logout", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func TestAuthTestSuite(t *testing.T) {
	suite.Run(t, new(AuthTestSuite))
}
