import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  XMarkIcon,
  CalendarIcon,
  BuildingOfficeIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { useUIStore } from '../../stores/uiStore';
import { Agency, Category, DocumentType, DocumentStatus } from '../../types';
import apiService from '../../services/api';

interface SearchFormProps {
  onSearch?: (params: SearchParams) => void;
  showAdvanced?: boolean;
  className?: string;
}

interface SearchParams {
  query: string;
  agency?: string;
  category?: string;
  type?: DocumentType;
  status?: DocumentStatus;
  dateFrom?: string;
  dateTo?: string;
  sort?: string;
  order?: 'asc' | 'desc';
}

const SearchForm: React.FC<SearchFormProps> = ({
  onSearch,
  showAdvanced = false,
  className = '',
}) => {
  const router = useRouter();
  const { searchQuery, setSearchQuery, searchFilters, setSearchFilters, clearSearchFilters } = useUIStore();
  
  const [localQuery, setLocalQuery] = useState(searchQuery);
  const [showFilters, setShowFilters] = useState(showAdvanced);
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading] = useState(false);

  // Document types and statuses
  const documentTypes: { value: DocumentType; label: string }[] = [
    { value: 'rule', label: 'Rule' },
    { value: 'proposed_rule', label: 'Proposed Rule' },
    { value: 'notice', label: 'Notice' },
    { value: 'presidential', label: 'Presidential Document' },
    { value: 'correction', label: 'Correction' },
    { value: 'other', label: 'Other' },
  ];

  const documentStatuses: { value: DocumentStatus; label: string }[] = [
    { value: 'published', label: 'Published' },
    { value: 'public_inspection', label: 'Public Inspection' },
    { value: 'draft', label: 'Draft' },
    { value: 'under_review', label: 'Under Review' },
    { value: 'approved', label: 'Approved' },
  ];

  // Load filter options
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const [agenciesResponse, categoriesResponse] = await Promise.all([
          apiService.getPublicAgencies({ page: 1, per_page: 100 }),
          apiService.getPublicCategories(),
        ]);
        
        setAgencies(agenciesResponse.data);
        setCategories(categoriesResponse.data);
      } catch (error) {
        console.error('Failed to load filter options:', error);
      }
    };

    if (showFilters) {
      loadFilterOptions();
    }
  }, [showFilters]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch();
  };

  const performSearch = () => {
    const searchParams: SearchParams = {
      query: localQuery.trim(),
      agency: searchFilters.agency,
      category: searchFilters.category,
      type: searchFilters.type as DocumentType,
      status: searchFilters.status as DocumentStatus,
      dateFrom: searchFilters.dateFrom,
      dateTo: searchFilters.dateTo,
    };

    // Update global search state
    setSearchQuery(localQuery);

    // Call onSearch callback if provided
    if (onSearch) {
      onSearch(searchParams);
    } else {
      // Navigate to search results page
      const queryParams = new URLSearchParams();
      
      if (searchParams.query) {
        queryParams.set('q', searchParams.query);
      }
      if (searchParams.agency) {
        queryParams.set('agency', searchParams.agency);
      }
      if (searchParams.category) {
        queryParams.set('category', searchParams.category);
      }
      if (searchParams.type) {
        queryParams.set('type', searchParams.type);
      }
      if (searchParams.status) {
        queryParams.set('status', searchParams.status);
      }
      if (searchParams.dateFrom) {
        queryParams.set('date_from', searchParams.dateFrom);
      }
      if (searchParams.dateTo) {
        queryParams.set('date_to', searchParams.dateTo);
      }

      router.push(`/search?${queryParams.toString()}`);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setSearchFilters({ [key]: value || undefined });
  };

  const clearFilters = () => {
    clearSearchFilters();
    setLocalQuery('');
  };

  const hasActiveFilters = Object.values(searchFilters).some(value => value !== undefined && value !== '');

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-md ${className}`}>
      <form onSubmit={handleSubmit} className="p-6">
        {/* Main search input */}
        <div className="flex gap-4 mb-4">
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={localQuery}
              onChange={(e) => setLocalQuery(e.target.value)}
              placeholder="Search documents, regulations, notices..."
              className="block w-full pl-10 pr-3 py-3 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-lg"
            />
          </div>
          
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className={`px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-primary-500 ${
              showFilters || hasActiveFilters
                ? 'bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-300 border-primary-300 dark:border-primary-600'
                : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
            }`}
          >
            <FunnelIcon className="h-5 w-5" />
          </button>
          
          <button
            type="submit"
            disabled={loading}
            className="px-6 py-3 bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors"
          >
            {loading ? 'Searching...' : 'Search'}
          </button>
        </div>

        {/* Advanced filters */}
        {showFilters && (
          <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
              {/* Agency filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <BuildingOfficeIcon className="h-4 w-4 inline mr-1" />
                  Agency
                </label>
                <select
                  value={searchFilters.agency || ''}
                  onChange={(e) => handleFilterChange('agency', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">All Agencies</option>
                  {agencies.map((agency) => (
                    <option key={agency.id} value={agency.id}>
                      {agency.short_name} - {agency.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={searchFilters.category || ''}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Document type filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <DocumentTextIcon className="h-4 w-4 inline mr-1" />
                  Document Type
                </label>
                <select
                  value={searchFilters.type || ''}
                  onChange={(e) => handleFilterChange('type', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">All Types</option>
                  {documentTypes.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Date from */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <CalendarIcon className="h-4 w-4 inline mr-1" />
                  From Date
                </label>
                <input
                  type="date"
                  value={searchFilters.dateFrom || ''}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              {/* Date to */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <CalendarIcon className="h-4 w-4 inline mr-1" />
                  To Date
                </label>
                <input
                  type="date"
                  value={searchFilters.dateTo || ''}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              {/* Status filter (for authenticated users) */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <select
                  value={searchFilters.status || ''}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">All Statuses</option>
                  {documentStatuses.map((status) => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Filter actions */}
            {hasActiveFilters && (
              <div className="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Active filters applied
                </span>
                <button
                  type="button"
                  onClick={clearFilters}
                  className="inline-flex items-center px-3 py-1 text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100"
                >
                  <XMarkIcon className="h-4 w-4 mr-1" />
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        )}
      </form>
    </div>
  );
};

export default SearchForm;
