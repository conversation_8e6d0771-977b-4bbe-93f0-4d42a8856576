'use client'

import React, { useEffect } from 'react';
import <PERSON> from 'next/link';
import {
  ExclamationCircleIcon,
  ArrowPathIcon,
  HomeIcon,
  BugAntIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

const ErrorPage: React.FC<ErrorPageProps> = ({ error, reset }) => {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application Error:', error);
    
    // You could send this to an error tracking service like Sentry
    // logErrorToService(error);
  }, [error]);

  const getErrorMessage = (error: Error) => {
    // Provide user-friendly error messages for common errors
    if (error.message.includes('Network Error') || error.message.includes('fetch')) {
      return 'Unable to connect to the server. Please check your internet connection and try again.';
    }
    
    if (error.message.includes('Unauthorized') || error.message.includes('401')) {
      return 'Your session has expired. Please log in again to continue.';
    }
    
    if (error.message.includes('Forbidden') || error.message.includes('403')) {
      return 'You do not have permission to access this resource.';
    }
    
    if (error.message.includes('Not Found') || error.message.includes('404')) {
      return 'The requested resource could not be found.';
    }
    
    if (error.message.includes('Server Error') || error.message.includes('500')) {
      return 'A server error occurred. Our team has been notified and is working to resolve the issue.';
    }
    
    return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
  };

  const getErrorTitle = (error: Error) => {
    if (error.message.includes('Network Error') || error.message.includes('fetch')) {
      return 'Connection Error';
    }
    
    if (error.message.includes('Unauthorized') || error.message.includes('401')) {
      return 'Session Expired';
    }
    
    if (error.message.includes('Forbidden') || error.message.includes('403')) {
      return 'Access Denied';
    }
    
    if (error.message.includes('Not Found') || error.message.includes('404')) {
      return 'Not Found';
    }
    
    if (error.message.includes('Server Error') || error.message.includes('500')) {
      return 'Server Error';
    }
    
    return 'Something went wrong';
  };

  const copyErrorToClipboard = () => {
    const errorInfo = `
Error: ${error.message}
Stack: ${error.stack}
Digest: ${error.digest || 'N/A'}
Timestamp: ${new Date().toISOString()}
URL: ${window.location.href}
User Agent: ${navigator.userAgent}
    `.trim();
    
    navigator.clipboard.writeText(errorInfo).then(() => {
      alert('Error details copied to clipboard');
    }).catch(() => {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = errorInfo;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      alert('Error details copied to clipboard');
    });
  };

  const shouldShowTechnicalDetails = process.env.NODE_ENV === 'development';

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 text-center">
        {/* Error Icon */}
        <div className="mx-auto h-24 w-24 flex items-center justify-center rounded-full bg-red-100">
          <ExclamationCircleIcon className="h-12 w-12 text-red-600" />
        </div>

        {/* Error Message */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            {getErrorTitle(error)}
          </h1>
          <p className="text-gray-600 mb-8">
            {getErrorMessage(error)}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4">
          <button
            onClick={reset}
            className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Try Again
          </button>

          <Link
            href="/"
            className="w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <HomeIcon className="h-4 w-4 mr-2" />
            Go to Homepage
          </Link>

          <button
            onClick={() => window.location.reload()}
            className="w-full flex justify-center items-center py-3 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            <ArrowPathIcon className="h-4 w-4 mr-2" />
            Reload Page
          </button>
        </div>

        {/* Technical Details (Development Only) */}
        {shouldShowTechnicalDetails && (
          <div className="mt-8 p-4 bg-gray-100 rounded-lg text-left">
            <h3 className="text-sm font-medium text-gray-900 mb-2 flex items-center">
              <BugAntIcon className="h-4 w-4 mr-2" />
              Technical Details (Development)
            </h3>
            <div className="text-xs text-gray-700 space-y-2">
              <div>
                <strong>Error:</strong> {error.message}
              </div>
              {error.digest && (
                <div>
                  <strong>Digest:</strong> {error.digest}
                </div>
              )}
              {error.stack && (
                <div>
                  <strong>Stack:</strong>
                  <pre className="mt-1 text-xs bg-gray-200 p-2 rounded overflow-x-auto">
                    {error.stack}
                  </pre>
                </div>
              )}
            </div>
            <button
              onClick={copyErrorToClipboard}
              className="mt-3 flex items-center text-xs text-blue-600 hover:text-blue-800"
            >
              <ClipboardDocumentIcon className="h-3 w-3 mr-1" />
              Copy Error Details
            </button>
          </div>
        )}

        {/* Error Reporting */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Report This Error</h4>
          <p className="text-sm text-blue-700 mb-3">
            Help us improve by reporting this error. Include what you were doing when it occurred.
          </p>
          <div className="space-y-2">
            <Link
              href="/contact"
              className="block text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Contact Support
            </Link>
            <button
              onClick={copyErrorToClipboard}
              className="block text-sm text-blue-600 hover:text-blue-800 underline"
            >
              Copy Error Details for Support
            </button>
          </div>
        </div>

        {/* Error ID for Support */}
        <div className="text-xs text-gray-500">
          Error ID: {error.digest || 'N/A'} | {new Date().toISOString()}
        </div>
      </div>
    </div>
  );
};

export default ErrorPage;
