# Proceeding System API Documentation

## Base URL
```
/api/proceedings
```

## Authentication
All endpoints require authentication via <PERSON><PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Core CRUD Operations

### List Proceedings
```http
GET /api/proceedings
```

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `per_page` (integer): Items per page (default: 20)
- `search` (string): Search in name, description, objective, unique_id
- `status` (string): Filter by status (planning, active, suspended, completed, cancelled, under_review)
- `priority` (string): Filter by priority (low, medium, high, urgent, critical)
- `owner_id` (integer): Filter by owner ID
- `agency_id` (integer): Filter by agency ID
- `category_id` (integer): Filter by category ID
- `sort` (string): Sort field (created_at, updated_at, name, priority, status, progress_percent)
- `order` (string): Sort order (asc, desc)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "name": "PRP Section 5 Enhancement Initiative",
      "unique_id": "PRP Section 5 Enhancement Initiative 2025-01-02",
      "description": "Comprehensive review and enhancement of PRP Section 5",
      "objective": "Improve efficiency and compliance in Section 5 procedures",
      "status": "active",
      "priority": "high",
      "initiation_date": "2025-01-02T00:00:00Z",
      "progress_percent": 60.0,
      "total_steps": 5,
      "completed_steps": 3,
      "current_step_order": 4,
      "owner": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>"
      },
      "agency": {
        "id": 1,
        "name": "Student Affairs"
      },
      "created_at": "2025-01-02T10:00:00Z",
      "updated_at": "2025-01-02T15:30:00Z"
    }
  ],
  "page": 1,
  "per_page": 20,
  "total": 1,
  "total_pages": 1,
  "has_next": false,
  "has_prev": false
}
```

### Get Proceeding
```http
GET /api/proceedings/{id}
```

**Response:**
```json
{
  "message": "Proceeding retrieved successfully",
  "data": {
    "id": 1,
    "name": "PRP Section 5 Enhancement Initiative",
    "unique_id": "PRP Section 5 Enhancement Initiative 2025-01-02",
    "description": "Comprehensive review and enhancement of PRP Section 5",
    "objective": "Improve efficiency and compliance in Section 5 procedures",
    "status": "active",
    "priority": "high",
    "initiation_date": "2025-01-02T00:00:00Z",
    "prp_alignment": "This proceeding directly addresses PRP Section 5 requirements...",
    "prp_sections": "Section 5.1, Section 5.2, Section 5.3",
    "new_prp_elements": "New subsection 5.4 for digital compliance",
    "review_required": true,
    "review_scheduled": false,
    "requires_ifr": false,
    "existing_directives_reviewed": true,
    "referenced_rules": "[\"rule-001\", \"rule-002\"]",
    "conflict_analysis": "No conflicts identified with existing directives",
    "integration_plan": "Gradual rollout over 6 months",
    "progress_percent": 60.0,
    "total_steps": 5,
    "completed_steps": 3,
    "current_step_order": 4,
    "initiated_by": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    },
    "owner": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>"
    },
    "primary_steps": [
      {
        "id": 1,
        "step_order": 1,
        "name": "Initial Assessment",
        "description": "Conduct comprehensive assessment of current Section 5",
        "status": "completed",
        "completion_criteria": "Assessment report completed and approved",
        "completed_at": "2025-01-03T14:00:00Z",
        "progress_percent": 100.0
      }
    ],
    "proceeding_logs": [
      {
        "id": 1,
        "log_type": "general",
        "title": "Proceeding Created",
        "content": "Proceeding 'PRP Section 5 Enhancement Initiative' has been created",
        "author": {
          "id": 1,
          "username": "admin"
        },
        "created_at": "2025-01-02T10:00:00Z"
      }
    ]
  }
}
```

### Create Proceeding
```http
POST /api/proceedings
```

**Request Body:**
```json
{
  "name": "New Proceeding Initiative",
  "description": "Description of the proceeding",
  "objective": "Clear objective statement",
  "priority": "medium",
  "prp_alignment": "Description of PRP correlation",
  "prp_sections": "Section 1.1, Section 1.2",
  "new_prp_elements": "New elements to be developed",
  "planned_start_date": "2025-01-15",
  "planned_end_date": "2025-06-15",
  "estimated_duration": 150,
  "review_required": true,
  "requires_ifr": false,
  "owner_id": 1,
  "agency_id": 1,
  "category_id": 1,
  "tags": "enhancement,compliance",
  "is_public": false,
  "notes": "Additional notes",
  "referenced_rules": "[\"rule-001\"]",
  "referenced_orders": "[\"order-001\"]",
  "conflict_analysis": "No conflicts identified",
  "integration_plan": "Phased implementation",
  "primary_steps": [
    {
      "name": "Step 1",
      "description": "First step description",
      "step_order": 1,
      "completion_criteria": "Criteria for completion",
      "priority": "medium",
      "notes": "Step notes"
    },
    {
      "name": "Step 2",
      "description": "Second step description",
      "step_order": 2,
      "completion_criteria": "Criteria for completion",
      "priority": "medium",
      "notes": "Step notes"
    },
    {
      "name": "Step 3",
      "description": "Third step description",
      "step_order": 3,
      "completion_criteria": "Criteria for completion",
      "priority": "medium",
      "notes": "Step notes"
    },
    {
      "name": "Step 4",
      "description": "Fourth step description",
      "step_order": 4,
      "completion_criteria": "Criteria for completion",
      "priority": "medium",
      "notes": "Step notes"
    },
    {
      "name": "Step 5",
      "description": "Fifth step description",
      "step_order": 5,
      "completion_criteria": "Criteria for completion",
      "priority": "medium",
      "notes": "Step notes"
    }
  ]
}
```

**Response:**
```json
{
  "message": "Proceeding created successfully",
  "data": {
    "id": 2,
    "name": "New Proceeding Initiative",
    "unique_id": "New Proceeding Initiative 2025-01-02",
    // ... full proceeding object
  }
}
```

### Update Proceeding
```http
PUT /api/proceedings/{id}
```

**Request Body:** (All fields optional)
```json
{
  "name": "Updated Proceeding Name",
  "description": "Updated description",
  "status": "active",
  "priority": "high",
  "prp_alignment": "Updated PRP alignment",
  "review_required": true,
  "notes": "Updated notes"
}
```

**Response:**
```json
{
  "message": "Proceeding updated successfully",
  "data": {
    // ... updated proceeding object
  }
}
```

### Delete Proceeding
```http
DELETE /api/proceedings/{id}
```

**Response:**
```json
{
  "message": "Proceeding deleted successfully",
  "data": {
    "id": 1
  }
}
```

## Step Management

### Get Proceeding Steps
```http
GET /api/proceedings/{id}/steps
```

**Response:**
```json
{
  "message": "Proceeding steps retrieved successfully",
  "data": [
    {
      "id": 1,
      "proceeding_id": 1,
      "step_order": 1,
      "name": "Initial Assessment",
      "description": "Conduct comprehensive assessment",
      "status": "completed",
      "can_start_concurrently": false,
      "previous_step_required": true,
      "blocks_next_step": true,
      "completion_criteria": "Assessment report completed",
      "completion_evidence": "Report submitted and approved",
      "completed_at": "2025-01-03T14:00:00Z",
      "assigned_to": {
        "id": 2,
        "username": "analyst",
        "email": "<EMAIL>"
      },
      "owner": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>"
      },
      "progress_percent": 100.0,
      "is_critical": true,
      "priority": "high"
    }
  ]
}
```

### Update Step Status
```http
PUT /api/proceedings/{id}/steps/{step_id}/status
```

**Request Body:**
```json
{
  "status": "completed",
  "completion_evidence": "Evidence of completion",
  "notes": "Additional notes about completion",
  "actual_end_date": "2025-01-03T14:00:00Z",
  "progress_percent": 100.0
}
```

**Response:**
```json
{
  "message": "Step status updated successfully",
  "data": {
    // ... updated step object
  }
}
```

## Activity Logging

### Add Log Entry
```http
POST /api/proceedings/{id}/logs
```

**Request Body:**
```json
{
  "log_type": "milestone",
  "title": "Major Milestone Reached",
  "content": "Detailed description of the milestone",
  "primary_step_id": 1,
  "decision": "Proceed to next phase",
  "rationale": "All criteria met successfully",
  "impact": "Positive impact on timeline",
  "event_date": "2025-01-03T14:00:00Z",
  "priority": "high",
  "is_public": false,
  "tags": "milestone,progress"
}
```

**Response:**
```json
{
  "message": "Log entry created successfully",
  "data": {
    "id": 5,
    "proceeding_id": 1,
    "log_type": "milestone",
    "title": "Major Milestone Reached",
    "content": "Detailed description of the milestone",
    "author": {
      "id": 1,
      "username": "admin"
    },
    "created_at": "2025-01-03T14:30:00Z"
  }
}
```

## System Integration

### Get Proceeding Relationships
```http
GET /api/proceedings/{id}/relationships
```

**Response:**
```json
{
  "message": "Proceeding relationships retrieved successfully",
  "data": {
    "proceeding_id": 1,
    "related_tasks": [
      {
        "task_id": 1,
        "title": "Assessment Task",
        "status": "completed",
        "relationship_type": "generated_by",
        "notes": "Task created from step 1",
        "created_at": "2025-01-02T11:00:00Z"
      }
    ],
    "related_documents": [
      {
        "document_id": 1,
        "title": "Assessment Report",
        "status": "published",
        "relationship_type": "output",
        "notes": "Generated from proceeding",
        "created_at": "2025-01-03T15:00:00Z"
      }
    ],
    "related_regulations": [
      {
        "regulation_id": 1,
        "title": "PRP Section 5 Rules",
        "status": "active",
        "relationship_type": "modifies",
        "notes": "Updated by this proceeding",
        "created_at": "2025-01-02T12:00:00Z"
      }
    ]
  }
}
```

### Link to Task
```http
POST /api/proceedings/{id}/link-task
```

**Request Body:**
```json
{
  "task_id": 1,
  "relationship_type": "related",
  "notes": "Task supports proceeding objective"
}
```

### Link to Document
```http
POST /api/proceedings/{id}/link-document
```

**Request Body:**
```json
{
  "document_id": 1,
  "relationship_type": "reference",
  "notes": "Document provides background information"
}
```

### Link to Regulation
```http
POST /api/proceedings/{id}/link-regulation
```

**Request Body:**
```json
{
  "regulation_id": 1,
  "relationship_type": "modifies",
  "notes": "Proceeding will update this regulation"
}
```

### Trigger Review Report
```http
POST /api/proceedings/{id}/trigger-review
```

**Request Body:**
```json
{
  "review_type": "milestone",
  "review_date": "2025-01-15",
  "reviewer_id": 2,
  "description": "Mid-proceeding milestone review",
  "is_scheduled": true
}
```

**Response:**
```json
{
  "message": "Review report triggered successfully",
  "data": {
    "proceeding_id": 1,
    "review_type": "milestone",
    "review_date": "2025-01-15T00:00:00Z",
    "reviewer_id": 2,
    "description": "Mid-proceeding milestone review"
  }
}
```

## Error Responses

### Validation Errors
```json
{
  "error": "Validation failed",
  "message": "Proceeding must have at least 5 primary steps (requirement 3)",
  "details": {
    "field": "primary_steps",
    "current_count": 3,
    "required_minimum": 5
  }
}
```

### Sequential Execution Violation
```json
{
  "error": "Sequential execution violation",
  "message": "Step 2 must be completed before step 3 can start (requirement 2)",
  "details": {
    "current_step": 3,
    "blocking_step": 2,
    "blocking_step_status": "in_progress"
  }
}
```

### Not Found
```json
{
  "error": "Not found",
  "message": "Proceeding not found"
}
```

### Unauthorized
```json
{
  "error": "Unauthorized",
  "message": "User not authenticated"
}
```

### Forbidden
```json
{
  "error": "Forbidden",
  "message": "Insufficient permissions for this operation"
}
```

## Status Codes

- `200 OK` - Successful GET, PUT operations
- `201 Created` - Successful POST operations
- `400 Bad Request` - Validation errors, malformed requests
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Insufficient permissions
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., duplicate relationships)
- `500 Internal Server Error` - Server errors

## Rate Limiting

API requests are limited to:
- 1000 requests per hour per user
- 100 requests per minute per user
- 10 concurrent requests per user

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1641024000
```
