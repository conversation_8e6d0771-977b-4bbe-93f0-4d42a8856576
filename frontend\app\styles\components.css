/* Component-specific styles */

/* Layout and Containers */
.container-custom {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Header and Navigation */
.header-nav {
  @apply bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700;
}

.nav-link {
  @apply text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 px-3 py-2 text-sm font-medium transition-colors duration-200;
}

.nav-link-active {
  @apply text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900 rounded-md;
}

.mobile-menu {
  @apply md:hidden bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700;
}

.mobile-nav-link {
  @apply block px-3 py-2 text-base font-medium text-gray-700 dark:text-gray-300 hover:text-primary-600 dark:hover:text-primary-400 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200;
}

/* Footer */
.footer {
  @apply bg-gray-900 dark:bg-gray-950 text-gray-300;
}

.footer-link {
  @apply text-gray-400 hover:text-white transition-colors duration-200;
}

.footer-heading {
  @apply text-sm font-semibold text-white tracking-wider uppercase;
}

/* Cards and Containers */
.document-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:border-primary-200 dark:hover:border-primary-700;
}

.agency-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:border-primary-200 dark:hover:border-primary-700;
}

.category-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200 hover:border-primary-200 dark:hover:border-primary-700;
}

.card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-200;
}

.stats-card {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-700;
}

.hero-section {
  @apply bg-gradient-to-br from-primary-50 to-primary-100 dark:from-primary-900 dark:to-primary-800 py-16 px-4;
}

/* Forms and Inputs */
.search-form {
  @apply bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6;
}

.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;
}

.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
}

.form-select {
  @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
}

.form-textarea {
  @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 dark:placeholder-gray-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  resize: vertical;
}

.form-checkbox {
  @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded;
}

.form-radio {
  @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600;
}

/* Buttons */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-xl {
  @apply px-8 py-4 text-lg;
}

.btn-primary {
  @apply btn border-transparent text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 shadow-sm hover:shadow-md;
}

.btn-secondary {
  @apply btn border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-primary-500;
}

.btn-outline {
  @apply btn border-primary-600 text-primary-600 dark:text-primary-400 bg-transparent hover:bg-primary-50 dark:hover:bg-primary-900 focus:ring-primary-500;
}

.btn-ghost {
  @apply btn border-transparent text-gray-700 dark:text-gray-300 bg-transparent hover:bg-gray-100 dark:hover:bg-gray-700 focus:ring-gray-500;
}

.btn-danger {
  @apply btn border-transparent text-white bg-red-600 hover:bg-red-700 focus:ring-red-500 shadow-sm hover:shadow-md;
}

.btn-success {
  @apply btn border-transparent text-white bg-green-600 hover:bg-green-700 focus:ring-green-500 shadow-sm hover:shadow-md;
}

.btn-warning {
  @apply btn border-transparent text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500 shadow-sm hover:shadow-md;
}

/* Badges and Tags */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-sm {
  @apply px-2 py-0.5 text-xs;
}

.badge-lg {
  @apply px-3 py-1 text-sm;
}

.badge-primary {
  @apply badge bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300;
}

.badge-secondary {
  @apply badge bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300;
}

.badge-success {
  @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.badge-warning {
  @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

.badge-danger {
  @apply badge bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}

.badge-info {
  @apply badge bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

/* Document Type Badges */
.badge-rule {
  @apply badge bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
}

.badge-proposed-rule {
  @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

.badge-notice {
  @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.badge-presidential {
  @apply badge bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300;
}

.badge-correction {
  @apply badge bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
}

/* Status Badges */
.badge-published {
  @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.badge-draft {
  @apply badge bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
}

.badge-archived {
  @apply badge bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300;
}

.badge-active {
  @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
}

.badge-inactive {
  @apply badge bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300;
}

/* Alerts and Messages */
.alert {
  @apply p-4 rounded-md border;
}

.alert-success {
  @apply alert bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700 text-green-800 dark:text-green-200;
}

.alert-warning {
  @apply alert bg-yellow-50 dark:bg-yellow-900 border-yellow-200 dark:border-yellow-700 text-yellow-800 dark:text-yellow-200;
}

.alert-error {
  @apply alert bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700 text-red-800 dark:text-red-200;
}

.alert-info {
  @apply alert bg-blue-50 dark:bg-blue-900 border-blue-200 dark:border-blue-700 text-blue-800 dark:text-blue-200;
}

/* Loading States */
.skeleton {
  @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
}

.skeleton-text {
  @apply skeleton h-4;
}

.skeleton-title {
  @apply skeleton h-6;
}

.skeleton-avatar {
  @apply skeleton h-10 w-10 rounded-full;
}

.skeleton-button {
  @apply skeleton h-10 w-24;
}

.skeleton-card {
  @apply skeleton h-32 w-full;
}

/* Pagination */
.pagination {
  @apply flex items-center justify-between border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-4 py-3 sm:px-6;
}

.pagination-button {
  @apply relative inline-flex items-center px-4 py-2 text-sm font-medium border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200;
}

.pagination-button-active {
  @apply relative inline-flex items-center px-4 py-2 text-sm font-semibold bg-primary-600 text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500;
}

.pagination-button-disabled {
  @apply pagination-button opacity-50 cursor-not-allowed;
}

/* Tables */
.table {
  @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
}

.table-header {
  @apply bg-gray-50 dark:bg-gray-800;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
}

.table-body {
  @apply bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700;
}

.table-row {
  @apply hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
}

/* Modals and Overlays */
.modal-overlay {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75 transition-opacity;
}

.modal-container {
  @apply fixed inset-0 z-10 overflow-y-auto;
}

.modal-content {
  @apply relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg;
}

.modal-header {
  @apply bg-white dark:bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4;
}

.modal-body {
  @apply bg-white dark:bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4;
}

.modal-footer {
  @apply bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6;
}

/* Dropdowns */
.dropdown {
  @apply relative inline-block text-left;
}

.dropdown-menu {
  @apply absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white dark:bg-gray-800 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none;
}

.dropdown-item {
  @apply block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100 transition-colors duration-200;
}

/* Breadcrumbs */
.breadcrumb {
  @apply flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400;
}

.breadcrumb-item {
  @apply hover:text-gray-700 dark:hover:text-gray-300 transition-colors duration-200;
}

.breadcrumb-separator {
  @apply text-gray-400 dark:text-gray-600;
}

/* Progress Bars */
.progress {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
}

.progress-bar {
  @apply bg-primary-600 h-2 rounded-full transition-all duration-300;
}

/* Tooltips */
.tooltip {
  @apply absolute z-10 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip-arrow;
}

.tooltip-arrow {
  @apply absolute w-2 h-2 bg-gray-900 transform rotate-45;
}

/* Tabs */
.tab-list {
  @apply flex space-x-8 border-b border-gray-200 dark:border-gray-700;
}

.tab {
  @apply py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200;
}

.tab-active {
  @apply tab border-primary-500 text-primary-600 dark:text-primary-400;
}

.tab-inactive {
  @apply tab border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600;
}

.tab-panel {
  @apply mt-4;
}
