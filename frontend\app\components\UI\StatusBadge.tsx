import React from 'react';
import {
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  EyeIcon,
  PencilIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface StatusBadgeProps {
  status: string;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ 
  status, 
  size = 'md', 
  showIcon = true,
  className = '' 
}) => {
  const getStatusConfig = (status: string) => {
    const configs: { [key: string]: { label: string, icon: React.ComponentType<{ className?: string }>, colors: string, iconColor: string } } = {
      // Document statuses
      'draft': {
        label: 'Draft',
        icon: PencilIcon,
        colors: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
        iconColor: 'text-gray-600'
      },
      'under_review': {
        label: 'Under Review',
        icon: ClockIcon,
        colors: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        iconColor: 'text-yellow-600'
      },
      'approved': {
        label: 'Approved',
        icon: CheckCircleIcon,
        colors: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        iconColor: 'text-green-600'
      },
      'published': {
        label: 'Published',
        icon: EyeIcon,
        colors: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        iconColor: 'text-blue-600'
      },
      'withdrawn': {
        label: 'Withdrawn',
        icon: XCircleIcon,
        colors: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        iconColor: 'text-red-600'
      },
      'rejected': {
        label: 'Rejected',
        icon: XCircleIcon,
        colors: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        iconColor: 'text-red-600'
      },
      'superseded': {
        label: 'Superseded',
        icon: ExclamationTriangleIcon,
        colors: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
        iconColor: 'text-orange-600'
      },
      'public_inspection': {
        label: 'Public Inspection',
        icon: EyeIcon,
        colors: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
        iconColor: 'text-purple-600'
      },
      // Task statuses
      'not_started': {
        label: 'Not Started',
        icon: ClockIcon,
        colors: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
        iconColor: 'text-gray-600'
      },
      'in_progress': {
        label: 'In Progress',
        icon: ClockIcon,
        colors: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        iconColor: 'text-blue-600'
      },
      'completed': {
        label: 'Completed',
        icon: CheckCircleIcon,
        colors: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        iconColor: 'text-green-600'
      },
      'cancelled': {
        label: 'Cancelled',
        icon: XCircleIcon,
        colors: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        iconColor: 'text-red-600'
      },
      // Legal statuses
      'effective': {
        label: 'Effective',
        icon: CheckCircleIcon,
        colors: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        iconColor: 'text-green-600'
      },
      'terminated': {
        label: 'Terminated',
        icon: XCircleIcon,
        colors: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        iconColor: 'text-red-600'
      },
      'proposed': {
        label: 'Proposed',
        icon: ClockIcon,
        colors: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        iconColor: 'text-yellow-600'
      },
      'final': {
        label: 'Final',
        icon: CheckCircleIcon,
        colors: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        iconColor: 'text-green-600'
      },
      // Priority levels
      'high': {
        label: 'High',
        icon: ExclamationTriangleIcon,
        colors: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        iconColor: 'text-red-600'
      },
      'medium': {
        label: 'Medium',
        icon: ClockIcon,
        colors: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        iconColor: 'text-yellow-600'
      },
      'low': {
        label: 'Low',
        icon: ClockIcon,
        colors: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
        iconColor: 'text-blue-600'
      },
      // System statuses
      'active': {
        label: 'Active',
        icon: CheckCircleIcon,
        colors: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        iconColor: 'text-green-600'
      },
      'inactive': {
        label: 'Inactive',
        icon: XCircleIcon,
        colors: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
        iconColor: 'text-gray-600'
      },
      'pending': {
        label: 'Pending',
        icon: ClockIcon,
        colors: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
        iconColor: 'text-orange-600'
      }
    };

    return configs[status] || {
      label: status.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      icon: ClockIcon,
      colors: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200',
      iconColor: 'text-gray-600'
    };
  };

  const getSizeClasses = (size: string) => {
    const sizes = {
      'sm': 'px-2 py-0.5 text-xs',
      'md': 'px-2.5 py-0.5 text-xs',
      'lg': 'px-3 py-1 text-sm'
    };
    return sizes[size as keyof typeof sizes] || sizes.md;
  };

  const getIconSize = (size: string) => {
    const sizes = {
      'sm': 'h-3 w-3',
      'md': 'h-3 w-3',
      'lg': 'h-4 w-4'
    };
    return sizes[size as keyof typeof sizes] || sizes.md;
  };

  const config = getStatusConfig(status);
  const Icon = config.icon;

  return (
    <span 
      className={`inline-flex items-center ${getSizeClasses(size)} rounded-full font-medium ${config.colors} ${className}`}
    >
      {showIcon && (
        <Icon className={`${getIconSize(size)} mr-1 ${config.iconColor}`} />
      )}
      {config.label}
    </span>
  );
};

export default StatusBadge;
