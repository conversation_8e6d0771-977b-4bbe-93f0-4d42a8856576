package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

// Config holds database configuration
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	DBName   string
	SSLMode  string
}

// Initialize initializes the database connection
func Initialize(config Config) error {
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		config.Host, config.Port, config.User, config.Password, config.DBName, config.SSLMode,
	)

	// Configure GORM logger
	gormLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags),
		logger.Config{
			SlowThreshold:             time.Second,
			LogLevel:                  logger.Info,
			IgnoreRecordNotFoundError: true,
			Colorful:                  true,
		},
	)

	var err error
	DB, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: gormLogger,
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}

	// Configure connection pool
	sqlDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %w", err)
	}

	// Set connection pool settings
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return nil
}

// Migrate runs database migrations
func Migrate() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// Enable UUID extension
	if err := DB.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		log.Printf("Warning: Could not create uuid-ossp extension: %v", err)
	}

	// Enable full-text search extension
	if err := DB.Exec("CREATE EXTENSION IF NOT EXISTS \"pg_trgm\"").Error; err != nil {
		log.Printf("Warning: Could not create pg_trgm extension: %v", err)
	}

	// Run manual migrations instead of AutoMigrate to avoid GORM issues
	err := runManualMigrations(DB)

	if err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	// Create indexes for better performance
	if err := createIndexes(); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// Create full-text search indexes
	if err := createFullTextSearchIndexes(); err != nil {
		return fmt.Errorf("failed to create full-text search indexes: %w", err)
	}

	log.Println("Database migration completed successfully")
	return nil
}

// createIndexes creates additional database indexes for performance
func createIndexes() error {
	indexes := []string{
		// Document indexes
		"CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status)",
		"CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(type)",
		"CREATE INDEX IF NOT EXISTS idx_documents_agency_id ON documents(agency_id)",
		"CREATE INDEX IF NOT EXISTS idx_documents_publication_date ON documents(publication_date)",
		"CREATE INDEX IF NOT EXISTS idx_documents_effective_date ON documents(effective_date)",
		"CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_documents_fr_document_number ON documents(fr_document_number)",

		// Agency indexes
		"CREATE INDEX IF NOT EXISTS idx_agencies_slug ON agencies(slug)",
		"CREATE INDEX IF NOT EXISTS idx_agencies_short_name ON agencies(short_name)",
		"CREATE INDEX IF NOT EXISTS idx_agencies_is_active ON agencies(is_active)",

		// User indexes
		"CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
		"CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
		"CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
		"CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)",

		// Category indexes
		"CREATE INDEX IF NOT EXISTS idx_categories_slug ON categories(slug)",
		"CREATE INDEX IF NOT EXISTS idx_categories_parent_category_id ON categories(parent_category_id)",

		// Tag indexes
		"CREATE INDEX IF NOT EXISTS idx_tags_slug ON tags(slug)",
		"CREATE INDEX IF NOT EXISTS idx_tags_usage_count ON tags(usage_count)",

		// File indexes
		"CREATE INDEX IF NOT EXISTS idx_document_files_document_id ON document_files(document_id)",
		"CREATE INDEX IF NOT EXISTS idx_document_files_file_type ON document_files(file_type)",

		// Comment indexes
		"CREATE INDEX IF NOT EXISTS idx_document_comments_document_id ON document_comments(document_id)",
		"CREATE INDEX IF NOT EXISTS idx_document_comments_is_public ON document_comments(is_public)",
		"CREATE INDEX IF NOT EXISTS idx_document_comments_created_at ON document_comments(created_at)",

		// Review indexes
		"CREATE INDEX IF NOT EXISTS idx_document_reviews_document_id ON document_reviews(document_id)",
		"CREATE INDEX IF NOT EXISTS idx_document_reviews_reviewer_id ON document_reviews(reviewer_id)",
		"CREATE INDEX IF NOT EXISTS idx_document_reviews_status ON document_reviews(status)",

		// Regulation indexes
		"CREATE INDEX IF NOT EXISTS idx_laws_and_rules_slug ON laws_and_rules(slug)",
		"CREATE INDEX IF NOT EXISTS idx_laws_and_rules_status ON laws_and_rules(status)",
		"CREATE INDEX IF NOT EXISTS idx_laws_and_rules_agency_id ON laws_and_rules(agency_id)",
		"CREATE INDEX IF NOT EXISTS idx_laws_and_rules_effective_date ON laws_and_rules(effective_date)",
		"CREATE INDEX IF NOT EXISTS idx_laws_and_rules_cfr_title ON laws_and_rules(cfr_title)",
		"CREATE INDEX IF NOT EXISTS idx_laws_and_rules_cfr_part ON laws_and_rules(cfr_part)",

		// Regulation version indexes
		"CREATE INDEX IF NOT EXISTS idx_regulation_document_versions_law_rule_id ON regulation_document_versions(law_rule_id)",
		"CREATE INDEX IF NOT EXISTS idx_regulation_document_versions_version_number ON regulation_document_versions(version_number)",
		"CREATE INDEX IF NOT EXISTS idx_regulation_document_versions_status ON regulation_document_versions(status)",
		"CREATE INDEX IF NOT EXISTS idx_regulation_document_versions_is_current ON regulation_document_versions(is_current)",
		"CREATE INDEX IF NOT EXISTS idx_regulation_document_versions_effective_date ON regulation_document_versions(effective_date)",

		// Summary indexes
		"CREATE INDEX IF NOT EXISTS idx_summaries_entity_type ON summaries(entity_type)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_entity_id ON summaries(entity_id)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_action_type ON summaries(action_type)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_summary_type ON summaries(summary_type)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_publication_date ON summaries(publication_date)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_is_public ON summaries(is_public)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_is_featured ON summaries(is_featured)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_created_at ON summaries(created_at)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_priority ON summaries(priority)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_agency_id ON summaries(agency_id)",
		"CREATE INDEX IF NOT EXISTS idx_summaries_category_id ON summaries(category_id)",
	}

	for _, index := range indexes {
		if err := DB.Exec(index).Error; err != nil {
			log.Printf("Warning: Could not create index: %s, error: %v", index, err)
		}
	}

	return nil
}

// createFullTextSearchIndexes creates full-text search indexes
func createFullTextSearchIndexes() error {
	// Create full-text search index for documents
	searchIndexes := []string{
		`CREATE INDEX IF NOT EXISTS idx_documents_search 
		 ON documents USING gin(to_tsvector('english', title || ' ' || abstract || ' ' || content))`,

		`CREATE INDEX IF NOT EXISTS idx_documents_title_search 
		 ON documents USING gin(to_tsvector('english', title))`,

		`CREATE INDEX IF NOT EXISTS idx_agencies_search 
		 ON agencies USING gin(to_tsvector('english', name || ' ' || description))`,

		`CREATE INDEX IF NOT EXISTS idx_categories_search 
		 ON categories USING gin(to_tsvector('english', name || ' ' || description))`,
	}

	for _, index := range searchIndexes {
		if err := DB.Exec(index).Error; err != nil {
			log.Printf("Warning: Could not create search index: %v", err)
		}
	}

	return nil
}

// Close closes the database connection
func Close() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	return sqlDB.Close()
}

// GetDB returns the database instance
func GetDB() *gorm.DB {
	return DB
}

// runManualMigrations executes SQL migration files instead of using GORM AutoMigrate
func runManualMigrations(db *gorm.DB) error {
	// Check if migrations have already been run by looking for the documents table
	var count int64
	err := db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'documents' AND table_schema = CURRENT_SCHEMA()").Scan(&count).Error
	if err != nil {
		return fmt.Errorf("failed to check existing tables: %w", err)
	}

	// If documents table exists, assume migrations are already run
	if count > 0 {
		log.Println("Database tables already exist, skipping manual migrations")

		// Check if role management tables exist, if not run the role migration
		var roleCount int64
		err := db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'roles' AND table_schema = CURRENT_SCHEMA()").Scan(&roleCount).Error
		if err == nil && roleCount == 0 {
			log.Println("Running role management migration...")
			if err := runRoleMigration(db); err != nil {
				log.Printf("Warning: Role migration failed: %v", err)
			}
		}

		// Check if task management tables exist, if not run the task migration
		var taskCount int64
		err = db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'tasks' AND table_schema = CURRENT_SCHEMA()").Scan(&taskCount).Error
		if err == nil && taskCount == 0 {
			log.Println("Running task management migration...")
			if err := runTaskMigration(db); err != nil {
				log.Printf("Warning: Task migration failed: %v", err)
			}
		}

		// Check if documents table has is_public column, if not run the visibility migration
		var columnCount int64
		err = db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'documents' AND column_name = 'is_public' AND table_schema = CURRENT_SCHEMA()").Scan(&columnCount).Error
		if err == nil && columnCount == 0 {
			log.Println("Running document visibility migration...")
			if err := runVisibilityMigration(db); err != nil {
				log.Printf("Warning: Visibility migration failed: %v", err)
			}
		}

		// Check if proceeding tables exist, if not run the proceeding migration
		var proceedingCount int64
		err = db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'proceedings' AND table_schema = CURRENT_SCHEMA()").Scan(&proceedingCount).Error
		if err == nil && proceedingCount == 0 {
			log.Println("Running proceeding system migration...")
			if err := runProceedingMigration(db); err != nil {
				log.Printf("Warning: Proceeding migration failed: %v", err)
			}
		}

		// Check if proceeding junction tables exist, if not create them
		var proceedingDocumentsCount int64
		err = db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'proceeding_documents' AND table_schema = CURRENT_SCHEMA()").Scan(&proceedingDocumentsCount).Error
		if err == nil && proceedingDocumentsCount == 0 {
			log.Println("Creating missing proceeding junction tables...")
			junctionTablesSQL := `
				CREATE TABLE IF NOT EXISTS proceeding_documents (
					proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
					document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
					relationship_type VARCHAR(50) DEFAULT 'related',
					notes TEXT,
					PRIMARY KEY (proceeding_id, document_id)
				);

				CREATE TABLE IF NOT EXISTS proceeding_tasks (
					proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
					task_id INTEGER NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
					relationship_type VARCHAR(50) DEFAULT 'related',
					notes TEXT,
					PRIMARY KEY (proceeding_id, task_id)
				);

				CREATE TABLE IF NOT EXISTS proceeding_regulations (
					proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
					regulation_id INTEGER NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
					created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
					relationship_type VARCHAR(50) DEFAULT 'related',
					notes TEXT,
					PRIMARY KEY (proceeding_id, regulation_id)
				);`
			if err := db.Exec(junctionTablesSQL).Error; err != nil {
				log.Printf("Warning: Failed to create proceeding junction tables: %v", err)
			} else {
				log.Println("Proceeding junction tables created successfully")
			}
		}

		// Check if finance tables exist, if not run the finance migration
		var financeCount int64
		err = db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'finances' AND table_schema = CURRENT_SCHEMA()").Scan(&financeCount).Error
		if err == nil && financeCount == 0 {
			log.Println("Running finance system migration...")
			if err := runFinanceMigration(db); err != nil {
				log.Printf("Warning: Finance migration failed: %v", err)
			}
		}

		// Check if enterprise content management tables exist, if not run the enterprise migration
		var enterpriseCount int64
		err = db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'content_repositories' AND table_schema = CURRENT_SCHEMA()").Scan(&enterpriseCount).Error
		if err == nil && enterpriseCount == 0 {
			log.Println("Running enterprise system migration...")
			if err := runEnterpriseMigration(db); err != nil {
				log.Printf("Warning: Enterprise migration failed: %v", err)
			}
		}

		// Update laws_and_rules table structure to match model
		log.Println("Updating laws_and_rules table structure...")

		// Add missing columns to laws_and_rules table
		missingColumns := []struct {
			name       string
			definition string
		}{
			{"short_title", "TEXT"},
			{"slug", "TEXT"},
			{"type", "TEXT NOT NULL DEFAULT 'regulation'"},
			{"current_document_version_id", "BIGINT"},
			{"public_law_number", "TEXT"},
			{"usc_title", "TEXT"},
			{"cfr_part", "TEXT"},
			{"enactment_date", "TIMESTAMPTZ"},
			{"termination_date", "TIMESTAMPTZ"},
			{"publication_date", "TIMESTAMPTZ"},
			{"notes", "TEXT"},
			{"hierarchy_level", "TEXT DEFAULT 'regulation'"},
			{"parent_id", "BIGINT"},
			{"order_in_parent", "INTEGER DEFAULT 0"},
		}

		for _, col := range missingColumns {
			var colExists int64
			err = db.Raw(`
				SELECT COUNT(*) FROM information_schema.columns
				WHERE table_name = 'laws_and_rules' AND column_name = ? AND table_schema = CURRENT_SCHEMA()
			`, col.name).Scan(&colExists).Error
			if err != nil {
				log.Printf("Error checking for %s column: %v", col.name, err)
				continue
			}

			if colExists == 0 {
				log.Printf("Adding %s column to laws_and_rules table...", col.name)
				err = db.Exec(fmt.Sprintf(`ALTER TABLE laws_and_rules ADD COLUMN %s %s`, col.name, col.definition)).Error
				if err != nil {
					log.Printf("Error adding %s column: %v", col.name, err)
				}
			}
		}

		// Add missing columns to regulation_document_versions table
		log.Println("Updating regulation_document_versions table structure...")
		versionColumns := []struct {
			name       string
			definition string
		}{
			{"status", "TEXT DEFAULT 'draft'"},
			{"is_current", "BOOLEAN DEFAULT false"},
		}

		for _, col := range versionColumns {
			var count int64
			err := db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'regulation_document_versions' AND column_name = ? AND table_schema = CURRENT_SCHEMA()", col.name).Scan(&count).Error
			if err == nil && count == 0 {
				log.Printf("Adding %s column to regulation_document_versions table...", col.name)
				err = db.Exec(fmt.Sprintf(`ALTER TABLE regulation_document_versions ADD COLUMN %s %s`, col.name, col.definition)).Error
				if err != nil {
					log.Printf("Error adding %s column: %v", col.name, err)
				}
			}
		}

		// Add missing columns to summaries table
		log.Println("Updating summaries table structure...")
		summaryColumns := []struct {
			name       string
			definition string
		}{
			{"action_type", "TEXT"},
			{"is_featured", "BOOLEAN DEFAULT false"},
			{"agency_id", "BIGINT REFERENCES agencies(id)"},
			{"category_id", "BIGINT REFERENCES categories(id)"},
		}

		for _, col := range summaryColumns {
			var count int64
			err := db.Raw("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'summaries' AND column_name = ? AND table_schema = CURRENT_SCHEMA()", col.name).Scan(&count).Error
			if err == nil && count == 0 {
				log.Printf("Adding %s column to summaries table...", col.name)
				err = db.Exec(fmt.Sprintf(`ALTER TABLE summaries ADD COLUMN %s %s`, col.name, col.definition)).Error
				if err != nil {
					log.Printf("Error adding %s column: %v", col.name, err)
				}
			}
		}

		return nil
	}

	log.Println("Running manual database migrations...")

	// Migration 1: Initial schema
	schema := `
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create agencies table
CREATE TABLE agencies (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name TEXT NOT NULL,
    short_name TEXT UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    website TEXT,
    email TEXT,
    phone TEXT,
    address TEXT,
    city TEXT,
    state TEXT,
    zip_code TEXT,
    country TEXT DEFAULT 'US',
    parent_agency_id BIGINT,
    is_active BOOLEAN DEFAULT true,
    agency_type TEXT,
    jurisdiction TEXT,
    established_at TIMESTAMPTZ,
    logo_url TEXT,
    primary_color TEXT,
    secondary_color TEXT
);

-- Create users table
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    username TEXT NOT NULL UNIQUE,
    email TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    title TEXT,
    department TEXT,
    organization TEXT,
    phone TEXT,
    bio TEXT,
    role TEXT DEFAULT 'viewer',
    is_active BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMPTZ,
    agency_id BIGINT REFERENCES agencies(id)
);

-- Create user sessions table
CREATE TABLE user_sessions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    user_id BIGINT NOT NULL REFERENCES users(id),
    token TEXT NOT NULL UNIQUE,
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT true,
    ip_address TEXT,
    user_agent TEXT
);

-- Create user preferences table
CREATE TABLE user_preferences (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    user_id BIGINT NOT NULL UNIQUE REFERENCES users(id),
    email_notifications BOOLEAN DEFAULT true,
    document_alerts BOOLEAN DEFAULT true,
    weekly_digest BOOLEAN DEFAULT false,
    comment_notifications BOOLEAN DEFAULT true,
    documents_per_page BIGINT DEFAULT 25,
    default_view TEXT DEFAULT 'list',
    theme TEXT DEFAULT 'light',
    language TEXT DEFAULT 'en',
    timezone TEXT DEFAULT 'UTC',
    default_search_sort TEXT DEFAULT 'relevance',
    save_search_history BOOLEAN DEFAULT true,
    auto_complete_enabled BOOLEAN DEFAULT true
);

-- Create categories table
CREATE TABLE categories (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    parent_category_id BIGINT,
    color TEXT,
    icon TEXT,
    sort_order BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true
);

-- Create tags table
CREATE TABLE tags (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    color TEXT,
    usage_count BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by_id BIGINT REFERENCES users(id)
);

-- Create subjects table
CREATE TABLE subjects (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    name TEXT NOT NULL UNIQUE,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    cfr_title TEXT,
    parent_subject_id BIGINT,
    sort_order BIGINT DEFAULT 0,
    is_active BOOLEAN DEFAULT true
);

-- Create documents table
CREATE TABLE documents (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    -- Basic document information
    title TEXT NOT NULL,
    slug TEXT NOT NULL UNIQUE,
    abstract TEXT,
    content TEXT,
    type TEXT NOT NULL,
    status TEXT DEFAULT 'draft',

    -- Federal Register specific
    fr_document_number TEXT UNIQUE,
    fr_citation TEXT,
    cfr_citations TEXT,

    -- Important dates
    publication_date TIMESTAMPTZ,
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    comment_due_date TIMESTAMPTZ,

    -- Document metadata
    page_count BIGINT,
    word_count BIGINT,
    language TEXT DEFAULT 'en',
    original_format TEXT,
    file_size BIGINT,
    checksum TEXT,

    -- Relationships
    agency_id BIGINT NOT NULL REFERENCES agencies(id),
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    updated_by_id BIGINT REFERENCES users(id),
    parent_document_id BIGINT,

    -- Search and analytics
    search_vector TSVECTOR,
    view_count BIGINT DEFAULT 0,
    download_count BIGINT DEFAULT 0,

    -- Workflow
    workflow_stage TEXT,
    approved_at TIMESTAMPTZ,
    approved_by_id BIGINT REFERENCES users(id),
    published_at TIMESTAMPTZ,
    published_by_id BIGINT REFERENCES users(id),

    -- Regulatory information
    regulatory_identifier TEXT,
    docket_number TEXT,
    significant_rule BOOLEAN DEFAULT false,
    economic_impact TEXT,
    small_entity_impact BOOLEAN DEFAULT false,

    -- Comments
    accepts_comments BOOLEAN DEFAULT false,
    comment_count BIGINT DEFAULT 0,
    comment_instructions TEXT,
    public_hearing_date TIMESTAMPTZ,
    public_hearing_info TEXT
);

-- Create junction tables for many-to-many relationships
CREATE TABLE document_category_assignments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    category_id BIGINT REFERENCES categories(id) ON DELETE CASCADE,
    assigned_by_id BIGINT REFERENCES users(id),
    UNIQUE(document_id, category_id)
);

CREATE TABLE document_tag_assignments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    tag_id BIGINT REFERENCES tags(id) ON DELETE CASCADE,
    assigned_by_id BIGINT REFERENCES users(id),
    UNIQUE(document_id, tag_id)
);

CREATE TABLE document_subject_assignments (
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    subject_id BIGINT REFERENCES subjects(id) ON DELETE CASCADE,
    PRIMARY KEY (document_id, subject_id)
);

-- Create laws_and_rules table for regulations
CREATE TABLE laws_and_rules (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    title TEXT NOT NULL,
    short_title TEXT,
    slug TEXT NOT NULL UNIQUE,
    description TEXT,
    type TEXT NOT NULL DEFAULT 'regulation',
    status TEXT DEFAULT 'draft',
    current_document_version_id BIGINT,

    -- Legal identifiers
    public_law_number TEXT,
    regulatory_identifier TEXT,
    cfr_title TEXT,
    usc_title TEXT,
    docket_number TEXT,

    -- Dates
    enactment_date TIMESTAMPTZ,
    effective_date TIMESTAMPTZ,
    termination_date TIMESTAMPTZ,
    publication_date TIMESTAMPTZ,

    -- Relationships
    agency_id BIGINT REFERENCES agencies(id),
    created_by_id BIGINT REFERENCES users(id),

    -- Metadata
    notes TEXT,
    is_significant BOOLEAN DEFAULT false,

    -- Hierarchical structure
    hierarchy_level TEXT DEFAULT 'regulation',
    parent_id BIGINT REFERENCES laws_and_rules(id),
    order_in_parent INTEGER DEFAULT 0
);

-- Create regulation_document_versions table for regulation versioning
CREATE TABLE regulation_document_versions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    law_rule_id BIGINT REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    version_number TEXT NOT NULL,
    title TEXT NOT NULL,
    content TEXT,
    change_summary TEXT,
    effective_date TIMESTAMPTZ,
    status TEXT DEFAULT 'draft',
    created_by_id BIGINT REFERENCES users(id),
    approved_at TIMESTAMPTZ,
    approved_by_id BIGINT REFERENCES users(id),
    published_at TIMESTAMPTZ,
    published_by_id BIGINT REFERENCES users(id),
    is_current BOOLEAN DEFAULT false,
    UNIQUE(law_rule_id, version_number)
);

-- Create summaries table for news-like activity summaries
CREATE TABLE summaries (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    -- Basic summary information
    title TEXT NOT NULL,
    abstract TEXT,
    summary_type TEXT NOT NULL DEFAULT 'news',

    -- Entity information
    entity_type TEXT NOT NULL,
    entity_id BIGINT NOT NULL,
    action_type TEXT NOT NULL,

    -- Publication information
    publication_date TIMESTAMPTZ,
    is_public BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,

    -- Metadata
    view_count BIGINT DEFAULT 0,
    priority INTEGER DEFAULT 0,
    tags TEXT,
    external_link TEXT,

    -- Relationships
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    agency_id BIGINT REFERENCES agencies(id),
    category_id BIGINT REFERENCES categories(id)
);`

	if err := db.Exec(schema).Error; err != nil {
		return fmt.Errorf("failed to create initial schema: %w", err)
	}

	log.Println("Manual database migrations completed successfully")
	return nil
}

// runRoleMigration runs the role management system migration
func runRoleMigration(db *gorm.DB) error {
	roleMigrationSQL := `
-- Role Management System Migration
-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_system_role BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(150) NOT NULL,
    description TEXT,
    resource VARCHAR(50) NOT NULL,
    action VARCHAR(50) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
    id SERIAL PRIMARY KEY,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id INTEGER NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS user_roles (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by INTEGER REFERENCES users(id),
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);`

	// Execute the migration
	if err := db.Exec(roleMigrationSQL).Error; err != nil {
		return fmt.Errorf("failed to create role tables: %w", err)
	}

	// Insert system roles and permissions
	if err := insertRoleData(db); err != nil {
		return fmt.Errorf("failed to insert role data: %w", err)
	}

	return nil
}

// insertRoleData inserts system roles, permissions, and role assignments
func insertRoleData(db *gorm.DB) error {
	// Insert system roles
	rolesSQL := `
INSERT INTO roles (name, display_name, description, is_system_role) VALUES
('admin', 'Administrator', 'Full system access with all permissions', TRUE),
('editor', 'Editor', 'Can create, edit, and manage documents and regulations', TRUE),
('viewer', 'Viewer', 'Read-only access to public and assigned content', TRUE),
('agency_manager', 'Agency Manager', 'Can manage agency information and agency-specific content', TRUE),
('category_manager', 'Category Manager', 'Can manage categories and category-specific content', TRUE),
('publisher', 'Publisher', 'Can publish and approve documents for public release', TRUE),
('reviewer', 'Reviewer', 'Can review and provide feedback on documents', TRUE)
ON CONFLICT (name) DO NOTHING;`

	if err := db.Exec(rolesSQL).Error; err != nil {
		return fmt.Errorf("failed to insert roles: %w", err)
	}

	// Insert permissions
	permissionsSQL := `
INSERT INTO permissions (name, display_name, description, resource, action) VALUES
-- User management
('users.create', 'Create Users', 'Create new user accounts', 'users', 'create'),
('users.read', 'View Users', 'View user information', 'users', 'read'),
('users.update', 'Update Users', 'Edit user information', 'users', 'update'),
('users.delete', 'Delete Users', 'Delete user accounts', 'users', 'delete'),
('users.manage_roles', 'Manage User Roles', 'Assign and remove user roles', 'users', 'manage_roles'),
-- Document management
('documents.create', 'Create Documents', 'Create new documents', 'documents', 'create'),
('documents.read', 'View Documents', 'View documents', 'documents', 'read'),
('documents.update', 'Update Documents', 'Edit documents', 'documents', 'update'),
('documents.delete', 'Delete Documents', 'Delete documents', 'documents', 'delete'),
('documents.publish', 'Publish Documents', 'Publish documents for public access', 'documents', 'publish'),
('documents.approve', 'Approve Documents', 'Approve documents for publication', 'documents', 'approve'),
('documents.review', 'Review Documents', 'Review and comment on documents', 'documents', 'review'),
-- Agency management
('agencies.create', 'Create Agencies', 'Create new agencies', 'agencies', 'create'),
('agencies.read', 'View Agencies', 'View agency information', 'agencies', 'read'),
('agencies.update', 'Update Agencies', 'Edit agency information', 'agencies', 'update'),
('agencies.delete', 'Delete Agencies', 'Delete agencies', 'agencies', 'delete'),
('agencies.manage', 'Manage Agency Content', 'Manage agency-specific content', 'agencies', 'manage'),
-- System management
('system.admin', 'System Administration', 'Full system administration access', 'system', 'admin')
ON CONFLICT (name) DO NOTHING;`

	if err := db.Exec(permissionsSQL).Error; err != nil {
		return fmt.Errorf("failed to insert permissions: %w", err)
	}

	// Assign permissions to admin role
	adminPermissionsSQL := `
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'admin'
ON CONFLICT (role_id, permission_id) DO NOTHING;`

	if err := db.Exec(adminPermissionsSQL).Error; err != nil {
		return fmt.Errorf("failed to assign admin permissions: %w", err)
	}

	return nil
}

// runTaskMigration runs the task management system migration
func runTaskMigration(db *gorm.DB) error {
	taskMigrationSQL := `
-- Task Management System Migration
-- Create tasks table
CREATE TABLE tasks (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    -- Basic task information
    title TEXT NOT NULL,
    description TEXT,
    type TEXT NOT NULL DEFAULT 'general',
    status TEXT NOT NULL DEFAULT 'pending',
    priority TEXT NOT NULL DEFAULT 'medium',

    -- Timing information
    due_date TIMESTAMPTZ,
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    duration INTEGER, -- Duration in minutes
    is_all_day BOOLEAN NOT NULL DEFAULT false,
    time_zone TEXT NOT NULL DEFAULT 'UTC',

    -- Recurrence information
    is_recurring BOOLEAN NOT NULL DEFAULT false,
    recurrence_rule TEXT, -- RRULE format
    recurrence_end TIMESTAMPTZ,
    parent_task_id BIGINT REFERENCES tasks(id) ON DELETE CASCADE,

    -- Source information (what triggered this task)
    source_type TEXT, -- "document", "regulation", "manual", "parsed_text"
    source_id BIGINT, -- ID of the source entity
    source_text TEXT, -- Original text that generated this task
    parsed_from_text BOOLEAN NOT NULL DEFAULT false,

    -- Relationships
    assigned_to_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    created_by_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    document_id BIGINT REFERENCES documents(id) ON DELETE CASCADE,
    regulation_id BIGINT REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    agency_id BIGINT REFERENCES agencies(id) ON DELETE SET NULL,
    category_id BIGINT REFERENCES categories(id) ON DELETE SET NULL,

    -- Notification and reminder settings
    reminder_enabled BOOLEAN NOT NULL DEFAULT false,
    reminder_time TIMESTAMPTZ,
    notification_sent BOOLEAN NOT NULL DEFAULT false,

    -- Additional metadata
    location TEXT,
    url TEXT,
    notes TEXT,
    tags TEXT, -- Comma-separated tags
    is_public BOOLEAN NOT NULL DEFAULT false,
    completed_at TIMESTAMPTZ,
    completed_by BIGINT REFERENCES users(id) ON DELETE SET NULL
);

-- Create task_attachments table
CREATE TABLE task_attachments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    task_id BIGINT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    mime_type TEXT,
    uploaded_by BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- Create task_comments table
CREATE TABLE task_comments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    task_id BIGINT NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    author_id BIGINT NOT NULL REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX idx_tasks_deleted_at ON tasks(deleted_at);
CREATE INDEX idx_tasks_assigned_to_id ON tasks(assigned_to_id);
CREATE INDEX idx_tasks_created_by_id ON tasks(created_by_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_type ON tasks(type);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_start_date ON tasks(start_date);
CREATE INDEX idx_tasks_document_id ON tasks(document_id);
CREATE INDEX idx_tasks_regulation_id ON tasks(regulation_id);
CREATE INDEX idx_tasks_agency_id ON tasks(agency_id);
CREATE INDEX idx_tasks_category_id ON tasks(category_id);
CREATE INDEX idx_tasks_parsed_from_text ON tasks(parsed_from_text);
CREATE INDEX idx_tasks_is_public ON tasks(is_public);
CREATE INDEX idx_tasks_source_type ON tasks(source_type);
CREATE INDEX idx_tasks_source_id ON tasks(source_id);

-- Indexes for task_attachments
CREATE INDEX idx_task_attachments_deleted_at ON task_attachments(deleted_at);
CREATE INDEX idx_task_attachments_task_id ON task_attachments(task_id);
CREATE INDEX idx_task_attachments_uploaded_by ON task_attachments(uploaded_by);

-- Indexes for task_comments
CREATE INDEX idx_task_comments_deleted_at ON task_comments(deleted_at);
CREATE INDEX idx_task_comments_task_id ON task_comments(task_id);
CREATE INDEX idx_task_comments_author_id ON task_comments(author_id);

-- Add constraints for task types
ALTER TABLE tasks ADD CONSTRAINT check_task_type 
    CHECK (type IN (
        'review', 'deadline', 'hearing', 'comment', 'general', 
        'reminder', 'follow_up', 'task', 'effective', 'termination', 'meeting'
    ));

-- Add constraints for task status
ALTER TABLE tasks ADD CONSTRAINT check_task_status
    CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled', 'overdue'));

-- Add constraints for task priority
ALTER TABLE tasks ADD CONSTRAINT check_task_priority
    CHECK (priority IN ('low', 'medium', 'high', 'urgent'));

-- Add constraints for source type
ALTER TABLE tasks ADD CONSTRAINT check_source_type
    CHECK (source_type IS NULL OR source_type IN ('document', 'regulation', 'manual', 'parsed_text'));

-- Add constraint to ensure end_date is after start_date
ALTER TABLE tasks ADD CONSTRAINT check_date_order
    CHECK (start_date IS NULL OR end_date IS NULL OR end_date >= start_date);

-- Add constraint to ensure due_date is reasonable
ALTER TABLE tasks ADD CONSTRAINT check_due_date_reasonable
    CHECK (due_date IS NULL OR due_date >= '2020-01-01'::date);

-- Add constraint for duration (must be positive)
ALTER TABLE tasks ADD CONSTRAINT check_duration_positive
    CHECK (duration IS NULL OR duration > 0);`

	// Execute the migration
	if err := db.Exec(taskMigrationSQL).Error; err != nil {
		return fmt.Errorf("failed to create task tables: %w", err)
	}

	log.Println("Task management migration completed successfully")
	return nil
}

// runVisibilityMigration runs the document visibility and access control migration
func runVisibilityMigration(db *gorm.DB) error {
	visibilityMigrationSQL := `
-- Migration 009: Add document visibility and access control fields
-- This migration adds missing fields to the documents table that are defined in the Document model

-- Add visibility and access control fields to documents table
ALTER TABLE documents
ADD COLUMN IF NOT EXISTS visibility_level INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS is_public BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS regulation_count INTEGER DEFAULT 0;

-- Add comment to explain visibility levels
COMMENT ON COLUMN documents.visibility_level IS '1=public, 2=restricted, 3=confidential';
COMMENT ON COLUMN documents.is_public IS 'Whether the document is publicly accessible';
COMMENT ON COLUMN documents.regulation_count IS 'Number of regulations associated with this document';

-- Create document_files table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_files (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,

    file_name TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT,
    mime_type TEXT,
    checksum TEXT,

    -- File metadata
    description TEXT,
    is_public BOOLEAN DEFAULT true,
    is_primary BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,

    -- Upload information
    uploaded_by_id BIGINT NOT NULL REFERENCES users(id),

    -- Download tracking
    download_count INTEGER DEFAULT 0
);

-- Create document_comments table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_comments (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,

    -- Commenter information
    commenter_name TEXT NOT NULL,
    commenter_email TEXT,
    organization TEXT,

    -- Comment content
    subject TEXT,
    content TEXT NOT NULL,

    -- Comment metadata
    is_public BOOLEAN DEFAULT true,
    is_verified BOOLEAN DEFAULT false,
    ip_address TEXT,

    -- Moderation
    is_moderated BOOLEAN DEFAULT false,
    moderated_by BIGINT REFERENCES users(id),
    moderated_at TIMESTAMPTZ
);

-- Create document_reviews table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_reviews (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    reviewer_id BIGINT NOT NULL REFERENCES users(id),

    status TEXT, -- pending, approved, rejected, needs_changes
    comments TEXT,
    rating INTEGER, -- 1-5 scale

    reviewed_at TIMESTAMPTZ
);

-- Create document_versions table if it doesn't exist
CREATE TABLE IF NOT EXISTS document_versions (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    document_id BIGINT NOT NULL REFERENCES documents(id) ON DELETE CASCADE,

    version_number INTEGER NOT NULL,
    title TEXT,
    content TEXT,
    change_log TEXT,
    is_current BOOLEAN DEFAULT false,

    created_by_id BIGINT NOT NULL REFERENCES users(id)
);

-- Create summaries table if it doesn't exist
CREATE TABLE IF NOT EXISTS summaries (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,

    title TEXT NOT NULL,
    content TEXT NOT NULL,
    summary_type TEXT NOT NULL, -- document_change, regulation_change, agency_change, category_change
    entity_type TEXT NOT NULL,  -- document, regulation, agency, category
    entity_id BIGINT NOT NULL,

    -- Publication control
    is_public BOOLEAN DEFAULT true,
    publication_date TIMESTAMPTZ,

    -- Metadata
    created_by_id BIGINT NOT NULL REFERENCES users(id),
    priority INTEGER DEFAULT 1, -- 1=low, 2=medium, 3=high

    -- Content details
    change_description TEXT,
    impact_assessment TEXT,
    related_entities TEXT -- JSON array of related entity references
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_is_public ON documents(is_public);
CREATE INDEX IF NOT EXISTS idx_documents_visibility_level ON documents(visibility_level);
CREATE INDEX IF NOT EXISTS idx_documents_publication_date ON documents(publication_date);
CREATE INDEX IF NOT EXISTS idx_document_files_document_id ON document_files(document_id);
CREATE INDEX IF NOT EXISTS idx_document_comments_document_id ON document_comments(document_id);
CREATE INDEX IF NOT EXISTS idx_document_reviews_document_id ON document_reviews(document_id);
CREATE INDEX IF NOT EXISTS idx_document_versions_document_id ON document_versions(document_id);
CREATE INDEX IF NOT EXISTS idx_summaries_entity_type_id ON summaries(entity_type, entity_id);
CREATE INDEX IF NOT EXISTS idx_summaries_is_public ON summaries(is_public);
CREATE INDEX IF NOT EXISTS idx_summaries_publication_date ON summaries(publication_date);`

	// Execute the migration
	if err := db.Exec(visibilityMigrationSQL).Error; err != nil {
		return fmt.Errorf("failed to create visibility tables and columns: %w", err)
	}

	log.Println("Document visibility migration completed successfully")
	return nil
}

// runProceedingMigration runs the proceeding system migration
func runProceedingMigration(db *gorm.DB) error {
	// Read the migration file content
	proceedingMigrationSQL := `
-- Migration 010: Proceeding System
-- Creates tables for the formal proceeding management system
-- Implements the 7 key requirements for proceedings

-- Create proceedings table (main proceeding entity)
CREATE TABLE IF NOT EXISTS proceedings (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,

    -- Unique identification (requirement 4)
    name VARCHAR(255) NOT NULL,
    initiation_date TIMESTAMP WITH TIME ZONE NOT NULL,
    unique_id VARCHAR(500) NOT NULL UNIQUE, -- Generated: "Name YYYY-MM-DD"

    -- Basic proceeding information
    description TEXT,
    objective TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'planning' CHECK (status IN ('planning', 'active', 'suspended', 'completed', 'cancelled', 'under_review')),
    priority VARCHAR(50) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent', 'critical')),

    -- Administrative proceeding requirements
    requires_mandatory_review BOOLEAN DEFAULT TRUE,
    minimum_steps_required INTEGER DEFAULT 5,
    sequential_execution BOOLEAN DEFAULT TRUE,

    -- PRP Alignment (requirement 6)
    prp_sections TEXT, -- Comma-separated PRP section references
    prp_alignment TEXT NOT NULL, -- Description of PRP correlation
    new_prp_elements TEXT, -- New PRP elements to be developed

    -- Timing information
    planned_start_date TIMESTAMP WITH TIME ZONE,
    actual_start_date TIMESTAMP WITH TIME ZONE,
    planned_end_date TIMESTAMP WITH TIME ZONE,
    actual_end_date TIMESTAMP WITH TIME ZONE,
    estimated_duration INTEGER, -- Duration in days
    actual_duration INTEGER, -- Duration in days

    -- Review requirements (requirement 5)
    review_required BOOLEAN DEFAULT TRUE,
    review_scheduled BOOLEAN DEFAULT FALSE,
    review_date TIMESTAMP WITH TIME ZONE,
    review_completed BOOLEAN DEFAULT FALSE,
    review_completed_at TIMESTAMP WITH TIME ZONE,
    review_report_id INTEGER, -- Link to review report
    critical_milestones TEXT, -- JSON array of milestone dates

    -- Existing directives consideration (requirement 7)
    existing_directives_reviewed BOOLEAN DEFAULT FALSE,
    referenced_rules TEXT, -- JSON array of rule IDs
    referenced_orders TEXT, -- JSON array of order IDs
    referenced_directives TEXT, -- JSON array of directive IDs
    conflict_analysis TEXT, -- Analysis of potential conflicts
    integration_plan TEXT, -- Plan for integrating existing frameworks

    -- IFR Integration (requirement 6)
    requires_ifr BOOLEAN DEFAULT FALSE,
    ifr_triggered BOOLEAN DEFAULT FALSE,
    ifr_description TEXT,
    ifr_document_id INTEGER, -- Link to IFR document if created

    -- Relationships
    initiated_by_id INTEGER NOT NULL REFERENCES users(id),
    owner_id INTEGER NOT NULL REFERENCES users(id),

    -- Related entities
    agency_id INTEGER REFERENCES agencies(id),
    category_id INTEGER REFERENCES categories(id),

    -- Progress tracking
    total_steps INTEGER DEFAULT 0,
    completed_steps INTEGER DEFAULT 0,
    progress_percent DECIMAL(5,2) DEFAULT 0,
    current_step_order INTEGER,

    -- Additional metadata
    tags TEXT, -- Comma-separated tags
    is_public BOOLEAN DEFAULT FALSE,
    notes TEXT,
    attachments TEXT, -- JSON array of file paths
    external_refs TEXT -- JSON array of external references
);

-- Junction table for proceeding-document relationships
CREATE TABLE IF NOT EXISTS proceeding_documents (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    document_id INTEGER NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "source", "output", "reference"
    notes TEXT,
    PRIMARY KEY (proceeding_id, document_id)
);

-- Junction table for proceeding-task relationships
CREATE TABLE IF NOT EXISTS proceeding_tasks (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    task_id INTEGER NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "generated_by", "triggers", "blocks"
    notes TEXT,
    PRIMARY KEY (proceeding_id, task_id)
);

-- Junction table for proceeding-regulation relationships
CREATE TABLE IF NOT EXISTS proceeding_regulations (
    proceeding_id INTEGER NOT NULL REFERENCES proceedings(id) ON DELETE CASCADE,
    regulation_id INTEGER NOT NULL REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    relationship_type VARCHAR(50) DEFAULT 'related', -- "related", "source", "implements", "modifies"
    notes TEXT,
    PRIMARY KEY (proceeding_id, regulation_id)
);`

	// Execute the first part of migration
	if err := db.Exec(proceedingMigrationSQL).Error; err != nil {
		return fmt.Errorf("failed to create proceedings table: %w", err)
	}

	log.Println("Proceeding system migration completed successfully")
	return nil
}

// runFinanceMigration runs the finance system migration
func runFinanceMigration(db *gorm.DB) error {
	financeMigrationSQL := `
-- Finance System Migration
-- This migration creates the finances table for budget allocations linked to documents and regulations
-- and performance tracking for budget calculations

CREATE TABLE IF NOT EXISTS finances (
    id SERIAL PRIMARY KEY,
    amount NUMERIC(18,2) NOT NULL,
    year INTEGER NOT NULL,
    description TEXT,
    document_id INTEGER REFERENCES documents(id) ON DELETE SET NULL,
    regulation_id INTEGER REFERENCES laws_and_rules(id) ON DELETE SET NULL,
    budget_type VARCHAR(20) DEFAULT 'original' CHECK (budget_type IN ('original', 'actual')),
    performance_percentage NUMERIC(5,2) DEFAULT 100.00,
    is_auto_calculated BOOLEAN DEFAULT FALSE,
    source_finance_id INTEGER REFERENCES finances(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Performance tracking table
CREATE TABLE IF NOT EXISTS finance_performance (
    id SERIAL PRIMARY KEY,
    document_id INTEGER REFERENCES documents(id) ON DELETE CASCADE,
    regulation_id INTEGER REFERENCES laws_and_rules(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    performance_percentage NUMERIC(5,2) NOT NULL DEFAULT 100.00,
    performance_notes TEXT,
    evaluation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    evaluated_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Finance categories table
CREATE TABLE IF NOT EXISTS finance_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7) DEFAULT '#007bff',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Add category relationship to finances table
ALTER TABLE finances ADD COLUMN IF NOT EXISTS category_id INTEGER REFERENCES finance_categories(id) ON DELETE SET NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_finances_document_id ON finances(document_id);
CREATE INDEX IF NOT EXISTS idx_finances_regulation_id ON finances(regulation_id);
CREATE INDEX IF NOT EXISTS idx_finances_year ON finances(year);
CREATE INDEX IF NOT EXISTS idx_finances_budget_type ON finances(budget_type);
CREATE INDEX IF NOT EXISTS idx_finances_category_id ON finances(category_id);
CREATE INDEX IF NOT EXISTS idx_finance_performance_document_id ON finance_performance(document_id);
CREATE INDEX IF NOT EXISTS idx_finance_performance_regulation_id ON finance_performance(regulation_id);
CREATE INDEX IF NOT EXISTS idx_finance_performance_year ON finance_performance(year);

-- Insert default finance categories
INSERT INTO finance_categories (name, description, color) VALUES
('Operations', 'Operational expenses and budget allocations', '#28a745'),
('Compliance', 'Compliance and regulatory enforcement costs', '#dc3545'),
('Research', 'Research and development funding', '#007bff'),
('Infrastructure', 'Infrastructure and technology investments', '#6c757d'),
('Personnel', 'Personnel and human resources costs', '#fd7e14')
ON CONFLICT (name) DO NOTHING;
`

	if err := db.Exec(financeMigrationSQL).Error; err != nil {
		return fmt.Errorf("failed to create finance tables: %w", err)
	}

	log.Println("Finance system migration completed successfully")
	return nil
}

// runEnterpriseMigration creates enterprise management tables
func runEnterpriseMigration(db *gorm.DB) error {
	log.Println("Creating enterprise management tables...")

	enterpriseMigrationSQL := `
-- Content Repository Management
CREATE TABLE IF NOT EXISTS content_repositories (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	name VARCHAR(255) NOT NULL,
	description TEXT,
	type VARCHAR(50) DEFAULT 'document',
	storage_type VARCHAR(50) DEFAULT 'filesystem',
	storage_path TEXT,
	storage_config TEXT,
	max_size BIGINT DEFAULT 0,
	current_size BIGINT DEFAULT 0,
	max_files BIGINT DEFAULT 0,
	current_files BIGINT DEFAULT 0,
	classification VARCHAR(50) DEFAULT 'internal',
	encryption_key TEXT,
	access_policy TEXT,
	versioning_enabled BOOLEAN DEFAULT true,
	max_versions INTEGER DEFAULT 10,
	backup_enabled BOOLEAN DEFAULT true,
	backup_schedule VARCHAR(50) DEFAULT 'daily',
	retention_policy_id BIGINT,
	compliance_level VARCHAR(50) DEFAULT 'standard',
	is_active BOOLEAN DEFAULT true,
	last_accessed TIMESTAMPTZ,
	access_count BIGINT DEFAULT 0,
	metadata TEXT
);

-- Content Version Management
CREATE TABLE IF NOT EXISTS content_versions (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	document_id BIGINT NOT NULL REFERENCES documents(id),
	version_number VARCHAR(50) NOT NULL,
	title TEXT,
	content TEXT,
	content_hash VARCHAR(255),
	file_size BIGINT,
	mime_type VARCHAR(100),
	change_type VARCHAR(50),
	change_log TEXT,
	author_id BIGINT NOT NULL REFERENCES users(id),
	approval_status VARCHAR(50) DEFAULT 'pending',
	approved_by_id BIGINT REFERENCES users(id),
	approved_at TIMESTAMPTZ,
	rejection_reason TEXT,
	is_active BOOLEAN DEFAULT true,
	published_at TIMESTAMPTZ,
	deprecated_at TIMESTAMPTZ,
	metadata TEXT
);

-- Content Workflow Management
CREATE TABLE IF NOT EXISTS content_workflows (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	name VARCHAR(255) NOT NULL,
	description TEXT,
	type VARCHAR(50) DEFAULT 'approval',
	steps TEXT,
	rules TEXT,
	conditions TEXT,
	is_parallel BOOLEAN DEFAULT false,
	require_all BOOLEAN DEFAULT true,
	timeout_hours INTEGER DEFAULT 72,
	escalation_hours INTEGER DEFAULT 24,
	trigger_events TEXT,
	auto_start BOOLEAN DEFAULT false,
	is_active BOOLEAN DEFAULT true,
	last_executed TIMESTAMPTZ,
	execution_count BIGINT DEFAULT 0,
	success_rate DECIMAL(5,2) DEFAULT 0,
	metadata TEXT
);

-- Content Workflow Instances
CREATE TABLE IF NOT EXISTS content_workflow_instances (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	workflow_id BIGINT NOT NULL REFERENCES content_workflows(id),
	document_id BIGINT NOT NULL REFERENCES documents(id),
	instance_id VARCHAR(255) UNIQUE NOT NULL,
	status VARCHAR(50) DEFAULT 'running',
	current_step INTEGER DEFAULT 0,
	total_steps INTEGER,
	started_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	completed_at TIMESTAMPTZ,
	started_by_id BIGINT NOT NULL REFERENCES users(id),
	step_results TEXT,
	error_log TEXT,
	timeout_at TIMESTAMPTZ,
	escalation_at TIMESTAMPTZ,
	is_escalated BOOLEAN DEFAULT false,
	metadata TEXT
);

-- Content Collaboration
CREATE TABLE IF NOT EXISTS content_collaborations (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	document_id BIGINT NOT NULL REFERENCES documents(id),
	user_id BIGINT NOT NULL REFERENCES users(id),
	type VARCHAR(50) NOT NULL,
	permission VARCHAR(50) DEFAULT 'read',
	started_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	ended_at TIMESTAMPTZ,
	is_active BOOLEAN DEFAULT true,
	session_id VARCHAR(255),
	last_action TIMESTAMPTZ,
	action_count BIGINT DEFAULT 0,
	metadata TEXT
);

-- Chart of Accounts
CREATE TABLE IF NOT EXISTS chart_of_accounts (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	account_code VARCHAR(50) UNIQUE NOT NULL,
	account_name VARCHAR(255) NOT NULL,
	account_type VARCHAR(50) NOT NULL,
	description TEXT,
	parent_account_id BIGINT REFERENCES chart_of_accounts(id),
	level INTEGER DEFAULT 0,
	is_active BOOLEAN DEFAULT true,
	is_control_account BOOLEAN DEFAULT false,
	allow_posting BOOLEAN DEFAULT true,
	normal_balance VARCHAR(50) NOT NULL,
	current_balance DECIMAL(15,2) DEFAULT 0,
	opening_balance DECIMAL(15,2) DEFAULT 0,
	currency_code VARCHAR(3) DEFAULT 'USD',
	tax_code VARCHAR(50),
	reporting_code VARCHAR(50),
	gl_class VARCHAR(50),
	metadata TEXT
);

-- General Ledger
CREATE TABLE IF NOT EXISTS general_ledger (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	entry_number VARCHAR(100) UNIQUE NOT NULL,
	transaction_date DATE NOT NULL,
	posting_date DATE NOT NULL,
	account_id BIGINT NOT NULL REFERENCES chart_of_accounts(id),
	transaction_type VARCHAR(50) NOT NULL,
	debit_amount DECIMAL(15,2) DEFAULT 0,
	credit_amount DECIMAL(15,2) DEFAULT 0,
	reference_type VARCHAR(50),
	reference_id VARCHAR(100),
	description TEXT,
	document_id BIGINT REFERENCES documents(id),
	created_by_id BIGINT NOT NULL REFERENCES users(id),
	approved_by_id BIGINT REFERENCES users(id),
	approved_at TIMESTAMPTZ,
	status VARCHAR(50) DEFAULT 'pending',
	is_reconciled BOOLEAN DEFAULT false,
	reconciled_at TIMESTAMPTZ,
	currency_code VARCHAR(3) DEFAULT 'USD',
	exchange_rate DECIMAL(10,6) DEFAULT 1.0,
	base_currency_amount DECIMAL(15,2),
	metadata TEXT
);

-- Budget Plans
CREATE TABLE IF NOT EXISTS budget_plans (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	budget_code VARCHAR(100) UNIQUE NOT NULL,
	budget_name VARCHAR(255) NOT NULL,
	description TEXT,
	fiscal_year INTEGER NOT NULL,
	start_date DATE NOT NULL,
	end_date DATE NOT NULL,
	budget_type VARCHAR(50) DEFAULT 'operational',
	budget_category VARCHAR(50),
	planned_amount DECIMAL(15,2) NOT NULL,
	revised_amount DECIMAL(15,2),
	actual_amount DECIMAL(15,2) DEFAULT 0,
	committed_amount DECIMAL(15,2) DEFAULT 0,
	variance_amount DECIMAL(15,2) DEFAULT 0,
	variance_percent DECIMAL(5,2) DEFAULT 0,
	account_id BIGINT NOT NULL REFERENCES chart_of_accounts(id),
	department_id BIGINT,
	cost_center_id BIGINT,
	project_id BIGINT,
	status VARCHAR(50) DEFAULT 'draft',
	submitted_at TIMESTAMPTZ,
	approved_by_id BIGINT REFERENCES users(id),
	approved_at TIMESTAMPTZ,
	alert_threshold DECIMAL(5,2) DEFAULT 90,
	is_alert_enabled BOOLEAN DEFAULT true,
	last_alert_sent TIMESTAMPTZ,
	metadata TEXT
);

-- Cost Centers
CREATE TABLE IF NOT EXISTS cost_centers (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	cost_center_code VARCHAR(50) UNIQUE NOT NULL,
	name VARCHAR(255) NOT NULL,
	description TEXT,
	parent_cost_center_id BIGINT REFERENCES cost_centers(id),
	level INTEGER DEFAULT 0,
	manager_id BIGINT REFERENCES users(id),
	budget_amount DECIMAL(15,2) DEFAULT 0,
	actual_amount DECIMAL(15,2) DEFAULT 0,
	committed_amount DECIMAL(15,2) DEFAULT 0,
	allocation_method VARCHAR(50) DEFAULT 'direct',
	allocation_base VARCHAR(50),
	allocation_rate DECIMAL(10,4) DEFAULT 0,
	is_active BOOLEAN DEFAULT true,
	last_reviewed TIMESTAMPTZ,
	metadata TEXT
);

-- Financial Reports
CREATE TABLE IF NOT EXISTS financial_reports (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	report_code VARCHAR(100) UNIQUE NOT NULL,
	report_name VARCHAR(255) NOT NULL,
	report_type VARCHAR(100) NOT NULL,
	description TEXT,
	period_type VARCHAR(50) NOT NULL,
	start_date DATE NOT NULL,
	end_date DATE NOT NULL,
	generated_at TIMESTAMPTZ,
	generated_by_id BIGINT REFERENCES users(id),
	report_data TEXT,
	report_format VARCHAR(50) DEFAULT 'json',
	file_path TEXT,
	status VARCHAR(50) DEFAULT 'draft',
	reviewed_by_id BIGINT REFERENCES users(id),
	reviewed_at TIMESTAMPTZ,
	distribution_list TEXT,
	published_at TIMESTAMPTZ,
	metadata TEXT
);

-- Compliance Requirements
CREATE TABLE IF NOT EXISTS compliance_requirements (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	requirement_code VARCHAR(100) UNIQUE NOT NULL,
	title VARCHAR(255) NOT NULL,
	description TEXT,
	framework VARCHAR(50) NOT NULL,
	category VARCHAR(100),
	subcategory VARCHAR(100),
	risk_level VARCHAR(50) DEFAULT 'medium',
	control_type VARCHAR(50),
	implementation TEXT,
	testing_method TEXT,
	testing_frequency VARCHAR(50) DEFAULT 'annual',
	review_frequency VARCHAR(50) DEFAULT 'annual',
	effective_date DATE,
	expiration_date DATE,
	status VARCHAR(50) DEFAULT 'active',
	owner_id BIGINT REFERENCES users(id),
	metadata TEXT
);

-- Compliance Assessments
CREATE TABLE IF NOT EXISTS compliance_assessments (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	assessment_code VARCHAR(100) UNIQUE NOT NULL,
	title VARCHAR(255) NOT NULL,
	description TEXT,
	assessment_type VARCHAR(50) DEFAULT 'internal',
	framework VARCHAR(50) NOT NULL,
	scope TEXT,
	planned_start_date DATE,
	planned_end_date DATE,
	actual_start_date DATE,
	actual_end_date DATE,
	lead_assessor_id BIGINT NOT NULL REFERENCES users(id),
	assessment_team TEXT,
	status VARCHAR(50) DEFAULT 'planned',
	overall_score DECIMAL(5,2) DEFAULT 0,
	compliance_level VARCHAR(50),
	findings_count INTEGER DEFAULT 0,
	critical_count INTEGER DEFAULT 0,
	high_count INTEGER DEFAULT 0,
	medium_count INTEGER DEFAULT 0,
	low_count INTEGER DEFAULT 0,
	report_generated BOOLEAN DEFAULT false,
	report_path TEXT,
	report_date DATE,
	metadata TEXT
);

-- Compliance Findings
CREATE TABLE IF NOT EXISTS compliance_findings (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	finding_code VARCHAR(100) UNIQUE NOT NULL,
	title VARCHAR(255) NOT NULL,
	description TEXT,
	assessment_id BIGINT NOT NULL REFERENCES compliance_assessments(id),
	requirement_id BIGINT NOT NULL REFERENCES compliance_requirements(id),
	finding_type VARCHAR(50) DEFAULT 'deficiency',
	risk_level VARCHAR(50) NOT NULL,
	impact TEXT,
	likelihood VARCHAR(50) DEFAULT 'medium',
	evidence TEXT,
	document_ids TEXT,
	recommendation TEXT,
	remediation_plan TEXT,
	target_date DATE,
	responsible_id BIGINT REFERENCES users(id),
	status VARCHAR(50) DEFAULT 'open',
	resolution_date DATE,
	resolution_notes TEXT,
	verified_by_id BIGINT REFERENCES users(id),
	verified_at TIMESTAMPTZ,
	metadata TEXT
);

-- Risk Assessments
CREATE TABLE IF NOT EXISTS risk_assessments (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	risk_code VARCHAR(100) UNIQUE NOT NULL,
	risk_title VARCHAR(255) NOT NULL,
	risk_description TEXT,
	risk_category VARCHAR(100),
	inherent_risk VARCHAR(50) NOT NULL,
	residual_risk VARCHAR(50) NOT NULL,
	risk_appetite VARCHAR(50) DEFAULT 'medium',
	impact_score INTEGER DEFAULT 1,
	likelihood_score INTEGER DEFAULT 1,
	risk_score DECIMAL(5,2) DEFAULT 0,
	risk_owner_id BIGINT NOT NULL REFERENCES users(id),
	treatment_strategy VARCHAR(50),
	mitigation_plan TEXT,
	last_review_date DATE,
	next_review_date DATE,
	review_frequency VARCHAR(50) DEFAULT 'quarterly',
	status VARCHAR(50) DEFAULT 'active',
	metadata TEXT
);

-- Policy Management
CREATE TABLE IF NOT EXISTS policy_management (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	policy_code VARCHAR(100) UNIQUE NOT NULL,
	policy_title VARCHAR(255) NOT NULL,
	policy_type VARCHAR(50) DEFAULT 'policy',
	description TEXT,
	content TEXT,
	version VARCHAR(50) DEFAULT '1.0',
	parent_policy_id BIGINT REFERENCES policy_management(id),
	framework VARCHAR(50),
	requirement_ids TEXT,
	owner_id BIGINT NOT NULL REFERENCES users(id),
	approver_id BIGINT REFERENCES users(id),
	approved_at TIMESTAMPTZ,
	effective_date DATE,
	expiration_date DATE,
	review_date DATE,
	review_frequency VARCHAR(50) DEFAULT 'annual',
	status VARCHAR(50) DEFAULT 'draft',
	is_published BOOLEAN DEFAULT false,
	published_at TIMESTAMPTZ,
	requires_training BOOLEAN DEFAULT false,
	requires_acknowledgment BOOLEAN DEFAULT false,
	metadata TEXT
);

-- Data Warehouses
CREATE TABLE IF NOT EXISTS data_warehouses (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	warehouse_name VARCHAR(255) NOT NULL,
	description TEXT,
	database_type VARCHAR(50) DEFAULT 'postgresql',
	connection_string TEXT,
	schema_name VARCHAR(100) DEFAULT 'public',
	storage_size BIGINT DEFAULT 0,
	max_connections INTEGER DEFAULT 100,
	query_timeout INTEGER DEFAULT 300,
	retention_period INTEGER DEFAULT 2555,
	archival_enabled BOOLEAN DEFAULT true,
	compression_type VARCHAR(50) DEFAULT 'gzip',
	encryption_enabled BOOLEAN DEFAULT true,
	access_policy TEXT,
	is_active BOOLEAN DEFAULT true,
	last_maintenance TIMESTAMPTZ,
	next_maintenance TIMESTAMPTZ,
	query_count BIGINT DEFAULT 0,
	avg_query_time DECIMAL(10,2) DEFAULT 0,
	last_optimized TIMESTAMPTZ,
	metadata TEXT
);

-- Data Sources
CREATE TABLE IF NOT EXISTS data_sources (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	source_name VARCHAR(255) NOT NULL,
	description TEXT,
	source_type VARCHAR(50) NOT NULL,
	connection_string TEXT,
	auth_method VARCHAR(50) DEFAULT 'basic',
	credentials TEXT,
	schema TEXT,
	table_mapping TEXT,
	field_mapping TEXT,
	sync_enabled BOOLEAN DEFAULT true,
	sync_frequency VARCHAR(50) DEFAULT 'hourly',
	last_sync_at TIMESTAMPTZ,
	next_sync_at TIMESTAMPTZ,
	quality_rules TEXT,
	error_threshold DECIMAL(5,2) DEFAULT 5.0,
	last_quality_check TIMESTAMPTZ,
	quality_score DECIMAL(5,2) DEFAULT 0,
	is_active BOOLEAN DEFAULT true,
	record_count BIGINT DEFAULT 0,
	last_record_at TIMESTAMPTZ,
	metadata TEXT
);

-- Dashboards
CREATE TABLE IF NOT EXISTS dashboards (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	dashboard_name VARCHAR(255) NOT NULL,
	description TEXT,
	category VARCHAR(100),
	layout TEXT,
	widgets TEXT,
	filters TEXT,
	is_public BOOLEAN DEFAULT false,
	share_token VARCHAR(255),
	access_list TEXT,
	refresh_interval INTEGER DEFAULT 300,
	cache_enabled BOOLEAN DEFAULT true,
	cache_duration INTEGER DEFAULT 3600,
	last_refreshed TIMESTAMPTZ,
	owner_id BIGINT NOT NULL REFERENCES users(id),
	view_count BIGINT DEFAULT 0,
	last_viewed TIMESTAMPTZ,
	avg_load_time DECIMAL(10,2) DEFAULT 0,
	is_active BOOLEAN DEFAULT true,
	metadata TEXT
);

-- Reports
CREATE TABLE IF NOT EXISTS reports (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	report_name VARCHAR(255) NOT NULL,
	description TEXT,
	report_type VARCHAR(50) DEFAULT 'standard',
	analytics_type VARCHAR(50) DEFAULT 'descriptive',
	query TEXT,
	data_sources TEXT,
	parameters TEXT,
	output_format VARCHAR(50) DEFAULT 'pdf',
	template TEXT,
	visualization TEXT,
	is_scheduled BOOLEAN DEFAULT false,
	schedule VARCHAR(255),
	next_run TIMESTAMPTZ,
	last_run TIMESTAMPTZ,
	recipients TEXT,
	delivery_method VARCHAR(50) DEFAULT 'email',
	created_by_id BIGINT NOT NULL REFERENCES users(id),
	execution_count BIGINT DEFAULT 0,
	avg_execution_time DECIMAL(10,2) DEFAULT 0,
	last_error TEXT,
	status VARCHAR(50) DEFAULT 'active',
	metadata TEXT
);

-- KPIs
CREATE TABLE IF NOT EXISTS kpis (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	kpi_name VARCHAR(255) NOT NULL,
	description TEXT,
	category VARCHAR(100),
	formula TEXT,
	data_source TEXT,
	unit VARCHAR(50),
	target_value DECIMAL(15,4),
	min_threshold DECIMAL(15,4),
	max_threshold DECIMAL(15,4),
	critical_min DECIMAL(15,4),
	critical_max DECIMAL(15,4),
	current_value DECIMAL(15,4) DEFAULT 0,
	previous_value DECIMAL(15,4) DEFAULT 0,
	trend VARCHAR(50) DEFAULT 'stable',
	last_calculated TIMESTAMPTZ,
	update_frequency VARCHAR(50) DEFAULT 'daily',
	next_update TIMESTAMPTZ,
	owner_id BIGINT NOT NULL REFERENCES users(id),
	alert_enabled BOOLEAN DEFAULT false,
	alert_threshold DECIMAL(15,4),
	last_alert TIMESTAMPTZ,
	is_active BOOLEAN DEFAULT true,
	metadata TEXT
);

-- Data Mining
CREATE TABLE IF NOT EXISTS data_mining (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	model_name VARCHAR(255) NOT NULL,
	description TEXT,
	model_type VARCHAR(100) NOT NULL,
	algorithm VARCHAR(100),
	analytics_type VARCHAR(50) DEFAULT 'predictive',
	parameters TEXT,
	features TEXT,
	training_data TEXT,
	accuracy DECIMAL(5,4) DEFAULT 0,
	precision DECIMAL(5,4) DEFAULT 0,
	recall DECIMAL(5,4) DEFAULT 0,
	f1_score DECIMAL(5,4) DEFAULT 0,
	trained_at TIMESTAMPTZ,
	deployed_at TIMESTAMPTZ,
	last_retrained TIMESTAMPTZ,
	retraining_frequency VARCHAR(50) DEFAULT 'monthly',
	model_path TEXT,
	model_version VARCHAR(50) DEFAULT '1.0',
	model_size BIGINT DEFAULT 0,
	prediction_count BIGINT DEFAULT 0,
	last_prediction TIMESTAMPTZ,
	avg_response_time DECIMAL(10,2) DEFAULT 0,
	created_by_id BIGINT NOT NULL REFERENCES users(id),
	status VARCHAR(50) DEFAULT 'training',
	metadata TEXT
);

-- Employees
CREATE TABLE IF NOT EXISTS employees (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	employee_id VARCHAR(50) UNIQUE NOT NULL,
	user_id BIGINT NOT NULL REFERENCES users(id),
	first_name VARCHAR(100) NOT NULL,
	last_name VARCHAR(100) NOT NULL,
	middle_name VARCHAR(100),
	date_of_birth DATE,
	gender VARCHAR(20),
	marital_status VARCHAR(50),
	nationality VARCHAR(100),
	personal_email VARCHAR(255),
	personal_phone VARCHAR(50),
	emergency_contact TEXT,
	address TEXT,
	hire_date DATE NOT NULL,
	employment_type VARCHAR(50) NOT NULL,
	employment_status VARCHAR(50) DEFAULT 'active',
	termination_date DATE,
	termination_reason TEXT,
	job_title VARCHAR(255) NOT NULL,
	job_description TEXT,
	department_id BIGINT REFERENCES departments(id),
	manager_id BIGINT REFERENCES employees(id),
	base_salary DECIMAL(12,2) DEFAULT 0,
	currency VARCHAR(3) DEFAULT 'USD',
	pay_frequency VARCHAR(50) DEFAULT 'monthly',
	work_schedule TEXT,
	time_zone VARCHAR(100) DEFAULT 'UTC',
	work_location VARCHAR(100),
	performance_rating DECIMAL(3,2) DEFAULT 0,
	last_review_date DATE,
	next_review_date DATE,
	vacation_days INTEGER DEFAULT 0,
	sick_days INTEGER DEFAULT 0,
	personal_days INTEGER DEFAULT 0,
	security_clearance VARCHAR(100),
	access_level VARCHAR(50) DEFAULT 'standard',
	metadata TEXT
);

-- Departments
CREATE TABLE IF NOT EXISTS departments (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	department_code VARCHAR(50) UNIQUE NOT NULL,
	name VARCHAR(255) NOT NULL,
	description TEXT,
	parent_department_id BIGINT REFERENCES departments(id),
	level INTEGER DEFAULT 0,
	manager_id BIGINT REFERENCES employees(id),
	budget_amount DECIMAL(15,2) DEFAULT 0,
	actual_spend DECIMAL(15,2) DEFAULT 0,
	cost_center_id BIGINT REFERENCES cost_centers(id),
	location VARCHAR(255),
	office_space VARCHAR(255),
	is_active BOOLEAN DEFAULT true,
	employee_count INTEGER DEFAULT 0,
	metadata TEXT
);

-- Positions
CREATE TABLE IF NOT EXISTS positions (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	position_code VARCHAR(50) UNIQUE NOT NULL,
	title VARCHAR(255) NOT NULL,
	description TEXT,
	level VARCHAR(50),
	grade VARCHAR(50),
	job_family VARCHAR(100),
	department_id BIGINT NOT NULL REFERENCES departments(id),
	reports_to_id BIGINT REFERENCES positions(id),
	requirements TEXT,
	qualifications TEXT,
	skills TEXT,
	min_salary DECIMAL(12,2) DEFAULT 0,
	max_salary DECIMAL(12,2) DEFAULT 0,
	currency VARCHAR(3) DEFAULT 'USD',
	is_active BOOLEAN DEFAULT true,
	is_approved BOOLEAN DEFAULT false,
	headcount_limit INTEGER DEFAULT 1,
	current_count INTEGER DEFAULT 0,
	metadata TEXT
);

-- Performance Reviews
CREATE TABLE IF NOT EXISTS performance_reviews (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	review_id VARCHAR(100) UNIQUE NOT NULL,
	employee_id BIGINT NOT NULL REFERENCES employees(id),
	reviewer_id BIGINT NOT NULL REFERENCES employees(id),
	review_period VARCHAR(100) NOT NULL,
	start_date DATE NOT NULL,
	end_date DATE NOT NULL,
	review_date DATE NOT NULL,
	review_type VARCHAR(50) DEFAULT 'annual',
	review_cycle VARCHAR(100),
	overall_rating DECIMAL(3,2) DEFAULT 0,
	goal_achievement DECIMAL(5,2) DEFAULT 0,
	competency_rating TEXT,
	achievements TEXT,
	areas_for_improvement TEXT,
	goals TEXT,
	development_plan TEXT,
	employee_comments TEXT,
	manager_comments TEXT,
	hr_comments TEXT,
	status VARCHAR(50) DEFAULT 'draft',
	submitted_at TIMESTAMPTZ,
	approved_at TIMESTAMPTZ,
	completed_at TIMESTAMPTZ,
	salary_increase DECIMAL(5,2) DEFAULT 0,
	bonus_amount DECIMAL(12,2) DEFAULT 0,
	promotion_recommended BOOLEAN DEFAULT false,
	metadata TEXT
);

-- Trainings
CREATE TABLE IF NOT EXISTS trainings (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	training_code VARCHAR(100) UNIQUE NOT NULL,
	title VARCHAR(255) NOT NULL,
	description TEXT,
	category VARCHAR(100),
	training_type VARCHAR(50) DEFAULT 'online',
	duration INTEGER DEFAULT 0,
	max_participants INTEGER DEFAULT 0,
	curriculum TEXT,
	materials TEXT,
	prerequisites TEXT,
	instructor_id BIGINT REFERENCES employees(id),
	provider VARCHAR(255),
	start_date DATE,
	end_date DATE,
	schedule TEXT,
	location VARCHAR(255),
	certification_offered BOOLEAN DEFAULT false,
	certification_valid INTEGER DEFAULT 0,
	compliance_required BOOLEAN DEFAULT false,
	cost DECIMAL(10,2) DEFAULT 0,
	currency VARCHAR(3) DEFAULT 'USD',
	budget_code VARCHAR(100),
	status VARCHAR(50) DEFAULT 'planned',
	enrollment_count INTEGER DEFAULT 0,
	completion_rate DECIMAL(5,2) DEFAULT 0,
	metadata TEXT
);

-- Training Enrollments
CREATE TABLE IF NOT EXISTS training_enrollments (
	id BIGSERIAL PRIMARY KEY,
	created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	deleted_at TIMESTAMPTZ,
	employee_id BIGINT NOT NULL REFERENCES employees(id),
	training_id BIGINT NOT NULL REFERENCES trainings(id),
	enrolled_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
	enrolled_by_id BIGINT NOT NULL REFERENCES employees(id),
	status VARCHAR(50) DEFAULT 'enrolled',
	started_at TIMESTAMPTZ,
	completed_at TIMESTAMPTZ,
	progress_percent DECIMAL(5,2) DEFAULT 0,
	score DECIMAL(5,2) DEFAULT 0,
	passing_score DECIMAL(5,2) DEFAULT 70,
	attempts INTEGER DEFAULT 0,
	certificate_issued BOOLEAN DEFAULT false,
	certificate_number VARCHAR(255),
	certificate_expiry DATE,
	feedback TEXT,
	rating DECIMAL(3,2) DEFAULT 0,
	metadata TEXT
);

-- Create indexes for enterprise tables
CREATE INDEX IF NOT EXISTS idx_content_repositories_classification ON content_repositories(classification);
CREATE INDEX IF NOT EXISTS idx_content_repositories_is_active ON content_repositories(is_active);
CREATE INDEX IF NOT EXISTS idx_content_versions_document_id ON content_versions(document_id);
CREATE INDEX IF NOT EXISTS idx_content_versions_approval_status ON content_versions(approval_status);
CREATE INDEX IF NOT EXISTS idx_content_workflow_instances_status ON content_workflow_instances(status);
CREATE INDEX IF NOT EXISTS idx_content_collaborations_document_id ON content_collaborations(document_id);
CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_account_code ON chart_of_accounts(account_code);
CREATE INDEX IF NOT EXISTS idx_chart_of_accounts_account_type ON chart_of_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_general_ledger_entry_number ON general_ledger(entry_number);
CREATE INDEX IF NOT EXISTS idx_general_ledger_transaction_date ON general_ledger(transaction_date);
CREATE INDEX IF NOT EXISTS idx_budget_plans_fiscal_year ON budget_plans(fiscal_year);
CREATE INDEX IF NOT EXISTS idx_budget_plans_status ON budget_plans(status);
CREATE INDEX IF NOT EXISTS idx_compliance_requirements_framework ON compliance_requirements(framework);
CREATE INDEX IF NOT EXISTS idx_compliance_assessments_status ON compliance_assessments(status);
CREATE INDEX IF NOT EXISTS idx_compliance_findings_status ON compliance_findings(status);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_status ON risk_assessments(status);
CREATE INDEX IF NOT EXISTS idx_employees_employee_id ON employees(employee_id);
CREATE INDEX IF NOT EXISTS idx_employees_employment_status ON employees(employment_status);
CREATE INDEX IF NOT EXISTS idx_departments_department_code ON departments(department_code);
CREATE INDEX IF NOT EXISTS idx_positions_position_code ON positions(position_code);
CREATE INDEX IF NOT EXISTS idx_performance_reviews_review_id ON performance_reviews(review_id);
CREATE INDEX IF NOT EXISTS idx_trainings_training_code ON trainings(training_code);`

	if err := db.Exec(enterpriseMigrationSQL).Error; err != nil {
		return fmt.Errorf("failed to create enterprise content and financial tables: %w", err)
	}

	log.Println("Enterprise system migration completed successfully")
	return nil
}
