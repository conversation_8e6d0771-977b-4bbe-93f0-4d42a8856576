import ApiService from './api';
import { DigitalCertificate, DigitalSignature, SignedCommentFormData, CommentFormData } from '../types';

export class DigitalSignatureService {
  private api: typeof ApiService;

  constructor() {
    this.api = ApiService;
  }

  // Certificate Management
  async getUserCertificates(): Promise<DigitalCertificate[]> {
    try {
      const response = await this.api.getUserCertificates();
      return response.data || [];
    } catch (error) {
      console.error('Failed to fetch user certificates:', error);
      throw error;
    }
  }

  async getCertificate(certificateId: number): Promise<DigitalCertificate> {
    try {
      const response = await this.api.getCertificate(certificateId);
      return response.data;
    } catch (error) {
      console.error('Failed to fetch certificate:', error);
      throw error;
    }
  }

  // Digital Signature Operations
  async createSignedComment(documentId: number, commentData: CommentFormData, certificateId: number, signatureData: string): Promise<{ document_id: number; comment_id: number; signature_id: string; verified: boolean }> {
    try {
      const signedCommentData: SignedCommentFormData = {
        ...commentData,
        digital_signature: {
          signature_value: signatureData,
          certificate_id: certificateId,
          timestamp: new Date().toISOString(),
          hash_algorithm: 'sha256',
          signing_method: 'rsa_pss'
        }
      };

      const response = await this.api.createSignedComment(documentId, signedCommentData);
      return response.data;
    } catch (error) {
      console.error('Failed to create signed comment:', error);
      throw error;
    }
  }

  async verifyCommentSignature(commentId: number): Promise<DigitalSignature> {
    try {
      const response = await this.api.verifyCommentSignature(commentId);
      return response.data;
    } catch (error) {
      console.error('Failed to verify comment signature:', error);
      throw error;
    }
  }

  // Utility Methods
  async validateCertificate(certificate: DigitalCertificate): Promise<boolean> {
    const now = new Date();
    const notBefore = new Date(certificate.not_before);
    const notAfter = new Date(certificate.not_after);

    // Check if certificate is within validity period
    if (now < notBefore || now > notAfter) {
      return false;
    }

    // Check if certificate is active
    if (certificate.status !== 'active') {
      return false;
    }

    return true;
  }

  async getActiveCertificates(): Promise<DigitalCertificate[]> {
    try {
      const certificates = await this.getUserCertificates();
      const activeCertificates = [];

      for (const cert of certificates) {
        if (await this.validateCertificate(cert)) {
          activeCertificates.push(cert);
        }
      }

      return activeCertificates;
    } catch (error) {
      console.error('Failed to get active certificates:', error);
      throw error;
    }
  }

  async getDefaultCertificate(): Promise<DigitalCertificate | null> {
    try {
      const certificates = await this.getActiveCertificates();
      return certificates.find(cert => cert.is_default) || certificates[0] || null;
    } catch (error) {
      console.error('Failed to get default certificate:', error);
      return null;
    }
  }

  // Real digital signature generation using Web Crypto API
  async generateSignature(content: string, certificateId: number): Promise<string> {
    try {
      // Check if Web Crypto API is available
      if (!window.crypto || !window.crypto.subtle) {
        throw new Error('Web Crypto API is not available in this browser');
      }

      // Get or generate a key pair for signing
      const keyPair = await this.getOrCreateSigningKeyPair(certificateId);

      // Prepare the content to be signed
      const encoder = new TextEncoder();
      const timestamp = Date.now().toString();
      const dataToSign = content + '|' + certificateId.toString() + '|' + timestamp;
      const data = encoder.encode(dataToSign);

      // Sign the data using RSASSA-PKCS1-v1_5 with SHA-256
      const signature = await window.crypto.subtle.sign(
        {
          name: 'RSASSA-PKCS1-v1_5',
        },
        keyPair.privateKey,
        data
      );

      // Convert signature to base64
      const signatureArray = new Uint8Array(signature);
      const signatureBase64 = btoa(String.fromCharCode.apply(null, Array.from(signatureArray)));

      // Store signature metadata for verification
      const signatureData = {
        signature: signatureBase64,
        algorithm: 'RSASSA-PKCS1-v1_5',
        hash: 'SHA-256',
        timestamp: timestamp,
        certificateId: certificateId,
        contentHash: await this.hashContent(content)
      };

      // Store in localStorage for later verification
      const signatures = JSON.parse(localStorage.getItem('digitalSignatures') || '[]');
      signatures.push(signatureData);
      localStorage.setItem('digitalSignatures', JSON.stringify(signatures));

      return signatureBase64;
    } catch (error) {
      console.error('Failed to generate signature:', error);
      throw error;
    }
  }

  // Get or create a signing key pair for the certificate
  private async getOrCreateSigningKeyPair(certificateId: number): Promise<CryptoKeyPair> {
    const keyStorageKey = `signingKeyPair_${certificateId}`;

    try {
      // Try to load existing key pair from IndexedDB
      const storedKeyPair = await this.loadKeyPairFromStorage(keyStorageKey);
      if (storedKeyPair) {
        return storedKeyPair;
      }
    } catch (error) {
      console.warn('Could not load existing key pair, generating new one:', error);
    }

    // Generate new key pair
    const keyPair = await window.crypto.subtle.generateKey(
      {
        name: 'RSASSA-PKCS1-v1_5',
        modulusLength: 2048,
        publicExponent: new Uint8Array([1, 0, 1]),
        hash: 'SHA-256',
      },
      true, // extractable
      ['sign', 'verify']
    );

    // Store the key pair for future use
    await this.storeKeyPairToStorage(keyStorageKey, keyPair);

    return keyPair;
  }

  // Hash content for integrity verification
  private async hashContent(content: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(content);
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
    const hashArray = new Uint8Array(hashBuffer);
    return btoa(String.fromCharCode.apply(null, Array.from(hashArray)));
  }

  // Store key pair to IndexedDB (simplified implementation)
  private async storeKeyPairToStorage(key: string, keyPair: CryptoKeyPair): Promise<void> {
    try {
      // Export keys for storage
      const publicKey = await window.crypto.subtle.exportKey('spki', keyPair.publicKey);
      const privateKey = await window.crypto.subtle.exportKey('pkcs8', keyPair.privateKey);

      const keyData = {
        publicKey: btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(publicKey)))),
        privateKey: btoa(String.fromCharCode.apply(null, Array.from(new Uint8Array(privateKey)))),
        algorithm: 'RSASSA-PKCS1-v1_5',
        created: Date.now()
      };

      // Store in localStorage (in production, use IndexedDB)
      localStorage.setItem(key, JSON.stringify(keyData));
    } catch (error) {
      console.error('Failed to store key pair:', error);
      throw error;
    }
  }

  // Load key pair from storage
  private async loadKeyPairFromStorage(key: string): Promise<CryptoKeyPair | null> {
    try {
      const keyDataStr = localStorage.getItem(key);
      if (!keyDataStr) {
        return null;
      }

      const keyData = JSON.parse(keyDataStr);

      // Convert base64 back to ArrayBuffer
      const publicKeyBuffer = Uint8Array.from(atob(keyData.publicKey), c => c.charCodeAt(0));
      const privateKeyBuffer = Uint8Array.from(atob(keyData.privateKey), c => c.charCodeAt(0));

      // Import keys
      const publicKey = await window.crypto.subtle.importKey(
        'spki',
        publicKeyBuffer,
        {
          name: 'RSASSA-PKCS1-v1_5',
          hash: 'SHA-256',
        },
        true,
        ['verify']
      );

      const privateKey = await window.crypto.subtle.importKey(
        'pkcs8',
        privateKeyBuffer,
        {
          name: 'RSASSA-PKCS1-v1_5',
          hash: 'SHA-256',
        },
        true,
        ['sign']
      );

      return { publicKey, privateKey };
    } catch (error) {
      console.error('Failed to load key pair:', error);
      return null;
    }
  }

  // Real signature verification using Web Crypto API
  async verifySignature(content: string, signature: string, certificateId: number): Promise<boolean> {
    try {
      // Check if Web Crypto API is available
      if (!window.crypto || !window.crypto.subtle) {
        throw new Error('Web Crypto API is not available in this browser');
      }

      // Get the key pair for verification
      const keyPair = await this.getOrCreateSigningKeyPair(certificateId);

      // Find the signature metadata
      const signatures = JSON.parse(localStorage.getItem('digitalSignatures') || '[]');
      const signatureData = signatures.find((sig: any) =>
        sig.signature === signature && sig.certificateId === certificateId
      );

      if (!signatureData) {
        console.warn('Signature metadata not found');
        return false;
      }

      // Reconstruct the original data that was signed
      const encoder = new TextEncoder();
      const dataToVerify = content + '|' + certificateId.toString() + '|' + signatureData.timestamp;
      const data = encoder.encode(dataToVerify);

      // Convert signature from base64 to ArrayBuffer
      const signatureBuffer = Uint8Array.from(atob(signature), c => c.charCodeAt(0));

      // Verify the signature
      const isValid = await window.crypto.subtle.verify(
        {
          name: 'RSASSA-PKCS1-v1_5',
        },
        keyPair.publicKey,
        signatureBuffer,
        data
      );

      // Additional integrity check - verify content hash
      const currentContentHash = await this.hashContent(content);
      const hashMatches = currentContentHash === signatureData.contentHash;

      return isValid && hashMatches;
    } catch (error) {
      console.error('Failed to verify signature:', error);
      return false;
    }
  }

  // Get signature information for display
  async getSignatureInfo(signature: string): Promise<any> {
    try {
      const signatures = JSON.parse(localStorage.getItem('digitalSignatures') || '[]');
      const signatureData = signatures.find((sig: any) => sig.signature === signature);

      if (!signatureData) {
        return null;
      }

      return {
        algorithm: signatureData.algorithm,
        hash: signatureData.hash,
        timestamp: new Date(parseInt(signatureData.timestamp)),
        certificateId: signatureData.certificateId,
        contentHash: signatureData.contentHash
      };
    } catch (error) {
      console.error('Failed to get signature info:', error);
      return null;
    }
  }

  // Format certificate display name
  formatCertificateDisplayName(certificate: DigitalCertificate): string {
    if (certificate.common_name) {
      return certificate.common_name;
    }
    
    // Extract CN from subject if available
    const cnMatch = certificate.subject.match(/CN=([^,]+)/);
    if (cnMatch) {
      return cnMatch[1];
    }
    
    return `Certificate ${certificate.serial.substring(0, 8)}...`;
  }

  // Get certificate status display
  getCertificateStatusDisplay(certificate: DigitalCertificate): { status: string; color: string; icon: string } {
    switch (certificate.status) {
      case 'active':
        return { status: 'Active', color: 'green', icon: '✓' };
      case 'expired':
        return { status: 'Expired', color: 'red', icon: '⚠' };
      case 'revoked':
        return { status: 'Revoked', color: 'red', icon: '✗' };
      case 'suspended':
        return { status: 'Suspended', color: 'orange', icon: '⏸' };
      case 'pending':
        return { status: 'Pending', color: 'blue', icon: '⏳' };
      case 'renewing':
        return { status: 'Renewing', color: 'blue', icon: '🔄' };
      default:
        return { status: 'Unknown', color: 'gray', icon: '?' };
    }
  }

  // Check if certificate expires soon (within 30 days)
  isCertificateExpiringSoon(certificate: DigitalCertificate): boolean {
    const now = new Date();
    const notAfter = new Date(certificate.not_after);
    const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
    
    return notAfter <= thirtyDaysFromNow;
  }
}

// Export singleton instance
export const digitalSignatureService = new DigitalSignatureService();
