<!DOCTYPE html>
<html>
<head>
    <title>Authentication Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px 15px; cursor: pointer; background-color: #007bff; color: white; border: none; border-radius: 3px; }
        button:hover { background-color: #0056b3; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Authentication & CRUD Testing</h1>
    
    <div class="section">
        <h2>Step 1: Login</h2>
        <button onclick="login()">Login as Admin</button>
        <div id="loginResult"></div>
    </div>

    <div class="section">
        <h2>Step 2: Check User Info</h2>
        <button onclick="checkUser()">Check Current User</button>
        <div id="userResult"></div>
    </div>

    <div class="section">
        <h2>Step 3: Test CRUD Permissions</h2>
        <button onclick="testPermissions()">Test All Permissions</button>
        <div id="permissionResult"></div>
    </div>

    <div class="section">
        <h2>Step 4: Test Page Access</h2>
        <button onclick="testPageAccess()">Test Admin Pages</button>
        <div id="pageResult"></div>
    </div>

    <script>
        const API_URL = 'http://127.0.0.1:8080';
        
        async function login() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '<div class="result info">Logging in...</div>';
            
            try {
                const response = await fetch(`${API_URL}/api/v1/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        identifier: '<EMAIL>',
                        password: 'password'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    localStorage.setItem('federal_register_token', data.token);
                    if (data.refresh_token) {
                        localStorage.setItem('federal_register_refresh_token', data.refresh_token);
                    }
                    resultDiv.innerHTML = '<div class="result success">✅ Login successful!</div>';
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ Login failed: ${data.error || data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ Login error: ${error.message}</div>`;
            }
        }

        async function checkUser() {
            const resultDiv = document.getElementById('userResult');
            const token = localStorage.getItem('federal_register_token');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="result error">❌ No token found. Please login first.</div>';
                return;
            }

            try {
                const response = await fetch(`${API_URL}/api/v1/auth/me`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="result success">✅ User info retrieved:</div>';
                    resultDiv.innerHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                } else {
                    resultDiv.innerHTML = `<div class="result error">❌ User check failed: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="result error">❌ User check error: ${error.message}</div>`;
            }
        }

        async function testPermissions() {
            const resultDiv = document.getElementById('permissionResult');
            const token = localStorage.getItem('federal_register_token');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="result error">❌ No token found. Please login first.</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="result info">Testing API permissions...</div>';

            const endpoints = [
                { name: 'Documents', url: '/api/v1/public/documents' },
                { name: 'Agencies', url: '/api/v1/agencies' },
                { name: 'Categories', url: '/api/v1/categories' },
                { name: 'Regulations', url: '/api/v1/regulations' },
                { name: 'Tasks', url: '/api/v1/tasks' }
            ];

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_URL}${endpoint.url}`, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        resultDiv.innerHTML += `<div class="result success">✅ ${endpoint.name} - OK (${data.data?.length || data.length || 'N/A'} items)</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="result error">❌ ${endpoint.name} - Failed (${response.status})</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<div class="result error">❌ ${endpoint.name} - Error: ${error.message}</div>`;
                }
            }
        }

        async function testPageAccess() {
            const resultDiv = document.getElementById('pageResult');
            resultDiv.innerHTML = '<div class="result info">Testing page access...</div>';

            const pages = [
                '/admin/agencies',
                '/admin/categories', 
                '/admin/regulations/new',
                '/tasks/new',
                '/documents/new'
            ];

            for (const page of pages) {
                try {
                    const response = await fetch(`http://127.0.0.1:3000${page}`);
                    
                    if (response.ok) {
                        resultDiv.innerHTML += `<div class="result success">✅ ${page} - Accessible</div>`;
                    } else if (response.status === 404) {
                        resultDiv.innerHTML += `<div class="result error">❌ ${page} - Not Found (404)</div>`;
                    } else {
                        resultDiv.innerHTML += `<div class="result error">❌ ${page} - Status: ${response.status}</div>`;
                    }
                } catch (error) {
                    resultDiv.innerHTML += `<div class="result error">❌ ${page} - Error: ${error.message}</div>`;
                }
            }
        }

        // Auto-check token on page load
        window.onload = function() {
            const token = localStorage.getItem('federal_register_token');
            if (token) {
                document.getElementById('loginResult').innerHTML = '<div class="result info">Token found in storage. Click "Check Current User" to verify.</div>';
            } else {
                document.getElementById('loginResult').innerHTML = '<div class="result info">No token found. Please login first.</div>';
            }
        };
    </script>
</body>
</html>
