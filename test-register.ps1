# Test script to register and login a user
$registerUrl = "http://localhost:8080/api/v1/auth/register"
$loginUrl = "http://localhost:8080/api/v1/auth/login"

$registerData = @{
    username = "testuser2"
    email = "<EMAIL>"
    password = "password123"
    first_name = "Test"
    last_name = "User"
    title = "Editor"
    organization = "Test Organization"
} | ConvertTo-Json

$loginData = @{
    identifier = "testuser2"
    password = "password123"
} | ConvertTo-Json

Write-Host "Testing user registration..."
try {
    $registerResponse = Invoke-RestMethod -Uri $registerUrl -Method POST -ContentType "application/json" -Body $registerData
    Write-Host "Registration successful:" -ForegroundColor Green
    $registerResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Registration failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}

Write-Host "`nTesting user login..."
try {
    $loginResponse = Invoke-RestMethod -Uri $loginUrl -Method POST -ContentType "application/json" -Body $loginData
    Write-Host "Login successful:" -ForegroundColor Green
    $loginResponse | ConvertTo-Json -Depth 3
    
    # Test getting current user
    $headers = @{
        "Authorization" = "Bearer $($loginResponse.token)"
    }
    
    Write-Host "`nTesting get current user..."
    $userResponse = Invoke-RestMethod -Uri "http://localhost:8080/api/v1/auth/me" -Method GET -Headers $headers
    Write-Host "Get user successful:" -ForegroundColor Green
    $userResponse | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host "Login failed:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response body: $responseBody"
    }
}
