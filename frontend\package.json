{"name": "federal-register-clone-frontend", "version": "1.0.0", "description": "Federal Register Clone Frontend - Document Management System", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS='--inspect' next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "@hookform/resolvers": "^3.3.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.0.0", "@types/react": "19.1.8", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "19.1.6", "@types/react-syntax-highlighter": "^15.5.0", "autoprefixer": "^10.4.0", "axios": "^1.6.0", "clsx": "^2.0.0", "cross-env": "^7.0.3", "date-fns": "^2.30.0", "framer-motion": "^10.16.0", "lucide-react": "^0.294.0", "next": "^15.4.0-canary.104", "postcss": "^8.4.0", "react": "18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "18.3.1", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.0", "react-markdown": "^9.1.0", "react-query": "^3.39.0", "react-router-dom": "^6.18.0", "react-syntax-highlighter": "^15.5.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@babel/core": "^7.27.7", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "babel-loader": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "15.3.4", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "overrides": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}