package tests

import (
	"testing"
	"time"

	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"

	"github.com/stretchr/testify/assert"
)

// TestRegulationTypes tests the regulation type constants and validation
func TestRegulationTypes(t *testing.T) {
	validTypes := []models.RegulationType{"law", "rule", "regulation", "code"}

	for _, regType := range validTypes {
		assert.NotEmpty(t, string(regType), "Regulation type should not be empty")
		assert.Contains(t, []string{"law", "rule", "regulation", "code"}, string(regType))
	}
}

// TestRegulationValidation tests basic regulation validation logic
func TestRegulationValidation(t *testing.T) {
	tests := []struct {
		name        string
		regulation  models.LawsAndRules
		expectValid bool
	}{
		{
			name: "Valid regulation",
			regulation: models.LawsAndRules{
				Title:       "Test Regulation",
				Type:        "rule",
				Status:      "draft",
				AgencyID:    1,
				CreatedByID: 1,
			},
			expectValid: true,
		},
		{
			name: "Missing title",
			regulation: models.LawsAndRules{
				Type:        "rule",
				Status:      "draft",
				AgencyID:    1,
				CreatedByID: 1,
			},
			expectValid: false,
		},
		{
			name: "Invalid type",
			regulation: models.LawsAndRules{
				Title:       "Test Regulation",
				Type:        "invalid",
				Status:      "draft",
				AgencyID:    1,
				CreatedByID: 1,
			},
			expectValid: false,
		},
		{
			name: "Missing agency",
			regulation: models.LawsAndRules{
				Title:       "Test Regulation",
				Type:        "rule",
				Status:      "draft",
				CreatedByID: 1,
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid := validateRegulation(tt.regulation)
			assert.Equal(t, tt.expectValid, isValid)
		})
	}
}

// TestChunkHierarchy tests the chunk hierarchy validation
func TestChunkHierarchy(t *testing.T) {
	tests := []struct {
		name        string
		chunks      []models.Chunk
		expectValid bool
	}{
		{
			name: "Valid hierarchy: Chapter -> Title -> Section",
			chunks: []models.Chunk{
				{
					ID:              1,
					ChunkType:       "chapter",
					ChunkIdentifier: "CHAPTER-I",
					Number:          "I",
					Title:           "Test Chapter",
					OrderInParent:   1,
				},
				{
					ID:              2,
					ParentChunkID:   uintPtr(1),
					ChunkType:       "title",
					ChunkIdentifier: "CHAPTER-I-TITLE-I",
					Number:          "I",
					Title:           "Test Title",
					OrderInParent:   1,
				},
				{
					ID:              3,
					ParentChunkID:   uintPtr(2),
					ChunkType:       "section",
					ChunkIdentifier: "CHAPTER-I-TITLE-I-SECTION-1001",
					Number:          "1001",
					Title:           "Test Section",
					OrderInParent:   1,
				},
			},
			expectValid: true,
		},
		{
			name: "Invalid hierarchy: Section without parent",
			chunks: []models.Chunk{
				{
					ChunkType:       "section",
					ChunkIdentifier: "SECTION-1001",
					Number:          "1001",
					Title:           "Orphan Section",
					OrderInParent:   1,
				},
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid := validateChunkHierarchy(tt.chunks)
			assert.Equal(t, tt.expectValid, isValid)
		})
	}
}

// TestEffectivenessStatus tests the effectiveness status calculation
func TestEffectivenessStatus(t *testing.T) {
	now := time.Now()
	future := now.AddDate(0, 1, 0)   // 1 month from now
	past := now.AddDate(0, -1, 0)    // 1 month ago
	farPast := now.AddDate(-1, 0, 0) // 1 year ago

	tests := []struct {
		name           string
		regulation     models.LawsAndRules
		expectedStatus string
	}{
		{
			name: "Not yet effective",
			regulation: models.LawsAndRules{
				EffectiveDate: &future,
			},
			expectedStatus: "not_yet_effective",
		},
		{
			name: "Currently effective",
			regulation: models.LawsAndRules{
				EffectiveDate: &past,
			},
			expectedStatus: "effective",
		},
		{
			name: "Terminated",
			regulation: models.LawsAndRules{
				EffectiveDate:   &farPast,
				TerminationDate: &past,
			},
			expectedStatus: "terminated",
		},
		{
			name: "No effective date",
			regulation: models.LawsAndRules{
				EffectiveDate: nil,
			},
			expectedStatus: "unknown",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			status := calculateEffectivenessStatus(tt.regulation)
			assert.Equal(t, tt.expectedStatus, status)
		})
	}
}

// TestCreateChunkRequest tests the chunk request validation
func TestCreateChunkRequest(t *testing.T) {
	tests := []struct {
		name        string
		request     services.CreateChunkRequest
		expectValid bool
	}{
		{
			name: "Valid chapter request",
			request: services.CreateChunkRequest{
				ChunkType:       "chapter",
				Number:          "I",
				Title:           "Test Chapter",
				ChunkIdentifier: "CHAPTER-I",
				OrderInParent:   1,
			},
			expectValid: true,
		},
		{
			name: "Missing chunk type",
			request: services.CreateChunkRequest{
				Number:          "I",
				Title:           "Test Chapter",
				ChunkIdentifier: "CHAPTER-I",
				OrderInParent:   1,
			},
			expectValid: false,
		},
		{
			name: "Missing title",
			request: services.CreateChunkRequest{
				ChunkType:       "chapter",
				Number:          "I",
				ChunkIdentifier: "CHAPTER-I",
				OrderInParent:   1,
			},
			expectValid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isValid := validateCreateChunkRequest(tt.request)
			assert.Equal(t, tt.expectValid, isValid)
		})
	}
}

// Helper functions for validation (these would normally be in the service layer)

func validateRegulation(reg models.LawsAndRules) bool {
	if reg.Title == "" {
		return false
	}

	validTypes := []string{"law", "rule", "regulation", "code"}
	typeValid := false
	for _, validType := range validTypes {
		if string(reg.Type) == validType {
			typeValid = true
			break
		}
	}
	if !typeValid {
		return false
	}

	if reg.AgencyID == 0 {
		return false
	}

	if reg.CreatedByID == 0 {
		return false
	}

	return true
}

func validateChunkHierarchy(chunks []models.Chunk) bool {
	// Create a map of chunk IDs for parent validation
	chunkMap := make(map[uint]models.Chunk)
	for _, chunk := range chunks {
		chunkMap[chunk.ID] = chunk
	}

	// Validate hierarchy rules
	for _, chunk := range chunks {
		switch chunk.ChunkType {
		case "chapter":
			// Chapters can be root level (no parent required)
			continue
		case "title":
			// Titles should have chapter parents (but we'll allow root for this test)
			continue
		case "section":
			// Sections should have parents (title or chapter)
			if chunk.ParentChunkID == nil {
				return false
			}
		}
	}
	return true
}

func calculateEffectivenessStatus(reg models.LawsAndRules) string {
	if reg.EffectiveDate == nil {
		return "unknown"
	}

	now := time.Now()

	if reg.TerminationDate != nil && reg.TerminationDate.Before(now) {
		return "terminated"
	}

	if reg.EffectiveDate.After(now) {
		return "not_yet_effective"
	}

	return "effective"
}

func validateCreateChunkRequest(req services.CreateChunkRequest) bool {
	if req.ChunkType == "" {
		return false
	}

	if req.Title == "" {
		return false
	}

	return true
}

// Helper function to create uint pointer
func uintPtr(u uint) *uint {
	return &u
}
