package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// RegulationChunkRequest represents the request structure for regulation chunks
type RegulationChunkRequest struct {
	ChunkOrder int    `json:"chunk_order" binding:"required"`
	Title      string `json:"title" binding:"required"`
	Content    string `json:"content" binding:"required"`
	ChunkType  string `json:"chunk_type"`
	IsActive   bool   `json:"is_active"`
}

// GetRegulationChunks returns chunks for a regulation
func GetRegulationChunks(c *gin.Context) {
	regulationID, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Get chunks for this regulation
	var chunks []models.RegulationChunk
	if err := db.Where("law_rule_id = ?", regulationID).
		Order("chunk_order ASC").
		Find(&chunks).Error; err != nil {
		HandleInternalError(c, "Failed to fetch regulation chunks: "+err.Error())
		return
	}

	// Convert to response format
	chunkResponses := make([]gin.H, len(chunks))
	for i, chunk := range chunks {
		chunkResponses[i] = gin.H{
			"id":          chunk.ID,
			"law_rule_id": chunk.LawRuleID,
			"chunk_order": chunk.ChunkOrder,
			"title":       chunk.Title,
			"content":     chunk.Content,
			"chunk_type":  chunk.ChunkType,
			"is_active":   chunk.IsActive,
			"created_at":  chunk.CreatedAt,
			"updated_at":  chunk.UpdatedAt,
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation chunks retrieved successfully",
		Data:    chunkResponses,
	})
}

// GetRegulationChunk returns a single regulation chunk by ID
func GetRegulationChunk(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get regulation chunk
	var chunk models.RegulationChunk
	if err := db.Preload("Regulation").First(&chunk, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation chunk")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation chunk: "+err.Error())
		return
	}

	response := gin.H{
		"id":          chunk.ID,
		"regulation":  chunk.Regulation,
		"chunk_order": chunk.ChunkOrder,
		"title":       chunk.Title,
		"content":     chunk.Content,
		"chunk_type":  chunk.ChunkType,
		"is_active":   chunk.IsActive,
		"created_at":  chunk.CreatedAt,
		"updated_at":  chunk.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation chunk retrieved successfully",
		Data:    response,
	})
}

// CreateRegulationChunk creates a new regulation chunk
func CreateRegulationChunk(c *gin.Context) {
	// Get regulation ID from URL parameter
	regulationID, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RegulationChunkRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Verify regulation exists
	var regulation models.LawsAndRules
	if err := db.First(&regulation, regulationID).Error; err != nil {
		if err.Error() == "record not found" {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error:   "Invalid regulation",
				Message: "Regulation not found",
			})
			return
		}
		HandleInternalError(c, "Failed to fetch regulation: "+err.Error())
		return
	}

	// Create regulation chunk
	chunk := &models.RegulationChunk{
		LawRuleID:  regulationID,
		ChunkOrder: req.ChunkOrder,
		Title:      req.Title,
		Content:    req.Content,
		ChunkType:  req.ChunkType,
		IsActive:   req.IsActive,
	}

	if err := db.Create(chunk).Error; err != nil {
		HandleInternalError(c, "Failed to create regulation chunk: "+err.Error())
		return
	}

	response := gin.H{
		"id":          chunk.ID,
		"law_rule_id": chunk.LawRuleID,
		"chunk_order": chunk.ChunkOrder,
		"title":       chunk.Title,
		"content":     chunk.Content,
		"chunk_type":  chunk.ChunkType,
		"is_active":   chunk.IsActive,
		"created_at":  chunk.CreatedAt,
		"updated_at":  chunk.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Regulation chunk created successfully",
		Data:    response,
	})
}

// UpdateRegulationChunk updates an existing regulation chunk
func UpdateRegulationChunk(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req RegulationChunkRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing regulation chunk
	var chunk models.RegulationChunk
	if err := db.First(&chunk, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation chunk")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation chunk: "+err.Error())
		return
	}

	// Update regulation chunk fields
	chunk.ChunkOrder = req.ChunkOrder
	chunk.Title = req.Title
	chunk.Content = req.Content
	chunk.ChunkType = req.ChunkType
	chunk.IsActive = req.IsActive

	if err := db.Save(&chunk).Error; err != nil {
		HandleInternalError(c, "Failed to update regulation chunk: "+err.Error())
		return
	}

	response := gin.H{
		"id":          chunk.ID,
		"law_rule_id": chunk.LawRuleID,
		"chunk_order": chunk.ChunkOrder,
		"title":       chunk.Title,
		"content":     chunk.Content,
		"chunk_type":  chunk.ChunkType,
		"is_active":   chunk.IsActive,
		"created_at":  chunk.CreatedAt,
		"updated_at":  chunk.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation chunk updated successfully",
		Data:    response,
	})
}

// DeleteRegulationChunk deletes a regulation chunk
func DeleteRegulationChunk(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if regulation chunk exists
	var chunk models.RegulationChunk
	if err := db.First(&chunk, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Regulation chunk")
			return
		}
		HandleInternalError(c, "Failed to fetch regulation chunk: "+err.Error())
		return
	}

	// Delete regulation chunk
	if err := db.Delete(&chunk).Error; err != nil {
		HandleInternalError(c, "Failed to delete regulation chunk: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation chunk deleted successfully",
	})
}

// ReorderRegulationChunks reorders chunks for a regulation
func ReorderRegulationChunks(c *gin.Context) {
	regulationID, valid := ValidateID(c, "regulation_id")
	if !valid {
		return
	}

	var req struct {
		ChunkOrders []struct {
			ID    uint `json:"id"`
			Order int  `json:"order"`
		} `json:"chunk_orders" binding:"required"`
	}

	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Update chunk orders
	for _, chunkOrder := range req.ChunkOrders {
		if err := db.Model(&models.RegulationChunk{}).
			Where("id = ? AND law_rule_id = ?", chunkOrder.ID, regulationID).
			Update("chunk_order", chunkOrder.Order).Error; err != nil {
			HandleInternalError(c, "Failed to reorder regulation chunks: "+err.Error())
			return
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Regulation chunks reordered successfully",
		Data: gin.H{
			"law_rule_id":   regulationID,
			"updated_count": len(req.ChunkOrders),
		},
	})
}
