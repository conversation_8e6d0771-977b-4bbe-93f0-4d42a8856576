import {
  ContentRepository,
  ContentVersion,
  ContentWorkflow,
  ChartOfAccounts,
  GeneralLedger,
  BudgetPlan,
  CostCenter,
  FinancialReport,
  ComplianceRequirement,
  ComplianceAssessment,
  RiskAssessment,
  Dashboard,
  Report,
  KPI,
  Employee,
  Department,
  Training,
  ApiResponse,
  PaginatedResponse,
  FilterOptions
} from '../types/enterprise';

const API_BASE_URL = (process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8080') + '/api/v1';

// Generic API helper functions
async function apiRequest<T>(
  endpoint: string, 
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const token = localStorage.getItem('token');
  
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`API request failed: ${response.statusText}`);
  }

  return response.json();
}

async function apiGet<T>(endpoint: string, params?: FilterOptions): Promise<ApiResponse<T>> {
  const queryString = params ? new URLSearchParams(params as any).toString() : '';
  const url = queryString ? `${endpoint}?${queryString}` : endpoint;
  return apiRequest<T>(url);
}

async function apiPost<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

async function apiPut<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

async function apiDelete<T>(endpoint: string): Promise<ApiResponse<T>> {
  return apiRequest<T>(endpoint, {
    method: 'DELETE',
  });
}

// Enterprise Content Management API
export const contentApi = {
  // Content Repositories
  getRepositories: (params?: FilterOptions) => 
    apiGet<ContentRepository[]>('/enterprise/content/repositories', params),
  
  getRepository: (id: number) => 
    apiGet<ContentRepository>(`/enterprise/content/repositories/${id}`),
  
  createRepository: (data: Partial<ContentRepository>) => 
    apiPost<ContentRepository>('/enterprise/content/repositories', data),
  
  updateRepository: (id: number, data: Partial<ContentRepository>) => 
    apiPut<ContentRepository>(`/enterprise/content/repositories/${id}`, data),
  
  deleteRepository: (id: number) => 
    apiDelete<void>(`/enterprise/content/repositories/${id}`),

  // Content Versions
  getVersions: (documentId: number, params?: FilterOptions) => 
    apiGet<ContentVersion[]>(`/enterprise/content/documents/${documentId}/versions`, params),
  
  createVersion: (data: Partial<ContentVersion>) =>
    apiPost<ContentVersion>('/enterprise/content/versions', data),

  getVersion: (id: number) =>
    apiGet<ContentVersion>(`/enterprise/content/versions/${id}`),

  updateVersion: (id: number, data: Partial<ContentVersion>) =>
    apiPut<ContentVersion>(`/enterprise/content/versions/${id}`, data),

  deleteVersion: (id: number) =>
    apiDelete<void>(`/enterprise/content/versions/${id}`),

  approveVersion: (id: number, data: { approval_status: string; rejection_reason?: string }) =>
    apiPut<void>(`/enterprise/content/versions/${id}/approve`, data),

  // Content Workflows
  getWorkflows: (params?: FilterOptions) => 
    apiGet<ContentWorkflow[]>('/enterprise/content/workflows', params),
  
  createWorkflow: (data: Partial<ContentWorkflow>) =>
    apiPost<ContentWorkflow>('/enterprise/content/workflows', data),

  deleteWorkflow: (id: number) =>
    apiDelete<void>(`/enterprise/content/workflows/${id}`),

  startWorkflow: (workflowId: number, data: { document_id: number }) =>
    apiPost<void>(`/enterprise/content/workflows/${workflowId}/start`, data),
  
  getWorkflowInstances: (params?: FilterOptions) => 
    apiGet<any[]>('/enterprise/content/workflow-instances', params),

  // Content Collaboration
  createCollaboration: (data: Partial<any>) => 
    apiPost<any>('/enterprise/content/collaborations', data),
  
  getActiveCollaborations: (documentId: number) => 
    apiGet<any[]>(`/enterprise/content/documents/${documentId}/collaborations`),
};

// Enterprise Financial Management API
export const financialApi = {
  // Chart of Accounts
  getAccounts: (params?: FilterOptions) =>
    apiGet<ChartOfAccounts[]>('/enterprise/financial/accounts', params),

  getAccount: (id: number) =>
    apiGet<ChartOfAccounts>(`/enterprise/financial/accounts/${id}`),

  createAccount: (data: Partial<ChartOfAccounts>) =>
    apiPost<ChartOfAccounts>('/enterprise/financial/accounts', data),

  updateAccount: (id: number, data: Partial<ChartOfAccounts>) =>
    apiPut<ChartOfAccounts>(`/enterprise/financial/accounts/${id}`, data),

  deleteAccount: (id: number) =>
    apiDelete<void>(`/enterprise/financial/accounts/${id}`),

  // General Ledger
  getGLEntries: (params?: FilterOptions) =>
    apiGet<GeneralLedger[]>('/enterprise/financial/gl-entries', params),

  getGLEntry: (id: number) =>
    apiGet<GeneralLedger>(`/enterprise/financial/gl-entries/${id}`),

  createGLEntry: (data: Partial<GeneralLedger>) =>
    apiPost<GeneralLedger>('/enterprise/financial/gl-entries', data),

  updateGLEntry: (id: number, data: Partial<GeneralLedger>) =>
    apiPut<GeneralLedger>(`/enterprise/financial/gl-entries/${id}`, data),

  deleteGLEntry: (id: number) =>
    apiDelete<void>(`/enterprise/financial/gl-entries/${id}`),

  postGLEntry: (id: number) =>
    apiPost<void>(`/enterprise/financial/gl-entries/${id}/post`, {}),

  // Budget Management
  getBudgets: (params?: FilterOptions) =>
    apiGet<BudgetPlan[]>('/enterprise/financial/budgets', params),

  getBudget: (id: number) =>
    apiGet<BudgetPlan>(`/enterprise/financial/budgets/${id}`),

  createBudget: (data: Partial<BudgetPlan>) =>
    apiPost<BudgetPlan>('/enterprise/financial/budgets', data),

  updateBudget: (id: number, data: Partial<BudgetPlan>) =>
    apiPut<BudgetPlan>(`/enterprise/financial/budgets/${id}`, data),

  deleteBudget: (id: number) =>
    apiDelete<void>(`/enterprise/financial/budgets/${id}`),

  approveBudget: (id: number) =>
    apiPost<void>(`/enterprise/financial/budgets/${id}/approve`, {}),

  getBudgetVarianceAnalysis: (fiscalYear: string) =>
    apiGet<any>('/enterprise/financial/budgets/variance-analysis', { fiscal_year: fiscalYear }),

  // Cost Centers
  getCostCenters: (params?: FilterOptions) =>
    apiGet<CostCenter[]>('/enterprise/financial/cost-centers', params),

  getCostCenter: (id: number) =>
    apiGet<CostCenter>(`/enterprise/financial/cost-centers/${id}`),

  createCostCenter: (data: Partial<CostCenter>) =>
    apiPost<CostCenter>('/enterprise/financial/cost-centers', data),

  updateCostCenter: (id: number, data: Partial<CostCenter>) =>
    apiPut<CostCenter>(`/enterprise/financial/cost-centers/${id}`, data),

  deleteCostCenter: (id: number) =>
    apiDelete<void>(`/enterprise/financial/cost-centers/${id}`),

  // Financial Reports
  getFinancialReports: (params?: FilterOptions) =>
    apiGet<FinancialReport[]>('/enterprise/financial/reports', params),

  getFinancialReport: (id: number) =>
    apiGet<FinancialReport>(`/enterprise/financial/reports/${id}`),

  generateFinancialReport: (data: Partial<FinancialReport>) =>
    apiPost<FinancialReport>('/enterprise/financial/reports', data),

  updateFinancialReport: (id: number, data: Partial<FinancialReport>) =>
    apiPut<FinancialReport>(`/enterprise/financial/reports/${id}`, data),

  deleteFinancialReport: (id: number) =>
    apiDelete<void>(`/enterprise/financial/reports/${id}`),
};

// Enterprise Compliance Management API
export const complianceApi = {
  // Compliance Requirements
  getRequirements: (params?: FilterOptions) => 
    apiGet<ComplianceRequirement[]>('/enterprise/compliance/requirements', params),
  
  getRequirement: (id: number) => 
    apiGet<ComplianceRequirement>(`/enterprise/compliance/requirements/${id}`),
  
  createRequirement: (data: Partial<ComplianceRequirement>) => 
    apiPost<ComplianceRequirement>('/enterprise/compliance/requirements', data),
  
  updateRequirement: (id: number, data: Partial<ComplianceRequirement>) =>
    apiPut<ComplianceRequirement>(`/enterprise/compliance/requirements/${id}`, data),

  deleteRequirement: (id: number) =>
    apiDelete<void>(`/enterprise/compliance/requirements/${id}`),

  // Compliance Assessments
  getAssessments: (params?: FilterOptions) =>
    apiGet<ComplianceAssessment[]>('/enterprise/compliance/assessments', params),

  getAssessment: (id: number) =>
    apiGet<ComplianceAssessment>(`/enterprise/compliance/assessments/${id}`),

  createAssessment: (data: Partial<ComplianceAssessment>) =>
    apiPost<ComplianceAssessment>('/enterprise/compliance/assessments', data),

  updateAssessment: (id: number, data: Partial<ComplianceAssessment>) =>
    apiPut<ComplianceAssessment>(`/enterprise/compliance/assessments/${id}`, data),

  deleteAssessment: (id: number) =>
    apiDelete<void>(`/enterprise/compliance/assessments/${id}`),

  startAssessment: (id: number) =>
    apiPost<void>(`/enterprise/compliance/assessments/${id}/start`, {}),

  completeAssessment: (id: number, data: { overall_score: number; compliance_level: string }) =>
    apiPost<void>(`/enterprise/compliance/assessments/${id}/complete`, data),

  // Compliance Findings
  getFindings: (params?: FilterOptions) =>
    apiGet<any[]>('/enterprise/compliance/findings', params),

  getFinding: (id: number) =>
    apiGet<any>(`/enterprise/compliance/findings/${id}`),

  createFinding: (data: any) =>
    apiPost<any>('/enterprise/compliance/findings', data),

  updateFinding: (id: number, data: any) =>
    apiPut<any>(`/enterprise/compliance/findings/${id}`, data),

  deleteFinding: (id: number) =>
    apiDelete<void>(`/enterprise/compliance/findings/${id}`),

  updateFindingStatus: (id: number, data: { status: string; resolution_notes?: string }) =>
    apiPut<void>(`/enterprise/compliance/findings/${id}/status`, data),

  // Risk Assessments
  getRisks: (params?: FilterOptions) =>
    apiGet<RiskAssessment[]>('/enterprise/compliance/risks', params),

  getRisk: (id: number) =>
    apiGet<RiskAssessment>(`/enterprise/compliance/risks/${id}`),

  createRisk: (data: Partial<RiskAssessment>) =>
    apiPost<RiskAssessment>('/enterprise/compliance/risks', data),

  updateRisk: (id: number, data: Partial<RiskAssessment>) =>
    apiPut<RiskAssessment>(`/enterprise/compliance/risks/${id}`, data),

  deleteRisk: (id: number) =>
    apiDelete<void>(`/enterprise/compliance/risks/${id}`),

  getRiskDashboard: () =>
    apiGet<any>('/enterprise/compliance/risks/dashboard'),

  // Policy Management
  getPolicies: (params?: FilterOptions) =>
    apiGet<any[]>('/enterprise/compliance/policies', params),

  getPolicy: (id: number) =>
    apiGet<any>(`/enterprise/compliance/policies/${id}`),

  createPolicy: (data: any) =>
    apiPost<any>('/enterprise/compliance/policies', data),

  updatePolicy: (id: number, data: any) =>
    apiPut<any>(`/enterprise/compliance/policies/${id}`, data),

  deletePolicy: (id: number) =>
    apiDelete<void>(`/enterprise/compliance/policies/${id}`),

  approvePolicy: (id: number) =>
    apiPost<void>(`/enterprise/compliance/policies/${id}/approve`, {}),
};

// Enterprise Business Intelligence API
export const biApi = {
  // Data Warehouses
  getDataWarehouses: (params?: FilterOptions) => 
    apiGet<any[]>('/enterprise/bi/warehouses', params),
  
  createDataWarehouse: (data: any) => 
    apiPost<any>('/enterprise/bi/warehouses', data),

  // Data Sources
  getDataSources: (params?: FilterOptions) => 
    apiGet<any[]>('/enterprise/bi/data-sources', params),
  
  createDataSource: (data: any) => 
    apiPost<any>('/enterprise/bi/data-sources', data),
  
  syncDataSource: (id: number) => 
    apiPost<void>(`/enterprise/bi/data-sources/${id}/sync`, {}),

  // Dashboards
  getDashboards: (params?: FilterOptions) => 
    apiGet<Dashboard[]>('/enterprise/bi/dashboards', params),
  
  getDashboard: (id: number) => 
    apiGet<Dashboard>(`/enterprise/bi/dashboards/${id}`),
  
  createDashboard: (data: Partial<Dashboard>) =>
    apiPost<Dashboard>('/enterprise/bi/dashboards', data),

  updateDashboard: (id: number, data: Partial<Dashboard>) =>
    apiPut<Dashboard>(`/enterprise/bi/dashboards/${id}`, data),

  deleteDashboard: (id: number) =>
    apiDelete<void>(`/enterprise/bi/dashboards/${id}`),

  refreshDashboard: (id: number) =>
    apiPost<void>(`/enterprise/bi/dashboards/${id}/refresh`, {}),

  // Reports
  getReports: (params?: FilterOptions) => 
    apiGet<Report[]>('/enterprise/bi/reports', params),
  
  createReport: (data: Partial<Report>) =>
    apiPost<Report>('/enterprise/bi/reports', data),

  getReport: (id: number) =>
    apiGet<Report>(`/enterprise/bi/reports/${id}`),

  updateReport: (id: number, data: Partial<Report>) =>
    apiPut<Report>(`/enterprise/bi/reports/${id}`, data),

  deleteReport: (id: number) =>
    apiDelete<void>(`/enterprise/bi/reports/${id}`),

  executeReport: (id: number) =>
    apiPost<any>(`/enterprise/bi/reports/${id}/execute`, {}),

  // KPIs
  getKPIs: (params?: FilterOptions) =>
    apiGet<KPI[]>('/enterprise/bi/kpis', params),

  createKPI: (data: Partial<KPI>) =>
    apiPost<KPI>('/enterprise/bi/kpis', data),

  updateKPIValue: (id: number, data: { current_value: number }) =>
    apiPut<void>(`/enterprise/bi/kpis/${id}/value`, data),

  deleteKPI: (id: number) =>
    apiDelete<void>(`/enterprise/bi/kpis/${id}`),

  // Data Mining
  getDataMiningModels: (params?: FilterOptions) => 
    apiGet<any[]>('/enterprise/bi/data-mining', params),
  
  createDataMiningModel: (data: any) =>
    apiPost<any>('/enterprise/bi/data-mining', data),

  deleteDataMiningModel: (id: number) =>
    apiDelete<void>(`/enterprise/bi/data-mining/${id}`),

  trainDataMiningModel: (id: number) =>
    apiPost<void>(`/enterprise/bi/data-mining/${id}/train`, {}),

  stopDataMiningModel: (id: number) =>
    apiPost<void>(`/enterprise/bi/data-mining/${id}/stop`, {}),
};

// Enterprise Human Resources API
export const hrApi = {
  // Employees
  getEmployees: (params?: FilterOptions) => 
    apiGet<Employee[]>('/enterprise/hr/employees', params),
  
  getEmployee: (id: number) => 
    apiGet<Employee>(`/enterprise/hr/employees/${id}`),
  
  createEmployee: (data: Partial<Employee>) => 
    apiPost<Employee>('/enterprise/hr/employees', data),
  
  updateEmployee: (id: number, data: Partial<Employee>) =>
    apiPut<Employee>(`/enterprise/hr/employees/${id}`, data),

  deleteEmployee: (id: number) =>
    apiDelete<void>(`/enterprise/hr/employees/${id}`),

  terminateEmployee: (id: number, data: { termination_date: string; termination_reason: string }) =>
    apiPost<void>(`/enterprise/hr/employees/${id}/terminate`, data),

  // Departments
  getDepartments: (params?: FilterOptions) =>
    apiGet<Department[]>('/enterprise/hr/departments', params),

  getDepartment: (id: number) =>
    apiGet<Department>(`/enterprise/hr/departments/${id}`),

  createDepartment: (data: Partial<Department>) =>
    apiPost<Department>('/enterprise/hr/departments', data),

  updateDepartment: (id: number, data: Partial<Department>) =>
    apiPut<Department>(`/enterprise/hr/departments/${id}`, data),

  deleteDepartment: (id: number) =>
    apiDelete<void>(`/enterprise/hr/departments/${id}`),

  // Positions
  getPositions: (params?: FilterOptions) => 
    apiGet<any[]>('/enterprise/hr/positions', params),
  
  createPosition: (data: any) =>
    apiPost<any>('/enterprise/hr/positions', data),

  getPosition: (id: number) =>
    apiGet<any>(`/enterprise/hr/positions/${id}`),

  updatePosition: (id: number, data: any) =>
    apiPut<any>(`/enterprise/hr/positions/${id}`, data),

  deletePosition: (id: number) =>
    apiDelete<void>(`/enterprise/hr/positions/${id}`),

  approvePosition: (id: number) =>
    apiPost<void>(`/enterprise/hr/positions/${id}/approve`, {}),

  // Performance Reviews
  getPerformanceReviews: (params?: FilterOptions) => 
    apiGet<any[]>('/enterprise/hr/performance-reviews', params),
  
  createPerformanceReview: (data: any) => 
    apiPost<any>('/enterprise/hr/performance-reviews', data),
  
  submitPerformanceReview: (id: number) => 
    apiPost<void>(`/enterprise/hr/performance-reviews/${id}/submit`, {}),
  
  approvePerformanceReview: (id: number) => 
    apiPost<void>(`/enterprise/hr/performance-reviews/${id}/approve`, {}),

  // Training
  getTrainings: (params?: FilterOptions) => 
    apiGet<Training[]>('/enterprise/hr/trainings', params),
  
  createTraining: (data: Partial<Training>) =>
    apiPost<Training>('/enterprise/hr/trainings', data),

  deleteTraining: (id: number) =>
    apiDelete<void>(`/enterprise/hr/trainings/${id}`),

  enrollInTraining: (trainingId: number, data: { employee_id: number }) =>
    apiPost<any>(`/enterprise/hr/trainings/${trainingId}/enroll`, data),
  
  getTrainingEnrollments: (params?: FilterOptions) => 
    apiGet<any[]>('/enterprise/hr/training-enrollments', params),
  
  completeTraining: (id: number, data: any) => 
    apiPost<void>(`/enterprise/hr/training-enrollments/${id}/complete`, data),

  // HR Analytics
  getHRDashboard: () => 
    apiGet<any>('/enterprise/hr/dashboard'),
};
