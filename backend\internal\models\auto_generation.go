package models

import (
	"time"
)

// AutoGenerationConfig represents user-specific auto-generation settings
type AutoGenerationConfig struct {
	ID                           uint      `json:"id" gorm:"primaryKey"`
	CreatedAt                    time.Time `json:"created_at"`
	UpdatedAt                    time.Time `json:"updated_at"`
	UserID                       uint      `json:"user_id" gorm:"not null"`
	EntityType                   string    `json:"entity_type" gorm:"not null"`
	EnableSummaryGeneration      bool      `json:"enable_summary_generation" gorm:"default:true"`
	EnableTaskGeneration         bool      `json:"enable_task_generation" gorm:"default:true"`
	EnableCalendarGeneration     bool      `json:"enable_calendar_generation" gorm:"default:true"`
	EnableFinanceGeneration      bool      `json:"enable_finance_generation" gorm:"default:true"`
	EnableRelationshipGeneration bool      `json:"enable_relationship_generation" gorm:"default:true"`
	ConfigData                   string    `json:"config_data" gorm:"type:text"` // JSON configuration

	// Relationships
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// SystemEvent represents a system event for tracking auto-generation activities
type SystemEvent struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time `json:"created_at"`
	EventType string    `json:"event_type" gorm:"not null"`
	EntityID  *uint     `json:"entity_id"`
	UserID    *uint     `json:"user_id"`
	Data      string    `json:"data" gorm:"type:text"` // JSON data
	Processed bool      `json:"processed" gorm:"default:false"`
	Error     string    `json:"error"`

	// Relationships
	User *User `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// EntityRelationship represents a generic relationship between entities
type EntityRelationship struct {
	ID               uint                     `json:"id" gorm:"primaryKey"`
	CreatedAt        time.Time                `json:"created_at"`
	UpdatedAt        time.Time                `json:"updated_at"`
	SourceEntityType string                   `json:"source_entity_type" gorm:"not null"`
	SourceEntityID   uint                     `json:"source_entity_id" gorm:"not null"`
	TargetEntityType string                   `json:"target_entity_type" gorm:"not null"`
	TargetEntityID   uint                     `json:"target_entity_id" gorm:"not null"`
	RelationshipType DocumentRelationshipType `json:"relationship_type" gorm:"not null"`
	Description      string                   `json:"description"`
	IsActive         bool                     `json:"is_active" gorm:"default:true"`
	CreatedByID      uint                     `json:"created_by_id" gorm:"not null"`

	// Relationships
	CreatedBy User `json:"created_by" gorm:"foreignKey:CreatedByID"`
}

// TableName returns the table name for AutoGenerationConfig
func (AutoGenerationConfig) TableName() string {
	return "auto_generation_configs"
}

// TableName returns the table name for SystemEvent
func (SystemEvent) TableName() string {
	return "system_events"
}

// TableName returns the table name for EntityRelationship
func (EntityRelationship) TableName() string {
	return "entity_relationships"
}
