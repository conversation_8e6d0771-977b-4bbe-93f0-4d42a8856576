'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { contentApi } from '../../../services/enterpriseApi';
import { ContentRepository } from '../../../types/enterprise';

const ContentRepositoriesListPage: React.FC = () => {
  const router = useRouter();
  const [repositories, setRepositories] = useState<ContentRepository[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('');

  useEffect(() => {
    fetchRepositories();
  }, []);

  const fetchRepositories = async () => {
    try {
      setLoading(true);
      const response = await contentApi.getRepositories({
        search: searchTerm,
        repository_type: filterType || undefined,
      });
      setRepositories(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch content repositories');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this content repository?')) return;
    
    try {
      await contentApi.deleteRepository(id);
      await fetchRepositories(); // Refresh the list
    } catch (err: any) {
      setError(err.message || 'Failed to delete content repository');
    }
  };

  const filteredRepositories = repositories.filter(repository =>
    repository.repository_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    repository.repository_code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) return <div className="p-6">Loading content repositories...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Content Repositories</h1>
        <button
          onClick={() => router.push('/enterprise/content/repositories/new')}
          className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
        >
          Create New Repository
        </button>
      </div>

      {/* Search and Filter */}
      <div className="mb-6 flex gap-4">
        <input
          type="text"
          placeholder="Search repositories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="border rounded px-3 py-2 flex-1"
        />
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="border rounded px-3 py-2"
        >
          <option value="">All Types</option>
          <option value="document">Document</option>
          <option value="media">Media</option>
          <option value="template">Template</option>
          <option value="archive">Archive</option>
        </select>
        <button
          onClick={fetchRepositories}
          className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
        >
          Search
        </button>
      </div>

      {/* Repositories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredRepositories.map((repository) => (
          <div key={repository.id} className="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{repository.repository_name}</h3>
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  repository.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {repository.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
              
              <p className="text-sm text-gray-600 mb-2">Code: {repository.repository_code}</p>
              <p className="text-sm text-gray-600 mb-4">Type: {repository.repository_type}</p>
              
              {repository.description && (
                <p className="text-sm text-gray-700 mb-4 line-clamp-3">{repository.description}</p>
              )}
              
              <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>Storage: {repository.storage_type}</span>
                <span>Size: {repository.total_size ? `${(repository.total_size / 1024 / 1024).toFixed(1)} MB` : 'N/A'}</span>
              </div>
              
              <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>Files: {repository.file_count || 0}</span>
                <span className={`inline-flex px-2 py-1 rounded-full ${
                  repository.is_public 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {repository.is_public ? 'Public' : 'Private'}
                </span>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => router.push(`/enterprise/content/repositories/${repository.id}`)}
                  className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700"
                >
                  View
                </button>
                <button
                  onClick={() => router.push(`/enterprise/content/repositories/${repository.id}/edit`)}
                  className="flex-1 bg-green-600 text-white px-3 py-2 rounded text-sm hover:bg-green-700"
                >
                  Edit
                </button>
                <button
                  onClick={() => handleDelete(repository.id)}
                  className="bg-red-600 text-white px-3 py-2 rounded text-sm hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {filteredRepositories.length === 0 && (
        <div className="text-center py-12 text-gray-500">
          <div className="text-6xl mb-4">📁</div>
          <h3 className="text-lg font-medium mb-2">No repositories found</h3>
          <p className="text-sm">Create your first content repository to start managing documents and media.</p>
        </div>
      )}
    </div>
  );
};

export default ContentRepositoriesListPage;
