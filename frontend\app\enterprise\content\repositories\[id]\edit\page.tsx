'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { contentApi } from '../../../../../services/enterpriseApi';
import { ContentRepository } from '../../../../../types/enterprise';

const EditContentRepositoryPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const repositoryId = parseInt(params.id as string);
  
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<Partial<ContentRepository>>({
    repository_code: '',
    repository_name: '',
    name: '',
    description: '',
    type: 'document',
    repository_type: 'document',
    storage_type: 'local',
    storage_path: '',
    storage_config: '',
    max_size: 1073741824, // 1GB
    current_size: 0,
    total_size: 0,
    max_files: 10000,
    current_files: 0,
    file_count: 0,
    classification: 'internal',
    encryption_key: '',
    access_policy: '',
    is_public: false,
    versioning_enabled: true,
    max_versions: 10,
    backup_enabled: true,
    backup_schedule: 'daily',
    retention_policy_id: undefined,
    compliance_level: 'standard',
    is_active: true,
    last_accessed: '',
    access_count: 0,
    metadata: ''
  });

  useEffect(() => {
    if (repositoryId) {
      fetchRepository();
    }
  }, [repositoryId]);

  const fetchRepository = async () => {
    try {
      setFetchLoading(true);
      const response = await contentApi.getRepository(repositoryId);
      const repository = response.data;
      setFormData({
        ...repository,
        name: repository.repository_name || repository.name,
        repository_type: repository.type || repository.repository_type,
        total_size: repository.current_size || repository.total_size,
        file_count: repository.current_files || repository.file_count
      });
    } catch (err: any) {
      setError(err.message || 'Failed to fetch content repository');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Ensure aliases are set
      const submitData = {
        ...formData,
        name: formData.repository_name,
        repository_type: formData.type,
        total_size: formData.current_size,
        file_count: formData.current_files
      };
      await contentApi.updateRepository(repositoryId, submitData);
      router.push(`/enterprise/content/repositories/${repositoryId}`);
    } catch (err: any) {
      setError(err.message || 'Failed to update content repository');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked 
              : type === 'number' ? parseFloat(value) || 0 
              : value
    }));
  };

  if (fetchLoading) return <div className="p-6">Loading content repository...</div>;
  if (error && fetchLoading) return <div className="p-6 text-red-600">Error: {error}</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Edit Content Repository</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push(`/enterprise/content/repositories/${repositoryId}`)}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Repository Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Repository Code *
            </label>
            <input
              type="text"
              name="repository_code"
              value={formData.repository_code}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Repository Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Repository Name *
            </label>
            <input
              type="text"
              name="repository_name"
              value={formData.repository_name}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Repository Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Repository Type *
            </label>
            <select
              name="type"
              value={formData.type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="document">Document</option>
              <option value="media">Media</option>
              <option value="template">Template</option>
              <option value="archive">Archive</option>
              <option value="backup">Backup</option>
            </select>
          </div>

          {/* Storage Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Storage Type *
            </label>
            <select
              name="storage_type"
              value={formData.storage_type}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="local">Local Storage</option>
              <option value="s3">Amazon S3</option>
              <option value="azure">Azure Blob</option>
              <option value="gcp">Google Cloud Storage</option>
              <option value="ftp">FTP Server</option>
              <option value="network">Network Drive</option>
            </select>
          </div>

          {/* Classification */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Classification *
            </label>
            <select
              name="classification"
              value={formData.classification}
              onChange={handleChange}
              required
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="public">Public</option>
              <option value="internal">Internal</option>
              <option value="confidential">Confidential</option>
              <option value="restricted">Restricted</option>
              <option value="top_secret">Top Secret</option>
            </select>
          </div>

          {/* Max Size (MB) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Size (MB)
            </label>
            <input
              type="number"
              name="max_size"
              value={Math.round(formData.max_size / 1024 / 1024)}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                max_size: parseFloat(e.target.value) * 1024 * 1024 || 0
              }))}
              min="1"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Max Files */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Files
            </label>
            <input
              type="number"
              name="max_files"
              value={formData.max_files}
              onChange={handleChange}
              min="1"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Max Versions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Max Versions
            </label>
            <input
              type="number"
              name="max_versions"
              value={formData.max_versions}
              onChange={handleChange}
              min="1"
              max="100"
              className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Description */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Storage Configuration */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Storage Configuration (JSON)
          </label>
          <textarea
            name="storage_config"
            value={formData.storage_config}
            onChange={handleChange}
            rows={3}
            className="w-full border rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Checkboxes */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_public"
              checked={formData.is_public}
              onChange={handleChange}
              className="mr-2"
            />
            Public Repository
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="versioning_enabled"
              checked={formData.versioning_enabled}
              onChange={handleChange}
              className="mr-2"
            />
            Enable Versioning
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="backup_enabled"
              checked={formData.backup_enabled}
              onChange={handleChange}
              className="mr-2"
            />
            Enable Backup
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              name="is_active"
              checked={formData.is_active}
              onChange={handleChange}
              className="mr-2"
            />
            Active Repository
          </label>
        </div>

        {/* Submit Button */}
        <div className="mt-8 flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.push(`/enterprise/content/repositories/${repositoryId}`)}
            className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Updating...' : 'Update Repository'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default EditContentRepositoryPage;
