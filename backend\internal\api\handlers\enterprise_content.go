package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// ContentTemplateRequest represents the request structure for content templates
type ContentTemplateRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	Type        string `json:"type" binding:"required"`
	Content     string `json:"content" binding:"required"`
	Variables   string `json:"variables"`
	IsActive    bool   `json:"is_active"`
	CategoryID  uint   `json:"category_id"`
}

// GetContentTemplates returns all content templates with pagination
func GetContentTemplates(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.<PERSON>ult<PERSON>("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total content templates
	var total int64
	db.Model(&models.ContentTemplate{}).Count(&total)

	// Get content templates with pagination
	var templates []models.ContentTemplate
	offset := (page - 1) * perPage
	if err := db.Preload("Category").
		Order("name ASC").
		Limit(perPage).
		Offset(offset).
		Find(&templates).Error; err != nil {
		HandleInternalError(c, "Failed to fetch content templates: "+err.Error())
		return
	}

	// Convert to response format
	templateResponses := make([]gin.H, len(templates))
	for i, template := range templates {
		templateResponses[i] = gin.H{
			"id":          template.ID,
			"name":        template.Name,
			"description": template.Description,
			"type":        template.Type,
			"content":     template.Content,
			"variables":   template.Variables,
			"is_active":   template.IsActive,
			"category":    template.Category,
			"created_at":  template.CreatedAt,
			"updated_at":  template.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, PaginationResponse{
		Data:       templateResponses,
		Total:      total,
		Page:       page,
		PerPage:    perPage,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	})
}

// GetContentTemplate returns a single content template by ID
func GetContentTemplate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get content template
	var template models.ContentTemplate
	if err := db.Preload("Category").First(&template, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Content template")
			return
		}
		HandleInternalError(c, "Failed to fetch content template: "+err.Error())
		return
	}

	response := gin.H{
		"id":          template.ID,
		"name":        template.Name,
		"description": template.Description,
		"type":        template.Type,
		"content":     template.Content,
		"variables":   template.Variables,
		"is_active":   template.IsActive,
		"category":    template.Category,
		"created_at":  template.CreatedAt,
		"updated_at":  template.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Content template retrieved successfully",
		Data:    response,
	})
}

// CreateContentTemplate creates a new content template
func CreateContentTemplate(c *gin.Context) {
	var req ContentTemplateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create content template
	template := &models.ContentTemplate{
		Name:        req.Name,
		Description: req.Description,
		Type:        req.Type,
		Content:     req.Content,
		Variables:   req.Variables,
		IsActive:    req.IsActive,
		Category:    "default",
	}

	if err := db.Create(template).Error; err != nil {
		HandleInternalError(c, "Failed to create content template: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Category").First(template, template.ID)

	response := gin.H{
		"id":          template.ID,
		"name":        template.Name,
		"description": template.Description,
		"type":        template.Type,
		"content":     template.Content,
		"variables":   template.Variables,
		"is_active":   template.IsActive,
		"category":    template.Category,
		"created_at":  template.CreatedAt,
		"updated_at":  template.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Content template created successfully",
		Data:    response,
	})
}

// UpdateContentTemplate updates an existing content template
func UpdateContentTemplate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req ContentTemplateRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing content template
	var template models.ContentTemplate
	if err := db.First(&template, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Content template")
			return
		}
		HandleInternalError(c, "Failed to fetch content template: "+err.Error())
		return
	}

	// Update content template fields
	template.Name = req.Name
	template.Description = req.Description
	template.Type = req.Type
	template.Content = req.Content
	template.Variables = req.Variables
	template.IsActive = req.IsActive
	template.Category = "default"

	if err := db.Save(&template).Error; err != nil {
		HandleInternalError(c, "Failed to update content template: "+err.Error())
		return
	}

	// Load related data
	db.Preload("Category").First(&template, template.ID)

	response := gin.H{
		"id":          template.ID,
		"name":        template.Name,
		"description": template.Description,
		"type":        template.Type,
		"content":     template.Content,
		"variables":   template.Variables,
		"is_active":   template.IsActive,
		"category":    template.Category,
		"created_at":  template.CreatedAt,
		"updated_at":  template.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Content template updated successfully",
		Data:    response,
	})
}

// DeleteContentTemplate deletes a content template
func DeleteContentTemplate(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if content template exists
	var template models.ContentTemplate
	if err := db.First(&template, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Content template")
			return
		}
		HandleInternalError(c, "Failed to fetch content template: "+err.Error())
		return
	}

	// Delete content template
	if err := db.Delete(&template).Error; err != nil {
		HandleInternalError(c, "Failed to delete content template: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Content template deleted successfully",
	})
}
