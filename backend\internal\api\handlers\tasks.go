package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
	"federal-register-clone/internal/services"
)

// TaskRequest represents the request structure for tasks
type TaskRequest struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Priority    string `json:"priority"`
	DueDate     string `json:"due_date"`
	AssignedTo  uint   `json:"assigned_to"`
	CreatedBy   uint   `json:"created_by"`
	CategoryID  uint   `json:"category_id"`
	EntityType  string `json:"entity_type"`
	EntityID    uint   `json:"entity_id"`
}

// UpdateTaskRequest represents a partial update request for tasks
type UpdateTaskRequest struct {
	Title       *string `json:"title"`
	Description *string `json:"description"`
	Status      *string `json:"status"`
	Priority    *string `json:"priority"`
	DueDate     *string `json:"due_date"`
	AssignedTo  *uint   `json:"assigned_to"`
	CategoryID  *uint   `json:"category_id"`
	EntityType  *string `json:"entity_type"`
	EntityID    *uint   `json:"entity_id"`
}

// GetTasksTest - temporary test function to bypass GORM issue
func GetTasksTest(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"message": "Test tasks endpoint working",
		"data":    []gin.H{},
	})
}

// GetTasks returns all tasks with pagination
func GetTasks(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Temporarily hardcode total to isolate the GORM issue
	var total int64 = 0
	// db.Raw("SELECT COUNT(*) FROM tasks WHERE deleted_at IS NULL").Scan(&total)

	// Use raw SQL query as workaround for GORM relationship issue
	type TaskBasic struct {
		ID           uint       `json:"id"`
		Title        string     `json:"title"`
		Description  string     `json:"description"`
		Type         string     `json:"type"`
		Status       string     `json:"status"`
		Priority     string     `json:"priority"`
		DueDate      *time.Time `json:"due_date"`
		StartDate    *time.Time `json:"start_date"`
		EndDate      *time.Time `json:"end_date"`
		AssignedToID *uint      `json:"assigned_to_id"`
		CreatedByID  uint       `json:"created_by_id"`
		CategoryID   *uint      `json:"category_id"`
		SourceType   string     `json:"source_type"`
		SourceID     *uint      `json:"source_id"`
		CreatedAt    time.Time  `json:"created_at"`
		UpdatedAt    time.Time  `json:"updated_at"`
	}

	var tasks []TaskBasic
	offset := (page - 1) * perPage

	query := `
		SELECT id, title, description, type, status, priority, due_date, start_date, end_date,
		       assigned_to_id, created_by_id, category_id, source_type, source_id, created_at, updated_at
		FROM tasks
		WHERE deleted_at IS NULL
		ORDER BY created_at DESC
		LIMIT ? OFFSET ?`

	if err := db.Raw(query, perPage, offset).Scan(&tasks).Error; err != nil {
		HandleInternalError(c, "Failed to fetch tasks: "+err.Error())
		return
	}

	// Return the actual tasks data
	c.JSON(http.StatusOK, gin.H{
		"message":  "Tasks retrieved successfully",
		"data":     tasks,
		"total":    total,
		"page":     page,
		"per_page": perPage,
	})
}

// GetTask returns a single task by ID
func GetTask(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get task
	var task models.Task
	if err := db.Preload("AssignedTo").
		Preload("CreatedBy").
		Preload("Category").
		First(&task, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	response := gin.H{
		"id":          task.ID,
		"title":       task.Title,
		"description": task.Description,
		"type":        task.Type,
		"status":      task.Status,
		"priority":    task.Priority,
		"due_date":    task.DueDate,
		"start_date":  task.StartDate,
		"end_date":    task.EndDate,
		"assigned_to": task.AssignedTo,
		"created_by":  task.CreatedBy,
		"category":    task.Category,
		"document":    task.Document,
		"regulation":  task.Regulation,
		"agency":      task.Agency,
		"source_type": task.SourceType,
		"source_id":   task.SourceID,
		"created_at":  task.CreatedAt,
		"updated_at":  task.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task retrieved successfully",
		Data:    response,
	})
}

// CreateTask creates a new task
func CreateTask(c *gin.Context) {
	var req TaskRequest
	if !BindJSON(c, &req) {
		return
	}

	// Validate required fields
	if strings.TrimSpace(req.Title) == "" {
		HandleBadRequest(c, "Title is required")
		return
	}

	// Validate status
	if req.Status != "" {
		validStatuses := []string{"pending", "in_progress", "completed", "cancelled", "on_hold"}
		statusValid := false
		for _, validStatus := range validStatuses {
			if req.Status == validStatus {
				statusValid = true
				break
			}
		}
		if !statusValid {
			HandleBadRequest(c, "Invalid status. Must be one of: pending, in_progress, completed, cancelled, on_hold")
			return
		}
	} else {
		req.Status = "pending" // Default status
	}

	// Validate priority
	if req.Priority != "" {
		validPriorities := []string{"low", "medium", "high", "urgent"}
		priorityValid := false
		for _, validPriority := range validPriorities {
			if req.Priority == validPriority {
				priorityValid = true
				break
			}
		}
		if !priorityValid {
			HandleBadRequest(c, "Invalid priority. Must be one of: low, medium, high, urgent")
			return
		}
	} else {
		req.Priority = "medium" // Default priority
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create task
	task := &models.Task{
		Title:        req.Title,
		Description:  req.Description,
		Type:         models.TaskTypeGeneral,
		Status:       models.TaskStatus(req.Status),
		Priority:     models.TaskPriority(req.Priority),
		AssignedToID: &req.AssignedTo,
		CreatedByID:  req.CreatedBy,
		CategoryID:   &req.CategoryID,
		SourceType:   req.EntityType,
		SourceID:     &req.EntityID,
	}

	if err := db.Create(task).Error; err != nil {
		HandleInternalError(c, "Failed to create task: "+err.Error())
		return
	}

	// Load related data
	db.Preload("AssignedTo").Preload("CreatedBy").Preload("Category").Preload("Document").Preload("Regulation").Preload("Agency").First(task, task.ID)

	response := gin.H{
		"id":          task.ID,
		"title":       task.Title,
		"description": task.Description,
		"type":        task.Type,
		"status":      task.Status,
		"priority":    task.Priority,
		"due_date":    task.DueDate,
		"start_date":  task.StartDate,
		"end_date":    task.EndDate,
		"assigned_to": task.AssignedTo,
		"created_by":  task.CreatedBy,
		"category":    task.Category,
		"document":    task.Document,
		"regulation":  task.Regulation,
		"agency":      task.Agency,
		"source_type": task.SourceType,
		"source_id":   task.SourceID,
		"created_at":  task.CreatedAt,
		"updated_at":  task.UpdatedAt,
	}

	c.JSON(http.StatusCreated, SuccessResponse{
		Message: "Task created successfully",
		Data:    response,
	})
}

// UpdateTask updates an existing task
func UpdateTask(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req UpdateTaskRequest
	fmt.Printf("DEBUG: UpdateTask called for ID %d\n", id)
	if !BindJSON(c, &req) {
		fmt.Printf("DEBUG: BindJSON failed for UpdateTaskRequest\n")
		return
	}
	fmt.Printf("DEBUG: UpdateTask request bound successfully: Title=%v\n", req.Title)

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get existing task
	var task models.Task
	if err := db.First(&task, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Update task fields only if provided
	if req.Title != nil {
		if strings.TrimSpace(*req.Title) == "" {
			HandleBadRequest(c, "Title cannot be empty")
			return
		}
		task.Title = strings.TrimSpace(*req.Title)
	}
	if req.Description != nil {
		task.Description = *req.Description
	}
	if req.Status != nil {
		// Validate status value
		validStatuses := []string{"pending", "in_progress", "completed", "cancelled", "on_hold"}
		statusValid := false
		for _, validStatus := range validStatuses {
			if *req.Status == validStatus {
				statusValid = true
				break
			}
		}
		if !statusValid {
			HandleBadRequest(c, "Invalid status. Must be one of: pending, in_progress, completed, cancelled, on_hold")
			return
		}
		task.Status = models.TaskStatus(*req.Status)
	}
	if req.Priority != nil {
		// Validate priority value
		validPriorities := []string{"low", "medium", "high", "urgent"}
		priorityValid := false
		for _, validPriority := range validPriorities {
			if *req.Priority == validPriority {
				priorityValid = true
				break
			}
		}
		if !priorityValid {
			HandleBadRequest(c, "Invalid priority. Must be one of: low, medium, high, urgent")
			return
		}
		task.Priority = models.TaskPriority(*req.Priority)
	}
	if req.AssignedTo != nil {
		task.AssignedToID = req.AssignedTo
	}
	if req.CategoryID != nil {
		task.CategoryID = req.CategoryID
	}
	if req.EntityType != nil {
		task.SourceType = *req.EntityType
	}
	if req.EntityID != nil {
		task.SourceID = req.EntityID
	}

	if err := db.Save(&task).Error; err != nil {
		HandleInternalError(c, "Failed to update task: "+err.Error())
		return
	}

	// Load related data
	db.Preload("AssignedTo").Preload("CreatedBy").Preload("Category").Preload("Document").Preload("Regulation").Preload("Agency").First(&task, task.ID)

	response := gin.H{
		"id":          task.ID,
		"title":       task.Title,
		"description": task.Description,
		"type":        task.Type,
		"status":      task.Status,
		"priority":    task.Priority,
		"due_date":    task.DueDate,
		"start_date":  task.StartDate,
		"end_date":    task.EndDate,
		"assigned_to": task.AssignedTo,
		"created_by":  task.CreatedBy,
		"category":    task.Category,
		"document":    task.Document,
		"regulation":  task.Regulation,
		"agency":      task.Agency,
		"source_type": task.SourceType,
		"source_id":   task.SourceID,
		"created_at":  task.CreatedAt,
		"updated_at":  task.UpdatedAt,
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task updated successfully",
		Data:    response,
	})
}

// DeleteTask deletes a task
func DeleteTask(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Check if task exists
	var task models.Task
	if err := db.First(&task, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Delete task
	if err := db.Delete(&task).Error; err != nil {
		HandleInternalError(c, "Failed to delete task: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task deleted successfully",
	})
}

// CompleteTaskRequest represents a request to complete a task
type CompleteTaskRequest struct {
	CompletionNotes string `json:"completion_notes"`
	ActualHours     *int   `json:"actual_hours"`
	QualityScore    *int   `json:"quality_score"`
}

// CompleteTask marks a task as complete
func CompleteTask(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req CompleteTaskRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing task
	var task models.Task
	if err := db.First(&task, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Update task status to completed
	task.Status = models.TaskStatusCompleted
	task.CompletedAt = &time.Time{}
	*task.CompletedAt = time.Now()
	userIDUint := userID.(uint)
	task.CompletedBy = &userIDUint

	if req.CompletionNotes != "" {
		task.Notes = req.CompletionNotes
	}
	if req.QualityScore != nil {
		task.QualityScore = float64(*req.QualityScore)
	}

	if err := db.Save(&task).Error; err != nil {
		HandleInternalError(c, "Failed to complete task: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("AssignedTo").Preload("CreatedBy").Preload("CompletedBy").First(&task, task.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task completed successfully",
		Data:    task,
	})
}

// ParseTextRequest represents a request to parse text for task creation
type ParseTextRequest struct {
	Text       string `json:"text" binding:"required"`
	Context    string `json:"context"`
	ProjectID  *uint  `json:"project_id"`
	CategoryID *uint  `json:"category_id"`
	AutoCreate bool   `json:"auto_create"`
}

// ParseText parses text for task creation
func ParseText(c *gin.Context) {
	var req ParseTextRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Use advanced NLP service for intelligent text parsing
	nlpService := services.NewNLPService()

	// Extract deadlines and costs using NLP
	deadlines := nlpService.ExtractDeadlines(req.Text)
	costs := nlpService.ExtractCosts(req.Text)

	// Use extracted costs for task metadata
	var estimatedCost *float64
	if len(costs) > 0 {
		estimatedCost = &costs[0].Amount
	}

	// Enhanced text parsing with better algorithms
	title := req.Text
	if len(title) > 100 {
		// Find sentence boundary for better title extraction
		sentences := strings.Split(title, ".")
		if len(sentences) > 0 && len(sentences[0]) <= 100 {
			title = strings.TrimSpace(sentences[0])
		} else {
			title = title[:100] + "..."
		}
	}

	// Use NLP-extracted deadlines or fallback to keyword detection
	var dueDate *time.Time
	if len(deadlines) > 0 {
		dueDate = &deadlines[0].Date
	} else {
		// Fallback to simple keyword detection
		lowerText := strings.ToLower(req.Text)
		if strings.Contains(lowerText, "tomorrow") {
			tomorrow := time.Now().AddDate(0, 0, 1)
			dueDate = &tomorrow
		} else if strings.Contains(lowerText, "next week") {
			nextWeek := time.Now().AddDate(0, 0, 7)
			dueDate = &nextWeek
		} else if strings.Contains(lowerText, "next month") {
			nextMonth := time.Now().AddDate(0, 1, 0)
			dueDate = &nextMonth
		}
	}

	// Enhanced priority detection
	priority := models.TaskPriorityMedium
	lowerText := strings.ToLower(req.Text)

	// High priority indicators
	highPriorityWords := []string{"urgent", "asap", "immediately", "critical", "emergency", "high priority"}
	for _, word := range highPriorityWords {
		if strings.Contains(lowerText, word) {
			priority = models.TaskPriorityHigh
			break
		}
	}

	// Low priority indicators
	if priority == models.TaskPriorityMedium {
		lowPriorityWords := []string{"low priority", "when possible", "when you can", "no rush", "eventually"}
		for _, word := range lowPriorityWords {
			if strings.Contains(lowerText, word) {
				priority = models.TaskPriorityLow
				break
			}
		}
	}

	// Create parsed task structure
	parsedTask := gin.H{
		"title":          title,
		"description":    req.Text,
		"priority":       priority,
		"due_date":       dueDate,
		"context":        req.Context,
		"project_id":     req.ProjectID,
		"category_id":    req.CategoryID,
		"estimated_cost": estimatedCost,
		"deadlines":      deadlines,
		"costs":          costs,
	}

	// If auto_create is true, create the task
	if req.AutoCreate {
		task := models.Task{
			Title:          title,
			Description:    req.Text,
			Type:           models.TaskTypeGeneral,
			Priority:       priority,
			Status:         models.TaskStatusPending,
			CreatedByID:    userID.(uint),
			CategoryID:     req.CategoryID,
			SourceType:     "parsed_text",
			SourceText:     req.Text,
			ParsedFromText: true,
		}

		if dueDate != nil {
			task.DueDate = dueDate
		}

		if err := db.Create(&task).Error; err != nil {
			HandleInternalError(c, "Failed to create task: "+err.Error())
			return
		}

		// Load relationships for response
		db.Preload("CreatedBy").Preload("Category").First(&task, task.ID)

		c.JSON(http.StatusCreated, SuccessResponse{
			Message: "Task parsed and created successfully",
			Data: gin.H{
				"parsed_data":  parsedTask,
				"created_task": task,
			},
		})
	} else {
		c.JSON(http.StatusOK, SuccessResponse{
			Message: "Text parsed successfully",
			Data: gin.H{
				"parsed_data": parsedTask,
			},
		})
	}
}

// CalculateTaskPerformance calculates task performance
func CalculateTaskPerformance(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get task
	var task models.Task
	if err := db.First(&task, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Calculate performance metrics
	performance := gin.H{
		"task_id": task.ID,
		"title":   task.Title,
	}

	// Calculate completion time performance
	if task.CompletedAt != nil && task.DueDate != nil {
		timeDiff := task.CompletedAt.Sub(*task.DueDate)
		performance["completion_timeliness"] = gin.H{
			"due_date":     task.DueDate,
			"completed_at": task.CompletedAt,
			"days_early":   -timeDiff.Hours() / 24,
			"on_time":      timeDiff <= 0,
		}
	}

	// Calculate overall score based on existing performance fields
	score := task.PerformancePercentage
	if score == 0 {
		score = 100.0
		if task.CompletedAt != nil && task.DueDate != nil {
			if task.CompletedAt.After(*task.DueDate) {
				score -= 20 // Penalty for late completion
			}
		}
	}

	performance["overall_score"] = score
	performance["deadline_adherence_score"] = task.DeadlineAdherenceScore
	performance["quality_score"] = task.QualityScore
	performance["completion_efficiency"] = task.CompletionEfficiency
	performance["priority_handling_score"] = task.PriorityHandlingScore
	performance["calculated_at"] = time.Now()

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performance calculated successfully",
		Data:    performance,
	})
}

// UpdateTaskPerformanceRequest represents a request to update task performance
type UpdateTaskPerformanceRequest struct {
	PerformancePercentage  float64 `json:"performance_percentage"`
	DeadlineAdherenceScore float64 `json:"deadline_adherence_score"`
	QualityScore           float64 `json:"quality_score"`
	CompletionEfficiency   float64 `json:"completion_efficiency"`
	PriorityHandlingScore  float64 `json:"priority_handling_score"`
	PerformanceNotes       string  `json:"performance_notes"`
	IsAutoCalculated       bool    `json:"is_auto_calculated"`
}

// UpdateTaskPerformance updates task performance
func UpdateTaskPerformance(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	var req UpdateTaskPerformanceRequest
	if !BindJSON(c, &req) {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get existing task
	var task models.Task
	if err := db.First(&task, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Store previous performance in history
	if task.PerformancePercentage > 0 {
		history := models.TaskPerformanceHistory{
			TaskID:                 task.ID,
			PerformancePercentage:  task.PerformancePercentage,
			DeadlineAdherenceScore: task.DeadlineAdherenceScore,
			QualityScore:           task.QualityScore,
			CompletionEfficiency:   task.CompletionEfficiency,
			PriorityHandlingScore:  task.PriorityHandlingScore,
			PerformanceNotes:       task.PerformanceNotes,
			EvaluationDate:         time.Now(),
			IsAutoCalculated:       task.IsAutoCalculated,
			EvaluatedByID:          task.EvaluatedByID,
			ChangeReason:           "Performance update",
		}
		db.Create(&history)
	}

	// Update task performance fields
	task.PerformancePercentage = req.PerformancePercentage
	task.DeadlineAdherenceScore = req.DeadlineAdherenceScore
	task.QualityScore = req.QualityScore
	task.CompletionEfficiency = req.CompletionEfficiency
	task.PriorityHandlingScore = req.PriorityHandlingScore
	task.PerformanceNotes = req.PerformanceNotes
	task.IsAutoCalculated = req.IsAutoCalculated
	task.EvaluationDate = &time.Time{}
	*task.EvaluationDate = time.Now()
	userIDUint := userID.(uint)
	task.EvaluatedByID = &userIDUint

	if err := db.Save(&task).Error; err != nil {
		HandleInternalError(c, "Failed to update task performance: "+err.Error())
		return
	}

	// Load relationships for response
	db.Preload("EvaluatedBy").First(&task, task.ID)

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performance updated successfully",
		Data:    task,
	})
}

// GetTaskPerformanceHistory returns task performance history
func GetTaskPerformanceHistory(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get task
	var task models.Task
	if err := db.First(&task, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task")
			return
		}
		HandleInternalError(c, "Failed to fetch task: "+err.Error())
		return
	}

	// Get performance history
	var history []models.TaskPerformanceHistory
	if err := db.Where("task_id = ?", id).Preload("EvaluatedBy").Order("evaluation_date DESC").Find(&history).Error; err != nil {
		HandleInternalError(c, "Failed to fetch task performance history: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performance history retrieved successfully",
		Data: gin.H{
			"task_id": task.ID,
			"task":    task,
			"history": history,
		},
	})
}

// RecalculateAllTaskPerformances recalculates all task performances
func RecalculateAllTaskPerformances(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get all completed tasks
	var tasks []models.Task
	if err := db.Where("status = ?", models.TaskStatusCompleted).Find(&tasks).Error; err != nil {
		HandleInternalError(c, "Failed to fetch completed tasks: "+err.Error())
		return
	}

	recalculatedCount := 0
	userIDUint := userID.(uint)

	for _, task := range tasks {
		// Calculate basic performance metrics
		var performancePercentage float64 = 100.0
		var deadlineAdherenceScore float64 = 100.0
		var completionEfficiency float64 = 100.0

		// Calculate deadline adherence
		if task.CompletedAt != nil && task.DueDate != nil {
			if task.CompletedAt.After(*task.DueDate) {
				deadlineAdherenceScore = 50.0 // Penalty for late completion
				performancePercentage -= 20
			}
		}

		// Update task performance
		task.PerformancePercentage = performancePercentage
		task.DeadlineAdherenceScore = deadlineAdherenceScore
		task.CompletionEfficiency = completionEfficiency
		task.IsAutoCalculated = true
		task.EvaluationDate = &time.Time{}
		*task.EvaluationDate = time.Now()
		task.EvaluatedByID = &userIDUint

		if err := db.Save(&task).Error; err == nil {
			recalculatedCount++
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performances recalculated successfully",
		Data: gin.H{
			"recalculated_count": recalculatedCount,
			"total_tasks":        len(tasks),
			"recalculated_at":    time.Now(),
		},
	})
}

// GetTaskPerformanceSchedulerStatus returns task performance scheduler status
func GetTaskPerformanceSchedulerStatus(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get task performance statistics
	var totalTasks, evaluatedTasks, autoCalculatedTasks int64
	db.Model(&models.Task{}).Count(&totalTasks)
	db.Model(&models.Task{}).Where("performance_percentage > 0").Count(&evaluatedTasks)
	db.Model(&models.Task{}).Where("is_auto_calculated = ?", true).Count(&autoCalculatedTasks)

	// Get recent evaluation activity
	var recentEvaluations []models.Task
	db.Where("evaluation_date IS NOT NULL").Order("evaluation_date DESC").Limit(10).Preload("EvaluatedBy").Find(&recentEvaluations)

	status := gin.H{
		"scheduler_status":      "active",
		"last_run":              time.Now().Add(-time.Hour), // Simulated last run
		"next_run":              time.Now().Add(time.Hour),  // Simulated next run
		"total_tasks":           totalTasks,
		"evaluated_tasks":       evaluatedTasks,
		"auto_calculated_tasks": autoCalculatedTasks,
		"evaluation_coverage":   float64(evaluatedTasks) / float64(totalTasks) * 100,
		"recent_evaluations":    recentEvaluations,
		"status_checked_at":     time.Now(),
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task performance scheduler status retrieved successfully",
		Data:    status,
	})
}

// RunManualTaskPerformanceEvaluation runs manual task performance evaluation
func RunManualTaskPerformanceEvaluation(c *gin.Context) {
	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		HandleUnauthorized(c, "User not authenticated")
		return
	}

	// Get all tasks that need evaluation (completed but not evaluated recently)
	var tasks []models.Task
	cutoffDate := time.Now().AddDate(0, 0, -7) // Tasks not evaluated in last 7 days
	if err := db.Where("status = ? AND (evaluation_date IS NULL OR evaluation_date < ?)",
		models.TaskStatusCompleted, cutoffDate).Find(&tasks).Error; err != nil {
		HandleInternalError(c, "Failed to fetch tasks for evaluation: "+err.Error())
		return
	}

	evaluatedCount := 0
	userIDUint := userID.(uint)

	for _, task := range tasks {
		// Calculate performance metrics
		var performancePercentage float64 = 100.0
		var deadlineAdherenceScore float64 = 100.0
		var qualityScore float64 = 80.0 // Default quality score
		var completionEfficiency float64 = 100.0
		var priorityHandlingScore float64 = 100.0

		// Calculate deadline adherence
		if task.CompletedAt != nil && task.DueDate != nil {
			timeDiff := task.CompletedAt.Sub(*task.DueDate)
			if timeDiff > 0 {
				// Late completion
				daysLate := timeDiff.Hours() / 24
				deadlineAdherenceScore = max(0, 100.0-daysLate*10) // 10 points per day late
				performancePercentage -= min(50, daysLate*5)       // Up to 50 point penalty
			} else {
				// Early or on-time completion
				daysEarly := -timeDiff.Hours() / 24
				deadlineAdherenceScore = min(100, 100.0+daysEarly*2) // Bonus for early completion
			}
		}

		// Adjust for priority
		switch task.Priority {
		case models.TaskPriorityUrgent:
			priorityHandlingScore = 120.0 // Bonus for handling urgent tasks
		case models.TaskPriorityHigh:
			priorityHandlingScore = 110.0
		case models.TaskPriorityLow:
			priorityHandlingScore = 90.0
		}

		// Calculate overall performance
		performancePercentage = (deadlineAdherenceScore + qualityScore + completionEfficiency + priorityHandlingScore) / 4

		// Update task performance
		task.PerformancePercentage = performancePercentage
		task.DeadlineAdherenceScore = deadlineAdherenceScore
		task.QualityScore = qualityScore
		task.CompletionEfficiency = completionEfficiency
		task.PriorityHandlingScore = priorityHandlingScore
		task.IsAutoCalculated = false // Manual evaluation
		task.EvaluationDate = &time.Time{}
		*task.EvaluationDate = time.Now()
		task.EvaluatedByID = &userIDUint
		task.PerformanceNotes = "Manual performance evaluation"

		if err := db.Save(&task).Error; err == nil {
			evaluatedCount++
		}
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Manual task performance evaluation completed successfully",
		Data: gin.H{
			"evaluated_count":  evaluatedCount,
			"total_candidates": len(tasks),
			"evaluation_date":  time.Now(),
			"evaluated_by_id":  userIDUint,
		},
	})
}

// GetTaskComment returns a specific task comment
func GetTaskComment(c *gin.Context) {
	id, valid := ValidateID(c, "id")
	if !valid {
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Get task comment with relationships
	var comment models.TaskComment
	if err := db.Preload("Author").Preload("Task").First(&comment, id).Error; err != nil {
		if err.Error() == "record not found" {
			HandleNotFound(c, "Task comment")
			return
		}
		HandleInternalError(c, "Failed to fetch task comment: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task comment retrieved successfully",
		Data:    comment,
	})
}

// Helper function for min/max since Go doesn't have built-in generics for these
func min(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

func max(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// GetTaskStatusHistory returns the status change history for a task
func GetTaskStatusHistory(c *gin.Context) {
	// Get task ID from URL parameter
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		HandleBadRequest(c, "Invalid task ID")
		return
	}

	// Get pagination parameters
	limitStr := c.DefaultQuery("limit", "50")
	offsetStr := c.DefaultQuery("offset", "0")

	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 50
	}

	offset, err := strconv.Atoi(offsetStr)
	if err != nil {
		offset = 0
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize task status tracker
	tracker := services.NewTaskStatusTracker(db)

	// Get status history
	history, err := tracker.GetTaskStatusHistory(uint(taskID), limit, offset)
	if err != nil {
		HandleInternalError(c, "Failed to get task status history: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task status history retrieved successfully",
		Data:    history,
	})
}

// GetTaskStatusSummary returns a summary of status changes for a task
func GetTaskStatusSummary(c *gin.Context) {
	// Get task ID from URL parameter
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 32)
	if err != nil {
		HandleBadRequest(c, "Invalid task ID")
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize task status tracker
	tracker := services.NewTaskStatusTracker(db)

	// Get status summary
	summary, err := tracker.GetTaskStatusSummary(uint(taskID))
	if err != nil {
		HandleInternalError(c, "Failed to get task status summary: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task status summary retrieved successfully",
		Data:    summary,
	})
}

// GetTaskStatusAnalytics returns analytics on task status changes
func GetTaskStatusAnalytics(c *gin.Context) {
	// Get query parameters
	userIDStr := c.Query("user_id")
	dateFromStr := c.Query("date_from")
	dateToStr := c.Query("date_to")

	var userID *uint
	var dateFrom, dateTo *time.Time

	// Parse user ID if provided
	if userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			userIDUint := uint(uid)
			userID = &userIDUint
		}
	}

	// Parse date range if provided
	if dateFromStr != "" {
		if df, err := time.Parse("2006-01-02", dateFromStr); err == nil {
			dateFrom = &df
		}
	}
	if dateToStr != "" {
		if dt, err := time.Parse("2006-01-02", dateToStr); err == nil {
			dateTo = &dt
		}
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Initialize task status tracker
	tracker := services.NewTaskStatusTracker(db)

	// Get analytics
	analytics, err := tracker.GetTaskStatusAnalytics(userID, dateFrom, dateTo)
	if err != nil {
		HandleInternalError(c, "Failed to get task status analytics: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{
		Message: "Task status analytics retrieved successfully",
		Data:    analytics,
	})
}
