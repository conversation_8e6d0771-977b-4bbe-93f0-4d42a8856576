'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { biApi } from '../../../../services/enterpriseApi';
import { Report } from '../../../../types/enterprise';

const ReportViewPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const reportId = parseInt(params.id as string);
  
  const [report, setReport] = useState<Report | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (reportId) {
      fetchReport();
    }
  }, [reportId]);

  const fetchReport = async () => {
    try {
      setLoading(true);
      const response = await biApi.getReport(reportId);
      setReport(response.data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch report');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this report?')) return;
    
    try {
      await biApi.deleteReport(reportId);
      router.push('/enterprise/bi/reports');
    } catch (err: any) {
      setError(err.message || 'Failed to delete report');
    }
  };

  const handleExecute = async () => {
    try {
      await biApi.executeReport(reportId);
      await fetchReport(); // Refresh data
    } catch (err: any) {
      setError(err.message || 'Failed to execute report');
    }
  };

  if (loading) return <div className="p-6">Loading report...</div>;
  if (error) return <div className="p-6 text-red-600">Error: {error}</div>;
  if (!report) return <div className="p-6">Report not found</div>;

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Report Details</h1>
        <div className="flex space-x-2">
          <button
            onClick={() => router.push('/enterprise/bi/reports')}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Back to Reports
          </button>
          <button
            onClick={handleExecute}
            className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
          >
            Execute Report
          </button>
          <button
            onClick={() => router.push(`/enterprise/bi/reports/${reportId}/edit`)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Edit Report
          </button>
          <button
            onClick={handleDelete}
            className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
          >
            Delete Report
          </button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-white rounded-lg shadow">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">{report.report_name}</h2>
              <p className="text-sm text-gray-600">Code: {report.report_code}</p>
              <p className="text-sm text-gray-600">Category: {report.category}</p>
            </div>
            <div className="text-right">
              <div className="flex flex-col space-y-2">
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  report.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {report.is_active ? 'Active' : 'Inactive'}
                </span>
                <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                  report.is_public 
                    ? 'bg-blue-100 text-blue-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {report.is_public ? 'Public' : 'Private'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Report Overview */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Report Overview</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600 capitalize">
                {report.report_type.replace('_', ' ')}
              </div>
              <div className="text-sm text-blue-600">Report Type</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {report.execution_count || 0}
              </div>
              <div className="text-sm text-green-600">Executions</div>
            </div>

            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {report.avg_execution_time ? `${report.avg_execution_time.toFixed(2)}s` : 'N/A'}
              </div>
              <div className="text-sm text-orange-600">Avg Execution Time</div>
            </div>

            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {report.file_size ? `${(report.file_size / 1024).toFixed(1)} KB` : 'N/A'}
              </div>
              <div className="text-sm text-purple-600">File Size</div>
            </div>
          </div>
        </div>

        {/* Report Information */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Report Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700">Output Format</label>
              <p className="mt-1 text-sm text-gray-900 uppercase">{report.output_format}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Data Source ID</label>
              <p className="mt-1 text-sm text-gray-900">{report.data_source_id || 'N/A'}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Owner ID</label>
              <p className="mt-1 text-sm text-gray-900">{report.owner_id}</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Retention Days</label>
              <p className="mt-1 text-sm text-gray-900">{report.retention_days} days</p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Last Executed</label>
              <p className="mt-1 text-sm text-gray-900">
                {report.last_executed ? 
                  new Date(report.last_executed).toLocaleString() : 
                  'Never'
                }
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <p className="mt-1 text-sm text-gray-900">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                  report.status === 'completed' 
                    ? 'bg-green-100 text-green-800'
                    : report.status === 'running'
                    ? 'bg-blue-100 text-blue-800'
                    : report.status === 'failed'
                    ? 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {report.status || 'ready'}
                </span>
              </p>
            </div>
          </div>

          {report.description && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Description</label>
              <div className="mt-1 text-sm text-gray-900 prose max-w-none">
                {report.description}
              </div>
            </div>
          )}
        </div>

        {/* Query Definition */}
        {report.query_definition && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Query Definition</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto font-mono">
                {report.query_definition}
              </pre>
            </div>
          </div>
        )}

        {/* Parameters */}
        {report.parameters && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Parameters</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">
                {JSON.stringify(JSON.parse(report.parameters || '{}'), null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Execution History */}
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Execution Statistics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {report.execution_count || 0}
              </div>
              <div className="text-sm text-blue-600">Total Executions</div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {report.avg_execution_time ? `${report.avg_execution_time.toFixed(2)}s` : 'N/A'}
              </div>
              <div className="text-sm text-green-600">Average Execution Time</div>
            </div>
            
            <div className="bg-orange-50 p-4 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {report.last_error ? 'Error' : 'Success'}
              </div>
              <div className="text-sm text-orange-600">Last Status</div>
            </div>
          </div>

          {report.last_error && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700">Last Error</label>
              <div className="mt-1 p-3 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-sm text-red-800">{report.last_error}</p>
              </div>
            </div>
          )}
        </div>

        {/* Schedule Information */}
        {report.is_scheduled && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Schedule Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700">Schedule</label>
                <p className="mt-1 text-sm text-gray-900">{report.schedule || 'N/A'}</p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700">Next Run</label>
                <p className="mt-1 text-sm text-gray-900">
                  {report.next_run ? 
                    new Date(report.next_run).toLocaleString() : 
                    'N/A'
                  }
                </p>
              </div>
            </div>

            {report.recipients && (
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700">Recipients</label>
                <p className="mt-1 text-sm text-gray-900">{report.recipients}</p>
              </div>
            )}
          </div>
        )}

        {/* Metadata */}
        {report.metadata && (
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Metadata</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm text-gray-800 whitespace-pre-wrap overflow-x-auto">
                {JSON.stringify(JSON.parse(report.metadata || '{}'), null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Timestamps */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
            <div>
              <label className="block font-medium">Created At</label>
              <p>{new Date(report.created_at).toLocaleString()}</p>
            </div>
            <div>
              <label className="block font-medium">Updated At</label>
              <p>{new Date(report.updated_at).toLocaleString()}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReportViewPage;
