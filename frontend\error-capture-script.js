/**
 * Standalone Error Capture Script
 * This script can be injected into any page to capture all frontend errors
 * Usage: Copy and paste this script into the browser console
 */

(function() {
  'use strict';

  // Error storage
  let capturedErrors = [];
  let sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Utility functions
  function generateErrorId() {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  function logErrorToConsole(error) {
    const styles = {
      low: 'color: #3b82f6; font-weight: bold;',
      medium: 'color: #f59e0b; font-weight: bold;',
      high: 'color: #ef4444; font-weight: bold;',
      critical: 'color: #dc2626; font-weight: bold; background: #fee2e2; padding: 2px 4px;',
    };
    
    const style = styles[error.severity] || styles.medium;
    
    console.group(`%c🚨 ${error.type.toUpperCase()} ERROR [${error.severity.toUpperCase()}]`, style);
    console.log('Message:', error.message);
    console.log('Timestamp:', error.timestamp);
    console.log('URL:', error.url);
    if (error.component) console.log('Component:', error.component);
    if (error.stack) console.log('Stack:', error.stack);
    if (error.request) console.log('Request:', error.request);
    if (error.response) console.log('Response:', error.response);
    if (error.context) console.log('Context:', error.context);
    console.groupEnd();
  }

  function captureError(errorData) {
    const error = {
      id: generateErrorId(),
      timestamp: new Date().toISOString(),
      type: errorData.type || 'runtime',
      message: errorData.message || 'Unknown error',
      stack: errorData.stack,
      url: window.location.href,
      userAgent: navigator.userAgent,
      sessionId: sessionId,
      severity: errorData.severity || 'medium',
      component: errorData.component,
      props: errorData.props,
      state: errorData.state,
      request: errorData.request,
      response: errorData.response,
      context: errorData.context,
    };

    capturedErrors.push(error);
    logErrorToConsole(error);

    // Keep only last 1000 errors
    if (capturedErrors.length > 1000) {
      capturedErrors = capturedErrors.slice(-1000);
    }

    // Save to localStorage
    try {
      localStorage.setItem('frontend_errors_script', JSON.stringify(capturedErrors.slice(-100)));
    } catch (e) {
      console.warn('Failed to save errors to localStorage:', e);
    }
  }

  // Global error handlers
  window.addEventListener('error', (event) => {
    captureError({
      type: 'runtime',
      message: event.message,
      stack: event.error?.stack,
      url: event.filename,
      severity: 'high',
      context: {
        lineno: event.lineno,
        colno: event.colno,
        source: 'window.error',
      },
    });
  });

  window.addEventListener('unhandledrejection', (event) => {
    captureError({
      type: 'promise',
      message: event.reason?.message || 'Unhandled Promise Rejection',
      stack: event.reason?.stack,
      severity: 'high',
      context: {
        reason: event.reason,
        source: 'unhandledrejection',
      },
    });
  });

  // Override console methods
  const originalConsole = {
    error: console.error,
    warn: console.warn,
    log: console.log,
  };

  console.error = function(...args) {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    captureError({
      type: 'runtime',
      message: `Console Error: ${message}`,
      severity: 'high',
      context: {
        consoleArgs: args,
        source: 'console.error',
      },
    });

    originalConsole.error.apply(console, args);
  };

  console.warn = function(...args) {
    const message = args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
    ).join(' ');

    captureError({
      type: 'runtime',
      message: `Console Warning: ${message}`,
      severity: 'medium',
      context: {
        consoleArgs: args,
        source: 'console.warn',
      },
    });

    originalConsole.warn.apply(console, args);
  };

  // Monitor fetch requests
  const originalFetch = window.fetch;
  window.fetch = async function(...args) {
    const [resource, config] = args;
    const url = typeof resource === 'string' ? resource : resource.url;
    const method = config?.method || 'GET';

    try {
      const response = await originalFetch(...args);
      
      if (!response.ok) {
        captureError({
          type: 'axios',
          message: `Fetch request failed: ${response.status} ${response.statusText}`,
          severity: response.status >= 500 ? 'critical' : 'high',
          request: {
            method,
            url,
            data: config?.body,
            headers: config?.headers,
          },
          response: {
            status: response.status,
            statusText: response.statusText,
          },
        });
      }

      return response;
    } catch (error) {
      captureError({
        type: 'axios',
        message: `Fetch request error: ${error.message}`,
        severity: 'critical',
        stack: error.stack,
        request: {
          method,
          url,
          data: config?.body,
          headers: config?.headers,
        },
      });
      throw error;
    }
  };

  // Monitor XMLHttpRequest
  const originalXHROpen = XMLHttpRequest.prototype.open;
  const originalXHRSend = XMLHttpRequest.prototype.send;

  XMLHttpRequest.prototype.open = function(method, url, ...args) {
    this._errorCaptureMethod = method;
    this._errorCaptureUrl = url;
    return originalXHROpen.apply(this, [method, url, ...args]);
  };

  XMLHttpRequest.prototype.send = function(data) {
    const xhr = this;
    const method = this._errorCaptureMethod;
    const url = this._errorCaptureUrl;

    xhr.addEventListener('error', () => {
      captureError({
        type: 'axios',
        message: `XMLHttpRequest error: ${method} ${url}`,
        severity: 'high',
        request: {
          method,
          url,
          data,
        },
        context: {
          source: 'XMLHttpRequest',
        },
      });
    });

    xhr.addEventListener('load', () => {
      if (xhr.status >= 400) {
        captureError({
          type: 'axios',
          message: `XMLHttpRequest failed: ${xhr.status} ${xhr.statusText}`,
          severity: xhr.status >= 500 ? 'critical' : 'high',
          request: {
            method,
            url,
            data,
          },
          response: {
            status: xhr.status,
            statusText: xhr.statusText,
            data: xhr.responseText,
          },
        });
      }
    });

    return originalXHRSend.apply(this, arguments);
  };

  // Utility functions for the global scope
  window.errorCapture = {
    getErrors: () => capturedErrors,
    clearErrors: () => {
      capturedErrors = [];
      localStorage.removeItem('frontend_errors_script');
      console.log('✅ All captured errors cleared');
    },
    exportErrors: () => {
      const data = JSON.stringify(capturedErrors, null, 2);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `frontend_errors_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      console.log('📁 Errors exported to file');
    },
    getSummary: () => {
      const byType = {};
      const bySeverity = {};
      
      capturedErrors.forEach(error => {
        byType[error.type] = (byType[error.type] || 0) + 1;
        bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
      });

      const summary = {
        total: capturedErrors.length,
        byType,
        bySeverity,
        recent: capturedErrors.slice(-10),
      };

      console.group('🚨 ERROR CAPTURE SUMMARY');
      console.log('Total Errors:', summary.total);
      console.log('By Type:', summary.byType);
      console.log('By Severity:', summary.bySeverity);
      console.log('Recent Errors:', summary.recent);
      console.groupEnd();

      return summary;
    },
    logTestError: (message = 'Test error from error capture script') => {
      captureError({
        type: 'runtime',
        message,
        severity: 'medium',
        context: {
          source: 'manual_test',
          timestamp: Date.now(),
        },
      });
    }
  };

  // Keyboard shortcuts
  document.addEventListener('keydown', (e) => {
    // Ctrl+Shift+E: Show error summary
    if (e.ctrlKey && e.shiftKey && e.key === 'E') {
      e.preventDefault();
      window.errorCapture.getSummary();
    }
    // Ctrl+Shift+X: Export errors
    if (e.ctrlKey && e.shiftKey && e.key === 'X') {
      e.preventDefault();
      window.errorCapture.exportErrors();
    }
    // Ctrl+Shift+C: Clear errors
    if (e.ctrlKey && e.shiftKey && e.key === 'C') {
      e.preventDefault();
      window.errorCapture.clearErrors();
    }
  });

  console.log('🚨 Error Capture Script Initialized');
  console.log('📋 Available commands:');
  console.log('  - errorCapture.getErrors() - Get all captured errors');
  console.log('  - errorCapture.getSummary() - Show error summary');
  console.log('  - errorCapture.exportErrors() - Export errors to file');
  console.log('  - errorCapture.clearErrors() - Clear all errors');
  console.log('  - errorCapture.logTestError() - Log a test error');
  console.log('🎹 Keyboard shortcuts:');
  console.log('  - Ctrl+Shift+E - Show error summary');
  console.log('  - Ctrl+Shift+X - Export errors');
  console.log('  - Ctrl+Shift+C - Clear errors');

})();
