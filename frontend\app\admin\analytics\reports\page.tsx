'use client'

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentChartBarIcon,
  ArrowDownTrayIcon,
  PlayIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';
import Layout from '../../../components/Layout/Layout';
import { useAuthStore } from '../../../stores/authStore';
import apiService from '../../../services/api';

interface AnalyticsReport {
  id: number;
  name: string;
  description: string;
  report_type: 'scheduled' | 'on_demand' | 'real_time';
  data_source: string;
  parameters: any;
  schedule: string;
  format: 'pdf' | 'excel' | 'csv' | 'json';
  status: 'active' | 'inactive' | 'running' | 'completed' | 'failed';
  last_run: string;
  next_run: string;
  created_by: number;
  created_at: string;
  updated_at: string;
}

const AnalyticsReportsPage: React.FC = () => {
  const router = useRouter();
  const { user } = useAuthStore();
  const [reports, setReports] = useState<AnalyticsReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      setLoading(true);
      // Fetch real analytics reports from API
      const response = await fetch('/api/analytics/reports', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch analytics reports');
      }

      const data = await response.json();
      const reports: AnalyticsReport[] = data.data || [];
      const mockReports: AnalyticsReport[] = [
        {
          id: 1,
          name: 'Monthly User Activity Report',
          description: 'Comprehensive report on user engagement and activity patterns',
          report_type: 'scheduled',
          data_source: 'user_analytics',
          parameters: { period: 'monthly', metrics: ['logins', 'page_views', 'actions'] },
          schedule: '0 0 1 * *', // First day of every month
          format: 'pdf',
          status: 'active',
          last_run: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          next_run: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          created_by: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 2,
          name: 'Document Performance Analysis',
          description: 'Analysis of document views, downloads, and user interactions',
          report_type: 'on_demand',
          data_source: 'document_analytics',
          parameters: { include_charts: true, breakdown_by_category: true },
          schedule: '',
          format: 'excel',
          status: 'completed',
          last_run: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          next_run: '',
          created_by: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: 3,
          name: 'Real-time System Metrics',
          description: 'Live system performance and health metrics',
          report_type: 'real_time',
          data_source: 'system_metrics',
          parameters: { refresh_interval: 30, alerts_enabled: true },
          schedule: '',
          format: 'json',
          status: 'running',
          last_run: new Date().toISOString(),
          next_run: '',
          created_by: 1,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      setReports(reports);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to fetch analytics reports');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this report?')) return;
    
    try {
      // await apiService.deleteAnalyticsReport(id);
      setReports(reports.filter(report => report.id !== id));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to delete report');
    }
  };

  const handleRunReport = async (id: number) => {
    try {
      // await apiService.runAnalyticsReport(id);
      setReports(reports.map(report => 
        report.id === id ? { ...report, status: 'running' as const, last_run: new Date().toISOString() } : report
      ));
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to run report');
    }
  };

  const handleDownloadReport = async (id: number) => {
    try {
      // await apiService.downloadReport(id);
      alert('Report download started!');
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to download report');
    }
  };

  const filteredReports = reports.filter(report =>
    report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.report_type.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'on_demand':
        return 'bg-green-100 text-green-800';
      case 'real_time':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'running':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircleIcon className="h-4 w-4" />;
      case 'running':
        return <ClockIcon className="h-4 w-4" />;
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4" />;
      default:
        return <ClockIcon className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!user || user.role !== 'admin') {
    return (
      <Layout>
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900">Access Denied</h1>
            <p className="text-gray-600 mt-2">You must be an admin to view analytics reports.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container-custom py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Analytics Reports</h1>
              <p className="text-gray-600 mt-1">Create and manage automated analytics reports</p>
            </div>
            <Link
              href="/admin/analytics/reports/new"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Report
            </Link>
          </div>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search reports..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* Loading */}
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">Loading analytics reports...</p>
          </div>
        ) : (
          /* Reports Table */
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Report Name
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Format
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Run
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Next Run
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredReports.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                      No analytics reports found.
                      <Link
                        href="/admin/analytics/reports/new"
                        className="block mt-2 text-primary-600 hover:text-primary-500"
                      >
                        Create your first report
                      </Link>
                    </td>
                  </tr>
                ) : (
                  filteredReports.map((report) => (
                    <tr key={report.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{report.name}</div>
                          <div className="text-sm text-gray-500 truncate max-w-xs">{report.description}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(report.report_type)}`}>
                          {report.report_type.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 uppercase">
                        {report.format}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                          {getStatusIcon(report.status)}
                          <span className="ml-1">{report.status}</span>
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(report.last_run)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDate(report.next_run)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button
                            onClick={() => router.push(`/admin/analytics/reports/${report.id}`)}
                            className="text-blue-600 hover:text-blue-900"
                            title="View Report"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                          {(report.report_type === 'on_demand' || report.status === 'active') && (
                            <button
                              onClick={() => handleRunReport(report.id)}
                              className="text-green-600 hover:text-green-900"
                              title="Run Report"
                            >
                              <PlayIcon className="h-4 w-4" />
                            </button>
                          )}
                          {report.status === 'completed' && (
                            <button
                              onClick={() => handleDownloadReport(report.id)}
                              className="text-purple-600 hover:text-purple-900"
                              title="Download Report"
                            >
                              <ArrowDownTrayIcon className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => router.push(`/admin/analytics/reports/${report.id}/edit`)}
                            className="text-yellow-600 hover:text-yellow-900"
                            title="Edit Report"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(report.id)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Report"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default AnalyticsReportsPage;
