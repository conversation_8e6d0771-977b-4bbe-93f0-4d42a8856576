-- Fix proceeding unique_id trigger to include microseconds for better uniqueness
DROP TRIGGER IF EXISTS generate_proceeding_unique_id_trigger ON proceedings;

CREATE OR REPLACE FUNCTION generate_proceeding_unique_id()
RETURNS TRIGGER AS $$
BEGIN
    -- For INSERT operations, always generate unique_id
    -- For UPDATE operations, only regenerate if name or initiation_date changed
    IF TG_OP = 'INSERT' OR
       (TG_OP = 'UPDATE' AND (OLD.name != NEW.name OR OLD.initiation_date != NEW.initiation_date)) THEN
        -- Include microseconds and a random component for better uniqueness
        NEW.unique_id = NEW.name || ' ' || TO_CHAR(NEW.initiation_date, 'YYYY-MM-DD HH24:MI:SS.US') || '-' || EXTRACT(EPOCH FROM clock_timestamp())::text;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER generate_proceeding_unique_id_trigger BEFORE INSERT OR UPDATE ON proceedings
    FOR EACH ROW EXECUTE FUNCTION generate_proceeding_unique_id();
