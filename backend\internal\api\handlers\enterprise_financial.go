package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"federal-register-clone/internal/database"
	"federal-register-clone/internal/models"
)

// ChartOfAccountsRequest represents the request structure for chart of accounts
type ChartOfAccountsRequest struct {
	AccountCode      string  `json:"account_code" binding:"required"`
	AccountName      string  `json:"account_name" binding:"required"`
	AccountType      string  `json:"account_type" binding:"required"`
	Description      string  `json:"description"`
	ParentAccountID  *uint   `json:"parent_account_id"`
	Level            int     `json:"level"`
	IsActive         bool    `json:"is_active"`
	IsControlAccount bool    `json:"is_control_account"`
	AllowPosting     bool    `json:"allow_posting"`
	NormalBalance    string  `json:"normal_balance" binding:"required"`
	CurrentBalance   float64 `json:"current_balance"`
	OpeningBalance   float64 `json:"opening_balance"`
	CurrencyCode     string  `json:"currency_code"`
	TaxCode          string  `json:"tax_code"`
	ReportingCode    string  `json:"reporting_code"`
	GLClass          string  `json:"gl_class"`
	Metadata         string  `json:"metadata"`
}

// GLEntryRequest represents the request structure for general ledger entries
type GLEntryRequest struct {
	EntryNumber        string    `json:"entry_number" binding:"required"`
	TransactionDate    time.Time `json:"transaction_date" binding:"required"`
	PostingDate        time.Time `json:"posting_date" binding:"required"`
	AccountID          uint      `json:"account_id" binding:"required"`
	TransactionType    string    `json:"transaction_type" binding:"required"`
	DebitAmount        float64   `json:"debit_amount"`
	CreditAmount       float64   `json:"credit_amount"`
	ReferenceType      string    `json:"reference_type"`
	ReferenceID        string    `json:"reference_id"`
	Description        string    `json:"description"`
	DocumentID         *uint     `json:"document_id"`
	CreatedByID        uint      `json:"created_by_id" binding:"required"`
	ApprovedByID       *uint     `json:"approved_by_id"`
	Status             string    `json:"status"`
	IsReconciled       bool      `json:"is_reconciled"`
	CurrencyCode       string    `json:"currency_code"`
	ExchangeRate       float64   `json:"exchange_rate"`
	BaseCurrencyAmount float64   `json:"base_currency_amount"`
	Metadata           string    `json:"metadata"`
}

// BudgetRequest represents the request structure for budgets
type BudgetRequest struct {
	BudgetName      string    `json:"budget_name" binding:"required"`
	Description     string    `json:"description"`
	FiscalYear      int       `json:"fiscal_year" binding:"required"`
	StartDate       time.Time `json:"start_date" binding:"required"`
	EndDate         time.Time `json:"end_date" binding:"required"`
	BudgetType      string    `json:"budget_type"`
	BudgetCategory  string    `json:"budget_category"`
	PlannedAmount   float64   `json:"planned_amount" binding:"required"`
	RevisedAmount   float64   `json:"revised_amount"`
	ActualAmount    float64   `json:"actual_amount"`
	CommittedAmount float64   `json:"committed_amount"`
	VarianceAmount  float64   `json:"variance_amount"`
	VariancePercent float64   `json:"variance_percent"`
	AccountID       uint      `json:"account_id" binding:"required"`
	DepartmentID    *uint     `json:"department_id"`
	CostCenterID    *uint     `json:"cost_center_id"`
	ProjectID       *uint     `json:"project_id"`
	Status          string    `json:"status"`
	AlertThreshold  float64   `json:"alert_threshold"`
	IsAlertEnabled  bool      `json:"is_alert_enabled"`
	Metadata        string    `json:"metadata"`
}

// CostCenterRequest represents the request structure for cost centers
type CostCenterRequest struct {
	CostCenterCode   string  `json:"cost_center_code" binding:"required"`
	CostCenterName   string  `json:"cost_center_name" binding:"required"`
	Description      string  `json:"description"`
	ManagerID        *uint   `json:"manager_id"`
	BudgetAmount     float64 `json:"budget_amount"`
	ActualAmount     float64 `json:"actual_amount"`
	CommittedAmount  float64 `json:"committed_amount"`
	AllocationMethod string  `json:"allocation_method"`
	AllocationBase   string  `json:"allocation_base"`
	AllocationRate   float64 `json:"allocation_rate"`
	IsActive         bool    `json:"is_active"`
	Metadata         string  `json:"metadata"`
}

// FinancialReportRequest represents the request structure for financial reports
type FinancialReportRequest struct {
	ReportCode       string    `json:"report_code" binding:"required"`
	ReportName       string    `json:"report_name" binding:"required"`
	ReportType       string    `json:"report_type" binding:"required"`
	Description      string    `json:"description"`
	PeriodType       string    `json:"period_type" binding:"required"`
	StartDate        time.Time `json:"start_date" binding:"required"`
	EndDate          time.Time `json:"end_date" binding:"required"`
	GeneratedByID    *uint     `json:"generated_by_id"`
	ReportData       string    `json:"report_data"`
	ReportFormat     string    `json:"report_format"`
	FilePath         string    `json:"file_path"`
	Status           string    `json:"status"`
	DistributionList string    `json:"distribution_list"`
	Metadata         string    `json:"metadata"`
}

// Chart of Accounts Handlers

// GetChartOfAccounts returns all chart of accounts with pagination
func GetChartOfAccounts(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total accounts
	var total int64
	db.Model(&models.ChartOfAccounts{}).Count(&total)

	// Get accounts with pagination
	var accounts []models.ChartOfAccounts
	offset := (page - 1) * perPage
	if err := db.Preload("ParentAccount").
		Offset(offset).
		Limit(perPage).
		Order("account_code ASC").
		Find(&accounts).Error; err != nil {
		HandleInternalError(c, "Failed to fetch chart of accounts: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"accounts": accounts,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateAccount creates a new chart of accounts entry
func CreateAccount(c *gin.Context) {
	var req ChartOfAccountsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create account
	account := models.ChartOfAccounts{
		AccountCode:      req.AccountCode,
		AccountName:      req.AccountName,
		AccountType:      models.AccountType(req.AccountType),
		Description:      req.Description,
		ParentAccountID:  req.ParentAccountID,
		Level:            req.Level,
		IsActive:         req.IsActive,
		IsControlAccount: req.IsControlAccount,
		AllowPosting:     req.AllowPosting,
		NormalBalance:    models.TransactionType(req.NormalBalance),
		CurrentBalance:   req.CurrentBalance,
		OpeningBalance:   req.OpeningBalance,
		CurrencyCode:     req.CurrencyCode,
		TaxCode:          req.TaxCode,
		ReportingCode:    req.ReportingCode,
		GLClass:          req.GLClass,
		Metadata:         req.Metadata,
	}

	if err := db.Create(&account).Error; err != nil {
		HandleInternalError(c, "Failed to create account: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("ParentAccount").First(&account, account.ID)

	c.JSON(http.StatusCreated, gin.H{"account": account})
}

// GetAccount returns a specific chart of accounts entry
func GetAccount(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var account models.ChartOfAccounts
	if err := db.Preload("ParentAccount").First(&account, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"account": account})
}

// UpdateAccount updates a chart of accounts entry
func UpdateAccount(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	var req ChartOfAccountsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var account models.ChartOfAccounts
	if err := db.First(&account, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
		return
	}

	// Update account fields
	account.AccountCode = req.AccountCode
	account.AccountName = req.AccountName
	account.AccountType = models.AccountType(req.AccountType)
	account.Description = req.Description
	account.ParentAccountID = req.ParentAccountID
	account.Level = req.Level
	account.IsActive = req.IsActive
	account.IsControlAccount = req.IsControlAccount
	account.AllowPosting = req.AllowPosting
	account.NormalBalance = models.TransactionType(req.NormalBalance)
	account.CurrentBalance = req.CurrentBalance
	account.OpeningBalance = req.OpeningBalance
	account.CurrencyCode = req.CurrencyCode
	account.TaxCode = req.TaxCode
	account.ReportingCode = req.ReportingCode
	account.GLClass = req.GLClass
	account.Metadata = req.Metadata

	if err := db.Save(&account).Error; err != nil {
		HandleInternalError(c, "Failed to update account: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("ParentAccount").First(&account, account.ID)

	c.JSON(http.StatusOK, gin.H{"account": account})
}

// DeleteAccount deletes a chart of accounts entry
func DeleteAccount(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid account ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var account models.ChartOfAccounts
	if err := db.First(&account, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Account not found"})
		return
	}

	if err := db.Delete(&account).Error; err != nil {
		HandleInternalError(c, "Failed to delete account: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Account deleted successfully"})
}

// General Ledger Handlers

// GetGLEntries returns all general ledger entries with pagination
func GetGLEntries(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total entries
	var total int64
	db.Model(&models.GeneralLedger{}).Count(&total)

	// Get entries with pagination
	var entries []models.GeneralLedger
	offset := (page - 1) * perPage
	if err := db.Preload("Account").Preload("CreatedBy").Preload("ApprovedBy").
		Offset(offset).
		Limit(perPage).
		Order("transaction_date DESC").
		Find(&entries).Error; err != nil {
		HandleInternalError(c, "Failed to fetch GL entries: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"entries": entries,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateGLEntry creates a new general ledger entry
func CreateGLEntry(c *gin.Context) {
	var req GLEntryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create GL entry
	entry := models.GeneralLedger{
		EntryNumber:        req.EntryNumber,
		TransactionDate:    req.TransactionDate,
		PostingDate:        req.PostingDate,
		AccountID:          req.AccountID,
		TransactionType:    models.TransactionType(req.TransactionType),
		DebitAmount:        req.DebitAmount,
		CreditAmount:       req.CreditAmount,
		ReferenceType:      req.ReferenceType,
		ReferenceID:        req.ReferenceID,
		Description:        req.Description,
		DocumentID:         req.DocumentID,
		CreatedByID:        req.CreatedByID,
		ApprovedByID:       req.ApprovedByID,
		Status:             req.Status,
		IsReconciled:       req.IsReconciled,
		CurrencyCode:       req.CurrencyCode,
		ExchangeRate:       req.ExchangeRate,
		BaseCurrencyAmount: req.BaseCurrencyAmount,
		Metadata:           req.Metadata,
	}

	if err := db.Create(&entry).Error; err != nil {
		HandleInternalError(c, "Failed to create GL entry: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Account").Preload("CreatedBy").Preload("ApprovedBy").First(&entry, entry.ID)

	c.JSON(http.StatusCreated, gin.H{"entry": entry})
}

// GetGLEntry returns a specific general ledger entry
func GetGLEntry(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var entry models.GeneralLedger
	if err := db.Preload("Account").Preload("CreatedBy").Preload("ApprovedBy").First(&entry, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "GL entry not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"entry": entry})
}

// UpdateGLEntry updates a general ledger entry
func UpdateGLEntry(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	var req GLEntryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var entry models.GeneralLedger
	if err := db.First(&entry, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "GL entry not found"})
		return
	}

	// Update entry fields
	entry.EntryNumber = req.EntryNumber
	entry.TransactionDate = req.TransactionDate
	entry.PostingDate = req.PostingDate
	entry.AccountID = req.AccountID
	entry.TransactionType = models.TransactionType(req.TransactionType)
	entry.DebitAmount = req.DebitAmount
	entry.CreditAmount = req.CreditAmount
	entry.ReferenceType = req.ReferenceType
	entry.ReferenceID = req.ReferenceID
	entry.Description = req.Description
	entry.DocumentID = req.DocumentID
	entry.CreatedByID = req.CreatedByID
	entry.ApprovedByID = req.ApprovedByID
	entry.Status = req.Status
	entry.IsReconciled = req.IsReconciled
	entry.CurrencyCode = req.CurrencyCode
	entry.ExchangeRate = req.ExchangeRate
	entry.BaseCurrencyAmount = req.BaseCurrencyAmount
	entry.Metadata = req.Metadata

	if err := db.Save(&entry).Error; err != nil {
		HandleInternalError(c, "Failed to update GL entry: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Account").Preload("CreatedBy").Preload("ApprovedBy").First(&entry, entry.ID)

	c.JSON(http.StatusOK, gin.H{"entry": entry})
}

// DeleteGLEntry deletes a general ledger entry
func DeleteGLEntry(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var entry models.GeneralLedger
	if err := db.First(&entry, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "GL entry not found"})
		return
	}

	if err := db.Delete(&entry).Error; err != nil {
		HandleInternalError(c, "Failed to delete GL entry: "+err.Error())
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "GL entry deleted successfully"})
}

// PostGLEntry posts a general ledger entry (marks it as posted)
func PostGLEntry(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid entry ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var entry models.GeneralLedger
	if err := db.First(&entry, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "GL entry not found"})
		return
	}

	// Mark as posted
	now := time.Now()
	entry.Status = "posted"
	entry.ApprovedAt = &now

	if err := db.Save(&entry).Error; err != nil {
		HandleInternalError(c, "Failed to post GL entry: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Account").Preload("CreatedBy").Preload("ApprovedBy").First(&entry, entry.ID)

	c.JSON(http.StatusOK, gin.H{"entry": entry})
}

// Budget Management Handlers

// GetBudgets returns all budgets with pagination
func GetBudgets(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total budgets
	var total int64
	db.Model(&models.BudgetPlan{}).Count(&total)

	// Get budgets with pagination
	var budgets []models.BudgetPlan
	offset := (page - 1) * perPage
	if err := db.Preload("Account").Preload("ApprovedBy").
		Offset(offset).
		Limit(perPage).
		Order("fiscal_year DESC, budget_name ASC").
		Find(&budgets).Error; err != nil {
		HandleInternalError(c, "Failed to fetch budgets: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"budgets": budgets,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// CreateBudget creates a new budget
func CreateBudget(c *gin.Context) {
	var req BudgetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Create budget
	budget := models.BudgetPlan{
		BudgetName:      req.BudgetName,
		Description:     req.Description,
		FiscalYear:      req.FiscalYear,
		StartDate:       req.StartDate,
		EndDate:         req.EndDate,
		BudgetType:      req.BudgetType,
		BudgetCategory:  req.BudgetCategory,
		PlannedAmount:   req.PlannedAmount,
		RevisedAmount:   req.RevisedAmount,
		ActualAmount:    req.ActualAmount,
		CommittedAmount: req.CommittedAmount,
		VarianceAmount:  req.VarianceAmount,
		VariancePercent: req.VariancePercent,
		AccountID:       req.AccountID,
		DepartmentID:    req.DepartmentID,
		CostCenterID:    req.CostCenterID,
		ProjectID:       req.ProjectID,
		Status:          req.Status,
		AlertThreshold:  req.AlertThreshold,
		IsAlertEnabled:  req.IsAlertEnabled,
		Metadata:        req.Metadata,
	}

	if err := db.Create(&budget).Error; err != nil {
		HandleInternalError(c, "Failed to create budget: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Account").Preload("ApprovedBy").First(&budget, budget.ID)

	c.JSON(http.StatusCreated, gin.H{"budget": budget})
}

// GetBudget returns a specific budget
func GetBudget(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid budget ID"})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var budget models.BudgetPlan
	if err := db.Preload("Account").Preload("ApprovedBy").First(&budget, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Budget not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"budget": budget})
}

// UpdateBudget updates a budget
func UpdateBudget(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid budget ID"})
		return
	}

	var req BudgetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	var budget models.BudgetPlan
	if err := db.First(&budget, uint(id)).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Budget not found"})
		return
	}

	// Update budget fields
	budget.BudgetName = req.BudgetName
	budget.Description = req.Description
	budget.FiscalYear = req.FiscalYear
	budget.StartDate = req.StartDate
	budget.EndDate = req.EndDate
	budget.BudgetType = req.BudgetType
	budget.BudgetCategory = req.BudgetCategory
	budget.PlannedAmount = req.PlannedAmount
	budget.RevisedAmount = req.RevisedAmount
	budget.ActualAmount = req.ActualAmount
	budget.CommittedAmount = req.CommittedAmount
	budget.VarianceAmount = req.VarianceAmount
	budget.VariancePercent = req.VariancePercent
	budget.AccountID = req.AccountID
	budget.DepartmentID = req.DepartmentID
	budget.CostCenterID = req.CostCenterID
	budget.ProjectID = req.ProjectID
	budget.Status = req.Status
	budget.AlertThreshold = req.AlertThreshold
	budget.IsAlertEnabled = req.IsAlertEnabled
	budget.Metadata = req.Metadata

	if err := db.Save(&budget).Error; err != nil {
		HandleInternalError(c, "Failed to update budget: "+err.Error())
		return
	}

	// Load relationships
	db.Preload("Account").Preload("ApprovedBy").First(&budget, budget.ID)

	c.JSON(http.StatusOK, gin.H{"budget": budget})
}

// GetFinancialExpenses returns all financial expenses with pagination
func GetFinancialExpenses(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	if page < 1 {
		page = 1
	}
	if perPage < 1 || perPage > 100 {
		perPage = 20
	}

	// Get database connection
	db := database.GetDB()
	if db == nil {
		HandleInternalError(c, "Database connection is not initialized")
		return
	}

	// Count total expenses (using general ledger entries with positive amounts as expenses)
	var total int64
	db.Model(&models.GeneralLedger{}).Where("amount > 0").Count(&total)

	// Get expenses with pagination
	var expenses []models.GeneralLedger
	offset := (page - 1) * perPage
	if err := db.Preload("Account").
		Preload("CreatedBy").
		Preload("ApprovedBy").
		Where("amount > 0").
		Offset(offset).
		Limit(perPage).
		Order("transaction_date DESC").
		Find(&expenses).Error; err != nil {
		HandleInternalError(c, "Failed to fetch financial expenses: "+err.Error())
		return
	}

	// Return paginated response
	c.JSON(http.StatusOK, gin.H{
		"expenses": expenses,
		"pagination": gin.H{
			"page":     page,
			"per_page": perPage,
			"total":    total,
			"pages":    (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}
